package com.holderzone.member.marketing.service.cache;


import com.holderzone.member.common.qo.activity.RedPacketGameProcessQO;
import com.holderzone.member.common.qo.activity.RedPacketShareQO;
import com.holderzone.member.common.vo.nth.NthActivityDetailsVO;
import com.holderzone.member.common.vo.specials.LimitSpecialsActivityDetailsVO;
import com.holderzone.member.common.vo.voting.VotingActivityDetailsVO;
import com.holderzone.member.marketing.entity.follow.HsaFollowRedPacketActivity;

import java.math.BigDecimal;
import java.util.List;

/**
 * marketing 缓存服务
 *
 * <AUTHOR>
 * @since 2022-03-10
 */
public interface CacheService {

    /**
     * 计算红包订单剩余可优惠的金额
     *
     * @param qo                         qo
     * @param hsaFollowRedPacketActivity hsaFollowRedPacketActivity
     * @param orderDiscountKey           orderDiscountKey
     * @return BigDecimal
     */
    BigDecimal getActualDiscountAmount(RedPacketGameProcessQO qo, HsaFollowRedPacketActivity hsaFollowRedPacketActivity, String orderDiscountKey);

    /**
     * 更新累计红包金额
     *
     * @param redDiscountAmount redDiscountAmount
     * @param qo                RedPacketGameProcessQO
     * @return BigDecimal
     */
    BigDecimal getOrderTotalAmount(BigDecimal redDiscountAmount, RedPacketGameProcessQO qo);

    /**
     * 会员分享增加更新次数
     *
     * @param qo                         qo
     * @param gameNumKey                 gameNumKey
     * @param hsaFollowRedPacketActivity hsaFollowRedPacketActivity
     */
    void memberRedPacketShare(RedPacketShareQO qo, String gameNumKey, HsaFollowRedPacketActivity hsaFollowRedPacketActivity);

    /**
     * 更新订单分享次数
     *
     * @param orderShareNumKey orderShareNumKey
     */
    void updateOrderShareNum(String orderShareNumKey);

    /**
     * set
     *
     * @param orderRedActivityKey orderRedActivityKey
     * @param value               value
     */
    void setStringCache(String orderRedActivityKey, String value);

    /**
     * 将投票活动存入缓存
     *
     * @param jsons 投票活动列表str
     */
    void setVotingActivityList(List<String> jsons);

    /**
     * 获取缓存投票活动列表
     *
     * @return 投票活动列表
     */
    List<VotingActivityDetailsVO> getVotingActivityList(long start, long end);

    Long getVotingActivityCount();

    void deleteVotingActivityList();

    void updateInventoryNum(String code, int inventoryNum);

    int getInventoryNum(String code);

    Object getLockInventoryNum(String code);

    void deleteInventoryNum(String code);

    void saveLimitSpecialsActivityCache(LimitSpecialsActivityDetailsVO activityDetailsDTO);

    void saveLimitSpecialsActivityCacheByOperSubjectGuid(String operSubjectGuid, List<LimitSpecialsActivityDetailsVO> activityDetailsVOList);

    void deleteLimitSpecialsActivityCache(String guid);

    List<LimitSpecialsActivityDetailsVO> getLimitSpecialsActivityCache(String operSubjectGuid);

    boolean hasSpecialsActivityCacheKey(String operSubjectGuid);

    void saveNthActivityCacheByOperSubjectGuid(String operSubjectGuid, List<NthActivityDetailsVO> activityDetailsVOList);

    void deleteNthActivityCache(String guid);

    List<NthActivityDetailsVO> getNthActivityCache(String operSubjectGuid);

    boolean hasNthActivityCacheKey(String operSubjectGuid);
}
