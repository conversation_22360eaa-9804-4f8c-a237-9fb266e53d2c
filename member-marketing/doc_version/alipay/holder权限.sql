-- 小程序基础信息权限


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                          `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '微信小程序', NOW(),
        (select id
         from hp_function
         where name = '微页面'
         order by gmt_modified desc limit 1), 0, NULL, '', 0, 0, NOW(), NOW() );


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                          `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '支付宝小程序', NOW(),
        (select id
         from hp_function
         where name = '微页面'
         order by gmt_modified desc limit 1), 0, NULL, '', 0, 0, NOW(), NOW() );



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                          `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '微信小程序', NOW(),
        (select id
         from hp_function
         where name = '主题风格'
         order by gmt_modified desc limit 1), 0, NULL, '', 0, 0, NOW(), NOW() );

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                          `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '支付宝小程序', NOW(),
        (select id
         from hp_function
         where name = '主题风格'
         order by gmt_modified desc limit 1), 0, NULL, '', 0, 0, NOW(), NOW() );



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                          `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '微信小程序', NOW(),
        (select id
         from hp_function
         where name = '小程序导航'
         order by gmt_modified desc limit 1), 0, NULL, '', 0, 0, NOW(), NOW() );

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                          `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '支付宝小程序', NOW(),
        (select id
         from hp_function
         where name = '小程序导航'
         order by gmt_modified desc limit 1), 0, NULL, '', 0, 0, NOW(), NOW() );


-- 删除原有的微信公众号管理

delete
from hp_permission
where function_id in (select id from hp_function where name = '公众号授权');

delete
from hp_function
where group_id in (select id from hp_function_group where name = '公众号管理')

delete
from hp_function_group
where name = '公众号管理';

INSERT INTO hp_function_group
(create_time, name, `sequence`, update_time, gmt_create, gmt_modified, group_identification)
VALUES (CURRENT_TIMESTAMP, '授权管理', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, null);



INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified,
 function_identification)
VALUES (CURRENT_TIMESTAMP, '微信公众号', 1, 60, CURRENT_TIMESTAMP, 26,
        (SELECT id
         from hp_system
         WHERE name = '小程序基础配置' AND identification = 'member_tool'
         ORDER BY gmt_modified DESC LIMIT 1),
       (select id from hp_function_group where name = '授权管理' order by gmt_modified desc limit 1), CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`, `is_product`, `permission_type`,
                                      `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '查看', NOW(),
        (select id
         from hp_function
         where name = '微信公众号'
         order by gmt_modified desc limit 1)
     , 0
     , NULL
     , ''
     , 0
     , 0
     , NOW()
     , NOW() );

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`, `is_product`, `permission_type`,
                                      `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '立即绑定', NOW(),
        (select id
         from hp_function
         where name = '微信公众号'
         order by gmt_modified desc limit 1)
     , 0
     , NULL
     , ''
     , 0
     , 0
     , NOW()
     , NOW() );

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`, `is_product`, `permission_type`,
                                      `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '重新绑定', NOW(),
        (select id
         from hp_function
         where name = '微信公众号'
         order by gmt_modified desc limit 1)
     , 0
     , NULL
     , ''
     , 0
     , 0
     , NOW()
     , NOW() );



INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified,
 function_identification)
VALUES (CURRENT_TIMESTAMP, '支付宝小程序', 1, 60, CURRENT_TIMESTAMP, 26,
        (SELECT id
         from hp_system
         WHERE name = '小程序基础配置' AND identification = 'member_tool'
         ORDER BY gmt_modified DESC LIMIT 1),
       (select id from hp_function_group where name = '授权管理' order by gmt_modified desc limit 1), CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`, `is_product`, `permission_type`,
                                      `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '查看', NOW(),
        (select id
         from hp_function
         where name = '支付宝小程序'
         order by gmt_modified desc limit 1)
     , 0
     , NULL
     , ''
     , 0
     , 0
     , NOW()
     , NOW() );


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`, `is_product`, `permission_type`,
                                      `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '立即绑定', NOW(),
        (select id
         from hp_function
         where name = '支付宝小程序'
         order by gmt_modified desc limit 1)
     , 0
     , NULL
     , ''
     , 0
     , 0
     , NOW()
     , NOW() );



INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified,
 function_identification)
VALUES (CURRENT_TIMESTAMP, '餐饮云', 1, 60, CURRENT_TIMESTAMP, 26,
        (SELECT id
         from hp_system
         WHERE name = '小程序基础配置' AND identification = 'member_tool'
         ORDER BY gmt_modified DESC LIMIT 1),
       (select id from hp_function_group where name = '授权管理' order by gmt_modified desc limit 1), CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`, `is_product`, `permission_type`,
                                      `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '查看', NOW(),
        (select id
         from hp_function
         where name = '餐饮云'
         order by gmt_modified desc limit 1)
     , 0
     , NULL
     , ''
     , 0
     , 0
     , NOW()
     , NOW() );


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`, `is_product`, `permission_type`,
                                      `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '编辑', NOW(),
        (select id
         from hp_function
         where name = '餐饮云'
         order by gmt_modified desc limit 1)
     , 0
     , NULL
     , ''
     , 0
     , 0
     , NOW()
     , NOW() );








insert into gateway.hp_company_system (company_id,system_id) values (7,128),(7,141),(7,157),(7,172),(7,173);-- 何胜企业	 7
