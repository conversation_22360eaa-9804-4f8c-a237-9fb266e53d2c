ALTER TABLE hsm_member_mall_tool_platform_db.hsa_mall_page
    ADD `type` tinyint(4) DEFAULT 1 NULL;


ALTER TABLE hsm_member_mall_tool_platform_db.hsa_theme_style
    ADD `type` tinyint(1) DEFAULT 1 NULL;

ALTER TABLE hsm_member_mall_tool_platform_db.hsa_applet_navigation
    ADD `type` tinyint(1) DEFAULT 1 NULL;


ALTER TABLE hsm_member_marketing_platform_db.hsa_member_info_we_chat
    ADD `user_id` varchar(255) DEFAULT NULL COMMENT '支付宝用户user_id';
ALTER TABLE hsm_member_marketing_platform_db.hsa_member_info_we_chat MODIFY `open_id` varchar (255) DEFAULT NULL COMMENT '微信用户open_id';
ALTER TABLE hsa_member_info_we_chat ADD INDEX user_id_idx ( `user_id` );


CREATE TABLE `hsa_ali_applet_info`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `app_id`            varchar(50) NOT NULL COMMENT '小程序appid',
    `apply_public_key`  text        NOT NULL COMMENT '应用公钥',
    `apply_private_key` text        NOT NULL COMMENT '应用私钥',
    `ali_public_key`    text        NOT NULL COMMENT '支付宝公钥',
    `oper_subject_guid` varchar(50) NOT NULL COMMENT '主体guid',
    `enterprise_guid`   varchar(50) NOT NULL COMMENT '企业guid',
    `is_delete`         tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识',
    `gmt_create`        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `app_name`          varchar(100)         DEFAULT NULL,
    `app_logo`          varchar(1000)        DEFAULT NULL,
    `aes`               varchar(200)         DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;



CREATE TABLE `hsa_ali_authorizer_info`
(
    `id`                       bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`                     varchar(50) NOT NULL COMMENT '微页面guid',
    `gmt_create`               datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`             datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `oper_subject_guid`        varchar(50) NOT NULL COMMENT '运营主体GUID',
    `authorizer_app_Id`        varchar(300)         DEFAULT NULL,
    `authorizer_access_token`  varchar(300)         DEFAULT NULL,
    `authorizer_refresh_token` varchar(1000)        DEFAULT NULL,
    `qrcode_url`               varchar(1000)        DEFAULT NULL,
    `user_id`                  varchar(100)         DEFAULT NULL COMMENT '授权商户的user_id',
    `auth_app_id`              varchar(100)         DEFAULT NULL COMMENT '授权商户的appid',
    `is_deleted`               tinyint(4) DEFAULT NULL,
    `auth_start`               datetime             DEFAULT NULL COMMENT '授权开始时间',
    `expires_in`               int(11) DEFAULT NULL,
    `is_by_app_auth`           varchar(300)         DEFAULT NULL COMMENT '当商家选择全权委托授权时值为true，未选择时为false。',
    `status`                   varchar(100)         DEFAULT NULL COMMENT 'valid：有效状态；invalid：无效状态',
    `auth_end`                 datetime             DEFAULT NULL COMMENT '授权结束时间',
    `enterprise_guid`          varchar(100)         DEFAULT NULL,
    `notify_id`                varchar(100)         DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_guid` (`guid`)
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8mb4 COMMENT='支付宝商户授权表';



ALTER TABLE hsm_member_marketing_platform_db.hsa_operation_member_info
    ADD ali_head_img_url varchar(1024) NULL;
ALTER TABLE hsm_member_marketing_platform_db.hsa_operation_member_info
    ADD ali_nick_name varchar(100) NULL;
ALTER TABLE hsm_member_marketing_platform_db.hsa_operation_member_info
    ADD ali_state tinyint(1) NULL;


-- 订单聚合表
CREATE TABLE IF NOT EXISTS `hsa_aggregation_order`
(
    `id` bigint
(
    20
) NOT NULL AUTO_INCREMENT,
    `guid` varchar
(
    50
) NOT NULL COMMENT '订单聚合表guid',
    `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_delete` tinyint
(
    1
) NOT NULL DEFAULT '0' COMMENT '是否删除 0：false,1:true',
    `enterprise_guid` varchar
(
    50
) NOT NULL COMMENT '企业guid',
    `third_enterprise_guid` varchar
(
    50
) NOT NULL COMMENT '三方企业guid',
    `oper_subject_guid` varchar
(
    50
) NOT NULL COMMENT '运营主体guid',
    `third_oper_subject_guid` varchar
(
    50
) NOT NULL COMMENT '三方运营主体guid',
    `third_order_guid` varchar
(
    50
) DEFAULT NULL COMMENT '三方订单guid',
    `order_no` varchar
(
    50
) NOT NULL COMMENT '订单编号',
    `store_guid` varchar
(
    50
) DEFAULT NULL COMMENT '门店guid',
    `store_name` varchar
(
    50
) DEFAULT NULL COMMENT '门店名称',
    `order_fee` decimal
(
    15,
    2
) DEFAULT '0.00' COMMENT '订单金额',
    `actually_pay_fee` decimal
(
    15,
    2
) DEFAULT '0.00' COMMENT '实收金额',
    `state` int
(
    11
) DEFAULT NULL COMMENT '订单状态',
    `member_info_guid` varchar
(
    50
) DEFAULT NULL COMMENT '会员guid',
    `member_phone` varchar
(
    50
) DEFAULT NULL COMMENT '会员电话',
    `open_id` varchar
(
    100
) DEFAULT NULL COMMENT '会员微信/支付宝id',
    `business_type` char
(
    15
) NOT NULL COMMENT '订单业务类型',
    `device_type` int
(
    11
) NOT NULL COMMENT '订单设备类型',
    `checkin_time` datetime DEFAULT NULL COMMENT '下单时间',
    `checkout_time` datetime DEFAULT NULL COMMENT '结算时间',
    PRIMARY KEY
(
    `id`
)
                                                      USING BTREE,
    UNIQUE KEY `uk_guid`
(
    `guid`
)
                                                      USING BTREE,
    UNIQUE KEY `uk_third_order`
(
    `third_order_guid`,
    `business_type`
)
                                                      USING BTREE,
    KEY `oper_subject_member_idx`
(
    `oper_subject_guid`,
    `member_phone`,
    `open_id`
)
                                                      USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单聚合表';
