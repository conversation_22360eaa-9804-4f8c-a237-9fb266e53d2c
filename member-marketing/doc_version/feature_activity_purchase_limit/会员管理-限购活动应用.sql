-- 服务： base、market

use
hsm_member_marketing_platform_db;

DROP TABLE IF EXISTS `hsa_purchase_order`;

DROP TABLE IF EXISTS `hsa_purchase_order_commodity`;

-- 限量抢购活动
-- hsm_member_marketing_platform_db.hsa_purchase_order definition

-- hsm_member_marketing_platform_db.hsa_purchase_order definition

CREATE TABLE `hsa_purchase_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `purchase_id` varchar(50) DEFAULT NULL COMMENT '限购活动id',
  `purchase_guid` varchar(50) DEFAULT NULL COMMENT '限购活动guid',
  `purchase_name` varchar(100) DEFAULT NULL COMMENT '活动名称',
  `consumption_order_guid` varchar(50) DEFAULT NULL COMMENT '消费订单guid',
  `user_name` varchar(100) DEFAULT NULL COMMENT '下单用户',
  `phone` varchar(50) DEFAULT NULL COMMENT '下单手机号',
  `member_info_guid` varchar(100) DEFAULT NULL COMMENT '会员guid',
  `order_state` tinyint(4) DEFAULT NULL COMMENT '订单状态',
  `order_number` varchar(100) DEFAULT NULL COMMENT '订单号',
  `store_guid` varchar(50) DEFAULT NULL COMMENT '门店guid',
  `order_type` varchar(50) DEFAULT NULL COMMENT '业务类型',
  `order_time` datetime DEFAULT NULL COMMENT '订单时间',
  `is_delete` bigint(13) DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`),
  KEY `oper_subject_guid_idx` (`oper_subject_guid`) USING BTREE,
  KEY `hsa_purchase_order_order_number_IDX` (`order_number`) USING BTREE,
  KEY `hsa_purchase_order_member_info_guid_IDX` (`member_info_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb4 COMMENT='限量抢购活动-订单';


-- hsm_member_marketing_platform_db.hsa_purchase_order_commodity definition

CREATE TABLE `hsa_purchase_order_commodity` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `is_delete` bigint(13) NOT NULL DEFAULT '0' COMMENT '是否删除 0-否 1-是',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `purchase_order_guid` varchar(50) NOT NULL COMMENT '限购活动订单guid',
  `channel` varchar(50) DEFAULT NULL COMMENT '渠道',
  `commodity_id` varchar(50) DEFAULT NULL COMMENT '商品id',
  `commodity_code` varchar(50) NOT NULL COMMENT '商品编号',
  `commodity_num` int(10) NOT NULL DEFAULT '1' COMMENT '商品数量',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`),
  KEY `oper_subject_guid_idx` (`oper_subject_guid`) USING BTREE,
  KEY `hsa_purchase_order_commodity_purchase_order_guid_IDX` (`purchase_order_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COMMENT='限量抢购活动-订单商品';

