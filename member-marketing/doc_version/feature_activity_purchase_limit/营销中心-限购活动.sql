-- 服务： base、market

use hsm_member_marketing_center_platform_db;

DROP TABLE IF EXISTS `hsa_purchase_activity`;

DROP TABLE IF EXISTS `hsa_purchase_activity_commodity`;

-- hsm_member_marketing_center_platform_db.hsa_purchase_activity definition

CREATE TABLE `hsa_purchase_activity` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `is_delete` bigint(13) NOT NULL DEFAULT '0' COMMENT '是否删除 0-否 1-是',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `name` varchar(100) DEFAULT NULL COMMENT '活动名称',
  `state` tinyint(4) DEFAULT '1' COMMENT '活动状态',
  `published` tinyint(4) DEFAULT '0' COMMENT '发布状态：0-否 1-是',
  `activity_start_time` datetime DEFAULT NULL COMMENT '活动开始时间',
  `activity_end_time` datetime DEFAULT NULL COMMENT '活动结束时间',
  `apply_label_guid_json` mediumtext COMMENT '活动打标签',
  `purchase_rule_type` tinyint(4) DEFAULT '0' COMMENT '限购规则:0每人限购 1每单限购 2周期限购',
  `purchase_rule_period` tinyint(4) DEFAULT '0' COMMENT '周期限购：0 每日/人 ，1每周/人，2每月/人',
  `apply_business_json` varchar(50) DEFAULT NULL COMMENT '限购场景',
  `apply_store_type` tinyint(4) DEFAULT NULL COMMENT '应用门店类型：0全部 1部分',
  `apply_store_json` mediumtext COMMENT '应用门店',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`),
  KEY `oper_subject_guid_idx` (`oper_subject_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=100000 DEFAULT CHARSET=utf8mb4 COMMENT='限量抢购活动';


-- hsm_member_marketing_center_platform_db.hsa_purchase_activity_commodity definition

CREATE TABLE `hsa_purchase_activity_commodity` (
  `id` bigint(10) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `purchase_activity_guid` varchar(50) NOT NULL COMMENT '限购活动guid',
  `commodity_id` varchar(50) NOT NULL COMMENT '商品id',
  `commodity_name` varchar(50) DEFAULT NULL COMMENT '商品名称',
  `commodity_code` varchar(50) DEFAULT NULL COMMENT '商品编号',
  `commodity_price` varchar(50) DEFAULT NULL COMMENT '商品售价',
  `combo_type` tinyint(4) DEFAULT NULL COMMENT '单品1 固定2 可选3',
  `limit_number` int(10) NOT NULL DEFAULT '1' COMMENT '限购数量',
  `is_delete` bigint(13) NOT NULL DEFAULT '0' COMMENT '删除标志位，0-未删除，时间-已删除',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `channel` varchar(50) DEFAULT NULL COMMENT '渠道',
  `enterprise_guid` varchar(100) DEFAULT NULL COMMENT '企业guid',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`),
  KEY `hsa_purchase_activity_commodity_purchase_activity_guid_IDX` (`purchase_activity_guid`) USING BTREE,
  KEY `hsa_purchase_activity_commodity_commodity_code_IDX` (`commodity_code`,`channel`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='限量抢购活动商品';

-- 自增从10000
ALTER TABLE hsm_member_marketing_center_platform_db.hsa_purchase_activity AUTO_INCREMENT=100000;