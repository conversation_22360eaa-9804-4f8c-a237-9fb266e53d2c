use hsm_member_marketing_platform_db;

select * from hsa_apply_dictionaries had ;

-- 更新
delete from hsm_member_marketing_platform_db.hsa_apply_dictionaries;

INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified, channel)
VALUES(1, 'apply_terminal', 'mini_program', '微信小程序', '2022-01-24 19:56:13', '2022-01-24 19:56:13', NULL);
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified, channel)
VALUES(2, 'apply_terminal', 'pos', '一体机', '2022-01-24 19:56:29', '2022-01-24 19:56:29', NULL);
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified, channel)
VALUES(4, 'apply_business', '0', '食堂预订', '2022-01-24 19:57:05', '2022-01-24 19:57:05', 'pos');
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified, channel)
VALUES(6, 'apply_channel', 'pos', 'pos', '2022-01-24 19:57:36', '2022-01-24 19:57:36', NULL);
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified, channel)
VALUES(19, 'apply_business', '2', '堂食点餐', '2022-05-27 10:09:19', '2022-05-27 10:09:19', 'pos');
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified, channel)
VALUES(21, 'apply_business', '13', '会员商城', '2023-03-06 16:58:22', '2023-03-06 16:58:22', '会员商城');
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified, channel)
VALUES(23, 'apply_business', '4', '自营外卖', '2023-05-30 17:32:28', '2023-05-30 17:32:28', '自营外卖');
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified, channel)
VALUES(24, 'apply_business', '1', '堂食自助', '2023-05-31 09:58:45', '2023-05-31 09:58:45', '');
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified, channel)
VALUES(25, 'apply_business', '3', '快速收款', '2023-05-31 09:59:09', '2023-05-31 09:59:09', '');
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified, channel)
VALUES(26, 'apply_channel', 'mall', '商城渠道', '2023-05-31 10:23:28', '2023-05-31 10:23:28', NULL);
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified, channel)
VALUES(27, 'apply_terminal', '8', '点菜宝', '2023-08-09 09:45:36', '2023-08-09 09:45:36', NULL);
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified, channel)
VALUES(28, 'apply_terminal', '5', 'PAD', '2023-08-09 09:46:35', '2023-08-09 09:46:35', NULL);
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified, channel)
VALUES(29, 'apply_terminal', '30', '自助点餐屏', '2023-08-09 09:48:42', '2023-08-09 09:48:42', NULL);
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified, channel)
VALUES(30, 'apply_business', '14', '自助小碗菜', '2023-10-10 10:29:32', '2023-10-10 10:29:32', NULL);