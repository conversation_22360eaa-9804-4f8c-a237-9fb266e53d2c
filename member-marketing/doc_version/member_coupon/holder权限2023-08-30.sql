use gateway;
-- holder权限
 -- todo sql执行完请刷新holder权限redis 
INSERT INTO `gateway`.`hp_function_group`(`create_time`, `name`, `sequence`, `update_time`, `gmt_create`, `gmt_modified`) 
 VALUES 
(now(), '优惠劵',unix_timestamp(), now(), now(), now());

INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)  
VALUES 
(NOW(), '优惠劵', 1, 60, NOW(), 26,  
( SELECT id FROM hp_system WHERE name = '营销中心' AND identification = 'marketing_count'    
 ORDER BY gmt_modified DESC LIMIT 1 ), 
(select id from hp_function_group where name = '优惠劵'    
 order by gmt_modified desc limit 1)
,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '优惠券列表', NOW(), 
(select id from hp_function where name = '优惠劵' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '立即创建', NOW(), 
(select id from hp_function where name = '优惠劵' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '发布', NOW(), 
(select id from hp_function where name = '优惠劵' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '停止发放', NOW(), 
(select id from hp_function where name = '优惠劵' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '恢复发放', NOW(), 
(select id from hp_function where name = '优惠劵' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '编辑', NOW(), 
(select id from hp_function where name = '优惠劵' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '复制', NOW(), 
(select id from hp_function where name = '优惠劵' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '删除', NOW(), 
(select id from hp_function where name = '优惠劵' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)  
VALUES 
(NOW(), '发放统计', 1, 60, NOW(), 26,  
( SELECT id FROM hp_system WHERE name = '营销中心' AND identification = 'marketing_count'    
 ORDER BY gmt_modified DESC LIMIT 1 ), 
(select id from hp_function_group where name = '优惠劵'    
 order by gmt_modified desc limit 1)
,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '查看发放统计', NOW(), 
(select id from hp_function where name = '发放统计' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '导出发放明细', NOW(), 
(select id from hp_function where name = '发放统计' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '标记使用', NOW(), 
(select id from hp_function where name = '发放统计' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '券作废', NOW(), 
(select id from hp_function where name = '发放统计' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)  
VALUES 
(NOW(), '核销统计', 1, 60, NOW(), 26,  
( SELECT id FROM hp_system WHERE name = '营销中心' AND identification = 'marketing_count'    
 ORDER BY gmt_modified DESC LIMIT 1 ), 
(select id from hp_function_group where name = '优惠劵'    
 order by gmt_modified desc limit 1)
,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '查看核销统计', NOW(), 
(select id from hp_function where name = '核销统计' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '导出核销明细', NOW(), 
(select id from hp_function where name = '核销统计' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO `gateway`.`hp_function_group`(`create_time`, `name`, `sequence`, `update_time`, `gmt_create`, `gmt_modified`) 
 VALUES 
(now(), '发劵宝',unix_timestamp(), now(), now(), now());

INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)  
VALUES 
(NOW(), '发劵宝', 1, 60, NOW(), 26,  
( SELECT id FROM hp_system WHERE name = '营销中心' AND identification = 'marketing_count'    
 ORDER BY gmt_modified DESC LIMIT 1 ), 
(select id from hp_function_group where name = '发劵宝'    
 order by gmt_modified desc limit 1)
,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '发券宝列表', NOW(), 
(select id from hp_function where name = '发劵宝' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '立即创建', NOW(), 
(select id from hp_function where name = '发劵宝' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '发布', NOW(), 
(select id from hp_function where name = '发劵宝' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '暂停', NOW(), 
(select id from hp_function where name = '发劵宝' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '开启', NOW(), 
(select id from hp_function where name = '发劵宝' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '编辑', NOW(), 
(select id from hp_function where name = '发劵宝' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '复制', NOW(), 
(select id from hp_function where name = '发劵宝' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '删除', NOW(), 
(select id from hp_function where name = '发劵宝' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)  
VALUES 
(NOW(), '活动统计', 1, 60, NOW(), 26,  
( SELECT id FROM hp_system WHERE name = '营销中心' AND identification = 'marketing_count'    
 ORDER BY gmt_modified DESC LIMIT 1 ), 
(select id from hp_function_group where name = '发劵宝'    
 order by gmt_modified desc limit 1)
,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '查看发放统计', NOW(), 
(select id from hp_function where name = '活动统计' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '导出发放明细', NOW(), 
(select id from hp_function where name = '活动统计' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '补发功能', NOW(), 
(select id from hp_function where name = '活动统计' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '查看优惠券', NOW(), 
(select id from hp_function where name = '会员账户详情' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '会员管理' AND identification = 'member_marketing' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '导出优惠券', NOW(), 
(select id from hp_function where name = '会员账户详情' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '会员管理' AND identification = 'member_marketing' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '导出积分明细', NOW(), 
(select id from hp_function where name = '会员账户详情' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '会员管理' AND identification = 'member_marketing' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '导出挂账明细', NOW(), 
(select id from hp_function where name = '会员账户详情' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '会员管理' AND identification = 'member_marketing' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());