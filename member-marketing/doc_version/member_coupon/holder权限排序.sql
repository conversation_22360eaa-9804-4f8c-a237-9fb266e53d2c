use gateway;

-- 会员账户详情中的导出
update `gateway`.`hp_permission` set name = '导出成长明细' where function_id = '683' and name = '导出';

-- 会员账户，详情排序(账户详情functionId: 683,批量替换sql)
update `gateway`.`hp_permission` set priority = 1    where function_id = '683' and name = '查看会员账户详情';
update `gateway`.`hp_permission` set priority = 2    where function_id = '683' and name = '禁用';
update `gateway`.`hp_permission` set priority = 3    where function_id = '683' and name = '启用';
update `gateway`.`hp_permission` set priority = 4    where function_id = '683' and name = '修改手机号';
update `gateway`.`hp_permission` set priority = 5    where function_id = '683' and name = '修改钱包密码';
update `gateway`.`hp_permission` set priority = 6    where function_id = '683' and name = '修改归属门店';
update `gateway`.`hp_permission` set priority = 7    where function_id = '683' and name = '解绑归属门店';
update `gateway`.`hp_permission` set priority = 8    where function_id = '683' and name = '查看门店变更记录';
update `gateway`.`hp_permission` set priority = 9    where function_id = '683' and name = '编辑基本信息';
update `gateway`.`hp_permission` set priority = 10   where function_id = '683' and name = '查看会员标签';
update `gateway`.`hp_permission` set priority = 11   where function_id = '683' and name = '关联标签';
update `gateway`.`hp_permission` set priority = 12   where function_id = '683' and name = '查看关联标签记录';
update `gateway`.`hp_permission` set priority = 13   where function_id = '683' and name = '自动关联';
update `gateway`.`hp_permission` set priority = 14   where function_id = '683' and name = '解除关联';
update `gateway`.`hp_permission` set priority = 15   where function_id = '683' and name = '查看成长明细';
update `gateway`.`hp_permission` set priority = 16   where function_id = '683' and name = '导出成长明细';
update `gateway`.`hp_permission` set priority = 17   where function_id = '683' and name = '调整成长值';
update `gateway`.`hp_permission` set priority = 18   where function_id = '683' and name = '查看权益记录';
update `gateway`.`hp_permission` set priority = 19   where function_id = '683' and name = '查看积分明细';
update `gateway`.`hp_permission` set priority = 20   where function_id = '683' and name = '导出积分明细';
update `gateway`.`hp_permission` set priority = 21   where function_id = '683' and name = '调整积分';
update `gateway`.`hp_permission` set priority = 22   where function_id = '683' and name = '查看账户钱包';
update `gateway`.`hp_permission` set priority = 23   where function_id = '683' and name = '调整钱包余额';
update `gateway`.`hp_permission` set priority = 24   where function_id = '683' and name = '查看挂账账户';
update `gateway`.`hp_permission` set priority = 25   where function_id = '683' and name = '导出挂账明细';
update `gateway`.`hp_permission` set priority = 26   where function_id = '683' and name = '挂账明细';
update `gateway`.`hp_permission` set priority = 27   where function_id = '683' and name = '查看账户会员卡';
update `gateway`.`hp_permission` set priority = 28   where function_id = '683' and name = '开通会员卡';
update `gateway`.`hp_permission` set priority = 29   where function_id = '683' and name = '调整卡余额';
update `gateway`.`hp_permission` set priority = 30   where function_id = '683' and name = '调整适用范围';
update `gateway`.`hp_permission` set priority = 31   where function_id = '683' and name = '修改卡密码';
update `gateway`.`hp_permission` set priority = 32   where function_id = '683' and name = '冻结';
update `gateway`.`hp_permission` set priority = 33   where function_id = '683' and name = '解冻';
update `gateway`.`hp_permission` set priority = 34   where function_id = '683' and name = '查看会员卡详情';
update `gateway`.`hp_permission` set priority = 35   where function_id = '683' and name = '绑定实体卡';
update `gateway`.`hp_permission` set priority = 36   where function_id = '683' and name = '开通电子卡';
update `gateway`.`hp_permission` set priority = 37   where function_id = '683' and name = '实体卡退卡';
update `gateway`.`hp_permission` set priority = 38   where function_id = '683' and name = '查看优惠券';
update `gateway`.`hp_permission` set priority = 39   where function_id = '683' and name = '导出优惠券';
update `gateway`.`hp_permission` set priority = 40   where function_id = '683' and name = '查看交易明细';
update `gateway`.`hp_permission` set priority = 41   where function_id = '683' and name = '消费订单';
update `gateway`.`hp_permission` set priority = 42   where function_id = '683' and name = '充值订单';
update `gateway`.`hp_permission` set priority = 43   where function_id = '683' and name = '退款';
