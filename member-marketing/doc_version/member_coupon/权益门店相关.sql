-- 新增权益业务类型字段
ALTER TABLE hsm_member_marketing_platform_db.hsa_grade_equities
    ADD business_type tinyint(1) NULL;

-- 新增区分全部部分门店字段
ALTER TABLE hsm_member_marketing_platform_db.hsa_grade_equities
    ADD applicable_all_store tinyint(1) NULL;

-- 处理老数据权益为默认等级类型
UPDATE hsm_member_marketing_platform_db.hsa_grade_equities
set business_type= 0
where business_type is null;

-- 初始化默认值
ALTER TABLE hsm_member_marketing_platform_db.hsa_grade_equities MODIFY COLUMN business_type tinyint(1) DEFAULT 0 NOT NULL;

--初始化全部门店默认值
ALTER TABLE hsm_member_marketing_platform_db.hsa_business_equities MODIFY COLUMN applicable_all_store tinyint(1) DEFAULT 1 NOT NULL;


-- 删除权益叠加字段
ALTER TABLE hsm_member_marketing_platform_db.hsa_equities_rule DROP COLUMN discounts_superposition_set;
ALTER TABLE hsm_member_marketing_platform_db.hsa_equities_rule DROP COLUMN discounts_superposition_required;
ALTER TABLE hsm_member_marketing_platform_db.hsa_equities_rule DROP COLUMN discounts_superposition_type;


-- 权益中心新增字段
ALTER TABLE hsm_member_marketing_platform_db.hsa_equities_rule
    ADD store_required tinyint(1) NULL;

-- 新增权益门店表
CREATE TABLE `hsa_equities_store_rule`
(
    `id`                bigint(10) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `guid`              varchar(50) NOT NULL COMMENT 'guid',
    `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
    `store_guid`        varchar(50)          DEFAULT NULL COMMENT '门店guid',
    `store_name`        varchar(50)          DEFAULT NULL COMMENT '门店名称',
    `store_number`      varchar(50)          DEFAULT NULL COMMENT '门店编号',
    `time`              varchar(50)          DEFAULT NULL COMMENT '营业时间',
    `address`           varchar(50)          DEFAULT NULL COMMENT '营业地址',
    `address_point`     varchar(50)          DEFAULT NULL COMMENT '经纬度',
    `gmt_create`        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `store_logo`        varchar(1000)        DEFAULT NULL COMMENT '门店图片',
    `is_delete`         tinyint(4) DEFAULT NULL,
    `equities_guid`     varchar(50)          DEFAULT NULL,
    `effective`         tinyint(4) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `hsa_equities_store_rule_guid_IDX` (`guid`) USING BTREE,
    KEY                 `hsa_equities_store_rule_equities_guid_IDX` (`equities_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=383 DEFAULT CHARSET=utf8mb4;
