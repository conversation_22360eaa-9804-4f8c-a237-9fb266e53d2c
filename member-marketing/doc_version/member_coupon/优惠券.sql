ALTER TABLE hsm_member_marketing_platform_db.hsa_apply_dictionaries
    ADD channel varchar(128) NULL;

ALTER TABLE hsm_member_marketing_center_platform_db.hsa_coupon_commodity
    ADD enterprise_guid varchar(100) NULL;


-- hsm_member_marketing_platform_db.hsa_member_coupon_link definition

CREATE TABLE `hsa_member_coupon_link`
(
    `id`                          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `guid`                        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT 'guid',
    `is_delete`                   bigint(13) NOT NULL DEFAULT '0' COMMENT '是否删除 0-否 1-是',
    `gmt_create`                  datetime                                                         DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`                datetime                                                         DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `oper_subject_guid`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '运营主体guid',
    `coupon_name`                 varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci  DEFAULT NULL COMMENT '活动名称',
    `user_name`                   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci  DEFAULT NULL COMMENT '0 无门槛 1 满多少可用',
    `source`                      tinyint(4) DEFAULT NULL,
    `coupon_package_type`         tinyint(4) DEFAULT NULL COMMENT '* 使用有效期类型\n* 0 自定义固定时间（开启时需判断是否失效）\n* 1 领取后天/小时生效，有效期多少天/小时（开启时无需判断是否失效）\n* 2 领取后天/小时生效，有效期固定时间（开启时需判断是否失效）',
    `coupon_package_code`         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci   DEFAULT NULL,
    `store_guid`                  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci   DEFAULT NULL,
    `store_name`                  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci  DEFAULT NULL COMMENT '有效期时间值',
    `state`                       tinyint(4) DEFAULT NULL,
    `coupon_effective_start_time` datetime                                                         DEFAULT NULL,
    `coupon_effective_end_time`   datetime                                                         DEFAULT NULL,
    `apply_date_limited_json`     varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci DEFAULT NULL,
    `apply_business`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci   DEFAULT NULL,
    `remark`                      varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci DEFAULT NULL,
    `apply_terminal_json`         varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci  DEFAULT NULL,
    `apply_label_guid_json`       varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci  DEFAULT NULL,
    `applicable_all_store`        tinyint(4) DEFAULT NULL,
    `apply_commodity`             tinyint(4) DEFAULT NULL,
    `threshold_amount`            decimal(12, 2)                                                   DEFAULT NULL,
    `coupon_code`                 varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci   DEFAULT NULL,
    `coupon_type`                 tinyint(4) DEFAULT NULL,
    `apply_date_limited`          tinyint(4) DEFAULT NULL,
    `apply_time_limited_json`     varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci DEFAULT NULL,
    `apply_business_json`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci  DEFAULT NULL,
    `apply_time_limited_type`     tinyint(4) DEFAULT NULL,
    `reach_time`                  datetime                                                         DEFAULT NULL,
    `applicable_all_store_json`   mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci,
    `apply_commodity_json`        mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci,
    `member_phone`                varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci   DEFAULT NULL COMMENT '会员手机号',
    `coupon_package_name`         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci   DEFAULT NULL,
    `code`                        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci  DEFAULT NULL,
    `threshold_type`              tinyint(4) DEFAULT NULL,
    `discount_amount`             decimal(12, 2)                                                   DEFAULT NULL,
    `is_expire_remind`            tinyint(4) DEFAULT '0',
    `enterprise_guid`             varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci  DEFAULT NULL,
    `member_guid`                 varchar(100)                                                     DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_guid` (`guid`),
    KEY                           `hsa_member_coupon_link_member_guid_IDX` (`member_guid`) USING BTREE,
    KEY                           `hsa_member_coupon_link_coupon_name_IDX` (`coupon_name`,`coupon_package_code`,`user_name`,`member_phone`,`coupon_package_name`,`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1441447 DEFAULT CHARSET=utf8mb4 COMMENT='充值赠送活动';