# 结算中心服务

//历史主体初始化：结算项
re post  https://member-re.holderzone.cn/base/goalgo_callback/initialize_old_subject_data
prod post  https://member.holderzone.com/base/goalgo_callback/initialize_old_subject_data

nginx配置
1. https://member-center.holderzone.com/settlement

服务 分支 feature_settlement_rights
1. holder-member-gateway
2. holder-member-base
3. holder-member-marketing
4. holder-member-settlement (新增服务：port 8095)
5. holder-member-job-executor
6. holder-member-queue
其他：优化过？
7. holder-member-commodity
8. holder-member-mall
9. holder-member-mall-tool



## yaml
增加了 
1. member-settlement-dev.yaml
2. datasource-settlement.yaml
3. common-feign.yaml
4. 修改 member-gateway-dev.yaml


gateway 配置增加
```yaml
# gateway
- id: member-settlement-route
    uri: lb://member-settlement
    predicates:
      - Path=/settlement/**
    filters:
      - StripPrefix=1
```

commom-feign 配置增加
```yaml

feign:
  pay: http://test-holder-member-pay:8913
  store: https://sale-crm.holderzone.cn
  crm: https://sale-crm.holderzone.cn
  base: https://member-test.holderzone.cn
  goalgo: http://gateway.hp.goalgo.cn:42770
  saas: https://mch-sit.holderzone.cn
  zhuancan: https://zhuancan-sit.holderzone.cn
  mall: https://member-mall-test.holderzone.cn
  cloud: https://cloud-sit.holderzone.cn/gateway/cloud
  client:
    config:
      default: # default全局的配置
        connectTimeout: 90000
        readTimeout: 600000
        loggerLevel: BASIC # 日志级别，BASIC就是基本的请求和响应信息
  httpclient:
    enabled: true # 开启feign对HttpClient的支持
    max-connections: 2000 # 默认值200
    max-connections-per-route=: 500 # 默认值为50


```

## tomcat优化
      
gateway、base、marketing、settlement服务的配置
```yaml

# 替换之前的tomcat配置
server:
  tomcat:
    # 最大工作线程数，默认200, 4核8g内存，线程数经验值800
    # 操作系统做线程之间的切换调度是有系统开销的，所以不是越多越好。
    max-threads: 2000      # 目前起作用，测试到上万都没问题
    # 最小工作空闲线程数，默认10, 适当增大一些，以便应对突然增长的访问量
    min-spare-threads: 100 # 总保持备用线程数
    accept-count: 2000     # 等待队列长度，默认100
    max-connections: 2000  # 最大连接数，NIO默式默认 10000，其它为 maxThreads
```