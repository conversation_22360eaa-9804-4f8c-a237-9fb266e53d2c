use hsm_member_marketing_platform_db;






-- 等级权益id： 下单锁定权益规则
ALTER TABLE hsm_member_marketing_platform_db.hsa_member_grade_price_detail ADD equities_id varchar(50) CHARACTER SET
utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '会员等级权益ID';

CREATE INDEX hsa_member_grade_price_detail_member_info_guid_IDX
	USING BTREE ON
hsm_member_marketing_platform_db.hsa_member_grade_price_detail (member_info_guid, order_number);

-- 精度
ALTER TABLE hsm_member_marketing_platform_db.hsa_member_grade_price_detail MODIFY COLUMN discount_dynamics decimal(12,3) NULL;

-- 权益
ALTER TABLE hsm_member_marketing_platform_db.hsa_business_equities 
ADD version_id varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 0 NOT NULL COMMENT '版本id';


-- 权益商品
ALTER TABLE hsm_member_marketing_platform_db.hsa_grade_rights_commodity_rule 
ADD grade_equities_version_id varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 0 NOT NULL COMMENT '权益版本id';

-- 权益门店
ALTER TABLE hsm_member_marketing_platform_db.hsa_equities_store_rule 
ADD equities_version_id varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 0 NOT NULL COMMENT '权益版本id';





-- 积分消耗规则，假删
-- ALTER TABLE hsm_member_marketing_platform_db.hsa_integral_consume_rule ADD is_delete tinyint(4) DEFAULT 0 NOT NULL COMMENT '0正常 1删除';

ALTER TABLE hsm_member_marketing_platform_db.hsa_member_integral_deduct_detail ADD consume_rule_guid varchar(50) CHARACTER SET
utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '积分消耗规则guid';

ALTER TABLE hsm_member_marketing_platform_db.hsa_member_integral_deduct_detail ADD consume_version_id varchar(50) CHARACTER SET
utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '积分消耗规则版本id';

ALTER TABLE hsm_member_marketing_platform_db.hsa_integral_consume_rule ADD version_id varchar(50) CHARACTER SET
utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 0 NOT NULL COMMENT '版本id';

ALTER TABLE hsm_member_marketing_platform_db.hsa_integral_deduct_commodity ADD version_id varchar(50) CHARACTER SET
utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 0 NOT NULL COMMENT '版本id';


DROP TABLE IF EXISTS `hsa_integral_consume_rule_history`;

DROP TABLE IF EXISTS `hsa_integral_deduct_commodity_history`;

-- hsm_member_marketing_platform_db.hsa_integral_consume_rule definition

CREATE TABLE `hsa_integral_consume_rule_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) NOT NULL,
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',
  `is_for_now` tinyint(4) DEFAULT NULL COMMENT '是否开启 0 关闭 1 开启',
  `integral_num` int(20) DEFAULT '1' COMMENT '每多少积分',
  `for_now_money` decimal(12,2) DEFAULT NULL COMMENT '抵扣金额',
  `single_order_money` decimal(12,2) DEFAULT NULL COMMENT '单笔订单金额不足多少 不能使用积分',
  `single_order_type` tinyint(4) DEFAULT NULL COMMENT '单笔使用上限类型 0 不限制 1 上限金额 2 上限比例',
  `single_order_value` decimal(12,2) DEFAULT NULL COMMENT '单笔使用上限 0 不限制 1 上限金额 2 上限比例',
  `period_discount_type` tinyint(4) unsigned zerofill DEFAULT NULL COMMENT '周期使用上限类型 -1 不限制 0：天 1：周 2：月 3：年',
  `period_discount_limited` decimal(12,2) DEFAULT NULL COMMENT '周期使用上限',
  `apply_business_json` varchar(100) DEFAULT NULL COMMENT '适用业务json',
  `terminal` varchar(100) DEFAULT NULL COMMENT '终端',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `serviceable_item` varchar(50) DEFAULT NULL COMMENT '适用项目',
  `apply_goods_type` tinyint(4) DEFAULT '0' COMMENT '1：全部商品适用 0：适用商品 1：不适用商品',
  `operator_name` varchar(50) DEFAULT '0',
  `is_limit` tinyint(4) DEFAULT NULL COMMENT '周期使用上限类型是否限制 0 限制 1 不限制',
  `version_id` varchar(50) NOT NULL DEFAULT '0' COMMENT '版本id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `uk_guid` (`guid`) USING BTREE,
  KEY `index_label_setting_guid` (`is_for_now`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='积分规则历史表';


-- hsm_member_marketing_platform_db.hsa_integral_deduct_commodity definition

CREATE TABLE `hsa_integral_deduct_commodity_history` (
  `id` bigint(10) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `commodity_id` varchar(50) NOT NULL COMMENT '商品id',
  `commodity_name` varchar(50) DEFAULT NULL COMMENT '商品名称',
  `commodity_code` varchar(50) DEFAULT NULL COMMENT '商品编号',
  `commodity_price` varchar(50) DEFAULT NULL COMMENT '商品售价',
  `combo_type` tinyint(4) DEFAULT NULL COMMENT '单品1 固定2 可选3',
  `is_delete` tinyint(4) DEFAULT NULL,
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `integral_consume_rule_guid` varchar(50) DEFAULT NULL,
  `business_type` tinyint(4) DEFAULT NULL,
  `version_id` varchar(50) NOT NULL DEFAULT '0' COMMENT '版本id',
  PRIMARY KEY (`id`),
  KEY `uk_guid` (`guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;