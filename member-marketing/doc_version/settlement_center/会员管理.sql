
use hsm_member_marketing_platform_db;

ALTER TABLE hsm_member_marketing_platform_db.hsa_member_grade_price_detail MODIFY COLUMN member_info_grade_guid varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '等级guid  或 会员卡guid';
ALTER TABLE hsm_member_marketing_platform_db.hsa_member_grade_price_detail ADD business_type tinyint(1) DEFAULT 0 NOT NULL COMMENT '0等级 1会员卡';

CREATE INDEX hsa_member_coupon_link_oper_subject_guid_IDX USING BTREE ON hsm_member_marketing_platform_db.hsa_member_coupon_link (oper_subject_guid);


-- 同步门店
DROP TABLE IF EXISTS `hsa_sync_store`;
-- 订单优惠记录
DROP TABLE IF EXISTS `hsa_member_order_discount`;
-- 优惠券使用记录
DROP TABLE IF EXISTS `hsa_member_coupon_use`;

DROP TABLE IF EXISTS `hsa_system_dict`;
-- 订单商品优惠
--DROP TABLE IF EXISTS `hsa_member_consumption_commodity_discount`;

CREATE TABLE `hsa_sync_store` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `is_delete` bigint(13) NOT NULL DEFAULT '0' COMMENT '删除标志位，0-未删除，时间-已删除',
  `gmt_create` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `gmt_modified` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `store_guid` varchar(50) NOT NULL COMMENT '门店guid',
  `store_name` varchar(50) NOT NULL COMMENT '门店',
  `store_number` varchar(50) NOT NULL COMMENT '门店编号',
  `brand_id` varchar(50) NOT NULL COMMENT '品牌id',
  `brand_name` varchar(50) NOT NULL COMMENT '品牌',
  `contact` varchar(50) NOT NULL COMMENT '联系人',
  `phone_number` varchar(50) NOT NULL COMMENT '联系电话',
  `address` varchar(100) DEFAULT NULL COMMENT '营业地址',
  `address_point` varchar(100) DEFAULT NULL COMMENT '经纬度',
  `store_logo` varchar(500) DEFAULT NULL COMMENT 'logo',
  `status` varchar(500) DEFAULT NULL COMMENT '状态',
  `time` varchar(100) DEFAULT NULL COMMENT '营业时间',
  `is_monday` tinyint(4) NOT NULL COMMENT '营业时间：周一',
  `is_tuesday` tinyint(4) NOT NULL COMMENT '营业时间：周二',
  `is_wednesday` tinyint(4) NOT NULL COMMENT '营业时间：周三一',
  `is_thursday` tinyint(4) NOT NULL COMMENT '营业时间：周四',
  `is_friday` tinyint(4) NOT NULL COMMENT '营业时间：周五',
  `is_saturday` tinyint(4) NOT NULL COMMENT '营业时间：周六一',
  `is_sunday` tinyint(4) NOT NULL COMMENT '营业时间：周日',
  PRIMARY KEY (`id`),
  KEY `oper_subject_guid_idx` (`oper_subject_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='同步门店';



CREATE TABLE `hsa_member_order_discount` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` bigint(13) NOT NULL DEFAULT '0' COMMENT '删除标志位，0-未删除，时间-已删除',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `order_number` varchar(50) DEFAULT NULL COMMENT '会员消费记录GUID',
  `discount_option` tinyint(4) DEFAULT '0' COMMENT '优惠具体优惠项： SettlementDiscountOptionEnum',
  `discount_guid` varchar(50) NOT NULL DEFAULT '' COMMENT '优惠guid',
  `discount_name` varchar(50) NOT NULL DEFAULT '' COMMENT '优惠名称',
  `discount_id` varchar(50) NOT NULL DEFAULT '' COMMENT '优惠项主键数组,eg:优惠券id',
  `discount_dynamic` varchar(50) NOT NULL DEFAULT '' COMMENT '优惠力度',
  `discount_num` int(10) NOT NULL DEFAULT '1' COMMENT '数量',
  `discount_fee` decimal(10,2) DEFAULT '0.00' COMMENT '优惠总额',
  `discount_state` int(11) DEFAULT '0' COMMENT '折扣状态 0表示正常，1表示反结账折扣',
  `coupon_rollback` tinyint(4) DEFAULT '1' COMMENT '退款是否退优惠：0否 1是',
  `member_guid` varchar(50) DEFAULT NULL COMMENT '会员guid',
  PRIMARY KEY (`id`),
  KEY `idx_gmt` (`gmt_create`) USING BTREE,
  KEY `hsa_member_order_discount_order_number_IDX` (`order_number`) USING BTREE,
  KEY `hsa_member_order_discount_discount_option_IDX` (`discount_option`,`discount_guid`,`discount_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='订单优惠记录';

CREATE TABLE `hsa_member_coupon_use` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` bigint(13) NOT NULL DEFAULT '0' COMMENT '删除标志位，0-未删除，时间-已删除',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `member_coupon_link_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '优惠券guid',
  `lock_time` datetime NOT NULL COMMENT '锁定时间',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `order_number` varchar(50) DEFAULT NULL COMMENT '订单号',
  `store_guid` varchar(50) DEFAULT NULL,
  `store_name` varchar(100) DEFAULT NULL COMMENT '门店名称',
  `source` tinyint(4) DEFAULT '0' COMMENT '核销来源：sourceTypeEnum',
  `discount_amount` decimal(10,2) DEFAULT '0.00' COMMENT '优惠金额',
  `order_paid_amount` decimal(10,2) DEFAULT '0.00' COMMENT '订单实付金额',
  `operator_account_name` varchar(128) DEFAULT NULL COMMENT '操作人员账号名字',
  PRIMARY KEY (`id`),
  KEY `idx_gmt` (`gmt_create`) USING BTREE,
  KEY `hsa_member_coupon_use_order_number_IDX` (`order_number`) USING BTREE,
  KEY `hsa_member_coupon_use_member_coupon_link_guid_IDX` (`member_coupon_link_guid`) USING BTREE,
  KEY `hsa_member_coupon_use_lock_time_IDX` (`lock_time`,`pay_time`,`store_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COMMENT='优惠券使用记录';

--CREATE TABLE `hsa_member_consumption_commodity_discount` (
--  `id` bigint(20) NOT NULL AUTO_INCREMENT,
--  `guid` varchar(50) NOT NULL COMMENT 'guid',
--  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
--  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
--  `is_delete` bigint(13) NOT NULL DEFAULT '0' COMMENT '删除标志位，0-未删除，时间-已删除',
--  `member_consumption_guid` varchar(50) DEFAULT NULL COMMENT '会员消费记录GUID',
--  `discount_option` tinyint(4) DEFAULT '0' COMMENT '优惠具体优惠项： SettlementDiscountOptionEnum',
--  `commodity_id` varchar(50) NOT NULL DEFAULT '' COMMENT '商品id',
--  `commodity_code` varchar(50) NOT NULL DEFAULT '' COMMENT '商品编码',
--  `commodity_num` int(10) NOT NULL DEFAULT '1' COMMENT '商品数量',
--  `commodity_price` decimal(10,2) NOT NULL DEFAULT '0' COMMENT '商品单价',
--  `commodity_name` varchar(50) DEFAULT '' COMMENT '商品名称',
--  `commodity_discount_json` varchar(512) DEFAULT NULL COMMENT '商品优惠明细',
--  PRIMARY KEY (`id`),
--  KEY `idx_gmt` (`gmt_create`) USING BTREE
--) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单优惠记录';


CREATE TABLE `hsa_system_dict` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `system` tinyint(4) NOT NULL default 0 comment '系统',
  `field` varchar(20) NOT NULL,
  `name` varchar(50) NOT NULL,
  `key` varchar(50) NOT NULL,
  `value` varchar(50) NOT NULL,
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='系统数据字典表';

INSERT INTO hsm_member_marketing_platform_db.hsa_system_dict
(id, field, `system`, name, `key`, value, gmt_create, gmt_modified, is_delete)
VALUES(1, 'couponUseSource', 0, '核销渠道', '53', '小程序', '2023-11-23 16:33:38', '2023-11-23 16:33:38', 0);
INSERT INTO hsm_member_marketing_platform_db.hsa_system_dict
(id, field, `system`, name, `key`, value, gmt_create, gmt_modified, is_delete)
VALUES(2, 'couponUseSource', 0, '核销渠道', '2', '一体机', '2023-11-23 16:33:38', '2023-11-23 16:33:38', 0);
INSERT INTO hsm_member_marketing_platform_db.hsa_system_dict
(id, field, `system`, name, `key`, value, gmt_create, gmt_modified, is_delete)
VALUES(3, 'couponUseSource', 0, '核销渠道', '0', '管理后台', '2023-11-23 16:33:38', '2023-11-23 16:33:38', 0);


