use hsm_member_marketing_platform_db;


DROP TABLE IF EXISTS `hsa_equities_init`;


CREATE TABLE `hsa_equities_init` (
	`id` bigint(20) NOT NULL AUTO_INCREMENT,
	`guid` varchar(50) NOT NULL COMMENT 'guid',
    `gmt_create` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  	`gmt_modified` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
     `is_delete` bigint(13) NOT NULL DEFAULT '0' COMMENT '删除标志位，0-未删除，时间-已删除',
	`enterprise_guid` varchar(50) NOT NULL COMMENT '企业GUID',
	`is_init` tinyint(4) NOT NULL DEFAULT 0 COMMENT '0 未初始化，1已初始化', 
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`),
  KEY `enterprise_guid_idx` (`enterprise_guid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='权益初始化';


-- 初始化历史数据
INSERT into hsa_equities_init(guid,enterprise_guid,is_init)
select e.guid,e.enterprise_guid,1 from hsa_equities_info e
left join hsa_equities_init i 
on e.enterprise_guid = i.enterprise_guid
where i.id is null
group by enterprise_guid

-- 优化
CREATE INDEX hsa_integral_detail_member_info_guid_IDX 
USING BTREE ON hsm_member_marketing_platform_db.hsa_integral_detail (member_info_guid);



CREATE INDEX hsa_member_grade_rights_record_member_info_guid_IDX 
USING BTREE ON hsm_member_marketing_platform_db.hsa_member_grade_rights_record (member_info_guid);

CREATE INDEX hsa_label_setting_oper_subject_guid_IDX 
USING BTREE ON hsm_member_marketing_platform_db.hsa_label_setting (oper_subject_guid);

CREATE INDEX hsa_member_grade_info_oper_subject_guid_IDX USING BTREE ON hsm_member_marketing_platform_db.hsa_member_grade_info (oper_subject_guid);




CREATE INDEX hsa_business_equities_member_grade_info_guid_IDX USING BTREE ON hsm_member_marketing_platform_db.hsa_business_equities (member_grade_info_guid);
CREATE INDEX hsa_business_equities_oper_subject_guid_IDX USING BTREE ON hsm_member_marketing_platform_db.hsa_business_equities (oper_subject_guid);

CREATE INDEX hsa_grade_gift_bag_member_grade_info_guid_IDX USING BTREE ON hsm_member_marketing_platform_db.hsa_grade_gift_bag (member_grade_info_guid);

CREATE INDEX hsa_member_label_operation_member_info_guid_IDX 
USING BTREE ON hsm_member_marketing_platform_db.hsa_member_label (operation_member_info_guid);

CREATE INDEX hsa_recharge_gift_threshold_recharge_gift_guid_IDX 
USING BTREE ON hsm_member_marketing_center_platform_db.hsa_recharge_gift_threshold (recharge_gift_guid);

CREATE INDEX hsa_recharge_gift_store_recharge_gift_guid_IDX 
USING BTREE ON hsm_member_marketing_center_platform_db.hsa_recharge_gift_store (recharge_gift_guid);

CREATE INDEX hsa_card_recharge_gift_detail_activity_guid_IDX USING BTREE ON hsm_member_marketing_platform_db.hsa_card_recharge_gift_detail (activity_guid);


CREATE INDEX hsa_grade_rights_commodity_rule_grade_equities_guid_IDX 
USING BTREE ON hsm_member_marketing_platform_db.hsa_grade_rights_commodity_rule (grade_equities_guid,grade_equities_version_id);

CREATE INDEX hsa_store_card_rule_member_info_card_guid_IDX 
USING BTREE ON hsm_member_marketing_platform_db.hsa_store_card_rule (member_info_card_guid);

CREATE INDEX hsa_integral_consume_rule_oper_subject_guid_IDX 
USING BTREE ON hsm_member_marketing_platform_db.hsa_integral_consume_rule (oper_subject_guid);

CREATE INDEX hsa_integral_deduct_commodity_oper_subject_guid_IDX 
USING BTREE ON hsm_member_marketing_platform_db.hsa_integral_deduct_commodity (oper_subject_guid);


CREATE INDEX hsa_member_integral_deduct_detail_member_info_guid_IDX 
USING BTREE ON hsm_member_marketing_platform_db.hsa_member_integral_deduct_detail (member_info_guid);

CREATE INDEX hsa_member_integral_deduct_detail_order_number_IDX 
USING BTREE ON hsm_member_marketing_platform_db.hsa_member_integral_deduct_detail (order_number);


CREATE INDEX hsa_controlled_grade_state_oper_subject_guid_IDX 
USING BTREE ON hsm_member_marketing_platform_db.hsa_controlled_grade_state (oper_subject_guid);

CREATE INDEX hsa_purchase_order_purchase_guid_IDX 
USING BTREE ON hsm_member_marketing_platform_db.hsa_purchase_order (purchase_guid);
