-- holder权限 
 -- todo sql执行完请刷新holder权限redis 
INSERT INTO `gateway`.`hp_function_group`(`create_time`, `name`, `sequence`, `update_time`, `gmt_create`, `gmt_modified`) 
 VALUES 
(now(), '会员结算台',unix_timestamp(), now(), now(), now());

INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)  
VALUES 
(NOW(), '会员结算台', 1, 60, NOW(), 26,  
( SELECT id FROM hp_system WHERE name = '营销中心' AND identification = 'marketing_count'    
 ORDER BY gmt_modified DESC LIMIT 1 ), 
(select id from hp_function_group where name = '会员结算台'    
 order by gmt_modified desc limit 1)
,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '查看会员结算台', NOW(), 
(select id from hp_function where name = '会员结算台' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '新建结算规则', NOW(), 
(select id from hp_function where name = '会员结算台' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '查看规则详情', NOW(), 
(select id from hp_function where name = '会员结算台' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '编辑', NOW(), 
(select id from hp_function where name = '会员结算台' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '复制规则', NOW(), 
(select id from hp_function where name = '会员结算台' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '删除', NOW(), 
(select id from hp_function where name = '会员结算台' 
 and   system_id = (
	SELECT id FROM hp_system 
WHERE name = '营销中心' AND identification = 'marketing_count' limit 1
)   
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());