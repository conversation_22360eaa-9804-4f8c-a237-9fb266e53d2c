-- 新库
use hsm_member_settlement_platform_db;

DROP TABLE IF EXISTS `hsa_settlement_rule`;
-- 结算规则已选优惠项
DROP TABLE IF EXISTS `hsa_settlement_rule_discount`;
-- 结算规则门店
DROP TABLE IF EXISTS `hsa_settlement_rule_store`;
-- 结算优惠项
DROP TABLE IF EXISTS `hsa_settlement_discount`;

-- hsm_member_settlement_platform_db.hsa_settlement_discount definition

CREATE TABLE `hsa_settlement_discount` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `gmt_create` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `gmt_modified` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `is_delete` bigint(13) NOT NULL DEFAULT '0' COMMENT '删除标志位，0-未删除，时间-已删除',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',
  `parent_guid` varchar(50) NOT NULL DEFAULT '0' COMMENT '父级guid,',
  `discount_type` tinyint(4) NOT NULL COMMENT '类型：0 单品级优惠 1订单级优惠 2资产优惠',
  `discount_item` tinyint(4) NOT NULL COMMENT '优惠项： SettlementDiscountItemEnum',
  `discount_option` tinyint(4) DEFAULT '0' COMMENT '优惠具体优惠项： SettlementDiscountOptionEnum',
  `discount_guid` varchar(50) NOT NULL DEFAULT '' COMMENT '优惠guid',
  `discount_name` varchar(50) NOT NULL DEFAULT '' COMMENT '优惠名称',
  `discount_dynamic` varchar(50) NOT NULL DEFAULT '' COMMENT '优惠力度',
  `discount_num` int(10) NOT NULL DEFAULT '1' COMMENT '数量',
  PRIMARY KEY (`id`),
  KEY `hsa_settlement_discount_discount_guid_IDX` (`discount_guid`,`discount_option`) USING BTREE,
  KEY `hsa_settlement_discount_oper_subject_guid_IDX` (`oper_subject_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='结算优惠项';


-- hsm_member_settlement_platform_db.hsa_settlement_rule definition

CREATE TABLE `hsa_settlement_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `gmt_create` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `gmt_modified` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `is_delete` bigint(13) NOT NULL DEFAULT '0' COMMENT '删除标志位，0-未删除，时间-已删除',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',
  `name` varchar(50) NOT NULL COMMENT '名称',
  `coupon_limit` tinyint(4) NOT NULL DEFAULT '0' COMMENT '优惠券叠上限限制：0否 1是',
  `coupon_limit_num` int(10) NOT NULL DEFAULT '0' COMMENT '优惠券单笔限制张数',
  `coupon_rollback` tinyint(4) NOT NULL DEFAULT '0' COMMENT '退款是否退优惠：0否 1是',
  `use_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '核销顺序及规则：0自动 1手动',
  `apply_business` tinyint(4) NOT NULL DEFAULT '0' COMMENT '业务：0全部 1部分',
  `apply_business_json` varchar(100) DEFAULT '' COMMENT '业务数组',
  `applicable_all_store` tinyint(4) NOT NULL DEFAULT '0' COMMENT '门店：0全部 1部分',
  `coupon_view_limit_num` int(10) NOT NULL DEFAULT '1' COMMENT '优惠券单笔限制张数',
  PRIMARY KEY (`id`),
  KEY `hsa_settlement_rule_guid_IDX` (`guid`) USING BTREE,
  KEY `hsa_settlement_rule_oper_subject_guid_IDX` (`oper_subject_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='结算规则';


-- hsm_member_settlement_platform_db.hsa_settlement_rule_discount definition

CREATE TABLE `hsa_settlement_rule_discount` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `gmt_create` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `gmt_modified` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `is_delete` bigint(13) NOT NULL DEFAULT '0' COMMENT '删除标志位，0-未删除，时间-已删除',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',
  `settlement_rule_guid` varchar(50) NOT NULL COMMENT '规则guid',
  `settlement_discount_guid` varchar(50) NOT NULL DEFAULT '0' COMMENT '结算优惠项guid,',
  `discount_num` int(10) NOT NULL DEFAULT '1' COMMENT '数量',
  `rank` int(10) NOT NULL DEFAULT '1' COMMENT '排序',
  `is_append` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否可叠加：1是 0否',
  PRIMARY KEY (`id`),
  KEY `hsa_settlement_rule_discount_guid_IDX` (`guid`) USING BTREE,
  KEY `hsa_settlement_rule_discount_oper_subject_guid_IDX` (`oper_subject_guid`) USING BTREE,
  KEY `hsa_settlement_rule_discount_settlement_discount_guid_IDX` (`settlement_discount_guid`,`settlement_rule_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='结算规则已选优惠项';


-- hsm_member_settlement_platform_db.hsa_settlement_rule_store definition

CREATE TABLE `hsa_settlement_rule_store` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `is_delete` bigint(13) NOT NULL DEFAULT '0' COMMENT '删除标志位，0-未删除，时间-已删除',
  `gmt_create` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `gmt_modified` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `store_guid` varchar(50) NOT NULL COMMENT '门店guid',
  `settlement_rule_guid` varchar(50) NOT NULL COMMENT '规则guid',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`),
  KEY `oper_subject_guid_idx` (`oper_subject_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='结算规则门店';


CREATE INDEX hsa_settlement_rule_store_settlement_rule_guid_IDX 
USING BTREE ON hsm_member_settlement_platform_db.hsa_settlement_rule_store (settlement_rule_guid);
