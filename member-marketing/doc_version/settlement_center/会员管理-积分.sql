use hsm_member_marketing_platform_db;

-- 积分使用明细
DROP TABLE IF EXISTS `hsa_member_integral_deduct_list`;

 -- 积分使用明细：用于回收
CREATE TABLE `hsa_member_integral_deduct_list` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `guid` varchar(50) NOT NULL COMMENT '会员等级会员价周期累计明细guid',
  `is_delete` bigint(13) NOT NULL DEFAULT '0' COMMENT '删除标志位，0-未删除，时间-已删除',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `integral_deduct_guid` varchar(50) NOT NULL COMMENT '积分使用guid',
  `integral_detail_guid` varchar(50) NOT NULL COMMENT '积分明细guid',
  `integral_number` int(11) NOT NULL COMMENT '抵扣积分',
  `protect_date` datetime NOT NULL COMMENT '积分有效期',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT '即将过期积分使用明细';

CREATE INDEX hsa_member_integral_deduct_list_integral_deduct_guid_IDX 
USING BTREE ON hsm_member_marketing_platform_db.hsa_member_integral_deduct_list (integral_deduct_guid,integral_detail_guid);
