
CREATE TABLE `hsm_member_marketing_center_platform_db`.`hsa_limit_specials_activity` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '活动guid',
  `oper_subject_guid` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '运营主体guid',
  `name` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '活动名称',
  `activity_code` varchar(20) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '活动ID',
  `start_time` datetime NOT NULL COMMENT '活动开始时间',
  `end_time` datetime NOT NULL COMMENT '活动结束时间',
  `state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '活动状态 0-未发布 1-已发布 2-已暂停',
  `is_limit_period` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否限制活动时段 0-否 1-是',
  `limit_period_type` tinyint(1) DEFAULT '0' COMMENT '限制时段类型 -1:自定义 0：日 1：周 2：月 3：年',
  `limit_period_json` varchar(500) COLLATE utf8mb4_icelandic_ci DEFAULT NULL COMMENT '限制时段限制类型json',
  `label_guid_json` varchar(2000) COLLATE utf8mb4_icelandic_ci DEFAULT NULL COMMENT '活动标签guid集合json',
  `relation_rule` tinyint(1) NOT NULL DEFAULT '0' COMMENT '活动规则 共享互斥关系 0-互斥 1-共享',
  `apply_business` tinyint(1) NOT NULL DEFAULT '0' COMMENT '适用场景 0:全部业务 1：部分业务',
  `apply_business_json` varchar(100) COLLATE utf8mb4_icelandic_ci DEFAULT NULL COMMENT '适用场景json',
  `is_all_store` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否适用于所有门店 1:全部门店；0:部分门店(外关联表)',
  `group_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '适用人群类型 0不限制 1所有注册会员 2指定人群',
  `condition_label_json` varchar(2000) COLLATE utf8mb4_icelandic_ci DEFAULT NULL COMMENT '指定标签json',
  `condition_grade_json` varchar(2000) COLLATE utf8mb4_icelandic_ci DEFAULT NULL COMMENT '指定等级json',
  `condition_member_json` text COLLATE utf8mb4_icelandic_ci COMMENT '指定会员json',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0-否 1-是',
  `is_publish` tinyint(1) DEFAULT '0' COMMENT '是否发布过 0-否 1-是',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_guid` (`guid`) USING BTREE,
  KEY `idx_oper_subject_guid` (`oper_subject_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_icelandic_ci ROW_FORMAT=DYNAMIC COMMENT='限时特价活动';


CREATE TABLE `hsm_member_marketing_center_platform_db`.`hsa_limit_specials_activity_item` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT 'guid',
  `oper_subject_guid` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '运营主体guid',
  `activity_guid` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '活动guid',
  `commodity_id` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '商品id',
  `commodity_code` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '商品编码',
  `commodity_type` tinyint(1) NOT NULL COMMENT '商品类型',
  `commodity_name` varchar(40) COLLATE utf8mb4_icelandic_ci DEFAULT NULL COMMENT '商品名称',
  `specials_type` tinyint(1) NOT NULL COMMENT '特价类型 1打折 2减价 3指定价格',
  `specials_number` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '特价数额',
  `limit_number` int(4) DEFAULT NULL COMMENT '优惠限购',
  `channel` varchar(20) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '商品渠道',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0-否 1-是',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_guid` (`guid`) USING BTREE,
  KEY `idx_activity_guid` (`activity_guid`) USING BTREE,
  KEY `idx_oper_subject_guid` (`oper_subject_guid`) USING BTREE,
  KEY `idx_commodity_id` (`commodity_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_icelandic_ci ROW_FORMAT=DYNAMIC COMMENT='限时特价活动商品';


CREATE TABLE `hsm_member_marketing_center_platform_db`.`hsa_limit_specials_activity_record` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) NOT NULL COMMENT '唯一标识',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '运营主体guid',
  `activity_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '活动guid',
  `activity_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '活动ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '活动名称',
  `store_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci DEFAULT NULL COMMENT '门店名称',
  `order_guid` varchar(50) NOT NULL COMMENT '订单guid',
  `order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci DEFAULT NULL COMMENT '订单编号',
  `order_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单时间',
  `order_state` tinyint(1) NOT NULL COMMENT '订单状态',
  `order_type` tinyint(1) NOT NULL COMMENT '订单类型',
  `order_fee` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '订单金额',
  `order_discount_fee` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '订单优惠金额',
  `order_actually_fee` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '订单实收金额',
  `order_source` tinyint(1) NOT NULL COMMENT '订单来源',
  `member_phone` varchar(50) DEFAULT NULL COMMENT '会员电话',
  `member_name` varchar(50) DEFAULT NULL COMMENT '会员名字',
  `is_delete` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0-否 1-是',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`),
  KEY `idx_oper_subject_guid` (`oper_subject_guid`),
  KEY `idx_activity_guid` (`activity_guid`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='限时折扣记录';


CREATE TABLE `hsm_member_marketing_center_platform_db`.hsa_limit_specials_activity_store` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT 'guid',
  `oper_subject_guid` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '运营主体guid',
  `activity_guid` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '活动guid',
  `store_guid` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '门店guid',
  `store_name` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '门店名称',
  `store_code` varchar(30) COLLATE utf8mb4_icelandic_ci DEFAULT NULL COMMENT '门店id',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0-否 1-是',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_guid` (`guid`) USING BTREE,
  KEY `idx_activity_guid` (`activity_guid`) USING BTREE,
  KEY `idx_oper_subject_guid` (`oper_subject_guid`) USING BTREE,
  KEY `idx_store_guid` (`store_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_icelandic_ci ROW_FORMAT=DYNAMIC COMMENT='限时特价活动门店';


ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_apply_dictionaries`
ADD COLUMN `system` tinyint(4) NULL DEFAULT 0 COMMENT '系统';

INSERT INTO `hsm_member_marketing_platform_db`.`hsa_apply_dictionaries` (`id`, `module`, `type`, `type_name`, `channel`, `system`) VALUES (31, 'apply_business', '0', '正餐',  NULL, 1);
INSERT INTO `hsm_member_marketing_platform_db`.`hsa_apply_dictionaries` (`id`, `module`, `type`, `type_name`, `channel`, `system`) VALUES (32, 'apply_business', '1', '快餐',  NULL, 1);
