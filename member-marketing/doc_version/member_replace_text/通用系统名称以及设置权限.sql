CREATE TABLE `hsa_system_role`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `gmt_create`        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `oper_subject_guid` varchar(50)          DEFAULT NULL COMMENT '运营主体GUID',
    `name`              varchar(50) NOT NULL COMMENT '角色名',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=321 DEFAULT CHARSET=utf8mb4 COMMENT='系统角色名表';







INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified,
 function_identification)
VALUES (CURRENT_TIMESTAMP, '通用设置', 1, 60, CURRENT_TIMESTAMP, 26,
        (SELECT id
         from hp_system
         WHERE name = '会员管理' AND identification = 'member_marketing'
         ORDER BY gmt_modified DESC LIMIT 1),
       (select id from hp_function_group where name = '会员设置' and sequence >0 order by gmt_modified desc limit 1), CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');





-- 查看
INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                          `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '查看', NOW(),
        (select id
         from hp_function
         where name = '通用设置'
         order by gmt_modified desc limit 1), 0, NULL, '', 0, 0, NOW(), NOW() );

-- 编辑
INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                          `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '编辑', NOW(),
        (select id
         from hp_function
         where name = '通用设置'
         order by gmt_modified desc limit 1), 0, NULL, '', 0, 0, NOW(), NOW() );
