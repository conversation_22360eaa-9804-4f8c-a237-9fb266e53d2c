update hsa_operation_member_info set password= UPPER(MD5('123456'));



ALTER TABLE hsa_operation_member_info
    ALTER COLUMN password SET DEFAULT '123456';

ALTER TABLE hsa_account_setting ADD member_mode tinyint(1) DEFAULT 0 NOT NULL;



ALTER TABLE hsa_credit_user ADD total_credit DECIMAL(12,2) DEFAULT '0.00' COMMENT '累计挂账';




-- 权限

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (NOW(), NULL, '修改账户密码', NOW(),
     (select id from hp_function where name = '会员账户详情'
                                   and   system_id = (
             SELECT id FROM hp_system
             WHERE name = '会员管理' AND identification = 'member_marketing' limit 1
    )
order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());


-- 初始化资料项
/base/data_item/init_update_info






















ALTER TABLE hsa_card_balance_rule ADD terminal_check_status VARCHAR(50) DEFAULT NULL COMMENT '终端经营校验';