USE `hsm_member_marketing_platform_db`;

-- 会员微信公众号关联表
DROP TABLE IF EXISTS `hsa_member_info_wx_mp`;
CREATE TABLE `hsa_member_info_wx_mp` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) NOT NULL,
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0：false,1:true',
  `app_id` varchar(255) DEFAULT NULL COMMENT 'app_id',
  `union_id` varchar(255) DEFAULT NULL COMMENT '微信用户union_id',
  `open_id` varchar(255) NOT NULL COMMENT '微信用户open_id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_guid` (`guid`) USING BTREE,
  UNIQUE KEY `uk_open_id` (`app_id` ,`union_id`, `open_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='会员微信公众号关联表';


USE `hsm_member_mall_tool_platform_db`;
ALTER TABLE hsa_messages_config ADD COLUMN `mp_title` varchar(25) DEFAULT NULL COMMENT '公众号消息标题';
ALTER TABLE hsa_messages_config ADD COLUMN `mp_msg_content` varchar(255) DEFAULT NULL COMMENT '公众号消息模板实例';
ALTER TABLE hsa_messages_config ADD COLUMN `mp_template_no` varchar(50) DEFAULT NULL COMMENT '公众号消息模板编号';


