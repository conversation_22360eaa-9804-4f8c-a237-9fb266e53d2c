hsm_member_marketing_center_platform_db
CREATE TABLE `hsa_certified_theme` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `oper_subject_guid` varchar(50) DEFAULT NULL COMMENT '运营主体',
  `background_picture` varchar(200) DEFAULT NULL COMMENT '背景图',
  `button_color` varchar(50) DEFAULT NULL COMMENT '按钮色',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='认证有礼主题表';

ALTER TABLE `hsm_member_marketing_center_platform_db`.`hsa_certified_activity`
ADD COLUMN `background_picture` varchar(200) NULL COMMENT '背景图' ,
ADD COLUMN `button_color` varchar(50) NULL COMMENT '按钮色' ;

ALTER TABLE `hsm_member_marketing_center_platform_db`.`hsa_certified_activity_record`
ADD COLUMN `issuance_status` tinyint(4) NULL COMMENT '发放状态 0：发放成功 1：发放失败' ;



use hsm_member_marketing_platform_db;

-- 会员认证学生信息
CREATE TABLE IF NOT EXISTS `hsa_member_certificate_student_info` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT,
 `guid` varchar(50) NOT NULL COMMENT '认证学生信息guid',
 `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
 `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0：false,1:true',
 `enterprise_guid` varchar(50) NOT NULL COMMENT '企业guid',
 `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
 `member_info_guid` varchar(50) NOT NULL COMMENT '会员guid',
 `user_id` varchar(100) DEFAULT NULL COMMENT '支付宝id',
 `college_online_tag` tinyint(1) DEFAULT 0 COMMENT '是否学生 1是 0否',
 `school_name` varchar(100) DEFAULT NULL COMMENT '学校名称',
 `school_id` varchar(100) DEFAULT NULL COMMENT '学校内标',
 `member_ship_status` tinyint(1) DEFAULT NULL COMMENT '会员状态，1 校园会员；0 新用户',
 `type` tinyint(1) DEFAULT NULL COMMENT '身份类型，1-学生；2-教师；3-校友；4-考生；5-家长',
 `enroll_date` date DEFAULT NULL COMMENT '入学时间，格式yyyy-mm-dd',
 `degree` tinyint(1) DEFAULT NULL COMMENT '学历，枚举类型：专科0，本科1，研究生2，博士3，本硕连读4，硕博连读5，本硕博连读6',
 `examine_status` tinyint(1) DEFAULT NULL COMMENT '审核状态，0-审核中，2-审核不通过，3-审核通过，5-无数据',
 PRIMARY KEY (`id`) USING BTREE,
 UNIQUE KEY `uk_guid` (`guid`) USING BTREE,
 UNIQUE KEY `uk_member_info_guid` (`member_info_guid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='会员认证学生信息';