-- 付费等级
ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_label_setting_base_info`
    ADD COLUMN paid_member_level_guid VARCHAR(1000) DEFAULT NULL COMMENT '付费等级（英文逗号分隔）',
    ADD COLUMN paid_member_start_date_from VARCHAR(20) DEFAULT NULL COMMENT '成为付费会员时间范围-开始',
    ADD COLUMN paid_member_start_date_to VARCHAR(20) DEFAULT NULL COMMENT '成为付费会员时间范围-结束',
    ADD COLUMN paid_member_duration_min INT DEFAULT NULL COMMENT '成为付费会员时长-最小值',
    ADD COLUMN paid_member_duration_max INT DEFAULT NULL COMMENT '成为付费会员时长-最大值',
    ADD COLUMN paid_member_duration_unit VARCHAR(10) DEFAULT NULL COMMENT '成为付费会员时长-单位',
    ADD COLUMN paid_member_expire_remain_min INT DEFAULT NULL COMMENT '距离会员到期时间-最小值',
    ADD COLUMN paid_member_expire_remain_max INT DEFAULT NULL COMMENT '距离会员到期时间-最大值',
    ADD COLUMN paid_member_expire_remain_unit VARCHAR(10) DEFAULT NULL COMMENT '距离会员到期时间-单位';

-- 资料项
ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_label_setting_base_info`
    ADD COLUMN data_item_json TEXT COMMENT '自定义资料项内容，JSON字符串';

-- 注册信息
ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_label_setting_base_info`
    ADD COLUMN is_bind_alipay TINYINT(1) DEFAULT NULL COMMENT '是否绑定支付宝（0-否，1-是）' AFTER is_bind_wechat;