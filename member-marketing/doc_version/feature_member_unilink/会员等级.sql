-- 新增会员等级类型
alter table hsm_member_marketing_platform_db.hsa_member_grade_info
    add type tinyint(1) default 0 not null comment '等级类型：0免费会员，1付费会员；默认免费会员' after name;

-- 新增会员等级购买流水表
create table hsm_member_marketing_platform_db.hsa_member_grade_purchase_history
(
    id                     int auto_increment comment '主键'
        primary key,
    guid                    varchar(50)                        null comment 'guid',
    no                      varchar(40)                         null comment '订单号',
    member_info_guid       varchar(50)                              null comment '会员GUID',
    member_info_name       varchar(40)                              null comment '会员昵称',
    user_phone              varchar(255)                        null comment '用户手机号',
    member_info_grade_guid varchar(50)                              null comment '会员等级GUID',
    member_info_grade_name varchar(50)                              null comment '会员等级名称',
    vip_grade               int                                 not null comment '购买会员级别',
    effective_duration_type int                                 not null comment '有效期',
    pay_amount             decimal(12, 2) default 0.00          null comment '支付金额（单位：毫）',
    pay_type                varchar(20)                         null comment '支付方式
aliPay  支付宝
wxPay 微信支付
balance 储值支付',
    expire_time             datetime                            not null comment '到期时间',
    gmt_create             timestamp default CURRENT_TIMESTAMP not null comment '创建时间',
    gmt_modified             timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    version                 int                                 not null comment '乐观锁版本号',
    is_delete              tinyint(1)     default 0             not null comment '逻辑删除标记 0 未删除  1 已删除',
    order_no                varchar(40)                         null comment '支付订单编号',
    type                    tinyint                             not null comment '类型（新开，续费）',
    oper_subject_guid       varchar(50)                             null comment '操作人平台id',
    remark                 text                                     null comment '备注'
)
    comment '会员等级购买流水' row_format = DYNAMIC;

create index idx_oper_subject_id
    on hsm_member_marketing_platform_db.hsa_member_grade_purchase_history (oper_subject_guid);


-- 新增会员等级卡表
create table hsm_member_marketing_platform_db.hsa_member_grade_card
(
    id                     int auto_increment comment '主键'
        primary key,
    guid                varchar(50)                           not null comment 'guid',
    oper_subject_guid   varchar(50)                           not null comment '运营主体guid',
    grade_guid          varchar(50)     not null comment '会员级别id',
    member_info_guid        varchar(50)     not null comment '用户id',
    vip_grade              int        null comment '会员等级',
    grade_type             tinyint(1)        not null comment '会员类型:0免费，1付费',
    member_card_valid_time date       null comment '会员卡有效时间',
    member_card_status     int        not null comment '会员卡状态',
    open_type              int        null comment '开卡方式',
    gmt_create              timestamp      default CURRENT_TIMESTAMP not null comment '创建时间',
    gmt_modified            timestamp      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    version                int        not null comment '乐观锁版本号',
    is_delete              tinyint(1)     default 0             not null comment '逻辑删除标记 0 未删除  1 已删除'
)
    comment '用户等级卡' row_format = DYNAMIC;

create index idx_member_card_valid_time
    on hsa_member_grade_card (member_card_valid_time);

create index idx_grade_guid
    on hsa_member_grade_card (grade_guid);

create index idx_oper_subject_guid
    on hsa_member_grade_card (oper_subject_guid);

create index idx_vip_grade
    on hsa_member_grade_card (vip_grade);

create index idx_member_info_guid
    on hsa_member_grade_card (member_info_guid);

alter table hsm_member_marketing_platform_db.hsa_member_grade_purchase_history
    add record_desc varchar(100) null comment '记录说明';

alter table hsm_member_marketing_platform_db.hsa_member_grade_purchase_history
    add source int null comment '变动来源(0管理后台，2一体机，53微信小程序)';

alter table hsm_member_marketing_platform_db.hsa_member_grade_purchase_history
    add store_guid varchar(50) null comment '门店guid';

alter table hsm_member_marketing_platform_db.hsa_member_grade_purchase_history
    add store_name varchar(40) null comment '门店名称';

alter table hsm_member_marketing_platform_db.hsa_member_grade_purchase_history
    add operator_name varchar(50) null comment '操作人名字';

-- 运营主体会员信息表 新增字段冗余付费等级信息
alter table hsm_member_marketing_platform_db.hsa_operation_member_info
    add member_paid_grade_info_guid varchar(50) null comment '付费会员等级guid' after member_grade_info_name;

alter table hsm_member_marketing_platform_db.hsa_operation_member_info
    add member_paid_grade_info_name varchar(50) null comment '付费会员等级名称' after member_paid_grade_info_guid;

-- 初始化适用业务，适用终端数据

INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries (module, type, type_name, channel, `system`)
VALUES ('apply_business', '21', '私域商城-快递配送', '私域商城', 4);

INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries (module, type, type_name, channel, `system`)
VALUES ('apply_business', '22', '私域商城-同城配送', '私域商城', 4);

INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries (module, type, type_name, channel, `system`)
VALUES ('apply_business', '23', '私域商城-到店自提', '私域商城', 4);

INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries (module, type, type_name, channel, `system`)
VALUES ('apply_business', '24', '私域商城-无需物流', '私域商城', 4);

INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries (module, type, type_name, channel, `system`)
VALUES ('apply_terminal', '70', '私域商城-PC', '私域商城', 4);

INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries (module, type, type_name, channel, `system`)
VALUES ('apply_terminal', '71', '私域商城-H5', '私域商城', 4);

INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries (module, type, type_name, channel, `system`)
VALUES ('apply_terminal', '72', '私域商城-小程序', '私域商城', 4);


alter table hsm_member_marketing_platform_db.hsa_member_grade_purchase_history
    modify vip_grade int null comment '购买会员级别';

alter table hsm_member_marketing_platform_db.hsa_member_grade_purchase_history
    modify effective_duration_type int null comment '有效期';

alter table hsm_member_marketing_platform_db.hsa_member_grade_purchase_history
    modify expire_time datetime null comment '到期时间';

-- 字典表增加是否展示字段
alter table hsm_member_marketing_platform_db.hsa_apply_dictionaries
    add is_show tinyint default 0 not null comment '是否展示,0:不展示1:展示，默认都不展示';

-- 修改权益规则表字段类型
alter table hsm_member_marketing_platform_db.hsa_equities_rule
    modify apply_channel_limited varchar(128) null comment '适用渠道限制 0：全部 1：部分,多个，分割';

alter table hsm_member_marketing_platform_db.hsa_equities_store_rule
    add system varchar(20) null coment '来源系统： SALE(0, "新会员"), SAAS(1, "老会员"), PARTNER(2, "好搭档"), RETAIL(3, "零售"),  MALL(4, "私域商城")' after equities_version_id;

-- 新增会员消费分销记录表
create table hsm_member_marketing_platform_db.hsa_member_consumption_distribute
(
    id                      bigint auto_increment
        primary key,
    guid                    varchar(50)                  not null comment '分销记录guid',
    consumption_guid        varchar(50)                  not null comment '会员消费记录GUID',
    order_number            varchar(50)                  null comment '订单编号（外部订单编号）',
    oper_subject_guid       varchar(50)                  not null comment '运营主体GUID',
    operator_tel_name       varchar(45)                  null comment '操作人员账号名字/手机号',
    distribute_record_type  tinyint        default -1    null comment '分销记录类型-1:未退款（订单完成）1:整单退款，0:部分退款',
    is_complete             tinyint        default 0     not null comment '是否已经完成分销通知（0未完成，1已完成）',
    is_delete               tinyint(1)     default 0     not null comment '是否删除,0未删除,1已删除',
    gmt_create              datetime       default CURRENT_TIMESTAMP not null,
    gmt_modified            datetime       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    constraint uk_guid
        unique (guid)
)
    comment '会员消费分销记录表' row_format = DYNAMIC;