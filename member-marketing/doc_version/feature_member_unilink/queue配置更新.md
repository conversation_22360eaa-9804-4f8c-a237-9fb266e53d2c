# 在 spring.cloud.stream.rabbit.bindings 部分添加：
inputMemberConsumptionDistribute:
consumer:
acknowledge-mode: manual

# 在 spring.cloud.stream.bindings 部分添加：
inputMemberConsumptionDistribute:
group: memberConsumptionDistribute
destination: memberConsumptionDistributeExchange
consumer:
max-attempts: 1


## 在base也需要添加对应的消息配置
outputMemberConsumptionDistribute:
destination: memberConsumptionDistributeExchange
contentType: application/json