CREATE TABLE `hsm_member_marketing_platform_db.hsa_member_everyday_record`
(
    `id`                int(11)     NOT NULL AUTO_INCREMENT COMMENT '自增主键id',
    `guid`              varchar(50) NOT NULL COMMENT '会员guid',
    `enterprise_guid`   varchar(50) NOT NULL DEFAULT '' COMMENT '企业guid',
    `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
    `member_info_guid`  varchar(50) NULL     DEFAULT NULL,
    `record_type`       tinyint(4)  NULL     DEFAULT NULL,
    `record_source`     tinyint(4)  NULL     DEFAULT NULL,
    `gmt_create`        datetime    NULL     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`      timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_guid` (`guid`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 519
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '会员日常行为表'
  ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
