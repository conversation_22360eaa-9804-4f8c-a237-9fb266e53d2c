DROP TABLE IF EXISTS `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system`;
CREATE TABLE `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system`  (
                                                                                 `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                                                                 `enterprise_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '企业GUID',
                                                                                 `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '运营主体GUID',
                                                                                 `industry_id` bigint(20) NOT NULL COMMENT '行业id',
                                                                                 `system_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '系统类型',
                                                                                 `system_remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '系统备注',
                                                                                 `system_description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '系统描述',
                                                                                 `unified_member` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用统一会员：0-否，1-是',
                                                                                 `unified_marketing` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用统一营销：0-否，1-是',
                                                                                 `is_auth` tinyint(1) NULL DEFAULT 0 COMMENT '是否授权',
                                                                                 `auth_enterprise_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '授权企业GUID',
                                                                                 `auth_oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '授权运营主体GUID',
                                                                                 `auth_enterprise_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '授权企业名称',
                                                                                 `auth_oper_subject_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '授权运营主体名称',
                                                                                 `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                                                                 `gmt_modified` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                                                                 `is_delete` tinyint(1) NULL DEFAULT 0,
                                                                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员通-系统配置' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_biz_config`;
CREATE TABLE `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_biz_config`  (
                                                                                            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                                                            `system_config_id` int(11) NOT NULL COMMENT '系统配置ID',
                                                                                            `biz_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务编码',
                                                                                            `biz_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务名称',
                                                                                            `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
                                                                                            `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                                                                            `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                                                                            PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统业务配置表' ROW_FORMAT = Dynamic;

INSERT INTO `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_biz_config` VALUES (1, 1, '1', '门店收银', 1, '2025-05-21 11:49:16', '2025-05-21 11:59:43');
INSERT INTO `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_biz_config` VALUES (2, 2, '21', '快递配送', 1, '2025-05-21 11:49:52', '2025-05-21 11:59:30');
INSERT INTO `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_biz_config` VALUES (3, 2, '22', '同城配送', 2, '2025-05-21 11:50:09', '2025-05-21 11:59:31');
INSERT INTO `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_biz_config` VALUES (4, 2, '23', '到店自提', 3, '2025-05-21 11:50:18', '2025-05-21 11:59:32');
INSERT INTO `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_biz_config` VALUES (5, 2, '24', '无需物流', 4, '2025-05-21 11:50:34', '2025-05-21 11:59:33');
INSERT INTO `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_biz_config` VALUES (6, 2, '25', '付费会员', 5, '2025-05-21 11:51:00', '2025-05-21 11:59:34');

DROP TABLE IF EXISTS `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_channel_config`;
CREATE TABLE `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_channel_config`  (
                                                                                                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                                                                `system_config_id` int(11) NOT NULL COMMENT '系统配置ID',
                                                                                                `channel_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '渠道编码',
                                                                                                `channel_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '渠道名称',
                                                                                                `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
                                                                                                `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                                                                                `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                                                                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统渠道配置表' ROW_FORMAT = Dynamic;

INSERT INTO `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_channel_config` VALUES (1, 0, '0', '后台添加', 1, '2025-05-21 11:52:35', '2025-05-21 11:59:50');
INSERT INTO `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_channel_config` VALUES (2, 0, '3', '后台导入', 2, '2025-05-21 11:53:56', '2025-05-21 11:59:51');
INSERT INTO `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_channel_config` VALUES (3, 1, '2', 'POS', 1, '2025-05-21 11:52:35', '2025-05-21 11:59:52');
INSERT INTO `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_channel_config` VALUES (4, 2, '70', 'PC', 2, '2025-05-21 11:53:56', '2025-05-21 11:59:53');
INSERT INTO `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_channel_config` VALUES (5, 2, '71', 'H5', 3, '2025-05-21 11:54:05', '2025-05-21 11:59:54');
INSERT INTO `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_channel_config` VALUES (6, 2, '72', '小程序', 4, '2025-05-21 11:54:20', '2025-05-21 11:59:55');

DROP TABLE IF EXISTS `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_config`;
CREATE TABLE `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_config`  (
                                                                                        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                                                        `system_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '系统类型',
                                                                                        `system_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '系统编码',
                                                                                        `system_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '系统名称',
                                                                                        `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                                                                        `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                                                                        PRIMARY KEY (`id`) USING BTREE,
                                                                                        UNIQUE INDEX `system_code`(`system_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统配置表' ROW_FORMAT = Dynamic;

INSERT INTO `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_config` VALUES (1, 'RETAIL', '3', '零售云', '2025-05-21 11:47:39', '2025-05-21 12:07:28');
INSERT INTO `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_config` VALUES (2, 'MALL', '4', '私域商城', '2025-05-21 11:47:50', '2025-05-21 12:07:29');

DROP TABLE IF EXISTS `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_terminal_config`;
CREATE TABLE `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_terminal_config`  (
                                                                                                 `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                                                                 `system_config_id` int(11) NOT NULL COMMENT '系统配置ID',
                                                                                                 `terminal_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '终端编码',
                                                                                                 `terminal_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '终端名称',
                                                                                                 `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
                                                                                                 `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                                                                                 `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                                                                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统终端配置表' ROW_FORMAT = Dynamic;

INSERT INTO `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_terminal_config` VALUES (3, 1, '2', 'POS', 1, '2025-05-21 11:52:35', '2025-05-21 11:59:52');
INSERT INTO `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_terminal_config` VALUES (4, 2, '70', 'PC', 2, '2025-05-21 11:53:56', '2025-05-21 11:59:53');
INSERT INTO `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_terminal_config` VALUES (5, 2, '71', 'H5', 3, '2025-05-21 11:54:05', '2025-05-21 11:59:54');
INSERT INTO `hsm_member_mall_tool_platform_db`.`hsa_member_unilink_system_terminal_config` VALUES (6, 2, '72', '小程序', 4, '2025-05-21 11:54:20', '2025-05-21 11:59:55');