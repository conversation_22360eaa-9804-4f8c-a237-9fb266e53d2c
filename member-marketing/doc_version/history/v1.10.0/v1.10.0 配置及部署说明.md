# v1.10.0配置及部署说明

## 接口
1.接口：member_grade/initialize_old_change_grade
    在hsa_member_grade_change_detail表中新增了三个字段，该接口是给之前老的数据初始化的

2.接口：goalgo_callback/multiple_growth_data_handler
    订单回退时,为了追踪之前翻倍成长值使用权益记录，所以在’hsa_member_growth_value_detail‘表中
    新增了两个字段，该接口是给这两个字段初始化数据的
3.接口：goalgo_callback/delete_multiple_growth_data_handler
    该接口和’goalgo_callback/multiple_growth_data_handler‘接口配套使用，主要作用是删除多增加的翻倍成长值的
    先执行’goalgo_callback/multiple_growth_data_handler‘，在执行’goalgo_callback/delete_multiple_growth_data_handler‘

## 服务


## 配置


```