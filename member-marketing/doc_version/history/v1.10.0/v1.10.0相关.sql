USE `hsm_member_marketing_platform_db`;

/*会员管理*/
use hsm_member_marketing_platform_db;
ALTER TABLE hsa_member_grade_info
    DROP COLUMN `effective_grade_guid`;
ALTER TABLE hsa_grade_equities
    DROP COLUMN `effective_equities_guid`;

ALTER TABLE hsa_grade_gift_bag
    ADD effective TINYINT(1) COMMENT '是否有效 0:无效 1:有效';
ALTER TABLE hsa_grade_rights_commodity_rule
    ADD effective TINYINT(1) COMMENT '是否有效 0:无效 1:有效';

ALTER TABLE hsa_member_growth_value_detail
    ADD multiple_equities_guid varchar(50) DEFAULT NULL COMMENT '翻倍权益guid';
ALTER TABLE hsa_member_growth_value_detail
    ADD multiple_growth_value int(10) DEFAULT NULL COMMENT '翻倍增加的成长值';

ALTER TABLE hsa_member_grade_change_detail
    ADD after_change_grade_icon varchar(512) DEFAULT NULL COMMENT '等级变化后的等级图标';

ALTER TABLE hsa_member_grade_change_detail
    ADD after_change_background_color varchar(128) DEFAULT NULL COMMENT '等级变化后的背景颜色';

ALTER TABLE hsa_member_grade_change_detail
    ADD after_change_growth_value int(11) DEFAULT NULL COMMENT '等级变化后的成长值';


CREATE TABLE `hsa_credit_apply_store`
(
    `id`                bigint(20)  NOT NULL AUTO_INCREMENT COMMENT 'id',
    `guid`              varchar(50) NOT NULL COMMENT 'guid',
    `credit_info_guid`  varchar(50) NOT NULL COMMENT '挂账信息guid',
    `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
    `store_guid`        varchar(50)          DEFAULT NULL COMMENT '门店guid',
    `store_name`        varchar(50)          DEFAULT NULL COMMENT '门店名称',
    `store_number`      varchar(50) NOT NULL COMMENT '门店编号',
    `time`              varchar(50)          DEFAULT NULL COMMENT '营业时间',
    `address`           varchar(128)         DEFAULT NULL COMMENT '营业地址',
    `parent_guid`       varchar(50)          DEFAULT NULL COMMENT '父guid',
    `gmt_create`        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `address_point`     varchar(128)         DEFAULT NULL COMMENT '经纬度',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 235
  DEFAULT CHARSET = utf8mb4;


CREATE TABLE `hsa_credit_info`
(
    `id`                    bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `guid`                  varchar(50) NOT NULL COMMENT 'guid',
    `credit_account_name`   varchar(10) NOT NULL COMMENT '挂账账户名称',
    `credit_type`           tinyint(4)  NOT NULL COMMENT '挂账类型 0：个人 1：单位',
    `linkman_phone`         varchar(11) NOT NULL COMMENT '联系人电话',
    `area_code`             varchar(20) NOT NULL COMMENT '区号',
    `credit_prove`          varchar(1000)        DEFAULT NULL COMMENT '挂账证明',
    `stored_amount`         decimal(12, 2)       DEFAULT '0.00' COMMENT '预存金额',
    `credit_wallet`         decimal(12, 2)       DEFAULT '0.00' COMMENT '挂账钱包',
    `current_credit`        decimal(12, 2)       DEFAULT '0.00' COMMENT '当前挂账',
    `credit_limited_set`    tinyint(4)  NOT NULL COMMENT '挂账上限设置 0:不设置上限 1：设置',
    `credit_limited_amount` decimal(12, 2)       DEFAULT '0.00' COMMENT '挂账上限金额',
    `applicable_all_store`  tinyint(4)  NOT NULL COMMENT '挂账范围(1:全部门店；0:部分门店(外关联表))',
    `account_validity`      tinyint(4)  NOT NULL COMMENT '账户有效期 0：永久有效 1：固定有效期',
    `account_validity_date` datetime             DEFAULT NULL COMMENT '固定有效期失效时间',
    `settle_period`         tinyint(4)  NOT NULL COMMENT '结算周期 0：手动生成结算单 1：自动生成结算单',
    `settle_period_type`    tinyint(4)           DEFAULT NULL COMMENT '结算周期类型 0：日 1：周 2：月 3：年',
    `settle_period_date`    varchar(128)         DEFAULT NULL COMMENT '结算周期时间',
    `operator_name`         varchar(64) NOT NULL COMMENT '操作人',
    `gmt_create`            datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`          datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_enable`             tinyint(1)  NOT NULL DEFAULT '1' COMMENT '是否开启 0：启用  1：禁用',
    `is_delete`             tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否删除,0未删除,1已删除',
    `oper_subject_guid`     varchar(50) NOT NULL COMMENT '运营主体guid',
    `credit_number`         varchar(7)  NOT NULL COMMENT '挂账编号',
    `company_name`          varchar(30)          DEFAULT NULL COMMENT '企业名称',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 70
  DEFAULT CHARSET = utf8mb4;


CREATE TABLE `hsa_credit_user`
(
    `id`                        bigint(20)     NOT NULL AUTO_INCREMENT COMMENT 'id',
    `guid`                      varchar(50)    NOT NULL COMMENT 'guid',
    `credit_info_guid`          varchar(50)    NOT NULL COMMENT '挂账信息guid',
    `member_info_guid`          varchar(50)    NOT NULL COMMENT '会员guid',
    `single_person_upper_limit` decimal(12, 2) NOT NULL DEFAULT '0.00' COMMENT '单人可挂账金额上限',
    `single_count_upper_limit`  decimal(12, 2) NOT NULL DEFAULT '0.00' COMMENT '单笔上限',
    `is_enable`                 tinyint(1)     NOT NULL COMMENT '是否开启 0：停止  1：开启',
    `gmt_create`                datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`              datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `oper_subject_guid`         varchar(50)    NOT NULL COMMENT '运营主体guid',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 346
  DEFAULT CHARSET = utf8mb4;


CREATE TABLE `hsa_credit_wallet_log`
(
    `id`                    bigint(20)   NOT NULL AUTO_INCREMENT,
    `guid`                  varchar(50)  NOT NULL,
    `oper_subject_guid`     varchar(50)  NOT NULL COMMENT '运营主体guid',
    `credit_info_guid`      varchar(50)  NOT NULL COMMENT '挂账信息guid',
    `change_type`           tinyint(4)   NOT NULL COMMENT '变动类型 0:预存余额',
    `change_amount`         decimal(12, 2)        DEFAULT '0.00' COMMENT '变动金额',
    `credit_wallet_balance` decimal(12, 2)        DEFAULT '0.00' COMMENT '挂账钱余额',
    `change_source`         tinyint(4)            DEFAULT NULL COMMENT '变动来源',
    `operator_name`         varchar(128) NOT NULL COMMENT '操作人',
    `gmt_create`            datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 26
  DEFAULT CHARSET = utf8mb4;


CREATE TABLE `hsa_member_grade_price_detail`
(
    `id`                     bigint(20)                                                   NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `guid`                   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '会员等级会员价周期累计明细guid',
    `oper_subject_guid`      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主体',
    `member_info_guid`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '会员guid',
    `member_info_grade_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '会员等级GUID',
    `equities_guid`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '会员等级权益GUID',
    `discounted_price`       decimal(12, 2)                                               NULL     DEFAULT NULL COMMENT '优惠金额',
    `consumption_guid`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '消费记录guid',
    `gmt_create`             datetime(0)                                                  NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    `gmt_modified`           datetime(0)                                                  NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
    `order_number`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '订单编号',
    `is_delete`              tinyint(1)                                                   NULL     DEFAULT NULL COMMENT '是否删除,0未删除,1已删除',
    `is_cancel`              tinyint(4)                                                   NULL     DEFAULT 0 COMMENT '是否被撤销:0 否，1 是',
    `discount_type`          tinyint(4)                                                   NULL     DEFAULT NULL,
    `discount_dynamics`      decimal(12, 2)                                               NULL     DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_guid` (`guid`) USING BTREE
)




