## sql
#use hsm_member_marketing_platform_db;

-- 修改会员信息表会员等级guid字段名
ALTER TABLE hsa_operation_member_info
    CHANGE operation_member_info_card_level_guid member_grade_info_guid VARCHAR(50) COMMENT '会员等级guid';

-- 修改会员信息表会员等级名称字段名
ALTER TABLE hsa_operation_member_info
    CHANGE operation_member_info_card_level_name member_grade_info_name VARCHAR(50) COMMENT '会员等级名称';

-- 新增会员信息表会员等级升级时间字段
ALTER TABLE hsa_operation_member_info
    ADD upgrade_time datetime DEFAULT NULL COMMENT '会员等级升级时间';

-- 新增升级礼包基础信息表
CREATE TABLE `hsa_gift_bag_base_info`
(
    `id`                bigint(20)                                                    NOT NULL AUTO_INCREMENT,
    `guid`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT 'guid',
    `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '运营主体',
    `equities_type`     tinyint(4)                                                    NULL DEFAULT NULL COMMENT '权益类型（升级礼包：1）',
    `equities_name`     varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '权益名称',
    `equities_icon`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权益图标',
    `equities_describe` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权益说明',
    `equities_rule`     tinyint(4)                                                    NULL DEFAULT NULL COMMENT '权益规则（0：实时赠送  1：自主领取）',
    `gmt_create`        datetime(0)                                                   NULL DEFAULT CURRENT_TIMESTAMP(0),
    `gmt_modified`      datetime(0)                                                   NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
    `operator_name`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '操作人名字',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 694
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- 新增会员等级状态控制表
CREATE TABLE `hsa_controlled_grade_state`
(
    `id`                int(11)                                                      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `guid`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '成长值规则GUID',
    `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运营主体GUID',
    `state`             tinyint(4)                                                   NOT NULL COMMENT '0启用 1禁用',
    `gmt_create`        datetime(0)                                                  NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    `gmt_modified`      datetime(0)                                                  NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    `operator_name`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '操作人名字',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 289
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- 新增会员等级变化明细表
CREATE TABLE `hsa_member_grade_rights_record`
(
    `id`                bigint(20)                                                   NOT NULL AUTO_INCREMENT,
    `guid`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '会员等级权益记录GUID',
    `member_info_guid`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '会员GUID',
    `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运营主体GUID',
    `rights_guid`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权益GUID',
    `rights_type`       tinyint(4)                                                   NOT NULL COMMENT '权益类型 0 升级礼包 1 等级权益',
    `member_grade_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '等级guid',
    `gmt_create`        datetime(0)                                                  NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    `gmt_modified`      datetime(0)                                                  NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_guid` (`guid`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '会员等级变化明细表'
  ROW_FORMAT = Dynamic;

-- 新增会员等级变化明细表
CREATE TABLE `hsa_member_grade_change_detail`
(
    `id`                            bigint(20)                                                     NOT NULL AUTO_INCREMENT,
    `guid`                          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '会员等级明细GUID',
    `member_info_guid`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '会员GUID',
    `oper_subject_guid`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '运营主体GUID',
    `before_change_grade_name`      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL     DEFAULT NULL COMMENT '等级变化前名称',
    `before_change_grade_vip_grade` int(11)                                                        NULL     DEFAULT NULL,
    `before_change_grade_guid`      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL     DEFAULT NULL COMMENT '等级变化前等级GUID',
    `after_change_grade_name`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '等级变化后等级名称',
    `after_change_grade_vip_grade`  int(11)                                                        NULL     DEFAULT NULL,
    `after_change_grade_guid`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '等级变化后等级GUID',
    `grade_change_type`             tinyint(4)                                                     NOT NULL COMMENT '等级变化类型,0升级,1降级',
    `current_growth_value`          int(11)                                                        NOT NULL DEFAULT 0,
    `remark`                        varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '备注',
    `is_show_applet`                tinyint(4)                                                     NOT NULL COMMENT '是否展示在小程序',
    `gmt_create`                    datetime(0)                                                    NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    `gmt_modified`                  datetime(0)                                                    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_guid` (`guid`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '会员等级变化明细表'
  ROW_FORMAT = Dynamic;


CREATE TABLE `hsa_member_grade_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `vip_grade` int(11) NOT NULL COMMENT 'VIP等级',
  `name` varchar(10) NOT NULL COMMENT '等级名称',
  `grade_icon` varchar(512) NOT NULL COMMENT '等级图标',
  `background_color` varchar(128) NOT NULL COMMENT '背景颜色',
  `growth_value` int(11) NOT NULL COMMENT '成长值',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `is_delete` tinyint(1) NOT NULL COMMENT '是否删除 0：未删除 1：已删除 2:删除但有效',
  `default_grade` tinyint(1) DEFAULT NULL COMMENT '默认等级 0：不是 1：是',
  `operator_name` varchar(128) NOT NULL COMMENT '操作人 姓名/账号',
  `effective` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否有效 0:无效 1:有效',
  `effective_grade_guid` varchar(50) DEFAULT NULL COMMENT '有效等级guid',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1495 DEFAULT CHARSET=utf8mb4;


CREATE TABLE `hsa_grade_gift_bag` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT '礼包guid',
  `member_grade_info_guid` varchar(50) NOT NULL COMMENT '会员等级guid',
  `type` tinyint(4) NOT NULL COMMENT '礼包类型 0:成长值 1:积分',
  `value` int(11) NOT NULL COMMENT '赠送多少值',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `is_delete` tinyint(1) DEFAULT NULL COMMENT '是否删除 0：未删除 1:已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=182 DEFAULT CHARSET=utf8mb4;


CREATE TABLE `hsa_grade_equities_explain` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `type` tinyint(4) NOT NULL COMMENT '等级说明类型 0：系统生成 1：自定义生成',
  `custom_content` text COMMENT '自定义内容',
  `operator_name` varchar(128) NOT NULL COMMENT '操作人 姓名/账号',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4;