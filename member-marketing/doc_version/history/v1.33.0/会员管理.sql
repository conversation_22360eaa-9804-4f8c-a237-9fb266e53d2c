use hsm_member_marketing_platform_db;

ALTER TABLE hsm_member_marketing_platform_db.hsa_member_funding_detail MODIFY COLUMN card_recharge_residual_balance decimal(12,2) DEFAULT 0 NULL COMMENT '卡实充剩余余额';
ALTER TABLE hsm_member_marketing_platform_db.hsa_member_funding_detail MODIFY COLUMN card_gift_residual_balance decimal(12,2) DEFAULT 0 NULL COMMENT '卡赠送剩余余额';


-- 每日统计卡余额
drop table if exists hsa_member_info_card_total;

CREATE TABLE `hsa_member_info_card_total` (
`id` bigint(20) NOT NULL AUTO_INCREMENT,
`guid` varchar(50) NOT NULL COMMENT 'guid',
`gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
`gmt_modified` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`is_delete` bigint(13) NOT NULL DEFAULT 0 COMMENT '删除标志位，0-未删除，时间-已删除',
`oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',
  `record_date` char(10) DEFAULT NULL COMMENT '日期',
  `subsidy_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '补贴余额',
  `card_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '充值余额',
  `gift_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '赠送余额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='会员卡余额历史';


-- hsm_member_marketing_platform_db.hsa_card_recharge_gift_detail definition

CREATE TABLE `hsa_card_recharge_gift_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) NOT NULL,
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',
  `member_guid` varchar(50) DEFAULT NULL COMMENT '会员GUID',
  `member_card_guid` varchar(50) DEFAULT NULL COMMENT '会员持卡GUID',
  `card_num` varchar(50) DEFAULT NULL,
  `card_guid` varchar(50) DEFAULT NULL,
  `order_num` varchar(50) DEFAULT NULL,
  `order_paid_amount` decimal(12,2) DEFAULT NULL,
  `activity_guid` varchar(50) DEFAULT NULL COMMENT '活动guid',
  `threshold_guid` varchar(50) DEFAULT NULL COMMENT '门槛guid',
  `gift_threshold_limit_sum` int(11) DEFAULT NULL COMMENT '本次消耗次数',
  `give_integral_value` int(11) DEFAULT '0' COMMENT '赠送积分',
  `give_growth_value` int(11) DEFAULT '0' COMMENT '赠送成长值',
  `give_card_guid` varchar(50) DEFAULT '0.00' COMMENT '赠送会员卡',
  `money_amount` decimal(12,2) NOT NULL COMMENT '赠送金额',
  `money_effective_type` tinyint(4) DEFAULT NULL COMMENT '赠送金额生效类型 0 立即生效 1 定时生效',
  `money_effective_time` datetime DEFAULT NULL COMMENT '赠送金额生效时间',
  `physical_card_guid` varchar(50) DEFAULT NULL COMMENT '实体卡guid',
  `electronic_card_guid` varchar(50) DEFAULT NULL COMMENT '电子卡guid',
  `is_refresh` tinyint(4) DEFAULT NULL COMMENT '是否刷新',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` tinyint(4) DEFAULT '0',
  `member_funding_detail_guid` varchar(100) DEFAULT NULL,
  `money_effective_unit_type` tinyint(4) DEFAULT NULL,
  `marking_label_guid_json` varchar(1000) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`),
  KEY `index_member_info_guid` (`member_guid`) USING BTREE,
  KEY `index_order_number` (`physical_card_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='会员消费记录表';


-- hsm_member_marketing_platform_db.hsa_recharge_gift_amount_record definition

CREATE TABLE `hsa_recharge_gift_amount_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) NOT NULL COMMENT '账户资金明细GUID',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',
  `member_info_guid` varchar(50) DEFAULT NULL COMMENT '会员GUID',
  `member_info_card_guid` varchar(50) NOT NULL COMMENT '会员持卡GUID',
  `card_guid` varchar(50) NOT NULL,
  `card_num` varchar(50) DEFAULT NULL COMMENT '实体卡号或电子卡号',
  `card_name` varchar(15) NOT NULL COMMENT '卡名称',
  `store_guid` varchar(50) DEFAULT NULL COMMENT '门店GUID',
  `store_name` varchar(50) DEFAULT NULL COMMENT '门店名称',
  `amount_recharge_type` tinyint(4) DEFAULT NULL COMMENT '资金来往类型(实充),0增加,1减少',
  `amount_gift_type` tinyint(4) DEFAULT NULL COMMENT '资金来往类型(赠送,0增加,1减少',
  `recharge_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '卡实充使用金额',
  `gift_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '卡赠送使用金额',
  `is_delete` tinyint(4) DEFAULT '0' COMMENT '是否删除',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `order_number` varchar(50) DEFAULT NULL,
  `change_source` tinyint(4) DEFAULT NULL COMMENT '变动来源 0：管理后台 2：一体机 53：小程序',
  `member_name` varchar(100) DEFAULT NULL,
  `member_phone` varchar(30) DEFAULT NULL,
  `recharge_activity_guid` varchar(50) DEFAULT NULL,
  `member_funding_detail_guid` varchar(50) DEFAULT NULL,
  `recharge_status` tinyint(4) DEFAULT NULL,
  `pay_name` varchar(20) DEFAULT NULL,
  `recharge_activity_name` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`),
  KEY `hsa_member_funding_detail_gmt_modified_idx` (`gmt_modified`) USING BTREE,
  KEY `index_member_info_card_guid` (`member_info_card_guid`) USING BTREE,
  KEY `index_member_info_guid` (`member_info_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='会员资金来往明细表';


-- 异常记录表
CREATE TABLE `hsa_member_info_card_error` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) NOT NULL COMMENT '运营主体会员持卡GUID',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',
  `enterprise_guid` varchar(50) DEFAULT NULL COMMENT '企业GUID',
  `member_info_guid` varchar(50) DEFAULT NULL COMMENT '会员GUID',
  `physical_card_guid` varchar(50) DEFAULT NULL COMMENT '实体卡GUID',
  `electronic_card_guid` varchar(50) DEFAULT NULL COMMENT '电子卡GUID',
  `card_guid` varchar(50) NOT NULL COMMENT '会员卡GUID',
  `card_name` varchar(15) NOT NULL COMMENT '卡名称',
  `subsidy_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '补贴余额',
  `card_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '充值余额',
  `gift_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '赠送余额',
  `excess_amount` decimal(12,2) DEFAULT '0.00' COMMENT '超额使用余额',
  `excess_times` int(11) DEFAULT '0' COMMENT '超额使用次数',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `card_validity` tinyint(1) NOT NULL COMMENT '0永久有效1固定时间',
  `card_validity_date` datetime DEFAULT NULL COMMENT '失效时间',
  `applicable_all_store` tinyint(4) NOT NULL COMMENT '是否适用于所有门店(1:全部门店；0:部分门店)',
  `card_recharge_explain` text COMMENT '充值说明',
  `is_physical_card_retreat` tinyint(4) DEFAULT NULL COMMENT '实体卡是否可退',
  `is_support_physical_card` tinyint(4) DEFAULT NULL COMMENT '是否支持实体卡1支持0不支持',
  `card_employ_explain` text COMMENT '使用须知',
  `card_color` varchar(50) DEFAULT NULL COMMENT '卡颜色',
  `card_image` varchar(255) DEFAULT NULL COMMENT '卡图片',
  `card_pay_password` varchar(50) DEFAULT NULL COMMENT '卡默认支付密码',
  `default_card` tinyint(1) DEFAULT NULL COMMENT '是否是默认卡 1：是',
  `physical_card_num` varchar(50) DEFAULT NULL COMMENT '实体卡号',
  `electronic_card_num` varchar(50) DEFAULT NULL COMMENT '电子卡号',
  `member_phone_num` varchar(50) DEFAULT NULL COMMENT '若已绑定会员则是账户手机号，若未绑定则是实体卡手机号',
  `physical_card_activation_time` datetime DEFAULT NULL COMMENT '实体卡激活时间',
  `physical_card_state` tinyint(4) DEFAULT NULL COMMENT '实体卡状态 -1 未激活  0已冻结 1 正常 2 已过期',
  `electronic_card_state` tinyint(4) DEFAULT NULL COMMENT '电子卡状态',
  `electronic_open_time` datetime DEFAULT NULL COMMENT '电子卡开卡时间',
  `retreat_subsidy_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '补贴可退金额',
  `default_choose` tinyint(1) DEFAULT NULL COMMENT '付款账户默认选择 1：默认选择',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_member_info_guid` (`member_info_guid`) USING BTREE,
  KEY `index_physical_card_guid` (`physical_card_guid`) USING BTREE,
  KEY `index_electronic_card_guid` (`electronic_card_guid`) USING BTREE,
  KEY `index_oper_subject_guid` (`oper_subject_guid`),
  KEY `index_card_guid` (`card_guid`),
  KEY `index_card_name` (`card_name`),
  KEY `index_physical_card_num` (`physical_card_num`),
  KEY `index_electronic_card_num` (`electronic_card_num`),
  KEY `index_member_phone_num` (`member_phone_num`),
  KEY `index_gmt_create` (`gmt_create`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='会员持卡关联表异常记录表';
