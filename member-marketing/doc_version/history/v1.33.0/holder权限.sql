-- holder权限 
 -- todo sql执行完请刷新holder权限redis 
INSERT INTO `gateway`.`hp_function_group`(`create_time`, `name`, `sequence`, `update_time`, `gmt_create`, `gmt_modified`) 
VALUES (now(), '充值营销',unix_timestamp(), now(), now(), now());

INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`) 
VALUES (NOW(), '充值营销', 1, 60, NOW(), 26,  
( SELECT id FROM hp_system WHERE name = '营销中心' AND identification = 'marketing_count' ORDER BY gmt_modified DESC LIMIT 1 ), 
(select id from hp_function_group where name = '充值营销' order by gmt_modified desc limit 1)
,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES (NOW(), NULL, '活动列表', NOW(), 
(select id from hp_function where name = '充值营销' order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES (NOW(), NULL, '新建活动', NOW(), 
(select id from hp_function where name = '充值营销' order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES (NOW(), NULL, '发布活动', NOW(), 
(select id from hp_function where name = '充值营销' order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES (NOW(), NULL, '暂停活动', NOW(), 
(select id from hp_function where name = '充值营销' order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES (NOW(), NULL, '启用活动', NOW(), 
(select id from hp_function where name = '充值营销' order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES (NOW(), NULL, '编辑活动', NOW(), 
(select id from hp_function where name = '充值营销' order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES (NOW(), NULL, '复制活动', NOW(), 
(select id from hp_function where name = '充值营销' order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES (NOW(), NULL, '详情查看', NOW(), 
(select id from hp_function where name = '充值营销' order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES (NOW(), NULL, '活动统计', NOW(), 
(select id from hp_function where name = '充值营销' order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES (NOW(), NULL, '删除活动', NOW(), 
(select id from hp_function where name = '充值营销' order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES (NOW(), NULL, '导出统计数据', NOW(), 
(select id from hp_function where name = '充值营销' order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

-- 会员卡余额记录的FunctionId是多少？可能会重名
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '退款', NOW(),
(select id from hp_function where name = '会员卡余额记录' order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());