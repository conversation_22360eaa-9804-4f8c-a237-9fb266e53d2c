use hsm_member_mall_tool_platform_db;

-- hsm_member_mall_tool_platform_db.hsa_messages_config definition

drop table if exists hsa_messages_config;

CREATE TABLE `hsa_messages_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `template_no` varchar(255) DEFAULT NULL COMMENT '公众号模板编号',
  `applet_template_id` varchar(255) DEFAULT NULL COMMENT '小程序消息模板id',
  `msg_template_id` varchar(255) DEFAULT NULL COMMENT '公众号消息模板id',
  `msg_type` tinyint(1) DEFAULT NULL COMMENT '消息类型 1 商家通知  0 用户通知',
  `is_delete` tinyint(1) DEFAULT NULL COMMENT '是否删除,0未删除,1已删除',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `oper_subject_guid` varchar(50) DEFAULT NULL COMMENT '运营主体GUID',
  `msg_category` tinyint(1) DEFAULT NULL COMMENT '消息类型',
  `msg_title` varchar(100) DEFAULT NULL COMMENT '消息标题',
  `push_rule` varchar(100) DEFAULT NULL COMMENT '推送规则',
  `msg_content` varchar(1000) DEFAULT NULL COMMENT '详细字段',
  `msg_kid` varchar(100) DEFAULT NULL COMMENT '公众号订阅号关键词',
  `page_path` varchar(1000) DEFAULT NULL,
  `applet_msg_kid` varchar(100) DEFAULT NULL COMMENT '小程序订阅号关键字',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '公众号模板状态 0关闭  1 开启',
  `applet_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '小程序模板状态 0关闭  1 开启',
  `applet_template_no` varchar(100) DEFAULT NULL COMMENT '小程序消息模板编号',
  `scenario_description` varchar(1000) DEFAULT NULL COMMENT '场景说明',
  `applet_msg_content` varchar(1000) DEFAULT NULL,
  `data_sort` varchar(1000) DEFAULT NULL,
  `title` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=928 DEFAULT CHARSET=utf8 COMMENT='小程序导航';