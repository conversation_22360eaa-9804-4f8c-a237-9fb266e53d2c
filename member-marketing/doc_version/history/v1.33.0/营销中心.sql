use hsm_member_marketing_center_platform_db;
-- 充值赠送

-- hsm_member_marketing_center_platform_db.hsa_recharge_gift_activity definition

CREATE TABLE `hsa_recharge_gift_activity` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT 'guid',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0-否 1-是',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `oper_subject_guid` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '运营主体guid',
  `activity_name` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '活动名称',
  `activity_type` int(4) NOT NULL COMMENT '活动类型0充值赠送、1充值优惠',
  `activity_code` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '活动类型0充值赠送、1充值优惠',
  `activity_state` int(4) NOT NULL COMMENT '活动状态：1草稿，2未发布，3未开始，4进行中，5已暂停，6已结束',
  `activity_start_time` datetime DEFAULT NULL COMMENT '活动开始时间',
  `activity_end_time` datetime DEFAULT NULL COMMENT '活动结束时间',
  `applicable_all_store` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1全部门店 0 部分门店（外关联表）',
  `description` varchar(250) COLLATE utf8mb4_icelandic_ci DEFAULT NULL,
  `recharge_filter_type` tinyint(4) DEFAULT NULL,
  `card_guid_json` varchar(2000) COLLATE utf8mb4_icelandic_ci DEFAULT NULL,
  `label_guid_json` varchar(2000) COLLATE utf8mb4_icelandic_ci DEFAULT NULL,
  `grade_guid_json` varchar(2000) COLLATE utf8mb4_icelandic_ci DEFAULT NULL,
  `member_info_guid_json` varchar(2000) COLLATE utf8mb4_icelandic_ci DEFAULT NULL,
  `terminal_type` varchar(100) COLLATE utf8mb4_icelandic_ci DEFAULT NULL,
  `marking_label_guid_json` varchar(2000) COLLATE utf8mb4_icelandic_ci DEFAULT NULL,
  `recharge_gift_threshold_json` varchar(2000) COLLATE utf8mb4_icelandic_ci DEFAULT NULL,
  `member_num` int(50) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_guid` (`guid`),
  KEY `oper_subject_guid_idx` (`oper_subject_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_icelandic_ci COMMENT='充值赠送活动';


-- hsm_member_marketing_center_platform_db.hsa_recharge_gift_store definition

CREATE TABLE `hsa_recharge_gift_store` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT 'guid',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0-否 1-是',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `oper_subject_guid` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '运营主体guid',
  `store_guid` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '门店guid',
  `store_name` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '门店guid',
  `recharge_gift_guid` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '赠送活动guid',
  `time` varchar(100) COLLATE utf8mb4_icelandic_ci DEFAULT NULL COMMENT '营业时间',
  `address` varchar(100) COLLATE utf8mb4_icelandic_ci DEFAULT NULL COMMENT '营业地址',
  `address_point` varchar(100) COLLATE utf8mb4_icelandic_ci DEFAULT NULL COMMENT '经纬度',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_guid` (`guid`),
  KEY `oper_subject_guid_idx` (`oper_subject_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_icelandic_ci COMMENT='充值赠送活动门店';


-- hsm_member_marketing_center_platform_db.hsa_recharge_gift_threshold definition

CREATE TABLE `hsa_recharge_gift_threshold` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT 'guid',
  `is_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0-否 1-是',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `oper_subject_guid` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '运营主体guid',
  `recharge_gift_guid` varchar(50) COLLATE utf8mb4_icelandic_ci DEFAULT NULL,
  `gift_threshold_type` tinyint(4) DEFAULT NULL,
  `gift_threshold_amount` decimal(12,2) DEFAULT NULL,
  `gift_threshold_limit_type` tinyint(4) DEFAULT NULL,
  `gift_threshold_limit_sum` tinyint(4) DEFAULT NULL,
  `recharge_gift_json` varchar(1000) COLLATE utf8mb4_icelandic_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`),
  KEY `oper_subject_guid_idx` (`oper_subject_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_icelandic_ci COMMENT='充值赠送门槛';

