-- 等级折扣商品表新增业务字段
ALTER TABLE hsm_member_marketing_platform_db.hsa_grade_rights_commodity_rule
    ADD business_type TINYINT NULL;

-- 商城订单表新增折扣字段
ALTER TABLE hsm_member_mall_platform_db.hsa_mall_base_order
    ADD discount_dynamics decimal(12, 2) NULL;
ALTER TABLE hsm_member_mall_platform_db.hsa_mall_base_order
    ADD discount_type TINYINT NULL;
ALTER TABLE hsm_member_mall_platform_db.hsa_mall_base_order
    ADD grade_name varchar(50) NULL;

-- 积分抵现商品表新增业务字段
ALTER TABLE hsm_member_marketing_platform_db.hsa_integral_deduct_commodity
    ADD business_type TINYINT NULL;

-- 商城订单表新增订单优惠总额、积分抵扣优惠金额
ALTER TABLE hsm_member_mall_platform_db.hsa_mall_base_order
    ADD integral_deduct_amount DECIMAL(12, 2) DEFAULT 0.00 NULL;
ALTER TABLE hsm_member_mall_platform_db.hsa_mall_base_order
    ADD order_preferential_amount DECIMAL(12, 2) DEFAULT 0.00 NULL;


-- 会员积分抵扣记录表

CREATE TABLE `hsm_member_marketing_platform_db`.`hsa_member_integral_deduct_detail`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '会员等级会员价周期累计明细guid',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主体',
  `member_info_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '会员guid',
  `integral_deduct` int(11) NOT NULL COMMENT '抵扣积分',
  `discounted_price` decimal(12, 2) NULL DEFAULT NULL COMMENT '优惠金额',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `order_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单编号',
  `discount_type` tinyint(4) NULL DEFAULT NULL,
  `store_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `store_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `order_price` decimal(12, 2) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


-- 新增成长值任务商品分类表
CREATE TABLE `hsm_member_marketing_platform_db`.`hsa_growth_value_commodity_classify`  (
  `id` bigint(10) NOT NULL AUTO_INCREMENT,
  `task_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '成长值任务guid',
  `business_type` tinyint(4) NOT NULL COMMENT '业务类型(0食堂预订2堂食点餐13会员商城4自营外卖)',
  `store_guid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '门店guid',
  `store_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '门店名称',
  `strategy_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '策略单id',
  `strategy_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '策略单名称',
  `goods_category` json NOT NULL COMMENT '商品类型',
  `category_ids` json NULL COMMENT '所含商品分类id合集',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_task_guid`(`task_guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 44 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE `hsm_member_marketing_platform_db`.`hsa_integral_commodity_classify`  (
  `id` bigint(10) NOT NULL AUTO_INCREMENT,
  `task_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '积分任务guid',
  `business_type` tinyint(4) NOT NULL COMMENT '业务类型(0食堂预订2堂食点餐13会员商城4自营外卖)',
  `store_guid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '门店guid',
  `store_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '门店名称',
  `strategy_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '策略单id',
  `strategy_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '策略单名称',
  `goods_category` json NOT NULL COMMENT '商品类型',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_task_guid`(`task_guid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE `hsm_member_marketing_platform_db`.`hsa_integral_commodity_touch_detail`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '全局主键',
  `integral_task_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '积分任务guid',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运营主体guid',
  `member_info_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '会员guid',
  `buy_type` tinyint(4) NULL DEFAULT NULL,
  `number` int(50) NULL DEFAULT NULL COMMENT '次数/数量',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `member_consumption_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `order_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `order_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员标签表' ROW_FORMAT = DYNAMIC;

-- 新增自营外卖业务

delete from hsm_member_marketing_platform_db.hsa_apply_dictionaries;

INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified)
VALUES(1, 'apply_terminal', 'mini_program', '微信小程序', '2022-01-24 19:56:13', '2022-01-24 19:56:13');
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified)
VALUES(2, 'apply_terminal', 'pos', '一体机', '2022-01-24 19:56:29', '2022-01-24 19:56:29');
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified)
VALUES(4, 'apply_business', '0', '食堂预订', '2022-01-24 19:57:05', '2022-01-24 19:57:05');
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified)
VALUES(6, 'apply_channel', 'pos', 'pos', '2022-01-24 19:57:36', '2022-01-24 19:57:36');
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified)
VALUES(19, 'apply_business', '2', '堂食点餐', '2022-05-27 10:09:19', '2022-05-27 10:09:19');
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified)
VALUES(21, 'apply_business', '13', '会员商城', '2023-03-06 16:58:22', '2023-03-06 16:58:22');
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified)
VALUES(23, 'apply_business', '4', '自营外卖', '2023-05-30 17:32:28', '2023-05-30 17:32:28');
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified)
VALUES(24, 'apply_business', '1', '堂食自助', '2023-05-31 09:58:45', '2023-05-31 09:58:45');
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified)
VALUES(25, 'apply_business', '3', '快速收款', '2023-05-31 09:59:09', '2023-05-31 09:59:09');
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries
(id, module, `type`, type_name, gmt_create, gmt_modified)
VALUES(26, 'apply_channel', 'mall', '商城渠道', '2023-05-31 10:23:28', '2023-05-31 10:23:28');
