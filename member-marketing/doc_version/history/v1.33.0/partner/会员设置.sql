use hsm_member_marketing_platform_db;

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_data_item` MODIFY COLUMN `data_item` text  NULL COMMENT '资料项设置信息（JSON格式）' AFTER `oper_subject_guid`;

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_data_item` ADD COLUMN `platform` varchar(20)  DEFAULT NULL COMMENT '不同平台信息' AFTER `gmt_modified`;

DROP TABLE IF EXISTS `hsa_member_personal`;
CREATE TABLE `hsa_member_personal` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) NOT NULL,
  `member_guid` varchar(50) NOT NULL COMMENT '会员guid',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '主体guid',
  `title` varchar(50) DEFAULT NULL COMMENT '标题',
  `description` text COMMENT '描述',
  PRIMARY KEY (`id`),
  KEY `index_membre_guid` (`member_guid`) USING BTREE,
  KEY `uk_guid` (`guid`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;


DROP TABLE IF EXISTS `hsa_customize_field`;
CREATE TABLE `hsa_customize_field` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `member_guid` varchar(50) NOT NULL COMMENT '会员guid',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '主体guid',
  `field` varchar(20) DEFAULT NULL COMMENT '字段名称',
  `content` text COMMENT '内容数据',
  `info_format` varchar(50) DEFAULT NULL COMMENT '信息格式',
  PRIMARY KEY (`id`),
  KEY `index_member_guid` (`member_guid`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `hsa_member_education`;
CREATE TABLE `hsa_member_education` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) NOT NULL,
  `member_guid` varchar(50) NOT NULL COMMENT '会员guid',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '主体guid',
  `education` varchar(50) DEFAULT NULL COMMENT '学历',
  `school` varchar(50) DEFAULT NULL COMMENT '学校名称',
  `major` varchar(50) DEFAULT NULL COMMENT '专业',
  `admission_time` varchar(20) DEFAULT NULL COMMENT '入学时间',
  `graduation_time` varchar(20) DEFAULT NULL COMMENT '毕业时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`) USING BTREE,
  KEY `index_member_guid` (`member_guid`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `hsa_account_setting`;
CREATE TABLE `hsa_account_setting` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标识',
  `oper_subject_guid` varchar(50) DEFAULT NULL COMMENT '运营主体GUID',
  `login_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '登陆类型：1登录+注册 2仅登录',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员账户设置表';