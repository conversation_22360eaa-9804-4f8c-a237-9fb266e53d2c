use hsm_member_marketing_platform_db;

DROP TABLE IF EXISTS `hsa_member_grade_role`;
-- 用此表替换 hsa_member_grade_role
ALTER TABLE hsm_member_marketing_platform_db.hsa_controlled_grade_state ADD role_type varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'MEMBER' NOT NULL COMMENT '角色体系';
ALTER TABLE hsm_member_marketing_platform_db.hsa_controlled_grade_state ADD name varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '会员等级' NOT NULL COMMENT '体系名称';
ALTER TABLE hsm_member_marketing_platform_db.hsa_controlled_grade_state ADD is_delete tinyint(1) DEFAULT 0 NOT NULL COMMENT '是否删除,0未删除,1已删除';

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_member_grade_info`
ADD COLUMN `role_type` varchar(20) NOT NULL DEFAULT 'MEMBER' COMMENT '角色类型' AFTER `temporary_vip_grade`,
ADD COLUMN `upgrade_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '升级机制：1成长值升级 2付费升级' AFTER `role_type`,
ADD COLUMN `pay_rule` text NULL COMMENT '付费规则' AFTER `upgrade_type`,
ADD COLUMN `pay_limit` tinyint(1) NOT NULL DEFAULT 0 COMMENT '付费限制：0关闭 1开启' AFTER `pay_rule`,
ADD COLUMN `audit_desc` varchar(100) NULL COMMENT '审核说明' AFTER `pay_limit`,
MODIFY COLUMN `growth_value` int(11) NULL COMMENT '成长值';


-- hsm_member_marketing_platform_db.hsa_member_grade_relation definition
DROP TABLE IF EXISTS `hsa_member_grade_relation`;
CREATE TABLE `hsa_member_grade_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除,0未删除,1已删除',
  `member_info_guid` varchar(50) NOT NULL COMMENT '会员GUID',
  `member_info_grade_guid` varchar(50) NOT NULL COMMENT '会员等级GUID',
  `pay_time` datetime DEFAULT NULL COMMENT '付费时间',
  `expire_time` datetime DEFAULT NULL COMMENT '到期时间',
  `role_type` varchar(50) NOT NULL COMMENT '角色类型',
  `num` int(10) DEFAULT NULL COMMENT '有效数量',
  `unit` tinyint(4) DEFAULT NULL COMMENT '有效期单位：3月 4年',
  `is_enable` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用：0未启用 1启用中',
  `source_type` tinyint(4) DEFAULT NULL COMMENT '会员注册来源,0后台添加,1POS机注册,2一体机注册,3后台导入，微网站(21微信扫码点餐，24微信注册.25微信C端后台注册)，微信小程序(51和惠多，52翼惠天下，53赚餐)',
  `temporary_vip_grade` int(12) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `index_member_info_guid` (`member_info_guid`),
  KEY `index_member_grade_guid` (`member_info_grade_guid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员和等级关联表';

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_member_grade_info`
ADD INDEX `index_guid`(`guid`) USING BTREE;

DROP TABLE IF EXISTS `hsa_member_apply_grade_record`;
CREATE TABLE `hsa_member_apply_grade_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `apply_state` tinyint(1) NOT NULL COMMENT '审核状态 0待审核 1 审核通过 2驳回',
  `member_guid` varchar(50) NOT NULL COMMENT '会员guid',
  `member_name` varchar(50) DEFAULT NULL COMMENT '会员name',
  `member_phone` varchar(50) DEFAULT NULL COMMENT '会员phone',
  `current_grade_guid` varchar(50) DEFAULT NULL COMMENT '当前等级guid',
  `current_grade_name` varchar(50) DEFAULT NULL COMMENT '当前等级名称',
  `apply_grade_guid` varchar(50) DEFAULT NULL COMMENT '申请等级guid',
  `apply_grade_name` varchar(50) DEFAULT NULL COMMENT '申请等级名称',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `num` int(12) DEFAULT NULL COMMENT '有效期数量',
  `unit` int(12) DEFAULT NULL COMMENT '付款账户默认选择 1：默认选择',
  `role_type` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_guid` (`guid`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_member_growth_value_detail` MODIFY COLUMN `change_type` tinyint(4) NOT NULL COMMENT '成长值变动类型（1初始成长值，2系统调整，3成长值失效，4消费退款，5成长值任务，6等级权益）' AFTER `store_guid`;

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_member_growth_value_detail` ADD COLUMN `role_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'MEMBER' COMMENT '角色类型' AFTER `multiple_growth_value`;

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_member_growth_value_detail` ADD COLUMN `member_info_grade_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '会员等级GUID' AFTER `role_type`;

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_member_growth_value_detail` ADD COLUMN `expire_time` datetime NULL DEFAULT NULL COMMENT '到期时间' AFTER `member_info_grade_guid`;

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_member_growth_value_detail` ADD COLUMN `num` int(10) NULL DEFAULT NULL COMMENT '有效数量' AFTER `expire_time`;

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_member_growth_value_detail` ADD COLUMN `unit` tinyint(4) NULL DEFAULT NULL COMMENT '有效期单位：3月 4年' AFTER `num`;

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_member_growth_value_detail` ADD COLUMN `upgrade_type` tinyint(4) NULL DEFAULT 1 COMMENT '升级机制：1成长值升级 2付费升级' AFTER `unit`;

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_member_grade_change_detail` ADD COLUMN `role_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'MEMBER' COMMENT '角色类型' AFTER `after_change_growth_value`;


DROP TABLE IF EXISTS `hsa_member_grade_pay_record`;
-- hsm_member_marketing_platform_db.hsa_member_grade_pay_record definition

CREATE TABLE `hsa_member_grade_pay_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除,0未删除,1已删除',
  `member_info_guid` varchar(50) DEFAULT NULL COMMENT '会员GUID',
  `member_info_name` varchar(40) DEFAULT NULL COMMENT '会员名称',
  `phone_num` char(11) DEFAULT NULL COMMENT '会员手机号',
  `member_info_grade_guid` varchar(50) DEFAULT NULL COMMENT '会员等级GUID',
  `member_info_grade_name` varchar(50) DEFAULT NULL COMMENT '会员等级名称',
  `pay_time` datetime DEFAULT NULL COMMENT '付费时间',
  `expire_time` datetime DEFAULT NULL COMMENT '到期时间',
  `role_type` varchar(50) DEFAULT NULL COMMENT '角色类型',
  `num` int(10) DEFAULT NULL COMMENT '有效数量',
  `unit` tinyint(4) DEFAULT NULL COMMENT '有效期单位：3月 4年',
  `change_type` tinyint(4) DEFAULT '0' COMMENT '变动类型',
  `remark` text COMMENT '备注',
  `pay_amount` decimal(12,2) DEFAULT '0.00' COMMENT '付费金额',
  `state` tinyint(4) DEFAULT NULL,
  `store_name` varchar(50) DEFAULT NULL,
  `store_guid` varchar(50) DEFAULT NULL,
  `bank_transaction_id` varchar(100) DEFAULT NULL COMMENT '银行流水号',
  `order_holder_no` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员等级付费记录表';