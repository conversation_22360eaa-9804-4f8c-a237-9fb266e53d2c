use hsm_member_marketing_platform_db;

DROP TABLE IF EXISTS `hsa_dict`;
CREATE TABLE `hsa_dict` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `field` varchar(20) NOT NULL,
  `name` varchar(50) NOT NULL,
  `options` json NOT NULL,
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` int(1) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据字典表';

INSERT INTO `hsa_dict`(`id`, `field`, `name`, `options`, `gmt_create`, `gmt_modified`, `is_delete`) VALUES (1, 'other_service', '商家其他服务', '[{\"key\": \"场地服务\", \"values\": [{\"key\": \"可停车\", \"value\": \"可停车\"}]}, {\"key\": \"店内服务\", \"values\": [{\"key\": \"Wi-Fi\", \"value\": \"Wi-Fi\"}, {\"key\": \"充电宝\", \"value\": \"充电宝\"}, {\"key\": \"充电线\", \"value\": \"充电线\"}, {\"key\": \"寄存区\", \"value\": \"寄存区\"}, {\"key\": \"可带宠物\", \"value\": \"可带宠物\"}, {\"key\": \"吸烟区\", \"value\": \"吸烟区\"}, {\"key\": \"无烟区\", \"value\": \"无烟区\"}, {\"key\": \"电视\", \"value\": \"电视\"}, {\"key\": \"儿童游乐区\", \"value\": \"儿童游乐区\"}]}, {\"key\": \"座位设施\", \"values\": [{\"key\": \"宝宝椅\", \"value\": \"宝宝椅\"}, {\"key\": \"可拼桌\", \"value\": \"可拼桌\"}, {\"key\": \"卡座\", \"value\": \"卡座\"}, {\"key\": \"观景位\", \"value\": \"观景位\"}, {\"key\": \"沙发位\", \"value\": \"沙发位\"}, {\"key\": \"露台\", \"value\": \"露台\"}, {\"key\": \"露天位\", \"value\": \"露天位\"}, {\"key\": \"宴会厅\", \"value\": \"宴会厅\"}, {\"key\": \"包厢\", \"value\": \"包厢\"}, {\"key\": \"大桌\", \"value\": \"大桌\"}]}, {\"key\": \"套餐服务\", \"values\": [{\"key\": \"儿童套餐\", \"value\": \"儿童套餐\"}, {\"key\": \"单人套餐\", \"value\": \"单人套餐\"}, {\"key\": \"午市套餐\", \"value\": \"午市套餐\"}, {\"key\": \"双人套餐\", \"value\": \"双人套餐\"}, {\"key\": \"团队套餐\", \"value\": \"团队套餐\"}, {\"key\": \"自助点餐\", \"value\": \"自助点餐\"}]}, {\"key\": \"结账服务\", \"values\": [{\"key\": \"自助结账\", \"value\": \"自助结账\"}, {\"key\": \"手机支付\", \"value\": \"手机支付\"}, {\"key\": \"可刷卡\", \"value\": \"可刷卡\"}]}]', '2023-04-12 09:19:15', '2023-04-13 10:54:31', 0);
INSERT INTO `hsa_dict`(`id`, `field`, `name`, `options`, `gmt_create`, `gmt_modified`, `is_delete`) VALUES (2, 'dinner_category', '商家餐别', '[{\"key\": \"BREAKFAST\", \"value\": \"早餐\"}, {\"key\": \"LUNCH\", \"value\": \"午餐\"}, {\"key\": \"AFTERNOON_TEA\", \"value\": \"下午茶\"}, {\"key\": \"DINNER\", \"value\": \"晚餐\"}, {\"key\": \"MIDNIGHT_SNACK\", \"value\": \"夜宵\"}]', '2023-04-12 09:24:56', '2023-04-12 09:27:49', 0);
