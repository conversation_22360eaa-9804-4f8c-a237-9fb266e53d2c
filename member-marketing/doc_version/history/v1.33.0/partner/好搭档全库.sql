/*
 Navicat Premium Data Transfer

 Source Server         : sit market
 Source Server Type    : MySQL
 Source Server Version : 50726
 Source Host           : ***************:3306
 Source Schema         : hsm_member_partner_platform_db

 Target Server Type    : MySQL
 Target Server Version : 50726
 File Encoding         : 65001

 Date: 29/07/2023 11:05:41
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for hsa_evaluation_order
-- ----------------------------
DROP TABLE IF EXISTS `hsa_evaluation_order`;
CREATE TABLE `hsa_evaluation_order`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签设置GUID',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运营主体GUID',
  `order_type` tinyint(4) NULL DEFAULT NULL COMMENT '业务类型 0:堂食预定 1：自营外卖 2：食堂订单',
  `member_info_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'memberInfoGuid',
  `order_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单编号',
  `store_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单门店',
  `evaluation` tinyint(4) NULL DEFAULT NULL COMMENT '评价对象 0:对订单评价 1：对商品评价 2：自定义内容评价',
  `member_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名称',
  `member_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户手机',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `evaluate_detailed_json` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评价详情',
  `member_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `store_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `score` decimal(10, 1) NULL DEFAULT NULL,
  `is_custom` tinyint(4) NULL DEFAULT NULL,
  `tag_json` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE,
  INDEX `hsa_evaluation_order_order_num_IDX`(`order_num`) USING BTREE,
  INDEX `hsa_evaluation_order_store_guid_IDX`(`store_guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '标签设置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for hsa_evaluation_rule
-- ----------------------------
DROP TABLE IF EXISTS `hsa_evaluation_rule`;
CREATE TABLE `hsa_evaluation_rule`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT 'guid',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '运营主体guid',
  `is_enable` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否开始 0:未开启 1：开启',
  `valid_day` int(11) NULL DEFAULT NULL COMMENT '评论有效期天数',
  `business` tinyint(1) NULL DEFAULT 1 COMMENT '评价范围 0：部分业务 1:全部业务',
  `business_type` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci NULL DEFAULT NULL COMMENT '业务类型 0:堂食预定 1：自营外卖 2：食堂订单',
  `target_json` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci NULL DEFAULT NULL COMMENT '评价对象 0:对订单评价 1：对商品评价 2：自定义内容评价',
  `dimension_json` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci NULL DEFAULT NULL COMMENT '维度对象',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_icelandic_ci COMMENT = '评价表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hsa_member_friend
-- ----------------------------
DROP TABLE IF EXISTS `hsa_member_friend`;
CREATE TABLE `hsa_member_friend`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `member_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '会员guid',
  `friend_member_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '好友会员guid',
  `heat` int(10) NULL DEFAULT NULL COMMENT '好友热度',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_member_guid`(`member_guid`) USING BTREE,
  INDEX `idx_friend_guid`(`friend_member_guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员好友关系表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hsa_member_label
-- ----------------------------
DROP TABLE IF EXISTS `hsa_member_label`;
CREATE TABLE `hsa_member_label`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0未删除,1已删除',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运营主体GUID',
  `name` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签名字',
  `merchant_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家guid',
  `member_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '会员guid',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hsa_member_location
-- ----------------------------
DROP TABLE IF EXISTS `hsa_member_location`;
CREATE TABLE `hsa_member_location`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `member_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '会员guid',
  `longitude` decimal(15, 10) NOT NULL COMMENT '经度',
  `latitude` decimal(15, 10) NOT NULL COMMENT '纬度',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_member_guid`(`member_guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hsa_merchant
-- ----------------------------
DROP TABLE IF EXISTS `hsa_merchant`;
CREATE TABLE `hsa_merchant`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0未删除,1已删除',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '运营主体GUID',
  `merchant_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家编号',
  `merchant_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家名称',
  `business_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '经营类型guid',
  `leader` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '负责人名称',
  `phone` char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系电话',
  `service_phone` char(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客服电话',
  `area` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家地区',
  `longitude` decimal(15, 10) NULL DEFAULT NULL COMMENT '经度',
  `latitude` decimal(15, 10) NULL DEFAULT NULL COMMENT '纬度',
  `address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家详细地址',
  `head_img` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '门头图',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '状态 0禁用 1启用',
  `authentication_flag` tinyint(1) NULL DEFAULT 0 COMMENT '是否认证 0否 1是',
  `holder_store_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'holder_store_id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE,
  INDEX `business_idx`(`business_guid`) USING BTREE,
  INDEX `phone_idx`(`phone`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for hsa_merchant_auth
-- ----------------------------
DROP TABLE IF EXISTS `hsa_merchant_auth`;
CREATE TABLE `hsa_merchant_auth`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0未删除,1已删除',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '运营主体GUID',
  `business_license` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '营业执照',
  `company_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司名称',
  `company_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '社会统一信用代码',
  `legal_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '法人',
  `id_card_img_front` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证照片-正面',
  `id_card_img_reverse` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证照片-反面',
  `id_card` char(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证',
  `id_card_name` char(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证件姓名',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家信息-认证信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for hsa_merchant_business
-- ----------------------------
DROP TABLE IF EXISTS `hsa_merchant_business`;
CREATE TABLE `hsa_merchant_business`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '经营类型guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0未删除,1已删除',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '运营主体GUID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型名称',
  `parent_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '上级分类guid',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家经营类型' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for hsa_merchant_label
-- ----------------------------
DROP TABLE IF EXISTS `hsa_merchant_label`;
CREATE TABLE `hsa_merchant_label`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0未删除,1已删除',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运营主体GUID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签名称',
  `type` tinyint(1) NULL DEFAULT 0 COMMENT '标签类型 0自动 1手动',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 0禁用 1启用',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家标签' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for hsa_merchant_label_relation
-- ----------------------------
DROP TABLE IF EXISTS `hsa_merchant_label_relation`;
CREATE TABLE `hsa_merchant_label_relation`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运营主体GUID',
  `label_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签guid',
  `merchant_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家guid',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE,
  UNIQUE INDEX `uk_merchant_label_guid`(`label_guid`, `merchant_guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家关联标签' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for hsa_merchant_likes
-- ----------------------------
DROP TABLE IF EXISTS `hsa_merchant_likes`;
CREATE TABLE `hsa_merchant_likes`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运营主体GUID',
  `merchant_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家guid',
  `member_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '会员guid',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_guid`(`merchant_guid`, `member_guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '点赞商家' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for hsa_merchant_management
-- ----------------------------
DROP TABLE IF EXISTS `hsa_merchant_management`;
CREATE TABLE `hsa_merchant_management`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0未删除,1已删除',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运营主体GUID',
  `business_time` json NULL COMMENT '营业时间',
  `dinner_category` json NULL COMMENT '餐别',
  `seats` int(11) NULL DEFAULT NULL COMMENT '餐位',
  `avg_price` decimal(15, 0) NULL DEFAULT NULL COMMENT '人均价',
  `byob_flag` tinyint(1) NULL DEFAULT 1 COMMENT '自带酒水许可 0否 1是',
  `receive_child_flag` tinyint(1) NULL DEFAULT 1 COMMENT '接待儿童 0否 1是',
  `other_service` json NULL COMMENT '其他服务',
  `other_custom_service` json NULL COMMENT '其他自定义服务',
  `introduction` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '店铺简介',
  `optional_label` json NULL COMMENT '自选标签',
  `menu_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单说明',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家信息-经营管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for hsa_merchant_medium
-- ----------------------------
DROP TABLE IF EXISTS `hsa_merchant_medium`;
CREATE TABLE `hsa_merchant_medium`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0未删除,1已删除',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运营主体GUID',
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺logo',
  `main_img` json NULL COMMENT '店铺主图',
  `environment` json NULL COMMENT '店铺环境',
  `video` json NULL COMMENT '店铺视频',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家装修' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for hsa_merchant_menu
-- ----------------------------
DROP TABLE IF EXISTS `hsa_merchant_menu`;
CREATE TABLE `hsa_merchant_menu`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0未删除,1已删除',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标',
  `page` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '页面',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家装修' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for hsa_merchant_menu_relation
-- ----------------------------
DROP TABLE IF EXISTS `hsa_merchant_menu_relation`;
CREATE TABLE `hsa_merchant_menu_relation`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0未删除,1已删除',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运营主体GUID',
  `member_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家guid',
  `menu_list` json NULL COMMENT '菜单列表',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家装修' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for hsa_merchant_reservation_rule
-- ----------------------------
DROP TABLE IF EXISTS `hsa_merchant_reservation_rule`;
CREATE TABLE `hsa_merchant_reservation_rule`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0未删除,1已删除',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运营主体GUID',
  `deposit_flag` tinyint(1) NULL DEFAULT 0 COMMENT '预约是否有订金,0否 1是',
  `deposit` decimal(15, 2) NULL DEFAULT 0.00 COMMENT '订金',
  `order_num` int(11) NULL DEFAULT 10 COMMENT '当天预约单数',
  `max_day` int(11) NULL DEFAULT 0 COMMENT '可预约的时间天数',
  `cancel_day` int(11) NULL DEFAULT 0 COMMENT '取消天数',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家预约规则' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for hsa_merchant_reserve
-- ----------------------------
DROP TABLE IF EXISTS `hsa_merchant_reserve`;
CREATE TABLE `hsa_merchant_reserve`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '预定订单guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除,0未删除,1已删除',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运营主体GUID',
  `order_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单编号',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '订单状态',
  `reserve_date` date NULL DEFAULT NULL COMMENT '预订日期',
  `reserve_time` time NULL DEFAULT NULL COMMENT '预订时间',
  `member_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预约人guid',
  `reserve_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留名称',
  `reserve_phone` char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留电话',
  `area_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预订区域guid',
  `area_name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预订区域名称',
  `num` int(11) NULL DEFAULT NULL COMMENT '预订人数',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '留言',
  `reserve_type` tinyint(4) NULL DEFAULT 1 COMMENT '预约类型：1本人预订 2代预订',
  `deposit` decimal(10, 2) NULL DEFAULT NULL COMMENT '预订金额',
  `cancel_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取消原因',
  `cancel_type` tinyint(4) NULL DEFAULT NULL COMMENT '取消类型',
  `cancel_time` datetime NULL DEFAULT NULL COMMENT '取消时间',
  `verify_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '核销码',
  `verify_time` datetime NULL DEFAULT NULL COMMENT '核销时间',
  `merchant_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家guid',
  `merchant_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家名称',
  `merchant_logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家logo',
  `pay_time` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `pay_way` tinyint(4) NULL DEFAULT NULL COMMENT '支付方式',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `uk_guid`(`guid`) USING BTREE,
  INDEX `idx_order_no`(`order_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家预定表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hsa_merchant_room
-- ----------------------------
DROP TABLE IF EXISTS `hsa_merchant_room`;
CREATE TABLE `hsa_merchant_room`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0未删除,1已删除',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运营主体GUID',
  `merchant_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家guid',
  `name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '包厢名称',
  `img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '包厢图片',
  `num` int(11) NULL DEFAULT NULL COMMENT '包厢人数',
  `area` decimal(15, 2) NULL DEFAULT NULL COMMENT '包厢大小',
  `min_use_amount` decimal(15, 2) NULL DEFAULT NULL COMMENT '最低消费',
  `introduction` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '包厢介绍',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE,
  INDEX `merchant_idx`(`merchant_guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家包厢' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for hsa_message_apply_record
-- ----------------------------
DROP TABLE IF EXISTS `hsa_message_apply_record`;
CREATE TABLE `hsa_message_apply_record`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `apply_member_guid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '申请人guid',
  `apply_type` tinyint(2) NOT NULL COMMENT '申请类型0申请为好友1申请推荐',
  `apply_state` tinyint(2) NOT NULL COMMENT '申请结果0拒绝1申请中2申请成功3引荐中4引荐成功5引荐被拒绝',
  `principal_member_guid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '被申请人或引荐人',
  `introduced_member_guid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '被引荐人',
  `verify_message` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '验证信息',
  `apply_time` datetime NOT NULL COMMENT '申请时间',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_index_guid`(`guid`) USING BTREE,
  INDEX `index_principal`(`principal_member_guid`) USING BTREE,
  INDEX `index_introduced`(`introduced_member_guid`) USING BTREE,
  INDEX `index_apply_principal`(`apply_member_guid`, `principal_member_guid`) USING BTREE,
  INDEX `index_apply_principal_introduced`(`apply_member_guid`, `principal_member_guid`, `introduced_member_guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员消息申请记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hsa_offline_activity
-- ----------------------------
DROP TABLE IF EXISTS `hsa_offline_activity`;
CREATE TABLE `hsa_offline_activity`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0未删除,1已删除',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运营主体GUID',
  `activity_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动编号',
  `activity_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动名称',
  `start_time` date NOT NULL COMMENT '活动开始时间',
  `end_time` date NOT NULL COMMENT '活动结束时间',
  `sponsor` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主办方',
  `phone` char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系电话',
  `address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动地点',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '线下活动' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for hsa_reserve_transaction_record
-- ----------------------------
DROP TABLE IF EXISTS `hsa_reserve_transaction_record`;
CREATE TABLE `hsa_reserve_transaction_record`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `guid` bigint(20) UNSIGNED NOT NULL COMMENT '全局唯一主键',
  `gmt_create` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 0：false,1:true',
  `enterprise_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '企业guid',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '运营主体GUID',
  `order_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单guid',
  `order_holder_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '聚合支付订单号',
  `bank_transaction_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '银行流水号',
  `amount` decimal(15, 2) NULL DEFAULT 0.00 COMMENT '交易金额',
  `state` int(11) NULL DEFAULT NULL COMMENT '0：待支付 1：支付中 2：支付成功 3：支付失败 ',
  `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `store_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '门店名',
  `store_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '门店guid',
  `refund_time` datetime NULL DEFAULT NULL COMMENT '退款时间',
  `pay_time` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `pay_way` tinyint(8) NULL DEFAULT NULL COMMENT '支付方式',
  `pay_way_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付方式名称',
  `app_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '聚合支付账号',
  `app_secret` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '聚合支付账号密钥',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order_guid`(`order_guid`) USING BTREE,
  INDEX `idx_store_guid`(`store_guid`) USING BTREE,
  INDEX `idx_order_holder_no`(`order_holder_no`) USING BTREE,
  INDEX `idx_gmt`(`gmt_create`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '预定交易记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hsa_room_reserve_record
-- ----------------------------
DROP TABLE IF EXISTS `hsa_room_reserve_record`;
CREATE TABLE `hsa_room_reserve_record`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `reserve_order_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预约订单guid',
  `merchant_room_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '门店包厢guid',
  `reserve_date` date NOT NULL COMMENT '预约日期',
  `reserve_time` time NOT NULL COMMENT '预约时间',
  `state` tinyint(1) NOT NULL COMMENT '预约状态0可预约1已满',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '记录是否被删除',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_room_guid`(`merchant_room_guid`) USING BTREE,
  INDEX `index_order_guid`(`reserve_order_guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
