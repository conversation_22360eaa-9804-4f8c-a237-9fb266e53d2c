use hsm_member_marketing_platform_db;

-- hsm_member_marketing_platform_db.hsa_partner_merchant_permission_type definition
DROP TABLE IF EXISTS `hsa_partner_merchant_permission_type`;

CREATE TABLE `hsa_partner_merchant_permission_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `enterprise_guid` varchar(50) NOT NULL DEFAULT '' COMMENT '企业guid',
  `position_guid` varchar(50) NOT NULL COMMENT '岗位id和角色id',
  `source_type` tinyint(4) DEFAULT NULL COMMENT '1：会员管理   2：营销中心',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_role` tinyint(4) DEFAULT NULL COMMENT '权限类型（0：岗位  1：角色）',
  `is_all` tinyint(4) DEFAULT NULL COMMENT '设置的类型（1：全部  0：部分）默认为全部',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='商家权限类型表（用于判断拥有全部权限还是部分权限）';


-- hsm_member_marketing_platform_db.hsa_partner_merchant_permission definition
DROP TABLE IF EXISTS `hsa_partner_merchant_permission`;

CREATE TABLE `hsa_partner_merchant_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `enterprise_guid` varchar(50) NOT NULL DEFAULT '' COMMENT '企业guid',
  `position_guid` varchar(50) NOT NULL COMMENT '岗位id和角色id',
  `merchant_guid` varchar(50) DEFAULT NULL COMMENT '商家guid',
  `source_type` tinyint(4) DEFAULT NULL COMMENT '1：会员管理   2：营销中心',
  `merchant_name` varchar(50) DEFAULT NULL COMMENT '商家名称',
  `is_checked` tinyint(4) DEFAULT NULL COMMENT '是否选择（1：选中，0未选中）',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_role` tinyint(4) DEFAULT NULL COMMENT '权限类型（0：岗位  1：角色）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家权限表';
