use hsm_member_marketing_platform_db;
-- 线下活动关联会员
DROP TABLE IF EXISTS `hsa_offline_activity_member_relation`;
CREATE TABLE `hsa_offline_activity_member_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',
  `activity_guid` varchar(50) NOT NULL COMMENT '活动guid',
  `member_guid` varchar(50) NOT NULL COMMENT '会员guid',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_activity_member_guid`(`activity_guid`,`member_guid`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 ROW_FORMAT = DYNAMIC COMMENT = '线下活动关联会员';