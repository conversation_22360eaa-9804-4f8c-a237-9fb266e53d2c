-- holder权限 
 -- sql执行完请刷新holder权限redis

 use gateway;



-- 运营主体
INSERT INTO hp_function_group
(create_time, name, `sequence`, update_time, gmt_create, gmt_modified, group_identification)
VALUES(CURRENT_TIMESTAMP, '运营主体', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, null);



-- 运营主体
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '运营主体', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '运营主体' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');




INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '运营主体管理范围', NOW(),
     (select id from hp_function where name = '运营主体' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '完整手机号', NOW(),
     (select id from hp_function where name = '运营主体' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());




-- 会员账户管理
INSERT INTO hp_function_group
(create_time, name, `sequence`, update_time, gmt_create, gmt_modified, group_identification)
VALUES(CURRENT_TIMESTAMP, '会员账户管理', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, null);


-- 会员账户管理
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '会员账户列表', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '会员账户管理' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '会员账户详情', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '会员账户管理' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


-- 会员账户详情
INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看会员账户详情', NOW(),
     (select id from hp_function where name = '会员账户详情' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '禁用', NOW(),
     (select id from hp_function where name = '会员账户详情' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '启用', NOW(),
     (select id from hp_function where name = '会员账户详情' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '修改手机号', NOW(),
     (select id from hp_function where name = '会员账户详情' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '修改归属门店', NOW(),
     (select id from hp_function where name = '会员账户详情' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '解绑归属门店', NOW(),
     (select id from hp_function where name = '会员账户详情' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看门店变更记录', NOW(),
     (select id from hp_function where name = '会员账户详情' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '编辑基本信息', NOW(),
     (select id from hp_function where name = '会员账户详情' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看会员标签', NOW(),
     (select id from hp_function where name = '会员账户详情' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '关联标签', NOW(),
     (select id from hp_function where name = '会员账户详情' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看关联标签记录', NOW(),
     (select id from hp_function where name = '会员账户详情' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '自动关联', NOW(),
     (select id from hp_function where name = '会员账户详情' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '解除关联', NOW(),
     (select id from hp_function where name = '会员账户详情' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看成长明细', NOW(),
     (select id from hp_function where name = '会员账户详情' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '导出', NOW(),
     (select id from hp_function where name = '会员账户详情' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '调整等级', NOW(),
     (select id from hp_function where name = '会员账户详情' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看权益记录', NOW(),
     (select id from hp_function where name = '会员账户详情' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看交易明细', NOW(),
     (select id from hp_function where name = '会员账户详情' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '消费订单', NOW(),
     (select id from hp_function where name = '会员账户详情' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());









-- 会员账户列表
INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看会员账户列表', NOW(),
     (select id from hp_function where name = '会员账户列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '批量关联标签', NOW(),
     (select id from hp_function where name = '会员账户列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '批量调整等级', NOW(),
     (select id from hp_function where name = '会员账户列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '批量修改归属门店', NOW(),
     (select id from hp_function where name = '会员账户列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '批量禁用', NOW(),
     (select id from hp_function where name = '会员账户列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '批量启用', NOW(),
     (select id from hp_function where name = '会员账户列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '添加会员', NOW(),
     (select id from hp_function where name = '会员账户列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '批量导入', NOW(),
     (select id from hp_function where name = '会员账户列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '导出', NOW(),
     (select id from hp_function where name = '会员账户列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看会员账户列表', NOW(),
     (select id from hp_function where name = '会员账户列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_company_system
(company_id, system_id, create_time, gmt_create, gmt_modified)
VALUES(2570, (select id from hp_system where name = '好搭档平台' order by gmt_modified desc limit 1), CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);









-- 会员标签管理
INSERT INTO hp_function_group
(create_time, name, `sequence`, update_time, gmt_create, gmt_modified, group_identification)
VALUES(CURRENT_TIMESTAMP, '会员标签管理', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, null);


INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '会员标签列表', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '会员标签管理' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


-- 会员标签列表
INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看会员标签列表', NOW(),
     (select id from hp_function where name = '会员标签列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '导出', NOW(),
     (select id from hp_function where name = '会员标签列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '新增标签', NOW(),
     (select id from hp_function where name = '会员标签列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '禁用', NOW(),
     (select id from hp_function where name = '会员标签列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '启用', NOW(),
     (select id from hp_function where name = '会员标签列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看标签详情', NOW(),
     (select id from hp_function where name = '会员标签列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '编辑标签', NOW(),
     (select id from hp_function where name = '会员标签列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '删除标签', NOW(),
     (select id from hp_function where name = '会员标签列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



-- 标签关联会员
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '标签关联会员', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '会员标签管理' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');

-- 标签关联会员

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看标签关联会员', NOW(),
     (select id from hp_function where name = '标签关联会员' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '新增关联会员', NOW(),
     (select id from hp_function where name = '标签关联会员' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '批量自动关联', NOW(),
     (select id from hp_function where name = '标签关联会员' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '批量解除关联', NOW(),
     (select id from hp_function where name = '标签关联会员' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '导出', NOW(),
     (select id from hp_function where name = '标签关联会员' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());




-- 会员等级
INSERT INTO hp_function_group
(create_time, name, `sequence`, update_time, gmt_create, gmt_modified, group_identification)
VALUES(CURRENT_TIMESTAMP, '会员等级', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, null);

-- 会员等级
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '会员等级', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '会员等级' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '添加等级体系', NOW(),
     (select id from hp_function where name = '会员等级' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '等级体系启用/禁用', NOW(),
     (select id from hp_function where name = '会员等级' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


-- 等级权益规则
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '等级权益规则', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '会员等级' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');




INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看等级规则', NOW(),
     (select id from hp_function where name = '等级权益规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '新增等级', NOW(),
     (select id from hp_function where name = '等级权益规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '详情', NOW(),
     (select id from hp_function where name = '等级权益规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '编辑', NOW(),
     (select id from hp_function where name = '等级权益规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());




INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '更新会员等级', NOW(),
     (select id from hp_function where name = '等级权益规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '删除', NOW(),
     (select id from hp_function where name = '等级权益规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '付费记录', NOW(),
     (select id from hp_function where name = '等级权益规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '导出付费记录', NOW(),
     (select id from hp_function where name = '等级权益规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '申请记录', NOW(),
     (select id from hp_function where name = '等级权益规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '通过申请', NOW(),
     (select id from hp_function where name = '等级权益规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '驳回申请', NOW(),
     (select id from hp_function where name = '等级权益规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '导出申请', NOW(),
     (select id from hp_function where name = '等级权益规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看等级用户', NOW(),
     (select id from hp_function where name = '等级权益规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '导出等级用户', NOW(),
     (select id from hp_function where name = '等级权益规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '调整等级', NOW(),
     (select id from hp_function where name = '等级权益规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());




-- 会员设置
INSERT INTO hp_function_group
(create_time, name, `sequence`, update_time, gmt_create, gmt_modified, group_identification)
VALUES(CURRENT_TIMESTAMP, '会员设置', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, null);



-- 会员账户管理
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '资料项设置', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '会员设置' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看', NOW(),
     (select id from hp_function where name = '资料项设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '添加自定义', NOW(),
     (select id from hp_function where name = '资料项设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '编辑', NOW(),
     (select id from hp_function where name = '资料项设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

-- 账户设置
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '账户设置', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '会员设置' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看', NOW(),
     (select id from hp_function where name = '账户设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '编辑', NOW(),
     (select id from hp_function where name = '账户设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



-- 商家管理
INSERT INTO hp_function_group
(create_time, name, `sequence`, update_time, gmt_create, gmt_modified, group_identification)
VALUES(CURRENT_TIMESTAMP, '商家管理', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, null);


-- 商家列表
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '商家列表', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '商家管理' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看商家列表', NOW(),
     (select id from hp_function where name = '商家列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '新增商家', NOW(),
     (select id from hp_function where name = '商家列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '禁用', NOW(),
     (select id from hp_function where name = '商家列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '启用', NOW(),
     (select id from hp_function where name = '商家列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看商家详情', NOW(),
     (select id from hp_function where name = '商家列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '编辑商家', NOW(),
     (select id from hp_function where name = '商家列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


-- 商家经营类型
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '商家经营类型', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '商家管理' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看商家经营类型', NOW(),
     (select id from hp_function where name = '商家经营类型' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '新增一级分类', NOW(),
     (select id from hp_function where name = '商家经营类型' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '新增子分类', NOW(),
     (select id from hp_function where name = '商家经营类型' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '编辑分类', NOW(),
     (select id from hp_function where name = '商家经营类型' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '删除分类', NOW(),
     (select id from hp_function where name = '商家经营类型' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());





-- 商家标签管理
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '商家标签管理', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '商家管理' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看商家标签管理', NOW(),
     (select id from hp_function where name = '商家标签管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '新增标签', NOW(),
     (select id from hp_function where name = '商家标签管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '禁用', NOW(),
     (select id from hp_function where name = '商家标签管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '启用', NOW(),
     (select id from hp_function where name = '商家标签管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看标签详情', NOW(),
     (select id from hp_function where name = '商家标签管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '编辑标签', NOW(),
     (select id from hp_function where name = '商家标签管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '删除标签', NOW(),
     (select id from hp_function where name = '商家标签管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());




-- 标签关联商家
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '标签关联商家', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '商家管理' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看标签关联商家', NOW(),
     (select id from hp_function where name = '标签关联商家' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '新增关联商家', NOW(),
     (select id from hp_function where name = '标签关联商家' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '批量解除关联', NOW(),
     (select id from hp_function where name = '标签关联商家' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());




-- 商家订单管理
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '商家订单管理', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '商家管理' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看订单', NOW(),
     (select id from hp_function where name = '商家订单管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看订单详情', NOW(),
     (select id from hp_function where name = '商家订单管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '导出订单', NOW(),
     (select id from hp_function where name = '商家订单管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '确认核销', NOW(),
     (select id from hp_function where name = '商家订单管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '取消订单', NOW(),
     (select id from hp_function where name = '商家订单管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());







-- 线下活动管理
INSERT INTO hp_function_group
(create_time, name, `sequence`, update_time, gmt_create, gmt_modified, group_identification)
VALUES(CURRENT_TIMESTAMP, '线下活动管理', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, null);



-- 线下活动
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '线下活动', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '线下活动管理' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看活动列表', NOW(),
     (select id from hp_function where name = '线下活动' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '新建活动', NOW(),
     (select id from hp_function where name = '线下活动' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看活动详情', NOW(),
     (select id from hp_function where name = '线下活动' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '编辑', NOW(),
     (select id from hp_function where name = '线下活动' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '删除', NOW(),
     (select id from hp_function where name = '线下活动' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看参与人员', NOW(),
     (select id from hp_function where name = '线下活动' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '导出参与人员', NOW(),
     (select id from hp_function where name = '线下活动' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());




-- 订单评价
INSERT INTO hp_function_group
(create_time, name, `sequence`, update_time, gmt_create, gmt_modified, group_identification)
VALUES(CURRENT_TIMESTAMP, '订单评价', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, null);


-- 订单评价列表
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '订单评价列表', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '订单评价' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '订单评价列表查看', NOW(),
     (select id from hp_function where name = '订单评价列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


-- 订单评价规则
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '订单评价规则', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '订单评价' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '开启/关闭订单评价', NOW(),
     (select id from hp_function where name = '订单评价规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看', NOW(),
     (select id from hp_function where name = '订单评价规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '编辑', NOW(),
     (select id from hp_function where name = '订单评价规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



-- 商家工具
INSERT INTO hp_function_group
(create_time, name, `sequence`, update_time, gmt_create, gmt_modified, group_identification)
VALUES(CURRENT_TIMESTAMP, '商家工具', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, null);

-- 店铺管理范围
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '店铺管理范围', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '商家工具' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '各门店数据管理范围', NOW(),
     (select id from hp_function where name = '店铺管理范围' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



-- 预约管理
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '预约管理', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '商家工具' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '预约管理查看', NOW(),
     (select id from hp_function where name = '预约管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '核销订单', NOW(),
     (select id from hp_function where name = '预约管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '预约规则设置', NOW(),
     (select id from hp_function where name = '预约管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '取消订单', NOW(),
     (select id from hp_function where name = '预约管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看会员信息', NOW(),
     (select id from hp_function where name = '预约管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());




-- 店铺管理
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '店铺管理', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '商家工具' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '经营信息查看', NOW(),
     (select id from hp_function where name = '店铺管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '经营信息编辑', NOW(),
     (select id from hp_function where name = '店铺管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '店铺装修查看', NOW(),
     (select id from hp_function where name = '店铺管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '店铺装修编辑', NOW(),
     (select id from hp_function where name = '店铺管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '店铺包厢查看', NOW(),
     (select id from hp_function where name = '店铺管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '店铺包厢编辑', NOW(),
     (select id from hp_function where name = '店铺管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



-- 菜单管理
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '菜单管理', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '商家工具' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '店铺菜单查看', NOW(),
     (select id from hp_function where name = '菜单管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '店铺菜单编辑', NOW(),
     (select id from hp_function where name = '菜单管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



-- 会员评价
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '会员评价', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '商家工具' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '会员评价查看', NOW(),
     (select id from hp_function where name = '会员评价' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());




-- 营业数据
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '营业数据', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '商家工具' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '营业额+订单数量', NOW(),
     (select id from hp_function where name = '营业数据' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



-- 财务对账
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '财务对账', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '商家工具' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看', NOW(),
     (select id from hp_function where name = '财务对账' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


-- 活动中心
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '活动中心', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '商家工具' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看', NOW(),
     (select id from hp_function where name = '活动中心' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



-- 门店人员
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '门店人员', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '商家工具' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看', NOW(),
     (select id from hp_function where name = '门店人员' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



-- 精细化排版
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '精细化排版', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '商家工具' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看', NOW(),
     (select id from hp_function where name = '精细化排版' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


-- 薪酬结算
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '薪酬结算', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '商家工具' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看', NOW(),
     (select id from hp_function where name = '薪酬结算' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


-- 门店资产
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '门店资产', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '商家工具' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看', NOW(),
     (select id from hp_function where name = '门店资产' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


-- 门店采购
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '门店采购', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '商家工具' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看', NOW(),
     (select id from hp_function where name = '门店采购' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



-- 智能门店
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '智能门店', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '商家工具' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');


INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看', NOW(),
     (select id from hp_function where name = '智能门店' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



-- 食安监管
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '食安监管', 1, 60, CURRENT_TIMESTAMP,26,
       ( SELECT id from hp_system WHERE name = '好搭档平台' AND identification = 'member_partner' ORDER BY gmt_modified DESC LIMIT 1 ),
      (select id from hp_function_group where name = '商家工具' order by gmt_modified desc limit 1),
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');



INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
                          `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
    (CURRENT_TIMESTAMP, NULL, '查看', NOW(),
     (select id from hp_function where name = '食安监管' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());