-- 业务系统
INSERT INTO gateway.hp_system
(address, create_time, description, identification, login_type, name, single, status, support_type, update_time, version, gmt_create, gmt_modified, display)
VALUES('https://member-partner.holderzone.com', CURRENT_TIMESTAMP, null, 'member_partner', null, '好搭档平台',null, 0, null, CURRENT_TIMESTAMP,NULL, CURRENT_TIMESTAMP,
CURRENT_TIMESTAMP, 1);

-- 指定企业增加系统（每个企业都要加，只加一次） 2570为holder企业id
-- todo 暂未确定企业
-- INSERT INTO gateway.hp_company_system
-- (company_id, system_id, create_time, gmt_create, gmt_modified)
-- VALUES(2570, (select id from hp_system where identification = 'member_partner' order by gmt_modified desc limit 1), CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);


 -- 应用
 INSERT INTO team.app_info
 (name, relation_id, `type`, description, logo, is_third)
 VALUES('好搭档平台', (select id from gateway.hp_system hs where identification = 'member_partner' limit 1), 1, '', '', 1);

 -- 应用在分类下：6 市场销售
 INSERT INTO team.app_info_relation_type (app_info_id, app_type_id)
 select id,6 from team.app_info where name in ('好搭档平台');