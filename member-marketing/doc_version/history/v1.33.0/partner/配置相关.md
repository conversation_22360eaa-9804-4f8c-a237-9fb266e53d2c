# 服务

# 好搭档可发布分支
```
v1.33.0

附加 feature_optimized/会员base服务调整.sql
```


```yml
#好搭档
holder-member-base
holder-member-gateway
holder-member-partner-applet
holder-member-partner-platform
# 不发 holder-member-pay 
holder-member-queue
holder-member-good_partner_web
holder-member-xxl-job-executor
# 优化
holder-member-commodity
holder-member-mall
holder-member-mall-tool
holder-member-marketing
# web调整
holder-member-marketing-center-web  # url:  ?token=&enterpriseGuid=2570
holder-member-marketing-web	        # url:  ?token=&enterpriseGuid=2570
holder-member-mall-tool-web	        # url:  ?token=&enterpriseGuid=2570
holder-member-mall-web		        # url:  ?token=&enterpriseGuid=2570

# 外部服务
食堂
holder
```

# yml配置

1. base.yml
2. gateway.yml
3. member-queue-test.yaml
4. member-job-executor.yaml
5. datasource-partner.yaml 全部
6. member-partner-platform.yaml 全部
7. member-partner-applet.yaml  全部


# 资料项配置人脸
-- 清空人脸，再重新初始化主题
-- delete from hsa_data_item where oper_subject_guid = '61';

# 接口初始化

1. 运营主体初始化  goalgo_callback/initialize_subject_data?operSubjectGuid=
2. 好搭档初始化    goalgo_callback/initialize_partner_data?operSubjectGuid=
2. holder权限sql执行，数据权限接口执行，刷新权限接口执行

# mq 配置

共用member的

# 定时器

需要创建并启动
1. 预定到期结束             ReserveAutoCancelJopHandler  0 */1 * * * ?
2. 会员等级过期处理          MemberGradeExpireJobHandler  0 * * * * ?
3. 查询即将过期等级任务处理    QueryGradeExpireJobHandler  0 0 0 * * ?
4. 预定核销超时自动取消       ReserveVerifyAutoCancelJopHandler  0 */1 * * * ?
5. 保存每日卡余额    MemberInfoCardTotalJopHandler  0 0 0 * * ?

# 线上数仓增加表：
每日卡余额统计 hsa_member_info_card_total
mateBase 调整金额日报表  [勐海金额日报表.sql](..%2F..%2Freport%2F%DB%C2%BA%A3%BD%F0%B6%EE%C8%D5%B1%A8%B1%ED.sql)