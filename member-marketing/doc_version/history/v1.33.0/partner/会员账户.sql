use hsm_member_marketing_platform_db;

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_operation_member_info` MODIFY COLUMN `sex` tinyint(4) NULL DEFAULT 0 COMMENT '性别，值为1时是男性，值为2时是女性，值为0时是未知' AFTER `user_name`;

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_operation_member_info` ADD COLUMN `industry` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所在行业' AFTER `member_num`;

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_operation_member_info` ADD COLUMN `job_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '职位名称' AFTER `industry`;

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_operation_member_info` ADD COLUMN `introduction` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '个人简介' AFTER `job_title`;

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_operation_member_info` ADD COLUMN `role_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'MEMBER' COMMENT '角色类型' AFTER `introduction`;

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_operation_member_info` ADD COLUMN `industry_codes` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所在行业codes' AFTER `role_type`;

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_operation_member_info` ADD COLUMN `company_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司地址' AFTER `industry_codes`;

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_operation_member_info` ADD COLUMN `last_login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间';

ALTER TABLE hsm_member_marketing_platform_db.hsp_pay_record ADD COLUMN call_back_status int(1) default NULL COMMENT '回调状态0失败1成功';

-- 欢迎信息
CREATE TABLE `hsa_welcome_message` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) NOT NULL COMMENT '资料项设置guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `welcome_message` varchar(500) DEFAULT NULL COMMENT '欢迎信息',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='欢迎信息';

insert into hsa_welcome_message(guid,welcome_message) values (1,'大事要敢想，小事要一点点做');
insert into hsa_welcome_message(guid,welcome_message) values (2,'给岁月以生命，而不是给生命以岁月');
insert into hsa_welcome_message(guid,welcome_message) values (3,'与强者竞争，更容易成为强者');
insert into hsa_welcome_message(guid,welcome_message) values (4,'行难事必有所得');
insert into hsa_welcome_message(guid,welcome_message) values (5,'事业常成于坚忍，毁于急躁');
insert into hsa_welcome_message(guid,welcome_message) values (6,'过程管好了，结果不会差');
insert into hsa_welcome_message(guid,welcome_message) values (7,'知道没有能量，行动才有力量');
insert into hsa_welcome_message(guid,welcome_message) values (10,'唯一知道的事就是我一无所知');
insert into hsa_welcome_message(guid,welcome_message) values (11,'活着就要学习，学习不是为了活着');
insert into hsa_welcome_message(guid,welcome_message) values (12,'诚实者既不怕光明，也不怕黑暗');
insert into hsa_welcome_message(guid,welcome_message) values (13,'志士惜年，贤人惜日，圣人惜时');
insert into hsa_welcome_message(guid,welcome_message) values (14,'只要心是晴朗的，就没有雨天');
insert into hsa_welcome_message(guid,welcome_message) values (15,'光阴是有脚当珍惜，书田无税应勤耕');
insert into hsa_welcome_message(guid,welcome_message) values (16,'辛勤的蜜蜂永没有时间悲哀');
insert into hsa_welcome_message(guid,welcome_message) values (17,'谁不能主宰自己，永远是一个奴隶');
insert into hsa_welcome_message(guid,welcome_message) values (18,'不要在意犯错，但是一定要在意修正');
insert into hsa_welcome_message(guid,welcome_message) values (19,'希望是厄运的忠实的姐妹');
insert into hsa_welcome_message(guid,welcome_message) values (20,'人的价值是由自己决定的');
insert into hsa_welcome_message(guid,welcome_message) values (21,'君子喻于义，小人喻于利');
insert into hsa_welcome_message(guid,welcome_message) values (22,'思路决定出路，布局决定结局');
insert into hsa_welcome_message(guid,welcome_message) values (23,'最可怕的敌人，就是没有坚强的信念');
insert into hsa_welcome_message(guid,welcome_message) values (24,'信念是人生的火把');
insert into hsa_welcome_message(guid,welcome_message) values (25,'理想是世界的主宰');
insert into hsa_welcome_message(guid,welcome_message) values (26,'毅力可以征服任何一座高峰');
insert into hsa_welcome_message(guid,welcome_message) values (27,'所有欺骗中，自欺是最为严重的');
insert into hsa_welcome_message(guid,welcome_message) values (28,'当你能飞的时候就不要放弃飞');
insert into hsa_welcome_message(guid,welcome_message) values (29,'经验是由痛苦中粹取出来的');
insert into hsa_welcome_message(guid,welcome_message) values (30,'人生最大的错误是不断担心会犯错');
insert into hsa_welcome_message(guid,welcome_message) values (31,'一个今天胜过两个明天');
insert into hsa_welcome_message(guid,welcome_message) values (32,'“不可能”只存在于蠢人的字典里');
insert into hsa_welcome_message(guid,welcome_message) values (33,'世上最累人的事，莫过于虚伪的过日子');
insert into hsa_welcome_message(guid,welcome_message) values (34,'对产品质量来说，不是100分就是0分');
insert into hsa_welcome_message(guid,welcome_message) values (35,'理想的路总是为有信心的人预备着');
insert into hsa_welcome_message(guid,welcome_message) values (36,'行就行，不行我再想想办法');
insert into hsa_welcome_message(guid,welcome_message) values (37,'如果你曾歌颂黎明，那么也请你拥抱黑夜');
insert into hsa_welcome_message(guid,welcome_message) values (38,'我们内心早有答案，却一直向外张望');
insert into hsa_welcome_message(guid,welcome_message) values (39,'发光并非太阳的专利，你也可以发光');
insert into hsa_welcome_message(guid,welcome_message) values (40,'肯承认错误则错已改了一半');
insert into hsa_welcome_message(guid,welcome_message) values (41,'想象力比知识更重要');
insert into hsa_welcome_message(guid,welcome_message) values (42,'不骄方能师人之长，而自成其学');
insert into hsa_welcome_message(guid,welcome_message) values (43,'重复是学习之母');
insert into hsa_welcome_message(guid,welcome_message) values (44,'一个人可以被毁灭，但不能被打败');
insert into hsa_welcome_message(guid,welcome_message) values (45,'浅水是喧哗的，深水是沉默的');
insert into hsa_welcome_message(guid,welcome_message) values (46,'创新要从点滴小事做起');
insert into hsa_welcome_message(guid,welcome_message) values (47,'创造性的模仿，也是创新的战略之一');
insert into hsa_welcome_message(guid,welcome_message) values (48,'宁愿做过了后悔，也不要错过了后悔');
insert into hsa_welcome_message(guid,welcome_message) values (49,'不能预见明天，但可以把握今天');
insert into hsa_welcome_message(guid,welcome_message) values (50,'企业和企业之间的差距就是人的差距');
insert into hsa_welcome_message(guid,welcome_message) values (51,'总之岁月漫长，然而值得等待');
insert into hsa_welcome_message(guid,welcome_message) values (52,'心中无敌，无敌于天下');
insert into hsa_welcome_message(guid,welcome_message) values (53,'凡是过去，皆为序章');
insert into hsa_welcome_message(guid,welcome_message) values (54,'我走得很慢，但我从不后退');
insert into hsa_welcome_message(guid,welcome_message) values (55,'我荒废了时间，时间便把我荒废了');
insert into hsa_welcome_message(guid,welcome_message) values (56,'月缺不改光，剑折不改刚');
insert into hsa_welcome_message(guid,welcome_message) values (57,'人生没有白走的路，每一步都算数');
insert into hsa_welcome_message(guid,welcome_message) values (58,'成功就是从失败到失败，也依然不改热情');
insert into hsa_welcome_message(guid,welcome_message) values (59,'方向是比速度更重要的追求');
insert into hsa_welcome_message(guid,welcome_message) values (60,'时间是伟大的作者，她能写出未来的结局');
insert into hsa_welcome_message(guid,welcome_message) values (61,'先相信你自己，然后别人才会相信你');
insert into hsa_welcome_message(guid,welcome_message) values (62,'有航道的人，再渺小也不会迷途');
insert into hsa_welcome_message(guid,welcome_message) values (63,'失败是坚忍的最后考验');
insert into hsa_welcome_message(guid,welcome_message) values (64,'宿命论是那些缺乏意志力的弱者的借口');
insert into hsa_welcome_message(guid,welcome_message) values (65,'知识是珍宝，但实践是得到它的钥匙');
insert into hsa_welcome_message(guid,welcome_message) values (66,'苦难磨炼一些人，也毁灭另一些人');
insert into hsa_welcome_message(guid,welcome_message) values (67,'生活的理想，就是为了理想的生活');
insert into hsa_welcome_message(guid,welcome_message) values (68,'聪明的人有长的耳朵和短的舌头');
insert into hsa_welcome_message(guid,welcome_message) values (69,'有教养的头脑的第一个标志就是善于提问');
insert into hsa_welcome_message(guid,welcome_message) values (70,'学习是劳动，是充满思想的劳动');
insert into hsa_welcome_message(guid,welcome_message) values (71,'不放过任何细节');
insert into hsa_welcome_message(guid,welcome_message) values (72,'用心带脑，用脑带人');
insert into hsa_welcome_message(guid,welcome_message) values (73,'人之为学，不可自小，又不可自大');
insert into hsa_welcome_message(guid,welcome_message) values (74,'人生乐在相知心');
insert into hsa_welcome_message(guid,welcome_message) values (75,'朋友丰富人生');
insert into hsa_welcome_message(guid,welcome_message) values (76,'心志要坚，意趣要乐');
insert into hsa_welcome_message(guid,welcome_message) values (77,'不要等待机会，而要创造机会');
insert into hsa_welcome_message(guid,welcome_message) values (78,'真诚才是人生最高的美德');
insert into hsa_welcome_message(guid,welcome_message) values (79,'时间是真理的挚友');
insert into hsa_welcome_message(guid,welcome_message) values (80,'知者不惑，仁者不忧，勇者不惧');
insert into hsa_welcome_message(guid,welcome_message) values (81,'君子务本，本立而道生');
insert into hsa_welcome_message(guid,welcome_message) values (82,'生命，只要你充分利用，它便是长久的');
insert into hsa_welcome_message(guid,welcome_message) values (83,'生命不等于是呼吸，生命是活动');
insert into hsa_welcome_message(guid,welcome_message) values (84,'一个伟大的灵魂，会强化思想和生命');
insert into hsa_welcome_message(guid,welcome_message) values (85,'附和真理，生命便会得到永生');
insert into hsa_welcome_message(guid,welcome_message) values (86,'希望与理想是最令人珍惜的部分');
insert into hsa_welcome_message(guid,welcome_message) values (87,'人类的全部尊严，就在于思想');
insert into hsa_welcome_message(guid,welcome_message) values (88,'自我控制是最强者的本能');
insert into hsa_welcome_message(guid,welcome_message) values (89,'生活中唯一乐趣就是忘却');
insert into hsa_welcome_message(guid,welcome_message) values (90,'信是立身之本，恕乃接物之要');
insert into hsa_welcome_message(guid,welcome_message) values (91,'哪里有意志存在，哪里就会有出路');
insert into hsa_welcome_message(guid,welcome_message) values (92,'时间就是速度，时间就是力量');
insert into hsa_welcome_message(guid,welcome_message) values (93,'成功，是内心的造就');
insert into hsa_welcome_message(guid,welcome_message) values (94,'思想感情的一致产生友谊');
insert into hsa_welcome_message(guid,welcome_message) values (95,'脚下有大地，心中有梦想');
insert into hsa_welcome_message(guid,welcome_message) values (96,'你终究会成为你正在成为的人');
insert into hsa_welcome_message(guid,welcome_message) values (97,'趁我们头脑发热，我们要不顾一切');
insert into hsa_welcome_message(guid,welcome_message) values (98,'一个人的理想越崇高，生活越纯洁');
insert into hsa_welcome_message(guid,welcome_message) values (99,'我，是一切的根源');
insert into hsa_welcome_message(guid,welcome_message) values (100,'只有结果，才能犒劳自己');
insert into hsa_welcome_message(guid,welcome_message) values (101,'第一次，就把事情做对');
insert into hsa_welcome_message(guid,welcome_message) values (102,'着眼于外，发力于内');
insert into hsa_welcome_message(guid,welcome_message) values (103,'别把今天的彩虹，放在明天的天空');
insert into hsa_welcome_message(guid,welcome_message) values (104,'你的大好机会可能就在当下');
insert into hsa_welcome_message(guid,welcome_message) values (105,'实践长才干');
insert into hsa_welcome_message(guid,welcome_message) values (106,'如果不开始行动，我们就无法知道结果');

-- 权益

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_equities_rule` MODIFY COLUMN `type` tinyint(4) NOT NULL COMMENT '类型 0：商品折扣 1：满减 2：直减 3：会员价 4：赠送成长值 5：翻倍成长值  8:商品会员价' AFTER `guid`;

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_equities_rule` ADD COLUMN `product_show_set` tinyint(1) NULL DEFAULT NULL COMMENT '商品会员价是否已经配置好了  0：未设置  1：已设置' AFTER `equities_time_required`;

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_offline_activity_member_relation`
ADD INDEX `idnex_member_guid`(`member_guid`) USING BTREE;

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_member_growth_value_detail`
ADD INDEX `index_member_guid`(`member_info_guid`) USING BTREE