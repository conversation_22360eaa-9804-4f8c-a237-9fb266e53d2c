  USE `hsm_member_marketing_platform_db`;


DROP TABLE IF EXISTS `hsa_apply_dictionaries`;

CREATE TABLE `hsa_apply_dictionaries` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `module` VARCHAR(128) NOT NULL COMMENT '所属模块',
  `type` VARCHAR(128) NOT NULL COMMENT '所属类型',
  `type_name` VARCHAR(128) NOT NULL COMMENT '类型名称',
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=INNODB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4;

INSERT  INTO `hsa_apply_dictionaries`(`id`,`module`,`type`,`type_name`,`gmt_create`,`gmt_modified`) VALUES
(1,'apply_terminal','mini_program','微信小程序','2022-01-24 19:56:13','2022-01-24 19:56:13'),
(2,'apply_terminal','pos','一体机','2022-01-24 19:56:29','2022-01-24 19:56:29'),
(3,'apply_terminal','attendance_booking','考勤预定','2022-01-24 19:56:44','2022-01-24 19:56:44'),
(4,'apply_business','0','食堂预订','2022-01-24 19:57:05','2022-01-24 19:57:05'),
(5,'apply_business','3','快速收款','2022-01-24 19:57:28','2022-01-24 19:57:28'),
(6,'apply_channel','pos','pos','2022-01-24 19:57:36','2022-01-24 19:57:36');


-- 新增会员领取权益历史表
CREATE TABLE `hsa_member_equities_receive_record`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权益guid',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '运营主体guid',
  `member_grade_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '会员等级guid',
  `member_info_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '会员guid',
  `grade_equities_guid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权益guid',
  `double_value_count` int(11) NULL DEFAULT NULL COMMENT '翻倍成长值使用次数',
  `total_double_upper_value` int(11) NULL DEFAULT NULL COMMENT '累计翻倍成长值',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  `give_growth_value_number` int(11) NULL DEFAULT NULL COMMENT '成长值赠送数量',
  PRIMARY KEY (`id`, `member_grade_guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16226 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- 新增权益id字段
ALTER TABLE hsa_member_grade_rights_record ADD equities_id bigint(20) DEFAULT NULL COMMENT '权益id';

DROP TABLE IF EXISTS `hsa_equities_info`;
CREATE TABLE `hsa_equities_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `guid` varchar(50) DEFAULT NULL COMMENT '权益guid',
  `enterprise_guid` varchar(20) NOT NULL COMMENT '企业guid',
  `equities_name` varchar(30) DEFAULT NULL COMMENT '权益名称',
  `equities_img` varchar(255) DEFAULT NULL COMMENT '权益图片',
  `equities_explain` text COMMENT '权益描述',
  `equities_type` tinyint(4) DEFAULT NULL COMMENT '权益适用类型(1:全部运营主体  2:部分运营主体)',
  `oper_subject_guid_list` varchar(1000) DEFAULT NULL COMMENT '权益适用的运营主体，存储JSON字符串',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启 0：正常  1：禁用 2：草稿',
  `type` tinyint(4) NOT NULL COMMENT '权益类型 0:折扣权益 1:成长值权益 2:线下权益 3:积分权益 4:新品优先权 5:消费项权益',
  `is_delete` tinyint(1) NOT NULL COMMENT '是否删除,0未删除,1已删除',
  `equities_number` varchar(6) NOT NULL COMMENT '权益编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=187 DEFAULT CHARSET=utf8mb4;


DROP TABLE IF EXISTS `hsa_equities_rule`;
CREATE TABLE `hsa_equities_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `type` tinyint(4) NOT NULL COMMENT '类型 0：商品折扣 1：满减 2：直减 3：会员价 4：赠送成长值 5：翻倍成长值',
  `goods_discount_set` tinyint(1) DEFAULT NULL COMMENT '商品折扣 0：未设置 1：已设置',
  `share_and_mutex` varchar(128) DEFAULT NULL COMMENT '0:同享 1：互斥 多个用逗号隔开',
  `equities_time_set` tinyint(1) DEFAULT NULL COMMENT '权益时段 0：未设置 1：已设置',
  `equities_time_limited_type` varchar(128) DEFAULT NULL COMMENT '权益时段限制类型 0：不限制 1：限制时段 多个用逗号隔开',
  `limited_time_type` varchar(128) DEFAULT NULL COMMENT '限制时段类型 0：日 1：周 2：月 3：年',
  `apply_store_set` tinyint(1) DEFAULT NULL COMMENT '适用门店设置 0：未设置 1：已设置',
  `apply_store_type` varchar(128) DEFAULT NULL COMMENT '适用门店设置类型 0：全部门店 1：选择部分门店 多个用逗号隔开',
  `apply_business_set` tinyint(1) DEFAULT NULL COMMENT '适用业务 0：未设置 1：已设置',
  `apply_business_type` varchar(128) DEFAULT NULL COMMENT '适用业务类型 0：全部业务 1：部分业务 多个用逗号隔开',
  `business_type` varchar(128) DEFAULT NULL COMMENT '业务类型 0：食堂预定 3：快速收款 多个用逗号隔开',
  `apply_terminal_set` tinyint(1) DEFAULT NULL COMMENT '适用终端 0：未设置 1：已设置',
  `apply_terminal_type` varchar(128) DEFAULT NULL COMMENT '适用终端类型 0：全部终端 1：部分终端 多个用逗号隔开',
  `terminal_type` varchar(128) DEFAULT NULL COMMENT '部分终端类型 53：小程序 2：一体机 多个用逗号隔开',
  `apply_commodity_set` tinyint(1) DEFAULT NULL COMMENT '适用商品 0：未设置 1：已设置',
  `apply_commodity_type` varchar(128) DEFAULT NULL COMMENT '0：适用商品 1：不适用商品 多个用逗号隔开',
  `enough_reduce_set` tinyint(1) DEFAULT NULL COMMENT '满减 0：未设置 1：已设置',
  `multiple_enough_reduce` tinyint(1) DEFAULT NULL COMMENT '是否可设置多个满减 0：不能 1：能',
  `amount_satisfy_scope_set` tinyint(1) DEFAULT NULL COMMENT '金额满足计算范围 0：未设置 1：已设置',
  `amount_satisfy_scope_type` varchar(128) DEFAULT NULL COMMENT '金额满足计算范围类型 0：全部计算 1：部分计算设置 多个用逗号隔开',
  `part_valuation_type` varchar(128) DEFAULT NULL COMMENT '部分计价类型 0：商品价格 1：桌台费 2：配送费 多个用逗号隔开',
  `direct_reduce_set` tinyint(1) DEFAULT NULL COMMENT '直减 0：未设置 1：已设置',
  `member_discount_set` tinyint(1) DEFAULT NULL COMMENT '会员折扣 0：未设置 1：已设置',
  `single_discounts_limited_set` tinyint(1) DEFAULT NULL COMMENT '单次优惠限制 0：未设置 1：已设置',
  `single_discounts_limited_type` varchar(128) DEFAULT NULL COMMENT '单次优惠限制类型 0：不限制 1：限制  多个用逗号隔开',
  `total_most_limited_type_set` tinyint(1) DEFAULT NULL COMMENT '累计最高优惠限制 0：未设置 1：已设置',
  `total_most_limited_type` varchar(128) DEFAULT NULL COMMENT '0：不限 1：优惠金额限制  多个用逗号隔开',
  `single_give_number_set` tinyint(1) DEFAULT NULL COMMENT '单次赠送成长值数量 0：未设置 1：已设置',
  `give_way_set` tinyint(1) DEFAULT NULL COMMENT '赠送成长值方式 0：未设置 1：已设置',
  `period_type` varchar(128) DEFAULT NULL COMMENT '赠送成长值周期类型 0：日 1：周 2：月 3：年 多个用逗号隔开',
  `double_multiple_set` tinyint(1) DEFAULT NULL COMMENT '翻倍倍数 0：未设置 1：已设置',
  `single_double_limited_set` tinyint(1) DEFAULT NULL COMMENT '单次翻倍获取成长值上限 0：未设置 1：已设置',
  `single_double_limited_type` varchar(128) DEFAULT NULL COMMENT '单次翻倍获取成长值上限类型 0：不限 1：限制 多个用逗号隔开',
  `double_count_set` tinyint(1) DEFAULT NULL COMMENT '限制次数 0：未设置 1：已设置',
  `double_count_type` varchar(128) DEFAULT NULL COMMENT '翻倍次数限制类型 0：不限次数 1：设置可翻倍次数 多个用逗号隔开',
  `total_double_limited_set` tinyint(1) DEFAULT NULL COMMENT '累计翻倍获取成长值上限 0：未设置 1：已设置',
  `total_double_limited_type` varchar(128) DEFAULT NULL COMMENT '累计翻倍获取成长值上限类型 0：不限制 1：设置上限值 多个用逗号隔开',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `equities_info_guid` varchar(50) NOT NULL COMMENT '权益信息guid',
  `single_double_limited_required` tinyint(1) DEFAULT NULL COMMENT '单次翻倍获取成长值上线必填 0：非必填 1：必填',
  `double_count_required` tinyint(1) DEFAULT NULL COMMENT '翻倍次数必填 0：非必填 1：必填',
  `total_double_limited_required` tinyint(1) DEFAULT NULL COMMENT '累计翻倍获取成长值上限必填  0：非必填 1：必填',
  `period_discount_limited_set` tinyint(1) DEFAULT NULL COMMENT '周期优惠限制 0：设置 1：未设置',
  `period_discount_required` tinyint(1) DEFAULT NULL COMMENT '周期优惠限制必填 0：非必填 1：必填',
  `period_discount_type` varchar(128) DEFAULT NULL COMMENT '周期优惠限制类型 -1：不限制 0：天 1：周 2：月 3：天',
  `discounts_superposition_set` tinyint(1) DEFAULT NULL COMMENT '优惠叠加设置 0：未设置 1：已设置',
  `discounts_superposition_required` tinyint(1) DEFAULT NULL COMMENT '优惠叠加必填 0：非必填 1：必填',
  `equities_effective_date_set` tinyint(1) DEFAULT NULL COMMENT '权益生效时段设置 0：未设置 1：已设置',
  `equities_effective_date_required` tinyint(1) DEFAULT NULL COMMENT '权益生效时段必填 0：非必填 1：必填',
  `equities_effective_date_limited` tinyint(1) DEFAULT NULL COMMENT '权益生效时段限制 0：不限制 1：限制',
  `equities_effective_date_type` varchar(128) DEFAULT NULL COMMENT '权益生效时段限制类型 -1：自定义 0：天 1：周 2：月 3：天',
  `single_discounts_limited_required` tinyint(1) DEFAULT NULL COMMENT '单次优惠限制必填 0：非必填 1：必填',
  `total_most_limited_type_required` tinyint(1) DEFAULT NULL COMMENT '累计最高优惠限制必填 0：非必填 1：必填',
  `apply_channel_set` tinyint(1) DEFAULT NULL COMMENT '适用渠道 0：设置 1：未设置',
  `apply_channel_required` tinyint(1) DEFAULT NULL COMMENT '适用渠道必填 0：非必填 1：必填',
  `apply_channel_limited` tinyint(1) DEFAULT NULL COMMENT '适用渠道限制 0：全部 1：部分',
  `apply_channel_type` varchar(128) DEFAULT NULL COMMENT '适用渠道类型',
  `discounts_superposition_type` varchar(128) DEFAULT NULL COMMENT '优惠叠加类型 0：不可叠加 1:可叠加',
  `apply_commodity_required` tinyint(1) DEFAULT NULL COMMENT '适用商品必填 0：非必填 1：必填',
  `apply_terminal_required` tinyint(1) DEFAULT NULL COMMENT '适用终端必填 0：非必填 1：必填',
  `apply_business_required` tinyint(1) DEFAULT NULL COMMENT '适用业务必填 0：非必填 1：必填',
  `single_discounts_required` tinyint(1) DEFAULT NULL COMMENT '单次优惠限制必填 0：非必填 1：必填',
  `equities_time_required` tinyint(1) DEFAULT NULL COMMENT '权益时段必填 0：非必填 1：必填',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=161 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `hsa_grade_equities` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `member_grade_info_guid` varchar(50) NOT NULL COMMENT '等级guid',
  `equities_type` tinyint(4) NOT NULL COMMENT '权益类型 0:折扣权益 1:成长值权益 2:线下权益 3:积分权益 4:新品优先权 5:消费项权益',
  `equities_rule_type` tinyint(4) NOT NULL COMMENT '权益规则类型 0：商品折扣 1：满减 2：直减 3：会员价 4：赠送成长值 5：翻倍成长值',
  `equities_guid` varchar(50) NOT NULL COMMENT '权益guid',
  `grade_value_double_number` decimal(10,1) DEFAULT NULL COMMENT '成长值翻倍倍数',
  `double_count_limited` tinyint(4) DEFAULT NULL COMMENT '成长值翻倍次数限制 0:不限制 1:限制',
  `double_count_upper_limit` int(11) DEFAULT NULL COMMENT '翻倍次数上限',
  `single_double_limited` tinyint(4) DEFAULT NULL COMMENT '成长值单次翻倍限制 0:不限制 1:限制',
  `single_double_upper_limit` int(11) DEFAULT NULL COMMENT '单次翻倍成长值上限',
  `total_double_count_limited` tinyint(4) DEFAULT NULL COMMENT '累计翻倍限制 0:不限制 1:限制',
  `total_double_count_upper_limit` int(11) DEFAULT NULL COMMENT '累计翻倍次数上限',
  `total_give_number` int(11) DEFAULT NULL COMMENT '累计赠送成长值数量',
  `set_period` tinyint(4) DEFAULT NULL COMMENT '设置周期 -1:一次性  0:天  1:周  2:月  3:年',
  `period_give_number` int(11) DEFAULT NULL COMMENT '周期赠送数量',
  `discount_dynamics` decimal(10,1) DEFAULT NULL COMMENT '折扣力度',
  `single_discounts_limited` tinyint(4) DEFAULT NULL COMMENT '单次优惠限制 0：不限制 1：限制',
  `single_discounts_limited_amount` decimal(12,2) DEFAULT '0.00' COMMENT '单次优惠限制金额',
  `period_discount_limited` tinyint(4) DEFAULT NULL COMMENT '周期优惠限制 0：不限制 1：限制',
  `period_discount_type` varchar(512) DEFAULT NULL COMMENT '周期优惠限制类型 -1 不限制 0：天 1：周 2：月 3：天',
  `total_discount_limited` tinyint(4) DEFAULT NULL COMMENT '累计优惠限制 0：不限制 1：限制',
  `total_discount_limited_amount` decimal(12,2) DEFAULT '0.00' COMMENT '累计优惠限制金额',
  `discounts_superposition` tinyint(4) DEFAULT NULL COMMENT '优惠叠加限制 0：不可叠加 1：可叠加',
  `equities_effective_date_limited` tinyint(4) DEFAULT NULL COMMENT '权益生效时段限制 0：不限制 1：限制',
  `equities_time_limited_type` tinyint(4) DEFAULT NULL COMMENT '限制时段类型 -1:自定义 0：日 1：周 2：月 3：年',
  `equities_time_limited_json` varchar(1000) DEFAULT NULL COMMENT '限制时段限制类型json',
  `apply_goods_type` tinyint(4) DEFAULT NULL COMMENT '-1：全部商品适用 0：适用商品 1：不适用商品',
  `apply_terminal` tinyint(4) DEFAULT NULL COMMENT '适用终端 0:全部终端 1：部分终端',
  `apply_terminal_json` varchar(128) DEFAULT NULL COMMENT '适用终端json',
  `apply_business` tinyint(4) DEFAULT NULL COMMENT '适用业务 0:全部业务 1：部分业务',
  `apply_business_json` varchar(128) DEFAULT NULL COMMENT '适用业务json',
  `apply_channel` tinyint(4) DEFAULT NULL COMMENT '适用渠道 0:全部渠道 1：部分渠道',
  `apply_channel_json` varchar(128) DEFAULT NULL COMMENT '适用渠道json',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint(4) DEFAULT NULL COMMENT '是否删除,0未删除,1已删除 2：删除但未生效',
  `effective_equities_guid` varchar(50) DEFAULT NULL COMMENT '有效权益guid',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `equities_name` varchar(128) NOT NULL COMMENT '权益名称',
  `equities_number` varchar(6) DEFAULT NULL COMMENT '权益编号',
  `effective` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否有效 0:无效 1:有效',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=618 DEFAULT CHARSET=utf8mb4;

-- 修改成长值明细详情表中  翻倍成长值字段类型
alter table hsa_member_growth_value_detail  modify column multiple_growth decimal(10,1);


-- 运营主体权限类型表
  CREATE TABLE `hsa_oper_subject_permission_type`  (
       `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
       `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'guid',
       `enterprise_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '企业guid',
       `position_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位id和角色id',
       `source_type` tinyint(4) NULL DEFAULT NULL COMMENT '1：会员管理   2：营销中心',
       `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
       `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
       `is_role` tinyint(4) NULL DEFAULT NULL COMMENT '权限类型（0：岗位  1：角色）',
       `is_all` tinyint(4) NULL DEFAULT NULL COMMENT '设置的类型（1：全部  0：部分）默认为全部',
       PRIMARY KEY (`id`) USING BTREE
  ) ENGINE = InnoDB AUTO_INCREMENT = 42936 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '运营主体权限类型表（用于判断拥有全部权限还是部分权限）' ROW_FORMAT = Dynamic;



