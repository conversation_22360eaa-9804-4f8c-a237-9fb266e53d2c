# v1.13.0配置及部署说明


## 访问


## 接口


{adr}  =  线上地址  re地址  test地址
线上地址： https://member-center.holderzone.com
re地址：    https://member-center-re.holderzone.cn
test地址： https://member-center-test.holderzone.cn

1. 初始化补贴活动迁移的库表数据接口，该接口用于线上处理数据，只执行一次
{adr}/subsidy_activity/init_subsidy_data
   
1. 初始化营销中心权限代码的库表数据，该接口用于线上处理数据，只执行一次
{adr}/member_permission/init_subject_permission_data



## 服务



## 配置

门店云平台配置
> 随行红包门店跳转会员地址
> 测试环境 https://member-center-test.holderzone.cn/memberCare/accompanyPocket/list
> 线上环境 https://member-center.holderzone.com/memberCare/accompanyPocket/list


新增旧会员系统，门店后台随行红包权限相关配置


holder营销中的权限url地址需要更换成，迁移后的营销中心url
holder team库  data_permission_type_outside表中 query_url和save_url两个自动需要更新url值为新营销中心地址


profiles : dev | test | re | prod

2. member-marketing-{profiles}.yaml

```yaml
# member-marketing-{profiles}.yaml 增加：访问各业务系统的不同环境的访问地址
feign:
  fixed: holder,saas-store,member-base  #固定地址的服务，其他服务的feign.url会按企业动态查询
  zhuancan: https://zhuancan-sit.holderzone.cn  #赚餐地址服务
  
# datasource-marketing.yaml  增加优化配置
hikari:
    minimum-idle: 5
    maximum-pool-size: 15
    auto-commit: true
    idle-timeout: 30000
    pool-name: DatebookHikariCP
    max-lifetime: 500000  //不小于30s 否则会回到默认的1800秒
    connection-timeout: 30000
    connection-test-query: SELECT 1
       
# member-marketing-{profiles}.yaml 增加：补贴活动队列配置信息 
spring:
  cloud:
    stream:
      # 新增rabbitMq 补贴信道通道
      rabbit:
        bindings:
          inputSubsidySendRights:
            consumer:
              acknowledge-mode: manual
          inputSubsidySendBackRights:
            consumer:
              acknowledge-mode: manual
      # 新增补贴活动生产和消费队列配置
      bindings:
        inputSubsidySendRights:
          group: subsidySendRights
          destination: subsidySendRightsExchange
          consumer:
            max-attempts: 1
        inputSubsidySendBackRights:
          group: subsidySendBackRights
          destination: subsidySendBackRightsExchange
          consumer:
            max-attempts: 1

        outputSubsidySendRights:
          destination: subsidySendRightsExchange
          contentType: application/json
        outputSubsidySendBackRights:
          destination: subsidySendBackRightsExchange
          contentType: application/json
          
          
# Nacos线上 --member-base-prod.yaml-- 配置文件修改  
goalgo:
  store: https://zkz.holderzone.com 
  crm: https://zkz.holderzone.com


# Nacos线上 --member-queue-prod.yaml-- 配置文件修改  
goalgo:
  store: https://zkz.holderzone.com
  
  
# Nacos线上 --member-marketing-prod.yaml-- 配置文件修改  
feign:
  store: https://zkz.holderzone.com
  crm: https://zkz.holderzone.com
goalgo:
  store: https://zkz.holderzone.com
  

```