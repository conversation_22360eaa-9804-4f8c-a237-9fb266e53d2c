use gateway;
INSERT INTO `gateway`.`hp_function_group`(`create_time`, `name`, `sequence`, `update_time`, `gmt_create`, `gmt_modified`)
VALUES (now(), '随行红包管理', 108, now(), now(), now());


INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), '随行红包', 1, 60, NOW(), 26,  ( SELECT id FROM hp_system WHERE NAME = '营销中心' AND identification = 'marketing_count' ORDER BY gmt_modified DESC LIMIT 1 ), (select id from hp_function_group where name = '随行红包管理' order by gmt_modified desc limit 1),NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '活动列表', NOW(), (select id from hp_function where name = '随行红包' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '新建活动', NOW(), (select id from hp_function where name = '随行红包' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '发布活动', NOW(), (select id from hp_function where name = '随行红包' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '暂停活动', NOW(), (select id from hp_function where name = '随行红包' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '启用活动', NOW(), (select id from hp_function where name = '随行红包' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '编辑活动', NOW(), (select id from hp_function where name = '随行红包' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '查看活动详情', NOW(), (select id from hp_function where name = '随行红包' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '试玩游戏', NOW(), (select id from hp_function where name = '随行红包' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '查看活动统计', NOW(), (select id from hp_function where name = '随行红包' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '删除活动', NOW(), (select id from hp_function where name = '随行红包' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '导出统计数据', NOW(), (select id from hp_function where name = '随行红包' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '查看统计数据详情', NOW(), (select id from hp_function where name = '随行红包' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());




/**
  修改 re环境 holder team库  data_permission_type_outside表中
  会员获取运营主体和修改运营主体地址信息
 */
use team;
UPDATE data_permission_type_outside
SET query_url = 'https://member-center-re.holderzone.cn/marketing/member_permission/get_marketing_oper_subject',
    save_url = 'https://member-center-re.holderzone.cn/marketing/member_permission/update_marketing_oper_subject'
WHERE
        query_url = 'https://member-re.holderzone.cn/base/member_permission/get_marketing_oper_subject'
  and save_url = 'https://member-re.holderzone.cn/base/member_permission/update_marketing_oper_subject'


/**
  修改 线上环境 holder team库  data_permission_type_outside表中
  会员获取运营主体和修改运营主体地址信息
 */
UPDATE data_permission_type_outside
SET query_url = 'https://member-center.holderzone.com/marketing/member_permission/get_marketing_oper_subject',
    save_url = 'https://member-center.holderzone.com/marketing/member_permission/update_marketing_oper_subject'
WHERE
        query_url = 'https://member-center.holderzone.com/base/member_permission/get_marketing_oper_subject'
  and save_url = 'https://member-center.holderzone.com/base/member_permission/update_marketing_oper_subject'
