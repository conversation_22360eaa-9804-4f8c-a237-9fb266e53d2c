use hsm_member_marketing_center_platform_db;

#feign url 配置
CREATE TABLE `system_feign`
(
    `id`               bigint(20)   NOT NULL AUTO_INCREMENT,
    `guid`             varchar(50)  NOT NULL COMMENT 'guid',
    `feign_name`       varchar(50)  NOT NULL COMMENT 'feign_name',
    `feign_url`        varchar(100) NOT NULL COMMENT 'feign_url',
    `feign_enterprise` varchar(100) NOT NULL COMMENT '所属企业',
    `gmt_create`       datetime DEFAULT CURRENT_TIMESTAMP,
    `gmt_modified`     datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

#权限
CREATE TABLE `hsa_oper_subject_permission`
(
    `id`                  int(11)                                                      NOT NULL AUTO_INCREMENT COMMENT 'id',
    `guid`                varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'guid',
    `enterprise_guid`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '企业guid',
    `position_guid`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位id和角色id',
    `oper_subject_guid`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '运营主体guid',
    `source_type`         tinyint(4)                                                   NULL     DEFAULT NULL COMMENT '1：会员管理   2：营销中心',
    `multi_member_name`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '运营主体名称',
    `multi_member_status` tinyint(4)                                                   NULL     DEFAULT NULL COMMENT '运营主体状态（0:启用 １未启用）',
    `is_checked`          tinyint(4)                                                   NULL     DEFAULT NULL COMMENT '是否选择（1：选中，0未选中）',
    `gmt_modified`        datetime(0)                                                  NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
    `gmt_create`          datetime(0)                                                  NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    `is_role`             tinyint(4)                                                   NULL     DEFAULT NULL COMMENT '权限类型（0：岗位  1：角色）',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 48101
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '运营主体权限表'
  ROW_FORMAT = DYNAMIC;


CREATE TABLE `hsa_oper_subject_permission_type`
(
    `id`              int(11)                                                      NOT NULL AUTO_INCREMENT COMMENT 'id',
    `guid`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'guid',
    `enterprise_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '企业guid',
    `position_guid`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位id和角色id',
    `source_type`     tinyint(4)                                                   NULL     DEFAULT NULL COMMENT '1：会员管理   2：营销中心',
    `gmt_modified`    datetime(0)                                                  NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
    `gmt_create`      datetime(0)                                                  NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    `is_role`         tinyint(4)                                                   NULL     DEFAULT NULL COMMENT '权限类型（0：岗位  1：角色）',
    `is_all`          tinyint(4)                                                   NULL     DEFAULT NULL COMMENT '设置的类型（1：全部  0：部分）默认为全部',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 42949
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '运营主体权限类型表（用于判断拥有全部权限还是部分权限）'
  ROW_FORMAT = DYNAMIC;


/* 补贴活动数据表*/
CREATE TABLE `hsa_subsidy_activity`
(
    `id`                             bigint(20)                                                     NOT NULL AUTO_INCREMENT,
    `guid`                           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '营销活动GUID',
    `enterprise_guid`                varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL     DEFAULT NULL COMMENT '企业GUID',
    `oper_subject_guid`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL     DEFAULT NULL COMMENT '运营主体guid',
    `activity_num`                   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL     DEFAULT NULL COMMENT '活动编号',
    `activity_name`                  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL     DEFAULT NULL,
    `activity_state`                 tinyint(4)                                                     NULL     DEFAULT NULL COMMENT '0 禁用 1 启用  2  已结束  3  草稿 4 未开始',
    `subsidy_money`                  decimal(12, 2)                                                 NULL     DEFAULT NULL COMMENT '补贴金额',
    `card_guid_json`                 varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '会员卡json',
    `subsidy_frequency_type`         tinyint(4)                                                     NULL     DEFAULT NULL COMMENT '0 单次 1 每天 2 每周 3 每月 4 每年',
    `subsidy_json`                   varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL,
    `subsidy_filter_type`            tinyint(4)                                                     NULL     DEFAULT NULL COMMENT '0 按手动标签筛选 1 指定用户 2 主体下所有会员',
    `member_info_guid_json`          mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci    NULL COMMENT '指定用户guid',
    `subsidy_appoint_time`           datetime(0)                                                    NULL     DEFAULT NULL COMMENT '指定发放时间',
    `label_guid_json`                varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '标签guid（按手动标签筛选存在）',
    `activity_start_time`            datetime(0)                                                    NULL     DEFAULT NULL,
    `activity_end_time`              datetime(0)                                                    NULL     DEFAULT NULL,
    `operator_tel_name`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '创建人GUID',
    `gmt_modified`                   datetime(0)                                                    NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
    `gmt_create`                     datetime(0)                                                    NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    `subsidy_member_num`             int(11)                                                        NULL     DEFAULT NULL COMMENT '补贴人数',
    `subsidy_money_period_type`      tinyint(4)                                                     NULL     DEFAULT NULL COMMENT '补贴金有效期类型 0 永久有效 1 固定清零 2 领取后过期',
    `subsidy_recycle_frequency_type` tinyint(4)                                                     NULL     DEFAULT NULL COMMENT '回收频率 1 每天 2 每周 3 每月 4 每年 5 指定日期',
    `subsidy_recycle_appoint_time`   datetime(0)                                                    NULL     DEFAULT NULL COMMENT '指定日期',
    `subsidy_recycle`                int(11)                                                        NULL     DEFAULT NULL COMMENT '领取后过期（数字标识）',
    `subsidy_recycle_json`           varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '回收机制  具体日期时间',
    `is_retreat`                     tinyint(4)                                                     NULL     DEFAULT NULL COMMENT '补贴金是否可退 0 不可退  1 可退',
    `is_del`                         tinyint(4)                                                     NULL     DEFAULT NULL,
    `marking_label_guid_json`        varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '打标标签',
    `validity_unit`                  tinyint(4)                                                     NULL     DEFAULT NULL COMMENT '1日、2周、3月、4年',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_guid` (`guid`) USING BTREE,
    INDEX `hsa_subsidy_activity_gmt_modified_idx` (`gmt_modified`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 4187
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '会员补贴活动信息表'
  ROW_FORMAT = DYNAMIC;


/*补贴活动记录详情表*/

CREATE TABLE `hsa_subsidy_activity_detail_record`
(
    `id`                           bigint(20) UNSIGNED ZEROFILL                                 NOT NULL AUTO_INCREMENT,
    `guid`                         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '营销活动明细表GUID',
    `oper_subject_guid`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '运营主体guid',
    `activity_num`                 varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '活动编号',
    `activity_name`                varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '活动名称',
    `subsidy_money`                decimal(12, 2)                                               NULL     DEFAULT NULL COMMENT '补贴金额',
    `subsidy_activity_guid`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '补贴活动guid',
    `subsidy_activity_record_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '补贴记录guid',
    `subsidy_time`                 datetime(0)                                                  NULL     DEFAULT NULL COMMENT '补贴机制时间(整点触发时间)',
    `subsidy_arrive_time`          datetime(0)                                                  NULL     DEFAULT NULL COMMENT '补贴到账时间(以此时间结算有效期)',
    `validity_expire_time`         datetime(0)                                                  NULL     DEFAULT NULL COMMENT '补贴金到期时间',
    `is_retreat`                   tinyint(4)                                                   NULL     DEFAULT NULL COMMENT '补贴金是否可退 0 不可退  1 可退',
    `card_guid`                    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '补贴会员卡guid',
    `member_info_card_guid`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL,
    `member_info_guid`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '补贴会员guid',
    `subsidy_result`               tinyint(4)                                                   NULL     DEFAULT NULL COMMENT '补贴状态 1：成功  0：失败',
    `subsidy_error`                varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '失败原因',
    `gmt_modified`                 datetime(0)                                                  NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
    `gmt_create`                   datetime(0)                                                  NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    `operator_tel_name`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '创建人GUID',
    `is_withdraw`                  tinyint(4)                                                   NULL     DEFAULT NULL COMMENT '是否已回收补贴金额',
    `is_destroy`                   tinyint(4) UNSIGNED ZEROFILL                                 NULL     DEFAULT NULL COMMENT '补贴活动是否删除（1：删除  0：未删除）',
    `subsidy_residue_money`        decimal(12, 2)                                               NOT NULL DEFAULT 0.00 COMMENT '此金额记录剩余补贴金额',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_guid` (`guid`) USING BTREE,
    INDEX `index_oper_subject_guid` (`oper_subject_guid`) USING BTREE,
    INDEX `index_subsidy_activity_record_guid` (`subsidy_activity_record_guid`) USING BTREE,
    INDEX `index_member_info_guid` (`member_info_guid`) USING BTREE,
    INDEX `index_activity_num` (`activity_num`) USING BTREE,
    INDEX `index_subsidy_time` (`subsidy_time`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 767634
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '营销活动明细表'
  ROW_FORMAT = DYNAMIC;


/*补贴活动记录表*/
CREATE TABLE `hsa_subsidy_activity_record`
(
    `id`                             bigint(20)                                                    NOT NULL AUTO_INCREMENT,
    `guid`                           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '营销活动GUID',
    `enterprise_guid`                varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '企业GUID',
    `oper_subject_guid`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '运营主体guid',
    `card_guid`                      varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '会员卡json',
    `activity_num`                   varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '活动编号',
    `activity_name`                  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '活动名称',
    `subsidy_money`                  decimal(12, 2)                                                NULL     DEFAULT NULL COMMENT '补贴金额',
    `subsidy_activity_guid`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '补贴活动guid',
    `subsidy_time`                   datetime(0)                                                   NULL     DEFAULT NULL COMMENT '补贴活动时间',
    `validity_time`                  datetime(0)                                                   NULL     DEFAULT NULL COMMENT '补贴金有效期',
    `is_retreat`                     tinyint(4)                                                    NULL     DEFAULT NULL COMMENT '补贴金是否可退 0 不可退  1 可退',
    `subsidy_num`                    int(11)                                                       NOT NULL DEFAULT 0 COMMENT '补贴数量',
    `subsidy_error_num`              int(11)                                                       NOT NULL DEFAULT 0 COMMENT '补贴发放失败人数',
    `gmt_modified`                   datetime(0)                                                   NULL     DEFAULT NULL,
    `gmt_create`                     datetime(0)                                                   NULL     DEFAULT NULL,
    `subsidy_money_period_type`      tinyint(4)                                                    NULL     DEFAULT NULL COMMENT '补贴金有效期类型 0 永久有效 1 固定清零 2 领取后过期',
    `subsidy_recycle_frequency_type` tinyint(4)                                                    NULL     DEFAULT NULL COMMENT '回收频率 1 每天 2 每周 3 每月 4 每年 5 指定日期',
    `subsidy_recycle_appoint_time`   datetime(0)                                                   NULL     DEFAULT NULL COMMENT '指定日期',
    `subsidy_recycle`                int(11)                                                       NULL     DEFAULT NULL COMMENT '领取后过期（数字标识）',
    `subsidy_recycle_json`           varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '回收机制  具体日期时间',
    `validity_unit`                  tinyint(4)                                                    NULL     DEFAULT NULL COMMENT '1日、2周、3月、4年',
    `operator_tel_name`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '创建人GUID',
    `subsidy_send_state`             tinyint(4)                                                    NULL     DEFAULT NULL COMMENT '补贴发放状态 0 发放中 1 发放结束',
    `subsidy_frequency_type`         tinyint(4) UNSIGNED                                           NULL     DEFAULT NULL COMMENT '0 单次 1 每天 2 每周 3 每月 4 每年',
    `is_destroy`                     tinyint(4) UNSIGNED ZEROFILL                                  NULL     DEFAULT NULL COMMENT '补贴活动是否删除（1：删除  0：未删除）',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_guid` (`guid`) USING BTREE,
    INDEX `index_subsidy_time` (`subsidy_time`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 15226
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '补贴记录表'
  ROW_FORMAT = DYNAMIC;


ALTER TABLE hsa_follow_red_packet_store
    ADD oper_subject_guid VARCHAR(50);
ALTER TABLE hsa_follow_red_packet_table
    ADD oper_subject_guid VARCHAR(50);

CREATE TABLE `hsa_follow_red_packet_activity_order_details`
(
    `id`                BIGINT(20)             NOT NULL AUTO_INCREMENT,
    `guid`              VARCHAR(50) CHARACTER
        SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'guid',
    `oper_subject_guid` VARCHAR(50) CHARACTER
        SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '运营主体guid',
    `activity_guid`     VARCHAR(50) CHARACTER
        SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '随行红包guid',
    `activity_entrance` TINYINT(4)             NULL     DEFAULT NULL COMMENT '活动入口',
    `member_info_guid`  VARCHAR(50) CHARACTER
        SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '会员',
    `member_phone_num`  VARCHAR(50) CHARACTER
        SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '手机',
    `member_nick_name`  VARCHAR(50) CHARACTER
        SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '昵称',
    `red_packet_ratio`  DECIMAL(12, 2)         NULL     DEFAULT 0.00 COMMENT '红包命中率',
    `red_packet_amount` DECIMAL(12, 2)         NULL     DEFAULT 0.00 COMMENT '红包获得金额',
    `order_number`      VARCHAR(50) CHARACTER
        SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '订单编号',
    `gmt_create`        datetime(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    `gmt_modified`      datetime(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
    `user_profile`      VARCHAR(256) CHARACTER
        SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '用户头像',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_guid` (`guid`) USING BTREE,
    INDEX `order_and_subject` (`oper_subject_guid`, `order_number`) USING BTREE
);

CREATE TABLE `hsa_follow_red_packet_activity_order_record`
(
    `id`                  BIGINT(20)           NOT NULL AUTO_INCREMENT,
    `guid`                VARCHAR(50) CHARACTER
        SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'guid',
    `oper_subject_guid`   VARCHAR(50) CHARACTER
        SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '运营主体guid',
    `activity_guid`       VARCHAR(50) CHARACTER
        SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '随行红包guid',
    `order_amount`        DECIMAL(12, 2)       NULL     DEFAULT 0.00 COMMENT '订单金额',
    `order_paid_amount`   DECIMAL(12, 2)       NULL     DEFAULT 0.00 COMMENT '订单实付金额',
    `red_packet_amount`   DECIMAL(12, 2)       NULL     DEFAULT 0.00 COMMENT '红包金额',
    `store_name`          VARCHAR(50) CHARACTER
        SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL,
    `store_guid`          VARCHAR(50) CHARACTER
        SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL,
    `order_source`        TINYINT(4)           NULL     DEFAULT NULL COMMENT '订单来源',
    `consumption_time`    datetime(0)          NULL     DEFAULT NULL COMMENT '消费时间',
    `order_number`        VARCHAR(50) CHARACTER
        SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '订单编号',
    `gmt_create`          datetime(0)          NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    `gmt_modified`        datetime(0)          NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
    `participant_count`   INT(11)              NULL     DEFAULT NULL COMMENT '参与次数',
    `participant_person`  INT(11)              NULL     DEFAULT NULL COMMENT '参与人数',
    `share_count`         INT(11)              NULL     DEFAULT NULL COMMENT '分享次数',
    `parent_order_number` VARCHAR(50) CHARACTER
        SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '父订单',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_guid` (`guid`) USING BTREE,
    INDEX `activity_guid_index` (`activity_guid`) USING BTREE
);

CREATE TABLE `hsa_red_packet_append_member`
(
    `id`                bigint(20)                                                   NOT NULL AUTO_INCREMENT,
    `guid`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'guid',
    `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '运营主体guid',
    `member_account`    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '账户',
    `member_info_guid`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '会员',
    `member_phone_num`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '手机',
    `member_nick_name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '昵称',
    `order_number`      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '订单编号',
    `gmt_create`        datetime(0)                                                  NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    `gmt_modified`      datetime(0)                                                  NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_guid` (`guid`) USING BTREE,
    INDEX `order_number_index` (`order_number`) USING BTREE
);

CREATE TABLE `operation_log`
(
    `id`                int(11)                                                        NOT NULL AUTO_INCREMENT COMMENT '自增主键id',
    `guid`              bigint(50)                                                     NOT NULL COMMENT '操作记录guid',
    `content_guid`      bigint(50)                                                     NOT NULL COMMENT '日志对象guid',
    `operator`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '操作人',
    `phone_num`         char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NULL DEFAULT NULL COMMENT '操作人电话号码',
    `operation_content` varchar(3024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作内容',
    `log_type`          tinyint(4)                                                     NOT NULL COMMENT '日志类型',
    `operation_time`    datetime(0)                                                    NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '操作时间',
    `gmt_create`        datetime(0)                                                    NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    `gmt_modified`      datetime(0)                                                    NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_guid` (`guid`) USING BTREE,
    INDEX `index_content_guid` (`content_guid`) USING BTREE
)
