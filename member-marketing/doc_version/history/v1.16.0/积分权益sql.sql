
use hsm_member_marketing_platform_db;
alter table hsa_member_equities_receive_record add COLUMN type tinyint(4) DEFAULT 0 COMMENT '权益类型（0：成长值，1：积分）默认为成长值';

alter table hsa_integral_detail add COLUMN multiple_equities_guid varchar(50) DEFAULT '' COMMENT '翻倍权益guid';
alter table hsa_integral_detail add COLUMN multiple_integral_value int(11) DEFAULT null COMMENT '翻倍增加的积分';
alter table hsa_integral_detail modify column multiple_integral decimal(10,1) DEFAULT NULL COMMENT '翻倍积分权益（翻了多少倍）';


ALTER TABLE hsa_member_consumption ADD refund_time DATETIME COMMENT '退款时间';
UPDATE hsa_member_consumption a INNER JOIN hsa_member_consumption b ON a.order_number = b.order_number AND b.is_cancel = 1
SET a.refund_time = b.gmt_create WHERE a.is_cancel = 0;
ALTER TABLE hsa_member_grade_info ADD temporary_vip_grade INT COMMENT '临时VIP等级（管理后台展示）';
UPDATE hsa_member_grade_info SET temporary_vip_grade = vip_grade;

-- 添加默认值
ALTER TABLE hsa_grade_equities MODIFY COLUMN double_count_limited TINYINT(4) DEFAULT 0 COMMENT '成长值翻倍次数限制 0:不限制 1:限制';
UPDATE hsa_grade_equities SET double_count_limited = 0 WHERE double_count_limited IS NULL;

ALTER TABLE hsa_grade_equities MODIFY COLUMN single_double_limited TINYINT(4) DEFAULT 0 COMMENT '成长值单次翻倍限制 0:不限制 1:限制';
UPDATE hsa_grade_equities SET single_double_limited = 0 WHERE single_double_limited IS NULL;

ALTER TABLE hsa_grade_equities MODIFY COLUMN total_double_count_limited TINYINT(4) DEFAULT 0 COMMENT '累计翻倍限制 0:不限制 1:限制';
UPDATE hsa_grade_equities SET total_double_count_limited = 0 WHERE total_double_count_limited IS NULL;

ALTER TABLE hsa_grade_equities MODIFY COLUMN single_discounts_limited TINYINT(4) DEFAULT 0 COMMENT '单次优惠限制 0：不限制 1：限制';
UPDATE hsa_grade_equities SET single_discounts_limited = 0 WHERE single_discounts_limited IS NULL;

ALTER TABLE hsa_grade_equities MODIFY COLUMN period_discount_limited TINYINT(4) DEFAULT 0 COMMENT '周期优惠限制 0：不限制 1：限制';
UPDATE hsa_grade_equities SET period_discount_limited = 0 WHERE period_discount_limited IS NULL;

ALTER TABLE hsa_grade_equities MODIFY COLUMN total_discount_limited TINYINT(4) DEFAULT 0 COMMENT '累计优惠限制 0：不限制 1：限制';
UPDATE hsa_grade_equities SET total_discount_limited = 0 WHERE total_discount_limited IS NULL;

ALTER TABLE hsa_grade_equities MODIFY COLUMN discounts_superposition TINYINT(4) DEFAULT 0 COMMENT '优惠叠加限制 0：不可叠加 1：可叠加';
UPDATE hsa_grade_equities SET discounts_superposition = 0 WHERE discounts_superposition IS NULL;

ALTER TABLE hsa_grade_equities MODIFY COLUMN equities_effective_date_limited TINYINT(4) DEFAULT 0 COMMENT '权益生效时段限制 0：不限制 1：限制';
UPDATE hsa_grade_equities SET equities_effective_date_limited = 0 WHERE equities_effective_date_limited IS NULL;

ALTER TABLE hsa_grade_equities MODIFY COLUMN apply_goods_type TINYINT(4) DEFAULT -1 COMMENT '-1：全部商品适用 0：适用商品 1：不适用商品';
UPDATE hsa_grade_equities SET apply_goods_type = -1 WHERE apply_goods_type IS NULL;

ALTER TABLE hsa_grade_equities MODIFY COLUMN apply_terminal TINYINT(4) DEFAULT 0 COMMENT '适用终端 0:全部终端 1：部分终端';
UPDATE hsa_grade_equities SET apply_terminal = 0 WHERE apply_terminal IS NULL;

ALTER TABLE hsa_grade_equities MODIFY COLUMN apply_business TINYINT(4) DEFAULT 0 COMMENT '适用业务 0:全部业务 1：部分业务';
UPDATE hsa_grade_equities SET apply_business = 0 WHERE apply_business IS NULL;

ALTER TABLE hsa_grade_equities MODIFY COLUMN apply_channel TINYINT(4) DEFAULT 0 COMMENT '适用渠道 0:全部渠道 1：部分渠道';
UPDATE hsa_grade_equities SET apply_channel = 0 WHERE apply_channel IS NULL;