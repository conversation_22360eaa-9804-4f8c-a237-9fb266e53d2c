-- use hsm_member_marketing_center_platform_db

CREATE TABLE `hsa_order_evaluation_rule` (
                                             `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                             `guid` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT 'guid',
                                             `oper_subject_guid` varchar(50) COLLATE utf8mb4_icelandic_ci NOT NULL COMMENT '运营主体guid',
                                             `is_open` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否开始 0:未开启 1：开启',
                                             `validity_day` int(11) DEFAULT NULL COMMENT '评论有效期天数',
                                             `is_all_business` tinyint(1) DEFAULT '1' COMMENT '评价范围 0：部分业务 1:全部业务',
                                             `business_type` varchar(512) COLLATE utf8mb4_icelandic_ci DEFAULT NULL COMMENT '业务类型 0:堂食预定 1：自营外卖 2：食堂订单',
                                             `evaluation_object` varchar(512) COLLATE utf8mb4_icelandic_ci DEFAULT NULL COMMENT '评价对象 0:对订单评价 1：对商品评价 2：自定义内容评价',
                                             `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
                                             `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
                                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_icelandic_ci COMMENT='订单评价表';


CREATE TABLE `hsa_evaluation_dimension` (
                                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                            `guid` varchar(50) NOT NULL COMMENT 'guid',
                                            `oper_subject_guid` varchar(50) DEFAULT NULL COMMENT '运营主体guid',
                                            `rule_guid` varchar(50) NOT NULL COMMENT '订单评价规则guid',
                                            `dimension_name` varchar(10) DEFAULT NULL COMMENT '维度名称',
                                            `star_rating_type` tinyint(1) DEFAULT NULL COMMENT '星级评分 0：关闭 1：开启',
                                            `star_rating_json` varchar(512) DEFAULT NULL COMMENT '星级评分json',
                                            `evaluation_label` tinyint(1) DEFAULT NULL COMMENT '评价标签 0：关闭 1：开启',
                                            `label_option` tinyint(4) DEFAULT NULL COMMENT '标签选项 0：单选 1：多选',
                                            `label_json` varchar(512) DEFAULT NULL COMMENT '标签json',
                                            `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
                                            `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论维度';

CREATE TABLE `hsa_order_evaluation` (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                        `guid` varchar(50) NOT NULL COMMENT '标签设置GUID',
                                        `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',
                                        `order_type` tinyint(4) DEFAULT NULL COMMENT '业务类型 0:堂食预定 1：自营外卖 2：食堂订单',
                                        `member_info_guid` varchar(50) DEFAULT NULL COMMENT 'memberInfoGuid',
                                        `order_num` varchar(50) DEFAULT NULL COMMENT '订单编号',
                                        `store_name` varchar(50) DEFAULT NULL COMMENT '订单门店',
                                        `evaluation` tinyint(4) DEFAULT NULL COMMENT '评价对象 0:对订单评价 1：对商品评价 2：自定义内容评价',
                                        `member_name` varchar(50) DEFAULT NULL COMMENT '用户名称',
                                        `member_phone` varchar(50) DEFAULT NULL COMMENT '用户手机',
                                        `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                        `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                        `evaluate_detailed_json` varchar(2000) DEFAULT NULL COMMENT '评价详情',
                                        `member_image` varchar(255) DEFAULT NULL,
                                        PRIMARY KEY (`id`) USING BTREE,
                                        UNIQUE KEY `uk_guid` (`guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1552 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='标签设置表';

SET FOREIGN_KEY_CHECKS = 1;