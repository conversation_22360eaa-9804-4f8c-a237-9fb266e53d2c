use gateway;

INSERT INTO `gateway`.`hp_function_group`(`create_time`, `name`, `sequence`, `update_time`, `gmt_create`, `gmt_modified`)
VALUES (now(), '订单评价', 109, now(), now(), now());


INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), '订单评价列表', 1, 60, NOW(), 26,
        ( SELECT id FROM hp_system WHERE NAME = '营销中心' AND identification = 'marketing_count' ORDER BY gmt_modified DESC LIMIT 1 ),
       (select id from hp_function_group where name = '订单评价' order by gmt_modified desc limit 1),NOW(), NOW());

INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), '订单评价规则', 2, 60, NOW(), 26,
        ( SELECT id FROM hp_system WHERE NAME = '营销中心' AND identification = 'marketing_count' ORDER BY gmt_modified DESC LIMIT 1 ),
       (select id from hp_function_group where name = '订单评价' order by gmt_modified desc limit 1),NOW(), NOW());



INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`,
                                      `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '订单评价列表查看', NOW(), (select id from hp_function where name = '订单评价列表' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`,
                                      `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '开启/关闭订单评价', NOW(), (select id from hp_function where name = '订单评价规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`,
                                      `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '订单评价规则查看', NOW(), (select id from hp_function where name = '订单评价规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`,
                                      `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '订单评价规则编辑', NOW(), (select id from hp_function where name = '订单评价规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
