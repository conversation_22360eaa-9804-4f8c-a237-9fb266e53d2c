use hsm_member_mall_tool_platform_db;
-- 店铺主页模板
DROP TABLE IF EXISTS `hsa_store_home_template`;
CREATE TABLE `hsa_store_home_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) NOT NULL COMMENT '模板guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0未删除,1已删除',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',
  `template_name` varchar(25) NOT NULL COMMENT '模板名称',
  `description` varchar(50) DEFAULT NULL COMMENT '页面描述',
  `json` json DEFAULT NULL COMMENT '模板名称',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE,
  INDEX `oper_subject_guid_idx`(`oper_subject_guid`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 ROW_FORMAT = DYNAMIC COMMENT = '店铺主页模板';

-- 店铺主页模板关联门店
DROP TABLE IF EXISTS `hsa_store_home_template_relation`;
CREATE TABLE `hsa_store_home_template_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) NOT NULL COMMENT '关联guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',
  `store_guid` varchar(25) NOT NULL COMMENT '门店guid',
  `template_guid` varchar(50) NOT NULL COMMENT '模板guid',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE,
  UNIQUE INDEX `uk_oper_subject_store_template_guid_idx`(`oper_subject_guid`,`store_guid`,`template_guid`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 ROW_FORMAT = DYNAMIC COMMENT = '店铺主页模板关联门店';