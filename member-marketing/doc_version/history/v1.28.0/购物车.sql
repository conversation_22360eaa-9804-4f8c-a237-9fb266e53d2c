use hsm_member_mall_platform_db;

DROP TABLE IF EXISTS `hsa_shopping_cart_commodity`;
CREATE TABLE `hsa_shopping_cart_commodity` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) NOT NULL COMMENT '购物车商品guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除,0未删除,1已删除',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `member_info_guid` varchar(50) NOT NULL COMMENT '会员GUID',
  `commodity_id` bigint(20) DEFAULT NULL COMMENT '商品id',
  `store_id` int(11) DEFAULT NULL COMMENT '门店id',
  `num` int(11) DEFAULT NULL COMMENT '商品数量',
  `sku` text COMMENT '规格参数',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '商品更新时间',
  `attr` text COMMENT '属性参数',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=205 DEFAULT CHARSET=utf8mb4 COMMENT='购物车商品表';