# v1.11.0配置及部署说明

## 访问
营销中心（member-center） nginx 配置需增加 /marketing  (test | re | prod)


## 接口
1. 营销中心所有接口前缀从 /base/ 改为 /marketing/
1. 适配老系统url跳转
```
https://member-center-test.holderzone.cn?system=1&logintype=0&terminalcode=100&menguid=2202221141272870002&token=xx
```

## 服务

增加服务
```
. holder-member-marketing v1.11.0
```

## 配置

> 可参考开发环境 http://192.168.100.111:8848/nacos/

1. 增加 datasource-marketing.yaml（从common复制）
```yaml
#修改数据库配置、
spring:
  datasource:
    url: hsm_member_marketing_center_platform_db  #数据库改了
    
mybatis-plus:
  type-aliases-package: com.holderzone.member.marketing.entity  #mybatis扫描包改了

```
profiles : dev | test | re | prod

2. 增加 member-marketing-{profiles}.yaml （复制 member-base-{profiles}.yaml）

```yaml
# member-marketing-{profiles}.yaml 增加：访问各业务系统的不同环境的访问地址
feign:
  goalgo: http://gateway.hp.goalgo.cn:42770
  store: https://sale-crm.holderzone.cn
  crm: https://sale-crm.holderzone.cn
  saas: https://mch-dev.holderzone.cn

# 新增了门店以及hodlerURL地址
goalgo:
  host: http://gateway.hp.goalgo.cn:42770
  store: https://sale-crm.holderzone.cn
  crm: https://sale-crm.holderzone.cn
```

3. member-gateway-{profiles}.yaml 增加
```yaml
- id: member-marketing-route
  uri: lb://member-marketing
  predicates:
  - Path=/marketing/**
  filters:
  - StripPrefix=1
```