use hsm_member_marketing_center_platform_db;
CREATE TABLE `hsa_follow_red_packet_store`
(
    `id`                     bigint(20)  NOT NULL AUTO_INCREMENT,
    `guid`                   varchar(50) NOT NULL COMMENT 'guid',
    `enterprise_guid`        varchar(50)          DEFAULT NULL COMMENT '企业guid',
    `follow_red_packet_guid` varchar(50) NOT NULL COMMENT '随行红包guid',
    `store_guid`             varchar(50) NOT NULL COMMENT '门店guid',
    `store_name`             varchar(50) NOT NULL COMMENT '门店名称',
    `store_number`           varchar(50)          DEFAULT NULL COMMENT '门店编号',
    `time`                   varchar(50)          DEFAULT NULL COMMENT '营业时间',
    `address`                varchar(512)         DEFAULT NULL COMMENT '营业地址',
    `gmt_create`             datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`           datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `address_point`          varchar(128)         DEFAULT NULL COMMENT '经纬度',
    `applicable_all_table`   tinyint(1)           DEFAULT '0' COMMENT '全部桌台 0：不是 1：是',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 220
  DEFAULT CHARSET = utf8mb4;


CREATE TABLE `hsa_follow_red_packet_table`
(
    `id`                     bigint(20)  NOT NULL AUTO_INCREMENT,
    `guid`                   varchar(50) NOT NULL COMMENT 'guid',
    `red_packet_store_guid`  varchar(50) NOT NULL COMMENT '门店guid',
    `area_guid`              varchar(50)  DEFAULT NULL COMMENT '区域guid',
    `area_name`              varchar(128) DEFAULT NULL COMMENT '区域名称',
    `table_code`             varchar(50)  DEFAULT NULL COMMENT '桌台编号',
    `gmt_create`             datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`           datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `follow_red_packet_guid` varchar(50) NOT NULL COMMENT '随行红包guid',
    `seats`                  int(11)      DEFAULT '0' COMMENT '桌台座位数',
    `table_guid`             varchar(50) NOT NULL COMMENT '桌台guid',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 35
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `hsa_follow_red_packet_activity`
(
    `id`                             int(11)                                                       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `guid`                           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '成长值规则GUID',
    `oper_subject_guid`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '运营主体GUID',
    `enterprise_guid`                varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '成长值规则',
    `gmt_create`                     datetime(0)                                                   NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    `gmt_modified`                   datetime(0)                                                   NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    `activity_name`                  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '活动主题名称',
    `activity_start_time`            datetime(0)                                                   NULL     DEFAULT NULL COMMENT '活动开始时间',
    `activity_end_time`              datetime(0)                                                   NULL     DEFAULT NULL COMMENT '活动结束时间',
    `activity_state`                 tinyint(4)                                                    NULL     DEFAULT NULL COMMENT '活动发布状态 1 未发布 2 未开始 3 发布中  4 已暂停 5 已结束　6进行中　7暂停已发布',
    `activity_code`                  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '活动编码',
    `effective_date_limited`         tinyint(4)                                                    NULL     DEFAULT NULL COMMENT '生效时段限制 0：不限制 1：限制',
    `time_limited_type`              tinyint(4)                                                    NULL     DEFAULT NULL COMMENT '限制时段类型 -1:自定义 0：日 1：周 2：月 3：年',
    `time_limited_json`              varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '限制时段限制类型json',
    `single_order_discounts_limited` tinyint(4)                                                    NULL     DEFAULT NULL COMMENT '单笔订单优惠限制 0 动态金额 1 固定金额',
    `single_order_money_percent`     decimal(12, 2)                                                NULL     DEFAULT NULL COMMENT '根据订单应付金额 %',
    `lowest_discount`                decimal(12, 2)                                                NULL     DEFAULT NULL COMMENT '最低优惠金额',
    `highest_discount`               decimal(12, 2)                                                NULL     DEFAULT NULL COMMENT '最高优惠金额',
    `fixed_amount`                   decimal(12, 2)                                                NULL     DEFAULT NULL COMMENT '固定金额',
    `order_source`                   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '支持的点餐订单来源',
    `single_order_member_num`        int(12)                                                       NULL     DEFAULT NULL COMMENT '每人每单参与次数',
    `single_member_day_order_num`    int(12)                                                       NULL     DEFAULT NULL COMMENT '每人每天最多参与单数',
    `first_share_Increase_num`       int(12)                                                       NULL     DEFAULT NULL COMMENT '首次分享战绩可此单增加参与次数',
    `activity_scope_limited`         int(12)                                                       NULL     DEFAULT NULL COMMENT '活动参与范围限制 0：不限制 1：限制',
    `scope_distance_limited`         int(12)                                                       NULL     DEFAULT NULL COMMENT '范围限制距离',
    `member_info_limited`            tinyint(4)                                                    NULL     DEFAULT NULL COMMENT '完善个人信息限制 0：不限制 1：限制',
    `single_duration_time_limited`   tinyint(4)                                                    NULL     DEFAULT NULL COMMENT '单次持续时长限制 0 固定时长 1 不限时长',
    `single_duration_time`           bigint(12)                                                    NULL     DEFAULT NULL COMMENT '固定时长多少秒（固定时长）',
    `red_envelope_end_total`         int(12)                                                       NULL     DEFAULT NULL COMMENT '游戏期间 多少个红包落地即触发“游戏结束”（不限时长）',
    `descent_speed_limited`          int(12)                                                       NULL     DEFAULT NULL COMMENT '降落速度限制 0 匀速 1 变速',
    `descent_second_time`            decimal(12, 1)                                                NULL     DEFAULT NULL COMMENT '多少秒降落一个红包 (匀速)',
    `descent_second_time_json`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '多少秒降落红包 (变速)',
    `bomb_percent`                   int(12)                                                       NULL     DEFAULT NULL COMMENT '炸弹概率 %',
    `bomb_deduction_type`            tinyint(4)                                                    NULL     DEFAULT NULL COMMENT '炸弹扣点方式 0 金额 1 游戏结束',
    `single_bomb_deduction_money`    decimal(12, 2)                                                NULL     DEFAULT NULL COMMENT '一个炸弹扣减',
    `null_red_envelope_percent`      int(12)                                                       NULL     DEFAULT NULL COMMENT '空红包概率 %',
    `red_envelope_percent`           int(12)                                                       NULL     DEFAULT NULL COMMENT '红包概率 %',
    `game_background_music_type`     tinyint(4)                                                    NULL     DEFAULT NULL COMMENT '游戏背景音乐类型 0 默认 1 自定义',
    `game_background_music`          varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '游戏背景音乐（自定义）',
    `game_background_music_name`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '背景音乐名称',
    `red_envelope_style_type`        tinyint(4)                                                    NULL     DEFAULT NULL COMMENT '掉落红包样式类型 0 默认 1 自定义',
    `red_envelope_style`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '掉落红包样式（自定义）',
    `scan_code_finish_ads_pictures`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '扫码点餐完成广告图',
    `scan_code_snack_entrance`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '扫码加餐入口',
    `share_copy`                     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '分享文案',
    `share_picture`                  varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '分享图片',
    `is_delete`                      tinyint(4)                                                    NULL     DEFAULT NULL COMMENT 'del',
    `operator_tel_name`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '操作人员账号',
    `activity_remark`                text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         NULL,
    `is_redact`                      tinyint(4) UNSIGNED                                           NULL     DEFAULT NULL COMMENT '是否编辑',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_guid` (`guid`) USING BTREE
)

CREATE TABLE `operation_log`
(
    `id`                int(11)                                                        NOT NULL AUTO_INCREMENT COMMENT '自增主键id',
    `guid`              bigint(50)                                                     NOT NULL COMMENT '操作记录guid',
    `content_guid`      bigint(50)                                                     NOT NULL COMMENT '日志对象guid',
    `operator`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '操作人',
    `phone_num`         char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NULL DEFAULT NULL COMMENT '操作人电话号码',
    `operation_content` varchar(3024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作内容',
    `log_type`          tinyint(4)                                                     NOT NULL COMMENT '日志类型',
    `operation_time`    datetime(0)                                                    NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '操作时间',
    `gmt_create`        datetime(0)                                                    NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    `gmt_modified`      datetime(0)                                                    NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_guid` (`guid`) USING BTREE,
    INDEX `index_content_guid` (`content_guid`) USING BTREE
)

