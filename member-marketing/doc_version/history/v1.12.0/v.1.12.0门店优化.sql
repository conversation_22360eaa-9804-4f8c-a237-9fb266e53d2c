CREATE TABLE `hsa_store_rule_info` (
  `id` BIGINT(10) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` VARCHAR(50) NOT NULL COMMENT 'guid',
  `oper_subject_guid` VARCHAR(50) NOT NULL COMMENT '运营主体guid',
  `store_guid` VARCHAR(50) DEFAULT NULL COMMENT '门店guid',
  `store_name` VARCHAR(50) DEFAULT NULL COMMENT '门店名称',
  `store_number` VARCHAR(50) DEFAULT NULL COMMENT '门店编号',
  `parent_guid` VARCHAR(50) DEFAULT NULL COMMENT '当有此数据时，此数据为档口',
  `time` VARCHAR(50) DEFAULT NULL COMMENT '营业时间',
  `address` VARCHAR(50) DEFAULT NULL COMMENT '营业地址',
  `address_point` VARCHAR(50) DEFAULT NULL COMMENT '经纬度',
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `store_logo` VARCHAR(1000) DEFAULT NULL COMMENT '门店图片',
  `type` TINYINT(4) DEFAULT NULL COMMENT '类型 0:挂账 1:成长值',
  `type_guid` VARCHAR(50) DEFAULT NULL COMMENT '类型guid',
  PRIMARY KEY (`id`)
) ENGINE=INNODB AUTO_INCREMENT=587 DEFAULT CHARSET=utf8mb4;



INSERT INTO hsa_store_rule_info (guid,oper_subject_guid,store_guid,store_number,store_name,parent_guid,TIME,address,address_point,store_logo,gmt_create,gmt_modified,TYPE,type_guid)
SELECT
guid,oper_subject_guid,store_guid,store_number,store_name,parent_guid,TIME,address,address_point,store_logo,gmt_create,gmt_modified,1 AS TYPE,growth_value_task_guid AS type_guid
 FROM hsa_growth_value_store_rule;


INSERT INTO hsa_store_rule_info (guid,oper_subject_guid,store_guid,store_number,store_name,parent_guid,TIME,address,address_point,gmt_create,gmt_modified,TYPE,type_guid)
SELECT guid,oper_subject_guid,store_guid,store_number,store_name,parent_guid,TIME,address,address_point,gmt_create,gmt_modified,0 AS TYPE,credit_info_guid AS type_guid
 FROM hsa_credit_apply_store;
