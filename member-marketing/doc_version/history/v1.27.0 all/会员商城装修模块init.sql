-- 新建会员商城装修库
CREATE DATABASE `hsm_member_mall_tool_platform_db` CHARACTER SET 'utf8' COLLATE 'utf8_general_ci';

use hsm_member_mall_tool_platform_db;

-- 微页面表
DROP TABLE IF EXISTS `hsa_mall_page`;
CREATE TABLE `hsa_mall_page` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) NOT NULL COMMENT '微页面guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0未删除,1已删除',
  `name` varchar(20) NOT NULL COMMENT '微页面名称',
  `description` varchar(50) NULL COMMENT '页面描述',
  `state` tinyint(4) NULL COMMENT '状态: 0未发布 1已发布',
  `oper_subject_guid` varchar(50) NULL COMMENT '运营主体GUID',
  `applet_id` varchar(50) NULL COMMENT '小程序id',
  `json` text NULL COMMENT '页面json数据',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE,
  INDEX `index_name`(`name`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 ROW_FORMAT = DYNAMIC COMMENT = '微页面表';

-- 基础页面表
DROP TABLE IF EXISTS `hsm_member_mall_tool_platform_db`.`hsa_basic_page`;
CREATE TABLE `hsm_member_mall_tool_platform_db`.`hsa_basic_page` (
	`id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
	`guid` VARCHAR ( 50 ) NOT NULL COMMENT '模块guid',
    `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
	`is_delete` TINYINT ( 1 ) NOT NULL DEFAULT 0 COMMENT '是否删除,0未删除,1已删除',
	`page_type` VARCHAR ( 50 ) NULL COMMENT '页面类型',
	`page_link` VARCHAR ( 255 ) NULL COMMENT '页面链接',
	`name` VARCHAR ( 50 ) NULL COMMENT '页面名称',
	`sort` int(10) unsigned NOT NULL COMMENT '序号',
	PRIMARY KEY ( `id` ),
UNIQUE INDEX `uk_guid` ( `guid` ) USING BTREE
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 ROW_FORMAT = DYNAMIC COMMENT = '基础页面表';

-- hsm_member_mall_tool_platform_db.hsa_applet_navigation definition

CREATE TABLE `hsa_applet_navigation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `title` varchar(255) DEFAULT NULL COMMENT '标题',
  `on_picture` varchar(255) DEFAULT NULL COMMENT '点亮时图片',
  `off_picture` varchar(255) DEFAULT NULL COMMENT '未点亮时图片',
  `url` varchar(255) DEFAULT NULL COMMENT '链接',
  `is_delete` tinyint(1) DEFAULT NULL COMMENT '是否删除,0未删除,1已删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  `oper_subject_guid` varchar(50) DEFAULT NULL COMMENT '运营主体GUID',
  `select_name` varchar(100) DEFAULT NULL COMMENT '页面名字',
  `select_value` varchar(100) DEFAULT NULL COMMENT '页面值',
  `type_text` varchar(100) DEFAULT NULL COMMENT '页面类型',
  `link_id` varchar(100) DEFAULT NULL COMMENT '链接地址id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=407 DEFAULT CHARSET=utf8 COMMENT='小程序导航';


-- hsm_member_mall_tool_platform_db.hsa_theme_style definition

CREATE TABLE `hsa_theme_style` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(100) CHARACTER SET utf8 NOT NULL COMMENT 'guid',
  `color_style` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '颜色风格',
  `is_delete` tinyint(1) DEFAULT NULL COMMENT '是否删除 0：未删除   1：已删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `oper_subject_guid` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '运营主体guid',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4;


-- hsm_member_mall_tool_platform_db.hsa_permission definition

CREATE TABLE `hsa_permission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `enterprise_guid` varchar(50) NOT NULL COMMENT '企业id',
  `position_guid` varchar(50) NOT NULL COMMENT '岗位、角色id',
  `is_role` tinyint(1) DEFAULT NULL COMMENT '类型 0：岗位 1：角色',
  `type_id` varchar(50) DEFAULT NULL COMMENT '门店id、运营主体id',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `type` tinyint(1) DEFAULT NULL COMMENT '0：运营主体权限 1：门店权限',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8 COMMENT='会员商城选择门店权限';


-- hsm_member_mall_tool_platform_db.hsa_permission_type definition

CREATE TABLE `hsa_permission_type` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `enterprise_guid` varchar(50) NOT NULL COMMENT '企业id',
  `position_guid` varchar(50) DEFAULT NULL COMMENT '岗位id或者角色id',
  `is_role` tinyint(1) DEFAULT NULL COMMENT '类型 0：岗位 1：角色',
  `is_all` tinyint(1) DEFAULT NULL COMMENT '全部门店 0:不是 1:是',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `type` tinyint(1) DEFAULT NULL COMMENT '0：运营主体权限 1：门店权限',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COMMENT='门店权限类型表';

-- hsm_member_mall_tool_platform_db.hsa_mall_picture definition

CREATE TABLE `hsa_mall_picture` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `oper_subject_guid` varchar(50) DEFAULT NULL COMMENT '运营主体guid',
  `picture_type` tinyint(4) DEFAULT NULL COMMENT '图片类型 0：我的图片 1：图片库',
  `picture_position` int(10) DEFAULT NULL COMMENT '图片库位置 0:底部导航 1:导航图标',
  `picture_url` varchar(255) DEFAULT NULL COMMENT '图片地址',
  `is_delete` tinyint(1) DEFAULT NULL COMMENT '是否删除,0未删除,1已删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=230 DEFAULT CHARSET=utf8;

-- 图片
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(7, '1', '', 1, 0, 'bottom_icon/1.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(8, '2', '', 1, 0, 'bottom_icon/2.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(9, '3', '', 1, 0, 'bottom_icon/3.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(10, '4', '', 1, 0, 'bottom_icon/4.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(11, '5', '', 1, 0, 'bottom_icon/5.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(12, '6', '', 1, 0, 'bottom_icon/6.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(13, '7', '', 1, 0, 'bottom_icon/7.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(14, '8', '', 1, 0, 'bottom_icon/8.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(15, '9', '', 1, 0, 'bottom_icon/9.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(16, '10', '', 1, 0, 'bottom_icon/10.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(17, '11', '', 1, 0, 'bottom_icon/11.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(18, '12', '', 1, 0, 'bottom_icon/12.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(19, '13', '', 1, 0, 'bottom_icon/13.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(20, '14', '', 1, 0, 'bottom_icon/14.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(21, '15', '', 1, 0, 'bottom_icon/15.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(22, '16', '', 1, 0, 'bottom_icon/16.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(23, '17', '', 1, 0, 'bottom_icon/17.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(24, '18', '', 1, 0, 'bottom_icon/18.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(25, '19', '', 1, 0, 'bottom_icon/19.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(26, '20', '', 1, 0, 'bottom_icon/20.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(27, '21', '', 1, 0, 'bottom_icon/21.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(28, '22', '', 1, 0, 'bottom_icon/22.png', 0, '2022-12-29 10:18:18', '2022-12-29 10:18:18');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(29, '23', '', 1, 1, 'Navigation_icon/0340607463945059517044757498196198032926.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(30, '24', '', 1, 1, 'Navigation_icon/0388525304555483856853751854303228119940.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(31, '25', '', 1, 1, 'Navigation_icon/0579942328871487444392382618203086565149.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(32, '26', '', 1, 1, 'Navigation_icon/0731848416801398010350341808382542996294.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(33, '27', '', 1, 1, 'Navigation_icon/0733408272674368464275190011027184791990.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(34, '28', '', 1, 1, 'Navigation_icon/0869337775887793009025061303101969510744.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(35, '29', '', 1, 1, 'Navigation_icon/1106424016876477724110287960809842257357.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(36, '30', '', 1, 1, 'Navigation_icon/2088250762057869712734404394505764930669.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(37, '31', '', 1, 1, 'Navigation_icon/2279046167399283445145415778966924851348.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(38, '32', '', 1, 1, 'Navigation_icon/2335736670726535992968720467886799758466.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(39, '33', '', 1, 1, 'Navigation_icon/2337054538824353523506081542941873301242.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(40, '34', '', 1, 1, 'Navigation_icon/2656244720698604093177017373330692069416.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(41, '35', '', 1, 1, 'Navigation_icon/2829321882020241081253169291247136785520.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(42, '36', '', 1, 1, 'Navigation_icon/3352352966199958964417406994805917804586.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(43, '37', '', 1, 1, 'Navigation_icon/3370138366937729319633139252140275756495.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(44, '38', '', 1, 1, 'Navigation_icon/3737957723785350459918399130101766469168.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(45, '39', '', 1, 1, 'Navigation_icon/3759751462888482989540594222600359628469.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(46, '40', '', 1, 1, 'Navigation_icon/4649782237229638338882765604394647444571.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(47, '41', '', 1, 1, 'Navigation_icon/4855209995357954609709926367510791106212.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(48, '42', '', 1, 1, 'Navigation_icon/4921624291906161842534650132285779363053.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(49, '43', '', 1, 1, 'Navigation_icon/5288098063101110966233270555541739617715.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(50, '44', '', 1, 1, 'Navigation_icon/5726738554440036685174699784892143980392.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(51, '45', '', 1, 1, 'Navigation_icon/6664598685057306335877405581738643327264.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(52, '46', '', 1, 1, 'Navigation_icon/7879296684673130611494885382820140944922.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(53, '47', '', 1, 1, 'Navigation_icon/7930198341299923551516631579545774871720.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(54, '48', '', 1, 1, 'Navigation_icon/7956680964416143463578673449991544513243.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(55, '49', '', 1, 1, 'Navigation_icon/8122986873324654774454455995185036008226.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(56, '50', '', 1, 1, 'Navigation_icon/8786310837887300764822476069649908593152.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(57, '51', '', 1, 1, 'Navigation_icon/9438527812438703364132294575693149797329.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(58, '52', '', 1, 1, 'Navigation_icon/9793135976280324777202370459516870247312.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');
INSERT INTO hsm_member_mall_tool_platform_db.hsa_mall_picture
(id, guid, oper_subject_guid, picture_type, picture_position, picture_url, is_delete, gmt_create, gmt_modified)
VALUES(59, '53', '', 1, 1, 'Navigation_icon/9854567974014734440020050680012820706688.png', 0, '2022-12-29 10:32:35', '2022-12-29 10:32:35');

-- 基础页面
INSERT INTO hsm_member_mall_tool_platform_db.hsa_basic_page
(id, guid, gmt_create, gmt_modified, is_delete, page_type, page_link, name, sort)
VALUES(20, '7018403357228269568', '2023-01-10 10:29:16', '2023-01-10 10:29:16', 0, 'BUSINESS_PAGE', '/package/store/store', '食堂/门店列表页', 1);
INSERT INTO hsm_member_mall_tool_platform_db.hsa_basic_page
(id, guid, gmt_create, gmt_modified, is_delete, page_type, page_link, name, sort)
VALUES(21, '7018403357245046784', '2023-01-10 10:29:16', '2023-01-10 10:29:16', 0, 'BUSINESS_PAGE', '/package/store/store', '食堂预定', 2);
INSERT INTO hsm_member_mall_tool_platform_db.hsa_basic_page
(id, guid, gmt_create, gmt_modified, is_delete, page_type, page_link, name, sort)
VALUES(22, '7018403357253435392', '2023-01-10 10:29:16', '2023-01-10 10:29:16', 0, 'BUSINESS_PAGE', '/pages/scanJump/scanJump', '扫码点餐', 3);
INSERT INTO hsm_member_mall_tool_platform_db.hsa_basic_page
(id, guid, gmt_create, gmt_modified, is_delete, page_type, page_link, name, sort)
VALUES(23, '7018403357261824000', '2023-01-29 14:28:38', '2023-01-29 14:28:38', 1, 'BUSINESS_PAGE', '', '外卖点餐', 4);
INSERT INTO hsm_member_mall_tool_platform_db.hsa_basic_page
(id, guid, gmt_create, gmt_modified, is_delete, page_type, page_link, name, sort)
VALUES(24, '7018403357266018304', '2023-01-10 10:29:16', '2023-01-10 10:29:16', 0, 'MALL_PAGE', '/pagesMall/classify/index', '商城分类页', 1);
INSERT INTO hsm_member_mall_tool_platform_db.hsa_basic_page
(id, guid, gmt_create, gmt_modified, is_delete, page_type, page_link, name, sort)
VALUES(25, '7018403357270212608', '2023-01-29 14:35:38', '2023-01-29 14:35:38', 1, 'MALL_PAGE', '/pagesMall/shoppingCart/index', '商城购物车', 2);
INSERT INTO hsm_member_mall_tool_platform_db.hsa_basic_page
(id, guid, gmt_create, gmt_modified, is_delete, page_type, page_link, name, sort)
VALUES(26, '7018403357274406912', '2023-01-10 10:29:16', '2023-01-10 10:29:16', 0, 'MALL_PAGE', '/pagesMall/mallSearch/index', '搜索页面', 3);
INSERT INTO hsm_member_mall_tool_platform_db.hsa_basic_page
(id, guid, gmt_create, gmt_modified, is_delete, page_type, page_link, name, sort)
VALUES(27, '7018403357274406913', '2023-01-17 12:08:58', '2023-01-17 12:08:58', 1, 'MALL_PAGE', '', '我的收藏', 4);
INSERT INTO hsm_member_mall_tool_platform_db.hsa_basic_page
(id, guid, gmt_create, gmt_modified, is_delete, page_type, page_link, name, sort)
VALUES(28, '7018403357278601216', '2023-01-10 10:29:16', '2023-01-10 10:29:16', 0, 'COMMON_PAGE', '/pages/mine/index', '个人中心', 1);
INSERT INTO hsm_member_mall_tool_platform_db.hsa_basic_page
(id, guid, gmt_create, gmt_modified, is_delete, page_type, page_link, name, sort)
VALUES(29, '7018403357282795520', '2023-01-10 10:29:16', '2023-01-10 10:29:16', 0, 'COMMON_PAGE', '/pages/memberCard/index', '我的会员卡', 2);
INSERT INTO hsm_member_mall_tool_platform_db.hsa_basic_page
(id, guid, gmt_create, gmt_modified, is_delete, page_type, page_link, name, sort)
VALUES(31, '7018403357286989825', '2023-01-16 17:35:09', '2023-01-16 17:35:09', 0, 'COMMON_PAGE', '/package/orderList/orderList', '我的订单', 3);
INSERT INTO hsm_member_mall_tool_platform_db.hsa_basic_page
(id, guid, gmt_create, gmt_modified, is_delete, page_type, page_link, name, sort)
VALUES(32, '7018403357291184128', '2023-01-16 17:35:13', '2023-01-16 17:35:13', 0, 'COMMON_PAGE', '/package/paymentCode/paymentCode', '支付码', 4);
INSERT INTO hsm_member_mall_tool_platform_db.hsa_basic_page
(id, guid, gmt_create, gmt_modified, is_delete, page_type, page_link, name, sort)
VALUES(33, '7018403357291184129', '2023-01-16 17:35:15', '2023-01-16 17:35:15', 0, 'COMMON_PAGE', '/package/integral/integral', '我的积分', 5);
INSERT INTO hsm_member_mall_tool_platform_db.hsa_basic_page
(id, guid, gmt_create, gmt_modified, is_delete, page_type, page_link, name, sort)
VALUES(34, '7018403357295378432', '2023-01-29 15:00:28', '2023-01-29 15:00:28', 0, 'COMMON_PAGE', '/pagesMall/addressAll/index', '我的地址', 6);
INSERT INTO hsm_member_mall_tool_platform_db.hsa_basic_page
(id, guid, gmt_create, gmt_modified, is_delete, page_type, page_link, name, sort)
VALUES(35, '7018403357299572736', '2023-01-16 17:35:16', '2023-01-16 17:35:16', 0, 'COMMON_PAGE', '/package/grade/grade', '我的等级', 7);
INSERT INTO hsm_member_mall_tool_platform_db.hsa_basic_page
(id, guid, gmt_create, gmt_modified, is_delete, page_type, page_link, name, sort)
VALUES(36, '7018403357303767040', '2023-01-17 12:09:01', '2023-01-17 12:09:01', 1, 'COMMON_PAGE', '', '我的消息', 8);
INSERT INTO hsm_member_mall_tool_platform_db.hsa_basic_page
(id, guid, gmt_create, gmt_modified, is_delete, page_type, page_link, name, sort)
VALUES(37, '7018403357307961344', '2023-01-17 12:08:59', '2023-01-17 12:08:59', 1, 'COMMON_PAGE', '', '在线客服', 9);
INSERT INTO hsm_member_mall_tool_platform_db.hsa_basic_page
(id, guid, gmt_create, gmt_modified, is_delete, page_type, page_link, name, sort)
VALUES(38, '7018403357307961345', '2023-01-10 10:29:16', '2023-01-10 10:29:16', 0, 'MARKETING_ACTIVITY', '/packageSecond/voteActivity/voteList/voteList', '投票活动列表', 1);
