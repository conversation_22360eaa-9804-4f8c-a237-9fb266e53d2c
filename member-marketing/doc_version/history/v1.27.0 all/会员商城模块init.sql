CREATE DATABASE `hsm_member_mall_platform_db` CHARACTER SET 'utf8' COLLATE 'utf8_general_ci';

use hsm_member_mall_platform_db;

-- hsm_member_mall_platform_db.hsa_after_sale_order definition

CREATE TABLE `hsa_after_sale_order` (
  `id` int(20) NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) NOT NULL,
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `enterprise_guid` varchar(50) NOT NULL DEFAULT '' COMMENT '企业guid',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `member_info_guid` varchar(50) DEFAULT NULL COMMENT '会员GUID',
  `after_order_num` varchar(50) NOT NULL DEFAULT '2' COMMENT '售后编号',
  `refund_condition` tinyint(4) DEFAULT NULL COMMENT '退款状态',
  `order_number` varchar(50) DEFAULT NULL COMMENT '订单编号',
  `pay_order_time` timestamp(4) NULL DEFAULT NULL COMMENT '下单时间',
  `order_condition` tinyint(4) DEFAULT NULL COMMENT '订单状态',
  `cancel` varchar(500) DEFAULT '0' COMMENT '退款原因',
  `refund_way` tinyint(4) DEFAULT '0' COMMENT '退款方式：目前仅有 仅退款',
  `refund_type` tinyint(4) DEFAULT NULL COMMENT '退款类型',
  `deal_type` tinyint(4) DEFAULT NULL COMMENT '是否已处理 0待处理 1已处理',
  `order_guid` varchar(50) DEFAULT NULL,
  `refund_amount` decimal(12,2) DEFAULT NULL COMMENT '退款金额',
  `actual_refund_amount` decimal(12,2) DEFAULT NULL COMMENT '实退金额',
  `refuse_reason` varchar(500) DEFAULT NULL COMMENT '拒绝退款原因',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`) USING BTREE,
  UNIQUE KEY `uk_after_order_num` (`after_order_num`) USING BTREE,
  KEY `index_order_guid` (`order_guid`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;


-- hsm_member_mall_platform_db.hsa_cancel_order_rule definition

CREATE TABLE `hsa_cancel_order_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) DEFAULT NULL COMMENT 'guid',
  `oper_subject_guid` varchar(100) DEFAULT NULL COMMENT '运营主体guid',
  `cancel` varchar(256) DEFAULT NULL COMMENT '原因',
  `cancel_type` varchar(100) DEFAULT NULL COMMENT '应用范围 0:退款 1:取消订单',
  `custom_input` tinyint(1) DEFAULT NULL COMMENT '是否允许用户自定义输入',
  `is_delete` tinyint(1) DEFAULT NULL COMMENT '是否删除',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;


-- hsm_member_mall_platform_db.hsa_commodity_label definition

CREATE TABLE `hsa_commodity_label` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `oper_subject_guid` varchar(50) DEFAULT NULL COMMENT '运营主体guid',
  `label_name` varchar(6) NOT NULL COMMENT '标签名称',
  `label_colour` varchar(255) DEFAULT NULL COMMENT '标签背景颜色',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_delete` tinyint(4) DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='商品标签';


-- hsm_member_mall_platform_db.hsa_commodity_set definition

CREATE TABLE `hsa_commodity_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `share_format` tinyint(4) NOT NULL DEFAULT '0' COMMENT '商品分享标签格式（0:商品售价+商品名称   1：商品名称）',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `operator_name` varchar(50) DEFAULT NULL COMMENT '操作人名字',
  `style` int(11) DEFAULT NULL COMMENT '商品海报样式 1:样式1 2:样式2 3:样式3',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='商品设置';


-- hsm_member_mall_platform_db.hsa_common_set definition

CREATE TABLE `hsa_common_set` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '主体guid',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `cart_state` tinyint(2) NOT NULL COMMENT '购物车状态0关闭1开启',
  `float_img` varchar(255) DEFAULT NULL COMMENT '浮窗图标',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;


-- hsm_member_mall_platform_db.hsa_delivery_set definition

CREATE TABLE `hsa_delivery_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `oper_subject_guid` varchar(50) DEFAULT NULL COMMENT '运营主体guid',
  `is_express_delivery` tinyint(4) DEFAULT '0' COMMENT '是否开启快递发货功能（0：未开启，1：开启）',
  `is_select_all` tinyint(4) DEFAULT '0' COMMENT '是否选择全部的物流公司（0：不全选，1：全选）',
  `logistics` varchar(255) DEFAULT NULL COMMENT '添加的物流公司（json格式存储）',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `operator_name` varchar(50) DEFAULT NULL COMMENT '操作人名字',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_guid` (`guid`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;


-- hsm_member_mall_platform_db.hsa_distribution_set definition

CREATE TABLE `hsa_distribution_set` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `linkman` varchar(20) DEFAULT NULL COMMENT '联系人',
  `linkman_phone` varchar(20) DEFAULT NULL COMMENT '联系人',
  `area_json` varchar(256) DEFAULT NULL COMMENT '省市区json',
  `detail_address` varchar(128) DEFAULT NULL COMMENT '详细地址',
  `address_type` varchar(128) NOT NULL DEFAULT '0' COMMENT '0:发货地址 1:发货地址是否是默认地址 2:收货地址 3:收货地址是否是默认地址',
  `oper_subject_guid` varchar(50) DEFAULT NULL COMMENT '运营主体',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP,
  `send_goods_choose` tinyint(1) DEFAULT NULL,
  `send_goods_default` tinyint(1) DEFAULT NULL,
  `receive_goods_choose` tinyint(1) DEFAULT NULL,
  `receive_goods_default` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;


-- hsm_member_mall_platform_db.hsa_logistics_charge definition

CREATE TABLE `hsa_logistics_charge` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) NOT NULL COMMENT '明细guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除,0未删除,1已删除',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',
  `template_guid` varchar(50) NOT NULL COMMENT '关联运费模板guid',
  `region` text COMMENT '配送区域 省-市-区',
  `weight` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '重量 多少KG/件',
  `price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '价格',
  `second_weight` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '续重 多少KG/件',
  `second_price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '续重价格',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`) USING BTREE,
  KEY `oper_subject_template_guid_idx` (`oper_subject_guid`,`template_guid`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='运费模板计费明细';


-- hsm_member_mall_platform_db.hsa_logistics_template definition

CREATE TABLE `hsa_logistics_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) NOT NULL COMMENT '模板guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除,0未删除,1已删除',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',
  `template_name` varchar(25) NOT NULL COMMENT '模板名称',
  `default_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认 1是 0否',
  `charge_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '计费方式 1-统一运费 2-商家包邮 3-按件数 4-按重量',
  `freight_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '运费',
  `charges` json DEFAULT NULL COMMENT '计费明细字符串',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`) USING BTREE,
  KEY `oper_subject_guid_idx` (`oper_subject_guid`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='运费模板';

-- hsm_member_mall_platform_db.hsa_mall_base_order definition

CREATE TABLE `hsa_mall_base_order` (
  `id` int(20) NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) NOT NULL,
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `enterprise_guid` varchar(50) NOT NULL DEFAULT '' COMMENT '企业guid',
  `oper_subject_guid` varchar(50) DEFAULT NULL COMMENT '运营主体guid',
  `member_info_guid` varchar(50) NOT NULL COMMENT '会员GUID',
  `member_phone` varchar(50) DEFAULT NULL COMMENT '会员手机号',
  `member_name` varchar(50) DEFAULT '1' COMMENT '会员昵称',
  `order_number` varchar(50) NOT NULL DEFAULT '2' COMMENT '订单编号',
  `order_condition` tinyint(4) DEFAULT NULL COMMENT '订单状态',
  `delivery_method` tinyint(4) DEFAULT NULL COMMENT '订单配送方式',
  `pay_method` tinyint(4) DEFAULT NULL COMMENT '支付方式',
  `freight_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '运费',
  `order_process_json` varchar(255) DEFAULT '0' COMMENT '订单流程',
  `order_discount_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '订单优惠金额，无优惠为0',
  `order_paid_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '订单实付金额',
  `store_guid` varchar(50) DEFAULT NULL,
  `store_name` varchar(50) DEFAULT NULL,
  `stall_guid` varchar(50) DEFAULT NULL,
  `stall_name` varchar(50) DEFAULT '0',
  `receiver_guid` varchar(50) DEFAULT NULL COMMENT '收货人',
  `logistics_name` varchar(50) DEFAULT NULL COMMENT '发货物流公司',
  `logistics_number` varchar(128) DEFAULT NULL COMMENT '发货物流单号',
  `sender_time` datetime DEFAULT NULL COMMENT '发货时间',
  `sender_name` varchar(50) DEFAULT NULL COMMENT '发货人',
  `sender_address` varchar(200) DEFAULT NULL COMMENT '发货地址',
  `sender_phone` varchar(50) DEFAULT NULL COMMENT '发货手机号',
  `order_after_condition` tinyint(4) DEFAULT NULL COMMENT '售后状态',
  `order_external_number` varchar(50) DEFAULT NULL COMMENT '外部订单编号',
  `order_source` tinyint(4) DEFAULT NULL COMMENT '来源，0微信，2 一体机,1POS',
  `operator_tel_name` varchar(50) DEFAULT NULL COMMENT '操作人员账号',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `order_payment_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '订单金额',
  `sender_guid` varchar(50) DEFAULT NULL COMMENT '发货人guid',
  `payment_time` datetime DEFAULT NULL COMMENT '付款时间',
  `pay_condition` tinyint(4) NOT NULL COMMENT '支付状态',
  `cancel_reason` varchar(500) DEFAULT NULL COMMENT '订单取消原因',
  `cancel_time` datetime DEFAULT NULL,
  `cancel_type` tinyint(4) DEFAULT NULL COMMENT '取消类型',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;


-- hsm_member_mall_platform_db.hsa_mall_order_time_rule definition

CREATE TABLE `hsa_mall_order_time_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) DEFAULT NULL,
  `oper_subject_guid` varchar(50) DEFAULT NULL,
  `pay_time_value` int(11) DEFAULT NULL,
  `take_time_value` int(11) DEFAULT NULL,
  `automatic_refund` tinyint(1) DEFAULT NULL,
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `automatic_time_value` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;

-- hsm_member_mall_platform_db.hsa_oper_subject_permission definition

CREATE TABLE `hsa_oper_subject_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `enterprise_guid` varchar(50) NOT NULL DEFAULT '' COMMENT '企业guid',
  `position_guid` varchar(50) NOT NULL COMMENT '岗位id和角色id',
  `oper_subject_guid` varchar(50) DEFAULT NULL COMMENT '运营主体guid',
  `source_type` tinyint(4) DEFAULT NULL COMMENT '1：会员管理   2：营销中心 4:会员商城',
  `multi_member_name` varchar(50) DEFAULT NULL COMMENT '运营主体名称',
  `multi_member_status` tinyint(4) DEFAULT NULL COMMENT '运营主体状态（0:启用 １未启用）',
  `is_checked` tinyint(4) DEFAULT NULL COMMENT '是否选择（1：选中，0未选中）',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_role` tinyint(4) DEFAULT NULL COMMENT '权限类型（0：岗位  1：角色）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='运营主体权限表';


-- hsm_member_mall_platform_db.hsa_oper_subject_permission_type definition

CREATE TABLE `hsa_oper_subject_permission_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `enterprise_guid` varchar(50) NOT NULL DEFAULT '' COMMENT '企业guid',
  `position_guid` varchar(50) NOT NULL COMMENT '岗位id和角色id',
  `source_type` tinyint(4) DEFAULT NULL COMMENT '1：会员管理   2：营销中心 4：会员商城',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_role` tinyint(4) DEFAULT NULL COMMENT '权限类型（0：岗位  1：角色）',
  `is_all` tinyint(4) DEFAULT NULL COMMENT '设置的类型（1：全部  0：部分）默认为全部',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='运营主体权限类型表（用于判断拥有全部权限还是部分权限）';


-- hsm_member_mall_platform_db.hsa_order_auto_config definition

CREATE TABLE `hsa_order_auto_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_guid` varchar(50) NOT NULL COMMENT '订单guid',
  `order_cancel_time` datetime DEFAULT NULL COMMENT '订单自动取消时间',
  `order_receive_time` datetime DEFAULT NULL COMMENT '订单自动收货时间',
  `order_refund_confirm_time` datetime DEFAULT NULL COMMENT '订单自动退款确认时间',
  `cancel_state` tinyint(1) DEFAULT '0' COMMENT '订单自动取消状态',
  `receive_state` tinyint(1) DEFAULT '0' COMMENT '订单自动收货状态',
  `refund_confirm_state` tinyint(1) DEFAULT '0' COMMENT '订单自动退款确认状态',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `index_order_guid` (`order_guid`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;


-- hsm_member_mall_platform_db.hsa_order_receiver_address definition

CREATE TABLE `hsa_order_receiver_address` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `oper_subject_guid` varchar(50) DEFAULT NULL COMMENT '运营主体guid',
  `member_guid` varchar(50) DEFAULT NULL COMMENT '会员guid',
  `receiver_type` tinyint(4) DEFAULT NULL COMMENT '地址类型 0:固定地址 1:动态地址',
  `receiver_name` varchar(50) DEFAULT NULL COMMENT '收货人名称',
  `receiver_sex` tinyint(4) DEFAULT NULL COMMENT '收货人性别 0:未知 1:男 2:女',
  `receiver_phone` varchar(50) DEFAULT NULL COMMENT '收货人手机号',
  `receiver_address` varchar(512) DEFAULT NULL COMMENT '收货人地址',
  `province` varchar(50) DEFAULT NULL COMMENT '省',
  `city` varchar(50) DEFAULT NULL COMMENT '市',
  `area` varchar(50) DEFAULT NULL COMMENT '区',
  `house_number` varchar(50) DEFAULT NULL COMMENT '门牌号',
  `address_label` varchar(100) DEFAULT NULL COMMENT '地址标签',
  `default_address` tinyint(1) DEFAULT NULL COMMENT '是否是默认地址 0:不是 1:是',
  `receiver_json` varchar(1000) DEFAULT NULL COMMENT '固定地址json',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `code` varchar(100) DEFAULT NULL,
  `region` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;


-- hsm_member_mall_platform_db.hsa_product_order_detail definition

CREATE TABLE `hsa_product_order_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `order_guid` varchar(50) NOT NULL DEFAULT '0' COMMENT '订单guid',
  `product_id` varchar(50) NOT NULL DEFAULT '0' COMMENT '商品id',
  `product_name` varchar(50) DEFAULT NULL COMMENT '商品名称',
  `product_detail` varchar(255) DEFAULT NULL COMMENT '商品详细',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `product_unit_price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '商品单价',
  `product_discount_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '商品优惠金额',
  `product_paid_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '商品交易额',
  `product_num` int(11) DEFAULT NULL COMMENT '商品数量',
  `product_number` varchar(50) DEFAULT NULL COMMENT '编码',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `product_img` text COMMENT '商品图片',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;


-- hsm_member_mall_platform_db.hsa_product_store_rule definition

CREATE TABLE `hsa_product_store_rule` (
  `id` int(20) NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) NOT NULL,
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `store_guid` varchar(50) DEFAULT '' COMMENT 'storeGuid/stallGuid',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `store_name` varchar(50) DEFAULT NULL COMMENT 'storeName',
  `product_guid` varchar(50) DEFAULT NULL COMMENT 'productGuid',
  `parent_guid` varchar(50) DEFAULT NULL COMMENT '父guid',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;


-- hsm_member_mall_platform_db.hsa_refund_negotiation_history definition

CREATE TABLE `hsa_refund_negotiation_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `after_sale_order_guid` varchar(50) NOT NULL COMMENT '售后订单guid',
  `state` tinyint(4) NOT NULL COMMENT '协商状态(0申请、1商家主动退款、2会员取消退款申请、3通过申请、4拒绝申请)',
  `role_type` tinyint(4) NOT NULL COMMENT '类型0：买家1：商家',
  `reason` varchar(500) DEFAULT NULL COMMENT '原因',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `index_after_order_guid` (`after_sale_order_guid`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;


-- hsm_member_mall_platform_db.hsa_transaction_record definition

CREATE TABLE `hsa_transaction_record` (
  `guid` bigint(20) unsigned NOT NULL COMMENT '全局唯一主键',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0：false,1:true',
  `order_guid` varchar(50) NOT NULL COMMENT '订单guid',
  `oper_subject_guid` varchar(50) DEFAULT NULL COMMENT '运营主体GUID',
  `order_holder_no` varchar(50) DEFAULT NULL COMMENT '聚合支付订单号',
  `bank_transaction_id` varchar(50) DEFAULT '' COMMENT '银行流水号',
  `amount` decimal(15,2) DEFAULT '0.00' COMMENT '交易金额',
  `discount_fee` decimal(15,2) DEFAULT '0.00' COMMENT '聚合支付优惠金额',
  `refundable_fee` decimal(15,2) DEFAULT '0.00' COMMENT '剩余可退款金额',
  `state` int(11) DEFAULT NULL COMMENT '0：待支付 1：支付中 2：支付成功 3：支付失败 ',
  `remark` varchar(128) DEFAULT NULL COMMENT '备注',
  `store_name` varchar(50) DEFAULT NULL COMMENT '门店名',
  `store_guid` varchar(50) DEFAULT NULL COMMENT '门店guid',
  `refund_time` datetime DEFAULT NULL COMMENT '退款时间',
  `app_id` varchar(128) DEFAULT NULL COMMENT '聚合支付账号',
  `app_secret` varchar(128) DEFAULT NULL COMMENT '聚合支付账号密钥',
  `enterprise_guid` varchar(50) DEFAULT NULL COMMENT '企业guid',
  PRIMARY KEY (`guid`),
  KEY `idx_order_guid` (`order_guid`),
  KEY `idx_store_guid` (`store_guid`),
  KEY `idx_order_holder_no` (`order_holder_no`),
  KEY `idx_gmt` (`gmt_create`) USING BTREE,
  KEY `htr_gmt_modified_idx` (`gmt_modified`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单交易记录';


-- 初始化数据

-- 主体下运费模板
INSERT INTO `hsm_member_mall_platform_db`.`hsa_logistics_template` ( `guid`, `oper_subject_guid`, `template_name`, `default_flag`, `charge_type`,
`freight_amount`, `charges` )
SELECT guid, oper_subject_guid, '默认模板',1,2,0,NULL FROM `hsm_member_marketing_platform_db`.`hsa_oper_subject_permission`
GROUP BY oper_subject_guid ORDER BY oper_subject_guid;

-- 主体下商品配送基础数据
INSERT INTO `hsm_member_mall_platform_db`.`hsa_delivery_set` ( `guid`, `oper_subject_guid`, `operator_name` )
SELECT guid, oper_subject_guid, '管理员' FROM `hsm_member_marketing_platform_db`.`hsa_oper_subject_permission`
GROUP BY oper_subject_guid ORDER BY oper_subject_guid;