-- 指定企业增加系统（每个企业都要加）

-- 企业id、应用id
-- 128：会员管理，141营销中心，157权益中心，172会员商城，173小程序装修，169老板助手

insert into gateway.hp_company_system (company_id,system_id) values (3659,172),(3659,173);-- 勐海 3659
insert into gateway.hp_company_system (company_id,system_id) values (3701,172),(3701,173);-- 普洱学院 3701
insert into gateway.hp_company_system (company_id,system_id) values (3832,172),(3832,173);-- 东兴北投 3832
insert into gateway.hp_company_system (company_id,system_id) values (3768,172),(3768,173);-- 辽河宾馆 3768
insert into gateway.hp_company_system (company_id,system_id) values (4048,172),(4048,173);-- 酒泉市二医院食堂 4048
insert into gateway.hp_company_system (company_id,system_id) values (4032,172),(4032,173);-- 成都云帆未来科技有限公司 4032

insert into gateway.hp_company_system (company_id,system_id) values (3837,172),(3837,173);-- 长江宜宾航道局 3837

-- 应用
INSERT INTO team.app_info
(name, relation_id, `type`, description, logo, is_third)
VALUES('会员商城', (select id from gateway.hp_system hs where identification = 'member_mall' limit 1), 1, '', '', 1)
,('小程序基础配置', (select id from gateway.hp_system hs where identification = 'member_tool' limit 1), 1, '', '', 1);

-- 应用在分类下：6 市场销售
INSERT INTO team.app_info_relation_type (app_info_id, app_type_id)
select id,6 from team.app_info where name in ('会员商城','小程序基础配置');

-- todo 最后：调用holder接口，增加字段弹窗权限 http://gateway.hp.goalgo.cn:42770/team/permission/dataPermissionType/add
-- todo 调用holder接口，刷新redis权限缓存


insert into hp_company_system (company_id,system_id) values (3768,128),(3768,141),(3768,157);