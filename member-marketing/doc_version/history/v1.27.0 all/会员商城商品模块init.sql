CREATE DATABASE `hsm_member_commodity_db` CHARACTER SET 'utf8' COLLATE 'utf8_general_ci';

use hsm_member_commodity_db;

CREATE TABLE `hsa_strategy` (
  `id` int(11) NOT NULL COMMENT '主键',
  `strategy_name` varchar(50) DEFAULT NULL COMMENT '名称',
  `strategy_code` varchar(50) DEFAULT NULL COMMENT '策略单编号',
  `sale_time` varchar(50) DEFAULT NULL COMMENT '售卖时段',
  `status` tinyint(1) DEFAULT NULL COMMENT '状态（已上架、未上架）',
  `channel` char(10) DEFAULT NULL COMMENT '所属渠道',
  `store_id` int(11) DEFAULT NULL COMMENT '门店id',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `store_id_idx` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='策略单';

DROP TABLE IF EXISTS `hsa_category`;
CREATE TABLE `hsa_category` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `category_name` varchar(50) DEFAULT NULL COMMENT '名称',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父分类',
  `strategy_id` bigint(20) DEFAULT NULL COMMENT '策略单id',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `strategy_id_idx` (`strategy_id`) USING BTREE,
  KEY `parent_id_idx` (`parent_id`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 ROW_FORMAT = DYNAMIC COMMENT = '分类';

DROP TABLE IF EXISTS `hsa_tag`;
CREATE TABLE `hsa_tag` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `tag_name` varchar(50) DEFAULT NULL COMMENT '名称',
  `color` varchar(50) DEFAULT NULL COMMENT '颜色',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(1) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 ROW_FORMAT = DYNAMIC COMMENT = '标签';


DROP TABLE IF EXISTS `hsa_commodity`;
CREATE TABLE `hsa_commodity` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `commodity_code` varchar(50) DEFAULT NULL COMMENT '商品编码',
  `commodity_name` varchar(50) DEFAULT NULL COMMENT '商品名称',
  `commodity_title` varchar(100) DEFAULT NULL COMMENT '商品标题、（售卖名称）',
  `commodity_img` varchar(5000) DEFAULT NULL COMMENT '商品图片（多张）第一张',
  `sequence` int(11) DEFAULT NULL COMMENT '排序值 取all分类下的',
  `store_state` int(11) DEFAULT NULL COMMENT '商品上架状态（告罄，未上架，已上架，已失效）',
  `state` int(11) DEFAULT NULL COMMENT '商品定价状态（未定价，已定价，已失效）',
  `group_type` int(11) DEFAULT NULL COMMENT '组合类型 1：单品 2：固定套餐 3 ：可选套餐',
  `grounding_date` datetime DEFAULT NULL COMMENT '上架时间',
  `sale_time` datetime DEFAULT NULL COMMENT '开售时间',
  `tag_id` bigint(20) DEFAULT NULL COMMENT '商品标签id',
  `category_id` bigint(20) DEFAULT NULL COMMENT '商品分类id',
  `strategy_id` bigint(20) DEFAULT NULL COMMENT '策略单id',
  `store_id` bigint(20) DEFAULT NULL COMMENT '门店id',
  `base_price` varchar(50) DEFAULT NULL COMMENT '售价',
  `underlined_price` varchar(50) DEFAULT NULL COMMENT '商品划线价',
  `equity_price` json DEFAULT NULL COMMENT '会员价(json字符串）',
  `starting_number` int(11) DEFAULT NULL COMMENT '起卖数',
  `virtual_sales` int(11) DEFAULT NULL COMMENT '虚拟销量',
  `total_sold_count` int(11) DEFAULT NULL COMMENT '实际销量',
  `sell_count` int(11) DEFAULT NULL COMMENT '库存',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `commodity_code_idx` (`commodity_code`) USING BTREE,
  KEY `tag_id_idx` (`tag_id`) USING BTREE,
  KEY `category_id_idx` (`category_id`) USING BTREE,
  KEY `strategy_id_idx` (`strategy_id`) USING BTREE,
  KEY `store_id_idx` (`store_id`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 ROW_FORMAT = DYNAMIC COMMENT = '商品';

-- 公共表
DROP TABLE IF EXISTS `hsa_dict`;
CREATE TABLE `hsa_dict` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `field` varchar(20) NOT NULL,
  `name` varchar(50) NOT NULL,
  `options` json NOT NULL,
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` int(1) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COMMENT='数据字典表';

INSERT INTO `hsa_dict` (`id`, `field`, `name`, `options`, `gmt_create`, `gmt_modified`, `is_delete`) VALUES (1, 'state', '商品定价状态', '{\"1\": \"未定价\", \"2\": \"已定价\", \"3\": \"已失效\"}', '2023-01-13 09:42:09', '2023-01-13 09:42:12', 0);
INSERT INTO `hsa_dict` (`id`, `field`, `name`, `options`, `gmt_create`, `gmt_modified`, `is_delete`) VALUES (2, 'store_state', '商品上架状态', '{\"1\": \"告罄\", \"2\": \"未上架\", \"3\": \"已上架\", \"4\": \"已失效\"}', '2023-01-13 09:42:51', '2023-01-13 09:42:51', 0);
INSERT INTO `hsa_dict` (`id`, `field`, `name`, `options`, `gmt_create`, `gmt_modified`, `is_delete`) VALUES (3, 'status', '策略单状态', '{\"1\": \"未上架\", \"2\": \"已上架\"}', '2023-01-13 09:43:15', '2023-01-13 09:43:29', 0);


DROP TABLE IF EXISTS `hsa_table`;
CREATE TABLE `hsa_table` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `table_name` varchar(20) NOT NULL,
  `ddl_sql` text NOT NULL,
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(1) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='ddl';

INSERT INTO `hsa_table` (`id`, `table_name`, `ddl_sql`, `gmt_create`, `gmt_modified`, `is_delete`) VALUES (1, 'hsa_strategy', 'CREATE TABLE `hsa_strategy` (\n  `id` int(11) NOT NULL COMMENT \'主键\',\n  `strategy_name` varchar(50) DEFAULT NULL COMMENT \'名称\',\n  `strategy_code` varchar(50) DEFAULT NULL COMMENT \'策略单编号\',\n  `sale_time` varchar(50) DEFAULT NULL COMMENT \'售卖时段\',\n  `status` tinyint(1) DEFAULT NULL COMMENT \'状态（已上架、未上架）\',\n  `channel` char(10) DEFAULT NULL COMMENT \'所属渠道\',\n  `store_id` int(11) DEFAULT NULL COMMENT \'门店id\',\n  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT \'创建时间\',\n  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT \'修改时间\',\n  `is_delete` int(1) DEFAULT \'0\',\n  PRIMARY KEY (`id`),\n  KEY `store_id_idx` (`store_id`)\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT=\'策略单\'', '2023-02-28 16:22:00', '2023-03-09 11:23:19', 0);
INSERT INTO `hsa_table` (`id`, `table_name`, `ddl_sql`, `gmt_create`, `gmt_modified`, `is_delete`) VALUES (2, 'hsa_category', 'CREATE TABLE `hsa_category` (\n  `id` int(11) NOT NULL COMMENT \'主键\',\n  `category_name` varchar(50) DEFAULT NULL COMMENT \'名称\',\n  `parent_id` int(11) DEFAULT NULL COMMENT \'父分类\',\n  `strategy_id` int(11) DEFAULT NULL COMMENT \'策略单id\',\n  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT \'创建时间\',\n  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT \'修改时间\',\n  `is_delete` int(1) DEFAULT \'0\',\n  PRIMARY KEY (`id`),\n  KEY `strategy_id_idx` (`strategy_id`),\n  KEY `parent_id_idx` (`parent_id`)\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT=\'分类\'', '2023-02-28 16:22:22', '2023-02-28 16:29:35', 0);
INSERT INTO `hsa_table` (`id`, `table_name`, `ddl_sql`, `gmt_create`, `gmt_modified`, `is_delete`) VALUES (3, 'hsa_tag', 'CREATE TABLE `hsa_tag` (\n  `id` int(11) NOT NULL COMMENT \'主键\',\n  `tag_name` varchar(50) DEFAULT NULL COMMENT \'名称\',\n  `color` varchar(50) DEFAULT NULL COMMENT \'颜色\',\n  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT \'创建时间\',\n  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT \'修改时间\',\n  `is_delete` int(1) DEFAULT \'0\',\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT=\'标签\'', '2023-02-28 16:22:52', '2023-02-28 16:30:25', 0);
INSERT INTO `hsa_table` (`id`, `table_name`, `ddl_sql`, `gmt_create`, `gmt_modified`, `is_delete`) VALUES (4, 'hsa_commodity', 'CREATE TABLE `hsa_commodity` (\n  `id` bigint(20) NOT NULL COMMENT \'主键\',\n  `commodity_code` varchar(50) DEFAULT NULL COMMENT \'商品编码\',\n  `commodity_name` varchar(50) DEFAULT NULL COMMENT \'商品名称\',\n  `commodity_title` varchar(100) DEFAULT NULL COMMENT \'商品标题、（售卖名称）\',\n  `commodity_img` varchar(5000) DEFAULT NULL COMMENT \'商品图片（多张）第一张\',\n  `sequence` int(11) DEFAULT NULL COMMENT \'排序值 取all分类下的\',\n  `store_state` int(11) DEFAULT NULL COMMENT \'商品上架状态（告罄，未上架，已上架，已失效）\',\n  `state` int(11) DEFAULT NULL COMMENT \'商品定价状态（未定价，已定价，已失效）\',\n  `group_type` int(11) DEFAULT NULL COMMENT \'组合类型 1：单品 2：固定套餐 3 ：可选套餐\',\n  `grounding_date` datetime DEFAULT NULL COMMENT \'上架时间\',\n  `sale_time` datetime DEFAULT NULL COMMENT \'开售时间\',\n  `tag_id` bigint(20) DEFAULT NULL COMMENT \'商品标签id\',\n  `category_id` bigint(20) DEFAULT NULL COMMENT \'商品分类id\',\n  `strategy_id` bigint(20) DEFAULT NULL COMMENT \'策略单id\',\n  `store_id` bigint(20) DEFAULT NULL COMMENT \'门店id\',\n  `base_price` varchar(50) DEFAULT NULL COMMENT \'售价\',\n  `underlined_price` varchar(50) DEFAULT NULL COMMENT \'商品划线价\',\n  `equity_price` json DEFAULT NULL COMMENT \'会员价(json字符串）\',\n  `starting_number` int(11) DEFAULT NULL COMMENT \'起卖数\',\n  `virtual_sales` int(11) DEFAULT NULL COMMENT \'虚拟销量\',\n  `total_sold_count` int(11) DEFAULT NULL COMMENT \'实际销量\',\n  `sell_count` int(11) DEFAULT NULL COMMENT \'库存\',\n  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT \'创建时间\',\n  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT \'修改时间\',\n  `is_delete` int(1) DEFAULT \'0\',\n  PRIMARY KEY (`id`),\n  KEY `commodity_code_idx` (`commodity_code`) USING BTREE,\n  KEY `tag_id_idx` (`tag_id`) USING BTREE,\n  KEY `category_id_idx` (`category_id`) USING BTREE,\n  KEY `strategy_id_idx` (`strategy_id`) USING BTREE,\n  KEY `store_id_idx` (`store_id`) USING BTREE\n) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 ROW_FORMAT = DYNAMIC COMMENT = \'商品\';', '2023-02-28 16:23:09', '2023-03-09 15:13:45', 0);


DROP TABLE IF EXISTS `hsa_logistics_commodity`;
CREATE TABLE `hsa_logistics_commodity` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) NOT NULL COMMENT '关联商品guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',
  `template_guid` varchar(50) NOT NULL COMMENT '关联运费模板guid',
  `commodity_code` varchar(50) NOT NULL COMMENT '商品编码',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE,
  UNIQUE INDEX `uk_oper_subject_commodity_code`(`oper_subject_guid`,`commodity_code`) USING BTREE,
  INDEX `template_guid_idx`(`template_guid`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 ROW_FORMAT = DYNAMIC COMMENT = '运费模板关联商品';

