-- holder数据库
use gateway;

-- 业务系统
INSERT INTO hp_system
(address, create_time, description, identification, login_type, name, single, status, support_type, update_time, version, gmt_create, gmt_modified, display)
VALUES('https://member-tool.holderzone.com/#/', CURRENT_TIMESTAMP, null, 'member_tool', null, '小程序基础配置',null, 0, null, CURRENT_TIMESTAMP,NULL, CURRENT_TIMESTAMP,
CURRENT_TIMESTAMP, 1);

-- 功能模块
INSERT INTO hp_function_group
(create_time, name, `sequence`, update_time, gmt_create, gmt_modified, group_identification)
VALUES(CURRENT_TIMESTAMP, '小程序装修', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, null);

-- 权限类型
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '小程序', 1, 60, CURRENT_TIMESTAMP,26,
( SELECT id from hp_system WHERE name = '小程序基础配置' AND identification = 'member_tool' ORDER BY gmt_modified DESC LIMIT 1 ),
(select id from hp_function_group where name = '小程序装修' order by gmt_modified desc limit 1),
CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');

INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '微页面', 2, 60, CURRENT_TIMESTAMP,26,
( SELECT id from hp_system WHERE name = '小程序基础配置' AND identification = 'member_tool' ORDER BY gmt_modified DESC LIMIT 1 ),
(select id from hp_function_group where name = '小程序装修' order by gmt_modified desc limit 1),
CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');

INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '店铺主页', 3, 60, CURRENT_TIMESTAMP,26,
( SELECT id from hp_system WHERE name = '小程序基础配置' AND identification = 'member_tool' ORDER BY gmt_modified DESC LIMIT 1 ),
(select id from hp_function_group where name = '小程序装修' order by gmt_modified desc limit 1),
CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');

INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '主题风格', 4, 60, CURRENT_TIMESTAMP,26,
( SELECT id from hp_system WHERE name = '小程序基础配置' AND identification = 'member_tool' ORDER BY gmt_modified DESC LIMIT 1 ),
(select id from hp_function_group where name = '小程序装修' order by gmt_modified desc limit 1),
CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');

INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '小程序导航', 5, 60, CURRENT_TIMESTAMP,26,
( SELECT id from hp_system WHERE name = '小程序基础配置' AND identification = 'member_tool' ORDER BY gmt_modified DESC LIMIT 1 ),
(select id from hp_function_group where name = '小程序装修' order by gmt_modified desc limit 1),
CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');

-- 权限
INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '小程序管理范围', NOW(),
(select id from hp_function where name = '小程序' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '查看', NOW(),
(select id from hp_function where name = '微页面' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '新建微页面', NOW(),
(select id from hp_function where name = '微页面' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '设为小程序首页', NOW(),
(select id from hp_function where name = '微页面' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '批量删除', NOW(),
(select id from hp_function where name = '微页面' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '推广', NOW(),
(select id from hp_function where name = '微页面' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '发布', NOW(),
(select id from hp_function where name = '微页面' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '取消发布', NOW(),
(select id from hp_function where name = '微页面' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '编辑', NOW(),
(select id from hp_function where name = '微页面' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '复制', NOW(),
(select id from hp_function where name = '微页面' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '删除', NOW(),
(select id from hp_function where name = '微页面' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '查看', NOW(),
(select id from hp_function where name = '店铺主页' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '新建主页模板', NOW(),
(select id from hp_function where name = '店铺主页' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '编辑', NOW(),
(select id from hp_function where name = '店铺主页' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '应用店铺', NOW(),
(select id from hp_function where name = '店铺主页' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '复制', NOW(),
(select id from hp_function where name = '店铺主页' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '删除', NOW(),
(select id from hp_function where name = '店铺主页' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '查看', NOW(),
(select id from hp_function where name = '主题风格' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '编辑', NOW(),
(select id from hp_function where name = '主题风格' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '查看顶部导航', NOW(),
(select id from hp_function where name = '小程序导航' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '编辑顶部导航', NOW(),
(select id from hp_function where name = '小程序导航' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '查看底部导航', NOW(),
(select id from hp_function where name = '小程序导航' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '编辑底部导航', NOW(),
(select id from hp_function where name = '小程序导航' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());






