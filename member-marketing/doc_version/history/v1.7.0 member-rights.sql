
use hsm_member_marketing_platform_db;


CREATE TABLE `hsa_equities_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `guid` varchar(50) DEFAULT NULL COMMENT '权益guid',
  `enterprise_guid` varchar(20) NOT NULL COMMENT '企业guid',
  `equities_name` varchar(30) DEFAULT NULL COMMENT '权益名称',
  `equities_img` varchar(255) DEFAULT NULL COMMENT '权益图片',
  `equities_explain` text COMMENT '权益描述',
  `equities_type` tinyint(4) DEFAULT NULL COMMENT '权益适用类型(1:全部运营主体  2:部分运营主体)',
  `oper_subject_guid_list` varchar(1000) DEFAULT NULL COMMENT '权益适用的运营主体，存储JSON字符串',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启 0：正常  1：禁用 2：草稿',
  `type` tinyint(4) NOT NULL COMMENT '权益类型 0:折扣权益 1:成长值权益 2:线下权益 3:积分权益 4:新品优先权 5:消费项权益',
  `is_delete` tinyint(1) NOT NULL COMMENT '是否删除,0未删除,1已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=66 DEFAULT CHARSET=utf8mb4;


CREATE TABLE `hsa_equities_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `type` tinyint(4) NOT NULL COMMENT '类型 0：商品折扣 1：满减 2：直减 3：会员价 4：赠送成长值 5：翻倍成长值',
  `goods_discount_set` tinyint(1) DEFAULT NULL COMMENT '商品折扣 0：未设置 1：已设置',
  `share_and_mutex` varchar(128) DEFAULT NULL COMMENT '0:同享 1：互斥 多个用逗号隔开',
  `equities_time_set` tinyint(1) DEFAULT NULL COMMENT '权益时段 0：未设置 1：已设置',
  `equities_time_limited_type` varchar(128) DEFAULT NULL COMMENT '权益时段限制类型 0：不限制 1：限制时段 多个用逗号隔开',
  `limited_time_type` varchar(128) DEFAULT NULL COMMENT '限制时段类型 1：日 2：周 3：月 4：年',
  `apply_store_set` tinyint(1) DEFAULT NULL COMMENT '适用门店设置 0：未设置 1：已设置',
  `apply_store_type` varchar(128) DEFAULT NULL COMMENT '适用门店设置类型 0：全部门店 1：选择部分门店 多个用逗号隔开',
  `apply_business_set` tinyint(1) DEFAULT NULL COMMENT '适用业务 0：未设置 1：已设置',
  `apply_business_type` varchar(128) DEFAULT NULL COMMENT '适用业务类型 0：全部业务 1：部分业务 多个用逗号隔开',
  `business_type` varchar(128) DEFAULT NULL COMMENT '业务类型 0：食堂预定 3：快速收款 多个用逗号隔开',
  `apply_terminal_set` tinyint(1) DEFAULT NULL COMMENT '适用终端 0：未设置 1：已设置',
  `apply_terminal_type` varchar(128) DEFAULT NULL COMMENT '适用终端类型 0：全部终端 1：部分终端 多个用逗号隔开',
  `terminal_type` varchar(128) DEFAULT NULL COMMENT '部分终端类型 53：小程序 2：一体机 多个用逗号隔开',
  `apply_commodity_set` tinyint(1) DEFAULT NULL COMMENT '适用商品 0：未设置 1：已设置',
  `apply_commodity_type` varchar(128) DEFAULT NULL COMMENT '0：适用商品 1：不适用商品 多个用逗号隔开',
  `apply_commodity` varchar(128) DEFAULT NULL COMMENT '0：全部适用商品 1：部分适用商品 多个用逗号隔开',
  `no_apply_commodity` varchar(128) DEFAULT NULL COMMENT '0：全部商品不适用 1：部分商品不适用 多个用逗号隔开',
  `enough_reduce_set` tinyint(1) DEFAULT NULL COMMENT '满减 0：未设置 1：已设置',
  `multiple_enough_reduce` tinyint(1) DEFAULT NULL COMMENT '是否可设置多个满减 0：不能 1：能',
  `amount_satisfy_scope_set` tinyint(1) DEFAULT NULL COMMENT '金额满足计算范围 0：未设置 1：已设置',
  `amount_satisfy_scope_type` varchar(128) DEFAULT NULL COMMENT '金额满足计算范围类型 0：全部计算 1：部分计算设置 多个用逗号隔开',
  `part_valuation_type` varchar(128) DEFAULT NULL COMMENT '部分计价类型 0：商品价格 1：桌台费 2：配送费 多个用逗号隔开',
  `direct_reduce_set` tinyint(1) DEFAULT NULL COMMENT '直减 0：未设置 1：已设置',
  `member_discount_set` tinyint(1) DEFAULT NULL COMMENT '会员折扣 0：未设置 1：已设置',
  `single_discounts_limited_set` tinyint(1) DEFAULT NULL COMMENT '单次优惠限制 0：未设置 1：已设置',
  `single_discounts_limited_type` varchar(128) DEFAULT NULL COMMENT '单次优惠限制类型 0：不限制 1：限制  多个用逗号隔开',
  `total_most_limited_type_set` tinyint(1) DEFAULT NULL COMMENT '累计最高优惠限制 0：未设置 1：已设置',
  `total_most_limited_type` varchar(128) DEFAULT NULL COMMENT '0：不限 1：优惠金额限制  多个用逗号隔开',
  `most_limited_type` varchar(128) DEFAULT NULL COMMENT '优惠金额限制 1：日 2：周 3：月 4：年 多个用逗号隔开',
  `single_give_number_set` tinyint(1) DEFAULT NULL COMMENT '单次赠送成长值数量 0：未设置 1：已设置',
  `give_way_set` tinyint(1) DEFAULT NULL COMMENT '赠送成长值方式 0：未设置 1：已设置',
  `give_way_type` varchar(128) DEFAULT NULL COMMENT '赠送方式类型 0：一次性 1：周期性  多个用逗号隔开',
  `period_type` varchar(128) DEFAULT NULL COMMENT '赠送成长值周期类型 1：日 2：周 3：月 4：年 多个用逗号隔开',
  `total_give_set` tinyint(1) DEFAULT NULL COMMENT '累计赠送 0：未设置 1：已设置',
  `total_give_type` varchar(128) DEFAULT NULL COMMENT '累计赠送类型 0：不限 1：限制 多个用逗号隔开',
  `double_multiple_set` tinyint(1) DEFAULT NULL COMMENT '翻倍倍数 0：未设置 1：已设置',
  `single_double_limited_set` tinyint(1) DEFAULT NULL COMMENT '单次翻倍获取成长值上限 0：未设置 1：已设置',
  `single_double_limited_type` varchar(128) DEFAULT NULL COMMENT '单次翻倍获取成长值上限类型 0：不限 1：限制 多个用逗号隔开',
  `double_count_set` tinyint(1) DEFAULT NULL COMMENT '限制次数 0：未设置 1：已设置',
  `double_count_type` varchar(128) DEFAULT NULL COMMENT '翻倍次数限制类型 0：不限次数 1：设置可翻倍次数 多个用逗号隔开',
  `total_double_limited_set` tinyint(1) DEFAULT NULL COMMENT '累计翻倍获取成长值上限 0：未设置 1：已设置',
  `total_double_limited_type` varchar(128) DEFAULT NULL COMMENT '累计翻倍获取成长值上限类型 0：不限制 1：设置上限值 多个用逗号隔开',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `equities_info_guid` varchar(50) NOT NULL COMMENT '权益信息guid',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=93 DEFAULT CHARSET=utf8mb4


