-- use hsm_member_marketing_center_platform_db;

-- 投票活动
DROP TABLE IF EXISTS `hsa_voting_activity`;
CREATE TABLE `hsa_voting_activity` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
 `guid` varchar(50) NOT NULL COMMENT 'guid',
 `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
 `activity_name` varchar(50) NOT NULL COMMENT '活动名称',
 `activity_start_time` datetime NOT NULL COMMENT '活动开始时间',
 `activity_end_time` datetime NOT NULL COMMENT '活动结束时间',
 `is_enable` tinyint(1) NOT NULL DEFAULT 1 COMMENT '0-禁用 1-启用',
 `remark` varchar(250) DEFAULT NULL COMMENT '活动备注',
 `is_open_record` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否公开投票记录 1: 0:未开启 1：开启',
 `rule_type` tinyint(1) DEFAULT 0 COMMENT '投票规则 0：区分类别 1：不区分类别',
 `join_number` int(11) NOT NULL DEFAULT 0 COMMENT '参与人数',
 `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
 `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0-否 1-是',
 PRIMARY KEY (`id`),
 UNIQUE KEY `uk_guid` (`guid`),
 KEY `oper_subject_guid_idx` (`oper_subject_guid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_icelandic_ci COMMENT='投票活动';

-- 投票活动类别
DROP TABLE IF EXISTS `hsa_voting_activity_category`;
CREATE TABLE `hsa_voting_activity_category` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
 `guid` varchar(50) NOT NULL COMMENT 'guid',
 `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
 `activity_guid` varchar(50) NOT NULL COMMENT '活动guid',
 `category_name` varchar(25) DEFAULT NULL COMMENT '类别名称',
 `is_multi_choice` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否支持多选 1-是 0-否',
 `choice_number` int(11) NOT NULL DEFAULT 1 COMMENT '多选数量',
 `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
 `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 0-否 1-是',
 PRIMARY KEY (`id`),
 UNIQUE KEY `uk_guid` (`guid`),
 KEY `activity_idx` (`oper_subject_guid`,`activity_guid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_icelandic_ci COMMENT='投票活动类别';

-- 投票活动选项
DROP TABLE IF EXISTS `hsa_voting_activity_options`;
CREATE TABLE `hsa_voting_activity_options` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
 `guid` varchar(50)  NOT NULL COMMENT 'guid',
 `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
 `activity_guid` varchar(50) NOT NULL COMMENT '活动guid',
 `category_guid` varchar(50) NOT NULL COMMENT '分类guid',
 `options_name` varchar(25) DEFAULT NULL COMMENT '选项名称',
 `options_img` varchar(255) DEFAULT NULL COMMENT '选项图片',
 `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
 `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 0-否 1-是',
 PRIMARY KEY (`id`),
 UNIQUE KEY `uk_guid` (`guid`),
 KEY `activity_idx` (`oper_subject_guid`,`activity_guid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_icelandic_ci COMMENT='投票活动选项';

-- 投票记录
DROP TABLE IF EXISTS `hsa_voting_activity_record`;
CREATE TABLE `hsa_voting_activity_record` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
 `guid` varchar(50) NOT NULL COMMENT 'guid',
 `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
 `activity_guid` varchar(50) NOT NULL COMMENT '活动guid',
 `category_guid` varchar(50) NOT NULL COMMENT '分类guid',
 `options_guid` varchar(50) NOT NULL COMMENT '选项guid',
 `options_name` varchar(25) NOT NULL COMMENT '选项名称',
 `member_info_guid` varchar(50) NOT NULL COMMENT '会员guid',

 `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
 `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 0-否 1-是',
 PRIMARY KEY (`id`),
 UNIQUE KEY `uk_guid` (`guid`),
 UNIQUE KEY `uk_member` (`oper_subject_guid`,`activity_guid`, `member_info_guid`,`options_guid`) USING BTREE,
 KEY `activity_idx` (`activity_guid`,`options_guid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_icelandic_ci COMMENT='投票记录';