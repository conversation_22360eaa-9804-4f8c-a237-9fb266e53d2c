# v1.8.0配置及部署说明

## 接口
1. 最后执行接口 `/base/ter-card/updateData` 处理脏数据、初始化会员等级（默认等级）
(hsa_member_consumption.member_info_card_guid != hsa_member_info_card.guid)
   
1. 执行接口 `/base/goalgo_callback/initialize_old_subject_data` 初始化运营主体数据




## 服务

需要部署服务
```
holder-member-base 
holder-member-gateway
holder-member-queue
holder-member-marketing-web 
```

## 配置

gateway、base配置修改（只有re需要）
```yaml
goalgo:
  #host: http://gateway.hp.goalgo.cn:42770 改为 
  host: http://goalgo-re.holderzone.cn
```

queue配置
```yaml
inputMemberGradeChange:
  consumer:
    acknowledge-mode: manual


inputMemberGradeChange:
  group: memberGradeChange
  destination: memberGradeChangeExchange
  consumer:
    max-attempts: 1
    
goalgo:
  #增加
  host: http://gateway.zx.goalgo.cn:31380
```

base配置
```yaml
spring:
  servlet:
    multipart:
      max-file-size: 5MB

outputMemberGradeChange:
    destination: memberGradeChangeExchange
    contentType: application/json
```