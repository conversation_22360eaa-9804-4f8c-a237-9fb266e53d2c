-- holder数据库
use gateway;

-- 业务系统
INSERT INTO hp_system
(address, create_time, description, identification, login_type, name, single, status, support_type, update_time, version, gmt_create, gmt_modified, display)
VALUES('https://member-mall.holderzone.com/#/', CURRENT_TIMESTAMP, null, 'member_mall', null, '会员商城',null, 0, null, CURRENT_TIMESTAMP,NULL, CURRENT_TIMESTAMP,
CURRENT_TIMESTAMP, 1);

-- 功能模块
INSERT INTO hp_function_group
(create_time, name, `sequence`, update_time, gmt_create, gmt_modified, group_identification)
VALUES(CURRENT_TIMESTAMP, '运营主体', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, null);
INSERT INTO hp_function_group
(create_time, name, `sequence`, update_time, gmt_create, gmt_modified, group_identification)
VALUES(CURRENT_TIMESTAMP, '商城商品', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, null);
INSERT INTO hp_function_group
(create_time, name, `sequence`, update_time, gmt_create, gmt_modified, group_identification)
VALUES(CURRENT_TIMESTAMP, '商城装修', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, null);
INSERT INTO hp_function_group
(create_time, name, `sequence`, update_time, gmt_create, gmt_modified, group_identification)
VALUES(CURRENT_TIMESTAMP, '商城设置', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, null);
INSERT INTO hp_function_group
(create_time, name, `sequence`, update_time, gmt_create, gmt_modified, group_identification)
VALUES(CURRENT_TIMESTAMP, '商城订单', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, null);


-- 运营主题
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '运营主体', 1, 60, CURRENT_TIMESTAMP,26,
( SELECT id from hp_system WHERE name = '会员商城' AND identification = 'member_mall' ORDER BY gmt_modified DESC LIMIT 1 ),
(select id from hp_function_group where name = '运营主题' order by gmt_modified desc limit 1),
CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '运营主体管理范围', NOW(),
(select id from hp_function where name = '运营主题' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '完整手机号', NOW(),
(select id from hp_function where name = '运营主题' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


-- 商品管理
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '商品管理', 1, 60, CURRENT_TIMESTAMP,26,
( SELECT id from hp_system WHERE name = '会员商城' AND identification = 'member_mall' ORDER BY gmt_modified DESC LIMIT 1 ),
(select id from hp_function_group where name = '商城商品' order by gmt_modified desc limit 1),
CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '查看商品管理', NOW(),
(select id from hp_function where name = '商品管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


-- 商城装修
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '商城装修', 1, 60, CURRENT_TIMESTAMP,26,
( SELECT id from hp_system WHERE name = '会员商城' AND identification = 'member_mall' ORDER BY gmt_modified DESC LIMIT 1 ),
(select id from hp_function_group where name = '商城装修' order by gmt_modified desc limit 1),
CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '查看商城装修', NOW(),
(select id from hp_function where name = '商城装修' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

-- 通用设置
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '通用设置', 1, 60, CURRENT_TIMESTAMP,26,
( SELECT id from hp_system WHERE name = '会员商城' AND identification = 'member_mall' ORDER BY gmt_modified DESC LIMIT 1 ),
(select id from hp_function_group where name = '商城设置' order by gmt_modified desc limit 1),
CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '查看通用设置', NOW(),
(select id from hp_function where name = '通用设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '编辑通用设置', NOW(),
(select id from hp_function where name = '通用设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

-- 商品设置
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '商品设置', 1, 60, CURRENT_TIMESTAMP,26,
( SELECT id from hp_system WHERE name = '会员商城' AND identification = 'member_mall' ORDER BY gmt_modified DESC LIMIT 1 ),
(select id from hp_function_group where name = '商城设置' order by gmt_modified desc limit 1),
CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '查看商品设置', NOW(),
(select id from hp_function where name = '商品设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '编辑商品设置', NOW(),
(select id from hp_function where name = '商品设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


-- 配送设置
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '配送设置', 1, 60, CURRENT_TIMESTAMP,26,
( SELECT id from hp_system WHERE name = '会员商城' AND identification = 'member_mall' ORDER BY gmt_modified DESC LIMIT 1 ),
(select id from hp_function_group where name = '商城设置' order by gmt_modified desc limit 1),
CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '查看物流到家设置', NOW(),
(select id from hp_function where name = '配送设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '快递发货功能', NOW(),
(select id from hp_function where name = '配送设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '查看商品运费', NOW(),
(select id from hp_function where name = '配送设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '编辑商品运费', NOW(),
(select id from hp_function where name = '配送设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '查看快递管理', NOW(),
(select id from hp_function where name = '配送设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '编辑快递管理', NOW(),
(select id from hp_function where name = '配送设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '查看物流设置', NOW(),
(select id from hp_function where name = '配送设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '编辑物流设置', NOW(),
(select id from hp_function where name = '配送设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '查看商家地址库', NOW(),
(select id from hp_function where name = '配送设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '新增地址', NOW(),
(select id from hp_function where name = '配送设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '编辑地址', NOW(),
(select id from hp_function where name = '配送设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '删除地址', NOW(),
(select id from hp_function where name = '配送设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

-- 订单设置
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '订单设置', 1, 60, CURRENT_TIMESTAMP,26,
( SELECT id from hp_system WHERE name = '会员商城' AND identification = 'member_mall' ORDER BY gmt_modified DESC LIMIT 1 ),
(select id from hp_function_group where name = '商城设置' order by gmt_modified desc limit 1),
CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '查看时间设置', NOW(),
(select id from hp_function where name = '订单设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '编辑时间设置', NOW(),
(select id from hp_function where name = '订单设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '查看取消原因设置', NOW(),
(select id from hp_function where name = '订单设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '编辑取消原因设置', NOW(),
(select id from hp_function where name = '订单设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

-- 订单查询
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '订单查询', 1, 60, CURRENT_TIMESTAMP,26,
( SELECT id from hp_system WHERE name = '会员商城' AND identification = 'member_mall' ORDER BY gmt_modified DESC LIMIT 1 ),
(select id from hp_function_group where name = '商城订单' order by gmt_modified desc limit 1),
CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '查看订单查询', NOW(),
(select id from hp_function where name = '订单查询' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '导出订单', NOW(),
(select id from hp_function where name = '订单查询' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '发货', NOW(),
(select id from hp_function where name = '订单查询' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '取消订单', NOW(),
(select id from hp_function where name = '订单查询' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '主动退款', NOW(),
(select id from hp_function where name = '订单查询' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

-- 订单发货
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '订单发货', 1, 60, CURRENT_TIMESTAMP,26,
( SELECT id from hp_system WHERE name = '会员商城' AND identification = 'member_mall' ORDER BY gmt_modified DESC LIMIT 1 ),
(select id from hp_function_group where name = '商城订单' order by gmt_modified desc limit 1),
CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '查看订单发货（物流）', NOW(),
(select id from hp_function where name = '订单发货' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '导出订单', NOW(),
(select id from hp_function where name = '订单发货' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '发货', NOW(),
(select id from hp_function where name = '订单发货' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

-- 订单售后
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '订单售后', 1, 60, CURRENT_TIMESTAMP,26,
( SELECT id from hp_system WHERE name = '会员商城' AND identification = 'member_mall' ORDER BY gmt_modified DESC LIMIT 1 ),
(select id from hp_function_group where name = '商城订单' order by gmt_modified desc limit 1),
CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '查看订单售后', NOW(),
(select id from hp_function where name = '订单售后' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '导出订单', NOW(),
(select id from hp_function where name = '订单售后' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '同意退款', NOW(),
(select id from hp_function where name = '订单售后' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '拒绝退款', NOW(),
(select id from hp_function where name = '订单售后' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

 -- INSERT INTO hp_company_system (company_id, system_id, create_time, gmt_create, gmt_modified)
-- VALUES(2570, (select id from hp_system where name = '会员商城' order by gmt_modified desc limit 1),CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);



