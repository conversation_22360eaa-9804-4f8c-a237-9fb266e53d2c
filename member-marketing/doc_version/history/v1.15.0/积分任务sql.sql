
--积分通用规则表
CREATE TABLE `hsa_integral_general_rules`  (
       `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
       `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '成长值规则GUID',
       `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运营主体GUID',
       `integral_name` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '积分名称',
       `is_overdue_reminder` tinyint(4) NOT NULL COMMENT '积分过期是否提醒（0：默认关闭，1：开启）',
       `overdue_day_num` int(11) NULL DEFAULT NULL COMMENT '提醒天数（用于提醒多少天内将要过期的积分）',
       `remind_the_way` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提醒方式(1.短信通知2.微信公众号通知3.微信小程序通知)',
       `is_integral_protect` tinyint(4) NOT NULL COMMENT '是否设置积分保护期（0：默认关闭，1：开启）',
       `integral_protect_day_num` int(11) NULL DEFAULT NULL COMMENT '积分保护期，设置的天数',
       `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
       `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
       `operator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作人名字',
       PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 226 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


--积分额外奖励信息表
CREATE TABLE `hsa_integral_extra_award_rule`  (
      `id` bigint(10) NOT NULL AUTO_INCREMENT COMMENT '主键id',
      `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'guid',
      `integral_task_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '成长值任务guid',
      `consumption_amount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '充值、消费金额',
      `type` tinyint(4) NULL DEFAULT NULL COMMENT '0：充值 1：消费',
      `integral_value` int(11) NULL DEFAULT NULL COMMENT '成长值',
      `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
      `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
      `consumption_frequency` int(11) NULL DEFAULT NULL COMMENT '消费笔数',
      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1089 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

--积分任务表
CREATE TABLE `hsa_integral_task`  (
      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
      `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '成长值规则GUID',
      `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运营主体GUID',
      `task_number` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务编号',
      `task_type` tinyint(4) NOT NULL COMMENT '任务类型 0:基础任务 1:消费任务 2:充值任务',
      `task_action` tinyint(4) NOT NULL COMMENT '任务动作 0:注册 1:完善个人信息 2:单笔消费金额 3:消费指定商品 4:累计消费金额 5:累计消费笔数 6:单笔充值金额 7:累计充值金额',
      `task_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
      `description_type` tinyint(4) NOT NULL COMMENT '任务描述类型 0：智能生成 1：自定义',
      `description` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '自定义描述',
      `amount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '每消费多少金额赠送积分或者每充值多少赠送多少积分',
      `integral_value` int(11) NULL DEFAULT NULL COMMENT '赠送多少积分',
      `personal_details_type_json` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '个人信息类型 0：性别 1;生日 多个用逗号隔开',
      `source_type_json` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源类型 0后台添加,1POS机注册,2一体机注册,3后台导入，微网站(21微信扫码点餐，24微信注册.25微信C端后台注册)，微信小程序(51和惠多，52翼惠天下，53赚餐)',
      `apply_business_json` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用业务 0、食堂预定 1、食堂点餐 2、自助餐 3、快速收款 4、外卖 5、卡包 多个用逗号隔开',
      `get_count_type` tinyint(4) NULL DEFAULT NULL COMMENT '获取次数类型 0：不限次数 1：固定限制 2：每周期限制',
      `period_limited_type` tinyint(4) NULL DEFAULT NULL COMMENT '周期限制类型 0：天 1：周 2：月',
      `limited_number` int(11) NULL DEFAULT NULL COMMENT '积分生效次数（0表示不限制）',
      `task_validity_type` tinyint(4) NULL DEFAULT NULL COMMENT '任务有效期类型 0：永久有限 1：固定有效期',
      `start_fixed_task_validity_date` datetime(0) NULL DEFAULT NULL COMMENT '开始任务固定有效期',
      `end_fixed_task_validity_date` datetime(0) NULL DEFAULT NULL COMMENT '结束任务固定有效期',
      `integral_validity_type` tinyint(4) NULL DEFAULT NULL COMMENT '积分有效期类型 0：永久有效 1：动态有效期 2：固定有效期',
      `dynamic_validity_type` tinyint(4) NULL DEFAULT NULL COMMENT '积分动态有效期类型 0：天 1：周 2：月 3：年',
      `dynamic_validity_number` int(11) NULL DEFAULT NULL COMMENT '动态有效数量',
      `fixed_integral_validity_date` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '积分固定有效期',
      `consumption_goods_type` tinyint(4) NULL DEFAULT NULL COMMENT '消费商品类型 0：指定商品 1：指定分类商品',
      `choose_goods_type` tinyint(4) NULL DEFAULT NULL COMMENT '选择商品类型 0：消费指定商品 1：消费指定全部商品',
      `strategy_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '策略单名称',
      `applicable_all_store` tinyint(4) NULL DEFAULT NULL COMMENT '是否适用于所有门店 1:全部门店；0:部分门店',
      `buy_type` tinyint(4) NULL DEFAULT NULL COMMENT '指定商品赠送成长值类型 0：购买次数 1:购买数量 2：购买周期',
      `buy_number` int(11) NULL DEFAULT NULL COMMENT '购买数量(单位：购买次数 次 ，购买数量 件 ，购买周期 天/周)',
      `buy_period_type` tinyint(4) NULL DEFAULT NULL COMMENT '购买周期类型 0：天 1：周',
      `total_period_type` tinyint(4) NULL DEFAULT NULL COMMENT '-1：始终累计 0：按天累计 1：按周累计 2：按月累计 3：按年累计',
      `total_period` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '累计周期',
      `consumption_count` int(11) NULL DEFAULT NULL COMMENT '累计消费多少次赠送成长值',
      `consumption_ignore_amount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '单笔消费不足多少，不记录奖励规则',
      `position` int(11) NOT NULL COMMENT '成长值任务位置',
      `strategy_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '策略单id',
      `category_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类id',
      `category_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类名称',
      `is_enable` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否开启 0：停止  1：开启',
      `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 0：未删除 1：已删除',
      `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
      `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
      `operator_tel_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作人员名字/账号',
      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 164 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE `hsa_integral_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT '积分明细GUID',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',
  `member_info_guid` varchar(50) NOT NULL COMMENT '会员GUID',
  `integral_task_guid` varchar(50) DEFAULT NULL COMMENT '积分任务guid',
  `task_name` varchar(50) DEFAULT NULL COMMENT '任务名称',
  `task_action` tinyint(4) DEFAULT NULL COMMENT '任务动作 0:注册 1:完善个人信息 2:单笔消费金额 3:消费指定商品 4:累计消费金额 5:累计消费笔数 6:单笔充值金额 7:累计充值金额',
  `task_number` varchar(50) DEFAULT NULL COMMENT '任务编号',
  `record_declaration` varchar(50) DEFAULT NULL COMMENT '记录说明',
  `task_finish_time` int(11) DEFAULT NULL COMMENT '任务完成第次数',
  `integral_type` tinyint(4) DEFAULT '0' COMMENT '积分变化类型,0增加,1减少',
  `integral` int(11) DEFAULT '0' COMMENT '积分',
  `multiple_integral` int(11) DEFAULT NULL COMMENT '翻倍积分权益（翻了多少倍）',
  `integral_validity` tinyint(4) DEFAULT NULL COMMENT '0永久有效1固定时间',
  `integral_validity_date` datetime DEFAULT NULL COMMENT '失效时间',
  `task_finish_date` datetime DEFAULT NULL COMMENT '任务完成时间',
  `current_integral` int(11) DEFAULT NULL COMMENT '当前积分',
  `operator_account_name` varchar(128) DEFAULT NULL COMMENT '操作人员账号名字',
  `current_member_level` varchar(128) DEFAULT NULL COMMENT '当前会员等级',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `integral_protect_date` datetime DEFAULT NULL COMMENT '积分保护期',
  `change_type` tinyint(4) DEFAULT NULL COMMENT '积分变动类型（1初始积分，2系统调整，3积分失效，4消费退款，5积分任务，6升级礼包 7赠送积分 8积分抵现）',
  `store_name` varchar(128) DEFAULT NULL COMMENT '门店名称',
  `integral_source_type` tinyint(4) DEFAULT NULL COMMENT '积分变动来源',
  `member_consumption_guid` varchar(50) DEFAULT NULL COMMENT '会员消费记录GUID',
  `order_number` varchar(50) DEFAULT NULL COMMENT '订单编号',
  `integral_declaration` varchar(128) DEFAULT NULL COMMENT '积分说明(会员账户详情)',
  `store_guid` varchar(50) DEFAULT NULL COMMENT '门店guid',
  `record_remain_integral` int(11) DEFAULT NULL COMMENT '积分记录剩余积分',
  `declaration` varchar(512) DEFAULT NULL COMMENT '说明(会员账户详情)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10678 DEFAULT CHARSET=utf8mb4;


-- 会员表修改字段
ALTER TABLE hsa_operation_member_info DROP COLUMN is_perfect;
ALTER TABLE hsa_operation_member_info ADD perfect_personal_detail_json VARCHAR(512) COMMENT '完善个人信息时间json';
UPDATE hsa_operation_member_info SET perfect_personal_detail_json =
 (CASE
 WHEN sex <> 0 AND birthday IS NULL THEN '{"perfectSexTime":"2022-05-19 09:54:33"}'
 WHEN sex = 0 AND birthday IS NOT NULL THEN '{"perfectBirthdayTime":"2022-05-19 09:54:33"}'
 WHEN sex <> 0 AND birthday IS NOT NULL THEN '{"perfectBirthdayTime":"2022-05-19 09:54:33","perfectSexTime":"2022-05-19 09:54:33"}'
 END)










