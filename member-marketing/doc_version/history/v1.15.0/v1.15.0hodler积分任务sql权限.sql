INSERT INTO `gateway`.`hp_function_group`(`create_time`, `name`, `sequence`, `update_time`, `gmt_create`, `gmt_modified`)
VALUES (now(), '会员积分', ( SELECT a.`sequence` FROM (select `sequence` from hp_function_group WHERE NAME = '会员等级' ORDER BY gmt_modified DESC LIMIT 1 ) a), now(), now(), now());


INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), '积分任务', 1, 60, NOW(), 26,  ( SELECT id FROM hp_system WHERE NAME = '会员管理' AND identification = 'member_marketing' ORDER BY gmt_modified DESC LIMIT 1 ), (select id from hp_function_group where name = '会员积分' order by gmt_modified desc limit 1),NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '查看任务', NOW(), (select id from hp_function where name = '积分任务' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '创建任务', NOW(), (select id from hp_function where name = '积分任务' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '查看任务记录', NOW(), (select id from hp_function where name = '积分任务' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '停止', NOW(), (select id from hp_function where name = '积分任务' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '启用', NOW(), (select id from hp_function where name = '积分任务' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '详情', NOW(), (select id from hp_function where name = '积分任务' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '排序', NOW(), (select id from hp_function where name = '积分任务' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '编辑', NOW(), (select id from hp_function where name = '积分任务' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '删除', NOW(), (select id from hp_function where name = '积分任务' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), '通用规则', 1, 60, NOW(), 26,  ( SELECT id FROM hp_system WHERE NAME = '会员管理' AND identification = 'member_marketing' ORDER BY gmt_modified DESC LIMIT 1 ), (select id from hp_function_group where name = '会员积分' order by gmt_modified desc limit 1),NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '查看规则', NOW(), (select id from hp_function where name = '通用规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '编辑', NOW(), (select id from hp_function where name = '通用规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), '消耗规则', 1, 60, NOW(), 26,  ( SELECT id FROM hp_system WHERE NAME = '会员管理' AND identification = 'member_marketing' ORDER BY gmt_modified DESC LIMIT 1 ), (select id from hp_function_group where name = '会员积分' order by gmt_modified desc limit 1),NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '查看规则', NOW(), (select id from hp_function where name = '消耗规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '编辑', NOW(), (select id from hp_function where name = '消耗规则' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());




#貌似线上加了权限的

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '查看积分明细', NOW(), (select id from hp_function where name = '会员账户详情' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '调整积分', NOW(), (select id from hp_function where name = '会员账户详情' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


