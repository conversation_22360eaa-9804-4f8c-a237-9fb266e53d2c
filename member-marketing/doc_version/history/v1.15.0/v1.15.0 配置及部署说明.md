# v1.15.0配置及部署说明


## 访问


## 接口

会员base服务
{adr}  =  线上地址  re地址  test地址
线上地址： https://member.holderzone.com/
re地址：    https://member-re.holderzone.cn/
test地址： https://member-test.holderzone.cn/

1. 初始化会员积分通用规则接口
   {adr}/base/goalgo_callback/initialize_old_subject_data
   
2. 适用业务类型回调接口，需要初始化值(type:2,typeName:堂食点餐)
   {adr}/base/type_config/callback_business_type
   {
      "applyBusiness":[
         {
            "type":"2",
            "typeName":"堂食点餐"
         }
      ]
   }
   
## 服务



## 配置
profiles : dev | test | re | prod
```