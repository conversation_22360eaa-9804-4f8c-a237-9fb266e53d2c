# v1.20.0配置及部署说明


## 访问


## 接口

#发服务时,需要手动初始化hodler权限关联

{test} = https://gateway.hp.goalgo.cn/loginService
POST  {test}/team/permission/dataPermissionType/add
            {
                "permissionId": 5261,    //权限id  （动态的需要查询 hp_permission 表中 id）
                "settingType": 2,
                "isFormPermission": 0,
                "url": "queryurl",  //动态配置查询url
                "saveUrl": "saveurl",  //动态配置保存url
                "message": "运营主体-数据范围",
                "shortMessage": "运营主体",
                "businessType": "",
                "justDept": 0,
                "dataPermissionTypeKey": "dhsauidhask",
                "systemName": "MEMBER",
                "hasAll": 1
            }
初始化运营主体相关数据：主要是会员商城数据的初始化
base/goalgo_callback/initialize_old_subject_data


## 服务
需要发的服务

holder-member-base
holder-member-gateway
holder-member-mall



## 配置