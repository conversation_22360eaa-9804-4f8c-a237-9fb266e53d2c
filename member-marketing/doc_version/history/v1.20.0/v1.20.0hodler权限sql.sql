
USE gateway;
INSERT INTO `gateway`.`hp_system` ( `address`, `create_time`, `identification`, `name`, `status`, `update_time`, `gmt_create`, `gmt_modified`, `display` )
VALUES
( 'https://member-mall-test.holderzone.cn/', now(), 'member_mall', '会员商城', 0, now(), now(), now(), 1 );



INSERT INTO `gateway`.`hp_function_group`(`create_time`, `name`, `sequence`, `update_time`, `gmt_create`, `gmt_modified`)
VALUES (now(), '运营主体', ( SELECT id FROM hp_system WHERE NAME = '会员商城' AND identification = 'member_mall' ORDER BY gmt_modified DESC LIMIT 1 ), now(), now(), now());

INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), '运营主体', 1, 60, NOW(), 26,  ( SELECT id FROM hp_system WHERE NAME = '会员商城' AND identification = 'member_mall' ORDER BY gmt_modified DESC LIMIT 1 ), (select id from hp_function_group where name = '运营主体' order by gmt_modified desc limit 1),NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '运营主体管理范围', NOW(), (select id from hp_function where name = '运营主体' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '完整手机号', NOW(), (select id from hp_function where name = '运营主体' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO `gateway`.`hp_function_group`(`create_time`, `name`, `sequence`, `update_time`, `gmt_create`, `gmt_modified`)
VALUES (now(), '商品管理',( SELECT id FROM hp_system WHERE NAME = '会员商城' AND identification = 'member_mall' ORDER BY gmt_modified DESC LIMIT 1 ), now(), now(), now());

INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), '商品管理', 1, 60, NOW(), 26,  ( SELECT id FROM hp_system WHERE name = '会员商城' AND identification = 'member_mall' ORDER BY gmt_modified DESC LIMIT 1 ), (select id from hp_function_group where name = '商品管理' order by gmt_modified desc limit 1),NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '查看商品列表', NOW(), (select id from hp_function where name = '商品管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '编辑商品详情', NOW(), (select id from hp_function where name = '商品管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '批量上架', NOW(), (select id from hp_function where name = '商品管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '批量下架', NOW(), (select id from hp_function where name = '商品管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '批量删除', NOW(), (select id from hp_function where name = '商品管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '批量改商品标签', NOW(), (select id from hp_function where name = '商品管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '批量改开售时间', NOW(), (select id from hp_function where name = '商品管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '推广', NOW(), (select id from hp_function where name = '商品管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '排序', NOW(), (select id from hp_function where name = '商品管理' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), '商品标签', 1, 60, NOW(), 26,  ( SELECT id FROM hp_system WHERE name = '会员商城' AND identification = 'member_mall' ORDER BY gmt_modified DESC LIMIT 1 ), (select id from hp_function_group where name = '商品管理' order by gmt_modified desc limit 1),NOW(), NOW());


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '查看商品标签列表', NOW(), (select id from hp_function where name = '商品标签' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '新增标签', NOW(), (select id from hp_function where name = '商品标签' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '编辑', NOW(), (select id from hp_function where name = '商品标签' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '删除', NOW(), (select id from hp_function where name = '商品标签' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), '商品设置', 1, 60, NOW(), 26,  ( SELECT id FROM hp_system WHERE name = '会员商城' AND identification = 'member_mall' ORDER BY gmt_modified DESC LIMIT 1 ), (select id from hp_function_group where name = '商品管理' order by gmt_modified desc limit 1),NOW(), NOW());


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '查看商品设置', NOW(), (select id from hp_function where name = '商品设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '编辑商品设置', NOW(), (select id from hp_function where name = '商品设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());




INSERT INTO `gateway`.`hp_function_group`(`create_time`, `name`, `sequence`, `update_time`, `gmt_create`, `gmt_modified`)
VALUES (now(), '商城设置',( SELECT id FROM hp_system WHERE NAME = '会员商城' AND identification = 'member_mall' ORDER BY gmt_modified DESC LIMIT 1 ), now(), now(), now());

INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), '配送设置', 1, 60, NOW(), 26,  ( SELECT id FROM hp_system WHERE name = '会员商城' AND identification = 'member_mall' ORDER BY gmt_modified DESC LIMIT 1 ), (select id from hp_function_group where name = '商城设置' order by gmt_modified desc limit 1),NOW(), NOW());


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '查看物流到家设置', NOW(), (select id from hp_function where name = '配送设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) VALUES (NOW(), NULL, '编辑物流到家设置', NOW(), (select id from hp_function where name = '配送设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO `gateway`.`hp_company_system` ( `company_id`, `system_id`, `create_time`, `gmt_create`, `gmt_modified` )
VALUES
(
    244,(
    SELECT
        id
    FROM
        hp_system
    WHERE
            NAME = '会员商城'
      AND identification = 'member_mall'
    ORDER BY
        gmt_modified DESC
    LIMIT 1
),
    NOW(),
    NOW(),
    NOW());



