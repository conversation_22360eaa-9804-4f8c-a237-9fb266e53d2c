-- 会员后台管理
USE hsm_member_marketing_platform_db;

ALTER TABLE hsa_operation_member_info
    ADD COLUMN integral_deduction tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否开启积分购物默认抵扣（0：未开启  1：开启）默认为未开启';

-- 亲属管理表
CREATE TABLE `hsa_kinsfolk_management`
(
    `id`                bigint(20)                                                    NOT NULL AUTO_INCREMENT COMMENT '主键',
    `guid`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '亲属信息guid',
    `member_info_guid`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '会员GUID',
    `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '运营主体GUID',
    `icon_url`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '头像地址',
    `name`              varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '姓名',
    `phone_num`         char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci     NULL     DEFAULT NULL COMMENT '手机号码',
    `class_and_grade`   varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '班级',
    `student_id`        varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '学号',
    `group_id`          varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '人脸识别（用户组id）',
    `user_id`           varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '人脸识别（用户id）',
    `log_id`            varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '人脸识别（请求标识码，随机数，唯一）',
    `face_token`        varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '人脸识别（图片的唯一表示）',
    `gmt_create`        datetime(0)                                                   NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    `gmt_modified`      datetime(0)                                                   NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    `is_delete`         tinyint(1)                                                    NOT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_member_guid` (`member_info_guid`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 36
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '亲属管理表'
  ROW_FORMAT = Dynamic;

-- 会员后台资料项表
CREATE TABLE `hsa_data_item`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资料项设置guid',
  `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运营主体GUID',
  `data_item` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资料项设置信息（JSON格式）',
  `operator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作人名字',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 236 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '资料项设置表' ROW_FORMAT = Dynamic;

-- 成长值、积分任务暂停时间段
CREATE TABLE `hsa_suspend_task_time_quantum` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `task_name` varchar(128) DEFAULT NULL COMMENT '任务名称',
  `task_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '任务类型 0：成长值 1：积分',
  `task_guid` varchar(50) DEFAULT NULL COMMENT '任务guid',
  `suspend_start_time` datetime DEFAULT NULL COMMENT '暂停开始时间',
  `suspend_end_time` datetime DEFAULT NULL COMMENT '暂停结束时间',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `type` tinyint(4) DEFAULT NULL COMMENT '类型 1：消费 2：充值',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `hsa_integral_deduct_commodity`;
CREATE TABLE `hsa_integral_deduct_commodity`
(
    `id`                         bigint(10)                                                   NOT NULL AUTO_INCREMENT COMMENT 'id',
    `guid`                       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'guid',
    `oper_subject_guid`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运营主体guid',
    `commodity_id`               varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品id',
    `commodity_name`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '商品名称',
    `commodity_code`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '商品编号',
    `commodity_price`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '商品售价',
    `combo_type`                 tinyint(4)                                                   NULL     DEFAULT NULL COMMENT '单品1 固定2 可选3',
    `is_delete`                  tinyint(4)                                                   NULL     DEFAULT NULL,
    `gmt_create`                 datetime(0)                                                  NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    `gmt_modified`               datetime(0)                                                  NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
    `integral_consume_rule_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_guid` (`guid`) USING BTREE
);


DROP TABLE IF EXISTS `hsa_integral_consume_rule`;
CREATE TABLE `hsa_integral_consume_rule`
(
    `id`                      bigint(20)                                                    NOT NULL AUTO_INCREMENT,
    `guid`                    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL,
    `oper_subject_guid`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '运营主体GUID',
    `is_for_now`              tinyint(4)                                                    NULL     DEFAULT NULL COMMENT '是否开启 0 关闭 1 开启',
    `integral_num`            int(20)                                                       NULL     DEFAULT 1 COMMENT '每多少积分',
    `for_now_money`           decimal(12, 2)                                                NULL     DEFAULT NULL COMMENT '抵扣金额',
    `single_order_money`      decimal(12, 2)                                                NULL     DEFAULT NULL COMMENT '单笔订单金额不足多少 不能使用积分',
    `single_order_type`       tinyint(4)                                                    NULL     DEFAULT NULL COMMENT '单笔使用上限类型 0 不限制 1 上限金额 2 上限比例',
    `single_order_value`      decimal(12, 2)                                                NULL     DEFAULT NULL COMMENT '单笔使用上限 0 不限制 1 上限金额 2 上限比例',
    `period_discount_type`    tinyint(4) UNSIGNED ZEROFILL                                  NULL     DEFAULT NULL COMMENT '周期使用上限类型 -1 不限制 0：天 1：周 2：月 3：年',
    `period_discount_limited` decimal(12, 2)                                                NULL     DEFAULT NULL COMMENT '周期使用上限',
    `apply_business_json`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '适用业务json',
    `terminal`                varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '终端',
    `gmt_create`              datetime(0)                                                   NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    `gmt_modified`            datetime(0)                                                   NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
    `serviceable_item`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '适用项目',
    `apply_goods_type`        tinyint(4)                                                    NULL     DEFAULT 0 COMMENT '1：全部商品适用 0：适用商品 1：不适用商品',
    `operator_name`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT '0',
    `is_limit`                tinyint(4)                                                    NULL     DEFAULT NULL COMMENT '周期使用上限类型是否限制 0 限制 1 不限制',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_guid` (`guid`) USING BTREE,
    INDEX `index_label_setting_guid` (`is_for_now`) USING BTREE
);
