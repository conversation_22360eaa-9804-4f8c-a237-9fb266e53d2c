
-- holder权限sql
use gateway
INSERT INTO `gateway`.`hp_function_group`(`create_time`, `name`, `sequence`, `update_time`, `gmt_create`, `gmt_modified`) VALUES (now(), '会员设置', 107, now(), now(), now());

INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), '资料项设置', 1, 60, NOW(), 26,  128, (select id from hp_function_group where name = '会员设置' order by gmt_modified desc limit 1),NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '查看', NOW(), (select id from hp_function where name = '资料项设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '编辑', NOW(), (select id from hp_function where name = '资料项设置' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());