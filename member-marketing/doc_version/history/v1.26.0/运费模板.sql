DROP TABLE IF EXISTS `hsa_logistics_template`;
CREATE TABLE `hsa_logistics_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) NOT NULL COMMENT '模板guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0未删除,1已删除',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',
  `template_name` varchar(25) NOT NULL COMMENT '模板名称',
  `default_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否默认 1是 0否',
  `charge_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '计费方式 1-统一运费 2-商家包邮 3-按件数 4-按重量',
  `freight_amount` decimal(12, 2) NOT NULL DEFAULT 0 COMMENT '运费',
  `charges` json DEFAULT NULL COMMENT '计费明细字符串',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE,
  INDEX `oper_subject_guid_idx`(`oper_subject_guid`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 ROW_FORMAT = DYNAMIC COMMENT = '运费模板';

DROP TABLE IF EXISTS `hsa_logistics_charge`;
CREATE TABLE `hsa_logistics_charge` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) NOT NULL COMMENT '明细guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0未删除,1已删除',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',
  `template_guid` varchar(50) NOT NULL COMMENT '关联运费模板guid',
  `region` text DEFAULT NULL COMMENT '配送区域 省-市-区',
  `weight` decimal(12, 2) NOT NULL DEFAULT 0 COMMENT '重量 多少KG/件',
  `price` decimal(12, 2) NOT NULL DEFAULT 0 COMMENT '价格',
  `second_weight` decimal(12, 2) NOT NULL DEFAULT 0 COMMENT '续重 多少KG/件',
  `second_price` decimal(12, 2) NOT NULL DEFAULT 0 COMMENT '续重价格',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE,
  INDEX `oper_subject_template_guid_idx`(`oper_subject_guid`,`template_guid`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 ROW_FORMAT = DYNAMIC COMMENT = '运费模板计费明细';

INSERT INTO `hsa_logistics_template` ( `guid`, `oper_subject_guid`, `template_name`, `default_flag`, `charge_type`, `freight_amount`, `charges` ) SELECT
guid,
oper_subject_guid,
'默认模板',
1,
2,
0,
NULL
FROM
	hsa_oper_subject_permission
GROUP BY
	oper_subject_guid
ORDER BY
	oper_subject_guid