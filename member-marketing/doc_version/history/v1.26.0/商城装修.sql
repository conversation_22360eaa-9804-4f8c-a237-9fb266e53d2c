-- 新建会员商城装修库
CREATE DATABASE `hsm_member_mall_tool_platform_db` CHARACTER SET 'utf8' COLLATE 'utf8_general_ci';

-- 微页面表
DROP TABLE IF EXISTS `hsa_mall_page`;
CREATE TABLE `hsa_mall_page` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) NOT NULL COMMENT '微页面guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0未删除,1已删除',
  `name` varchar(20) NOT NULL COMMENT '微页面名称',
  `description` varchar(50) NULL COMMENT '页面描述',
  `state` tinyint(4) NULL COMMENT '状态: 0未发布 1已发布',
  `oper_subject_guid` varchar(50) NULL COMMENT '运营主体GUID',
  `applet_id` varchar(50) NULL COMMENT '小程序id',
  `json` text NULL COMMENT '页面json数据',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_guid`(`guid`) USING BTREE,
  INDEX `index_name`(`name`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 ROW_FORMAT = DYNAMIC COMMENT = '微页面表';

-- 基础页面表
DROP TABLE IF EXISTS `hsm_member_mall_tool_platform_db`.`hsa_basic_page`;
CREATE TABLE `hsm_member_mall_tool_platform_db`.`hsa_basic_page` (
	`id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
	`guid` VARCHAR ( 50 ) NOT NULL COMMENT '模块guid',
    `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
	`is_delete` TINYINT ( 1 ) NOT NULL DEFAULT 0 COMMENT '是否删除,0未删除,1已删除',
	`page_type` VARCHAR ( 50 ) NULL COMMENT '页面类型',
	`page_link` VARCHAR ( 255 ) NULL COMMENT '页面链接',
	`name` VARCHAR ( 50 ) NULL COMMENT '页面名称',
	`sort` int(10) unsigned NOT NULL COMMENT '序号',
	PRIMARY KEY ( `id` ),
UNIQUE INDEX `uk_guid` ( `guid` ) USING BTREE
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 ROW_FORMAT = DYNAMIC COMMENT = '基础页面表';

-- hsm_member_mall_tool_platform_db.hsa_applet_navigation definition

CREATE TABLE `hsa_applet_navigation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `title` varchar(255) DEFAULT NULL COMMENT '标题',
  `on_picture` varchar(255) DEFAULT NULL COMMENT '点亮时图片',
  `off_picture` varchar(255) DEFAULT NULL COMMENT '未点亮时图片',
  `url` varchar(255) DEFAULT NULL COMMENT '链接',
  `is_delete` tinyint(1) DEFAULT NULL COMMENT '是否删除,0未删除,1已删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  `oper_subject_guid` varchar(50) DEFAULT NULL COMMENT '运营主体GUID',
  `select_name` varchar(100) DEFAULT NULL COMMENT '页面名字',
  `select_value` varchar(100) DEFAULT NULL COMMENT '页面值',
  `type_text` varchar(100) DEFAULT NULL COMMENT '页面类型',
  `link_id` varchar(100) DEFAULT NULL COMMENT '链接地址id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=407 DEFAULT CHARSET=utf8 COMMENT='小程序导航';


-- hsm_member_mall_tool_platform_db.hsa_theme_style definition

CREATE TABLE `hsa_theme_style` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(100) CHARACTER SET utf8 NOT NULL COMMENT 'guid',
  `color_style` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '颜色风格',
  `is_delete` tinyint(1) DEFAULT NULL COMMENT '是否删除 0：未删除   1：已删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `oper_subject_guid` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '运营主体guid',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4;


-- hsm_member_mall_tool_platform_db.hsa_permission definition

CREATE TABLE `hsa_permission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `enterprise_guid` varchar(50) NOT NULL COMMENT '企业id',
  `position_guid` varchar(50) NOT NULL COMMENT '岗位、角色id',
  `is_role` tinyint(1) DEFAULT NULL COMMENT '类型 0：岗位 1：角色',
  `type_id` varchar(50) DEFAULT NULL COMMENT '门店id、运营主体id',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `type` tinyint(1) DEFAULT NULL COMMENT '0：运营主体权限 1：门店权限',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8 COMMENT='会员商城选择门店权限';


-- hsm_member_mall_tool_platform_db.hsa_permission_type definition

CREATE TABLE `hsa_permission_type` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `enterprise_guid` varchar(50) NOT NULL COMMENT '企业id',
  `position_guid` varchar(50) DEFAULT NULL COMMENT '岗位id或者角色id',
  `is_role` tinyint(1) DEFAULT NULL COMMENT '类型 0：岗位 1：角色',
  `is_all` tinyint(1) DEFAULT NULL COMMENT '全部门店 0:不是 1:是',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `type` tinyint(1) DEFAULT NULL COMMENT '0：运营主体权限 1：门店权限',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COMMENT='门店权限类型表';