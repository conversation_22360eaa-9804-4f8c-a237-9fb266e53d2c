-- 服务
-- gateway、base、tool、market、job

ALTER TABLE `hsm_member_marketing_platform_db`.`hsa_card_open_rule` ADD COLUMN `open_physical_card_channel` json NULL COMMENT '实体卡开卡渠道，json' AFTER `send_count_num`;
-- 历史数据：默认一体机
update hsm_member_marketing_platform_db.hsa_card_open_rule set open_physical_card_channel = '["2"]'  where open_physical_card_channel is null;

ALTER TABLE hsm_member_marketing_platform_db.hsa_physical_card ADD card_strategy_record_guid varchar(50) NULL COMMENT '押金支付记录guid';


drop table if exists hsa_physical_card_strategy_record;

CREATE TABLE `hsa_physical_card_strategy_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `guid` varchar(50) NOT NULL COMMENT 'guid',
    `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `gmt_modified` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete` bigint(13) NOT NULL DEFAULT 0 COMMENT '删除标志位，0-未删除，时间-已删除',
    `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',
	`member_info_guid` bigint(20) DEFAULT NULL COMMENT '会员guid',
	`pay_guid` varchar(50) DEFAULT NULL COMMENT '支付记录guid',
	`make_physical_card_record_guid` bigint(20) DEFAULT NULL COMMENT '制卡记录guid',
	`card_guid` bigint(20) NOT NULL COMMENT '会员卡guid',
	`source` int(4) NOT NULL COMMENT '来源：0后台，2一体机，60自助充值机',
	`pay_status` int(4) NOT NULL COMMENT '支付状态：0未支付 1已支付 2已作废',
	`deposit_strategy_code` varchar(50) DEFAULT NULL COMMENT '押金策略编号:唯一6位数随机编码',
  	`deposit_strategy_name` varchar(20) NOT NULL COMMENT '押金策略名称：必填，1-20字，超过不可输入；与已保存的押金策略名称不可重复',
  	`deposit_amount` decimal(8,2) DEFAULT NULL COMMENT '押金金额',
  	`is_refund_deposit` tinyint(1) NOT NULL DEFAULT '0' COMMENT '退卡是否退押金 0关闭，1开启（关闭：押金不可退）',
 	 `refund_amount` decimal(8,2) DEFAULT NULL COMMENT '可退金额',
 	 `operator_account` varchar(50) DEFAULT NULL COMMENT '操作人账户',
	 `operator_name` varchar(50) DEFAULT NULL COMMENT '操作人名字',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='实体卡押金支付记录';
