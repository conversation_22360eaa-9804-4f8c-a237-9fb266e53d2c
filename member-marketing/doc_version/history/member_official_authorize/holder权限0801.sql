-- holder权限 
 -- todo sql执行完请刷新holder权限redis 
INSERT INTO `gateway`.`hp_function_group`(`create_time`, `name`, `sequence`, `update_time`, `gmt_create`, `gmt_modified`) 
VALUES 
(now(), '消息通知',unix_timestamp(), now(), now(), now());

INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), '用户通知', 1, 60, NOW(), 26,  
( SELECT id FROM hp_system WHERE name = '小程序基础配置' AND identification = 'member_tool'    
 ORDER BY gmt_modified DESC LIMIT 1 ), 
(select id from hp_function_group where name = '消息通知'    
 order by gmt_modified desc limit 1)
,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '查看', NOW(), 
(select id from hp_function where name = '用户通知'    
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '编辑', NOW(), 
(select id from hp_function where name = '用户通知'    
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());



INSERT INTO `gateway`.`hp_function_group`(`create_time`, `name`, `sequence`, `update_time`, `gmt_create`, `gmt_modified`) 
VALUES 
(now(), '公众号管理',unix_timestamp(), now(), now(), now());

INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), '公众号授权', 1, 60, NOW(), 26,  
( SELECT id FROM hp_system WHERE name = '小程序基础配置' AND identification = 'member_tool'    
 ORDER BY gmt_modified DESC LIMIT 1 ), 
(select id from hp_function_group where name = '公众号管理'    
 order by gmt_modified desc limit 1)
,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '查看', NOW(), 
(select id from hp_function where name = '公众号授权'    
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '立即绑定', NOW(), 
(select id from hp_function where name = '公众号授权'    
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`) 
VALUES 
(NOW(), NULL, '重新绑定', NOW(), 
(select id from hp_function where name = '公众号授权'    
 order by gmt_modified desc limit 1)
, 0, NULL, '', 0, 0,NOW(), NOW());