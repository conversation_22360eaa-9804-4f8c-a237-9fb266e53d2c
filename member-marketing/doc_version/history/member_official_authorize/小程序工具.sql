use hsm_member_mall_tool_platform_db;

-- hsm_member_mall_tool_platform_db.hsa_wechat_authorizer_info definition

CREATE TABLE `hsa_wechat_authorizer_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) NOT NULL COMMENT '微页面guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `oper_subject_guid` varchar(50) DEFAULT NULL COMMENT '运营主体GUID',
  `authorizer_appid` varchar(300) DEFAULT NULL,
  `authorizer_access_token` varchar(300) DEFAULT NULL,
  `authorizer_refresh_token` varchar(1000) DEFAULT NULL,
  `func_info` varchar(1000) DEFAULT NULL,
  `nick_name` varchar(100) DEFAULT NULL,
  `head_img` varchar(1000) DEFAULT NULL,
  `service_type_info` tinyint(4) DEFAULT NULL,
  `verify_type_info` tinyint(4) DEFAULT NULL,
  `user_name` varchar(100) DEFAULT NULL,
  `principal_name` varchar(100) DEFAULT NULL,
  `alias` varchar(100) DEFAULT NULL,
  `qrcode_url` varchar(1000) DEFAULT NULL,
  `qrcode_url_oss` varchar(1000) DEFAULT NULL,
  `signature` varchar(100) DEFAULT NULL,
  `un_band_user_guid` varchar(100) DEFAULT NULL,
  `un_band_time` timestamp NULL DEFAULT NULL,
  `is_deleted` tinyint(4) DEFAULT NULL,
  `version` tinyint(4) DEFAULT NULL,
  `expires_in` varchar(100) DEFAULT NULL,
  `component_appid` varchar(300) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`)
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4 COMMENT='微页面表';


-- 好搭档优化
ALTER TABLE hsm_member_marketing_platform_db.hsa_member_personal ADD video_json varchar(1000) NULL;

ALTER TABLE hsm_member_marketing_platform_db.hsa_operation_member_info ADD video_json varchar(1000) NULL;

