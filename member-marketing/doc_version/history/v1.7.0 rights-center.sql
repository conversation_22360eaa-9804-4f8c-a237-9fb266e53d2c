/*
权益中心
*/

/*holder 库*/
use gateway;
insert into `hp_system` (`address`, `create_time`, `description`, `identification`, `login_type`, `name`, `single`, `status`, `support_type`, `update_time`, `version`, `gmt_create`, `gmt_modified`)
values('https://member-rights.holderzone.com/',sysdate(),NULL,'member_rights',NULL,'权益中心',NULL,'0',NULL,
       sysdate(),NULL,sysdate(),sysdate());

/*新增权益管理分组权限*/
INSERT INTO `gateway`.`hp_function_group`(`create_time`, `name`, `sequence`, `update_time`, `gmt_create`, `gmt_modified`)
VALUES (now(), '权益管理', 1, now(), now(), now());

/*新增折扣权益权限*/
INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), '折扣权益', 1, 60, NOW(), 26,  (select id from hp_system where identification = 'member_rights'  order by gmt_modified desc limit 1), (select id from hp_function_group where name = '权益管理' order by gmt_modified desc limit 1),NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '查看权益', NOW(), (select id from hp_function where name = '折扣权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '添加权益', NOW(), (select id from hp_function where name = '折扣权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '禁用', NOW(), (select id from hp_function where name = '折扣权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '启用', NOW(), (select id from hp_function where name = '折扣权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '编辑', NOW(), (select id from hp_function where name = '折扣权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '删除', NOW(), (select id from hp_function where name = '折扣权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

/*新增成长值权益权限*/
INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), '成长值权益', 2, 60, NOW(), 26,  (select id from hp_system where identification = 'member_rights'  order by gmt_modified desc limit 1), (select id from hp_function_group where name = '权益管理' order by gmt_modified desc limit 1),NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '查看权益', NOW(), (select id from hp_function where name = '成长值权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '添加权益', NOW(), (select id from hp_function where name = '成长值权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '禁用', NOW(), (select id from hp_function where name = '成长值权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '启用', NOW(), (select id from hp_function where name = '成长值权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '编辑', NOW(), (select id from hp_function where name = '成长值权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '删除', NOW(), (select id from hp_function where name = '成长值权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

/*新增线下权益权限*/
INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), '线下权益', 3, 60, NOW(), 26,  (select id from hp_system where identification = 'member_rights'  order by gmt_modified desc limit 1), (select id from hp_function_group where name = '权益管理' order by gmt_modified desc limit 1),NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '查看权益', NOW(), (select id from hp_function where name = '线下权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '添加权益', NOW(), (select id from hp_function where name = '线下权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '禁用', NOW(), (select id from hp_function where name = '线下权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '启用', NOW(), (select id from hp_function where name = '线下权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '编辑', NOW(), (select id from hp_function where name = '线下权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '删除', NOW(), (select id from hp_function where name = '线下权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

/*新增积分权益权限*/
INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), '积分权益', 4, 60, NOW(), 26,  (select id from hp_system where identification = 'member_rights'  order by gmt_modified desc limit 1), (select id from hp_function_group where name = '权益管理' order by gmt_modified desc limit 1),NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '查看权益', NOW(), (select id from hp_function where name = '积分权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '添加权益', NOW(), (select id from hp_function where name = '积分权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '禁用', NOW(), (select id from hp_function where name = '积分权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '启用', NOW(), (select id from hp_function where name = '积分权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '编辑', NOW(), (select id from hp_function where name = '积分权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '删除', NOW(), (select id from hp_function where name = '积分权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

/*新增新品优先权权限*/
INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), '新品优先权', 5, 60, NOW(), 26,  (select id from hp_system where identification = 'member_rights'  order by gmt_modified desc limit 1), (select id from hp_function_group where name = '权益管理' order by gmt_modified desc limit 1),NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '查看权益', NOW(), (select id from hp_function where name = '新品优先权' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '添加权益', NOW(), (select id from hp_function where name = '新品优先权' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '禁用', NOW(), (select id from hp_function where name = '新品优先权' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '启用', NOW(), (select id from hp_function where name = '新品优先权' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '编辑', NOW(), (select id from hp_function where name = '新品优先权' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '删除', NOW(), (select id from hp_function where name = '新品优先权' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

/*新增消费项权益权限*/
INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), '消费项权益', 6, 60, NOW(), 26,  (select id from hp_system where identification = 'member_rights'  order by gmt_modified desc limit 1), (select id from hp_function_group where name = '权益管理' order by gmt_modified desc limit 1),NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '查看权益', NOW(), (select id from hp_function where name = '消费项权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '添加权益', NOW(), (select id from hp_function where name = '消费项权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '禁用', NOW(), (select id from hp_function where name = '消费项权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '启用', NOW(), (select id from hp_function where name = '消费项权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '编辑', NOW(), (select id from hp_function where name = '消费项权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '删除', NOW(), (select id from hp_function where name = '消费项权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

/*新增当前系统应用权限绑定企业*/
INSERT INTO `gateway`.`hp_company_system`(`company_id`, `system_id`, `create_time`, `gmt_create`, `gmt_modified`)
VALUES (244, (select id from hp_system where identification = 'member_rights'  order by gmt_modified desc limit 1), NOW(), NOW(), NOW());
