use hsm_member_mall_platform_db;

ALTER TABLE `hsa_product_order_detail`
ADD COLUMN `shopping_cart_guid` varchar(50) NULL COMMENT '购物车商品guid';


-- Rw
-- sql
update hsm_member_marketing_platform_db.hsa_data_scope_permission set list_field = '账户余额' WHERE list_field = '累计账户余额'

ALTER TABLE hsm_member_mall_platform_db.hsa_order_receiver_address ADD store_id varchar(50) NULL;

update hsm_member_mall_platform_db.hsa_order_receiver_address set receiver_type = 1 where receiver_type is null;
ALTER TABLE hsm_member_mall_platform_db.hsa_order_receiver_address MODIFY COLUMN receiver_type tinyint(4) DEFAULT 1 NOT NULL COMMENT '地址类型 0:固定地址 1:动态地址';

-- server：  base、mall、queue、gateway



-- 商城类型
INSERT INTO hsm_member_marketing_platform_db.hsa_apply_dictionaries (module, `type`, type_name, gmt_create, gmt_modified) VALUES('apply_business', '13', '会员商城', '2023-03-06 16:58:22', '2023-03-06 16:58:22');