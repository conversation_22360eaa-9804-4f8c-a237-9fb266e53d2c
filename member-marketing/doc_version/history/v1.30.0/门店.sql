CREATE TABLE `hst_takeout_call_record`
(
    `id`                      bigint(64) unsigned NOT NULL AUTO_INCREMENT,
    `guid`                    varchar(50) DEFAULT NULL COMMENT '唯一标识',
    `enterprise_guid`         varchar(50) DEFAULT NULL COMMENT '企业GUID',
    `gmt_create`              datetime    DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`            datetime    DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `order_id`                varchar(50) DEFAULT NULL COMMENT 'order_id',
    `order_guid`              bigint(20) NOT NULL COMMENT '订单guid',
    `customer_phone`          bigint(20) NOT NULL COMMENT '顾客手机号',
    `batch`                   varchar(50) NOT NULL COMMENT '数据批次号',
    `send_status`             tinyint(1) NOT NULL COMMENT '送达类型 0 超时 1 正常',
    `call_status`             tinyint(1) NOT NULL COMMENT '0 已拨打、1 未拨打、2 已接通、3 未接通',
    `order_sub_type`          tinyint(5) DEFAULT NULL COMMENT '订单子类型：OrderType=0时：0=美团，1=饿了么，2=百度，3=京东; OrderType=1时：0=扫码订单，1=微信预订单',
    `create_time`             datetime    DEFAULT NULL COMMENT '订单创建时间',
    `complete_time`           datetime    DEFAULT NULL COMMENT '订单完成时间',
    `estimate_delivered_time` datetime    DEFAULT NULL COMMENT '预计送达时间：转时间戳后，若0=立即配送，若大于0=具体要求的送达时间',
    `delivered_time`          datetime    DEFAULT NULL COMMENT '实际送达时间',
    `store_name`              varchar(50) DEFAULT NULL COMMENT '门店名称',
    `store_guid`              varchar(50) DEFAULT NULL COMMENT '门店GUID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_guid` (`guid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='外卖订单回访记录';



CREATE TABLE `hst_takeout_call_record`
(
    `id`                      bigint(64) unsigned NOT NULL AUTO_INCREMENT,
    `guid`                    varchar(50) DEFAULT NULL COMMENT '唯一标识',
    `gmt_create`              datetime    DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`            datetime    DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `order_id`                varchar(50) DEFAULT NULL COMMENT 'order_id',
    `order_guid`              bigint(20) NOT NULL COMMENT '订单guid',
    `phone`                   bigint(20) NOT NULL COMMENT '顾客手机号',
    `batch`                   varchar(50) NOT NULL COMMENT '数据批次号',
    `send_status`             tinyint(1) NOT NULL COMMENT '送达类型 0 超时 1 正常',
    `call_status`             tinyint(1) NOT NULL COMMENT '0 已拨打、1 未拨打、2 已接通、3 未接通',
    `order_sub_type`          tinyint(5) DEFAULT NULL COMMENT '订单子类型：OrderType=0时：0=美团，1=饿了么，2=百度，3=京东; OrderType=1时：0=扫码订单，1=微信预订单',
    `create_time`             datetime    DEFAULT NULL COMMENT '订单创建时间',
    `complete_time`           datetime    DEFAULT NULL COMMENT '订单完成时间',
    `estimate_delivered_time` datetime    DEFAULT NULL COMMENT '预计送达时间：转时间戳后，若0=立即配送，若大于0=具体要求的送达时间',
    `delivered_time`          datetime    DEFAULT NULL COMMENT '实际送达时间',
    `store_name`              varchar(50) DEFAULT NULL COMMENT '门店名称',
    `store_guid`              varchar(50) DEFAULT NULL COMMENT '门店GUID',
    `enterprise_guid`         varchar(50) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_guid` (`guid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='外卖订单回访记录';


ALTER TABLE hst_order_item
    ADD member_preferential DECIMAL(10, 2) DEFAULT 0.00 NULL COMMENT '会员价优惠金额';
ALTER TABLE hst_order_item
    ADD ticket_preferential DECIMAL(10, 2) DEFAULT 0.00 NULL COMMENT '商品券抵扣优惠金额';
ALTER TABLE hst_order_item
    ADD discount_preferential DECIMAL(10, 2) DEFAULT 0.00 NULL COMMENT '满折活动商品优惠金额';


ALTER TABLE hst_adjust_order_details
    ADD takeaway_accounting_price DECIMAL(10, 2) DEFAULT 0.00 NULL COMMENT '商品外卖核算价格';

ALTER TABLE hst_adjust_order_details
    ADD accounting_price DECIMAL(10, 2) DEFAULT 0.00 NULL COMMENT '商品堂食核算价格';



ALTER TABLE hst_order_item
    ADD sort_json varchar(50) COMMENT '商品分类排序';



