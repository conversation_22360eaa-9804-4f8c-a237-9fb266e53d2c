


use hsm_member_marketing_platform_db;
ALTER TABLE hsa_credit_info ADD COLUMN receiver_company_name VARCHAR(128) COMMENT '收款方企业名称';
alter table hsa_credit_wallet_log add COLUMN change_amount_type tinyint(4) COMMENT '变动类型 0 增加 1 减少';
ALTER TABLE `hsa_credit_user` ADD INDEX idx_guid ( `guid` );

CREATE TABLE `hsa_credit_funding_detail`  (
      `id` bigint(20) NOT NULL AUTO_INCREMENT,
      `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '挂账资金明细GUID',
      `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运营主体GUID',
      `credit_info_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '挂账账户guid',
      `member_info_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '挂账账户guid',
      `credit_order_record_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '挂账订单记录guid',
      `credit_amount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '挂账金额',
      `transaction_type` tinyint(4) NOT NULL COMMENT '0:消费,1:消费退款,2:结算收款',
      `sale_store` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '销售门店',
      `clearing_status` tinyint(4) NULL DEFAULT 0 COMMENT '结算状态 0:未结算 1:结算中 2:已结算',
      `change_amount_type` tinyint(4) NULL DEFAULT NULL COMMENT '变动类型 0 增加 1 减少',
      `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
      `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
      PRIMARY KEY (`id`) USING BTREE,
      UNIQUE INDEX `uk_guid`(`guid`) USING BTREE,
      INDEX `hsa_member_funding_detail_gmt_modified_idx`(`gmt_modified`) USING BTREE,
      INDEX `idx_member_guid_credit_guid`(`member_info_guid`, `credit_info_guid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '挂账资金来往明细表' ROW_FORMAT = DYNAMIC;


CREATE TABLE `has_cancellation_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `credit_info_guid` varchar(50) DEFAULT NULL COMMENT '挂账信息guid',
  `order_number` varchar(50) DEFAULT NULL COMMENT '订单编号',
  `order_type` tinyint(4) DEFAULT NULL COMMENT '订单类型 0:食堂预订 1:自助餐 2:堂食点餐 3:快速收款',
  `order_status` tinyint(4) DEFAULT NULL COMMENT '订单状态 0：已结账 1：已取消',
  `sale_store` varchar(128) DEFAULT NULL COMMENT '销售门店',
  `order_paid_amount` decimal(12,2) DEFAULT '0.00' COMMENT '订单实付金额',
  `credit_amount` decimal(12,2) DEFAULT '0.00' COMMENT '挂账金额',
  `clearing_statement_number` varchar(50) DEFAULT NULL COMMENT '结算单号',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `credit_username` varchar(128) DEFAULT NULL COMMENT '挂账使用人',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=58 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `hsa_clearing_statement` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `credit_info_guid` varchar(50) NOT NULL COMMENT '挂账信息guid',
  `clearing_statement_number` varchar(50) DEFAULT NULL COMMENT '结算单编号',
  `clearing_account` varchar(128) DEFAULT NULL COMMENT '结算账户',
  `start_clearing_date` datetime DEFAULT NULL COMMENT '开始结算周期',
  `end_clearing_date` datetime DEFAULT NULL COMMENT '结束结算周期',
  `current_period_receivable` decimal(10,2) DEFAULT '0.00' COMMENT '本期应收',
  `receivable_adjust` decimal(10,2) DEFAULT '0.00' COMMENT '应收调整',
  `current_period_official_receipts` decimal(10,2) DEFAULT '0.00' COMMENT '本期实收',
  `create_clearing_statement_user` varchar(50) DEFAULT NULL COMMENT '生成结算单操作人',
  `reconciliation_status` tinyint(4) DEFAULT '0' COMMENT '对账状态 0:待确认 1:已确认 2:自动确认',
  `pay_status` tinyint(4) DEFAULT '0' COMMENT '结算状态 0:未支付 1:已支付 2:已作废',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `automatic_affirm_date` datetime DEFAULT NULL COMMENT '自动确认时间',
  `affirm_date` datetime DEFAULT NULL COMMENT '确认时间',
  `affirm_remark` varchar(128) DEFAULT NULL COMMENT '确认备注',
  `payee_way` varchar(128) DEFAULT NULL COMMENT '收款方式',
  `payee_date` datetime DEFAULT NULL COMMENT '收款时间',
  `payee_remark` varchar(128) DEFAULT NULL COMMENT '收款备注',
  `operation_user` varchar(128) DEFAULT NULL COMMENT '操作人(收款人或作废人)',
  `operation_date` datetime DEFAULT NULL COMMENT '操作时间(作废时间)',
  `cancellation_remark` varchar(128) DEFAULT NULL COMMENT '作废备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=151 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `hsa_clearing_statement_operate_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `clearing_statement_number` varchar(50) NOT NULL COMMENT '结算单编号',
  `operate_type` tinyint(4) DEFAULT NULL COMMENT '操作类型 0:订单退款 1:应收调整 2:移除订单',
  `order_number` varchar(50) DEFAULT NULL COMMENT '订单编号',
  `order_status` tinyint(4) DEFAULT NULL COMMENT '订单状态 0：已结账 1：已取消',
  `order_paid_amount` decimal(10,2) DEFAULT '0.00' COMMENT '订单实付金额',
  `credit_amount` decimal(10,2) DEFAULT '0.00' COMMENT '挂账金额',
  `credit_username` varchar(50) DEFAULT NULL COMMENT '挂账使用人',
  `receivable_adjust_amount` decimal(10,2) DEFAULT '0.00' COMMENT '应收调整金额',
  `receivable_adjust_type` tinyint(4) DEFAULT '0' COMMENT '应收调整类型 0：增加 1：减少',
  `adjust_remark` varchar(256) DEFAULT NULL COMMENT '调整备注',
  `operation_username` varchar(128) DEFAULT NULL COMMENT '操作人',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `order_record_guid` varchar(50) DEFAULT NULL COMMENT '订单记录guid',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=159 DEFAULT CHARSET=utf8mb4;


CREATE TABLE `hsa_credit_order_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `credit_info_guid` varchar(50) NOT NULL COMMENT '挂账账户guid',
  `order_number` varchar(50) DEFAULT NULL COMMENT '订单编号',
  `order_type` tinyint(4) DEFAULT NULL COMMENT '订单类型 0:食堂预订 1:自助餐 2:堂食点餐 3:快速收款',
  `order_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '订单状态 0：已结账 1：已取消',
  `sale_store` varchar(128) DEFAULT NULL COMMENT '销售门店',
  `order_paid_amount` decimal(10,2) DEFAULT '0.00' COMMENT '订单实付金额',
  `credit_amount` decimal(10,2) DEFAULT '0.00' COMMENT '挂账金额',
  `credit_username` varchar(128) DEFAULT NULL COMMENT '挂账使用人',
  `member_guid` varchar(50) DEFAULT NULL COMMENT '会员guid',
  `clearing_status` tinyint(4) DEFAULT '0' COMMENT '结算状态 0:未结算 1:结算中 2:已结算',
  `clearing_statement_number` varchar(50) DEFAULT NULL COMMENT '结算单号',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `credit_user_guid` varchar(50) DEFAULT '' COMMENT '挂账账户guid',
  `order_source` tinyint(4) NOT NULL COMMENT '来源，53:微信，2:一体机 1:POS',
  PRIMARY KEY (`id`),
  KEY `idx_guid` (`guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=365 DEFAULT CHARSET=utf8mb4;


