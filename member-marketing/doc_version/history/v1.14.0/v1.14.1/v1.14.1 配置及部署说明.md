# 相关服务

新会员：
marketing/v1.14.1
base/v1.14.1
holder-member-queue/v1.14.1
holder-member-job-admin/v1.14.1
holder-member-job-executor/v1.14.1

```yaml
member-job-executor-{profiles}.yaml
cloud:
        stream:
            binders:
                defaultRabbit:
                    type: rabbit
            bindings:
                #新增两个配置
                outputSubsidySendRights:
                    destination: subsidySendRightsExchange
                    contentType: application/json
                outputSubsidySendBackRights:
                    destination: subsidySendBackRightsExchange
                    contentType: application/json
```

```yaml
member-queue-{profiles}.yaml
cloud:
  stream:
    rabbit:
      bindings:
        #新增配置
        inputSubsidySendRights:
          consumer:
            acknowledge-mode: manual
        inputSubsidySendBackRights:
          consumer:
            acknowledge-mode: manual
    bindings:
      #新增配置
      inputSubsidySendRights:
        group: subsidySendRights
        destination: subsidySendRightsExchange
        consumer:
          max-attempts: 1
      inputSubsidySendBackRights:
        group: subsidySendBackRights
        destination: subsidySendBackRightsExchange
        consumer:
          max-attempts: 1
```

