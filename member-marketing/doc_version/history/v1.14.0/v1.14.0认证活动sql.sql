#营销中心
#use hsm_member_marketing_center_platform_db;

/*认证活动审核记录*/
CREATE TABLE `hsa_audit_step`  (
   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
   `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'guid',
   `audit_user` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核人',
   `status` tinyint(4) NOT NULL COMMENT '审核状态 1：审核通过 2：驳回',
   `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
   `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
   `content` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注内容',
   `type` tinyint(4) NULL DEFAULT NULL COMMENT '类型 0：认证审核',
   `type_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型guid',
   `source` tinyint(4) NULL DEFAULT NULL COMMENT '审核来源 0：管理后台 8：商户小程序',
   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 441 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '认证活动审核记录' ROW_FORMAT = Dynamic;

/*# 认证有礼活动表*/
CREATE TABLE `hsa_certified_activity`  (
   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
   `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '认证有礼活动guid',
   `oper_subject_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '运营主体',
   `enterprise_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '企业GUID',
   `activity_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动编码',
   `activity_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动名称',
   `activity_start_time` datetime(0) NOT NULL COMMENT '活动开始时间',
   `activity_end_time` datetime(0) NOT NULL COMMENT '活动结束时间',
   `certified_type` tinyint(4) NULL DEFAULT NULL COMMENT '认证类型(1：行程票  2：自定义证件)',
   `certificate_name` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '自定义证件名称',
   `certification_info` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '认证信息',
   `certification_restrictions_type` tinyint(4) UNSIGNED NULL DEFAULT 0 COMMENT '认证限制类型(0:不限制，1：限制)',
   `certification_condition_limit` tinyint(4) NULL DEFAULT 0 COMMENT '认证条件是否限制（0：不限制，1：限制）',
   `time_restrictions_scope` int(11) NULL DEFAULT NULL COMMENT '认证限制时间范围（0~999）',
   `time_restrictions_unit` tinyint(4) NULL DEFAULT NULL COMMENT '认证限制时间单位（0天、1周、2月、3年）',
   `certification_condition_type` tinyint(4) NULL DEFAULT NULL COMMENT '认证条件类型(1：目的地，2：始发地，3：纯数字，4：纯字母，5：字母+数字)',
   `certification_condition_num` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '认证条件限制位数',
   `participate_certification_num` int(11) NULL DEFAULT NULL COMMENT '参与认证次数（默认：0不限制参与次数）范围：0~999',
   `merchants_entrance` tinyint(4) NULL DEFAULT NULL COMMENT '商家入口（默认值，1开启，0关闭）',
   `activity_label_guids` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动标签guid集合json',
   `activity_state` tinyint(4) NULL DEFAULT NULL COMMENT '活动发布状态 1 未发布 2 未开始 3 发布中  4 已暂停 5 已结束　6进行中　7暂停已发布',
   `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
   `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
   `is_redact` tinyint(4) UNSIGNED NULL DEFAULT NULL COMMENT '是否编辑',
   `is_delete` tinyint(4) UNSIGNED NOT NULL COMMENT '是否删除（默认为：0未删除  1：删除 2:历史数据）',
   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 352 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '认证有礼活动表' ROW_FORMAT = Dynamic;

/*#认证赠送礼品信息表*/
CREATE TABLE `hsa_certified_gift`  (
   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
   `certified_activity_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '认证活动guid',
   `certified_conditions` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '认证条件集合',
   `certified_conditions_info` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '认证条件内容(用于行程票：目的地或者始发地信息)',
   `fixed_number_start` tinyint(4) NULL DEFAULT NULL COMMENT '固定编号开始位',
   `fixed_number_end` tinyint(4) NULL DEFAULT NULL COMMENT '固定编号结束位',
   `fixed_number_info` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '固定编号内容信息',
   `giveaway_info_json` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '赠品信息json格式',
   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1166 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '认证赠送礼品信息表' ROW_FORMAT = Dynamic;

/*#认证活动记录*/
CREATE TABLE `hsa_certified_activity_record`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'guid',
  `certified_activity_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '认证活动guid',
  `activity_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动编号',
  `activity_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动名称',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户姓名',
  `phone_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `certified_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '认证类型',
  `status` tinyint(4) NOT NULL COMMENT '状态 0:待审核 1：审核通过 2：驳回',
  `audit_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  `audit_user` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核人',
  `gmt_create` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `trip_ticket_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '行程票类型/证件类型',
  `voucher` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '凭证',
  `certificate_validity` datetime(0) NULL DEFAULT NULL COMMENT '证件有效期',
  `discount_validity` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '优惠劵有效期',
  `discount` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '优惠价(json格式，优惠价guid加数量)',
  `arrive_time` datetime(0) NULL DEFAULT NULL COMMENT '到达日期',
  `journey_type` tinyint(4) NULL DEFAULT NULL COMMENT '行程类型 0：目的地 1：始发地',
  `journey_address` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '行程地',
  `certificate_number` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证件编号',
  `certificate_date` datetime(0) NULL DEFAULT NULL COMMENT '证件日期',
  `member_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户guid(赚餐服务guid)',
  `source` tinyint(4) NULL DEFAULT NULL COMMENT '53:微信小程序 8:点菜宝',
  `openid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '小程序用户openid',
  `is_agree_send_msg` tinyint(4) NULL DEFAULT NULL COMMENT '用户是否授权小程序推送认证结果消息(1:授权 0：未)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 404 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '认证活动记录' ROW_FORMAT = Dynamic;

CREATE TABLE `hsa_wx_user_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) NOT NULL,
  `open_id` varchar(100) DEFAULT NULL COMMENT '微信openId',
  `always_agree_message` varchar(255) DEFAULT '{}' COMMENT '总是同意消息通知:{"template_id1":1,"template_id2":0}',
  `phone` varchar(20) DEFAULT NULL COMMENT '用户手机号',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据更新时间',
  `is_delete` tinyint(2) DEFAULT '0',
  `msg_template_id` varchar(50) DEFAULT NULL COMMENT '消息模板id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `guid` (`guid`),
  KEY `open_id_idx` (`open_id`),
  KEY `hwur_gmt_modified_idx` (`gmt_modified`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
