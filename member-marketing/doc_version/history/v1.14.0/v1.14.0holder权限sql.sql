use gateway;

INSERT INTO `gateway`.`hp_function_group`(`create_time`, `name`, `sequence`, `update_time`, `gmt_create`, `gmt_modified`)
VALUES (now(), '认证有礼管理', 108, now(), now(), now());

INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), '认证有礼', 1, 60, NOW(), 26,  ( SELECT id FROM hp_system WHERE NAME = '营销中心' AND identification = 'marketing_count' ORDER BY gmt_modified DESC LIMIT 1 ), (select id from hp_function_group where name = '认证有礼管理' order by gmt_modified desc limit 1),NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '活动列表', NOW(), (select id from hp_function where name = '认证有礼' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '新建活动', NOW(), (select id from hp_function where name = '认证有礼' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '发布活动', NOW(), (select id from hp_function where name = '认证有礼' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '暂停活动', NOW(), (select id from hp_function where name = '认证有礼' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '启用活动', NOW(), (select id from hp_function where name = '认证有礼' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '编辑活动', NOW(), (select id from hp_function where name = '认证有礼' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '查看活动详情', NOW(), (select id from hp_function where name = '认证有礼' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '查看活动统计', NOW(), (select id from hp_function where name = '认证有礼' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '删除活动', NOW(), (select id from hp_function where name = '认证有礼' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '分享活动', NOW(), (select id from hp_function where name = '认证有礼' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '查看申请详情', NOW(), (select id from hp_function where name = '认证有礼' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '审核通过', NOW(), (select id from hp_function where name = '认证有礼' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '审核驳回', NOW(), (select id from hp_function where name = '认证有礼' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());



