# 相关服务

云平台权限配置：
认证有礼 https://member-center-test.holderzone.cn/approve/list
认证有礼 https://member-center.holderzone.com/approve/list

1. 门店修改 certification_polite 分支

```
holder-saas-aggregation-merchant/certification_polite	
holder-saas-store-staff/certification_polite

```


# 配置
门店 holder-saas-gateway   authorization-whitelist.txt增加白名单
```yaml
/menu/get_page_Url_by_menu
/hsa-LabelSetting/delete-user-label-batch

新会员：marketing/v1.14.0、base/v1.14.0

老会员：member-merchant/certification_polite、member-wechat/certification_polite、member-sop-service-web/dev-v1.0.0

赚餐：island/certification_polite
```

member-marketing-{profiles}.yaml

```yaml
feign:
    member-center: https://member-center-test.holderzone.cn

#miniprogramState: online线上、trial 体验
miniProgram:
  #活动吧 -> 线上正式使用
  #   appId: wxc44c6cb260685508
  #   appSecret: eecd05f3e80bf2d7adf0c147d57a181d
  #   templateId: rZRYMcwnSgs7u6kCxSnh_dte3i5myiefxVfo3dQJ2xY
  #   data: thing6,phrase5,thing29,time24,thing4
  #   miniprogramState: online
  #掌控者会员俱乐部 -> 线上测试使用
  appId: wx086ebdab4a6888dd
  appSecret: d7c0c7a1f5e2f4a4a5c62e9653f69003
  templateId: 3Jloya8LQX_un4UkM4_3GQxELf0vR24NSUCyAVat098
  data: thing6,phrase5,thing29,time24,thing4
  miniprogramState: online
  #跑山鸡 -> 测试环境使用
#  appId: wxc8803b5576f3dcdf
#  appSecret: 81c3d0308e469d4248bd68b20c6ad164
#  templateId: sf3pwmrBc37Z7P2x9WH84ywNgV78bHx3IthrPzy3wMM
#  data: thing1,phrase4,thing3,time2,thing5
#  miniprogramState: trial
```
member-base-{profiles}.yaml

```yaml
feign:
    pay: https://test-holder-member-pay
    crm: https://sale-crm.holderzone.cn
```

