
INSERT INTO `gateway`.`hp_function_group`(`create_time`, `name`, `sequence`, `update_time`, `gmt_create`, `gmt_modified`)
VALUES (now(), '商品会员价', 108, now(), now(), now());

INSERT INTO `gateway`.`hp_function`(`create_time`, `name`, `priority`, `type`, `update_time`, `parent_id`, `system_id`, `group_id`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), '商品会员价', 1, 60, NOW(), 26,  ( SELECT id FROM hp_system WHERE NAME = '营销中心' AND identification = 'marketing_count' ORDER BY gmt_modified DESC LIMIT 1 ), (select id from hp_function_group where name = '商品会员价' order by gmt_modified desc limit 1),NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '设置', NOW(), (select id from hp_function where name = '商品会员价' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());





INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '调研详情', NOW(), (select id from hp_function where name = '折扣权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '权益详情', NOW(), (select id from hp_function where name = '折扣权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '调研详情', NOW(), (select id from hp_function where name = '成长值权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '权益详情', NOW(), (select id from hp_function where name = '成长值权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '调研详情', NOW(), (select id from hp_function where name = '积分权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '权益详情', NOW(), (select id from hp_function where name = '积分权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '调研详情', NOW(), (select id from hp_function where name = '线下权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '权益详情', NOW(), (select id from hp_function where name = '线下权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '调研详情', NOW(), (select id from hp_function where name = '新品优先权' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '权益详情', NOW(), (select id from hp_function where name = '新品优先权' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '调研详情', NOW(), (select id from hp_function where name = '消费项权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());

INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`, `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (NOW(), NULL, '权益详情', NOW(), (select id from hp_function where name = '消费项权益' order by gmt_modified desc limit 1), 2, NULL, '', 0, 0,NOW(), NOW());


update hp_permission set name = '查看' where function_id = (select id from hp_function  where name = '折扣权益' order by gmt_modified desc limit 1) and name = '查看权益';

update hp_permission set name = '查看' where function_id = (select id from hp_function  where name = '成长值权益' order by gmt_modified desc limit 1) and name = '查看权益';

update hp_permission set name = '查看' where function_id = (select id from hp_function  where name = '线下权益' order by gmt_modified desc limit 1) and name = '查看权益';

update hp_permission set name = '查看' where function_id = (select id from hp_function  where name = '积分权益' order by gmt_modified desc limit 1) and name = '查看权益';

update hp_permission set name = '查看' where function_id = (select id from hp_function  where name = '新品优先权' order by gmt_modified desc limit 1) and name = '查看权益';

update hp_permission set name = '查看' where function_id = (select id from hp_function  where name = '消费项权益' order by gmt_modified desc limit 1) and name = '查看权益';

