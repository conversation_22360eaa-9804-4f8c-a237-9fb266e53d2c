ALTER TABLE hsa_label_setting_base_info
    ADD COLUMN set_integral TINYINT(1) COMMENT '设置积分行为,0未设置,1已设置';
ALTER TABLE hsa_label_setting_base_info
    ADD COLUMN min_integral INT(11) COMMENT '最小当前积分';
ALTER TABLE hsa_label_setting_base_info
    ADD COLUMN max_integral INT(11) COMMENT '最大当前积分';
ALTER TABLE hsa_label_setting_base_info
    ADD COLUMN min_get_integral INT(11) COMMENT '最小累计获得积分';
ALTER TABLE hsa_label_setting_base_info
    ADD COLUMN max_get_integral INT(11) COMMENT '最大累计获得积分';
ALTER TABLE hsa_label_setting_base_info
    ADD COLUMN get_integral_num_type INT(11) COMMENT '累计积分时间类型 1：注册至今、2：最近一段时间、3固定时间';
ALTER TABLE hsa_label_setting_base_info
    ADD COLUMN get_integral_recent_day INT(11) COMMENT '累计获取积分最近天数';
ALTER TABLE hsa_label_setting_base_info
    ADD COLUMN get_integral_fixed_date_json VARCHAR(128) COMMENT '累计获取积分固定时间json';
ALTER TABLE hsa_label_setting_base_info
    ADD COLUMN min_consumption_integral INT(11) COMMENT '最小累计消费积分';
ALTER TABLE hsa_label_setting_base_info
    ADD COLUMN max_consumption_integral INT(11) COMMENT '最大累计消费积分';
ALTER TABLE hsa_label_setting_base_info
    ADD COLUMN consumption_integral_num_type INT(11) COMMENT '累计消费积分时间类型 1：注册至今、2：最近一段时间、3固定时间';
ALTER TABLE hsa_label_setting_base_info
    ADD COLUMN consumption_integral_recent_day INT(11) COMMENT '累计消费积分最近天数';
ALTER TABLE hsa_label_setting_base_info
    ADD COLUMN consumption_integral_fixed_date_json VARCHAR(128) COMMENT '累计消费积分固定时间json';

-- 删除考勤预定
DELETE
FROM hsa_apply_dictionaries
WHERE TYPE = 'attendance_booking';

-- 修改使用业务类型 自助餐--->:堂食自助
UPDATE hsa_apply_dictionaries
SET type_name = '堂食自助'
WHERE module = 'apply_business'
  AND type = '1';

CREATE TABLE `hsa_card_pay_record`
(
    `id`                    bigint(20)                                                   NOT NULL AUTO_INCREMENT,
    `guid`                  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `oper_subject_guid`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运营主体GUID',
    `enterprise_guid`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT '1' COMMENT '企业',
    `member_info_guid`      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '会员guid',
    `member_info_card_guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '持卡guid',
    `card_guid`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '卡GUid',
    `order_guid`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT 'order_guid',
    `pay_guid`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT 'pay_guid',
    `pay_condition`         tinyint(4)                                                   NULL     DEFAULT NULL COMMENT '累计消费金额',
    `business_type`         tinyint(4)                                                   NULL     DEFAULT NULL COMMENT '10 充值开卡、20 消费开卡 30 微信充值 40 微信支付',
    `electronic_open_way`   tinyint(4)                                                   NULL     DEFAULT NULL COMMENT '开卡方式(0 免费开通 1付费开通 2充值开通 3注册开通 4满足条件 5指定开通 6实体卡同步开通)',
    `gmt_create`            datetime(0)                                                  NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    `gmt_modified`          datetime(0)                                                  NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
    `source`                varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '来源',
    `self_type`             tinyint(4)                                                   NULL     DEFAULT 0 COMMENT '自主开通类型（0免费；1直接付款；2充值）',
    `pay_way`               tinyint(4)                                                   NULL     DEFAULT 0 COMMENT '支付方式，0 现金支付 1聚合支付，2银行卡支付，3卡余额支付 ，4人脸支付 ，13微信支付 ，5自定义\"',
    `payment_money`         decimal(11, 2)                                               NOT NULL DEFAULT 0.00 COMMENT '开通付款金额',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_guid` (`guid`) USING BTREE
);

CREATE TABLE `hsa_commodity_member_price` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体guid',
  `channel` varchar(50) DEFAULT NULL COMMENT '销售渠道',
  `commodity_source` varchar(50) DEFAULT NULL COMMENT '商品来源类型 store:门店 stall:档口',
  `commodity_id` bigint(20) DEFAULT NULL COMMENT '商品id',
  `discount_type` tinyint(4) DEFAULT NULL COMMENT '折扣类型 0：折扣 1：减价 2：指定价格',
  `discount_value` decimal(12,2) DEFAULT '0.00' COMMENT '折扣值',
  `limited_type` tinyint(1) DEFAULT NULL COMMENT '限制类型 0:不限制 1:限制',
  `limited_number` int(11) DEFAULT NULL COMMENT '限制数量',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint(1) DEFAULT '0' COMMENT '是否删除 0 未删除 1：已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4;


CREATE TABLE `hsa_commodity_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `commodity_id` bigint(20) DEFAULT NULL COMMENT '商品id',
  `channel` varchar(128) DEFAULT NULL COMMENT '商品渠道',
  `store_name` varchar(128) DEFAULT NULL COMMENT '门店',
  `store_id` bigint(20) DEFAULT NULL COMMENT '门店id',
  `stall_name` varchar(128) DEFAULT NULL COMMENT '档口名称',
  `strategy_menu` varchar(128) DEFAULT NULL COMMENT '菜单',
  `commodity_category` varchar(128) DEFAULT NULL COMMENT '商品分类',
  `commodity_type` varchar(128) DEFAULT NULL COMMENT '商品类型',
  `commodity_code` varchar(128) DEFAULT NULL COMMENT '商品编号',
  `commodity_name` varchar(128) DEFAULT NULL COMMENT '商品名称',
  `commodity_price` decimal(12,2) DEFAULT '0.00' COMMENT '商品售价',
  `commodity_source` varchar(128) DEFAULT NULL COMMENT '商品来源 store:门店 stall:档口',
  `is_delete` tinyint(1) DEFAULT NULL COMMENT '0：未删除 1：已删除',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `stall_id` bigint(20) DEFAULT NULL COMMENT '档口id',
  `strategy_name` varchar(128) DEFAULT NULL COMMENT '策略单名称',
  `strategy_code` varchar(128) DEFAULT NULL COMMENT '策略单编号',
  `commodity_member_price_id` bigint(20) DEFAULT NULL COMMENT '商品会员价关联id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4


-- 电子卡开卡操作人名称 字段的数据类型
ALTER TABLE hsa_electronic_card MODIFY COLUMN `operation_name` VARCHAR ( 50 ) DEFAULT NULL COMMENT '开卡操作人名称';

ALTER TABLE hsa_member_consumption ADD (stall_guid VARCHAR(50));

-- member-pay 服务需要
CREATE TABLE `hsp_pay_record` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT,
  `store_guid` varchar(45) DEFAULT NULL,
  `pay_power_id` varchar(45) DEFAULT NULL,
  `pay_power_name` varchar(45) DEFAULT NULL,
  `pay_channel_id` varchar(45) DEFAULT NULL,
  `order_guid` varchar(45) DEFAULT NULL,
  `pay_guid` varchar(45) DEFAULT NULL,
  `amount` decimal(8,2) DEFAULT NULL,
  `order_holder_no` varchar(45) DEFAULT NULL,
  `order_no` varchar(45) DEFAULT NULL,
  `pay_st` varchar(45) DEFAULT NULL,
  `bank_transaction_id` varchar(45) DEFAULT NULL,
  `gmt_time_paid` datetime DEFAULT NULL,
  `ref_order_no` varchar(45) DEFAULT NULL,
  `gmt_refund` datetime DEFAULT NULL,
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_UNIQUE` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=72 DEFAULT CHARSET=utf8mb4;