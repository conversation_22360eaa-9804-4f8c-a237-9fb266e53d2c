INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '菜品投票取消暂停', NOW(),
        (select id
         from hp_function
         where name = '投票活动'
         order by gmt_modified desc limit 1), 0, NULL, '', 0, 0, NOW(), NOW() );


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '菜品投票新增活动', NOW(),
        (select id
         from hp_function
         where name = '投票活动'
         order by gmt_modified desc limit 1), 0, NULL, '', 0, 0, NOW(), NOW() );