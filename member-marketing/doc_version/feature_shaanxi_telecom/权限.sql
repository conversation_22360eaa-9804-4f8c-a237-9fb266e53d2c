INSERT INTO `gateway`.`hp_function_group`(`create_time`, `name`, `sequence`, `update_time`, `gmt_create`, `gmt_modified`)
VALUES (now(), '限量抢购', unix_timestamp(), now(), now(), now());


INSERT INTO `gateway`.`hp_function`
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified,
 function_identification)
VALUES (CURRENT_TIMESTAMP, '限量抢购', 1, 60, CURRENT_TIMESTAMP, 26,
        (SELECT id
         from hp_system
         WHERE name = '营销中心' AND identification = 'marketing_count'
         ORDER BY gmt_modified DESC LIMIT 1),
       (select id from hp_function_group where name = '限量抢购' and sequence >0 order by gmt_modified desc limit 1), CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');




INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '活动列表', NOW(),
        (select id
         from hp_function
         where name = '限量抢购'
         order by gmt_modified desc limit 1), 0, NULL, '', 0, 0, NOW(), NOW() );


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '新建活动', NOW(),
        (select id
         from hp_function
         where name = '限量抢购'
         order by gmt_modified desc limit 1), 0, NULL, '', 0, 0, NOW(), NOW() );



INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '发布活动', NOW(),
        (select id
         from hp_function
         where name = '限量抢购'
         order by gmt_modified desc limit 1), 0, NULL, '', 0, 0, NOW(), NOW() );



INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '暂停活动', NOW(),
        (select id
         from hp_function
         where name = '限量抢购'
         order by gmt_modified desc limit 1), 0, NULL, '', 0, 0, NOW(), NOW() );




INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '启用活动', NOW(),
        (select id
         from hp_function
         where name = '限量抢购'
         order by gmt_modified desc limit 1), 0, NULL, '', 0, 0, NOW(), NOW() );


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '编辑活动', NOW(),
        (select id
         from hp_function
         where name = '限量抢购'
         order by gmt_modified desc limit 1), 0, NULL, '', 0, 0, NOW(), NOW() );


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '复制活动', NOW(),
        (select id
         from hp_function
         where name = '限量抢购'
         order by gmt_modified desc limit 1), 0, NULL, '', 0, 0, NOW(), NOW() );



INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '查看活动详情', NOW(),
        (select id
         from hp_function
         where name = '限量抢购'
         order by gmt_modified desc limit 1), 0, NULL, '', 0, 0, NOW(), NOW() );




INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '查看活动统计', NOW(),
        (select id
         from hp_function
         where name = '限量抢购'
         order by gmt_modified desc limit 1), 0, NULL, '', 0, 0, NOW(), NOW() );


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '删除活动', NOW(),
        (select id
         from hp_function
         where name = '限量抢购'
         order by gmt_modified desc limit 1), 0, NULL, '', 0, 0, NOW(), NOW() );



INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '导出活动数据', NOW(),
        (select id
         from hp_function
         where name = '限量抢购'
         order by gmt_modified desc limit 1), 0, NULL, '', 0, 0, NOW(), NOW() );


