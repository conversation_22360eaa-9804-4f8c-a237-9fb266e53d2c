INSERT INTO `gateway`.`hp_function_group`(`create_time`, `name`, `sequence`, `update_time`, `gmt_create`,
                                          `gmt_modified`)
VALUES (now(), '消费有礼', unix_timestamp(), now(), now(), now());



INSERT INTO `gateway`.`hp_function` (create_time, NAME, priority, `type`, update_time, parent_id, system_id, group_id,
                                     gmt_create, gmt_modified, function_identification)
VALUES (CURRENT_TIMESTAMP,
        '消费有礼',
        1,
        60,
        CURRENT_TIMESTAMP,
        26,
        (SELECT id
         FROM hp_system
         WHERE NAME = '营销中心' AND identification = 'sr_marketing_count'
         ORDER BY gmt_modified DESC
         LIMIT 1),
        (SELECT id FROM hp_function_group WHERE NAME = '消费有礼' AND sequence > 0 ORDER BY gmt_modified DESC LIMIT 1),
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP,
        '');



INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '活动列表', NOW(),
        (select id
         from hp_function
         where name = '消费有礼'
         order by gmt_modified desc
         limit 1), 0, NULL, '', 0, 0, NOW(), NOW());


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '新建活动', NOW(),
        (select id
         from hp_function
         where name = '消费有礼'
         order by gmt_modified desc
         limit 1), 0, NULL, '', 0, 0, NOW(), NOW());



INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '发布活动', NOW(),
        (select id
         from hp_function
         where name = '消费有礼'
         order by gmt_modified desc
         limit 1), 0, NULL, '', 0, 0, NOW(), NOW());



INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '暂停活动', NOW(),
        (select id
         from hp_function
         where name = '消费有礼'
         order by gmt_modified desc
         limit 1), 0, NULL, '', 0, 0, NOW(), NOW());



INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '开启活动', NOW(),
        (select id
         from hp_function
         where name = '消费有礼'
         order by gmt_modified desc
         limit 1), 0, NULL, '', 0, 0, NOW(), NOW());


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '编辑活动', NOW(),
        (select id
         from hp_function
         where name = '消费有礼'
         order by gmt_modified desc
         limit 1), 0, NULL, '', 0, 0, NOW(), NOW());


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '复制活动', NOW(),
        (select id
         from hp_function
         where name = '消费有礼'
         order by gmt_modified desc
         limit 1), 0, NULL, '', 0, 0, NOW(), NOW());



INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '查看活动详情', NOW(),
        (select id
         from hp_function
         where name = '消费有礼'
         order by gmt_modified desc
         limit 1), 0, NULL, '', 0, 0, NOW(), NOW());



INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '查看活动订单', NOW(),
        (select id
         from hp_function
         where name = '消费有礼'
         order by gmt_modified desc
         limit 1), 0, NULL, '', 0, 0, NOW(), NOW());


INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '删除活动', NOW(),
        (select id
         from hp_function
         where name = '消费有礼'
         order by gmt_modified desc
         limit 1), 0, NULL, '', 0, 0, NOW(), NOW());



INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '导出统计数据', NOW(),
        (select id
         from hp_function
         where name = '消费有礼'
         order by gmt_modified desc
         limit 1), 0, NULL, '', 0, 0, NOW(), NOW());



INSERT INTO `gateway`.`hp_permission`(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`,
                                      `mobile_identification`, `identification_name`,
                                      `is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES (CURRENT_TIMESTAMP, NULL, '补发功能', NOW(),
        (select id
         from hp_function
         where name = '消费有礼'
         order by gmt_modified desc
         limit 1), 0, NULL, '', 0, 0, NOW(), NOW());