CREATE TABLE `hsa_oper_subject_permission_label`
(
    `id`                int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `guid`              varchar(50) NOT NULL COMMENT 'guid',
    `enterprise_guid`   varchar(50) NOT NULL DEFAULT '' COMMENT '企业guid',
    `position_guid`     varchar(50) NOT NULL COMMENT '岗位id和角色id',
    `oper_subject_guid` varchar(50)          DEFAULT NULL COMMENT '运营主体guid',
    `source_type`       tinyint(4) DEFAULT NULL COMMENT '1：会员管理   2：营销中心',
    `gmt_modified`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `gmt_create`        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `is_role`           tinyint(4) DEFAULT NULL COMMENT '权限类型（0：岗位  1：角色）',
    `label_guid`        varchar(100)         DEFAULT NULL,
    `label_name`        varchar(100)         DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='运营主体标签权限表';



CREATE TABLE `hsa_oper_subject_label_type`
(
    `id`                int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `guid`              varchar(50) NOT NULL COMMENT 'guid',
    `enterprise_guid`   varchar(50) NOT NULL DEFAULT '' COMMENT '企业guid',
    `position_guid`     varchar(50) NOT NULL COMMENT '岗位id和角色id',
    `source_type`       tinyint(4) DEFAULT NULL COMMENT '1：会员管理   2：营销中心',
    `gmt_modified`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `gmt_create`        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `is_role`           tinyint(4) DEFAULT NULL COMMENT '权限类型（0：岗位  1：角色）',
    `is_all`            tinyint(4) DEFAULT NULL COMMENT '设置的类型（1：全部  0：部分）默认为全部',
    `oper_subject_guid` varchar(100)         DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='标签权限类型表（用于判断拥有全部标签还是部分标签）';







-- 导航字段类型修改
ALTER TABLE hsm_member_mall_tool_platform_db.hsa_applet_navigation MODIFY COLUMN select_name text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '页面名字';
