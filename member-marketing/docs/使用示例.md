# HsaMemberGradeChangeDetailService 优化版本使用示例

## 1. 配置文件设置

### application.yml 配置
```yaml
# 会员等级服务配置
member:
  grade:
    service:
      # 启用优化版本服务（默认为true）
      optimized: true
    batch:
      # 批处理大小
      size: 500
    max:
      # 最大重试次数
      retry: 3
    lock:
      # 分布式锁超时时间（秒）
      timeout: 30
    async:
      # 启用异步处理
      enabled: true
    performance:
      # 启用性能监控
      monitor:
        enabled: true

# 线程池配置
spring:
  task:
    execution:
      pool:
        core-size: 8
        max-size: 16
        queue-capacity: 1000
        thread-name-prefix: member-grade-
```

### application.properties 配置
```properties
# 会员等级服务配置
member.grade.service.optimized=true
member.grade.batch.size=500
member.grade.max.retry=3
member.grade.lock.timeout=30
member.grade.async.enabled=true
member.grade.performance.monitor.enabled=true

# 线程池配置
spring.task.execution.pool.core-size=8
spring.task.execution.pool.max-size=16
spring.task.execution.pool.queue-capacity=1000
spring.task.execution.pool.thread-name-prefix=member-grade-
```

## 2. 服务注入和使用

### 基本使用方式
```java
@Service
@Slf4j
public class MemberGradeBusinessService {
    
    // 注入优化版本的服务
    @Resource(name = "hsaMemberGradeChangeDetailServiceOptimized")
    private HsaMemberGradeChangeDetailService gradeChangeService;
    
    /**
     * 处理会员等级变化
     */
    public void processMemberGradeChange(List<String> memberGuids, String operSubjectGuid) {
        try {
            // 1. 查询会员信息
            List<HsaOperationMemberInfo> memberInfos = queryMemberInfos(memberGuids, operSubjectGuid);
            
            // 2. 构建等级变化DTO
            MemberGradeChangeDTO gradeChangeDTO = buildGradeChangeDTO(memberInfos, operSubjectGuid);
            
            // 3. 执行等级变化处理
            gradeChangeService.memberGradeChange(gradeChangeDTO);
            
            log.info("会员等级变化处理完成，处理会员数量：{}", memberInfos.size());
            
        } catch (Exception e) {
            log.error("会员等级变化处理失败", e);
            throw new RuntimeException("会员等级变化处理失败", e);
        }
    }
    
    /**
     * 批量处理大量会员等级变化
     */
    public void batchProcessMemberGradeChange(List<String> memberGuids, String operSubjectGuid) {
        log.info("开始批量处理会员等级变化，会员数量：{}", memberGuids.size());
        
        try {
            // 查询会员信息
            List<HsaOperationMemberInfo> memberInfos = queryMemberInfos(memberGuids, operSubjectGuid);
            
            // 构建等级变化DTO
            MemberGradeChangeDTO gradeChangeDTO = buildGradeChangeDTO(memberInfos, operSubjectGuid);
            
            // 记录开始时间
            long startTime = System.currentTimeMillis();
            
            // 执行等级变化处理（优化版本会自动分批处理）
            gradeChangeService.memberGradeChange(gradeChangeDTO);
            
            // 记录处理时间
            long endTime = System.currentTimeMillis();
            log.info("批量处理完成，耗时：{}ms，处理会员数量：{}", 
                    endTime - startTime, memberInfos.size());
            
        } catch (Exception e) {
            log.error("批量处理会员等级变化失败", e);
            throw new RuntimeException("批量处理失败", e);
        }
    }
    
    /**
     * 查询会员信息
     */
    private List<HsaOperationMemberInfo> queryMemberInfos(List<String> memberGuids, String operSubjectGuid) {
        // 实现查询逻辑
        return hsaOperationMemberInfoMapper.selectList(
                new LambdaQueryWrapper<HsaOperationMemberInfo>()
                        .in(HsaOperationMemberInfo::getGuid, memberGuids)
                        .eq(HsaOperationMemberInfo::getOperSubjectGuid, operSubjectGuid)
        );
    }
    
    /**
     * 构建等级变化DTO
     */
    private MemberGradeChangeDTO buildGradeChangeDTO(List<HsaOperationMemberInfo> memberInfos, String operSubjectGuid) {
        MemberGradeChangeDTO dto = new MemberGradeChangeDTO();
        dto.setHsaOperationMemberInfos(memberInfos);
        dto.setOperSubjectGuid(operSubjectGuid);
        dto.setRoleType(RoleTypeEnum.MEMBER.name());
        dto.setSourceType(SourceTypeEnum.SYSTEM_AUTO.getCode());
        dto.setIsRefresh(BooleanEnum.FALSE.getCode());
        dto.setRemark("系统自动等级变更");
        return dto;
    }
}
```

### 定时任务使用示例
```java
@Component
@Slf4j
public class MemberGradeScheduledTask {
    
    @Resource(name = "hsaMemberGradeChangeDetailServiceOptimized")
    private HsaMemberGradeChangeDetailService gradeChangeService;
    
    @Resource
    private HsaOperationMemberInfoMapper memberInfoMapper;
    
    /**
     * 每日凌晨2点执行会员等级刷新
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void dailyGradeRefresh() {
        log.info("开始执行每日会员等级刷新任务");
        
        try {
            // 查询所有运营主体
            List<String> operSubjectGuids = queryAllOperSubjectGuids();
            
            for (String operSubjectGuid : operSubjectGuids) {
                refreshGradeForOperSubject(operSubjectGuid);
            }
            
            log.info("每日会员等级刷新任务完成");
            
        } catch (Exception e) {
            log.error("每日会员等级刷新任务失败", e);
        }
    }
    
    /**
     * 刷新指定运营主体的会员等级
     */
    private void refreshGradeForOperSubject(String operSubjectGuid) {
        log.info("开始刷新运营主体会员等级，operSubjectGuid：{}", operSubjectGuid);
        
        try {
            // 分页查询会员信息，避免一次性加载过多数据
            int pageSize = 1000;
            int pageNum = 1;
            
            while (true) {
                List<HsaOperationMemberInfo> memberInfos = queryMemberInfosByPage(
                        operSubjectGuid, pageNum, pageSize);
                
                if (CollectionUtils.isEmpty(memberInfos)) {
                    break;
                }
                
                // 构建等级变化DTO
                MemberGradeChangeDTO gradeChangeDTO = new MemberGradeChangeDTO();
                gradeChangeDTO.setHsaOperationMemberInfos(memberInfos);
                gradeChangeDTO.setOperSubjectGuid(operSubjectGuid);
                gradeChangeDTO.setRoleType(RoleTypeEnum.MEMBER.name());
                gradeChangeDTO.setSourceType(SourceTypeEnum.SCHEDULED_TASK.getCode());
                gradeChangeDTO.setIsRefresh(BooleanEnum.TRUE.getCode());
                gradeChangeDTO.setRemark("定时任务等级刷新");
                
                // 执行等级变化处理
                gradeChangeService.memberGradeChange(gradeChangeDTO);
                
                log.info("完成第{}页会员等级刷新，处理数量：{}", pageNum, memberInfos.size());
                
                pageNum++;
                
                // 如果查询结果少于页大小，说明已经是最后一页
                if (memberInfos.size() < pageSize) {
                    break;
                }
            }
            
        } catch (Exception e) {
            log.error("刷新运营主体会员等级失败，operSubjectGuid：{}", operSubjectGuid, e);
        }
    }
    
    private List<String> queryAllOperSubjectGuids() {
        // 实现查询所有运营主体GUID的逻辑
        return memberInfoMapper.selectDistinctOperSubjectGuids();
    }
    
    private List<HsaOperationMemberInfo> queryMemberInfosByPage(String operSubjectGuid, int pageNum, int pageSize) {
        // 实现分页查询逻辑
        return memberInfoMapper.selectByOperSubjectGuidWithPage(operSubjectGuid, pageNum, pageSize);
    }
}
```

## 3. 监控和告警

### 性能监控示例
```java
@Component
@Slf4j
public class GradeChangePerformanceMonitor {
    
    private final MeterRegistry meterRegistry;
    
    public GradeChangePerformanceMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }
    
    /**
     * 记录处理时间
     */
    public void recordProcessingTime(long processingTime, int memberCount) {
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("member.grade.change.duration")
                .description("会员等级变化处理时间")
                .tag("member.count", String.valueOf(memberCount))
                .register(meterRegistry));
        
        // 记录处理的会员数量
        Counter.builder("member.grade.change.count")
                .description("处理的会员数量")
                .register(meterRegistry)
                .increment(memberCount);
    }
    
    /**
     * 记录异常情况
     */
    public void recordException(String exceptionType) {
        Counter.builder("member.grade.change.error")
                .description("会员等级变化处理异常")
                .tag("exception.type", exceptionType)
                .register(meterRegistry)
                .increment();
    }
}
```

## 4. 测试示例

### 单元测试
```java
@RunWith(SpringRunner.class)
@SpringBootTest
public class MemberGradeChangeIntegrationTest {
    
    @Resource(name = "hsaMemberGradeChangeDetailServiceOptimized")
    private HsaMemberGradeChangeDetailService gradeChangeService;
    
    @Test
    public void testBatchProcessing() {
        // 创建测试数据
        List<HsaOperationMemberInfo> memberInfos = createTestMemberInfos(1000);
        
        MemberGradeChangeDTO gradeChangeDTO = new MemberGradeChangeDTO();
        gradeChangeDTO.setHsaOperationMemberInfos(memberInfos);
        gradeChangeDTO.setOperSubjectGuid("test-subject");
        gradeChangeDTO.setRoleType(RoleTypeEnum.MEMBER.name());
        gradeChangeDTO.setSourceType(SourceTypeEnum.SYSTEM_AUTO.getCode());
        gradeChangeDTO.setIsRefresh(BooleanEnum.FALSE.getCode());
        
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        
        // 执行处理
        gradeChangeService.memberGradeChange(gradeChangeDTO);
        
        // 记录结束时间
        long endTime = System.currentTimeMillis();
        
        log.info("批量处理完成，耗时：{}ms", endTime - startTime);
        
        // 验证结果
        // ... 添加验证逻辑
    }
    
    private List<HsaOperationMemberInfo> createTestMemberInfos(int count) {
        // 创建测试会员信息
        List<HsaOperationMemberInfo> memberInfos = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            HsaOperationMemberInfo memberInfo = new HsaOperationMemberInfo();
            memberInfo.setGuid("test-member-" + i);
            memberInfo.setOperSubjectGuid("test-subject");
            memberInfo.setMemberGrowthValue(100 + i);
            memberInfos.add(memberInfo);
        }
        return memberInfos;
    }
}
```

## 5. 故障排查

### 常见问题和解决方案

1. **处理超时**
   - 检查批处理大小配置
   - 检查数据库连接池配置
   - 检查分布式锁超时时间

2. **内存溢出**
   - 减小批处理大小
   - 检查是否有内存泄漏
   - 增加JVM堆内存

3. **死锁问题**
   - 检查事务边界设置
   - 检查数据库索引
   - 调整重试策略

### 日志配置
```yaml
logging:
  level:
    com.holderzone.member.queue.service.grade.impl.HsaMemberGradeChangeDetailServiceOptimizedImpl: DEBUG
    com.holderzone.member.queue.service.grade: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

通过以上配置和示例，可以充分利用优化版本的性能优势，提高系统的处理能力和稳定性。
