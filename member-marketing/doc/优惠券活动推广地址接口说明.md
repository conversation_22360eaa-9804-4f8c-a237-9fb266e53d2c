# 优惠券活动推广地址接口说明

## 概述

本文档描述了新增的优惠券活动推广地址查询和小程序二维码下载功能。

## 新增接口

### 1. 获取活动推广地址

**接口地址：** `GET /coupon_package/promotion_url`

**请求参数：**
- `activityGuid` (String, 必填): 活动GUID

**响应示例：**
```json
{
  "success": true,
  "data": {
    "activityGuid": "activity-guid-123",
    "activityName": "双十一优惠券活动",
    "h5Url": "https://h5.example.com/coupon-activity?activityGuid=activity-guid-123",
    "miniProgramUrl": "pages/coupon-activity/index?activityGuid=activity-guid-123",
    "miniProgramQrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "qrCodeDownloadUrl": "/coupon_package/download_qrcode?activityGuid=activity-guid-123"
  }
}
```

### 2. 下载小程序二维码

**接口地址：** `GET /coupon_package/download_qrcode`

**请求参数：**
- `activityGuid` (String, 必填): 活动GUID

**响应：**
- 直接返回PNG格式的二维码图片文件
- 文件名格式：`{活动名称}_miniprogram_qrcode.png`

## 配置说明

需要在nacos中配置以下参数：

```yaml
promotion:
  url:
    # 优惠券活动H5推广地址
    couponActivityH5Url: "https://h5.example.com/coupon-activity"

    # 优惠券活动小程序推广地址
    couponActivityMiniProgramUrl: "pages/coupon-activity/index"
```

## 使用场景

1. **活动推广页面展示**：在活动管理后台展示H5和小程序推广地址
2. **二维码展示**：在页面上直接显示小程序二维码
3. **二维码下载**：提供二维码图片下载功能，用于线下推广

## 技术实现

- 使用现有的二维码生成工具 `QrCodeSupport` 和 `QrcodeServer`
- 推广地址通过独立的配置类 `PromotionUrlConfig` 动态获取
- 二维码支持Base64格式返回和文件下载两种方式
- 配置与业务逻辑分离，便于维护和扩展

## 注意事项

1. 确保配置文件中的推广地址正确配置
2. 二维码生成依赖Redis缓存，确保Redis服务正常
3. 下载接口会直接返回图片流，前端需要正确处理文件下载
