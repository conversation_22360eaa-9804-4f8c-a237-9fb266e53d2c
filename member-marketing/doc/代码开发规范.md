# 代码开发规范

## 代码

## 数据库

1. 通用字段

```sql
# 根据业务场景来，代码手动赋值更新（部分操作不更新）
`gmt_modified` datetime(3) DEFAULT CURRENT_TIMESTAMP(3)  COMMENT '更新时间',


DROP TABLE IF EXISTS `hsa_xx`;

# isDelete 删除时间，用于删除数据追溯
CREATE TABLE `hsa_xx` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `guid` varchar(50) NOT NULL COMMENT 'guid',
  `gmt_create` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `gmt_modified` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `is_delete` bigint(13) NOT NULL DEFAULT 0 COMMENT '删除标志位，0-未删除，时间-已删除',
    `oper_subject_guid` varchar(50) NOT NULL COMMENT '运营主体GUID',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`),
  KEY `oper_subject_guid_idx` (`oper_subject_guid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='xx';
```

全局配置

```yml
# 全局配置，可能影响之前
mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: isDelete  # 全局逻辑删除的实体字段名
      logic-delete-value: UNIX_TIMESTAMP() # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
```

非全局生效

```java
/**
 * 删除标识: 0.未删除, 时间戳.删除，
 */
@TableLogic(value = "0", delval = "UNIX_TIMESTAMP()")
private Long isDelete;
```

2. 