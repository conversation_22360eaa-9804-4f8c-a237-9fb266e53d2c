# 根据企业guid查询用户信息（不需要）
入参：
```
|  字段   | 类型  | 是否必填 | 说明   |
| enterpriseGuid  | String |  是  | 企业guid  |
```
出参：
```
|  字段   | 类型  | 说明   |
|  operSubjectGuid   | String  | 运营主体   |
|  enterpriseGuid   | String  | 登陆用户的企业GUID   |
|  storeGuid   | String  | 登陆用户的门店GUID   |
|  userGuid   | String  | 登陆用户GUID   |
|  userName   | String  | 登陆用户名称   |
|  account   | String  | 登陆用户帐号   |
|  tel   | String  | 用户电话   |
|  userAccount   | String  | 用户账号   |
```



# 根据企业guid和token查询用户信息
入参：
```
|  字段   | 类型  | 是否必填 | 说明   |
| enterpriseGuid  | String |  是  | 企业guid  |
| token  | String |  是  | 用户token  |
```
出参：
```
|  字段   | 类型  | 说明   |
|  operSubjectGuid   | String  | 运营主体   |
|  enterpriseGuid   | String  | 登陆用户的企业GUID   |
|  storeGuid   | String  | 登陆用户的门店GUID   |
|  userGuid   | String  | 登陆用户GUID   |
|  userName   | String  | 登陆用户名称   |
|  account   | String  | 登陆用户帐号   |
|  tel   | String  | 用户电话   |
|  userAccount   | String  | 用户账号   |
```


# 根据主体和token查询业务数据
入参：
```
|  字段   | 类型  | 是否必填 | 说明   |
| operSubjectGuid  | String |  是  | 主体guid  |
| token  | String |  是  | 用户token  |
```

出参：
```
|  字段   | 类型  | 说明   |
|  teamId   | String  | 所属企业   |

```


# 根据企业查询主体列表（不需要）

入参：
```
|  字段   | 类型  | 是否必填 | 说明   |
| enterpriseGuid  | String |  是  | 企业guid  |
```

出参：
```
|  字段   | 类型  | 说明   |
|  operSubjectGuid   | String  | 主体guid   |
|  multiMemberName   | String  | 成员名称   |
|  multiMemberStatus   | Integer  | 数据状态   |

```


# 根据企业查询业务系统地址

入参：
```
|  字段   | 类型  | 是否必填 | 说明   |
| enterpriseGuid  | String |  是  | 企业guid  |
```

出参：
```
|  字段   | 类型  | 说明   |
|  url   | String  | 获取绑定小程序地址   |

```

# 查询所有主体信息（不需要）
（初始化老运营主体相关数据）
入参：无

出参：
```
|  字段   | 类型  | 说明   |
|  id   | List<String>  | 主体id列表   |

```

# 判断用户是否有对应功能权限

入参：
```
|  字段   | 类型  | 是否必填 | 说明   |
| teamId  | String |  是  | 企业id  |
| userId  | String |  是  | 用户id  |
| identifications  | List<String> |  是  | 系统权限标识member_marketing：会员管理 marketing_count：营销中心 equities_center: 权益中心  |
| permissionName  | String |  否  | 权限类型名称  |
| functionName  | String |  否  | 主体  |
| token  | String |  是  | 用户token  |
```

出参：
```
|  字段   | 类型  | 说明   |
|  true   | Boolean  | true/false   |

```

# 查询用户权限列表

入参：
```
| account  | String |  否  | 登录账户  |
| teamId  | String |  是  | 企业id  |
| userId  | String |  是  | 用户id  |
| identifications  | List<String> |  是  | 系统权限标识member_marketing：会员管理 marketing_count：营销中心 equities_center: 权益中心  |
| permissionName  | String |  否  | 权限类型名称  |
| functionName  | String |  否  | 主体  |
| token  | String |  是  | 用户token  |
```
出参：
```
|  字段   | 类型  | 说明   |
|  id   | Integer  | 分组id|
|  name   | String  | 分组名称   |
|  enterpriseGuid   | String  | 登陆用户的企业GUID   |
|  isFormPermissions   | Integer  | 是否报表权限   |
|  functionModels   | List<FunctionModelDTO>  | 功能集合   |
                    |  id   | String  | id   |
                    |  functionName   | String  | 功能名称   |
                    |  isFormPermissions   | Integer  | 是否报表权限 0 不是 1是  默认0   |
                    |  permissionModels   | List<PermissionModelDTO>  | 功能权限集合   |
                                        |  id   | String  | 权限id   |
                                        |  permissionName   | String  | 权限名称   |
                                        |  isFormPermissions   | Integer  | 是否报表权限 0 不是 1是  默认0   |
                                        |  isChecked   | Integer  | 是否选中 0 未选中 1 选中 -1 审批中   |

```



# 查询用户角色

入参：
```
|  字段   | 类型  | 是否必填 | 说明   |
| userId  | String |  是  | 用户id  |
| isRole  | Integer |  是  | 权限类型参数  0 = 岗位 1 = 角色  |
| teamId  | String |  是  | 企业id  |
| token  | String |  是  | 用户token  |
```

出参：
```
|  字段   | 类型  | 说明   |
|  id   | List<String>  | 角色列表   |

```


# 查询用户角色权限
入参：
```
|  字段   | 类型  | 是否必填 | 说明   |
| account  | String |  否  | 登录账户  |
| teamId  | String |  是  | 企业id  |
| userId  | String |  是  | 用户id  |
| identifications  | List<String> |  是  | 系统权限标识member_marketing：会员管理 marketing_count：营销中心 equities_center: 权益中心  |
| permissionName  | String |  否  | 权限类型名称  |
| functionName  | String |  否  | 主体  |
| token  | String |  是  | 用户token  |
```

出参：
```
|  字段   | 类型  | 说明   |
|  roleMap   | Map<String, List<MemberSystemPermissionDTO>>  | 岗位权限   |
             |  id   | Integer  | 分组id   |
             |  name   | String  | 分组名称   |
             |  isFormPermissions   | Integer  | 是否报表权限 0 不是 1是  默认0   |
             |  functionModels   | List<FunctionModelDTO>  | 功能集合   |
                                 |  id   | String  | id   |
                                 |  functionName   | String  | 功能名称   |
                                 |  isFormPermissions   | Integer  | 是否报表权限 0 不是 1是  默认0   |
                                 |  permissionModels   | List<PermissionModelDTO>  | 功能权限集合   |
                                                       |  id   | String  | 权限id   |
                                                       |  permissionName   | String  | 权限名称   |
                                                       |  isFormPermissions   | Integer  | 是否报表权限 0 不是 1是  默认0   |
                                                       |  isChecked   | Integer  | 是否选中 0 未选中 1 选中 -1 审批中   |


|  entRoleMap   | Map<String, List<MemberSystemPermissionDTO>>  | 角色权限   |
                |同上|    
```


# 获取系统权限列表
入参：
```
|  字段   | 类型  | 是否必填 | 说明   |
| account  | String |  否  | 登录账户  |
| teamId  | String |  是  | 企业id  |
| userId  | String |  是  | 用户id  |
| identifications  | List<String> |  是  | 系统权限标识member_marketing：会员管理 marketing_count：营销中心 equities_center: 权益中心  |
| permissionName  | String |  否  | 权限类型名称  |
| functionName  | String |  否  | 主体  |
```

出参：
```
|  字段   | 类型  | 说明   |
|  roleMap   | Map<String, List<MemberSystemPermissionDTO>>  | 岗位权限   |
             |  id   | Integer  | 分组id   |
             |  name   | String  | 分组名称   |
             |  isFormPermissions   | Integer  | 是否报表权限 0 不是 1是  默认0   |
             |  functionModels   | List<FunctionModelDTO>  | 功能集合   |
                                 |  id   | String  | id   |
                                 |  functionName   | String  | 功能名称   |
                                 |  isFormPermissions   | Integer  | 是否报表权限 0 不是 1是  默认0   |
                                 |  permissionModels   | List<PermissionModelDTO>  | 功能权限集合   |
                                                       |  id   | String  | 权限id   |
                                                       |  permissionName   | String  | 权限名称   |
                                                       |  isFormPermissions   | Integer  | 是否报表权限 0 不是 1是  默认0   |
                                                       |  isChecked   | Integer  | 是否选中 0 未选中 1 选中 -1 审批中   |


|  entRoleMap   | Map<String, List<MemberSystemPermissionDTO>>  | 角色权限   |
                |同上|    
```

# 根据账户获取临时token

入参：
```
|  字段   | 类型  | 是否必填 | 说明   |
| account  | String |  是  | 登录账户（手机号）  |

```

出参：
```
|  字段   | 类型  | 说明   |
|  token   | String  | 用户token   |

```


# 发送验证码
入参：

```
|  字段   | 类型  | 是否必填 | 说明   |
| type  | Integer |  是  | 0 短信 1 邮箱  |

```

出参：
```
|  字段   | 类型  | 说明   |
|  returnCode   | int  | 返回code不为0 及为成功  |

```

# 查询验证码
入参：
```
|  字段   | 类型  | 是否必填 | 说明   |
| code  | String |  是  | 验证码  |
| info  | String |  是  | 数据  |

```

出参：
```
|  字段   | 类型  | 说明   |
|  returnCode   | int  | 返回code为0 及为成功  |

```

# 查询用户角色

入参：
```
|  字段   | 类型  | 是否必填 | 说明   |
| enterpriseGuid  | String |  是  | 登陆用户的企业GUID  |
| tel  | String |  是  | 用户电话  |

```

出参：
```
|  字段   | 类型  | 说明   |
|  postIds   | List<Long>  | 岗位id  |
|  entRoleIds   | List<Long>  | 角色id  |

```
# 通过企业查询运营主体
入参：
```
|  字段   | 类型  | 是否必填 | 说明   |
| teamId  | String |  是  | 企业id  |

```

出参：
```
|  字段   | 类型  | 说明   |
|  data  | List<OperSubjectInfo>  | 通过企业查询运营主体   |
                    |  operSubjectGuid   | String  | 主体guid   |
                    |  multiMemberName   | String  | 成员名称   |
                    |  multiMemberStatus   | Integer  | 数据状态   |

```

# 获取系统权限列表
入参：
```
|  字段   | 类型  | 是否必填 | 说明   |
| account  | String |  否  | 登录账户  |
| teamId  | String |  是  | 企业id  |
| userId  | String |  是  | 用户id  |
| identifications  | List<String> |  是  | 系统权限标识member_marketing：会员管理 marketing_count：营销中心 equities_center: 权益中心  |
| permissionName  | String |  否  | 权限类型名称  |
| functionName  | String |  否  | 主体  |
```

出参：
```
|  字段   | 类型  | 说明   |
|  dataList  | List<MemberSystemPermissionDTO>  | 获取系统权限列表   |
         | id  | Integer | 分组id  |
         | name  | String | 分组名称  |
         | isFormPermissions  | Integer | 是否报表权限 0 不是 1是  默认0  |
         | functionModels  | List<FunctionModelDTO> | 功能集合  |
                           | id  | String | id  |
                           | functionName  | String | 功能名称  |
                           | isFormPermissions  | Integer | 是否报表权限 0 不是 1是  默认0  |
                           | permissionModels  | List<PermissionModelDTO> | 功能权限集合  |
                                               | id  | String | 权限id  |
                                               | permissionName  | String | 权限名称  |
                                               | isFormPermissions  | Integer | 是否报表权限 0 不是 1是  默认0  |
                                               | isChecked  | Integer | 是否选中 0 未选中 1 选中 -1 审批中  |
```


# 查询用户角色
入参：
```
|  字段   | 类型  | 是否必填 | 说明   |
| enterpriseGuid  | String |  是  | 登陆用户的企业GUID  |
| tel  | String |  是  | 用户电话  |

```

出参：
```
|  字段   | 类型  | 说明   |
|  postIds   | List<Long>  | 岗位id  |
|  entRoleIds| List<Long>  | 角色id  |

```



# 查询商店分页信息

入参：
```
|  字段   | 类型  | 是否必填 | 说明   |
|  teamId   | Long  | 是 | 企业id  |
|  keywords | String| 是 | 关键字（门店名称、门店编号）  |
|  pageNo   | int  | 是 | 分页页码  |
|  pageSize | int  | 是 | 分页长度  |
```

出参：
```
|  字段   | 类型  | 说明   |
|  id   | Long  | id  |
|  name   | String  | 组织架构名称  |

```

# 查询用户下权限范围
入参：
```
|  字段   | 类型  | 是否必填 | 说明   |
|  teamId   | Long  | 是 | 企业id  |
|  account | String| 是 | userAccount  |
|  permissionType   | Integer  | 是 | permissionType  |
|  typeId | Long  | 是 | 登陆用户的企业GUID  |
|  token | String  | 是 | 用户token  |
```

出参：
```
|  字段   | 类型  | 说明   |
|  dataList   | List<String>  | 查询用户下权限范围  |

```

# 通过账号获取临时token
入参：
```
|  字段   | 类型  | 是否必填 | 说明   |
|  phoneNum | String| 是 | 用户手机号  |
```

出参：
```
|  字段   | 类型  | 说明   |
|  data   | String  | 返回token  |

```