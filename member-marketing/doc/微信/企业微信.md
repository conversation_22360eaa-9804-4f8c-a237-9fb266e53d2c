## 企业微信相关流程及配置流程

企业微信目前对接了两种模式：1.企业内部开发；2.服务商代开发
-   企业内部开发模式对接文档：https://developer.work.weixin.qq.com/document/path/90664
-   服务商代开发模式对接文档：https://developer.work.weixin.qq.com/document/path/97159

## 为什么同时存在两种模式

因为企微对接在最开始只对接了企业内部开发模式，上线后提供给何师使用。但后续其他商户也需使用，故对接服务商代开发模式。
因最大程度的不影响何师正在使用的导客单活动，故在接口中对两种模式做了兼容处理。

## 两种模式如何兼容

- **企业内部开发配置**
  ![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736836291457_ad26a.png)
- **服务商代开发模式配置**
  ![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736837772668_1e00c.png)
- **线上环境何师企业下何师烧烤运营主体（operSubjectGuid：200814100559447054）使用企业内部开发模式，其他运营主体使用服务商代开发模式。**
- **测试环境赵氏企业下赵江VIP主体运营主体（operSubjectGuid：2010121440477930009）使用企业内部开发模式，其他运营主体使用服务商代开发模式。**

## 企微配置流程(服务商代开发)

### 1、企微扫码授权。

#### 1.1 打开小程序配置
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736838268404_4870d.png)
#### 1.2 使用企业微信扫码授权
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736838357000_ca62c.png)
#### 1.3 用户扫码
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736838600127_c27b8.png)
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736838643903_d5545.png)
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736838721382_18b3d.png)
#### 1.4 授权成功
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736838799199_cfd98.png)
#### **注意：授权成功之后还不能使用导客单应用，需继续完成下面步骤**

### 2、服务商后台上线应用
#### 企微服务商后台地址：https://open.work.weixin.qq.com/wwopen/login
#### 2.1 开始代开发应用
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736839232191_1716b.png)
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736839628571_458cb.png)
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736839447461_40e10.png)
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736839533762_4a54d.png)
#### 2.2 配置权限
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736839798930_bc5a7.png)
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736839889011_7ae2b.png)
#### 2.3 用户企业微信同意变更权限
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736840152098_13c79.png)
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736840197479_ee233.png)
#### 2.4 代开发应用上线
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736840315214_825db.png)
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736840373882_64a6e.png)
#### 2.5 应用上线(审核大概6-10分钟)
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736840875983_39fdd.png)
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736840933077_c9483.png)
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736840989327_6d66e.png)

### 3、用户企业微信管理后台修改可见范围
#### 企业微信管理后台：https://work.weixin.qq.com/wework_admin/loginpage_wx
#### 3.1 修改应用可见范围(此处可见范围影响导客单可以配置哪些员工，建议直接使用最高级组织架构)
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736841083486_d14c5.png)
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736841163056_c8195.png)
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736841281527_76f61.png)
#### **注意：修改完可见范围之后导客单活动后台配置可以正常使用，若需要发送欢迎语，则继续完成下面步骤**

### 4、服务商后台购买接口许可并激活账号
#### 企微服务商后台地址：https://open.work.weixin.qq.com/wwopen/login
#### 4.1 购买接口许可
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736841655636_4954c.png)
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736841720156_883da.png)
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736842065866_f2fd5.png)
#### 4.2 激活账号
##### 4.2.1 关闭自动激活，选择手动激活
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736842192469_19a0f.png)
##### 4.2.2 上传激活模板
填写须知：
1. 第一列为必填项，填写需要绑定账号的员工userid(userid可以根据员工列表接口获取或者导客单配置表中获取)，支持填写明文userid或加密userid
2. 第二列为必填项，填写账号类型，其中：0代表基础账号，1代表互通账号
3. 填写完成并上传该文件后，即将员工userid同企业下剩余可绑定的账号进行自动绑定
4. 上传前，务必保留表头 员工userid 和 账号类型
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736842348811_ce10f.png)
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736842478435_55fa5.png)
![image](https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2025-01-14/1736842632979_0a32f.png)
#### **注意：激活成功之后对应的员工则可以发送欢迎语**