# 全局类型

1. 系统
   1. 会员
   2. 好搭档
   3. 食堂
   4. 门店
   5. ...
2. 来源终端
   1. 后台
   2. 小程序
   3. 一体机
   4. 充值机
   5. POS
   6. ...
3. 

# 结算中心接口

1. 会员支付（登录了会员的所有支付都调用）责任链模式：组合支付
   1. 现金支付（生成记录和权益）
   2. 聚合支付（生成记录和权益）
   3. 挂账支付
   4. 卡支付
2. 会员退款
   1. 退现金、聚合
   2. 退挂账
   3. 退卡余额
      1. 退优惠方式
   4. 退权益
      1. 成长值
      2. 积分
      3. 标签
3. 优惠方式列表
   1. 按主体保存优惠顺序列表，组合
4. 优惠最优推荐
   1. 
5. 计算会员价（责任链模式）
   1. 优惠劵（商品兑换券、代金券、折扣劵）
   2. v 商品会员价
   3. v 会员折扣
   4. 满减
   5. 满折
   6. 随性红包
   7. v 积分抵现
   8. ...
6. 优惠项分摊到商品
   1. 
7. 支付账户列表
8. 统一获取会员卡余额
9. 统一权益生成
   1. 成长值
   2. 积分
   3. 标签


# 现有接口涉及场景
1. 批量订单：预定使用
2. 权益刷新
   1. 成长值
   2. 积分
   3. 标签