
## 资产查询sql

```sql

-- 消费记录
select c.order_number,c.refund_time,c.guid memberConsumptionGuid,c.is_complete,c.* from hsa_member_consumption c where order_number = '3545947832401823750';
-- 支付订单
select IF(c.refund_time is null,'结账','退款') '订单状态',c.order_time,c.order_number,c.order_type,c.order_source,c.member_info_guid,'' as memberPhone,c.store_guid,c.store_name,c.order_discount_amount,c.order_paid_amount
 from hsa_member_consumption c where oper_subject_guid = '87' and order_number = '3539764944388001798' and is_cancel = 0;

-- 优惠锁定（其他）
select d.coupon_rollback,d.discount_state ,d.* from hsa_member_order_discount d where oper_subject_guid = '87' order by id desc limit 5;

-- 券改回可用
-- update hsa_member_coupon_link set state = 3 where member_guid  = '6990232203686838272';
select l.state,l.* from hsa_member_coupon_link l where  member_guid = '6990232203686838272' order by gmt_modified desc;
-- 券使用
select u.order_number,u.* from hsa_member_coupon_use u where gmt_create > CURDATE()  order by id desc limit 10;

-- 折扣限额
select d.order_number,d.* from hsa_member_grade_price_detail d where oper_subject_guid = '87'and member_info_guid = '6990232203686838272' and gmt_create > CURDATE()  order by id desc limit 10;
-- 异常数据
select * from hsa_member_grade_price_detail where discounted_price is null;

-- 积分 430
select o.member_integral,o.* from hsa_operation_member_info o where guid = '6990232203686838272';
-- 积分明细
-- delete from hsa_integral_detail  where member_info_guid = '6990232203686838272';
select d.order_number,d.integral_validity,d.integral_validity_date,d.record_remain_integral,IF (d.integral_type = 0,d.integral,-d.integral) integral,d.current_integral,d.* from hsa_integral_detail d where oper_subject_guid = '87'and member_info_guid = '6990232203686838272' and gmt_create > CURDATE()  order by id desc;
-- 积分消费记录（退款删除）
select d.order_number,d.* from hsa_member_integral_deduct_detail d where oper_subject_guid = '87' and member_info_guid = '6990232203686838272'  and gmt_create > CURDATE() order by id desc limit 10;
-- 积分消费记录（即将过期记录）
select * from hsa_member_integral_deduct_list where integral_deduct_guid = '7143544355783966720';

-- 限购订单 
select o.is_delete,o.order_number ,o.consumption_order_guid,o.* from hsa_purchase_order o where member_info_guid = '6990232203686838272' and gmt_create > curdate() order by gmt_modified desc;
-- 限购商品
select * from hsa_purchase_order_commodity hpoc ;
```