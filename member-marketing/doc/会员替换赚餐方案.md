# 会员替换赚餐方案

## 问题

1. 门店管理后台：会员营销是嵌入？还是跳转新界面？界面风格是否统一
2. 需要考虑数仓新会员结构同步
3. 运营主体：门店的主体在云平台，食堂的主体在运营平台，同时使用
4. 会员数据量过大（162W会员），钱以后新会员需要优化查询

## 数据迁移：具体方案产品来确定

1. **账户基础信息，**数据量过大（162W会员），需要优化查询
   
   ``注册账户：是否需要区分食堂、门店（区分数据、业务）？食堂、门店会员是否共用或不公用？存在手机号相同的运营主体不同的会员``
   
1. 顾客人群（标签）

   ``先迁移会员数据、标签配置（手动标签、自动标签），根据新会员再写批量关联标签操作（不能使用老会员的关联记录，新会员较老会员逻辑有变化）``

1. 消费记录（消费订单、充值订单）

   ``根据商户需要看是否迁移``

1. 余额明细（会员卡、会员卡余额明细、补贴明细）

   ``会员卡配置迁移，明细迁移【不要历史明细，直接生成一条最新迁移记录】主卡、权益卡、食堂卡的卡设置，卡发放数据、卡交易明细、余额明细，如何做迁移？``

1. 成长记录、等级权益

1. 积分明细、

1. **新系统未做**：第三方授权（支付宝、微信）、权益卡、食堂卡、优惠劵

1. 挂账账户、挂账明细（门店库迁移到会员库）

1. **会员标签**：先迁移会员数据、标签配置（手动标签、自动标签），再写批处理生成关联记录，不能使用老会员的关联记录（新会员较老会员逻辑有变化）

1. **会员卡**：

1. 


## 常用sql

1. 运营主体查询（云平台）
   select * from hse_enterprise_db.hse_enterprise_multi_member;

## 新会员对接外部接口：crm、store食堂、Holder...

| 外部调用  | url                                              | 描述                             | 是否对接 | 服务      |
| --------- | ------------------------------------------------ | -------------------------------- | -------- | --------- |
| store调用 | /type_config/callback_business_type              | 新增适用业务类型信息             | 否       | base      |
| store调用 | /ter-card/order_rights_callback                  | 消费权益回调                     | 是       | base      |
| store调用 | /applets/card/order_generate_callback_processing | 订单生成回调处理                 | 是       | base      |
| store调用 | /hsa_follow/red_packet_order_settle_call_back    | 订单结算回调红包参数             | 是       | marketing |
| store调用 | /hsa_follow/order_rotary_table_call_back         | 转台回调                         | 是       | marketing |
| store调用 | /applets/card/save_member_grade_price_detail     | 关联累计订单                     | 是       | base      |
| store调用 | /applets/card/pay_order                          | 小程序会员卡支付                 | 是       | base      |
| store调用 | /applets/card/pay_recharge                       | 小程序会员卡充值                 | 是       | base      |
| store调用 | /applets/card/calculate_member_price_commodity   | 计算订单折扣                     | 是       | base      |
| store调用 | /ter-card/card_pay_order                         | 会员实体卡支付                   | 是       | base      |
| store调用 | /ter-card/check_is_card_exist                    | 校验卡是否存在                   | 是       | base      |
| store调用 | /ter-card/produce_physical_card_number           | 生成实体卡卡号                   | 是       | base      |
| store调用 | /ter-card/regenerate_physical_card_number        | 重新生成实体卡号                 | 是       | base      |
| store调用 | /ter-card/is_binding_member_card                 | 校验卡是否绑定用户               | 是       | base      |
| store调用 | /ter-card/card_activate                          | 实体卡激活                       | 是       | base      |
| store调用 | /ter-card/update_card_status                     | 实体卡更改卡状态                 | 是       | base      |
| store调用 | /ter-card/update_write_card_status               | 更新实体卡写卡状态信息           | 是       | base      |
| store调用 | /ter-card/binding_member_info                    | 实体卡绑定账户                   | 是       | base      |
| store调用 | /ter-card/login_member_card                      | 登录获取账号卡信息               | 是       | base      |
| store调用 | /ter-card/card_pay_order                         | 会员实体卡支付                   | 是       | base      |
| store调用 | /ter-card/check_card_pay_password                | 密码校验                         | 是       | base      |
| store调用 | /ter-card/check_card_pay_password_rule           | 密码规则校验                     | 是       | base      |
| store调用 | /ter-card/recharge                               | 会员卡充值                       | 是       | base      |
| store调用 | /ter-card/cash_pay_order                         | 支付记录                         | 是       | base      |
| store调用 | /ter-card/query_card_details                     | 一体机查询会员卡详情             | 是       | base      |
| store调用 | /ter_member/add                                  | 一体机会员新增                   | 是       | base      |
| store调用 | /login                                           | 一体机会员登录                   | 是       | base      |
| store调用 | /get_detail                                      | 一体机会员基础信息查看           | 是       | base      |
| store调用 | /update_state                                    | 修改会员账户状态（启用或者禁用） | 是       | base      |
| store调用 | /update_by_member_guid                           | 编辑更新顾客信息                 | 是       | base      |
| store调用 | /get_member_property                             | 一体机获取会员资产               | 是       | base      |
|           |                                                  |                                  |          |           |
| crm调用   | /applets/card/get_member_price_commodity         | 获取会员等级折扣商品             | 是       | base      |