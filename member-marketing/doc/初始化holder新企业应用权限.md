会员将所有应用加到holder中，需要输出以下sql到holder中执行，并调用刷新权限接口



### holder应用权限添加

![1645584909222](初始化holder新企业应用权限/holder权限.png)

use gateway;

1、新增业务系统

```sql
-- 业务系统
INSERT INTO hp_system
(address, create_time, description, identification, login_type, name, single, status, support_type, update_time, version, gmt_create, gmt_modified, display)
VALUES('https://member-tool.holderzone.com', CURRENT_TIMESTAMP, null, 'member_tool', null, '小程序基础配置',null, 0, null, CURRENT_TIMESTAMP,NULL, CURRENT_TIMESTAMP,
CURRENT_TIMESTAMP, 1);
```
***HolderPermissionSqlGenerator 可生成以下sql***

2、新增功能模块



```sql
-- 功能模块
INSERT INTO hp_function_group
(create_time, name, `sequence`, update_time, gmt_create, gmt_modified, group_identification)
VALUES(CURRENT_TIMESTAMP, '小程序装修', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, null);
```

3、权限类型

```sql
-- 权限类型
INSERT INTO hp_function
(create_time, name, priority, `type`, update_time, parent_id, system_id, group_id, gmt_create, gmt_modified, function_identification)
VALUES(CURRENT_TIMESTAMP, '小程序', 1, 60, CURRENT_TIMESTAMP,26,
( SELECT id from hp_system WHERE name = '小程序基础配置' AND identification = 'member_tool' ORDER BY gmt_modified DESC LIMIT 1 ),
(select id from hp_function_group where name = '小程序装修' order by gmt_modified desc limit 1),
CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '');
```

4、权限

```sql
-- 权限
INSERT INTO hp_permission(`create_time`, `identification`, `name`, `update_time`, `function_id`, `priority`, `mobile_identification`, `identification_name`,
`is_product`, `permission_type`, `gmt_create`, `gmt_modified`)
VALUES
(CURRENT_TIMESTAMP, NULL, '小程序管理范围', NOW(),
(select id from hp_function where name = '小程序' order by gmt_modified desc limit 1), 0, NULL, '', 0, 0,NOW(), NOW());
```

5、已有企业增加应用

```sql
-- 指定企业增加系统（每个企业都要加，只加一次） 2570为holder企业id
 INSERT INTO hp_company_system
 (company_id, system_id, create_time, gmt_create, gmt_modified)
 VALUES(2570, (select id from hp_system where name = '小程序基础配置' order by gmt_modified desc limit 1), CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
```

### 还需增加到holder系统应用中

![image-20230228161818880](初始化holder新企业应用权限/holder应用.png)

```sql
-- 应用
INSERT INTO team.app_info
(name, relation_id, `type`, description, logo, is_third)
VALUES('会员商城', (select id from gateway.hp_system hs where identification = 'member_mall' limit 1), 1, '', '', 1)
,('小程序基础配置', (select id from gateway.hp_system hs where identification = 'member_tool' limit 1), 1, '', '', 1);

-- 应用在分类下：6 市场销售
INSERT INTO team.app_info_relation_type (app_info_id, app_type_id)
select id,6 from team.app_info where name in ('会员商城','小程序基础配置');
```





### 字段权限增加弹窗

![](初始化holder新企业应用权限/holder字段权限.png)

参考文档 

[初始化holder新企业应用权限/holder添加数据权限范围接口.doc]: 初始化holder新企业应用权限/holder添加数据权限范围接口.doc

POST 测试环境 http://gateway.hp.goalgo.cn:42770/team/permission/dataPermissionType/add

这接口只能新增，不能更新
```json
入参 body raw(json)
{
    "permissionId": 5505, //权限id
    "settingType": 2,//弹窗类型：0组织，1字段，2树形
    "isFormPermission": 0,
    "url": "https://member-mall-test.holderzone.cn/mall/member_permission/get_mall_oper_subject",//查询接口
    "saveUrl": "https://member-mall-test.holderzone.cn/mall/member_permission/update_mall_oper_subject",//更新接口
    "message": "运营主题数据范围",//权限名称
    "shortMessage": "运营主题",//
    "businessType": "",
    "dataPermissionTypeKey": "",
    "systemName": "MEMBER", //会员的都传这个
    "hasAll": 1
}
```

```dtd
-- 检查字段权限
use team;

select pt.message,pt.permission_id,pt.setting_type,pto.* from 
        data_permission_type_outside pto 
        left join data_permission_type pt on pto.type_id = pt.id
where system_name = 'MEMBER' 
-- and pt.permission_id = 2745
order by query_url;

-- 好搭档应用
--   select * from gateway.hp_system hs where name like '%好搭档%';

-- 好搭档权限
-- select * from team.data_permission_type_outside where query_url like 'https://hddmember.schdd.com%'

```


### 刷新权限，正式生效

以上权限模块插入后，需要调用接口刷新redis，使权限正式生效

线上环境刷新 
POST https://goalgo-gateway.holderzone.com/team/permission/refreshRedisTeamPermission

测试环境刷新 
POST http://gateway.hp.goalgo.cn:42770/team/permission/refreshRedisTeamPermission

### 新企业需要增加所有应用

```sql
-- 企业id、应用id
-- 128：会员管理，141营销中心，157权益中心，172会员商城，173小程序装修，169老板助手
insert into gateway.hp_company_system (company_id,system_id) values (4091,128),(4091,141),(4091,157),(4091,172),(4091,173);
```

### 新商户需要报表，联系holder

```dtd
holder配置：按企业添加，会员模块

https://metabase.holderzone.com/dashboard/仪表盘id

id: 2446, name: 会员充值明细
id: 2457, name: 会员卡余额变动明细
id: 2450, name: 会员汇总统计
id: 2447, name: 会员消费明细,
id: 2456, name: 会员消费汇总统计(汇总表)
id: 2461, name: 充值统计(标签)
id: 2462, name: 消费统计(标签)

-- 2023-04-17 增加 4091 郫都菁弘投资食堂项目 会员报表
```