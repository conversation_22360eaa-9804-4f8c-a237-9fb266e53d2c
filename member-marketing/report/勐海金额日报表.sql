--------------------------------------- MataBase: 勐海金额日报表

-- 主表
with main_tb as (
    select to_char(dates.val,'yyyy-mm-dd') as day from generate_series({{startTime}}, {{endTime}}, interval '1 day') dates(val)
),
-- 卡余额
 card_amount_tb as (
select
    sum(mc.subsidy_amount + mc.card_amount + mc.gift_amount) as card_amount
    from hsm_member_marketing_platform_db.hsa_member_info_card mc
    where mc.oper_subject_guid = '54'
),
-- 卡当日余额
card_amount_today as (
    select
    record_date,
    COALESCE(sum(mc.subsidy_amount + mc.card_amount + mc.gift_amount),0) as card_amount
    from hsm_member_marketing_platform_db.hsa_member_info_card_total mc
    where mc.oper_subject_guid = '54'
    and mc.record_date between to_char({{startTime}}, 'YYYY-MM-DD') and to_char({{endTime}}, 'YYYY-MM-DD')
    group by record_date
),
-- 会员卡消费信息
 member_c_tb as (
select to_char(hc.consumption_time::DATE, 'YYYY-MM-DD') as day,
SUM(hc.order_paid_amount) as "总刷卡消费额",
SUM(case when pw.pay_way = 3 and  hc.store_guid = '9'  then hc.order_paid_amount ELSE 0 end) as "食堂刷卡消费",
SUM(case when pw.pay_way = 3 and  hc.store_guid = '10'  then hc.order_paid_amount ELSE 0 end) as "商超刷卡消费"
from hsm_member_marketing_platform_db.hsa_member_consumption hc join hsm_member_marketing_platform_db.hsa_member_consumption_pay_way pw on hc.guid = pw.consumption_guid
where hc.member_info_card_guid is not null
and hc.store_guid in ('9','10')
and hc.oper_subject_guid = '54'
and hc.consumption_type = 1
and hc.consumption_time BETWEEN {{startTime}} AND {{endTime}}
and hc.refund_time is null
and hc.is_complete = 1
and hc.is_cancel  = 0
and hc.member_info_guid is not null

group by day order by day
),
-- 会员充值信息
 member_r_tb as(
select t1.day, sum(t1."饭卡充值合计") as "饭卡充值合计",sum(t1."现金充值") as "现金充值",sum(t1."微信充值") as "微信充值" from (
select pw.pay_way,to_char(hc.consumption_time::DATE, 'YYYY-MM-DD') as day,
SUM(hc.order_paid_amount) as "饭卡充值合计",
case pw.pay_way
when 0 then sum(hc.order_paid_amount) end as "现金充值",
case  pw.pay_way
when 1 then sum(hc.order_paid_amount)  end as "微信充值"
from hsm_member_marketing_platform_db.hsa_member_consumption hc join hsm_member_marketing_platform_db.hsa_member_consumption_pay_way pw on hc.guid = pw.consumption_guid
where hc.member_info_card_guid is not null
and hc.consumption_type = 0
and hc.oper_subject_guid = '54'
and hc.consumption_time BETWEEN {{startTime}} AND {{endTime}}
and hc.is_complete = 1
group by day,
pw.pay_way
order by
day
) t1
group by
t1.day
),
-- 食堂消费信息
canteen_c_tb as(
select
	t1.day,
	sum(t1."总微信消费额") as "总微信消费额",
	sum(t1."总现金消费额") as "总现金消费额",
	sum(t1."食堂微信消费") as "食堂微信消费",
	sum(t1."食堂现金消费") as "食堂现金消费",
	sum(t1."商超微信消费") as "商超微信消费",
	sum(t1."商超现金消费") as "商超现金消费"
from
	(
	select
		pm.type,
		to_char(po.date_order::DATE, 'YYYY-MM-DD') as day,
		case
			pm.type when '聚合支付' then sum(po.amount_paid)
		end as "总微信消费额",
		case
			pm.type when '现金支付' then sum(po.amount_paid)
		end as "总现金消费额",
		SUM(case when pm.type = '聚合支付' and po.store_id = 9 then po.amount_paid else 0 end) as "食堂微信消费",
		SUM(case when pm.type = '现金支付' and po.store_id = 9 then po.amount_paid else 0 end) as "食堂现金消费",
		SUM(case when pm.type = '聚合支付' and po.store_id = 10 then po.amount_paid else 0 end) as "商超微信消费",
		SUM(case when pm.type = '现金支付' and po.store_id = 10 then po.amount_paid else 0 end) as "商超现金消费"
	from
		mhxzygjzx.store_order as po
	left join mhxzygjzx.pos_order_pay_message as pm on
		pm.oder_number = po.order_code
	where
		po.id is not null
		and po.state = 'paid'
		and po.date_order between {{startTime}} and {{endTime}}
		and pm.type in ('聚合支付', '现金支付')
		and po.store_id in (9, 10)
		and pm.pay_state = '2'
	group by
		day ,
		pm.type
	order by
		day
	) t1
group by
	t1.day
)
select
	tmp.统计日期,
	tmp.总刷卡消费额,
	tmp.总微信收款额,
	tmp.总现金消费额,
	tmp.当时卡余额,
	card_amount_tb.card_amount 当前卡余额,
	tmp.现金充值,
	tmp.微信充值,
	tmp.饭卡充值合计,
	tmp.食堂微信消费,
	tmp.食堂刷卡消费,
	tmp.食堂现金消费,
	tmp.食堂消费合计,
	tmp.商超微信消费,
	tmp.商超刷卡消费,
	tmp.商超现金消费,
	tmp.商超消费合计,
	concat(
        '[{"type":"mergeTitle", "addField":["合计金额","饭卡充值","食堂营业额","校园超市营业额"],"refField":[{"start":1,"end":5},{"start":6,"end":8},{"start":9,"end":12},{"start":13,"end":16}],"mergerRow":[0]};
        {"type":"totalNew",
        "name":"统计",
   "mergeField":["总刷卡消费额","总微信收款额","总现金消费额","现金充值","微信充值","饭卡充值合计","食堂微信消费","食堂刷卡消费","食堂现金消费","食堂消费合计","商超微信消费","商超刷卡消费","商超现金消费","商超消费合计"],
        "refField":[',
            sum(tmp.总刷卡消费额) over(), ',',
            sum(tmp.总微信收款额) over(), ',',
						sum(tmp.总现金消费额) over(), ',',
						sum(tmp.现金充值) over(), ',',
						sum(tmp.微信充值) over(), ',',
						sum(tmp.饭卡充值合计) over(), ',',
						sum(tmp.食堂微信消费) over(), ',',
						sum(tmp.食堂刷卡消费) over(), ',',
						sum(tmp.食堂现金消费) over(), ',',
						sum(tmp.食堂消费合计) over(), ',',
						sum(tmp.商超微信消费) over(), ',',
						sum(tmp.商超刷卡消费) over(), ',',
						sum(tmp.商超现金消费) over(), ',',
						sum(tmp.商超消费合计) over(),
            ']
        }]'
    ) 合并规则

from
(
 select
 main_tb.day 统计日期,
COALESCE(sum(card_amount_today.card_amount),0) 当时卡余额,
COALESCE(sum(member_c_tb."总刷卡消费额"),0) 总刷卡消费额,
(COALESCE(sum(canteen_c_tb."总微信消费额"),0) + COALESCE(sum(member_r_tb."微信充值"),0)) 总微信收款额,
COALESCE(sum(canteen_c_tb."总现金消费额"),0) + COALESCE(sum(member_r_tb."现金充值"),0) 总现金消费额,
COALESCE(sum(member_r_tb."现金充值"),0) 现金充值,
COALESCE(sum(member_r_tb."微信充值"),0) 微信充值,
COALESCE(sum(member_r_tb."饭卡充值合计"),0) 饭卡充值合计,
COALESCE(sum(canteen_c_tb."食堂微信消费"),0) 食堂微信消费,
COALESCE(sum(member_c_tb."食堂刷卡消费"),0) 食堂刷卡消费,
COALESCE(sum(canteen_c_tb."食堂现金消费"),0) 食堂现金消费,
(COALESCE(sum(canteen_c_tb."食堂微信消费"),0) + COALESCE(sum(canteen_c_tb."食堂现金消费"),0) + COALESCE(sum(member_c_tb."食堂刷卡消费"),0)) 食堂消费合计,
COALESCE(sum(canteen_c_tb."商超微信消费"),0) 商超微信消费,
COALESCE(sum(member_c_tb."商超刷卡消费"),0) 商超刷卡消费,
COALESCE(sum(canteen_c_tb."商超现金消费"),0) 商超现金消费,
(COALESCE(sum(canteen_c_tb."商超微信消费"),0) + COALESCE(sum(member_c_tb."商超刷卡消费"),0) + COALESCE(sum(canteen_c_tb."商超现金消费"),0)) 商超消费合计
 from main_tb
 left JOIN member_c_tb on main_tb.day = member_c_tb.day
 left JOIN member_r_tb on main_tb.day = member_r_tb.day
 left JOIN canteen_c_tb on main_tb.day = canteen_c_tb.day
 left join card_amount_today on main_tb.day = card_amount_today.record_date
 group by main_tb.day order by main_tb.day
) tmp left join card_amount_tb on true