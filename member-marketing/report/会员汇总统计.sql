-- 会员汇总统计：pgsql
with member_tbl as (
    select o.guid, o.user_name,o.phone_num,o.member_grade_info_name,o.member_integral
    from hsm_member_marketing_platform_db.hsa_operation_member_info o
    where o.oper_subject_guid = '26'
),label_tbl as (
    select mr.operation_member_info_guid,
           array_to_string(array_agg(ls.label_name),',') label_name
    from hsm_member_marketing_platform_db.hsa_member_label mr
             left join hsm_member_marketing_platform_db.hsa_label_setting ls on mr.label_setting_guid = ls.guid
    where mr.oper_subject_guid = '26'
      and mr.label_setting_guid in ('6925655229975232512')
    group by mr.operation_member_info_guid
),consumption_tbl as (
    select mc.member_info_guid,
           sum(case mc.consumption_type when 1 then mc.order_paid_amount else 0 end) consumptionAmount,
           sum(case mc.consumption_type when 1 then 1 else 0 end) consumptionCount,
           sum(case mc.consumption_type when 0 then mc.order_paid_amount else 0 end) rechargeAmount,
           sum(case mc.consumption_type when 0 then 1 else 0 end) rechargeCount
    from hsm_member_marketing_platform_db.hsa_member_consumption mc
    where mc.oper_subject_guid  = '26'
      and mc.consumption_time BETWEEN '2022-04-29 12:05:32' and '2022-07-29 12:05:32'
    group by mc.member_info_guid
),card_tbl as (
    select hc.member_info_guid,
           array_to_string(array_agg(hc.card_name),',') card_name,
           sum(hc.card_amount + hc.gift_amount + hc.subsidy_amount) card_balance
    from hsm_member_marketing_platform_db.hsa_member_info_card hc
    where hc.oper_subject_guid = '26'
--	and hc.card_guid in ('6877906498731638784')
    group by hc.member_info_guid
),subsidy_tbl as(
    select f.member_info_guid,sum(f.subsidy_amount) subsidy_amount ,count(*) subsidy_count
    from  hsm_member_marketing_platform_db.hsa_member_funding_detail f
    where f.oper_subject_guid = '26'
      and f.amount_source_type = 7
      and f.member_info_guid is not null
    group by f.member_info_guid
)
select
    member_tbl.user_name 会员姓名,
    member_tbl.phone_num 会员手机号,
    label_tbl.label_name 会员标签,
    card_tbl.card_name 会员卡,
    member_tbl.member_grade_info_name 会员等级,
    consumption_tbl.consumptionAmount 累计消费金额,
    consumption_tbl.consumptionCount 累计消费笔数,
    consumption_tbl.rechargeAmount 累计充值金额,
    consumption_tbl.rechargeCount 累计充值次数,
    subsidy_tbl.subsidy_amount 累计补贴金额,
    subsidy_tbl.subsidy_count 累计补贴次数,
    card_tbl.card_balance 当前总余额,
    member_tbl.member_integral 当前积分
from member_tbl
         left join label_tbl on member_tbl.guid = label_tbl.operation_member_info_guid
         left join card_tbl on member_tbl.guid = card_tbl.member_info_guid
         left join consumption_tbl on member_tbl.guid = consumption_tbl.member_info_guid
         left join subsidy_tbl on member_tbl.guid = subsidy_tbl.member_info_guid
where true
  and length('6925655229975232512') > 0 and label_tbl.operation_member_info_guid is not null
-- where consumption_tbl.consumptionAmount is not null
-- where position('6925655229975232512' in label_tbl.label_setting_guid )> 0
-- where position('6864025276737650688', in card_tbl.card_guid )> 0

/*
-- 标签
select guid,label_name
from hsm_member_marketing_platform_db.hsa_label_setting
where oper_subject_guid = cast(26 as varchar)

-- 卡
select guid,card_name
from hsm_member_marketing_platform_db.hsa_card_base_info
where oper_subject_guid = '26'

-- 等级
select guid,name
from hsm_member_marketing_platform_db.hsa_member_grade_info
where oper_subject_guid = '26' and is_delete = 0
*/

