<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.2.5.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.holderzone.member</groupId>
    <artifactId>member-marketing</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>member-marketing</name>
    <description>会员营销</description>
    <properties>
        <project.version>0.0.1-SNAPSHOT</project.version>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring.boot.version>2.2.5.RELEASE</spring.boot.version>
        <swagger.version>2.8.0</swagger.version>
        <spring.cloud.version>Hoxton.SR3</spring.cloud.version>
        <spring.cloud.alibaba.version>2.2.1.RELEASE</spring.cloud.alibaba.version>
        <custom_maven_url>http://nexus.holderzone.cn/nexus</custom_maven_url>
        <!--redis工具版本-->
        <redis.clients.version>2.9.0</redis.clients.version>
        <org.redisson.version>3.5.4</org.redisson.version>
        <!--ORM框架-->
        <fluent-mybatis.version>1.6.8</fluent-mybatis.version>
        <mybatis.plus.version>3.0.5</mybatis.plus.version>
        <druid.version>1.1.10</druid.version>
        <mybatis.version>1.3.2</mybatis.version>
        <spring.jdbc>5.0.5.RELEASE</spring.jdbc>
        <mysql.connector>6.0.6</mysql.connector>
        <!--对象转换-->
        <org.mapstruct.version>1.2.0.Final</org.mapstruct.version>
        <maven.compile.source>1.8</maven.compile.source>
        <maven.compile.target>1.8</maven.compile.target>
        <!--网关哨兵-->
        <sentinel-gateway>2.2.0.RELEASE</sentinel-gateway>
        <!--springboot分页-->
        <pagehelper-spring>1.2.5</pagehelper-spring>
        <log4j2.version>2.17.0</log4j2.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <!--模块化-->
    <modules>
        <module>holder-member-common</module>
        <module>holder-member-gateway</module>
        <module>holder-member-queue</module>
        <module>holder-member-base</module>
        <module>holder-member-marketing</module>
        <module>holder-member-mall</module>
        <module>holder-member-xxl-job/holder-member-job-executor</module>
        <module>holder-member-mall-tool</module>
        <module>holder-member-commodity</module>
        <module>holder-member-settlement</module>
        <module>holder-member-agg-jacoco-report</module>
        <module>holder-member-open-api</module>
    </modules>
    <dependencies>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.13.1</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>${spring.jdbc}</version>
            <optional>true</optional>
        </dependency>

        <!--sdk-->
        <dependency>
            <groupId>com.holderzone.framework</groupId>
            <artifactId>framework-sdk</artifactId>
            <version>1.1.8-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql.connector}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.jupiter</groupId>
                    <artifactId>junit-jupiter-api</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- pagehelper -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>${pagehelper-spring}</version>
        </dependency>

        <!--druid数据源-->
<!--        <dependency>-->
<!--            <groupId>com.alibaba</groupId>-->
<!--            <artifactId>druid-spring-boot-starter</artifactId>-->
<!--            <version>${druid.version}</version>-->
<!--        </dependency>-->

        <!--jwt-->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>0.10.7</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>0.10.7</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>0.10.7</version>
            <scope>runtime</scope>
        </dependency>

        <!--Mybatis-->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>${mybatis.version}</version>
        </dependency>

        <!--Mybatis-plus-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis.plus.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>com.alibaba.nacos</artifactId>
                    <groupId>nacos-client</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 重新引用nacos 客户端包-->
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
            <version>1.4.0</version>
        </dependency>

        <!--config-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!--Redis-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>${redis.clients.version}</version>
        </dependency>

        <dependency>
            <groupId>com.holderzone.framework</groupId>
            <artifactId>framework-redisson-sdk-starter</artifactId>
            <version>0.0.3-SNAPSHOT</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.redisson</groupId>-->
<!--            <artifactId>redisson</artifactId>-->
<!--            <version>${org.redisson.version}</version>-->
<!--        </dependency>-->

        <!-- 引入fluent-mybatis 运行依赖包, scope为compile -->
        <!--        <dependency>-->
        <!--            <groupId>com.github.atool</groupId>-->
        <!--            <artifactId>fluent-mybatis</artifactId>-->
        <!--            <version>${fluent-mybatis.version}</version>-->
        <!--        </dependency>-->
        <!-- 引入fluent-mybatis-processor, scope设置为provider 编译需要，运行时不需要 -->
        <!--        <dependency>-->
        <!--            <groupId>com.github.atool</groupId>-->
        <!--            <artifactId>fluent-mybatis-processor</artifactId>-->
        <!--            <version>${fluent-mybatis.version}</version>-->
        <!--        </dependency>-->
        <!--lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.16</version>
            <optional>true</optional>
        </dependency>
        <!--日志log4j2-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <!--对象转换-->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-jdk8</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>

        <!--swagger接口文档-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.9.2</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.1.0-beta1</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.9.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>mapstruct</artifactId>
                    <groupId>org.mapstruct</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <!--    <repositories>-->
    <!--        <repository>-->
    <!--            <id>snapshots</id>-->
    <!--            <name>Nexus Snapshots</name>-->
    <!--            <url>${custom_maven_url}/content/repositories/snapshots/</url>-->
    <!--            <snapshots>-->
    <!--                <enabled>true</enabled>-->
    <!--            </snapshots>-->
    <!--        </repository>-->
    <!--        <repository>-->
    <!--            <id>thirdparty</id>-->
    <!--            <name>Nexus ThirdParty</name>-->
    <!--            <url>${custom_maven_url}/content/repositories/thirdparty/</url>-->
    <!--        </repository>-->
    <!--        <repository>-->
    <!--            <id>releases</id>-->
    <!--            <name>Nexus Releases</name>-->
    <!--            <url>${custom_maven_url}/content/repositories/releases/</url>-->
    <!--        </repository>-->
    <!--        <repository>-->
    <!--            <id>spring-snapshots</id>-->
    <!--            <name>Spring Snapshots</name>-->
    <!--            <url>https://repo.spring.io/snapshot</url>-->
    <!--            <snapshots>-->
    <!--                <enabled>true</enabled>-->
    <!--            </snapshots>-->
    <!--        </repository>-->
    <!--        <repository>-->
    <!--            <id>spring-milestones</id>-->
    <!--            <name>Spring Milestones</name>-->
    <!--            <url>https://repo.spring.io/milestone</url>-->
    <!--            <snapshots>-->
    <!--                <enabled>false</enabled>-->
    <!--            </snapshots>-->
    <!--        </repository>-->
    <!--    </repositories>-->

    <!--    <pluginRepositories>-->
    <!--        <pluginRepository>-->
    <!--            <id>snapshots</id>-->
    <!--            <name>Nexus Snapshots</name>-->
    <!--            <url>${custom_maven_url}/content/repositories/snapshots/</url>-->
    <!--            <snapshots>-->
    <!--                <enabled>true</enabled>-->
    <!--            </snapshots>-->
    <!--        </pluginRepository>-->
    <!--        <pluginRepository>-->
    <!--            <id>thirdparty</id>-->
    <!--            <name>Nexus ThirdParty</name>-->
    <!--            <url>${custom_maven_url}/content/repositories/thirdparty/</url>-->
    <!--        </pluginRepository>-->
    <!--        <pluginRepository>-->
    <!--            <id>releases</id>-->
    <!--            <name>Nexus Releases</name>-->
    <!--            <url>${custom_maven_url}/content/repositories/releases/</url>-->
    <!--        </pluginRepository>-->
    <!--        <pluginRepository>-->
    <!--            <id>spring-snapshots</id>-->
    <!--            <name>Spring Snapshots</name>-->
    <!--            <url>https://repo.spring.io/snapshot</url>-->
    <!--            <snapshots>-->
    <!--                <enabled>true</enabled>-->
    <!--            </snapshots>-->
    <!--        </pluginRepository>-->
    <!--        <pluginRepository>-->
    <!--            <id>spring-milestones</id>-->
    <!--            <name>Spring Milestones</name>-->
    <!--            <url>https://repo.spring.io/milestone</url>-->
    <!--            <snapshots>-->
    <!--                <enabled>false</enabled>-->
    <!--            </snapshots>-->
    <!--        </pluginRepository>-->
    <!--    </pluginRepositories>-->
    <!--    <build>-->
    <!--        <plugins>-->
    <!--            <plugin>-->
    <!--                <groupId>org.springframework.boot</groupId>-->
    <!--                <artifactId>spring-boot-maven-plugin</artifactId>-->
    <!--            </plugin>-->
    <!--            <plugin>-->
    <!--                <groupId>org.apache.maven.plugins</groupId>-->
    <!--                <artifactId>maven-compiler-plugin</artifactId>-->
    <!--                <version>3.7.0</version>-->
    <!--                <configuration>-->
    <!--                    <source>1.8</source>-->
    <!--                    <target>1.8</target>-->
    <!--                    <encoding>UTF-8</encoding>-->
    <!--                </configuration>-->
    <!--            </plugin>-->
    <!--        </plugins>-->
    <!--    </build>-->


    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.18.1</version>
                <configuration>
                    <skipTests>false</skipTests>
                    <testFailureIgnore>true</testFailureIgnore>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.5</version>
                <executions>
                    <execution>
                        <id>my-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                    <skipTests>false</skipTests>
                    <forkMode>once</forkMode>
                    <testFailureIgnore>true</testFailureIgnore>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
