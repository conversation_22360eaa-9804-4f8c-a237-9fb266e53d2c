package com.holderzone.member.common.module.marketing.coupon.use.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 优惠劵核销统计
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CouponUseCountVO implements Serializable {

    private static final long serialVersionUID = 330383597983550412L;
    /**
     * 券核销数
     */
    private int useNum;
    
    /**
     * 优惠抵扣金额
     */
    private BigDecimal discountAmount;

    /**
     * 带动消费金额
     */
    private BigDecimal orderPaidAmount;

    /**
     * top 门店guid
     * 核销门店中数量最多的门店名称
     */
    @JsonIgnore
    private String storeGuid;

    /**
     * top 门店
     */
    private String storeName;
}
