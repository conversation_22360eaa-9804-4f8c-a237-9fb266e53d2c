package com.holderzone.member.common.enums.member;


import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 二维码类型
 */
public enum QrcodeTypeEnum {

    /**
     * 无
     */
    NONE(0, ""),

    /**
     * 会员卡
     */
    MEMBER(1, "qr_pre"),

    /**
     * 挂账
     */
    CREDIT(2, "credit_pre"),

    /**
     * 优惠券
     */
    COUPON(3, "coupon_"),
    ;

    /**
     * 编号
     */
    private final int code;

    /**
     * 描述
     */
    private final String des;

    QrcodeTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    public static QrcodeTypeEnum getEnumByCode(Integer code) {
        if (Objects.isNull(code)) {
            return NONE;
        }
        for (QrcodeTypeEnum type : QrcodeTypeEnum.values()) {
            if (type.code == code) {
                return type;
            }
        }
        return NONE;
    }

    /**
     * 是否对应类型二维码
     *
     * @param value
     * @param typeEnum
     * @return
     */
    public boolean include(String value, QrcodeTypeEnum typeEnum) {
        if (StringUtils.isBlank(value)) {
            return false;
        }
        return value.startsWith(typeEnum.des);
    }
}
