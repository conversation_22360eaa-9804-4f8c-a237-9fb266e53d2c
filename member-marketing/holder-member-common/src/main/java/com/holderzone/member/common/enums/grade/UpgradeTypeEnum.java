package com.holderzone.member.common.enums.grade;

/**
 * <AUTHOR>
 * @date 2023/3/30 上午10:32
 * @description 升级机制枚举
 */
public enum UpgradeTypeEnum {

    /**
     * 成长值升级
     */
    GROWTH_VALUE(1, "成长值升级"),

    /**
     * 付费升级
     */
    PAY(2, "付费升级"),
    ;


    /**
     * 编号
     */
    private final int code;

    /**
     * 描述
     */
    private final String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    UpgradeTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public static String getDesByCode(int code){
        UpgradeTypeEnum[] typeEnums = UpgradeTypeEnum.values();
        for (UpgradeTypeEnum typeEnum : typeEnums) {
            if(typeEnum.getCode() == code){
                return typeEnum.getDes();
            }
        }
        return "";
    }
}
