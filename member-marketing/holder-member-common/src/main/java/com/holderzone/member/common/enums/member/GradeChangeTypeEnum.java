package com.holderzone.member.common.enums.member;

import java.util.Objects;

/**
 * @ProjectName: member-marketing
 * @ClassName: GradeChangeTypeEnum
 * @Author: rw
 * @Description: 会员等级
 */
public enum GradeChangeTypeEnum {

    UPGRADE(0, "升级"),

    DEMOTION(1, "降级");

    /**
     * 编号
     */
    private int code;

    /**
     * 名称
     */
    private String des;

    GradeChangeTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    public static String getNameByCode(Integer code) {
        if (Objects.isNull(code)) {
            return "";
        }
        for (GradeChangeTypeEnum accountStateEnum : GradeChangeTypeEnum.values()) {
            if (accountStateEnum.code == code) {
                return accountStateEnum.getDes();
            }
        }
        return "";
    }
}
