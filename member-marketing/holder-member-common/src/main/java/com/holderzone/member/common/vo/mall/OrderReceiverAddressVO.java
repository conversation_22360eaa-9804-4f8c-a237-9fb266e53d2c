package com.holderzone.member.common.vo.mall;

import com.alibaba.fastjson.annotation.JSONField;
import com.holderzone.member.common.annotation.PrivacyEncrypt;
import com.holderzone.member.common.enums.PrivacyTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 会员收货地址
 * @date 2022/8/29 15:59
 */
@Data
@Accessors(chain = true)
public class OrderReceiverAddressVO implements Serializable {
    private String guid;

    /**
     * 会员guid
     */
    private String memberGuid;

    /**
     * memberAccount
     */
    private String memberAccount;

    /**
     * 收货人
     */
    private String receiverName;

    private String code;

    private String region;

    /**
     * 收货人性别
     *
     * @see com.holderzone.member.common.enums.member.SexEnum
     */
    private Integer receiverSex;

    /**
     * 收货人手机号
     */
    private String receiverPhone;

    /**
     * 收货人地址
     */
    private String receiverAddress;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 门牌号
     */
    private String houseNumber;

    /**
     * 地址标签名
     */
    private String addressLabel;

    /**
     * 是否默认地址
     */
    private Integer defaultAddress;

    /**
     * 固定地址Json
     */
    private String receiverJson;

    /**
     * 地址类型
     * @see com.holderzone.member.common.enums.mall.order.AddressEnum
     */
    private Integer receiverType;

    private String storeId;
}
