package com.holderzone.member.common.enums.member;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PayWayEnum
 * @description 支付方式
 */
@Getter
@AllArgsConstructor
public enum PayWayEnum {

    //支付方式枚举
    CASH_PAY(0, "现金支付"),
    AGGREGATION_PAY(1, "聚合支付"),
    BANK_CARD_PAY(2, "银联支付"),
    CARD_BALANCE_PAY(3, "会员支付"),
    FACE_PAY(4, "人脸支付"),
    CUSTOMER_PAY(5, "自定义支付方式"),
    CREDIT_PAY(6, "挂账支付"),
    MEMBER_PAY(10, "用户余额"),
    CANTEEN_ELECTRONIC_CARD(11, "食堂电子卡支付"),
    CANTEEN_PHYSICAL_CARD(12, "食堂实体卡支付"),
    WE_CHANT_PAY(13, "微信支付"),
    ALIPAY(14, "支付宝支付"),
    RECHARGE_GIFT(20, "充值赠送"),

    SCAN_QR_CODES_PAY(22, "扫码支付"),

    SWIPING_CARD_PAY(23, "刷卡支付"),

    PHONE_CARD_NUM_PAY(24, "输入手机号/卡号支付");

    private final int code;
    private final String des;


    public static String getPayName(Integer code) {
        if (code == null) {
            return "";
        }
        PayWayEnum[] values = PayWayEnum.values();
        for (PayWayEnum payWayEnum : values) {
            if (payWayEnum.getCode() == code) {
                return payWayEnum.des;
            }
        }
        return "";
    }

    public static PayWayEnum payWay(Integer code) {
        if (code == null) {
            return null;
        }
        PayWayEnum[] values = PayWayEnum.values();
        for (PayWayEnum payWayEnum : values) {
            if (payWayEnum.getCode() == code) {
                return payWayEnum;
            }
        }
        return null;
    }

    /**
     * 聚合支付的
     *
     * @param code
     * @return
     */
    public static Boolean isAgg(Integer code) {
        return Objects.equals(code, AGGREGATION_PAY.code) ||
                Objects.equals(code, BANK_CARD_PAY.code) ||
                Objects.equals(code, WE_CHANT_PAY.code);
    }
}
