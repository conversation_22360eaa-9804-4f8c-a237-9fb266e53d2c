package com.holderzone.member.common.config.mybatis;

import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;

/**
 * @date 2020/05/09 14:21
 * @description 自定义表根据Guid的基本数据库操作
 */
public interface IHolderBaseService<T> extends IService<T> {

    /**
     * 通过GUID查询
     *
     * @param guid guid
     * @return T
     */
    T queryByGuid(String guid);

    /**
     * 通过多个GUID查询
     *
     * @param guids guids
     * @return List<T>
     */
    List<T> queryByGuids(Collection<String> guids);

    /**
     * 根据GUID修改
     *
     * @param t update model
     * @return boolean
     */
    boolean updateByGuid(T t);

    /**
     * 根据GUID删除
     *
     * @param guid guid
     * @return boolean
     */
    boolean removeByGuid(String guid);

    /**
     * 根据多个GUID删除
     *
     * @param guids guids
     * @return boolean
     */
    boolean removeByGuids(Collection<String> guids);
}
