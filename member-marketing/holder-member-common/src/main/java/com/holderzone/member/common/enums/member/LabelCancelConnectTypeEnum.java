package com.holderzone.member.common.enums.member;

import java.util.Objects;

/**
 * 标签取消关联类型枚举
 * author: pantao
 */
public enum LabelCancelConnectTypeEnum {

    /**
     * 人工取关
     */
    MANUAL(0, "人工取关"),

    /**
     * 自动取关
     */
    AUTOMATION(1, "自动取关");

    /**
     * 编号
     */
    private int code;

    /**
     * 描述
     */
    private String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    LabelCancelConnectTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    /**
     * 根据标号获取描述
     *
     * @param code 编号
     * @return
     */
    public static String getDesByCode(Integer code) {
        if (Objects.isNull(code)) {
            return "";
        }
        for (LabelCancelConnectTypeEnum typeEnum : LabelCancelConnectTypeEnum.values()) {
            if (typeEnum.getCode() == code) {
                return typeEnum.getDes();
            }
        }
        return "";
    }
}
