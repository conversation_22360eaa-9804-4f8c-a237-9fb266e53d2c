package com.holderzone.member.common.dto.event;


import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 指定发卡
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-07
 */
@Data
@Accessors(chain = true)
public class SendOpenCardEvent implements Serializable {


    /**
     * cardGuid
     */
    private String cardGuid;

    /**
     * memberInfoGuid
     */
    private List<String> memberInfoGuid;
}
