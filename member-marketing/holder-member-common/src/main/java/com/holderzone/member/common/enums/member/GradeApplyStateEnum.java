package com.holderzone.member.common.enums.member;

import java.util.Objects;

/**
 * @ProjectName: member-marketing
 * @ClassName: GradeApplyStateEnum
 * @Description:
 * @Version: 1.0
 */
public enum GradeApplyStateEnum {

    AWAIT_AUDIT(0, "待审核"),

    AUDIT_PASS(1, "审核通过"),

    AUDIT_REJECTED(2, "驳回"),
    ;

    /**
     * 编号
     */
    private int code;

    /**
     * 名称
     */
    private String des;

    GradeApplyStateEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    public static String getNameByCode(Integer code){
        if (Objects.isNull(code)){
            return "";
        }
        for (GradeApplyStateEnum accountStateEnum : GradeApplyStateEnum.values()){
            if (accountStateEnum.code == code){
                return accountStateEnum.getDes();
            }
        }
        return "";
    }
}
