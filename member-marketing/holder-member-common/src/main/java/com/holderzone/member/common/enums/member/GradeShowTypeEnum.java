package com.holderzone.member.common.enums.member;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/22 下午2:48
 * @description 角色类型
 */
@Getter
@AllArgsConstructor
public enum GradeShowTypeEnum {

    /**
     * 可续费
     */
    RENEW(0, "可续费"),

    /**
     * 可升级
     */
    PROMOTION(1, "可升级"),

    /**
     * 未开通
     */
    NOT_OPEN(2, "未开通"),
    ;

    private final Integer code;

    private final String des;

    public static String getName(String name) {
        if (name == null) {
            return "";
        }
        GradeShowTypeEnum[] values = GradeShowTypeEnum.values();
        for (GradeShowTypeEnum roleTypeEnum : values) {
            if (Objects.equals(roleTypeEnum.name(), name)) {
                return roleTypeEnum.getDes();
            }
        }
        return "";
    }
}
