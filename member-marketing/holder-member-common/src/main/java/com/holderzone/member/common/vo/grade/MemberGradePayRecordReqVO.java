package com.holderzone.member.common.vo.grade;

import com.holderzone.member.common.exception.MemberBaseException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年04月11日 上午10:34
 * @description 会员付费等级记录实体
 */
@Data
public class MemberGradePayRecordReqVO implements Serializable {

    private static final long serialVersionUID = 2464880022951354326L;

    @ApiModelProperty(value = "会员GUID")
    private List<String> memberGuidList;
    ;

    @ApiModelProperty(value = "会员等级GUID")
    private String memberInfoGradeGuid;

    /**
     * 角色类型
     *
     * @see com.holderzone.member.common.enums.member.RoleTypeEnum
     */
    @ApiModelProperty(value = "角色类型")
    private String roleType;

    @ApiModelProperty(value = "有效数量")
    private Integer num;

    @ApiModelProperty(value = "有效期单位：3月 4年")
    private Integer unit;

    /**
     * 变动类型
     *
     * @see com.holderzone.member.common.enums.growth.SumValueChangeEnum
     */
    @ApiModelProperty(value = "变动类型")
    private Integer changeType;

    @ApiModelProperty(value = "备注")
    private String remark;

    private String operSubjectGuid;

    /**
     * 付费金额
     */
    private BigDecimal payAmount;

    public void validatedSave() {
        if (StringUtils.isEmpty(roleType)) {
            throw new MemberBaseException("角色类型不能为空");
        }
        if (CollectionUtils.isEmpty(memberGuidList)) {
            throw new MemberBaseException("会员GUID不能为空");
        }
        if (StringUtils.isEmpty(memberInfoGradeGuid)) {
            throw new MemberBaseException("会员等级GUID不能为空");
        }
    }
}
