package com.holderzone.member.common.dto.page;

import com.github.pagehelper.page.PageMethod;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class PageDTO implements Serializable {

    private static final long serialVersionUID = -4307925127548322621L;

    /**
     * 当前页数
     */
    private Integer currentPage = 1;

    /**
     * 每页显示条数
     */
    private Integer pageSize = 20;

    public Integer getBeginIndex() {
        if (this.currentPage == null) {
            return 0;
        } else {
            return this.pageSize == null ? 0 : (this.currentPage - 1) * this.pageSize;
        }
    }

    /**
     * 分页参数设置
     */
    public void startPage() {
        PageMethod.startPage(this.currentPage, this.pageSize);
    }
}
