package com.holderzone.member.common.enums.member;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @ProjectName: member-marketing
 * @ClassName: AccountLoginTypeEnum
 * @Author: rw
 * @Description:
 * @Version: 1.0
 */
@Getter
@AllArgsConstructor
public enum AccountLoginTypeEnum {

    LOGIN(1,"登录+注册"),

    ONLY_LOGIN(2,"仅登录"),

    ACCOUNT_LOGIN(3,"账号/手机号+密码登录");

    /**
     * 编号
     */
    private final int code;

    /**
     * 名称
     */
    private final String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    public static String getNameByCode(Integer code){
        if (Objects.isNull(code)){
            return "";
        }
        for (AccountLoginTypeEnum accountStateEnum : AccountLoginTypeEnum.values()){
            if (accountStateEnum.code == code){
                return accountStateEnum.getDes();
            }
        }
        return "";
    }

    /**
     *
     * @param code code
     * @return AccountLoginTypeEnum
     */
    public static AccountLoginTypeEnum getAccountLoginTypeEnum(Integer code){
        if(code == null) {
            return null;
        }
        for (AccountLoginTypeEnum s : AccountLoginTypeEnum.values()) {
            if(code.equals(s.getCode())){
                return s;
            }
        }
        return null;
    }
}
