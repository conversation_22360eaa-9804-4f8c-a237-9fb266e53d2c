package com.holderzone.member.common.enums.member;


import java.util.Objects;

/**
 * 权益运营主体枚举类
 * <AUTHOR>
 */

public enum EquitiesSubjectTypeEnum {

    /**
     * 全部运营主体
     */
    EQUITIES_ALL(1,"全部运营主体"),

    /**
     * 部分运营主体
     */
    EQUITIES_PART(2,"部分运营主体"),

    ;

    /**
     * 编号
     */
    private final int code;

    /**
     * 描述
     */
    private final String des;

    EquitiesSubjectTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    public static EquitiesSubjectTypeEnum getEnumByCode(Integer code){
        if (Objects.isNull(code)){
            return null;
        }
        for (EquitiesSubjectTypeEnum type : EquitiesSubjectTypeEnum.values()){
            if (type.code == code){
                return type;
            }
        }
        return null;
    }
}
