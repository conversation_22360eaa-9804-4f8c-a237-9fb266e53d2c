package com.holderzone.member.common.enums.member;

/**
 * author: pantao
 */
public enum BalanceChangeTypeEnum {

    RECHARGE(0,"实充"),

    GIFT(1,"赠送"),

    SUBSIDY(2,"补贴"),

    UNKNOWN(-1,"未知");

    private int code;

    private String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    BalanceChangeTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public static BalanceChangeTypeEnum getEnumByCode(int code){
        for (BalanceChangeTypeEnum balanceChangeTypeEnum : BalanceChangeTypeEnum.values()){
            if (balanceChangeTypeEnum.getCode() == code){
                return balanceChangeTypeEnum;
            }
        }
        return UNKNOWN;
    }
}
