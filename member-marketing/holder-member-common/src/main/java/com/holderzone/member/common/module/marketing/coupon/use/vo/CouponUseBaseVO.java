package com.holderzone.member.common.module.marketing.coupon.use.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 优惠劵核销明细
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CouponUseBaseVO implements Serializable {


    private static final long serialVersionUID = -2158955323930754271L;

    /**
     * guid
     */
    private String couponGuid;

    /**
     * 核销优惠券ID
     */
    private String couponCode;

    /**
     * 核销优惠券名称
     */
    private String couponName;

    /**
     * 核销优惠券类型
     *
     * @see com.holderzone.member.common.enums.coupon.CouponTypeEnum
     */
    private Integer couponType;

    /**
     * 核销券码
     */
    private String code;

    /**
     * memberGuid
     */
    private String memberGuid;

    /**
     * 会员名称
     */
    private String userName;

    /**
     * memberPhone
     */
    private String memberPhone;

    /**
     * 核销优惠券时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime couponUseTime;

    /**
     * 核销渠道
     *
     * @see com.holderzone.member.common.enums.member.SourceTypeEnum
     */
    private Integer source;

    /**
     * 发券方式类型（发券场景）
     *
     * @see com.holderzone.member.common.enums.coupon.CouponPackageTypeEnum
     */
    private Integer couponPackageType;

    /**
     * 券包ID：（场景id）
     */
    private String couponPackageCode;

    /**
     * 订单编码
     */
    private String orderNumber;

    /**
     * 订单实付金额
     * （带动消费金额）
     */
    private BigDecimal orderPaidAmount;

    /**
     * 抵扣优惠金额
     * （优惠券抵扣金额）
     */
    private BigDecimal discountAmount;

    /**
     * 门店Guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 操作人
     */
    private String operatorAccountName;

    private String memberCouponLinkGuid;
}
