package com.holderzone.member.common.enums.member;

import java.util.Objects;

/**
 * @ProjectName: member-marketing
 * @ClassName: GradeRightsTypeEnum
 * @Author: rw
 * @Description: 会员权益类型
 */
public enum GradeRightsTypeEnum {

    GRADE_RIGHTS_BAG(0, "升级礼包"),

    GRADE_RIGHTS(1, "等级权益");

    /**
     * 编号
     */
    private int code;

    /**
     * 名称
     */
    private String des;

    GradeRightsTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    public static String getNameByCode(Integer code) {
        if (Objects.isNull(code)) {
            return "";
        }
        for (GradeRightsTypeEnum accountStateEnum : GradeRightsTypeEnum.values()) {
            if (accountStateEnum.code == code) {
                return accountStateEnum.getDes();
            }
        }
        return "";
    }
}
