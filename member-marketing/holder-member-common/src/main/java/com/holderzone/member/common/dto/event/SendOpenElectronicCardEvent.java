package com.holderzone.member.common.dto.event;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 开通电子卡
 *
 * <AUTHOR>
 * @version 1.0
 * @className SendOpenElectronicCardEvent
 * @date 2021/09/16 15:49
 * @description 开通电子卡
 * @program holder-member-xxl-job
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SendOpenElectronicCardEvent extends BaseEvent {

    /**
     * 批量发卡
     */
    private List<SendOpenCardEvent> sendOpenCardEvents;
}
