package com.holderzone.member.common.enums.member;


import java.util.Objects;

/**
 * 累计消费关联标签类型枚举
 * author: pantao
 */
public enum LabelConsumptionTypeEnum {

    /**
     * 注册至今
     */
    REGISTER_TO_NOW(1,"注册至今"),

    /**
     * 最近一段时间
     */
    RECENTLY(2,"最近一段时间"),

    /**
     * 固定时间
     */
    FIXED_TIME(3,"固定时间"),

    ;

    /**
     * 编号
     */
    private int code;

    /**
     * 描述
     */
    private String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    LabelConsumptionTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public static LabelConsumptionTypeEnum getEnumByCode(Integer code){
        if (Objects.isNull(code)){
            return null;
        }
        for (LabelConsumptionTypeEnum type : LabelConsumptionTypeEnum.values()){
            if (type.code == code){
                return type;
            }
        }
        return null;
    }
}
