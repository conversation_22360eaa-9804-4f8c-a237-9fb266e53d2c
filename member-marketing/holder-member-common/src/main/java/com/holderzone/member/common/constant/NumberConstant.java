package com.holderzone.member.common.constant;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description 数字常量
 */
public interface NumberConstant {


    /**
     * 手机号长度
     */
    int NUMBER_11 = 11;

    int NUMBER_9 = 9;

    /**
     * 实体卡号
     */
    int NUMBER_12 = 12;

    int NUMBER_15 = 15;

    /**
     * 实体卡号
     */
    int NUMBER_18 = 18;

    /**
     * 绑定码
     */
    int NUMBER_6 = 6;

    /**
     * 会员姓名最大长度
     */
    int NUMBER_20 = 20;

    int NUMBER_10 = 10;

    int NUMBER_23 = 23;

    int NUMBER_24 = 24;

    int NUMBER_59 = 59;

    int NUMBER_60 = 60;

    int NUMBER_72 = 72;

    int NUMBER_720 = 720;

    int NUMBER_10000 = 10000;

    /**
     * 证件号码长度
     */
    int NUMBER_50 = 50;

    /**
     * 证件号码长度
     */
    int NUMBER_40 = 40;

    int NUMBER_5003 = 5003;

    /**
     * 导入时最大EXCEL条数
     */
    int NUMBER_20000 = 20000;

    int NUMBER_20001 = 20001;

    List<Integer> SOURCE_CODE = Arrays.asList(
            53,
            6,
            7,
            8
    );

    /**
     * 区号
     */
    List<String> AREA_CODE = Arrays.asList(
            "86",
            "852",
            "886",
            "853",
            "971",
            "353",
            "61",
            "55",
            "48",
            "45",
            "49",
            "670",
            "7",
            "33",
            "63",
            "358",
            "57",
            "82",
            "31",
            "1",
            "855",
            "856",
            "40",
            "60",
            "1",
            "95",
            "52",
            "977",
            "234",
            "47",
            "81",
            "46",
            "41",
            "966",
            "66",
            "90",
            "673",
            "34",
            "65",
            "64",
            "39",
            "62",
            "44",
            "84",
            "260");


    /**
     * 电子卡开卡路径
     */
    List<String> OPEN_CARD_PATH = Arrays.asList(
            "2",
            "53",
            "54");

    /**
     * 指定用户开卡，不要卡开二维码
     */
    List<String> OPEN_CARD_PATH_NOT_QR_CODE = Arrays.asList(
            "2",
            "53");


    /**
     * 会员最大成长值、积分
     */
    int NUMBER_999999 = 999999;

    int NUMBER_9999999 = 999999;

    long NUMBER_LONG_9999999 = 999999L;

    int NUMBER_9999 = 9999;

    /**
     * 活动保存并发布的类型（与保存到草稿区分）
     */
    int NUMBER_2 = 2;

    int NUMBER_3 = 3;

    int NUMBER_4 = 4;

    int NUMBER_5 = 5;

    /**
     * 数字0
     */
    int NUMBER_0 = 0;

    String NUMBER_FLOAT_1 = "0.1";

    /**
     * 数字1
     */
    int NUMBER_1 = 1;

    int NUMBER_MINUS_1 = -1;


    int NUMBER_7 = 7;

    int NUMBER_8 = 8;

    int NUMBER_53 = 53;


    int NUMBER_30 = 30;

    int NUMBER_110 = 110;

    int NUMBER_111 = 111;


    /**
     * 最大金额(5位整数)
     */
    BigDecimal MAX_MONEY_5 = new BigDecimal("99999.99");

    /**
     * 最大折扣
     */
    BigDecimal MAX_DISCOUNT = new BigDecimal("9.99");

    /**
     * 0.1
     */
    BigDecimal DECIMAL_POINT_01 = new BigDecimal("0.1");

    /**
     * 0.1
     */
    BigDecimal DECIMAL_POINT_1 = new BigDecimal("1");

    /**
     * 0.99
     */
    BigDecimal DECIMAL_POINT_99 = new BigDecimal("0.99");

    /**
     * 0.01
     */
    BigDecimal DECIMAL_POINT_001 = new BigDecimal("0.01");

    String ONE = "一";

    String TWO = "二";

    String THREE = "三";

    String FOUR = "四";

    String FIVE = "五";

    String SIX = "六";

    String DAY = "日";
}



