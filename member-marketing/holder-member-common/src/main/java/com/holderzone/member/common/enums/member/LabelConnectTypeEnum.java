package com.holderzone.member.common.enums.member;

import java.util.Objects;

/**
 * 标签连接类型枚举
 * author: pantao
 */
public enum LabelConnectTypeEnum {

    /**
     * 人工关联
     */
    MANUAL(0, "人工关联"),

    /**
     * 自动关联
     */
    AUTOMATION(1, "自动关联");

    /**
     * 编号
     */
    private int code;

    /**
     * 描述
     */
    private String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    LabelConnectTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    /**
     * 根据标号获取描述
     *
     * @param code 编号
     * @return
     */
    public static String getDesByCode(Integer code) {
        if (Objects.isNull(code)) {
            return "";
        }
        for (LabelConnectTypeEnum typeEnum : LabelConnectTypeEnum.values()) {
            if (typeEnum.getCode() == code) {
                return typeEnum.getDes();
            }
        }
        return "";
    }
}
