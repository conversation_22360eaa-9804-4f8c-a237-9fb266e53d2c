package com.holderzone.member.common.enums.member;

/**
 * @ProjectName: member-marketing
 * @ClassName: OperationLabelEnum
 * @Author: pantao
 * @Description: 操作标签列表枚举
 * @Date: 2021/8/26 16:40
 * @Version: 1.0
 */
public enum OperationLabelEnum {

    /**
     * 启用
     */
    START(0, "启用"),

    /**
     * 禁用
     */
    FORBIDDEN(1, "禁用"),

    /**
     * 删除
     */
    DELETE(2, "删除"),

    /**
     * 未知类型
     */
    UNDEFINED(-1, "未知类型");

    private int code;

    private String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    OperationLabelEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    /**
     * 根据类型获取枚举
     *
     * @param code 类型
     * @return 操作结果
     */
    public static OperationLabelEnum getEnumByCode(int code) {
        for (OperationLabelEnum operationLabelEnum : OperationLabelEnum.values()) {
            if (operationLabelEnum.getCode() == code) {
                return operationLabelEnum;
            }
        }
        return OperationLabelEnum.UNDEFINED;
    }
}
