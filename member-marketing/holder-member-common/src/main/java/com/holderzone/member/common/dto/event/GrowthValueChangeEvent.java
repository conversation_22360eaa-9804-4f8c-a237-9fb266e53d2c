package com.holderzone.member.common.dto.event;


import com.holderzone.member.common.enums.member.RoleTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 成长值变更参数
 *
 * <AUTHOR>
 * @version 1.0
 * @className GrowthValueChangeEvent
 * @date 2021/11/23 13:38
 * @description 成长值变更参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GrowthValueChangeEvent extends BaseEvent {
    /**
     * 会员GUID
     */
    private List<String> memberInfoGuidList;

    /**
     * 运营主体
     */
    private String operSubjectGuid;

    /**
     * 成长值
     */
    private Integer growthValue;
    /**
     * 备注
     */
    private String remark;

    /**
     * 会员消费记录GUID
     */
    private String memberConsumptionGuid;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 订单金额
     */
    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;

    /**
     * 会员卡
     */
    @ApiModelProperty("会员卡")
    private String cardGuid;

    /**
     * 成长值来源
     */
    private Integer source;

    /**
     * 成长值变动来源
     */
    private Integer changeType;

    /**
     * 任务类型 0:基础任务 1:消费任务 2:充值任务
     *
     * @see com.holderzone.member.common.enums.growth.TaskTypeEnum
     */
    private Integer taskType;

    @ApiModelProperty(value = "成长值类型，0增加，1减少", required = true)
    private Integer growthValueType;

    /**
     * 任务guid (来源为基础成长值任务时存在)
     */
    private String GrowthValueTaskGuid;

    /**
     * 门店来源guid
     */
    private String storeGuid;

    /**
     * 门店来源名称
     */
    private String storeName;

    /**
     * 操作人员账号名字
     */
    private String operatorAccountName;

    /**
     * 角色类型
     *
     * @see com.holderzone.member.common.enums.member.RoleTypeEnum
     */
    private String roleType = RoleTypeEnum.MEMBER.name();

    /**
     * 会员等级GUID，-1为无等级
     */
    private String memberInfoGradeGuid;

    /**
     * 有效期数量
     */
    private Integer num;

    /**
     * 有效期单位：3月 4年
     */
    private Integer unit;

}
