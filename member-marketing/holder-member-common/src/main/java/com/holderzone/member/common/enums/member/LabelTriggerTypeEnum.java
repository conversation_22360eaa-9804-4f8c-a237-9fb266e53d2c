package com.holderzone.member.common.enums.member;

/**
 * @program: member-marketing
 * @description: 标签触发类型枚举
 * @author: pan tao
 * @create: 2021-12-24 10:40
 */
public enum LabelTriggerTypeEnum {

    /**
     * 基本信息
     */
    BASE_INFO(0, "基本信息"),

    /**
     * 注册信息
     */
    REGISTER_INFO(1, "注册信息"),

    /**
     * 成长信息
     */
    GROWTH_INFO(2, "成长信息"),

    /**
     * 消费信息
     */
    CONSUMPTION_INFO(3, "消费信息"),

    /**
     * 充值信息
     */
    RECHARGE_INFO(4, "充值信息"),

    /**
     * 积分信息
     */
    INTEGRAL_INFO(5, "积分信息"),

    /**
     * 兑换活动
     */
    REDEEM_INFO(22, "兑换活动"),

    /**
     * 身份信息
     */
    IDENTITY_INFO(6, "身份信息"),

    UPDATE_STORE(7, "修改归属门店"),

    /**
     * 触发所有
     */
    ALL(-1, "触发所有");

    /**
     * 编号
     */
    private int code;

    /**
     * 描述
     */
    private String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    LabelTriggerTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }
}
