package com.holderzone.member.common.enums.member;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 性别，值为1时是男性，值为2时是女性，值为0时是未知
 *
 * <AUTHOR>
 * @date 2019/6/19 17:07
 */
@SuppressWarnings("AlibabaEnumConstantsMustHaveComment")
@Getter
@AllArgsConstructor
public enum SexEnum {

    SEX_WEIZHI(0, "未知"),
    SEX_NAN(1, "男"),
    SEX_NV(2, "女");

    private int code;

    /**
     * 信息
     */
    private String des;


    /**
     * 判断字符串是否是枚举定义的值
     *
     * @param str
     * @return
     */
    public static SexEnum asEnum(String str) {
        for (SexEnum me : SexEnum.values()) {
            if (me.des.equalsIgnoreCase(str)) {
                return me;
            }

        }
        return null;
    }

    /**
     * 根据code返回msg
     *
     * @param code code
     * @return msg
     */
    public static String getSex(int code) {
        for (SexEnum sexEnum : SexEnum.values()) {
            if (sexEnum.getCode() == code) {
                return sexEnum.getDes();
            }
        }
        return "未知";
    }
}
