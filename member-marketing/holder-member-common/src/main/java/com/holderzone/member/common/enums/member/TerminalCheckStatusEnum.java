package com.holderzone.member.common.enums.member;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 终端经营校验状态
 */
@Getter
@AllArgsConstructor
public enum TerminalCheckStatusEnum {

    WE_CHANT_PAY(13, "微信支付"),

    SCAN_QR_CODES_PAY(22, "扫码支付"),

    SWIPING_CARD_PAY(23, "刷卡支付"),

    PHONE_CARD_NUM_PAY(24, "输入手机号/卡号支付"),

    PHONE_NUM(30, "手机号"),

    CARD_NUM_PAY(40, "卡号");

    private final int code;
    private final String des;


    public static String getPayName(Integer code) {
        if (code == null) {
            return "";
        }
        TerminalCheckStatusEnum[] values = TerminalCheckStatusEnum.values();
        for (TerminalCheckStatusEnum terminalCheckStatusEnum : values) {
            if (terminalCheckStatusEnum.getCode() == code) {
                return terminalCheckStatusEnum.des;
            }
        }
        return "";
    }

    public static TerminalCheckStatusEnum payWay(Integer code) {
        if (code == null) {
            return null;
        }
        TerminalCheckStatusEnum[] values = TerminalCheckStatusEnum.values();
        for (TerminalCheckStatusEnum terminalCheckStatusEnum : values) {
            if (terminalCheckStatusEnum.getCode() == code) {
                return terminalCheckStatusEnum;
            }
        }
        return null;
    }

}
