package com.holderzone.member.common.dto.event;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 补贴金权益发放 适配class
 *
 * <AUTHOR>
 * @version 1.0
 * @className SendSubsidyRightsEvent
 * @date 2021/10/27 15:49
 * @description 补贴金发送
 * @program holder-member-xxl-job
 */
@Data
@Accessors(chain = true)
public class SendSubsidyRightsEvent extends BaseEvent {

    /**
     * 活动批量guid
     */
    private List<SubsidyActivityEvent> subsidyActivityEventList;

    /**
     * 补发记录guid
     */
    private String recordGuid;

    /**
     * 补发明细记录guid
     */
    private List<String> detailRecordGuid;

    /**
     * 执行时间
     */
    private Date date;
}
