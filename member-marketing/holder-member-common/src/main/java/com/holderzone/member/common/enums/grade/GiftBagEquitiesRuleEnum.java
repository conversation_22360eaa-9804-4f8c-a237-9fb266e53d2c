package com.holderzone.member.common.enums.grade;


import java.util.Objects;

/**
 * 升级礼包权益规则类型
 * <AUTHOR>
 */

public enum GiftBagEquitiesRuleEnum {

    /**
     * 实时赠送：升级时立即赠送，24小时内到账
     */
    GIVE_AWAY_REAL_TIME(0,"实时赠送"),

    /**
     * 自主领取：用户自行在权益中心领取
     */
    SELF_COLLECTION(1,"自主领取"),

    ;

    /**
     * 编号
     */
    private final int code;

    /**
     * 描述
     */
    private final String des;

    GiftBagEquitiesRuleEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    public static GiftBagEquitiesRuleEnum getEnumByCode(Integer code){
        if (Objects.isNull(code)){
            return null;
        }
        for (GiftBagEquitiesRuleEnum type : GiftBagEquitiesRuleEnum.values()){
            if (type.code == code){
                return type;
            }
        }
        return null;
    }
}
