package com.holderzone.member.common.enums.member;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 证件类型，0身份证，1军人证，2护照，3港澳台通行证，4其它
 *
 * <AUTHOR>
 */
@SuppressWarnings("AlibabaEnumConstantsMustHaveComment")
@Getter
@AllArgsConstructor
public enum CertificateTypeEnum {

    CERTIFICATE_IDCARD(0, "身份证"),
    CERTIFICATE_MILITARYID(1, "军人证"),
    CERTIFICATE_PASSPORT(2, "护照"),
    CERTIFICATE_HONGKONGMACAUANDTAIWAN_PASS(3, "港澳台通行证"),
    CERTIFICATE_OTHER(4, "其它");

    private final int code;

    //信息
    private final String des;

    /**
     * 根据code获取name
     *
     * @param code code
     * @return 操作结果
     */
    public static String getMsgByCode(int code) {
        for (CertificateTypeEnum titleEnum : CertificateTypeEnum.values()) {
            if (titleEnum.getCode() == code) {
                return titleEnum.getDes();
            }
        }
        return null;
    }

    /**
     * 判断字符串是否是枚举定义的值
     *
     * @param str 证件类型枚举值
     */
    public static CertificateTypeEnum asEnum(String str) {
        for (CertificateTypeEnum me : CertificateTypeEnum.values()) {
            if (me.des.equalsIgnoreCase(str)) {
                return me;
            }

        }
        return CERTIFICATE_OTHER;
    }


    /**
     * 判断字符串是否是枚举定义的值
     *
     * @param str 证件类型枚举值
     */
    public static CertificateTypeEnum asEnumOrNull(String str) {
        for (CertificateTypeEnum me : CertificateTypeEnum.values()) {
            if (me.des.equalsIgnoreCase(str)) {
                return me;
            }

        }
        return null;
    }
}
