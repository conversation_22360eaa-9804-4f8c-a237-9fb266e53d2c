package com.holderzone.member.common.dto.event;


import com.holderzone.member.common.dto.commodity.callback.CategoryCallbackDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * crm 商品分类回调
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class CategoryCallbackEvent extends BaseEvent {

    private List<CategoryCallbackDTO> categories;

    private List<Long> categoryIds;
}
