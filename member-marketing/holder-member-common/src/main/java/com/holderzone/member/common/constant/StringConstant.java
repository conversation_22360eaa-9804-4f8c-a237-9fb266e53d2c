package com.holderzone.member.common.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 字符串全局常量
 * @date 2021/8/20 10:33
 */
public final class StringConstant {

    /**
     * 零
     */
    public static String ZERO = "0";

    public static String ERRMSG = "errmsg";

    public static String STR_APPLET_ERRCODE = "errcode";

    public static String STR_APPLET_APPID = "appid";

    public static String STR_APPLET_SECRET = "secret";

    public static String STR_APPLET_CODE = "code";

    public static String STR_APPLET_OPENID = "openid";

    public static String STR_APPLET_SESSION_KEY = "session_key";

    public static String STR_APPLET_UNION_ID = "unionid";

    public static String MARKETING = "marketing";

    public static String STR_DEFAULT_PHONE_COUNTRY_CODE = "86";

    public static String STR_DEFAULT_STORE = "无归属门店";

    public static String STR_CONTENT_TYPE = "Content-type";

    public static String STR_APPLICATION_JSON_UTF_8 = "application/json;charset=utf-8";
    public static String I_PASS_CONTENT_TYPE = "application/x-www-form-urlencoded;charset=utf-8";
    public static String HEADER_BASIC = "Basic ";
    public static String HEADER_BEARER = "Bearer ";
    public static String CLIENT_CREDENTIALS = "grant_type=client_credentials";

    /**
     * 会员系统
     */
    public static String MEMBER_SYSTEM = "memberKey";

    public static String CONNECTION = "Connection";

    public static String HTTP_WITHOUT_RPC = "httpWithoutRpc";

    public static String CLOSE = "Close";

    public static final String STR_LINE_FEED = "\r\n";

    public static String HTTP = "http";

    public static String HTTPS = "https";

    public static String HE_SHI_PACKET_MUSIC = "https://oss-sit.holderzone.cn/framework-dev/2022-02-24/1645695350709_32194.mp3";

    public static String LOGIN_TOKEN = "loginToken";

    public static final String FACE_TOKEN = "face_token";

    public static String TEAM_ID = "teamId";

    public static String ACCOUNT_INFO = "userId";

    public static String IDENTIFICATION = "identifications";

    public static String OPERATING_SUBJECT_ID = "operating_subject_id";

    public static String UTF_8 = "UTF-8";

    public static String GBK = "GBK";

    public static String ADD_ALIPAY = "ADD_ALIPAY";

    public static String RSA2 = "RSA2";

    public static String STR_APPLICATION_JSON = "application/json";

    public static String STR_BIAS = "/";

    public static String STR_BIAS_TWO = "-";

    public static String POUND = "#";

    public static String STR_ADD = "+";

    public static String NUMERICAL_VALUE_01 = "0.1";

    private final static String NOTICE_URL = "%s/team/operationSubjectManagement/queryOperatingSubject";

    private final static String USER_URL = "%s/team/operationSubjectManagement/queryUserInformation";

    public final static String DATALIST = "dataList";

    public final static String RESULT = "result";

    public final static String SUCCESS = "success";

    public final static String DATA = "data";

    public final static String COUNT = "count";

    public final static String LENGTH = "length";

    public final static String RETURN_CODE = "returnCode";

    public final static String CURRENT_BALANCE = "，还差 ￥";

    public final static String PAY_SUCCESS = "支付成功";

    public final static String STR_GIFT_BALANCE = "赠送余额最多可扣减";

    public final static String STR_TASK_COMPLETED = "已完成此任务";

    public final static String STR_RECHARGE_BALANCE = "实充余额最多可扣减";

    public final static String STR_SUBSIDY_BALANCE = "补贴余额最多可扣减";

    public final static String FORMAT_TIME = "yyyy-MM-dd HH:mm:ss";

    public final static String FORMAT = "yyyy-MM-dd HH:mm";

    public final static String FORMAT_YYYY_MM_DD = "yyyy-MM-dd";

    public final static String YYYY_MM_DD = "yyyyMMdd";

    public final static String FORMAT_D = "d";

    public final static String FORMAT_M_D = "MM-d";

    public final static String M_D = "M-d";

    public final static String HH = "HH";

    public final static String H = "H";

    public final static String MM = "mm";

    public static String EMPTY = "";

    public static final String EMPTY_JSON_ARRAY = "[]";

    public static final String EMPTY_JSON_OBJ = "{}";

    public static String COMMA = ",";

    public static String CHINESE_COMMA = "，";

    public static String CHINESE_SEMICOLON = "；";

    public static String MARK = "、";

    /**
     * 冒号
     */
    public static String COLON = ":";

    public static String SEMICOLON = ";";

    public static String STAR = "*";

    public static String YEAR = "年";

    public static String MONTH = "月";

    public static String VIP = "VIP";

    public static final String TO = "至";

    public static String LEFT_BRACKET = "(";

    public static String RIGHT_BRACKET = ")";

    public static String PARENT = "0";

    /**
     * 表示当前字符是 获取次数 类型
     */
    public static final String TYPE_SYMBOL_1 = "-1";

    /**
     * 表示当前字符是 应用业务 类型
     */
    public static final String TYPE_SYMBOL_2 = "-2";

    public static final String ADMIN = "管理员";

    public static final String CONTENT_TYPE = "application/vnd.ms-excel";

    public static final String HEADER = "Content-disposition";

    public static final String ATTACHMENT = "attachment;filename=";

    public static final int NUM_VALUE = 5001;

    public static final int FIVE_THOUSAND = 5000;

    /**
     * 永久有效
     */
    public static final String FOREVER_VALIDITY = "永久有效";

    /**
     * 失效
     */
    public static final String UN_VALIDITY = "失效";

    public static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

    public static final String LABEL_UPDATE = "LABEL_UPDATE";

    public static final String GIFT_AMOUNT = "GIFT_AMOUNT";

    public static final String INTEGRAL_UPDATE = "INTEGRAL_UPDATE";

    public static final String SUBSIDY_REISSUE = "SUBSIDY_REISSUE";

    /**
     * 随行红包
     */
    //会员游戏每单次数:订单号:会员guid
    public static final String RED_GAME_NUM = "RED_GAME_NUM:";

    //订单累计分享次数
    public static final String ORDER_SHARE_NUM = "ORDER_SHARE_NUM:";

    //订单累计分享次数
    public static final String ORDER_SHARE_SORT = "ORDER_SHARE_SORT:";

    //订单累计已获取红包金额
    public static final String RED_GAME_AMOUNT_ORDER_TOTAL = "RED_GAME_AMOUNT_ORDER_TOTAL";

    //订单当前活动缓存
    //此缓存一旦锁定，非转台不可改变
    public static final String ORDER_RED_ACTIVITY = "ORDER_RED_ACTIVITY:";

    /**
     * 补贴
     */
    public static final String SUBSIDY_MEMBER_CARD = "SUBSIDY_MEMBER_CARD";

    /**
     * mall
     */
    public static final String MALL_ORDER_PAY_TIME = "MALL_ORDER_PAY_TIME:";

    public static final String MALL_ORDER_PAY_MEMBER = "MALL_ORDER_PAY_MEMBER:";

    /**
     * 会员折扣
     */
    public static final String MALL_DISCOUNT_CALLBACK = "MALL_DISCOUNT_CALLBACK:";


    /**
     * 会员积分
     */
    public static final String MEMBER_INTEGRAL_DEDUCT = "MEMBER_INTEGRAL_DEDUCT:";

    /**
     * 等级开通
     */
    public static final String GRADE_ORDER_PAY_MEMBER = "GRADE_ORDER_PAY_MEMBER:";

    public static final String GRADE_OPEN = "GRADE_OPEN:";

    public static final String RECHARGE_GIFT_MAX_GET = "充值有礼，最高获赠";

    public static final String RECHARGE_GIFT_MAX_GIVE = "最高获赠";

    public static final String RECHARGE_GIFT_GET = "充值有礼，可获赠";

    public static final String GROWTH_VALUE = "成长值";

    public static final String GROWTH_VALUE_X = "成长值*";

    public static final String INTEGRAL_VALUE = "积分";

    public static final String INTEGRAL_VALUE_X = "积分*";

    public static final String COUPON= "优惠券";

    public static final String COUPON_GIFT= "赠送优惠券";

    public static final String YUAN = "元";

    public static final String RECHARGE = "充值";

    public static final String RECHARGE_GIFT_AMOUNT = "赠送金额*%s元";

    public static final String GET_EXPIRE_HOUR = "发放后%s小时后生效";

    public static final String GET_EXPIRE_DAY = "发放后%s天后生效";

    public static final String JOIN_TIMES = "可参与%s次";

    public static final String X_ONE = "*1";

    public static final String COUPON_NOTICE = "卡券到账通知";

    public static final String COUPON_EXPIRE = "优惠券过期提醒";


    public static final String OUTPUT_SEND_MESSAGE = "outputSendMessage";

    public static final String OUTPUT_SEND_OPEN_ELECTRONIC_CARD = "outputSendOpenElectronicCard";

    public static final String OUTPUT_CHANGE_GROWTH_VALUE = "outputChangeGrowthValue";

    public static final String OUTPUT_MEMBER_GRADE_CHANGE = "outputMemberGradeChange";

    //微信授权
    public static final String TICKET = "TICKET:";

    public static final String HELLOWORLD = "HelloWorld";

    public static final String COMPONENT_APP_ID = "component_appid";

    public static final String COMPONENT_APP_SECRET = "component_appsecret";

    //凭据类型
    public static final String COMPONENT_VERIFY_TICKET = "component_verify_ticket";

    //取消授权通知类型
    public static final String UNAUTHORIZED = "unauthorized";

    public static final String BINDING_FAIL_MSG = "当前公众号已被其他运营主体绑定，若要使用该公众号，请先解绑";

    public static final String UNBINDING_FAIL_MSG = "重新授权的公众号需为之前已授权的公众号，不可使用其他公众号，否则将授权失败；\n若需使用其他公众号，需先解绑当前公众号";

    //会员文案缓存
    public static final String SYSTEM_ROLE = "SYSTEM_ROLE:";

    //会员
    public static final String MEMBER = "会员";

    public static final String POS = "pos";

    public static final String TAKEAWAY = "自营外卖";

    public static final String MALL = "会员商城";

    public static final String S2B2C = "私域商城";


    //支付宝授权
    public static final String APP_AUTH_CODE = "APP_AUTH_CODE:";

    public static final String APP_AUTH_TOKEN = "APP_AUTH_TOKEN:";

    public static final String REFRESH_TOKEN = "refresh_token";

    public static final String AUTHORIZATION_CODE = "authorization_code";
    public static final String AUTHORIZATION = "Authorization";

    public static final String TINYAPP = "TINYAPP";

    public static final String INTERFACE_AUTH = "INTERFACE_AUTH";

    /**
     * 无数据id
     */
    public static final String NO_DATA_ID = "0";


    public static final String XXL_JOB_SUBSIDY = "XXL_JOB_SUBSIDY:";


    /**
     * 库存数量查询
     */
    public static final String COUPON_PACKAGE_INVENTORY = "COUPON_PACKAGE_INVENTORY:";

    public static final String COUPON_PACKAGE_INVENTORY_NUM = "COUPON_PACKAGE_INVENTORY_NUM:";

    /**
     * 库存防止修改
     */
    public static final String LOCK_COUPON_PACKAGE_INVENTORY = "LOCK_COUPON_PACKAGE_INVENTORY:";

    public static final String LOCK_COUPON_PACKAGE_INVENTORY_NUM = "LOCK_COUPON_PACKAGE_INVENTORY_NUM:";


    /**
     * 活动防重复
     */
    public static final String LOCK_COUPON_PACKAGE_ACTIVITY = "LOCK_COUPON_PACKAGE_ACTIVITY:";

    public static final String COUPON_PACKAGE_ACTIVITY = "COUPON_PACKAGE_ACTIVITY:";


    /**
     * 定时防重复
     */
    public static final String LOCK_XXL_JOB_COUPON_NOTICE = "LOCK_XXL_JOB_COUPON_NOTICE:";
    public static final String XXL_JOB_COUPON_NOTICE = "XXL_JOB_COUPON_NOTICE:";


    public static final String LOCK_XXL_JOB_CARD_NOTICE = "LOCK_XXL_JOB_CARD_NOTICE:";
    public static final String XXL_JOB_CARD_NOTICE = "XXL_JOB_CARD_NOTICE:";


    /**
     * 定时防重复
     */
    public static final String LOCK_XXL_JOB_COUPON_PACKAGE = "LOCK_XXL_JOB_COUPON_PACKAGE:";

    public static final String XXL_JOB_COUPON_PACKAGE = "XXL_JOB_COUPON_PACKAGE:";

    /**
     * 累加成功发放库存
     */
    public static final String LOCK_SUCCESS_COUPON_PACKAGE_NUM = "LOCK_SUCCESS_COUPON_PACKAGE_NUM:";

    public static final String SUCCESS_COUPON_PACKAGE_NUM = "SUCCESS_COUPON_PACKAGE_NUM:";

    /**
     * 自助领券防重复锁
     */
    public static final String SELF_RECEIVE_COUPON_LOCK = "SELF_RECEIVE_COUPON_LOCK:";

    public static String COUPON_STR = "优惠劵";

    /**
     * 会员卡权益
     */
    public static final String CARD_RIGHTS = "CARD_RIGHTS:";

    /**
     * Card_JOB
     */
    public static final String CARD_JOB = "CARD_JOB";

    /**
     * 积分过期
     */
    public static final String DELAYED_INTEGRAL = "积分过期";

    public static final String ENTERPRISE_WECHAT_FILE = "enterprise_wechat_file:";

    /**
     * 企微客户昵称
     */
    public static final String ENTERPRISE_WECHAT_CUSTOMER_NICKNAME = "#客户昵称#";

    /**
     * 微信用户
     */
    public static final String WECHAT_DEFAULT_USERNAME = "微信用户";
}
