package com.holderzone.member.common.enums.member;

import java.util.Objects;

/**
 * @ProjectName: member-marketing
 * @ClassName: ConditionSetEnum
 * @Author: pantao
 * @Description:
 * @Date: 2021/8/24 11:33
 * @Version: 1.0
 */
public enum ConditionSetEnum {

    /**
     * 满足以下任一条件
     */
    ONE_CONDITION (0,"满足以下任一条件"),

    /**
     * 满足以下所有条件
     */
    ALL_CONDITION(1,"满足以下所有条件");

    /**
     * 编号
     */
    private int code;

    /**
     * 描述
     */
    private String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    ConditionSetEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    /**
     * 根据标号获取描述
     * @param code 编号
     * @return
     */
    public static String getDesByCode(Integer code){
        if (Objects.isNull(code)){
            return "";
        }
        for (ConditionSetEnum conditionSetEnum : ConditionSetEnum.values()){
            if (conditionSetEnum.getCode() == code){
                return conditionSetEnum.getDes();
            }
        }
        return "";
    }
}
