package com.holderzone.member.common.config.mybatis;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.member.common.annotation.FunctionLabel;

import java.util.Collection;
import java.util.List;

/**
 * @date 2020/05/09 14:25
 * @description 自定义表根据Guid的基本数据库操作实现
 */
public class HolderBaseServiceImpl<M extends HolderBaseMapper<T>, T> extends ServiceImpl<M, T> implements IHolderBaseService<T> {

    /**
     * 
     * 通过GUID查询
     *
     * @param guid guid
     * @return T
     */
    @FunctionLabel(mark = "通过GUID查询")
    @Override
    public T queryByGuid(String guid) {
        return baseMapper.queryByGuid(guid);
    }

    /**
     * 
     * 通过多个GUID查询
     *
     * @param guids guids
     * @return List<T>
     */
    @FunctionLabel(mark = "通过多个GUID查询")
    @Override
    public List<T> queryByGuids(Collection<String> guids) {
        return baseMapper.queryByGuids(guids);
    }

    /**
     * 
     * 根据GUID修改
     *
     * @param t update model
     * @return boolean
     */
    @FunctionLabel(mark = "根据GUID修改")
    @Override
    public boolean updateByGuid(T t) {
        return baseMapper.updateByGuid(t);
    }

    /**
     * 
     * 根据GUID删除
     *
     * @param guid guid
     * @return boolean
     */
    @FunctionLabel(mark = "根据GUID删除")
    @Override
    public boolean removeByGuid(String guid) {
        return baseMapper.removeByGuid(guid);
    }

    /**
     * 
     * 根据多个GUID删除
     *
     * @param guids guids
     * @return boolean
     */
    @FunctionLabel(mark = "根据多个GUID删除")
    @Override
    public boolean removeByGuids(Collection<String> guids) {
        return baseMapper.removeByGuids(guids);
    }


}
