package com.holderzone.member.common.enums.member;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 零售渠道
 * <AUTHOR>
 */
@SuppressWarnings("AlibabaEnumConstantsMustHaveComment")
@Getter
@AllArgsConstructor
public enum CloudSourceTypeEnum {
    UNDEFINED(-1, ""),
    ADD_BACKGROUND(0, "管理后台"),
    ADD_CLOUD_MARKET(2, "零售云-POS"),

    ADD_WECHAT_ZHUANCAN(53, "小程序"),

    ADD_BACKGROUND_IMPORT(3, "后台导入"),

    /**
     * 私域商城
     */
    MALL_PC(70, "私域商城-PC"),
    MALL_H5(71, "私域商城-H5"),
    MALL_WECHAT_APPLET(72, "微信小程序"),

    /**
     * 餐饮云
     */
    REPAST_WECHAT_APPLET(80,"餐饮云-微信小程序"),
    REPAST_ALI_APPLET(81,"餐饮云-支付宝小程序"),
    REPAST_H5(82,"餐饮云-H5"),
    REPAST_AIO(83, "餐饮云-一体机"),
    REPAST_POS(84, "餐饮云-POS"),
    ;

    private final int code;

    /**
     * 信息
     */
    private final String des;

    /**
     * 根据code获取name
     *
     * @param code code
     * @return 操作结果
     */
    public static String getMsgByCode(int code) {
        for (CloudSourceTypeEnum titleEnum : CloudSourceTypeEnum.values()) {
            if (titleEnum.getCode() == code) {
                return titleEnum.getDes();
            }
        }
        return null;
    }

    public static String getDesByCode(Integer code) {
        if (Objects.isNull(code)) {
            return "-";
        }
        for (CloudSourceTypeEnum titleEnum : CloudSourceTypeEnum.values()) {
            if (titleEnum.getCode() == code) {
                String d = titleEnum.getDes();
                if (d.contains("注册")) {
                    d = d.replace("注册", "");
                }
                return d;
            }
        }
        return null;
    }

    public static CloudSourceTypeEnum valueOfCode(Integer code) {
        if (code == null) {
            return UNDEFINED;
        }
        for (CloudSourceTypeEnum sourceType : values()) {
            if (code == sourceType.getCode()) {
                return sourceType;
            }
        }
        return UNDEFINED;
    }

    /**
     * 是否硬件设备
     * 一体机、自助充值机
     * @param source
     * @return
     */
    public static boolean isDevice(int source) {
        return source == 2 || source == 60;
    }

    /**
     * 私域商城来源
     */
    public static List<Integer> getMallSource() {
        List<CloudSourceTypeEnum> sourceTypeEnums = Arrays.asList(
                MALL_PC,
                MALL_H5,
                MALL_WECHAT_APPLET);

        return sourceTypeEnums.stream().map(CloudSourceTypeEnum::getCode).collect(Collectors.toList());
    }
}
