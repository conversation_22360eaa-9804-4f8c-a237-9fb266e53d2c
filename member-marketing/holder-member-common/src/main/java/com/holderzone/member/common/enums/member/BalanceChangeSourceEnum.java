package com.holderzone.member.common.enums.member;

/**
 * author: pantao
 */
public enum  BalanceChangeSourceEnum {

    MANAGE_BACK(0,"管理后台"),

    ALL_IN_ONE(1,"一体机"),

    APPLET(2,"小程序"),

    UNKNOWN(-1,"未知");

    private int code;

    private String des;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    BalanceChangeSourceEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public static String getDesByCode(int code){
        for (BalanceChangeSourceEnum balanceChangeSourceEnum : BalanceChangeSourceEnum.values()){
            if (balanceChangeSourceEnum.getCode() == code){
                return balanceChangeSourceEnum.getDes();
            }
        }
        return "";
    }
}
