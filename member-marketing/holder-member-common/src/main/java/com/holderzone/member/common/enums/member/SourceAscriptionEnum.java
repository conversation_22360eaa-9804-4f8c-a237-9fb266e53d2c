package com.holderzone.member.common.enums.member;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 会员来源归属，0门店，1品牌，2企业，3运营主体，4掌控者平台
 *
 * <AUTHOR>
 */
@SuppressWarnings("AlibabaEnumConstantsMustHaveComment")
@Getter
@AllArgsConstructor
public enum SourceAscriptionEnum {
    SHOP(0, "门店"),
    BRAND(1, "品牌"),
    ENTERPRISE(2, "企业"),
    OPERATION(3, "运营主体"),
    HOLDER_PLATFORM(4, "掌控者平台");

    private int code;

    /**
     * 信息
     */
    private String des;
}
