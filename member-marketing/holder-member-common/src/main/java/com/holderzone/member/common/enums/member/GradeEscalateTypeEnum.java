package com.holderzone.member.common.enums.member;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/22 下午2:48
 * @description 等级开通类型
 */
@Getter
@AllArgsConstructor
public enum GradeEscalateTypeEnum {

    /**
     * 申请开通
     */
    ESCALATE_OPEN(0, "申请开通"),

    /**
     * 审核中
     */
    EXAMINE(1, "审核中"),

    /**
     * 立即升级
     */
    UPGRADE_NOW(2, "立即升级"),
    ;

    private final Integer code;

    private final String des;

    public static String getName(String name) {
        if (name == null) {
            return "";
        }
        GradeEscalateTypeEnum[] values = GradeEscalateTypeEnum.values();
        for (GradeEscalateTypeEnum roleTypeEnum : values) {
            if (Objects.equals(roleTypeEnum.name(), name)) {
                return roleTypeEnum.getDes();
            }
        }
        return "";
    }
}
