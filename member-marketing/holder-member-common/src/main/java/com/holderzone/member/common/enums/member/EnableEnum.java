package com.holderzone.member.common.enums.member;

import java.util.Objects;

/**
 * @ProjectName: member-marketing
 * @ClassName: EnableEnum
 * @Author: pantao
 * @Description: 启用、禁用枚举（期望全局使用）
 * @Date: 2021/8/24 12:03
 * @Version: 1.0
 */
public enum EnableEnum {

    /**
     * 启用
     */
    ENABLE(0,"启用"),

    /**
     * 禁用
     */
    NOT_ENABLE (1,"禁用"),

    /**
     * 未知类型
     */
    UNDEFINED(-1, "未知类型");

    private int code;

    private String des;


    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    EnableEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }


    public static String getDesByCode(Integer code){
        if (Objects.isNull(code)){
            return "";
        }
        for (EnableEnum enableEnum : EnableEnum.values()){
            if (enableEnum.getCode() == code){
                return enableEnum.des;
            }
        }
        return "";
    }

    public static EnableEnum getEnumByCode(int code) {
        for (EnableEnum enableEnum : EnableEnum.values()) {
            if (enableEnum.getCode() == code) {
                return enableEnum;
            }
        }
        return EnableEnum.UNDEFINED;
    }


}
