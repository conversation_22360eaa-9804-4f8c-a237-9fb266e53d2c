package com.holderzone.member.common.util.number;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.member.common.exception.MemberBaseException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description guid 生成工具类
 */
@Component
public class GuidGeneratorUtil {

    @Autowired
    private RedisTemplate redisTemplate;

    public Long getGuid(String tab) {
        try {
            return BatchIdGenerator.getGuid(redisTemplate, tab);
        } catch (IOException e) {
            throw new MemberBaseException("BatchIdGenerator生成【" + tab + "】对应guid失败");
        }
    }

    public String getStringGuid(String tab) {
        return String.valueOf(getGuid(tab));
    }


    public String getCode(String guid) {
        StringBuilder code = new StringBuilder();
        if (StringUtils.isNotEmpty(guid)) {
            SecureRandom rand = new SecureRandom();
            for (int j = 0; j < 6; j++) {
                code.append(guid.charAt(rand.nextInt(9)));
            }
        }
        return code.toString();
    }

    public String getEightCode(String guid) {
        StringBuilder code = new StringBuilder();
        if (StringUtils.isNotEmpty(guid)) {
            SecureRandom rand = new SecureRandom();
            for (int j = 0; j < 8; j++) {
                code.append(guid.charAt(rand.nextInt(9)));
            }
        }
        return code.toString();
    }

    public List<String> getGuids(String tab, int count) {
        try {
            List<String> guids = new ArrayList<>();
            for (int i = 0; i < count; i++) {
                String guid = String.valueOf(BatchIdGenerator.getGuid(redisTemplate, tab));
                guids.add(guid);
            }
            return guids;
        } catch (IOException e) {
            throw new BusinessException("BatchIdGenerator生成【" + tab + "】对应guid失败");
        }
    }

    /**
     * 批量生成guids
     *
     * @param tab
     * @param count 最大 10240L
     * @return
     */
    public List<String> getGuidsNew(String tab, int count) {
        final List<Long> guids = BatchIdGenerator.batchGetGuids(redisTemplate, tab, (long)count);
        return guids.stream().map(String::valueOf).collect(Collectors.toList());
    }
}
