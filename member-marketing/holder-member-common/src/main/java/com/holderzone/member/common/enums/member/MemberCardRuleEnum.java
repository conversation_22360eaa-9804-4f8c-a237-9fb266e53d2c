package com.holderzone.member.common.enums.member;

/**
 * 会员卡规则枚举
 */
public enum MemberCardRuleEnum {

    SMS_NOTIFICATION(0, "短信通知"),
    ACCOUNT_NOTIFICATION(1, "微信公众号通知"),
    MINI_PROGRAM_NOTIFICATION(2, "小程序通知"),
    ;
    private final int code;

    /**
     * 信息
     */
    private final String des;

    MemberCardRuleEnum(int code,String des){
        this.code = code;
        this.des = des;
    }

    /**
     * 根据code获取name
     *
     * @param code code
     * @return 操作结果
     */
    public static String getMsgByCode(int code) {
        for (MemberCardRuleEnum titleEnum : MemberCardRuleEnum.values()) {
            if (titleEnum.getCode() == code) {
                return titleEnum.getDes();
            }
        }
        return null;
    }

    public int getCode(){
        return code;
    }

    public String getDes(){
        return des;
    }

}
