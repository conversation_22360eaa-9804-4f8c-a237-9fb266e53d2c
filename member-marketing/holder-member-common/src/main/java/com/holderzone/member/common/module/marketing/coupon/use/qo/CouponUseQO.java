package com.holderzone.member.common.module.marketing.coupon.use.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.dto.page.PageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 优惠劵核销明细
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class CouponUseQO extends PageDTO implements Serializable {


    private static final long serialVersionUID = 725728891084387542L;
    /**
     * 主体
     */
    private String operSubjectGuid;

    /**
     * 劵guid
     */
    private String couponGuid;

    /**
     * 关键字:
     * 优惠券ID/优惠券名称/券码/核销用户/核销手机号 模糊查询
     */
    private String keywords;

    /**
     * 活动id、活动名称
     */
    private String couponPackageKeywords;

    /**
     * 核销门店
     */
    private List<String> storeGuids;

    /**
     * 订单
     */
    private String orderNumber;

    /**
     * 核销渠道
     * @see com.holderzone.member.common.enums.member.SourceTypeEnum
     */
    private Integer source;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 是否导出操作
     */
    private Integer isExport;



}
