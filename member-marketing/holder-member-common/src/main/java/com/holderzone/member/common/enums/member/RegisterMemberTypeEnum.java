package com.holderzone.member.common.enums.member;


/**
 * <AUTHOR>
 */

public enum RegisterMemberTypeEnum {

    ALL_REGISTER(0,"所有注册会员"),

    SATISFY_CONDITION(1,"仅满足条件会员"),

    ASSIGN(2,"指定会员"),
    ;

    private int code;

    private String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    RegisterMemberTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }


}
