package com.holderzone.member.common.vo.growth;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-01-20 20:35
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MemberGrowthValueRelationVO {

    /**
     * 会员guid
     */
    private String memberGuid;

    /**
     * 成长值/积分
     */
    private Integer growthValue;
}
