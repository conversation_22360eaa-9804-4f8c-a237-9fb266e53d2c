package com.holderzone.member.common.dto.event;

import com.holderzone.member.common.enums.member.RoleTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SendMemberGradeChangeEvent
 * @date 2022/01/04 15:49
 * @description 会员等级变化
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SendMemberGradeChangeEvent extends BaseEvent {

    private List<String> memberGuidList;

    private String operSubjectGuid;

    private Integer sourceType;

    private Integer isRefresh;

    /**
     * @see com.holderzone.member.common.enums.member.RoleTypeEnum
     */
    private String roleType = RoleTypeEnum.MEMBER.name();
}
