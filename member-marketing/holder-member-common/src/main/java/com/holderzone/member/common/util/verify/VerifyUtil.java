package com.holderzone.member.common.util.verify;


import com.holderzone.member.common.enums.ResponseBase;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.function.BranchHandle;
import com.holderzone.member.common.function.PresentOrElseHandler;
import com.holderzone.member.common.function.SingleHandle;
import com.holderzone.member.common.function.ThrowExceptionFunction;

/**
 * @version 1.0
 * @className VerifyUtil
 * @description 校验工具
 * @program holder-member-merchant
 */
public enum VerifyUtil {
    Instance;

    public static void verify(boolean expression, MemberAccountExceptionEnum exceptionEnum) {
        if (!expression) {
            throw new MemberBaseException(exceptionEnum);
        }
    }

    public static void verify(boolean expression, ResponseBase response) {
        if (!expression) {
            throw new MemberBaseException(response);
        }
    }

    public static void isTrue(boolean expression, String message) {
        if (expression) {
            throw new MemberBaseException(message);
        }
    }

    public static void notNull(Object object, String message) {
        if (object == null) {
            throw new MemberBaseException(message);
        }
    }


    /**
     * 如果参数为true抛出异常
     *
     * @return com.holderzone.member.common.function.ThrowExceptionFunction
     **/
    public static ThrowExceptionFunction throwException(boolean condition) {

        return (errorMessage) -> {
            if (condition) {
                throw new RuntimeException(errorMessage);
            }
        };
    }

    /**
     * 参数为true时进行操作
     *
     * @return com.holderzone.member.common.function.SingleHandle
     **/
    public static SingleHandle isTure(boolean condition) {

        return (trueHandle) -> {
            if (condition) {
                trueHandle.run();
            }
        };
    }

    /**
     * 参数为true或false时，分别进行不同的操作
     *
     * @return com.holderzone.member.common.function.BranchHandle
     **/
    public static BranchHandle isTureOrFalse(boolean condition) {

        return (trueHandle, falseHandle) -> {
            if (condition) {
                trueHandle.run();
            } else {
                falseHandle.run();
            }
        };
    }

    /**
     * 参数为true或false时，分别进行不同的操作
     *
     * @return com.holderzone.member.common.function.PresentOrElseHandler
     **/
    public static PresentOrElseHandler<?> isBlankOrNoBlank(String str){

        return (consumer, runnable) -> {
            if (str == null || str.length() == 0){
                runnable.run();
            } else {
                consumer.accept(str);
            }
        };
    }

}
