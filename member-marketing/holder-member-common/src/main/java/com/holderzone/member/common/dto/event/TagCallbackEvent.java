package com.holderzone.member.common.dto.event;

import com.holderzone.member.common.dto.commodity.callback.TagCallbackDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * crm 商品标签回调
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class TagCallbackEvent extends BaseEvent {

    private List<TagCallbackDTO> tags;

    private List<Long> tagIds;
}
