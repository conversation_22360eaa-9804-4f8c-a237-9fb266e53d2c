package com.holderzone.member.common.enums.member;

public enum MemberStoreEnum {

    RELIEVE_STORE(1,"无归属门店"),
    MERCHANTS_UNBUNDLING(2,"商家解绑"),
    BUSINESS_CHANGE(3,"商家变更");

    private final int code;

    private final String des;

    MemberStoreEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

}
