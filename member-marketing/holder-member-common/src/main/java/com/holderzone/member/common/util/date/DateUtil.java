package com.holderzone.member.common.util.date;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.enums.DateUnitEnum;
import com.holderzone.member.common.enums.grade.GradeMessageInfoEnum;
import com.holderzone.member.common.enums.growth.DataUnitEnum;
import com.holderzone.member.common.enums.growth.EffectiveTimeEnum;
import com.holderzone.member.common.util.number.NumberUtil;
import com.holderzone.member.common.vo.grade.EffectiveTimeVO;
import lombok.Value;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * 使用JDK 1.8写的帮助类
 */
@UtilityClass //声明成一个工具类，加入static并且throw新异常
@Value
@Slf4j
public class DateUtil {

    public static final String PATTERN_DATETIME = "yyyy-MM-dd HH:mm:ss";
    public static final String PATTERN_DATE = "yyyy-MM-dd";
    public static final String PATTERN_TIME = "HH:mm:ss";
    public static final String PATTERN_SECOND = "yyyyMMddHHmmss";

    public static final String PATTERN_DAY_TIME = "dd日 HH:mm:ss";
    public static final String HOUR_MINUTE = "HH:mm";

    public static final String MIN_TIME = "T00:00:00";
    public static final String MAX_TIME = "T23:59:59.999999999";

    public static final String DATE_00_FORMAT = "yyyy-MM-dd 00:00:00";

    public static final String DATE_PATTERN = "yyyyMMdd";

    public static final String DATETIME = "yyyyMMddHHmmss";

    public static final String MONDAY = "周一";

    public static final String TUESDAY = "周二";

    public static final String WEDNESDAY = "周三";

    public static final String THURSDAY = "周四";

    public static final String FRIDAY = "周五";

    public static final String SATURDAY = "周六";

    public static final String SUNDAY = "周天";

    private static final ThreadLocal<Map<String, DateFormat>> dateFormatThreadLocal = new ThreadLocal<>();

    public static final DateTimeFormatter FORMAT_HM = DateTimeFormatter.ofPattern(HOUR_MINUTE);

    public static final DateTimeFormatter MINUTE_END_TIME = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:59");

    public static final DateTimeFormatter MINUTE_START_TIME = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:00:00");

    private static DateFormat getDateFormat(String pattern) {
        if (pattern == null || pattern.trim().length() == 0) {
            throw new IllegalArgumentException("pattern cannot be empty.");
        }

        Map<String, DateFormat> dateFormatMap = dateFormatThreadLocal.get();
        if (dateFormatMap != null && dateFormatMap.containsKey(pattern)) {
            return dateFormatMap.get(pattern);
        }

        synchronized (dateFormatThreadLocal) {
            if (dateFormatMap == null) {
                dateFormatMap = new HashMap<>();
            }
            dateFormatMap.put(pattern, new SimpleDateFormat(pattern));
            dateFormatThreadLocal.set(dateFormatMap);
        }
        dateFormatThreadLocal.remove();
        return dateFormatMap.get(pattern);
    }

    /**
     * parse date
     *
     * @param dateString
     * @param pattern
     * @return
     * @throws ParseException
     */
    public static Date parse(String dateString, String pattern) {
        try {
            return getDateFormat(pattern).parse(dateString);
        } catch (Exception e) {
            log.warn("parse date error, dateString = {}, pattern={}; errorMsg = {}", dateString, pattern, e.getMessage());
            return null;
        }
    }

    /**
     * format date
     *
     * @param date
     * @param patten
     * @return
     * @throws ParseException
     */
    public static String format(Date date, String patten) {
        return getDateFormat(patten).format(date);
    }


    /**
     * jdk1.8线程安全的时间处理类 返回还是jdk 1.8之前的类
     *
     * @param dateStr
     * @return
     */
    public Date parseSync8(String dateStr, String pattern) {
        if (StringUtils.isBlank(pattern)) {
            pattern = DATE_PATTERN;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);

        LocalDate date = LocalDate.parse(dateStr, formatter);
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdf = date.atStartOfDay(zoneId);

        return Date.from(zdf.toInstant());
    }


    public String formatSync8(Date date, String pattern) {
        if (StringUtils.isBlank(pattern)) {
            pattern = DATE_PATTERN;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        ZoneId zoneId = ZoneId.systemDefault();
        Instant instant = date.toInstant();
        LocalDateTime now = instant.atZone(zoneId).toLocalDateTime();

        return now.format(formatter);
    }


    /**
     * 格式化时间
     *
     * @param dateStr
     * @param pattern
     * @return 默认返回时间格式（yyyy-MM-dd) LocalDate
     */
    public LocalDate parseLocalDate(String dateStr, String pattern) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }

        if (StringUtils.isBlank(pattern)) {
            pattern = PATTERN_DATE;
        }

        return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(pattern));
    }

    public LocalTime parseLocalTime(String dateStr, String pattern) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }

        if (StringUtils.isBlank(pattern)) {
            pattern = PATTERN_TIME;
        }

        return LocalTime.parse(dateStr, DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 格式化时间
     *
     * @param dateStr
     * @param pattern
     * @return 默认返回时间格式（yyyy-MM-dd HH:mm:ss) LocalDateTime
     */
    public LocalDateTime parseLocalDateTime(String dateStr, String pattern) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }

        if (StringUtils.isBlank(pattern)) {
            pattern = PATTERN_DATETIME;
        }

        return LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern(pattern));
    }


    /**
     * localDate转换为Date时间
     *
     * @param localDate
     * @return
     */
    public Date localDateConvert(LocalDate localDate) {

        LocalDate currentDate = Optional.ofNullable(localDate).orElse(LocalDate.now());

        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdf = currentDate.atStartOfDay(zoneId);

        return Date.from(zdf.toInstant());
    }


    /**
     * localDateTime转换为Date
     *
     * @param localDateTime
     * @return
     */
    public Date localDateTimeConvert(LocalDateTime localDateTime) {
        //如果设置为空,择获取当前时间
        LocalDateTime currentDate = Optional.ofNullable(localDateTime)
                .orElse(LocalDateTime.now());

        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdf = currentDate.atZone(zoneId);

        return Date.from(zdf.toInstant());
    }


    /**
     * 将LocalDate 格式化
     *
     * @param localDate
     * @param pattern
     * @return
     */
    public String formatLocalDate(LocalDate localDate, String pattern) {
        if (Objects.isNull(localDate)) {
            return "";
        }
        DateTimeFormatter ofPattern = DateTimeFormatter.ofPattern(pattern);
        return localDate.format(ofPattern);
    }


    /**
     * 将localDateTime格式化
     *
     * @param localDateTime
     * @param pattern
     * @return
     */
    public String formatLocalDateTime(LocalDateTime localDateTime, String pattern) {
        if (Objects.isNull(localDateTime)) {
            return "";
        }
        DateTimeFormatter ofPattern = DateTimeFormatter.ofPattern(pattern);
        return localDateTime.format(ofPattern);
    }


    /**
     * 统计两个时间的相隔时间
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public Long betweenTime(LocalDateTime startTime, LocalDateTime endTime, DateUnit dateUnit) {

        DateUnit currentUnit = Optional.ofNullable(dateUnit).orElse(DateUnit.MILLIS);
        Long time = null;
        Duration between = Duration.between(startTime, endTime);
        switch (currentUnit) {
            case MILLIS:
                time = between.toMillis();
                break;
            case SECOND:
                time = between.getSeconds();
                break;
            case HOUR:
                time = between.toHours();
                break;
            case DAY:
                time = between.toDays();
                break;
            default:
                log.info("未找到对应的处理信息");
                break;

        }

        return time;
    }


    /**
     * 统计两个时间的相隔时间
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public Long between(LocalDate startTime, LocalDate endTime, DateUnit dateUnit) {

        DateUnit currentUnit = Optional.ofNullable(dateUnit).orElse(DateUnit.MILLIS);
        Long time = null;
        Duration between = Duration.between(startTime, endTime);
        if (currentUnit == DateUnit.DAY) {
            time = between.toDays();
        } else {
            log.info("未找到对应的处理信息");
        }
        return time;
    }

    /**
     * 获取本月最后一天时间
     *
     * @param localDate
     * @return
     */
    public LocalDate lastDayLocalDate(LocalDate localDate) {
        LocalDate today = Optional.ofNullable(localDate).orElse(LocalDate.now());

        return today.with(TemporalAdjusters.lastDayOfMonth());
    }


    /**
     * 获取本月最后一天时间
     *
     * @param localDate
     * @return
     */
    public LocalDateTime lastDayLocalDateTime(LocalDateTime localDate) {
        LocalDateTime today = Optional.ofNullable(localDate).orElse(LocalDateTime.now());

        return today.with(TemporalAdjusters.lastDayOfMonth());
    }


    /**
     * 获取对比时间
     *
     * @param localDate
     * @param day
     * @return
     */
    public LocalDate plusDays(LocalDate localDate, Integer day) {
        LocalDate today = Optional.ofNullable(localDate).orElse(LocalDate.now());

        return today.plusDays(day);
    }

    /**
     * @param localDate
     * @param day
     * @return
     */
    public LocalDateTime plusDaysTime(LocalDateTime localDate, Integer day) {
        LocalDateTime today = Optional.ofNullable(localDate).orElse(LocalDateTime.now());

        return today.plusDays(day);
    }

    /**
     * localDateTime比当前时间小 返回true
     *
     * @param localDateTime 指定时间
     * @return 操作结果
     */
    public boolean compareCurrentDate(LocalDateTime localDateTime) {

        if (Objects.isNull(localDateTime)) {
            return false;
        }
        //获取当前时间
        LocalDateTime nowTime = LocalDateTime.now();
        return nowTime.isAfter(localDateTime);
    }

    public boolean compareCurrentTime(String localTime, String pattern) {

        if (Objects.isNull(localTime)) {
            return false;
        }
        //获取当前时间
        LocalTime nowTime = LocalTime.now();
        return nowTime.isAfter(parseLocalTime(localTime, pattern));
    }

    /**
     * 字符串转 hh:mm
     *
     * @param localTime
     * @return
     */
    public LocalTime parseHM(String localTime) {
        return LocalTime.parse(localTime, DateTimeFormatter.ofPattern(HOUR_MINUTE));
    }

    /**
     * 是否在两个时间内 HH:MM
     *
     * @param startTime   开始
     * @param endTime     结束
     * @param currentTime 当前
     * @return
     */
    public boolean betweenHM(String startTime, String endTime, LocalTime currentTime) {
        final LocalTime start = parseHM(startTime);
        final LocalTime end = parseHM(endTime);
        return currentTime.isAfter(start) && currentTime.isBefore(end);
    }


    public String weekCipherTransform(String cipherWeek) {
        if (cipherWeek.equals(String.valueOf(NumberConstant.NUMBER_1))) {
            return MONDAY;
        }
        if (cipherWeek.equals(String.valueOf(NumberConstant.NUMBER_2))) {
            return TUESDAY;
        }
        if (cipherWeek.equals(String.valueOf(NumberConstant.NUMBER_3))) {
            return WEDNESDAY;
        }
        if (cipherWeek.equals(String.valueOf(NumberConstant.NUMBER_4))) {
            return THURSDAY;
        }
        if (cipherWeek.equals(String.valueOf(NumberConstant.NUMBER_5))) {
            return FRIDAY;
        }
        if (cipherWeek.equals(String.valueOf(NumberConstant.NUMBER_6))) {
            return SATURDAY;
        }
        if (cipherWeek.equals(String.valueOf(NumberConstant.NUMBER_7))) {
            return SUNDAY;
        }
        return null;
    }

    /**
     * 获取指定日期年第一天
     *
     * @param date
     * @return
     */
    public static Date getCurrYearFirst(Date date) {
        Calendar currCal = Calendar.getInstance();
        currCal.setTime(date);
        int currentYear = currCal.get(Calendar.YEAR);
        return getYearFirst(currentYear);
    }

    /**
     * 获取某年第一天日期
     *
     * @param year 年份
     * @return Date
     */
    public static Date getYearFirst(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        return calendar.getTime();
    }

    /**
     * 获取指定日期当周第一天
     *
     * @param time 指定日期
     * @return 操作结果
     */
    public static Date getWeekByDate(Date time) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(time);
        // 判断要计算的日期是否是周日，如果是则减一天计算周六的，否则会出问题，计算到下一周去了
        int dayWeek = cal.get(Calendar.DAY_OF_WEEK);// 获得当前日期是一个星期的第几天
        if (1 == dayWeek) {
            cal.add(Calendar.DAY_OF_MONTH, -1);
        }
        cal.setFirstDayOfWeek(Calendar.MONDAY);// 设置一个星期的第一天，按中国的习惯一个星期的第一天是星期一
        int day = cal.get(Calendar.DAY_OF_WEEK);// 获得当前日期是一个星期的第几天
        cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - day);// 根据日历的规则，给当前日期减去星期几与一个星期第一天的差值
        return findDayStart(cal.getTime());
    }

    /**
     * 获取指定日期当月第一天
     *
     * @param date 日期
     * @return 操作结果
     */
    public static Date findMonthStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return findDayStart(calendar.getTime());
    }


    /**
     * 查询指定日期 00:00:00
     *
     * @return 00:00:00
     * @version 4.0.0
     */
    public static Date findDayStart(Date date) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 参数日期是否属于今天
     *
     * @param date
     * @return
     */
    public static boolean isToday(Date date) {
        SimpleDateFormat fmt = new SimpleDateFormat(PATTERN_DATE);
        return fmt.format(date).equals(fmt.format(new Date()));
    }

    public static boolean isToday(LocalDate date) {
        return LocalDate.now().isEqual(date);
    }

    /**
     * Date转LocalDate
     *
     * @param date
     */
    public static LocalDateTime date2LocalDate(Date date) {
        if (null == date) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }


    public static String getTmpDate(Date date, String s) {
        SimpleDateFormat format2 = new SimpleDateFormat(s);
        return format2.format(date);
    }

    /**
     * 当天开始时间
     *
     * @return
     */
    public static LocalDateTime getDayStartTime() {
        Calendar todayStart = Calendar.getInstance();
        todayStart.set(Calendar.HOUR_OF_DAY, 0);
        todayStart.set(Calendar.MINUTE, 0);
        todayStart.set(Calendar.SECOND, 0);
        todayStart.set(Calendar.MILLISECOND, 0);
        return LocalDateTime.ofInstant(todayStart.getTime().toInstant(), ZoneId.systemDefault());
    }

    /**
     * 当天结束时间
     *
     * @return
     */
    public static LocalDateTime getDayEndTime() {
        Calendar todayEnd = Calendar.getInstance();
        todayEnd.set(Calendar.HOUR_OF_DAY, 23);
        todayEnd.set(Calendar.MINUTE, 59);
        todayEnd.set(Calendar.SECOND, 59);
        return LocalDateTime.ofInstant(todayEnd.getTime().toInstant(), ZoneId.systemDefault());
    }

    /**
     * 获得本周开始时间
     *
     * @return
     */
    public static LocalDateTime getWeekStartTime() {
        LocalDate today = LocalDate.now();
        return today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).atTime(0, 0);
    }

    /**
     * 获得本周结束点时间
     *
     * @return
     */
    public static LocalDateTime getWeekEndTime() {
        LocalDate today = LocalDate.now();
        return today.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY)).atTime(23, 59);
    }

    /**
     * 获得本周开始时间
     *
     * @return
     */
    public static LocalDateTime getWeekStartTimeToNum(LocalDateTime now, int num) {
        LocalDate today = now.toLocalDate();
        LocalDateTime toWeekStart = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).atTime(0, 0);
        return toWeekStart.plusWeeks(num);
    }

    /**
     * 获得本周结束点时间
     *
     * @return
     */
    public static LocalDateTime getWeekEndTimeToNum(LocalDateTime now, int num) {
        LocalDate today = now.toLocalDate();
        LocalDateTime toWeekEnd = today.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY)).atTime(23, 59);
        return toWeekEnd.plusWeeks(num);
    }

    /**
     * 获得本月开始时间
     *
     * @return
     */
    public static LocalDateTime getMonthStartTime() {
        LocalDate today = LocalDate.now();
        //本月的第一天
        return LocalDate.of(today.getYear(), today.getMonth(), 1).atTime(0, 0);

    }

    /**
     * 获得本月结束时间
     *
     * @return
     */
    public static LocalDateTime getMonthEndTime() {
        LocalDate today = LocalDate.now();
        //本月的最后一天
        return today.with(TemporalAdjusters.lastDayOfMonth()).atTime(23, 59);
    }

    /**
     * 获得本年开始时间
     *
     * @return
     */
    public static LocalDateTime getYearStartTime() {
        return LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.firstDayOfYear())), LocalTime.MIN);
    }

    /**
     * 获得本年结束时间
     *
     * @return
     */
    public static LocalDateTime getYearEndTime() {
        return LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.lastDayOfYear())), LocalTime.MAX);
    }

    /**
     * @Description:本周的开始时间
     * @Param: [today, isFirst: true 表示开始时间，false表示结束时间]
     */
    public static LocalDateTime getStartOrEndDayOfWeek(LocalDate today, Boolean isFirst) {
        String time = MIN_TIME;
        LocalDate resDate = LocalDate.now();
        if (today == null) {
            today = resDate;
        }
        DayOfWeek week = today.getDayOfWeek();
        int value = week.getValue();
        if (Boolean.TRUE.equals(isFirst)) {
            resDate = today.minusDays(Long.parseLong(String.valueOf(value - 1)));
        } else {
            resDate = today.plusDays(Long.parseLong(String.valueOf(7 - value)));
            time = MAX_TIME;
        }
        return LocalDateTime.parse(resDate + time);
    }

    /**
     * HH:mm 转换 LocalDateTime
     *
     * @param date
     * @param ewx
     * @return
     */
    public static LocalDateTime getDayLocalDateTime(Date date, String ewx) {
        String[] as = ewx.split(StringConstant.COLON);
        List<String> list = Arrays.asList(as);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        //set hour min
        int hour = Integer.parseInt(list.get(NumberConstant.NUMBER_0));
        int min = Integer.parseInt(list.get(NumberConstant.NUMBER_1));
        calendar.set(year, month, day, hour, min, NumberConstant.NUMBER_0);
        Date setDate = calendar.getTime();
        return DateUtil.date2LocalDate(setDate);
    }

    /**
     * 返回默认格式 yyyy-MM-dd HH:mm:ss
     *
     * @param localDateTime
     * @return
     */
    public static String getDateString(LocalDateTime localDateTime) {
        return localDateTime.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME));
    }

    /**
     * 日期转星期
     *
     * @param datetime
     * @return
     */
    public static String dateToWeek(String datetime) {
        SimpleDateFormat f = new SimpleDateFormat(PATTERN_DATE);
        String[] weekDays = {"0", "1", "2", "3", "4", "5", "6"};
        // 获得一个日历
        Calendar cal = Calendar.getInstance();
        Date datet = null;
        try {
            datet = f.parse(datetime);
            cal.setTime(datet);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        // 指示一个星期中的某天。
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) {
            w = 0;
        }
        return weekDays[w];
    }

    public static String appendDayStr(List<Integer> days) {
        StringBuilder joiner = new StringBuilder();
        for (Integer day : days) {
            joiner.append(day).append("号、");
        }
        joiner.setLength(joiner.length() - 1);
        return joiner.toString();
    }

    /**
     * 转字符串
     *
     * @param week 1,2,3,4,5,6,7
     * @return
     */
    public static String toWeekStr(List<Integer> week) {
        String[] weekDays = {"", MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY};
        StringBuilder str = new StringBuilder();
        for (Integer w : week) {
            str.append(weekDays[w]).append("、");
        }
        str.setLength(str.length() - 1);
        return str.toString();
    }

    public static String dateToWeekStr(String datetime) {
        SimpleDateFormat f = new SimpleDateFormat(PATTERN_DATE);
        String[] weekDays = {SUNDAY, MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY};
        // 获得一个日历
        Calendar cal = Calendar.getInstance();
        Date date;
        try {
            date = f.parse(datetime);
            cal.setTime(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        // 指示一个星期中的某天。
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) {
            w = 0;
        }
        return weekDays[w];
    }

    /**
     * 志琴快乐时间段，处理方法
     *
     * @param timeLimitedJson 时间段JSON
     * @param timeLimitedType 时间类型
     */
    public static List<String> effectivePeriod(String timeLimitedJson, Integer timeLimitedType) {
        StringBuilder time = new StringBuilder();
        List<EffectiveTimeVO> effectiveTimeList = JSON.parseArray(timeLimitedJson, EffectiveTimeVO.class);
        if (!CollectionUtils.isEmpty(effectiveTimeList)) {
            List<String> equitiesTimeLists = new ArrayList<>();
            //生效时间段拼接
            for (EffectiveTimeVO effectiveTimeVO : effectiveTimeList) {
                //时间类型
                time.append(EffectiveTimeEnum.getNameByCode(timeLimitedType));
                List<String> type = effectiveTimeVO.getType();
                if (!CollectionUtils.isEmpty(type)) {
                    for (int i = 0; i < type.size(); i++) {
                        periodTypeHandler(timeLimitedType, time, type, i);
                    }
                }
                time.append("   ")
                        .append(effectiveTimeVO.getValue().get(NumberConstant.NUMBER_0)).append("-")
                        .append(effectiveTimeVO.getValue().get(NumberConstant.NUMBER_1));
                equitiesTimeLists.add(time.toString());
                //清除
                time.delete(NumberConstant.NUMBER_0, time.length());
            }
            return equitiesTimeLists;
        }
        return Lists.newArrayList();
    }

    private void periodTypeHandler(Integer timeLimitedType, StringBuilder time, List<String> type, int i) {
        if (DataUnitEnum.WEEK.getCode() == timeLimitedType) {
            time.append(NumberUtil.timeHandler(type.get(i)));
        } else if (DataUnitEnum.MONTH.getCode() == timeLimitedType) {
            time.append(type.get(i))
                    .append(GradeMessageInfoEnum.PREVIEW_STRING_NUMBER.getDes());
        } else {
            time.append(type.get(i));
        }
        if (i < type.size() - 1) {
            time.append("、");
        }
    }

    /**
     * 过期时间计算
     */
    public static LocalDateTime loseDateTimeProcessor(Integer validityType,
                                                      String fixedValueValidityDate,
                                                      Integer dynamicValidityType,
                                                      Integer dynamicValidityNumber,
                                                      LocalDateTime dateTime) {
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        LocalDateTime localDateTime = null;
        int year = cal.get(Calendar.YEAR);
        if (validityType == 2) {
            //当前月日
            String current = getTmpDate(date, "M-d");
            String[] strCurrent = current.split("-");
            List<String> currentList = new ArrayList<>(Arrays.asList(strCurrent));
            int monthCurrent = Integer.parseInt(currentList.get(0));
            int dayCurrent = Integer.parseInt(currentList.get(1));
            String[] str = fixedValueValidityDate.split("-");
            List<String> resultList = new ArrayList<>(Arrays.asList(str));
            int monthInt = Integer.parseInt(resultList.get(0));
            int dayInt = Integer.parseInt(resultList.get(1));
            if (monthCurrent > monthInt) {
                //明年
                localDateTime = LocalDateTime.of(year + 1, monthInt, dayInt, NumberConstant.NUMBER_23, NumberConstant.NUMBER_59, NumberConstant.NUMBER_59);
            } else if (monthCurrent < monthInt) {
                //今年
                localDateTime = LocalDateTime.of(year, monthInt, dayInt, NumberConstant.NUMBER_23, NumberConstant.NUMBER_59, NumberConstant.NUMBER_59);
            } else {
                //同月则判断天
                if (dayCurrent > dayInt) {
                    //明年(昨天以前)
                    localDateTime = LocalDateTime.of(year + 1, monthInt, dayInt, NumberConstant.NUMBER_23, NumberConstant.NUMBER_59, NumberConstant.NUMBER_59);
                } else {
                    //今年(今天以后)
                    localDateTime = LocalDateTime.of(year, monthInt, dayInt, NumberConstant.NUMBER_23, NumberConstant.NUMBER_59, NumberConstant.NUMBER_59);
                }
            }
        } else {
            if (dynamicValidityType == DataUnitEnum.DAY.getCode()) {
                localDateTime = dateTime.plusDays(dynamicValidityNumber);
            } else if (dynamicValidityType == DataUnitEnum.WEEK.getCode()) {
                localDateTime = dateTime.plusWeeks(dynamicValidityNumber);
            } else if (dynamicValidityType == DataUnitEnum.MONTH.getCode()) {
                localDateTime = dateTime.plusMonths(dynamicValidityNumber);
            } else {
                localDateTime = dateTime.plusYears(dynamicValidityNumber);
            }
        }
        return localDateTime;
    }

    /**
     * 指定日期加上指定天数零点
     *
     * @param date   指定日期
     * @param number 指定天数
     * @return 操作结果
     */
    public static LocalDateTime addSpecifiedDaysZore(LocalDateTime date, int number) {
        LocalDateTime localDateTime = plusDaysTime(date, number);
        localDateTime = LocalDateTime.of(localDateTime.getYear(), localDateTime.getMonth(),
                localDateTime.getDayOfMonth(), 0, 0, 0);
        return localDateTime;
    }

    public static LocalDateTime addStartDayNum(LocalDateTime dateTime, int number) {
        LocalDateTime localDateTime = plusDaysTime(dateTime, number);
        localDateTime = LocalDateTime.of(localDateTime.getYear(), localDateTime.getMonth(),
                localDateTime.getDayOfMonth(), 0, 0, 0);
        return localDateTime;
    }

    public static LocalDateTime addEndDayNum(LocalDateTime dateTime, int number) {
        LocalDateTime localDateTime = plusDaysTime(dateTime, number);
        localDateTime = LocalDateTime.of(localDateTime.getYear(), localDateTime.getMonth(),
                localDateTime.getDayOfMonth(), 23, 59, 59);
        return localDateTime;
    }


    public static LocalDateTime formatDayPeriod(LocalDate beginDate, String totalPeriod) {
        LocalTime periodTime = LocalTime.parse(totalPeriod, DateTimeFormatter.ofPattern(HOUR_MINUTE));
        LocalTime nowTime = LocalTime.now();
        long minutes = Duration.between(nowTime, periodTime).toMinutes();
        //当前时间还没到累计时间，取昨天为开始时间
        if (minutes > NumberConstant.NUMBER_0) {
            return LocalDateTime.of(beginDate.minusDays(NumberConstant.NUMBER_1), periodTime);
        } else {
            return LocalDateTime.of(beginDate, periodTime);
        }
    }

    public static LocalDateTime formatWeekPeriod(LocalDate beginDate, String totalPeriod) {
        int nowWeek = beginDate.getDayOfWeek().getValue();
        //判断时间
        if (nowWeek >= Integer.parseInt(totalPeriod)) {
            return LocalDateTime.of(beginDate.minusDays(Long.parseLong(nowWeek - Integer.parseInt(totalPeriod) + StringConstant.EMPTY)), LocalTime.now());
        } else {
            int periodDay = nowWeek + NumberConstant.NUMBER_7 - Integer.parseInt(totalPeriod);
            return LocalDateTime.of(beginDate.minusDays(periodDay), LocalTime.now());
        }
    }

    public static LocalDateTime formatMonthPeriod(LocalDate beginDate, String totalPeriod) {
        int nowDay = beginDate.getDayOfMonth();
        //判断时间
        if (nowDay >= Integer.parseInt(totalPeriod)) {
            return LocalDateTime.of(beginDate.minusDays(Long.parseLong(nowDay - Integer.parseInt(totalPeriod) + StringConstant.EMPTY)), LocalTime.now());
        } else {
            LocalDateTime periodDate = LocalDateTime.of(beginDate.getYear(), beginDate.getMonthValue(),
                    Integer.parseInt(totalPeriod), NumberConstant.NUMBER_0, NumberConstant.NUMBER_0);
            return periodDate.minusMonths(NumberConstant.NUMBER_1);
        }
    }

    public static LocalDateTime formatYearPeriod(LocalDate beginDate, String totalPeriod) {
        LocalDate periodTime = LocalDate.parse(beginDate.getYear() + StringConstant.STR_BIAS_TWO + totalPeriod
                , DateTimeFormatter.ofPattern("yyyy-M-d"));
        long days = ChronoUnit.DAYS.between(periodTime, beginDate);
        //判断时间
        if (days >= NumberConstant.NUMBER_0) {
            return LocalDateTime.of(periodTime, LocalTime.now());
        } else {
            return LocalDateTime.of(periodTime.minusYears(NumberConstant.NUMBER_1), LocalTime.now());
        }
    }

    public static String getDateNum() {
        Date t = new Date();
        SimpleDateFormat df = new SimpleDateFormat(PATTERN_DATETIME);
        return df.format(t).replace(" ", "").replace(":", "").replace("-", "");
    }

    public static LocalDateTime getFirstExpireTime(Integer num, Integer unit) {
        LocalDateTime expireTime = LocalDateTime.now();
        if (Objects.equals(DateUnitEnum.MONTH.getCode(), unit)) {
            expireTime = expireTime.plusMonths(num);
        }
        if (Objects.equals(DateUnitEnum.YEAR.getCode(), unit)) {
            expireTime = expireTime.plusYears(num);
        }
        return expireTime;
    }

    /**
     * 获取当天开始、结束时间
     */
    public static LocalDateTime[] getDayTime(LocalDate date) {
        LocalDateTime firstDay = date.atStartOfDay();
        LocalDateTime lastDay = date.atTime(23, 59, 59);
        return new LocalDateTime[]{firstDay, lastDay};
    }

    /**
     * 获取周一到周日的日期
     */
    public static LocalDateTime[] getWeekDays(LocalDate date) {
        LocalDateTime firstDay = date.with(DayOfWeek.MONDAY).atStartOfDay();
        LocalDateTime lastDay = date.with(DayOfWeek.SUNDAY).atTime(23, 59, 59);
        return new LocalDateTime[]{firstDay, lastDay};
    }

    /**
     * 获取月初到月底的日期
     */
    public static LocalDateTime[] getMonthDays(LocalDate date) {
        LocalDateTime firstDay = date.withDayOfMonth(1).atStartOfDay();
        LocalDateTime lastDay = date.withDayOfMonth(date.lengthOfMonth()).atTime(23, 59, 59);
        return new LocalDateTime[]{firstDay, lastDay};
    }


    public static String toString(LocalDateTime dateTime) {
        return dateTime.format(DateTimeFormatter.ofPattern(PATTERN_DATETIME));
    }

    public List<Integer> getPeriodType() {
        List<Integer> periodTypeList = new ArrayList<>();
        //每天
        periodTypeList.add(DataUnitEnum.DAY.getCode());
        Date date = new Date();
        //判断今天是否是周一
        if (DateUtil.isToday(DateUtil.getWeekByDate(date))) {
            periodTypeList.add(DataUnitEnum.WEEK.getCode());
        }
        //判断今天是否是本月第一天
        if (DateUtil.isToday(DateUtil.findMonthStart(date))) {
            periodTypeList.add(DataUnitEnum.MONTH.getCode());
        }
        //判断今天是否是年第一天
        if (DateUtil.isToday(DateUtil.getCurrYearFirst(date))) {
            periodTypeList.add(DataUnitEnum.YEAR.getCode());
        }
        return periodTypeList;
    }

    public LocalDateTime buildBeginTime(Integer totalPeriodType,
                                        LocalDateTime gmtCreate,
                                        String totalPeriod) {
        LocalDateTime beginTime = LocalDateTime.now();
        LocalDate beginDate = beginTime.toLocalDate();
        switch (Objects.requireNonNull(DataUnitEnum.Enum(totalPeriodType))) {
            case FOREVER:
                beginTime = gmtCreate;
                break;
            case DAY:
                beginTime = DateUtil.formatDayPeriod(beginDate, totalPeriod);
                break;
            case WEEK:
                beginTime = DateUtil.formatWeekPeriod(beginDate, totalPeriod);
                beginTime = LocalDateTime.of(beginTime.getYear(), beginTime.getMonth(), beginTime.getDayOfMonth(), 0, 0, 0);
                break;
            case MONTH:
                beginTime = DateUtil.formatMonthPeriod(beginDate, totalPeriod);
                beginTime = LocalDateTime.of(beginTime.getYear(), beginTime.getMonth(), beginTime.getDayOfMonth(), 0, 0, 0);
                break;
            case YEAR:
                beginTime = DateUtil.formatYearPeriod(beginDate, totalPeriod);
                beginTime = LocalDateTime.of(beginTime.getYear(), beginTime.getMonth(), beginTime.getDayOfMonth(), 0, 0, 0);
                break;
            default:
                return gmtCreate;
        }
        //成长值创建时间大于计算的开始时间
        if (gmtCreate.isAfter(beginTime)) {
            beginTime = gmtCreate;
        }
        if (totalPeriodType == DataUnitEnum.DAY.getCode() &&
                beginTime.toLocalDate().equals(gmtCreate.toLocalDate()) && LocalDateTime.now().isBefore(beginTime)) {
            beginTime = gmtCreate;
        }
        return beginTime;
    }


    /**
     * 根据订单修改时间获取周期开始时间
     * @param totalPeriodType
     * @param totalPeriod
     * @return
     */
    public List<LocalDateTime> buildBeginPeriodTime(Integer totalPeriodType,
                                                    LocalDateTime orderGmtCreate,
                                                    LocalDateTime taskGmtCreate,
                                                    String totalPeriod) {
        List<LocalDateTime> localDateTimeList = new ArrayList<>(2);
        LocalDateTime beginTime;
        LocalDateTime endTime = null;
        switch (Objects.requireNonNull(DataUnitEnum.Enum(totalPeriodType))) {
            case FOREVER:
                beginTime = taskGmtCreate;
                break;
            case DAY:
                //若订单时间等于任务时间，说明是当天，需要获取任务开始时间
                LocalTime periodTime = LocalTime.parse(totalPeriod, DateTimeFormatter.ofPattern(HOUR_MINUTE));
                LocalTime nowTime = orderGmtCreate.toLocalTime();
                long minutes = Duration.between(nowTime, periodTime).toMinutes();

                List<LocalDateTime> localDateTimes = getDayPeriodTime(periodTime, orderGmtCreate, taskGmtCreate, minutes);
                beginTime = localDateTimes.get(0);
                endTime = localDateTimes.get(1);
                break;
            case WEEK:
                if (isSameWeek(orderGmtCreate.toLocalDate(), taskGmtCreate.toLocalDate())) {
                    beginTime = taskGmtCreate;
                } else {
                    beginTime = DateUtil.formatWeekStartPeriod(orderGmtCreate.toLocalDate(), totalPeriod);
                }
                endTime = beginTime.plusWeeks(1).minusDays(1).withHour(23).withMinute(59).withSecond(59);
                break;
            case MONTH:
                if (isSameMonth(orderGmtCreate.toLocalDate(), taskGmtCreate.toLocalDate())) {
                    beginTime = taskGmtCreate;
                } else {
                    beginTime = DateUtil.formatMonthStartPeriod(orderGmtCreate.toLocalDate(), totalPeriod);
                }
                endTime = beginTime.plusMonths(1).minusDays(1).withHour(23).withMinute(59).withSecond(59);

                break;
            case YEAR:
                if (isSameYear(orderGmtCreate.toLocalDate(), taskGmtCreate.toLocalDate())) {
                    beginTime = taskGmtCreate;
                } else {
                    beginTime = DateUtil.formatYearStartPeriod(orderGmtCreate.toLocalDate(), totalPeriod);
                }
                endTime = beginTime.plusYears(1).minusDays(1).withHour(23).withMinute(59).withSecond(59);

                break;
            default:
                return localDateTimeList;
        }
        localDateTimeList.add(beginTime);
        localDateTimeList.add(endTime);
        return localDateTimeList;
    }

    private List<LocalDateTime> getDayPeriodTime(LocalTime periodTime,
                                                 LocalDateTime orderGmtCreate,
                                                 LocalDateTime taskGmtCreate,
                                                 long minutes) {
        LocalDateTime beginTime;
        LocalDateTime endTime;
        List<LocalDateTime> localDateTimeList = new ArrayList<>(2);
        //订单时间还没到累计时间
        if (minutes > NumberConstant.NUMBER_0) {
            if (orderGmtCreate.toLocalDate().equals(taskGmtCreate.toLocalDate())) {
                beginTime = taskGmtCreate;
                endTime = LocalDateTime.of(orderGmtCreate.toLocalDate(), periodTime);
            } else {
                beginTime = LocalDateTime.of(orderGmtCreate.toLocalDate().minusDays(NumberConstant.NUMBER_1), periodTime);
                endTime = LocalDateTime.of(orderGmtCreate.toLocalDate().minusDays(NumberConstant.NUMBER_1), periodTime).plusDays(1);
            }

        } else {
            beginTime = LocalDateTime.of(orderGmtCreate.toLocalDate(), periodTime);
            endTime = LocalDateTime.of(orderGmtCreate.toLocalDate(), periodTime).plusDays(1);
            if (beginTime.isAfter(endTime)) {
                endTime = endTime.plusDays(1);
            }

        }
        localDateTimeList.add(beginTime);
        localDateTimeList.add(endTime);
        return localDateTimeList;
    }

    public static boolean isSameWeek(LocalDate dateTime1, LocalDate dateTime2) {
        LocalDate monday1 = dateTime1.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate monday2 = dateTime2.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));

        // 比较调整后周一所在的周是否相同
        return monday1.get(ChronoField.ALIGNED_WEEK_OF_YEAR) == monday2.get(ChronoField.ALIGNED_WEEK_OF_YEAR);
    }

    public static boolean isSameMonth(LocalDate dateTime1, LocalDate dateTime2) {
        return dateTime1.getMonth() == dateTime2.getMonth() && dateTime1.getYear() == dateTime2.getYear();
    }

    public static boolean isSameYear(LocalDate dateTime1, LocalDate dateTime2) {
        return dateTime1.getYear() == dateTime2.getYear();
    }

    public static LocalDateTime formatWeekStartPeriod(LocalDate beginDate, String totalPeriod) {
        int nowWeek = beginDate.getDayOfWeek().getValue();
        //判断时间
        if (nowWeek >= Integer.parseInt(totalPeriod)) {
            // 将 LocalDate 转换为 LocalDateTime，时间部分设置为当天的午夜
            return LocalDateTime.of(beginDate.minusDays(Long.parseLong(nowWeek - Integer.parseInt(totalPeriod)+ StringConstant.EMPTY)), LocalTime.MIDNIGHT);
        } else {
            int periodDay = nowWeek + NumberConstant.NUMBER_7 - Integer.parseInt(totalPeriod);
            return LocalDateTime.of(beginDate.minusDays(periodDay), LocalTime.now());
        }
    }


    public static LocalDateTime formatMonthStartPeriod(LocalDate beginDate, String totalPeriod) {
        int nowDay = beginDate.getDayOfMonth();
        //判断时间
        if (nowDay >= Integer.parseInt(totalPeriod)) {
            return LocalDateTime.of(beginDate.minusDays(Long.parseLong(nowDay - Integer.parseInt(totalPeriod)+ StringConstant.EMPTY)), LocalTime.MIDNIGHT);
        } else {
            LocalDateTime periodDate = LocalDateTime.of(beginDate.getYear(), beginDate.getMonthValue(),
                    Integer.parseInt(totalPeriod), NumberConstant.NUMBER_0, NumberConstant.NUMBER_0);
            return periodDate.minusMonths(NumberConstant.NUMBER_1);
        }
    }

    public static LocalDateTime formatYearStartPeriod(LocalDate beginDate, String totalPeriod) {
        LocalDate periodTime = LocalDate.parse(beginDate.getYear() + StringConstant.STR_BIAS_TWO + totalPeriod
                , DateTimeFormatter.ofPattern("yyyy-M-d"));
        long days = ChronoUnit.DAYS.between(periodTime, beginDate);
        //判断时间
        if (days >= NumberConstant.NUMBER_0) {
            return LocalDateTime.of(periodTime, LocalTime.MIDNIGHT);
        } else {
            return LocalDateTime.of(periodTime.minusYears(NumberConstant.NUMBER_1), LocalTime.MIDNIGHT);
        }
    }
}