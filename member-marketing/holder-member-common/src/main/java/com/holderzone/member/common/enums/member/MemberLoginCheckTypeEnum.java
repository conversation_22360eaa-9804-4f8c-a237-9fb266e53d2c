package com.holderzone.member.common.enums.member;

import com.google.common.collect.Lists;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * 会员支付校验方式
 */
public enum MemberLoginCheckTypeEnum {

    PAY_PASSWORD(1, "手输支付密码"),

    SCAN_MEMBER_CODE(2, "扫描会员码"),

    ;

    private final int code;

    private final String des;

    MemberLoginCheckTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public static List<Integer> convertList(String memberLoginCheckType) {
        List<Integer> convertList = Lists.newArrayList();
        if (StringUtils.hasText(memberLoginCheckType)) {
            List<String> memberLoginCheckTypeList = Lists.newArrayList(memberLoginCheckType.split(","));
            memberLoginCheckTypeList.forEach(type -> {
                for (MemberLoginCheckTypeEnum typeEnum : MemberLoginCheckTypeEnum.values()) {
                    if (Objects.equals(typeEnum.getCode(), Integer.parseInt(type))) {
                        convertList.add(typeEnum.getCode());
                    }
                }
            });
        }
        return convertList;
    }

    public int getCode() {
        return this.code;
    }

    public String getDes() {
        return this.des;
    }

    public static List<Integer> getALL() {
        List<Integer> all = Lists.newArrayList();
        for (MemberLoginCheckTypeEnum typeEnum : MemberLoginCheckTypeEnum.values()) {
            all.add(typeEnum.getCode());
        }
        return all;
    }

}
