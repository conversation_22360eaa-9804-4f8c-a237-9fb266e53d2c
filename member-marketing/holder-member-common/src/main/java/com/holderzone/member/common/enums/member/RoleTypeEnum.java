package com.holderzone.member.common.enums.member;


import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/22 下午2:48
 * @description 角色类型
 * 所有的会员等级图标顺序都是：会员 、商家、管家、达人
 */
@Getter
@AllArgsConstructor
public enum RoleTypeEnum {

    /**
     * 普通会员
     */
    MEMBER(0, "普通会员", "会员等级"),

    /**
     * 商家
     */
    MERCHANT(1, "商家", "商家等级"),
    /**
     * 管家
     */
    BUTLER(2,"管家","管家等级"),
    /**
     * 达人
     */
    TALENT(3,"达人","达人等级");

    private final Integer code;

    private final String des;

    private final String grade;

    public static String convertName(String str) {
        if (StringUtils.isEmpty(str)) {
            return "";
        }
        return getRoleName(getRoleTypeToVO(str));
    }

    public static String getName(String name) {
        if (name == null) {
            return "";
        }
        RoleTypeEnum[] values = RoleTypeEnum.values();
        for (RoleTypeEnum roleTypeEnum : values) {
            if (Objects.equals(roleTypeEnum.name(), name)) {
                return roleTypeEnum.getDes();
            }
        }
        return "";
    }

    public static String getName(Integer code) {
        if (code == null) {
            return "";
        }
        return code == 0 ? RoleTypeEnum.MEMBER + "" : RoleTypeEnum.MERCHANT + "";
    }

    public static String getGrade(String name) {
        if (name == null) {
            return "";
        }
        RoleTypeEnum[] values = RoleTypeEnum.values();
        for (RoleTypeEnum roleTypeEnum : values) {
            if (Objects.equals(roleTypeEnum.name(), name)) {
                return roleTypeEnum.getGrade();
            }
        }
        return "";
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return "";
        }
        RoleTypeEnum[] values = RoleTypeEnum.values();
        for (RoleTypeEnum roleTypeEnum : values) {
            if (Objects.equals(roleTypeEnum.getCode(), code)) {
                return roleTypeEnum.getDes();
            }
        }
        return "";
    }

    public static String getRoleTypeToDO(List<String> roleTypeList) {
        if (CollectionUtils.isEmpty(roleTypeList)) {
            return "";
        }
        StringBuilder roleTypeStr = new StringBuilder();
        int size = roleTypeList.size() - 1;
        for (int i = 0; i <= size; i++) {
            String roleType = roleTypeList.get(i);
            if (i != size) {
                roleTypeStr.append(roleType).append("、");
            } else {
                roleTypeStr.append(roleType);
            }
        }
        return roleTypeStr.toString();
    }

    public static Integer name2Code(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        RoleTypeEnum[] values = RoleTypeEnum.values();
        for (RoleTypeEnum roleTypeEnum : values) {
            if (roleTypeEnum.name().equals(name)) {
                return roleTypeEnum.getCode();
            }
        }
        return null;
    }

    public static List<String> getRoleTypeToVO(String roleTypeStr) {
        if (StringUtils.isEmpty(roleTypeStr)) {
            return Collections.emptyList();
        }
        return Arrays.stream(roleTypeStr.split("、")).collect(Collectors.toList());
    }

    public static String getRoleName(List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        int size = list.size() - 1;
        for (int i = 0; i <= size; i++) {
            String string = list.get(i);
            if (i != size) {
                sb.append(getName(string)).append("、");
            } else {
                sb.append(getName(string));
            }
        }
        return sb.toString();
    }
}
