package com.holderzone.member.common.dto.event;

import com.holderzone.member.common.dto.mall.OrderReceiverAddressDTO;
import com.holderzone.member.common.dto.order.MallBaseOrderDTO;
import com.holderzone.member.common.dto.order.MallBaseOrderDetailDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * 推动订单到crm
 */
@Data
@Accessors(chain = true)
public class PushCrmOrderEvent extends BaseEvent {

    private MallBaseOrderDTO order;

    private List<MallBaseOrderDetailDTO> details;

    private OrderReceiverAddressDTO orderReceiverAd;

    /**
     * 是否推送库存
     */
    private Boolean pushStock;
}
