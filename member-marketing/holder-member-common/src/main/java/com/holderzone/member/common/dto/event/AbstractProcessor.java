package com.holderzone.member.common.dto.event;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.member.common.constant.ThreadLocalCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;

/**
 * 通道绑定后事件处理模板
 *
 * <AUTHOR>
 * @date 2020/07/07 11:24
 * @date 2021/8/11
 */
@Slf4j
public abstract class AbstractProcessor<T extends BaseEvent> {

    public T event;

    public AbstractProcessor(T event) {
        this.event = event;
    }

    /**
     * 参数验证
     */
    protected void validate() {

    }

    protected String getLogTitle() {
        return Strings.EMPTY;
    }

    /**
     * 业务处理
     */
    protected abstract void process() throws InterruptedException;

    public void execute() {
        long time = System.currentTimeMillis();
        log.info("[{}] [{}]开始执行,参数={}", event.getSessionId(), getLogTitle(), JSONObject.toJSONString(event));
        try {
            ThreadLocalCache.put(JSONObject.toJSONString(event.getUserInfo()));
            process();
        } catch (Exception e) {
            log.error("[" + event.getSessionId() + "] [" + getLogTitle() + "] 执行错误", e);
            throw new BusinessException("[" + event.getSessionId() + "] [" + getLogTitle() + "] 执行错误");
        }
        log.info("[{}] [{}] 执行完成,耗时={}ms", event.getSessionId(), getLogTitle(), System.currentTimeMillis() - time);
    }
}
