package com.holderzone.member.common.enums.member;


/**
 * <AUTHOR>
 */

public enum AppletStoreTypeEnum {

    /**
     * 成长值商品获取门店
     */
    GROWTH_COMMODITY_STORE_TYPE(1,"成长值商品获取门店"),

    /**
     * 成长值任务指定门店
     */
    GROWTH_STORE_TYPE(2,"成长值任务指定门店"),

    /**
     * 会员卡适用门店
     */
    MEMBER_CARD_STORE_TYPE(3,"会员卡适用门店"),

    /**
     * 未知类型
     */
    UNDEFINED(-1, "未知类型"),

    ;

    /**
     * code值
     */
    private final int code;

    /**
     * 描述
     */
    private final String des;

    AppletStoreTypeEnum(int code,String des){
        this.code = code;
        this.des = des;
    }

    public int getCode(){
        return code;
    }

    public String getDes(){
        return des;
    }

    public static String getDesByCode(int code){
        for (AppletStoreTypeEnum appletStoreType : AppletStoreTypeEnum.values()){
            if (appletStoreType.getCode() == code){
                return appletStoreType.des;
            }
        }
        return "";
    }

    public static AppletStoreTypeEnum getEnumByCode(int code) {
        for (AppletStoreTypeEnum appletStoreType : AppletStoreTypeEnum.values()) {
            if (appletStoreType.getCode() == code) {
                return appletStoreType;
            }
        }
        return AppletStoreTypeEnum.UNDEFINED;
    }
}
