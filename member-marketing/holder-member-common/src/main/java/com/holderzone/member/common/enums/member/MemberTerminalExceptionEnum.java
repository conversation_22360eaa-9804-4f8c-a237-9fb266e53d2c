package com.holderzone.member.common.enums.member;

import com.holderzone.member.common.enums.ResponseBase;

/**
 * @version 1.0
 * <AUTHOR>
 * @className MemberTerminalExceptionEnum
 * @date 2019/05/16 15:21
 * @description 会员异常枚举类
 * @program holder-saas-member-terminal
 */
public enum MemberTerminalExceptionEnum implements ResponseBase {
    /**
     * 异常提示信息
     */
//    业务异常枚举定义在业务中抛出异常时使用(100001, "业务异常枚举定义，在业务中抛出异常时使用"),

    ERROR_METHOD_ARGUMENT_NOT_VALID(100002, "ERROR_METHOD_ARGUMENT_NOT_VALID"),
    ERROR_ILLEGAL_ARGUMENT(100003, "ILLEGAL_ARGUMENT"),
    ERROR_NOT_FOUND(100004, "NOT_FOUND"),
    ERROR_HYSTRIX_BAD_REQUEST(100005, "HYSTRIX_BAD_REQUEST"),
    ERROR_HYSTRIX_RUNTIME(100006, "HYSTRIX_RUNTIME"),
    ERROR_FEIGN(100007, "FEIGN"),
    ERROR_EXCEPTION(100008, "EXCEPTION"),
    ERROR_VERIFY(100009, "参数错误"),
    GLOBAL_E_UNREGISTERED(100010, "未注册"),
    GLOBAL_BAD_STATUS(100011, "会员账号已被禁用，请联系管理员"),
    ERROR_HEADER_VALID(100012, "头部信息解析错误！"),
    ERROR_HEADER_OPERATION_GUID(100013, "运营主体不能为空！"),
    ERROR_HEADER_OPERATION_STATUS(100014, "会员体系已被禁用,如有疑问请联系客服人员！"),
    ERROR_CANCEL_ORDER(100015, "该会员的运营主体发生变更，无法反结账！"),

    /**
     * member model
     */
    ERROR_EXIST_MAIN_OPERSUBJECT(110001, "手机号已存在"),
    ERROR_MEMBER_HAS_MAIN_CARD(110002, "会员已开通主卡"),
    ERROR_EQUITY_CARD_SPECIFICATIONS(110018, "未配置权益卡规格"),
    ERROR_CHANNEL_OPEN_EQUITY_CARD(110019, "卡规则配置不允许此通道开通权益卡"),
    ERROR_NUMBER_CARDS_ISSUED(110020, "已达到发卡数量限制"),
    ERROR_MEMBER_CARD_NOT_EXIST(110005, "会员卡不存在"),
    ERROR_MEMBER_CARD_NOT_INTEGRAL(110006, "可用积分为不足"),
    ERROR_MEMBER_NOT_CANCAL_CONSUMPTION(110007, "撤销的订单不存在"),
    ERROR_MOBILE_FORMAT(110008, "手机号码格式错误"),
    ERROR_MEMBER_NOT_MONEY(110009, "会员卡余额不足"),
    ERROR_MEMBER_CARD_NOT_EQUITY(110010, "此卡不是权益卡"),
    ERROR_MEMBER_PAY_PASSWORD_EMPTY(110011, "支付密码不能为空"),
    ERROR_MEMBER_PAY_PASSWORD_WRONG(110012, "支付密码不正确，请重新输入"),
    ERROR_MEMBER_NOT_EXIT(110013, "会员不存在"),
    ERROR_MEMBER_CARD_EXE(110014, "会员卡状态异常"),
    ERROR_CARD_HAS_FROZEN(110016, "会员卡已被冻结"),
    ERROR_MEMBER_CARD_HASBAD(110015, "该会员卡已禁用"),


    ERROR_MEMBER_ORDER_NOT_EXIT(110017, "订单不存在"),

    ERROR_MEMBER_PAY_MONEY_LIMIT(110018, "超过支付上限"),


    /**
     * label model
     */
    ERROR_NOT_LABEL(120001, "标签不存在"),


    /**
     * card model
     */
    ERROR_NOT_MAIN_CARD(130001, "请配置主卡信息"),
    ERROR_NOT_MAIN_CARD_LEVEL(130002, "请配置主卡等级信息"),
    ERROR_NOT_MAIN_CARD_ALLIANCE(130003, "该运营主体或联盟下没有此卡配置"),
    ERROR_MUST_OPEN_MAIN_CARD(130004, "只能开通会员卡"),
    ERROR_MORE_CARD_LEVEL(130005, "默认会员等级存在多个，请检查卡等级配置"),
    ERROR_NOT_RECHARGE_RULE(130006, "未配置充值规则"),
    ERROR_NO_RECHARGE(130007, "未开启充值功能"),
    ERROR_NO_CARD(130008, "未查询到卡信息"),
    ERROR_NO_CARD_FOR_EQUITY(130009, "未查询到权益卡信息"),
    ERROR_NO_RECHARGE_FOR_EQUITY(130010, "权益卡未开启充值功能"),
    ERROR_NO_RECHARGE_MONEY_FOR_EQUITY(130011, "权益卡充值金额错误"),
    ERROR_NOT_CARD(130012, "会员卡不存在,请先设置会员卡"),
    ERROR_NOT_DEFAULT_CARD_LEVEL(130013, "默认会员等级不存在"),
    ERROR_DONOT_REOPEN_CARD(130014, "卡已存在,请勿重复开卡"),
    ERROR_NO_OPEN_CARD(130014, "当前卡已停止发放"),
    ERROR_CARD_MONEY_LACK(130015, "会员卡余额不足"),
    ERROR_CARD_RECHARGE(130016, "会员卡充值失败"),
    OVER_LIMIT_RECHARGE(300005, "已超过充值上限"),
    CANTEEN_CARD_NOT_EXIST(300016, "当前用户不存在食堂卡"),
    INSUFFICIENT_BALANCE(300017, "余额不足，请充值"),

    ERROR_CARD_NOT_STORE(300018, "会员卡无法在当前门店使用"),

    ERROR_CARD_NOT_PAST(300019, "会员卡已过期无法使用"),

    ERROR_CARD_PAST_DISABLED(300020, "会员卡已禁用无法使用"),

    ERROR_CARD_FREEZE(300021, "会员卡已冻结无法使用"),

    ERROR_CARD_NOT_ACTIVATE(300022, "会员卡未激活,请先激活使用"),

    UN_SUPPORT_RECHARGE(300023, "此会员卡不支持充值"),

    ERROR_CREDIT_GENERAL_STATUS_DISABLED(300024, "挂账账户已禁用，请联系管理员"),

    ERROR_CREDIT_USER_STATUS_DISABLED(300025, "挂账账户已过期，不可使用"),

    ERROR_CREDIT_STORE(300026, "挂账账户无法在当前门店使用"),

    ERROR_CREDIT_USER_PHONE(300027, "挂账手机号与会员支付手机号不一致"),

    ERROR_CREDIT_INSUFFICIENT_AMOUNT_REMAINING(300028, "剩余挂账金额不足\n" + "\n"),

    ERROR_CREDIT_SINGLE_OVER(300029, "单笔超过挂账上限\n" + "\n"),

    ERROR_CANTEEN_CARD_RECHARGE(130016, "食堂卡充值失败"),


    /**
     * volume model
     */
    ERROR_VOLUME_CANT_USE_IN_BANQUET(140001, "宴会套餐暂不能使用优惠券"),
    ERROR_VOLUME_IN_USE(140002, "优惠券正在使用中"),
    ERROR_VOLUME_ONLY_USE_ONE_TYPE(140003, "只能使用代金券或商品券的一种"),
    ERROR_VOLUME_EXCEED_NUM_LIMIT(140004, "超过该优惠券的使用张数上限"),
    ERROR_VOLUME_CANT_USE_TOGETHER(140005, "选择的优惠券不能同时使用"),
    ERROR_VOLUME_NOT_ACHIEVE_THRESHOLD(140006, "未达到优惠券使用门槛"),
    ERROR_VOLUME_NOT_SUPPORT_STORE(140007, "该券码不支持本门店使用"),
    ERROR_VOLUME_NOT_SAME_MEMBER(140008, "优惠券只能本人使用"),
    ERROR_VOLUME_MEMBER_DISABLE(140009, "优惠券所属会员被禁用"),
    ERROR_VOLUME_NOT_VALID(140010, "优惠券未生效"),
    ERROR_VOLUME_USED(140011, "优惠劵已被使用"),
    ERROR_VOLUME_VOID(140012, "优惠劵已作废"),
    ERROR_VOLUME_OVERDUE(140013, "优惠劵已过期"),
    ERROR_VOLUME_CANT_MEMBER_PRICE(140014, "该优惠券不能与会员价同享"),
    ERROR_VOLUME_EXCEED_PRODUCT_NUM(140015, "选择的券超过可抵扣商品数量"),
    ERROR_VOLUME_NOT_SUPPORT_PRODUCT(140016, "没有可使用优惠券的菜品"),
    ERROR_VOLUME_NOT_SUPPORT_GIFT(140017, "赠品菜不在优惠劵使用范围内"),
    ERROR_VOLUME_CODE_NOT_EXSITS(140018, "券码不存在"),
    ERROR_VOLUME_NOT_BELONG_ONE_PERSON(140019, "券码必须使用同一个会员"),

    /**
     * order model
     */
    ERROR_MEMBER_PASSWORD_EMPTY(150001, "支付密码不能为空"),
    ERROR_MEMBER_PASSWORD_INCORRECT(150002, "支付密码不正确"),
    ERROR_INTEGRAL_DEDUCTION_DISABLE(150003, "未启用积分抵扣"),

    /**
     * activity model
     */
    ERROR_NOT_CONDUCT_ACTIVITIES(160001, "没有进行中的活动"),
    ERROR_NOT_STORE_CONDUCT_ACTIVITIES(160002, "当前门店没有进行中的活动"),

    /**
     * Face
     */
    ERROR_NOT_FACE(170000, "支付失败，面部数据不存在，可通过小程序认证"),
    ERROR_NOT_MEMBER_FORBIDDEN(170001, "账户已禁用无法使用"),

    ERROR_END(999999, "END");

    public int code;
    public String des;

    MemberTerminalExceptionEnum(final int code, final String des) {
        this.code = code;
        this.des = des;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDes() {
        return des;
    }
}
