package com.holderzone.member.common.dto.event;


import com.holderzone.member.common.dto.commodity.callback.StrategyCallbackDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * crm 策略单回调
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class StrategyCallbackEvent extends BaseEvent {

    private List<StrategyCallbackDTO> strategies;

    private List<Long> strategyIds;

}
