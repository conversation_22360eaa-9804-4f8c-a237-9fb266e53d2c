package com.holderzone.member.common.enums.member;

import org.apache.commons.lang3.StringUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分销类型枚举
 * <AUTHOR>
 * @version 2025/06/04
 */
@Getter
@AllArgsConstructor
public enum DistributeTypeEnum {

    /**
     * 未退款 (消费权益触发，订单完成)
     */
    NOT_REFUND(-1, "未退款"),

    /**
     * 整单退款
     */
    ALL_REFUND(1, "整单退款"),

    /**
     * 部分退款
     */
    PART_REFUND(0, "部分退款");

    private final int code;

    private final String des;

    public static String getKey(int code) {
        for (DistributeTypeEnum c : DistributeTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.name();
            }
        }
        return null;
    }

    public static String getKey(String message) {
        for (DistributeTypeEnum c : DistributeTypeEnum.values()) {
            if (StringUtils.isNotEmpty(message) && c.getDes().equals(message)) {
                return c.name();
            }
        }
        return message;
    }

    public static String getDesc(int code) {
        for (DistributeTypeEnum c : DistributeTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.des;
            }
        }
        return null;
    }
}
