package com.holderzone.member.common.enums.member;

import java.util.Objects;

/**
 * @ProjectName: member-marketing
 * @ClassName: AccountStateEnum
 * @Author: pantao
 * @Description:
 * @Date: 2021/8/17 16:49
 * @Version: 1.0
 */
public enum AccountStateEnum {

    NORMAL(0,"启用"),

    FREEZE(1,"禁用");

    /**
     * 编号
     */
    private int code;

    /**
     * 名称
     */
    private String des;

    AccountStateEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    public static String getNameByCode(Integer code){
        if (Objects.isNull(code)){
            return "";
        }
        for (AccountStateEnum accountStateEnum : AccountStateEnum.values()){
            if (accountStateEnum.code == code){
                return accountStateEnum.getDes();
            }
        }
        return "";
    }
}
