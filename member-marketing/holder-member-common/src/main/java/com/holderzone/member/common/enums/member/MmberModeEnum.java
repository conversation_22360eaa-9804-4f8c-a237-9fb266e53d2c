package com.holderzone.member.common.enums.member;

import lombok.Getter;

import java.util.Objects;

/**
 * @ProjectName: member-marketing
 * @ClassName: MmberModeEnum
 * @Author: rw
 * @Description:
 * @Version: 1.0
 */
@Getter
public enum MmberModeEnum {

    WEAK_LOGIN(0,"弱登录"),

    STRONG_LOGIN(1,"强登录");

    /**
     * 编号
     */
    private int code;

    /**
     * 名称
     */
    private String des;

    MmberModeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    public static String getNameByCode(Integer code){
        if (Objects.isNull(code)){
            return "";
        }
        for (MmberModeEnum accountStateEnum : MmberModeEnum.values()){
            if (accountStateEnum.code == code){
                return accountStateEnum.getDes();
            }
        }
        return "";
    }

    /**
     *
     * @param code code
     * @return MmberModeEnum
     */
    public static MmberModeEnum getMmberModeEnum(Integer code){
        if(code == null) {
            return null;
        }
        for (MmberModeEnum s : MmberModeEnum.values()) {
            if(code.equals(s.getCode())){
                return s;
            }
        }
        return null;
    }
}
