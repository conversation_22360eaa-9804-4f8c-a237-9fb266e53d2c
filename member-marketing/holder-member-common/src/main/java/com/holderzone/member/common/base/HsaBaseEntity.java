package com.holderzone.member.common.base;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @version 1.0
 * @description: 新实体公共类
 * @date 2023/7/2 17:38
 */
@Data
public class HsaBaseEntity implements Serializable {
    private static final long serialVersionUID = 8424514095125620853L;

    /**
     * 自增主键ID
     */
    @TableId
    private Long id;

    /**
     * 唯一GUID
     */
    private String guid;

    /**
     * 0未删除，时间戳：删除
     */
    @TableLogic(value = "0", delval = "UNIX_TIMESTAMP()")
    private Long isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    public void deleteTime(){
        setIsDelete(System.currentTimeMillis());
    }

    public void setIsDelete(Long isDelete) {
        this.isDelete = isDelete == null ? 0L : isDelete;
    }

    /**
     * 删除状态
     * @return
     */
    public boolean deleted(){
        return isDelete != null && isDelete > 0;
    }
}
