package com.holderzone.member.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 布尔或者是否枚举
 */
@SuppressWarnings("AlibabaEnumConstantsMustHaveComment")
@Getter
@AllArgsConstructor
public enum BooleanEnum {
    FALSE(0, "FALSE"),
    TRUE(1, "TRUE");

    private int code;

    /**
     * 信息
     */
    private String des;

    public static int getCode(Boolean b) {
        if (Boolean.TRUE.equals(b)) {
            return TRUE.getCode();
        }
        return FALSE.getCode();
    }

    public static boolean getCode(Integer b) {
        if (b == BooleanEnum.TRUE.getCode()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
