package com.holderzone.member.common.external.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.aimilin.bean.ExcelResult;
import com.aimilin.bean.ExcelType;
import com.aimilin.utils.ExcelWriteUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.member.common.client.FileOssService;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.*;
import com.holderzone.member.common.dto.crm.CrmCommodityReqDTO;
import com.holderzone.member.common.dto.excel.*;
import com.holderzone.member.common.dto.grade.CommodityInfoDTO;
import com.holderzone.member.common.dto.grade.StrategyInfoDTO;
import com.holderzone.member.common.dto.mall.TeamOperationSubjectModel;
import com.holderzone.member.common.dto.sales.*;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.base.ChannelEnum;
import com.holderzone.member.common.enums.base.SalesItemTypeEnum;
import com.holderzone.member.common.enums.growth.CommodityStatusEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.s2b2bc.ShopMode;
import com.holderzone.member.common.enums.specials.SpecialsTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.external.ExternalItemService;
import com.holderzone.member.common.feign.CrmFeign;
import com.holderzone.member.common.feign.IPaasFeign;
import com.holderzone.member.common.feign.SalesFeign;
import com.holderzone.member.common.qo.base.CommodityBasePageQO;
import com.holderzone.member.common.qo.grade.CommodityInfoQO;
import com.holderzone.member.common.qo.grade.GradeCommodityBasePageQO;
import com.holderzone.member.common.qo.mall.QueryProductCategoryCrmQO;
import com.holderzone.member.common.qo.tool.CommodityDetailsQO;
import com.holderzone.member.common.util.excel.ItemUploadExcelUtil;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.vo.base.CommodityBaseTypeVO;
import com.holderzone.member.common.vo.base.CommodityBaseVO;
import com.holderzone.member.common.vo.feign.CrmFeignModel;
import com.holderzone.member.common.vo.feign.IPaasFeignModel;
import com.holderzone.member.common.vo.feign.SalesDataPageModel;
import com.holderzone.member.common.vo.feign.SalesFeignModel;
import com.holderzone.member.common.vo.grade.StrategyInfoVO;
import com.holderzone.member.common.vo.ipass.ShopBusinessVO;
import com.holderzone.member.common.vo.ipass.StoreSubjectListVO;
import com.holderzone.member.common.vo.mall.ProductCrmCategoryVO;
import com.holderzone.member.common.vo.tool.CommodityDetailsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024-03-13
 * @description 食堂商品服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MarketItemServiceImpl implements ExternalItemService {

    private final CrmFeign crmFeign;

    private final SalesFeign salesFeign;

    private final IPaasFeign iPaasFeign;

    private final FileOssService fileOssService;

    private static final String TABLE_NAME = "goods";

    public static final String CRLF = "\r\n";
    public static final String ONE = "^-?\\d+(\\.\\d{1})?$";
    public static final String TWO = "^(([1-9]\\d*)|(0))(\\.\\d{0,2})?$";
    public static final String DISCOUNT_AMOUNT_ERROR = "【折扣力度】填写错误";
    public static final String LIMIT_ERROR = "【优惠限购】填写错误";


    @Override
    public List<ResArrayStrategyBase> getOperatingStrategy(QueryArrayShopBase queryStoreBasePage) {
        return Collections.emptyList();
    }

    @Override
    public List<ResCategoryBase> queryCategoryByStrategy(QueryArrayShopBase queryStoreBasePage) {
        return Collections.emptyList();
    }

    @Override
    public Pair<Integer, List<ResCommodityBase>> pageStrategyCommodity(QueryCommodityBasePage commodityBasePage) {
        return null;
    }

    @Override
    public List<ResCommodityBase> listCommodityBase(QueryArrayShopBase queryStoreBasePage) {
        return Collections.emptyList();
    }

    @Override
    public List<ResCommodityBase> listCommodityByDetail(QueryArrayShopBase queryStoreBasePage) {
        List<ResCommodityBase> resCommodityBases = new ArrayList<>();
        if (CollUtil.isEmpty(queryStoreBasePage.getCommodityIdList())) {
            log.error("商品编码不能为空");
            return resCommodityBases;
        }

        //获取门店
        IPaasFeignModel<StoreSubjectListVO> marketFeignStoreAllPage = getStoreSubjectListVOIPaasFeignModel();
        if (Objects.isNull(marketFeignStoreAllPage) || CollUtil.isEmpty(marketFeignStoreAllPage.getDataList())) {
            log.error("获取门店为空");
            return resCommodityBases;
        }
        Set<Long> storeBaseInfoList = new HashSet<>();
        for (StoreSubjectListVO storeQueryVO : marketFeignStoreAllPage.getDataList()) {
            storeBaseInfoList.add(storeQueryVO.getStoreTeamInfoId());
        }

        RequestSalesGoodsListDTO request = new RequestSalesGoodsListDTO();

        Integer channelId = salesFeign.querySaleChannel().getData();
        request.setSpu_codes(queryStoreBasePage.getCommodityIdList());
        request.setStore_ids(new ArrayList<>(storeBaseInfoList));
        request.setChannel_id(Collections.singletonList(channelId));
        request.setLimit(NumberConstant.NUMBER_9999);
        request.setPage(NumberConstant.NUMBER_1);
        log.info("querySalesGoodsListBySpu入参:{}", JSON.toJSONString(request));
        SalesFeignModel<SalesDataPageModel> salesFeignModel = salesFeign.querySalesGoodsList(request);
        log.info("querySalesGoodsListBySpu返参:{}", JSON.toJSONString(salesFeignModel));
        if (Objects.isNull(salesFeignModel) || salesFeignModel.getCode() != 0) {
            return resCommodityBases;
        }

        SalesDataPageModel responseSalesGoodsListDTO = salesFeignModel.getData();

        List<ResponseSalesGoodsListDTO> goodsListDTOS = responseSalesGoodsListDTO.getList();

        if (CollUtil.isNotEmpty(goodsListDTOS)) {
            for (ResponseSalesGoodsListDTO goods : responseSalesGoodsListDTO.getList()) {
                ResCommodityBase resCommodityBase = new ResCommodityBase();
                resCommodityBase.setCommodityId(goods.getSpu_code());
                resCommodityBase.setCommodityCode(goods.getSpu_code());
                resCommodityBase.setName(goods.getGoods_sale_name());
                resCommodityBase.setCommodityComboType(String.valueOf(goods.getCombo_type()));
                resCommodityBase.setSystem(SystemEnum.RETAIL.name());
                resCommodityBases.add(resCommodityBase);
            }
        }

        return resCommodityBases;
    }

    @Override
    public Pair<Integer, List<ResGradeCommodityBase>> listStoreCommodityPage(GradeCommodityBasePageQO pageQO) {
        RequestSalesGoodsListDTO request = new RequestSalesGoodsListDTO();
        Integer channelId = salesFeign.querySaleChannel().getData();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(pageQO.getCommityIds())) {
            request.setSpu_codes(pageQO.getCommityIds());
        }
        request.setChannel_id(Collections.singletonList(channelId));
        request.setLimit(NumberConstant.NUMBER_9999);
        request.setPage(NumberConstant.NUMBER_1);

        IPaasFeignModel<StoreSubjectListVO> marketFeignStoreAllPage = getStoreSubjectListVOIPaasFeignModel();

        if (Objects.isNull(marketFeignStoreAllPage) || CollUtil.isEmpty(marketFeignStoreAllPage.getDataList())) {
            return Pair.of(1, CollUtil.empty(List.class));
        }
        //获取门店
        Set<Long> storeBaseInfoList = new HashSet<>();
        for (StoreSubjectListVO storeQueryVO : marketFeignStoreAllPage.getDataList()) {
            storeBaseInfoList.add(storeQueryVO.getStoreTeamInfoId());
        }
        request.setStore_ids(new ArrayList<>(storeBaseInfoList));

        log.info("[querySalesGoodsListBySpu]入参:{}", JSON.toJSONString(request));
        SalesFeignModel<SalesDataPageModel> salesFeignModel = salesFeign.querySalesGoodsList(request);
        log.info("[querySalesGoodsListBySpu]返参:{}", JSON.toJSONString(salesFeignModel));
        List<ResGradeCommodityBase> commodityBaseList = new ArrayList<>();
        if (Objects.isNull(salesFeignModel) || salesFeignModel.getCode() != 0) {
            return Pair.of(1, commodityBaseList);
        }
        SalesDataPageModel responseSalesGoodsListDTO = salesFeignModel.getData();
        log.info("[querySalesGoodsListBySpu]返回数据:{}", JacksonUtils.writeValueAsString(responseSalesGoodsListDTO));

        List<ResponseSalesGoodsListDTO> goodsListDTOS = responseSalesGoodsListDTO.getList();
        if (CollUtil.isEmpty(goodsListDTOS)) {
            return Pair.of(1, commodityBaseList);
        }
        goodsListDTOS.forEach(goods -> {
            ResGradeCommodityBase commodityBase = new ResGradeCommodityBase();
            commodityBase.setCommodityId(goods.getSpu_code());
            commodityBase.setId(Math.toIntExact(goods.getGoods_id()));
            commodityBase.setName(goods.getGoods_sale_name());
            commodityBase.setType(String.valueOf(goods.getCombo_type()));
            commodityBase.setState(CommodityStatusEnum.COMMODITY_UP.getDes());
            commodityBase.setSystem(SystemEnum.RETAIL.name());
            commodityBaseList.add(commodityBase);
        });

        return Pair.of(1, commodityBaseList);
    }

    @Override
    public Page<CommodityBaseVO> pageCommodityBase(CommodityBasePageQO pageQO) {
        Page<CommodityBaseVO> page = new Page<>();
        page.setCurrentPage(pageQO.getCurrentPage());
        page.setPageSize(pageQO.getPageSize());


        IPaasFeignModel<StoreSubjectListVO> marketFeignStoreAllPage = getStoreSubjectListVOIPaasFeignModel();

        if (Objects.isNull(marketFeignStoreAllPage) || CollUtil.isEmpty(marketFeignStoreAllPage.getDataList())) {
            return page;
        }
        //获取门店
        Set<Long> storeBaseInfoList = new HashSet<>();
        for (StoreSubjectListVO storeQueryVO : marketFeignStoreAllPage.getDataList()) {
            storeBaseInfoList.add(storeQueryVO.getStoreTeamInfoId());
        }
        RequestSalesGoodsListDTO request = new RequestSalesGoodsListDTO();
        request.setPage(pageQO.getCurrentPage());
        request.setLimit(pageQO.getPageSize());
        request.setStore_ids(new ArrayList<>(storeBaseInfoList));

        if (StringUtils.isNotEmpty(pageQO.getCategoryId())) {
            request.setCategory(Collections.singletonList(Long.valueOf(pageQO.getCategoryId())));
        }

        //商品类型校验
        if (Objects.nonNull(pageQO.getCommodityType())) {
            request.setCombo_type(Collections.singletonList(pageQO.getCommodityType().longValue()));
        }

        Integer channelId = salesFeign.querySaleChannel().getData();
        log.info("[渠道获取:{}", JSON.toJSONString(channelId));
        request.setChannel_id(Collections.singletonList(channelId));
        if (StringUtils.isNotEmpty(pageQO.getSearchKey())) {
            request.setKeywords(pageQO.getSearchKey());
        }
        log.info("salesFeignModel request:{}", JSON.toJSONString(request));
        SalesFeignModel<SalesDataPageModel> salesFeignModel = salesFeign.querySalesGoodsList(request);


        log.info("salesFeignModel:{}", JSON.toJSONString(salesFeignModel));
        if (Objects.isNull(salesFeignModel) || salesFeignModel.getCode() != 0) {
            log.error("salesFeignModel error:{}", salesFeignModel.getMsg());
            return page;
        }
        SalesDataPageModel responseSalesGoodsListDTO = salesFeignModel.getData();
        page.setTotalCount(responseSalesGoodsListDTO.getTotal());

        List<CommodityBaseVO> commodityBaseVOList = new ArrayList<>();
        getCommodityBaseVOList(responseSalesGoodsListDTO, commodityBaseVOList, page);
        return page;
    }

    @Override
    public List<CommodityBaseTypeVO> getStoreGoodsComboType() {
        List<ResponseSalesGoodsTypeDTO> querySalesGoodsType = salesFeign.querySalesGoodsType().getData();

        if (CollUtil.isNotEmpty(querySalesGoodsType)) {
            return querySalesGoodsType.stream().map(item -> {
                CommodityBaseTypeVO commodityBaseVO = new CommodityBaseTypeVO();
                commodityBaseVO.setId(item.getId());
                commodityBaseVO.setName(item.getName());
                return commodityBaseVO;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private IPaasFeignModel<StoreSubjectListVO> getStoreSubjectListVOIPaasFeignModel() {
        List<String> operSubjectIdList = Collections.singletonList(ThreadLocalCache.getOperSubjectGuid());
        TeamOperationSubjectModel teamOperationSubjectModel = new TeamOperationSubjectModel();
        teamOperationSubjectModel.setOperationSubjectIds(operSubjectIdList);
        log.info("Ipass model:{}", JSON.toJSONString(operSubjectIdList));
        IPaasFeignModel<StoreSubjectListVO> marketFeignStoreAllPage = iPaasFeign.findStoreByOperationSubjectId(teamOperationSubjectModel);
        if (Objects.nonNull(marketFeignStoreAllPage) && CollUtil.isNotEmpty(marketFeignStoreAllPage.getDataList())) {
            //排除供应商
            marketFeignStoreAllPage.setDataList(
                    marketFeignStoreAllPage.getDataList()
                            .stream()
                            .filter(item -> {
                                ShopBusinessVO shopBusinessVO = JSON.parseObject(item.getBusiness(), ShopBusinessVO.class);
                                if (shopBusinessVO != null && shopBusinessVO.getShopMode() != null && shopBusinessVO.getShopMode().equals(ShopMode.SUPPLIER)) {
                                    return false;
                                } else {
                                    return true;
                                }
                            })
                            .collect(Collectors.toList())
            );
        }
        log.info("Ipass marketFeignStoreAllPage:{}", JSON.toJSONString(marketFeignStoreAllPage));
        return marketFeignStoreAllPage;
    }

    private static void getCommodityBaseVOList(SalesDataPageModel responseSalesGoodsListDTO,
                                               List<CommodityBaseVO> commodityBaseVOList,
                                               Page<CommodityBaseVO> page) {
        if (CollUtil.isNotEmpty(responseSalesGoodsListDTO.getList())) {
            for (ResponseSalesGoodsListDTO goods : responseSalesGoodsListDTO.getList()) {
                CommodityBaseVO commodityBaseVO = new CommodityBaseVO();
                commodityBaseVO.setCommodityId(goods.getSpu_code());
                commodityBaseVO.setCommodityName(goods.getGoods_sale_name());
                commodityBaseVO.setCommodityCode(goods.getSpu_code());
                commodityBaseVO.setChannel(SystemEnum.RETAIL.getDes());
                commodityBaseVO.setSystem(SystemEnum.RETAIL.name());
                commodityBaseVO.setBusinessType(SystemEnum.RETAIL.getCode());
                commodityBaseVO.setCategoryId(String.valueOf(goods.getCategory()));
                commodityBaseVO.setCommodityType(goods.getCombo_type());
                commodityBaseVO.setCommodityPrice(goods.getSellingPrice());
                commodityBaseVOList.add(commodityBaseVO);
            }
            page.setData(commodityBaseVOList);
        }
    }

    @Override
    public List<CommodityInfoDTO> listCommodity(CommodityInfoQO commodityInfoQO) {
        return Collections.emptyList();
    }

    @Override
    public CrmFeignModel<CommodityInfoDTO> listCommodityHasCount(CommodityInfoQO commodityInfoQO) {
        return new CrmFeignModel<>();
    }

    @Override
    public List<StrategyInfoVO> listStrategyInfo(StrategyInfoDTO strategyInfoDTO) {
        return Collections.emptyList();
    }


    @Override
    public String getCommodityUrl(CrmCommodityReqDTO reqDTO) {
        return null;
    }

    @Override
    public List<CommodityDetailsVO> getCommodityDetails(CommodityDetailsQO detailsQO) {
        return Collections.emptyList();
    }

    @Override
    public List<CommodityDetailsVO> getNewCommodityDetails(CommodityDetailsQO detailsQO) {
        return crmFeign.getNewCommodityDetails(detailsQO).getData();
    }

    @Override
    public List<ProductCrmCategoryVO> getCommodityCategory(QueryProductCategoryCrmQO categoryCrmQO) {
        List<ProductCrmCategoryVO> productCrmCategoryVOS = Lists.newArrayList();
        SalesFeignModel<List<ResponseSalesGoodsCategoryDTO>> salesFeignModel = salesFeign.querySalesGoodsCategory(TABLE_NAME, Boolean.TRUE, Boolean.TRUE);
        if (salesFeignModel.getCode() != 0) {
            log.error("salesFeignModel error:{}", salesFeignModel.getMsg());
            return productCrmCategoryVOS;
        }

        List<ResponseSalesGoodsCategoryDTO> responseSalesGoodsCategoryDTO = salesFeignModel.getData();

        if (CollUtil.isNotEmpty(responseSalesGoodsCategoryDTO)) {
            for (ResponseSalesGoodsCategoryDTO salesGoodsCategoryDTO : responseSalesGoodsCategoryDTO) {
                ProductCrmCategoryVO productCrmCategoryVO = new ProductCrmCategoryVO();

                productCrmCategoryVO.setCategory_id(salesGoodsCategoryDTO.getId().toString());
                productCrmCategoryVO.setCategory_name(salesGoodsCategoryDTO.getName());
                productCrmCategoryVOS.add(productCrmCategoryVO);

                forDealCategory(salesGoodsCategoryDTO.getChildren(), productCrmCategoryVOS);
            }
        }
        return productCrmCategoryVOS;
    }

    private static void forDealCategory(List<ResponseSalesGoodsCategoryDTO> children,
                                        List<ProductCrmCategoryVO> productCrmCategoryVOS) {
        if (CollUtil.isNotEmpty(children)) {

            for (ResponseSalesGoodsCategoryDTO child : children) {
                ProductCrmCategoryVO childVO = new ProductCrmCategoryVO();
                childVO.setCategory_id(child.getId().toString());
                childVO.setCategory_name(child.getName());
                productCrmCategoryVOS.add(childVO);
                forDealCategory(child.getChildren(), productCrmCategoryVOS);

            }

        }
    }

    @Override
    public ItemUploadVO itemUploadExcelUrl(String fileUrl, Integer activityType) {
        List<ItemUploadExcel> itemUploadExcels = ItemUploadExcelUtil.read(fileUrl);
        log.info("itemUploadExcels={}", JacksonUtils.writeValueAsString(itemUploadExcels));

        // 条件验证(默认不读取前三条模板数据)
        if (CollUtil.isEmpty(itemUploadExcels)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_EMPTY_UPLOAD_EXCEL);
        }

        // 数量校验
        if (itemUploadExcels.size() > NumberConstant.NUMBER_20000) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_MAX_EXPORT_EXCEL);
        }
        List<String> commodityCodeList = itemUploadExcels.stream()
                .map(ItemUploadExcel::getCommodityCode)
                .distinct()
                .collect(Collectors.toList());

        // 返回成功数据
        List<ItemUploadExcelVO> itemUploadExcelSuccessList = Lists.newArrayList();
        // 返回失败数据
        List<ItemUploadErrorExcel> itemUploadExcelErrorList = Lists.newArrayList();

        // 商品查询
        QueryArrayShopBase shopBase = new QueryArrayShopBase();
        shopBase.setCommodityIdList(commodityCodeList);
        List<ResCommodityBase> commodityBaseList = this.listCommodityByDetail(shopBase);
        if (CollectionUtils.isEmpty(commodityBaseList)) {
            // 全部商品都是失败
            log.warn("商品查询为空");
            itemUploadExcels.forEach(itemUploadExcel -> {
                ItemUploadErrorExcel error = getItemUploadErrorExcel(itemUploadExcel, "【商品编码】填写错误", activityType);
                itemUploadExcelErrorList.add(error);
            });
            return getItemUploadExcelVO(itemUploadExcelErrorList, itemUploadExcelSuccessList, activityType);
        }
        buildItemUploadExcelResult(commodityBaseList, itemUploadExcels, itemUploadExcelSuccessList, itemUploadExcelErrorList, activityType);

        return getItemUploadExcelVO(itemUploadExcelErrorList, itemUploadExcelSuccessList, activityType);
    }

    private ItemUploadErrorExcel getItemUploadErrorExcel(ItemUploadExcel itemUploadExcel, String failReason,
                                                         Integer activityType) {
        ItemUploadErrorExcel error = new ItemUploadErrorExcel();
        error.setChannel(itemUploadExcel.getChannel());
        error.setCommodityCode(itemUploadExcel.getCommodityCode());
        error.setCommodityName(itemUploadExcel.getCommodityName());
        if (Objects.isNull(activityType)) {
            error.setCommodityType(itemUploadExcel.getCommodityType());
            error.setSpecialsType(itemUploadExcel.getSpecialsType());
            error.setSpecialsNumber(itemUploadExcel.getSpecialsNumber());
            error.setLimitNumber(itemUploadExcel.getLimitNumber());
        }
        error.setFailReason(failReason);
        return error;
    }

    private ItemUploadVO getItemUploadExcelVO(List<ItemUploadErrorExcel> itemUploadExcelErrorList,
                                              List<ItemUploadExcelVO> itemUploadExceSuccesslList,
                                              Integer activityType) {
        // 上传至阿里oos
        ItemUploadVO excelVO = new ItemUploadVO();
        excelVO.setFail(itemUploadExcelErrorList.size());
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String formatDateTime = now.format(formatter);
        if (!CollectionUtils.isEmpty(itemUploadExcelErrorList)) {
            ExcelResult excelResult;
            String excelName;
            if (Objects.nonNull(activityType)) {
                List<ItemNotUploadErrorExcel> notUploadErrorExcels = Lists.newArrayList();
                for (ItemUploadErrorExcel itemUploadErrorExcel : itemUploadExcelErrorList) {
                    ItemNotUploadErrorExcel excelExcel = new ItemNotUploadErrorExcel();
                    BeanUtils.copyProperties(itemUploadErrorExcel, excelExcel);
                    notUploadErrorExcels.add(excelExcel);
                }
                excelResult = com.aimilin.utils.BeanUtils.toResult(notUploadErrorExcels);
                excelName = "满减满折批量导入商品%s";
            } else {
                excelResult = com.aimilin.utils.BeanUtils.toResult(itemUploadExcelErrorList);
                excelName = "限时特价批量导入商品%s";
            }
            byte[] write = ExcelWriteUtils.write(excelResult, ExcelType.XLSX);
            try {
                FileDto fileDto = new FileDto();
                fileDto.setFileContent(com.holderzone.framework.security.SecurityManager.entryptBase64(write));
                fileDto.setFileName(String.format(excelName, formatDateTime) + "." + ExcelType.XLSX);
                String upload = fileOssService.upload(fileDto);
                String newUpload = upload.replace("http", "https");
                excelVO.setFailUrl(newUpload);
                excelVO.setFail(itemUploadExcelErrorList.size());
            } catch (Exception e) {
                log.error("上传文件失败", e);
                excelVO.setFailUrl("上传错误信息失败");
            }
        }
        excelVO.setSuccess(itemUploadExceSuccesslList.size());
        excelVO.setItemUploadExcelList(itemUploadExceSuccesslList);
        return excelVO;
    }

    private void buildItemUploadExcelResult(List<ResCommodityBase> commodityBaseList,
                                            List<ItemUploadExcel> itemUploadExcelReadList,
                                            List<ItemUploadExcelVO> itemUploadExcelSuccessList,
                                            List<ItemUploadErrorExcel> itemUploadExcelErrorList,
                                            Integer activityType) {
        Map<String, ResCommodityBase> commodityBaseMap = commodityBaseList.stream()
                .collect(Collectors.toMap(ResCommodityBase::getCommodityCode, Function.identity(), (entity1, entity2) -> entity1));

        // 商品重复校验
        Set<String> commodityCodeSet = new HashSet<>();
        for (ItemUploadExcel uploadExcel : itemUploadExcelReadList) {
            StringBuilder errorSb = new StringBuilder();
            String channel = uploadExcel.getChannel();
            if (com.holderzone.framework.util.StringUtils.isEmpty(channel)) {
                errorSb.append("【商品销售渠道】未填写" + CRLF);
            }
            // 暂时只有这个一个
            if (!ChannelEnum.MARKET_POS.getType().equals(channel)) {
                errorSb.append("【商品销售渠道】填写错误" + CRLF);
            }
            checkItemInfo(uploadExcel, errorSb, commodityBaseMap, commodityCodeSet, activityType);
            if (Objects.isNull(activityType)) {
                checkActivityNeed(uploadExcel, errorSb);
            }
            if (StringUtils.isEmpty(errorSb.toString())) {
                ItemUploadExcelVO excelVO = getItemUploadExcelVO(uploadExcel, activityType);
                itemUploadExcelSuccessList.add(excelVO);
            } else {
                ItemUploadErrorExcel error = getItemUploadErrorExcel(uploadExcel, errorSb.toString(), activityType);
                itemUploadExcelErrorList.add(error);
            }
        }
    }

    private ItemUploadExcelVO getItemUploadExcelVO(ItemUploadExcel itemUploadExcel,
                                                   Integer activityType) {
        ItemUploadExcelVO excelVO = new ItemUploadExcelVO();
        excelVO.setChannel(itemUploadExcel.getChannel());
        excelVO.setCommodityId(itemUploadExcel.getCommodityCode());
        excelVO.setCommodityCode(itemUploadExcel.getCommodityCode());
        excelVO.setCommodityName(itemUploadExcel.getCommodityName());
        if (Objects.isNull(activityType)) {
            int commodityType = SalesItemTypeEnum.getCode(itemUploadExcel.getCommodityType());
            excelVO.setCommodityType(commodityType);

            int specialsType = SpecialsTypeEnum.getCode(itemUploadExcel.getSpecialsType());
            excelVO.setSpecialsType(specialsType);
            excelVO.setSpecialsNumber(new BigDecimal(itemUploadExcel.getSpecialsNumber()));
            if (!com.holderzone.framework.util.StringUtils.isEmpty(itemUploadExcel.getLimitNumber())) {
                excelVO.setLimitNumber(Integer.valueOf(itemUploadExcel.getLimitNumber()));
            }
        }
        return excelVO;
    }

    private void checkItemInfo(ItemUploadExcel itemUploadExcel,
                               StringBuilder errorSb,
                               Map<String, ResCommodityBase> commodityBaseMap,
                               Set<String> commodityCodeSet,
                               Integer activityType) {
        String commodityCode = itemUploadExcel.getCommodityCode();
        if (com.holderzone.framework.util.StringUtils.isEmpty(commodityCode)) {
            errorSb.append("【商品编码】未填写" + CRLF);
        }
        ResCommodityBase commodityBase = commodityBaseMap.get(commodityCode);
        if (commodityCodeSet.contains(commodityCode) || ObjectUtils.isEmpty(commodityBase)) {
            errorSb.append("【商品编码】填写错误" + CRLF);
            return;
        }
        itemUploadExcel.setCommodityName(commodityBase.getName());
        commodityCodeSet.add(commodityCode);

        if (Objects.isNull(activityType)) {
            String commodityType = itemUploadExcel.getCommodityType();
            if (com.holderzone.framework.util.StringUtils.isEmpty(commodityType)) {
                errorSb.append("【商品类型】未填写" + CRLF);
            }
            if (!Objects.equals(SalesItemTypeEnum.getName(Integer.parseInt(commodityBase.getCommodityComboType())), commodityType)) {
                errorSb.append("【商品类型】填写错误" + CRLF);
            }
        }
    }

    private void checkActivityNeed(ItemUploadExcel itemUploadExcel, StringBuilder errorSb) {
        String limitNumber = itemUploadExcel.getLimitNumber();
        try {
            if (!com.holderzone.framework.util.StringUtils.isEmpty(limitNumber) && Integer.parseInt(limitNumber) > 9999) {
                errorSb.append(LIMIT_ERROR + CRLF);
            }
        } catch (Exception e) {
            log.warn("[优惠限购类型转换错误]e=", e);
            errorSb.append(LIMIT_ERROR + CRLF);
            return;
        }

        String specialsType = itemUploadExcel.getSpecialsType();
        if (com.holderzone.framework.util.StringUtils.isEmpty(specialsType)) {
            errorSb.append("【折扣方式】未选择" + CRLF);
            return;
        }
        String specialsNumber = itemUploadExcel.getSpecialsNumber();
        if (com.holderzone.framework.util.StringUtils.isEmpty(specialsNumber)) {
            errorSb.append("【折扣力度】未填写" + CRLF);
            return;
        }
        checkSpecialsNum(errorSb, specialsNumber, specialsType);
    }

    private void checkSpecialsNum(StringBuilder errorSb, String specialsNumber, String specialsType) {
        BigDecimal specialsNum;
        try {
            specialsNum = new BigDecimal(specialsNumber);
        } catch (Exception e) {
            log.warn("[金额类型转换错误]e=", e);
            errorSb.append(DISCOUNT_AMOUNT_ERROR + CRLF);
            return;
        }
        switch (SpecialsTypeEnum.getEnum(specialsType)) {
            // 打折：0.1~9.9，最多1位小数
            case DISCOUNT:
                if (BigDecimalUtil.lessThan(specialsNum, new BigDecimal("0.1"))
                        || BigDecimalUtil.greaterThan(specialsNum, new BigDecimal("9.9"))
                        || !specialsNumber.matches(ONE)) {
                    errorSb.append(DISCOUNT_AMOUNT_ERROR + CRLF);
                }
                break;
            // 减价：正数输入框，最多2位小数，最大999999.99
            case SALE:
                if (BigDecimalUtil.lessEqual(specialsNum, new BigDecimal("0"))
                        || BigDecimalUtil.greaterThan(specialsNum, new BigDecimal("999999.99"))
                        || !specialsNumber.matches(TWO)) {
                    errorSb.append(DISCOUNT_AMOUNT_ERROR + CRLF);
                }
                break;
            // 指定价格：，可输入0，最大999999.99，最多2位小数
            case SPECIFY_PRICE:
                if (BigDecimalUtil.lessThan(specialsNum, new BigDecimal("0"))
                        || BigDecimalUtil.greaterThan(specialsNum, new BigDecimal("999999.99"))
                        || !specialsNumber.matches(TWO)) {
                    errorSb.append(DISCOUNT_AMOUNT_ERROR + CRLF);
                }
                break;
            default:
                errorSb.append("【折扣方式】填写错误" + CRLF);
                break;
        }
    }
}
