package com.holderzone.member.common.enums.member;


import java.util.Objects;

/**
 * <AUTHOR>
 */

public enum MemberInfoBaseItemEnum {

    HEAD_IMG_URL(1,"headImgUrl"),

    USER_NAME(2,"userName"),

    SEX(3,"sex"),

    BIRTHDAY(4,"birthday"),

    PHONE_NUM(5,"phoneNum"),

    EMAIL(6,"email"),

    WORK_AREA(7,"workArea"),

    COMPANY_NAME(8,"companyName"),

    COMPANY_ADDRESS(9,"companyAddress"),

    INDUSTRY(10,"industry"),

    JOB_TITLE(11,"jobTitle"),

    INTRODUCTION(12,"introduction"),

    DEPARTMENT_NAME(13,"departmentName"),

    CUSTOMIZE(0,"customize"),

    ;

    /**
     * 编号
     */
    private int seq;

    /**
     * 名称
     */
    private String field;

    MemberInfoBaseItemEnum(int seq, String field) {
        this.seq = seq;
        this.field = field;
    }

    public int getSeq() {
        return seq;
    }

    public String getField() {
        return field;
    }

    public static String getFieldBySeq(Integer seq){
        if (Objects.isNull(seq)){
            return "";
        }
        for (MemberInfoBaseItemEnum baseItemEnum : MemberInfoBaseItemEnum.values()){
            if (baseItemEnum.seq == seq){
                return baseItemEnum.getField();
            }
        }
        return "";
    }

}
