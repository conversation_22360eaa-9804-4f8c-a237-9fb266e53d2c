package com.holderzone.member.common.constant;

/**
 * <AUTHOR>
 * @description redis的键
 * @date 2021/9/1
 */
public final class RedisKeyConstant {

    /**
     * 获取锁超时时间: s
     */
    public final static int TRY_LOCK_TIME = 30;


    public static String COLON = ":";

    /**
     * 空值
     */
    public final static String EMPTY = "empty:";

    public final static String EXCEL_CACHE_KEY = "EXCEL_CACHE_KEY:";

    /**
     * 投票活动列表
     */
    public static final String VOTE_ACTIVITY_KEY = "VOTE_ACTIVITY_KEY:";

    /**
     * feign
     */
    public static final String CACHE_FEIGN = "cache:feign";

    /**
     * 微页面推广key
     */
    public static final String POPULARIZE_KEY = "POPULARIZE_KEY:";

    /**
     * 等级刷新
     */
    public static final String GRADE_REFRESH = "GRADE_REFRESH";

    /**
     * 等级信息变化
     */
    public static final String GRADE_INFO_CHANGE = "GRADE_INFO_CHANGE";

    /**
     * 旧等级
     */
    public static final String GRADE_EQUITIE_OLD_LIST = "GRADE_EQUITIE_OLD_LIST:%s:%s";

    /**
     * 同步门店
     */
    public static final String LOCK_SYNC_STORE = "lock:sync:store:%s:%s";

    /**
     * 应用业务
     */
    public static final String APPLY_BUSINESS_LIST = "APPLY_BUSINESS_LIST:";

    /**
     * 应用终端
     */
    public static final String APPLY_TERMINAL_LIST = "APPLY_TERMINAL_LIST:";

    /**
     * 结算规则
     */
    public static final String SETTLEMENT_RULE = "settlement_Rule:";

    /**
     * 同步结算规则
     */
    public static final String SYNC_SETTLEMENT_RULE = "sync_settlement_Rule:";

    /**
     * 订单优惠锁定
     */
    public static final String SETTLEMENT_LOCKED = "settlement_locked:";

    /**
     * 订单优惠释放
     */
    public static final String SETTLEMENT_UNLOCKED = "settlement_unlocked:";

    /**
     * 结算订单
     */
    public static final String SETTLEMENT_RECOUNT = "settlement_recount:%s:%s";

    /**
     * 结算规则缓存
     */
    public static final String SETTLEMENT_RULE_CACHE_KEY = "settlement_rule_cache:%s:%s:%s";

    /**
     * 获取锁超时时间: s
     */
    public static final int SETTLEMENT_RULE_CACHE_TIME = 60;

    /**
     * 门店id
     */
    public static final String STORE_ID = "storeId:";


    /**
     * 标签批次处理
     */
    public static final String LABEL_BATCH = "LABEL_BATCH:%s:%s";

    /**
     * 权益初始化
     */
    public static final String EQUITIES_INIT = "EQUITIES_INIT:%s";

    /**
     * 限时特价活动key
     */
    public static final String LIMIT_SPECIALS_ACTIVITY_KEY = "LIMIT_SPECIALS_ACTIVITY:";

    /**
     * 第N份优惠活动key
     */
    public static final String NTH_ACTIVITY_KEY = "NTH_ACTIVITY_KEY:";

    public static final String CARD_RECHARGE_NEW = "CARD_RECHARGE_NEW:";

    public static final String CUSTOMIZED_AUTH_URL = "CUSTOMIZED_AUTH_URL:";

    public static final String REDIRECT_CUSTOMIZED_AUTH_URL = "REDIRECT_CUSTOMIZED_AUTH_URL:%s:%s";

    public static final String CUSTOMIZED_AUTH_STATUS = "CUSTOMIZED_AUTH_STATUS:%s";

    public static final String EVERY_DAY_RECORD = "EVERY_DAY_RECORD:";

    /**
     * 获取key
     *
     * @param key    eq: settlement_recount:%s:%s
     * @param values 1,2,3
     * @return settlement_recount:1:2:3
     */
    public static String getKey(String key, Object... values) {
        return String.format(key, values);
    }
}
