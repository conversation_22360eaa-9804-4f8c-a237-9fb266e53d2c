package com.holderzone.member.common.enums.equities;


import java.util.Objects;

/**
 * 成长值规则类型枚举
 * author: pantao
 */
public enum EquitiesRuleTypeEnum {

    /**
     * 线下权益
     * 把之前的 商品折扣  改成：--->线下权益
     */
    OFFLINE_EQUITIES(0, "线下权益"),

    /**
     * 满减
     */
    ENOUGH_REDUCE(1, "满减"),

    /**
     * 直减
     */
    DIRECT_REDUCE(2, "直减"),

    /**
     * 会员折扣
     * 把之前的 会员价 改成 -->会员折扣
     */
    MEMBER_PRICE(3, "会员折扣"),

    /**
     * 赠送成长值
     */
    GIVE_GROWTH_VALUE(4, "赠送成长值"),

    /**
     * 翻倍成长值
     */
    DOUBLE_GROWTH_VALUE(5, "翻倍成长值"),

    /**
     * 赠送积分
     */
    GIVE_INTEGRAL(6,"赠送积分"),

    /**
     * 翻倍积分
     */
    DOUBLE_INTEGRAL(7,"翻倍积分"),

    /**
     * 商品会员价
     */
    GOODS_MEMBER(8,"商品会员价"),
    /**
     * 优先发货
     */
    PRIORITY_SHIPPING(9,"优先发货"),
    /**
     * 极速售后
     */
    FAST_AFTER_SALES(10,"极速售后"),
    /**
     * 专属客服
     */
    DEDICATED_CUSTOMER_SERVICE(11,"专属客服"),

    /**
     * 免运费
     */
    FREE_SHIPPING(12,"物流优惠"),


    /**
     * 升级礼包
     */
    GRAD_GIF_BAG(999,"升级礼包"),

    ;




    /**
     * 编号
     */
    private int code;

    /**
     * 描述
     */
    private String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    EquitiesRuleTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public static EquitiesRuleTypeEnum getEnumByCode(Integer code){
        if (Objects.isNull(code)){
            return null;
        }
        for (EquitiesRuleTypeEnum type : EquitiesRuleTypeEnum.values()){
            if (type.code == code){
                return type;
            }
        }
        return null;
    }
}
