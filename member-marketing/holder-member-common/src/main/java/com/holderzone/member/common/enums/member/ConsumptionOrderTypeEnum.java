package com.holderzone.member.common.enums.member;

import java.util.Objects;

/**
 * 【与数据库同步的类型和说明】
 * author: pantao
 */
public enum ConsumptionOrderTypeEnum {

    /**
     * 食堂预订
     */
    SCHEDULED_ORDER(0, "食堂预订"),

    /**
     * 自助餐
     */
    BUFFET(1, "堂食自助"),

    /**
     * 堂食点餐
     */
    ORDER_ORDER(2, "堂食点餐"),

    /**
     * 快速收款
     */
    FAST_PAYMENT(3, "快速收款"),

    /**
     * 自营外卖
     */
    TAKEAWAY(4, "自营外卖"),

    /**
     * 会员商城
     */
    MALL_ORDER(13, "商城订单"),

    /**
     * 自助小碗菜
     */
    SMALL_MEAL(14, "自助小碗菜"),

    /**
     * 好搭档
     */
    PARTNER(15, "好搭档"),

    /**
     * 私域商城
     */
    MALL(16, "私域商城"),

    /**
     * 餐饮云-正餐
     */
    DINNER_FOOD(51, "餐饮云-正餐"),
    /**
     * 餐饮云-快餐
     */
    FAST_FOOD(52, "餐饮云-快餐"),
    ;

    private int code;

    private String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    ConsumptionOrderTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public static String getDesByCode(Integer code) {
        if (Objects.isNull(code)) {
            return "";
        }
        for (ConsumptionOrderTypeEnum consumptionOrderTypeEnum : ConsumptionOrderTypeEnum.values()) {
            if (consumptionOrderTypeEnum.getCode() == code) {
                return consumptionOrderTypeEnum.getDes();
            }
        }
        return "";
    }
}
