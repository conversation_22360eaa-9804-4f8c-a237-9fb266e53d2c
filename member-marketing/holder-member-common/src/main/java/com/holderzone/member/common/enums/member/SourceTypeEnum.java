package com.holderzone.member.common.enums.member;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 会员注册/开卡来源,0后台添加,1POS机注册,2一体机注册,3后台导入，
 * 微网站(21微信扫码点餐，24微信注册.25微信C端后台注册)，
 * 微信小程序(51和惠多，52翼惠天下，53赚餐)
 * ps: 微信扫码点餐 微信注册 按产品要求统一合并成微信公众号
 *
 * <AUTHOR>
 */
@SuppressWarnings("AlibabaEnumConstantsMustHaveComment")
@Getter
@AllArgsConstructor
public enum SourceTypeEnum {
    UNDEFINED(-1, ""),
    ADD_BACKGROUND(0, "管理后台"),
    ADD_ONE_MACHINE(2, "一体机"),
    ADD_BACKGROUND_IMPORT(3, "后台导入"),

    ADD_SELF_HELP(60, "自助充值机"),
    /**
     * 新老会员都在用
     */
    ADD_WECHAT_ZHUANCAN(53, "微信小程序"),
    /**
     * 老会员
     */
    ADD_ZHUANCAN(8, "微信小程序"),
    /**
     * 支付宝小程序
     */
    ADD_ALI_PAY(55, "支付宝小程序"),

    /**
     * 私域商城
     *
     * h5，小程序，公用
     */
    MALL_PC(70, "私域商城-PC"),
    MALL_H5(71, "H5"),
    MALL_WECHAT_APPLET(72, "微信小程序"),

    /**
     * 餐饮云
     */
    REPAST_WECHAT_APPLET(80, "餐饮云-微信小程序"),
    REPAST_ALI_APPLET(81, "餐饮云-支付宝小程序"),
    REPAST_H5(82, "餐饮云-H5"),
    REPAST_AIO(83, "餐饮云-一体机"),
    REPAST_POS(84, "餐饮云-POS"),
    REPAST_PAD(85, "餐饮云-PAD"),
    ;

    private int code;

    /**
     * 信息
     */
    private String des;

    /**
     * 是否是私域商城来源
     *
     * @param source 来源
     * @return 是否是私域商城来源
     */
    public static boolean isMallSource(int source) {
        return source == MALL_PC.getCode()
                || source == MALL_H5.getCode()
                || source == MALL_WECHAT_APPLET.getCode();
    }

    /**
     * 私域商城来源
     */
    public static List<Integer> getMallSource() {
        List<SourceTypeEnum> sourceTypeEnums = Arrays.asList(
                MALL_PC,
                MALL_H5,
                MALL_WECHAT_APPLET);

        return sourceTypeEnums.stream().map(SourceTypeEnum::getCode).collect(Collectors.toList());
    }

    public static List<Integer> getAllWechatType() {
        List<SourceTypeEnum> sourceTypeEnums = Arrays.asList(
                MALL_WECHAT_APPLET,
                REPAST_WECHAT_APPLET,
                ADD_WECHAT_ZHUANCAN,
                ADD_ZHUANCAN);

        return sourceTypeEnums.stream().map(SourceTypeEnum::getCode).collect(Collectors.toList());
    }

    /**
     * 根据code获取name
     *
     * @param code code
     * @return 操作结果
     */
    public static String getMsgByCode(int code) {
        for (SourceTypeEnum titleEnum : SourceTypeEnum.values()) {
            if (titleEnum.getCode() == code) {
                return titleEnum.getDes();
            }
        }
        return null;
    }

    public static String getDesByCode(Integer code) {
        if (Objects.isNull(code)) {
            return "-";
        }
        for (SourceTypeEnum titleEnum : SourceTypeEnum.values()) {
            if (titleEnum.getCode() == code) {
                String d = titleEnum.getDes();
                if (d.contains("注册")) {
                    d = d.replace("注册", "");
                }
                return d;
            }
        }
        return null;
    }

    public static SourceTypeEnum valueOfCode(Integer code) {
        if (code == null) {
            return UNDEFINED;
        }
        for (SourceTypeEnum sourceType : values()) {
            if (code == sourceType.getCode()) {
                return sourceType;
            }
        }
        return UNDEFINED;
    }

    /**
     * 是否硬件设备
     * 一体机、自助充值机
     * @param source
     * @return
     */
    public static boolean isDevice(int source) {
        return source == 2 || source == 60;
    }
}
