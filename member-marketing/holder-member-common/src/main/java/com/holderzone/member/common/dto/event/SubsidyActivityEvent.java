package com.holderzone.member.common.dto.event;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 补贴活动
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
@Data
@Accessors(chain = true)
public class SubsidyActivityEvent implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 补贴活动guid
     */
    private String guid;

    /**
     * 企业guid
     */
    private String enterpriseGuid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 补贴金额
     */
    private BigDecimal subsidyMoney;

    /**
     * 活动状态 0 禁用 1 启用(进行中)  2  已结束  3  草稿 4 未开始
     */
    private Integer activityState;

    /**
     * 会员卡guid
     */
    private String cardGuidJson;

    /**
     * 活动编号
     */
    private String activityNum;

    /**
     * 补贴频率 0 单次 1 每天 2 每周 3 每月 4 每年
     */
    private Integer subsidyFrequencyType;

    /**
     * 补贴机制  具体日期时间
     */
    private String subsidyJson;

    /**
     * 补贴用户类型 0 按标签筛选 1 指定用户 2 主体下所有用户  3 缓存重发机制
     */
    private Integer subsidyFilterType;

    /**
     * 缓存重发指定用户会员guid
     * 此数据由程序筛选
     */
    private String cacheMemberInfoGuidJson;

    /**
     * 指定用户会员guid
     */
    private String memberInfoGuidJson;

    /**
     * 标签guid（按手动标签筛选存在）
     */
    private String labelGuidJson;

    /**
     * 活动开始时间
     */
    private LocalDateTime activityStartTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime activityEndTime;

    /**
     * 一个补贴活动对应多条记录guid
     */
    private List<String> recordGuid;

    /**
     * 操作人员账号
     */
    @ApiModelProperty("操作人员账号")
    private String operatorTelName;
}
