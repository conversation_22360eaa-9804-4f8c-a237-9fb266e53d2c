package com.holderzone.member.common.enums.member;

import java.util.List;

public enum MemberInfoListEnum {

    ACCOUNT_STATE(1,"账户状态"),

    MEMBER_ACCOUNT(2,"会员账号"),

    USER_NAME(3,"会员姓名"),

    PHONE_NUM(4,"手机号"),

    GENDER(5,"性别"),

    BIRTHDAY(6,"生日"),

    GROWTH_VALUE(7,"当前成长值"),

    MEMBER_LEVEL(8,"会员等级"),

    MEMBER_CARD(9,"会员卡"),

    MEMBER_LABEL(10,"会员标签"),

    CONSUMPTION_TIME(11,"最近消费时间"),

    AMOUNT_OF_CONSUMPTION(12,"累计消费金额"),

    NUMBER_OF_CONSUMPTION(13,"累计消费次数"),

    CUMULATIVE_RECHARGE_AMOUNT(14,"累计充值金额"),

    CUMULATIVE_TOP_TIMES(15,"累计充值次数"),

    MEMBER_INTEGRAL(16,"当前积分"),

    LEI_MEMBER_INTEGRAL(17,"累计积分"),

    ACCOUNT_MONEY(21,"钱包余额"),

    CUMULATIVE_ACCOUNT_BALANCE(22,"累计账户余额"),

    GMT_CREATE(18,"注册时间"),

    SOURCE_TYPE(19,"注册渠道"),

    BELONG_STORE(20,"归属门店"),

    ACCOUNT_TYPE(21,"账号类型"),
    ;

    public String getDes() {
        return des;
    }

    public int getIndex() {
        return index;
    }


    private final String des;

    private final int index;

    MemberInfoListEnum(int index ,String des) {
        this.index = index;
        this.des = des;
    }

    public static List<String> getFieldList(List<String> fields) {
        for (MemberInfoListEnum amountSourceTypeEnum : MemberInfoListEnum.values()) {
            fields.add(amountSourceTypeEnum.getDes());
        }
        return fields;
    }

    public static int getIndexByField(String field) {
        for (MemberInfoListEnum amountSourceTypeEnum : MemberInfoListEnum.values()) {
            if (amountSourceTypeEnum.getDes().equals(field)){
                return amountSourceTypeEnum.getIndex();
            }
        }
        return -1;
    }
}
