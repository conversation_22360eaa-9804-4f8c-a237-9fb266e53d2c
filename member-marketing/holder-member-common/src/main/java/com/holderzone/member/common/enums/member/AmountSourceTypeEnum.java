package com.holderzone.member.common.enums.member;

import com.beust.jcommander.internal.Lists;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * author: pantao
 */
public enum AmountSourceTypeEnum {
    ADMIN_EDIT(0, "后台调整"),
    RECHARGE(1, "充值"),
    RETREAT_CARD(2, "消费退款"),
    CONSUMPTION(3, "消费"),
    BACK_IMPORT(4, "后台导入"),
    OPEN_CARD(5, "开卡"),
    GIVE(6, "赠送"),
    SUBSIDY_GRANT(7, "补贴发放"),
    SUBSIDY_EXPIRED(8, "补贴过期"),
    RETURN_CARD(9, "退卡"),
    PRESTORE_BALANCE(10, "预存余额"),

    RECHARGE_GIFT(12, "充值赠送"),
    RECHARGE_REFUND(21, "充值退款"),

    REDEEM_CODE(22, "兑换码"),

    /**
     * 零售云-POS
     */
    MARKET_POS(23, "零售云-POS"),

    CERTIFIED(25, "认证有礼"),
    ;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    private int code;

    private String des;

    AmountSourceTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public static String getDesByCode(int code) {
        for (AmountSourceTypeEnum amountSourceTypeEnum : AmountSourceTypeEnum.values()) {
            if (amountSourceTypeEnum.getCode() == code) {
                return amountSourceTypeEnum.getDes();
            }
        }
        return "";
    }

    public static AmountSourceTypeEnum getEnum(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (AmountSourceTypeEnum typeEnum : AmountSourceTypeEnum.values()) {
            if (typeEnum.code == code) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 需要初始化消息推送
     *
     * @return
     */
    public static List<AmountSourceTypeEnum> initShortMessageValues() {
        return new ArrayList<>(Lists.newArrayList(
                RECHARGE,
                ADMIN_EDIT,
                RETREAT_CARD,
                CONSUMPTION,
                SUBSIDY_GRANT,
                SUBSIDY_EXPIRED));
    }
}
