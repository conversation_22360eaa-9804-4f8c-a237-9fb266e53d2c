package com.holderzone.member.common.enums.member;

/**
 * <AUTHOR>
 */
public enum DataItemFormatEnum {
    /**
     * 文本
     */
    TEXT(0,"文本"),

    /**
     * 数字
     */
    NUMBER(1,"数字"),

    /**
     * 数字
     */
//    DATE(2,"日期"),

    /**
     * 省市区
     */
    PROVINCES_MUNICIPALITIES(3,"省市区"),

    /**
     * 性别
     */
//    SEX(4,"性别"),

    /**
     * 单选项
     */
    RADIO_OPTION(5,"单选项"),

    /**
     * 多选项
     */
    MULTIPLE_OPTIONS(6,"多选项"),

    /**
     * 图片
     */
    IMAGE_OPTION(7,"图片"),

    /**
     * 日期
     */
    DATE_ITEM_OPTION(8,"日期"),

//    /**
//     * 面部信息录入
//     */
//    FACIAL_INFORMATION_ENTRY(7,"面部信息录入"),
//
//    /**
//     * 富文本
//     */
//    RICH_TEXT(8,"富文本"),


    ;

    private final Integer code;

    private final String desc;

    DataItemFormatEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getCode() {
        return code;
    }

    /**
     * 根据匹配code的值获取desc
     *
     * @param code code
     * @return desc
     */
    public static String getDescByCode(Integer code){
        if(code == null) {
            return "";
        }
        for (DataItemFormatEnum s : DataItemFormatEnum.values()) {
            if(code.equals(s.getCode())){
                return s.getDesc();
            }
        }
        return "";
    }

    /**
     * 获取DataItemFormatEnum
     *
     * @param code code
     * @return DataItemFormatEnum
     */
    public static DataItemFormatEnum getDataItemFormatEnum(Integer code){
        if(code == null) {
            return null;
        }
        for (DataItemFormatEnum s : DataItemFormatEnum.values()) {
            if(code.equals(s.getCode())){
                return s;
            }
        }
        return null;
    }
}
