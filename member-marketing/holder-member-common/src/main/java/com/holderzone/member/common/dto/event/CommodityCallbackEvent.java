package com.holderzone.member.common.dto.event;


import com.holderzone.member.common.dto.commodity.callback.CommodityCallbackDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * crm 商品回调
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class CommodityCallbackEvent extends BaseEvent {

    private List<CommodityCallbackDTO> commodities;

    private List<Long> commodityIds;
}
