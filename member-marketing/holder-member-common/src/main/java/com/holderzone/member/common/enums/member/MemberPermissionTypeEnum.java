package com.holderzone.member.common.enums.member;


import java.util.Objects;

public enum MemberPermissionTypeEnum {

    /**
     * 会员管理
     */
    MEMBER_MARKET(1,"sr_member_marketing"),

    /**
     * 营销中心
     */
    MARKET_COUNT(2,"sr_marketing_count"),

    ;

    /**
     * 编号
     */
    private final int code;

    /**
     * 描述
     */
    private final String des;

    MemberPermissionTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    public static MemberPermissionTypeEnum getEnumByCode(Integer code){
        if (Objects.isNull(code)){
            return null;
        }
        for (MemberPermissionTypeEnum type : MemberPermissionTypeEnum.values()){
            if (type.code == code){
                return type;
            }
        }
        return null;
    }
}
