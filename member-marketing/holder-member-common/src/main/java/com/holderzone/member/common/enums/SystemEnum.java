package com.holderzone.member.common.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * header中表示系统类型：（营销中心用来适配新老系统等）
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SystemEnum {

    /**
     * 新会员: 食堂
     */
    SALE(0, "新会员"),
    /**
     * 老会员: 门店，餐饮云
     */
    REPAST(1, "餐饮云"),
    /**
     * 好搭档
     */
    PARTNER(2, "好搭档"),

    /**
     * 零售系统
     */
    RETAIL(3, "零售云"),

    /**
     * 私域商城
     */
    MALL(4, "私域商城"),

    ;

    private final int code;

    /**
     * 信息
     */
    private final String des;

    /**
     * 需要初始化运营主体的系统
     */
    public static final List<Integer> NEED_INIT_SYSTEM = Lists.newArrayList(SALE.code,RETAIL.code);

    public static SystemEnum transferByCode(int code) {
        SystemEnum[] systemEnums = SystemEnum.values();
        for (SystemEnum systemEnum : systemEnums) {
            if (systemEnum.getCode() == code) {
                return systemEnum;
            }
        }
        return SystemEnum.SALE;
    }

    private static final Map<String, Integer> NAME_CODE_MAP =
            Arrays.stream(values())
                    .collect(Collectors.toMap(
                            SystemEnum::name,
                            SystemEnum::getCode,
                            (oldVal, newVal) -> oldVal
                    ));

    public static int getSystemCodeBySystemName (String systemName) {
        return Optional.ofNullable(systemName)
                       .map(name -> NAME_CODE_MAP.get(name.toUpperCase()))
                       .orElse(0);
    }
}
