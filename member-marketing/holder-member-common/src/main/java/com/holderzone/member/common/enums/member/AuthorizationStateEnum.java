package com.holderzone.member.common.enums.member;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * 是否授权状态 微信小程序/支付宝小程序
 *
 * <AUTHOR>
 * 是否授权状态 1 已授权 0 未授权
 */
@Getter
@AllArgsConstructor
public enum AuthorizationStateEnum {

    AUTH(1, "已授权"),

    UN_AUTH(0, "未授权");


    /**
     * 通过code获取描述信息
     *
     * @param code 参数值
     * @return 信息描述
     */
    public static String getDesByCode(int code) {
        AuthorizationStateEnum[] businessMessages = AuthorizationStateEnum.values();
        for (AuthorizationStateEnum businessMessage : businessMessages) {
            if (businessMessage.getCode() == code) {
                return businessMessage.getDes();
            }
        }
        return "";
    }

    /**
     * 通过描述获取code值
     *
     * @param des 信息描述
     * @return 参数值
     */
    public int getCodeByDes(String des) {
        if (StringUtils.isEmpty(des)) {
            return -1;
        }
        AuthorizationStateEnum[] businessMessages = AuthorizationStateEnum.values();
        for (AuthorizationStateEnum businessMessage : businessMessages) {
            if (businessMessage.getDes().equals(des)) {
                return businessMessage.getCode();
            }
        }
        return -1;
    }

    private final int code;
    private final String des;
}
