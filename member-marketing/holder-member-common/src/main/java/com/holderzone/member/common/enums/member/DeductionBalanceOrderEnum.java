package com.holderzone.member.common.enums.member;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 余额扣款顺序枚举
 */
@Getter
@AllArgsConstructor
public enum DeductionBalanceOrderEnum {
    /**
     * 实充余额
     */
    REAL_BALANCE(1, "实充余额"),

    /**
     * 赠送余额
     */
    GIFT_BALANCE(2, "赠送余额"),

    /**
     * 补贴余额
     */
    SUBSIDY_BALANCE(3, "补贴余额"),
    ;

    private final Integer code;
    private final String desc;
}
