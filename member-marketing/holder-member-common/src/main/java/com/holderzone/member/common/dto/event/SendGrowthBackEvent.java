package com.holderzone.member.common.dto.event;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SendGrowthBackEvent
 * @date 2021/11/27 15:49
 * @description 成长值回收
 * @program holder-member-xxl-job
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SendGrowthBackEvent extends BaseEvent {

}
