package com.holderzone.member.common.enums.member;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 零售业务
 */
@Getter
public enum MarketConsumptionOrderTypeEnum {

    /**
     * 门店收银
     */
    STORE_ORDER(1, "零售云-门店收银", "零售云POS"),

    /**
     * 美团
     */
    MEI_TUAN_ORDER(2, "零售云-美团", ""),

    /**
     * 饿了么
     */
    ELE_ME_ORDER(3, "零售云-饿了么", ""),

    /**
     * 会员商城
     */
    MALL_ORDER(13, "会员商城", ""),

    /**
     * 会员商城
     */
    MALL_ORDER_EXPRESS(21, "私域商城-快递配送", ""),

    /**
     * 会员商城
     */
    MALL_ORDER_INTRA_CITY_DISTRIBUTION(22, "私域商城-同城配送", ""),

    /**
     * 到店自提
     */
    MALL_ORDER_SHOP_STORE(23, "私域商城-到店自提", ""),

    /**
     * 会员商城
     */
    MALL_ORDER_VIRTUAL(24, "私域商城-无需物流", ""),

    /**
     * 私域商城-无需物流
     */
    MALL_ORDER_PAID_MEMBER(25, "私域商城-付费会员", ""),
    /**
     * 私域商城-积分兑换
     */
    MALL_ORDER_INTEGRAL_EXCHANGE(26, "私域商城-积分兑换", ""),
    /**
     * 私域商城-
     */
    ORDER_PAID_MEMBER(50, "私域商城-付费会员卡", ""),

    /**
     * 餐饮云-正餐
     */
    DINNER_FOOD(51, "餐饮云-正餐", ""),
    /**
     * 餐饮云-快餐
     */
    FAST_FOOD(52, "餐饮云-快餐", ""),
    ;

    private final int code;

    private final String des;

    private final String channel;

    MarketConsumptionOrderTypeEnum(int code, String des, String channel) {
        this.code = code;
        this.des = des;
        this.channel = channel;
    }

    public static String getDesByCode(Integer code) {
        if (Objects.isNull(code)) {
            return "";
        }
        for (MarketConsumptionOrderTypeEnum consumptionOrderTypeEnum : MarketConsumptionOrderTypeEnum.values()) {
            if (consumptionOrderTypeEnum.getCode() == code) {
                return consumptionOrderTypeEnum.getDes();
            }
        }
        return "";
    }

    public static Integer getCodeByDes(String des) {
        if (Objects.isNull(des)) {
            return null;
        }
        for (MarketConsumptionOrderTypeEnum consumptionOrderTypeEnum : MarketConsumptionOrderTypeEnum.values()) {
            if (consumptionOrderTypeEnum.getDes().equals(des)) {
                return consumptionOrderTypeEnum.getCode();
            }
        }
        return null;
    }

    public static String getChanelByCode(Integer code) {
        if (Objects.isNull(code)) {
            return "";
        }
        for (MarketConsumptionOrderTypeEnum consumptionOrderTypeEnum : MarketConsumptionOrderTypeEnum.values()) {
            if (consumptionOrderTypeEnum.getCode() == code) {
                return consumptionOrderTypeEnum.getChannel();
            }
        }
        return "";
    }

    //返回codeList
    public static List<String> getCodeList() {
        List<String> codeList = new ArrayList<>();
        for (MarketConsumptionOrderTypeEnum consumptionOrderTypeEnum : MarketConsumptionOrderTypeEnum.values()) {
            codeList.add(String.valueOf(consumptionOrderTypeEnum.getCode()));
        }
        return codeList;
    }


}
