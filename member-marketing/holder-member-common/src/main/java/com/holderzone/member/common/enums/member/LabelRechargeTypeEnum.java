package com.holderzone.member.common.enums.member;


import java.util.Objects;

public enum LabelRechargeTypeEnum {

    /**
     * 累计充值金额
     */
    RECHARGE_AMOUNT(1,"累计充值金额"),

    /**
     * 累计充值次数
     */
    RECHARGE_NUM(2,"累计充值次数"),

    /**
     * 平均充值金额
     */
    AVG_RECHARGE_AMOUNT(3,"平均充值金额"),

    ;

    /**
     * 编号
     */
    private final int code;

    /**
     * 描述
     */
    private final String des;

    LabelRechargeTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    public static LabelRechargeTypeEnum getEnumByCode(Integer code){
        if (Objects.isNull(code)){
            return null;
        }
        for (LabelRechargeTypeEnum type : LabelRechargeTypeEnum.values()){
            if (type.code == code){
                return type;
            }
        }
        return null;
    }
}
