package com.holderzone.member.common.enums.member;

import java.util.Objects;

/**
 * 登录
 */
public enum LoginTypeEnum {

    /**
     * 登陆类型：1登录+注册
     */
    LOGON(1,"启用"),

    /**
     * 登陆类型：2仅登录
     */
    LOGIN (2,"禁用");

    private int code;

    private String des;


    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    LoginTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }


    public static String getDesByCode(Integer code){
        if (Objects.isNull(code)){
            return "";
        }
        for (LoginTypeEnum enableEnum : LoginTypeEnum.values()){
            if (enableEnum.getCode() == code){
                return enableEnum.des;
            }
        }
        return "";
    }

    public static LoginTypeEnum getEnumByCode(int code) {
        for (LoginTypeEnum enableEnum : LoginTypeEnum.values()) {
            if (enableEnum.getCode() == code) {
                return enableEnum;
            }
        }
        return LoginTypeEnum.LOGON;
    }


}
