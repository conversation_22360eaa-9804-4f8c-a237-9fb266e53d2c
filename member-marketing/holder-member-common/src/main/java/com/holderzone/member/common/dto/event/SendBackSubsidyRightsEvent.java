package com.holderzone.member.common.dto.event;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SendBackSubsidyRightsEvent
 * @date 2021/10/27 15:49
 * @description 补贴金回收
 * @program holder-member-xxl-job
 */
@Data
@Accessors(chain = true)
public class SendBackSubsidyRightsEvent extends BaseEvent {

    /**
     * 补贴明细记录guid
     */
    private List<String> subsidyActivityEventList;

    /**
     * 执行时间
     */
    private Date date;
}
