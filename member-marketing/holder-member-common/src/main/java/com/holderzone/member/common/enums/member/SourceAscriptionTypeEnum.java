package com.holderzone.member.common.enums.member;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * 会员来源归属类型
 *
 * <AUTHOR>
 * 会员来源归属类型 0门店，1品牌，2企业，3运营主体，4掌控者平台
 */
@Getter
@AllArgsConstructor
public enum SourceAscriptionTypeEnum {

    STORE(0, "门店"),

    BRAND(1, "品牌"),

    ENTERPRISE(2, "企业"),

    SUBJECT(3, "运营主体"),

    ZKZ(4, "掌控者平台")
    ;


    /**
     * 通过code获取描述信息
     *
     * @param code 参数值
     * @return 信息描述
     */
    public static String getDesByCode(int code) {
        SourceAscriptionTypeEnum[] businessMessages = SourceAscriptionTypeEnum.values();
        for (SourceAscriptionTypeEnum businessMessage : businessMessages) {
            if (businessMessage.getCode() == code) {
                return businessMessage.getDes();
            }
        }
        return "";
    }

    /**
     * 通过描述获取code值
     *
     * @param des 信息描述
     * @return 参数值
     */
    public int getCodeByDes(String des) {
        if (StringUtils.isEmpty(des)) {
            return -1;
        }
        SourceAscriptionTypeEnum[] businessMessages = SourceAscriptionTypeEnum.values();
        for (SourceAscriptionTypeEnum businessMessage : businessMessages) {
            if (businessMessage.getDes().equals(des)) {
                return businessMessage.getCode();
            }
        }
        return -1;
    }

    private final int code;
    private final String des;
}
