package com.holderzone.member.common.enums.member;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.holderzone.member.common.enums.PlatformEnum;
import com.holderzone.member.common.qo.member.DataItemSetQO;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @description: 会员后台--- 资料项初始化信息
 */
public enum DataItemEnum {

    /**
     * 头像
     */
    ICON("头像", 1, "图片", 1, 0),

    /**
     * 昵称
     */
    NICKNAME("姓名", 1, "文本", 2, 0),

    /**
     * 手机号
     */
    TEL("手机号", 1, "文本", 3, 0),

    /**
     * 修改登录密码
     */
    UPDATE_PASSWORD("修改账户密码", 1, "文本", 23, 1),


    /**
     * 姓名
     */
//    NAME("姓名", 0, "文本", 4, 0),

    /**
     * 性别
     */
    SEX("性别", 0, "单选项", 5, 0),

    /**
     * 生日
     */
    BIRTHDAY("生日", 0, "日期", 6, 0),

    /**
     * 亲属管理
     */
    KINSFOLK("亲属管理", 0, "姓名", 7, 0),

    /**
     * 人脸识别
     */
    RECOGNITION("人脸识别", 0, "面部信息录入", 8, 0),

    /**
     * 编号
     */
    NUMBER("编号", 0, "文本", 9, 1),

    /**
     * 邮箱
     */
    EMAIL("邮箱", 0, "文本", 10, 0),

    /**
     * 工作地区
     */
    WORK_AREA("工作地区", 0, "省市区", 11, 0),

    /**
     * 公司名称
     */
    COMPANY_NAME("公司名称", 0, "文本", 12, 0),

    /**
     * 公司地址
     */
    COMPANY_ADDRESS("公司地址", 0, "文本", 13, 0),

    /**
     * 所在行业
     */
    INDUSTRY("所在行业", 0, "行业", 14, 0),

    /**
     * 职位名称
     */
    JOB_TITLE("职位名称", 0, "文本", 15, 0),

    /**
     * 所属部门
     */
    DEPARTMENT("所属部门", 0, "文本", 16, 0),

    /**
     * 教育经历
     */
    EDUCATION("教育经历", 0, "学历、学校名称、专业、时间", 17, 0),

    /**
     * 个人简介
     */
    INTRODUCTION("个人简介", 0, "文本", 18, 0),

    /**
     * 个人经历
     */
    PERSONAL("个人经历", 0, "富文本", 19, 0),

    /**
     * 好搭档性别
     */
    PARTNER_SEX("性别", 0, "性别", 20, 0),

    /**
     * 好搭档姓名
     */
    PARTNER_NAME("姓名", 1, "文本", 21, 0),


    /**
     * 翼支付
     */
//    BEST_PAY("支付设置", 1, "翼支付刷脸免密授权", 22, 1),
    ;


    /**
     * 信息
     */
    private final String infoName;

    /**
     * 使用
     * 1：使用   0：不使用
     */
    private final int isUse;

    /**
     * 信息格式
     */
    private final String infoFormat;

    /**
     * 排序位置
     */
    private final int index;

    /**
     * 是否可编辑
     */
    private final int isUpdate;

    DataItemEnum(String infoName, int isUse, String infoFormat, int index, int isUpdate) {
        this.infoName = infoName;
        this.isUse = isUse;
        this.infoFormat = infoFormat;
        this.index = index;
        this.isUpdate = isUpdate;
    }

    public String getInfoName() {
        return infoName;
    }

    public int getIsUse() {
        return isUse;
    }

    public String getInfoFormat() {
        return infoFormat;
    }

    public int getIndex() {
        return index;
    }

    public int getIsUpdate() {
        return isUpdate;
    }

    /**
     * 获取初始化的资料项信息
     *
     * @return 初始化资料项信息
     */
    public static String getInitDataItem() {
        return getInitDataItem(null);
    }

    public static String getInitDataItem(String platform) {
        DataItemEnum[] dataItemEnums = {};
        if (platform == null) {
            dataItemEnums = new DataItemEnum[]{ICON, NICKNAME, TEL, SEX, BIRTHDAY, KINSFOLK, RECOGNITION, NUMBER, DEPARTMENT};
        }
        if (Objects.equals(platform, PlatformEnum.PARTNER.name())) {
            dataItemEnums = new DataItemEnum[]{ICON, PARTNER_NAME, TEL, PARTNER_SEX, BIRTHDAY,
                    EMAIL, WORK_AREA, COMPANY_NAME, COMPANY_ADDRESS, INDUSTRY, JOB_TITLE, DEPARTMENT, EDUCATION, INTRODUCTION, PERSONAL};
        }
        List<DataItemSetQO> dataItemSetList = new ArrayList<>();
        for (int i = 0; i < dataItemEnums.length; i++) {
            DataItemSetQO qo = new DataItemSetQO();
            qo.setInfoName(dataItemEnums[i].getInfoName());
            qo.setIsUse(dataItemEnums[i].getIsUse());
            qo.setIndex(i + 1);
            qo.setIsUpdate(dataItemEnums[i].getIsUpdate());
            qo.setType(0);
            List<String> infoFormats = Lists.newArrayList(dataItemEnums[i].getInfoFormat());
            qo.setInfoFormat(infoFormats);
            dataItemSetList.add(qo);
        }
        return JSON.toJSONString(dataItemSetList);
    }
}
