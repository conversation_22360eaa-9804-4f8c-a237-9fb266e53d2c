package com.holderzone.member.common.enums.member;

import java.util.Objects;

/**
 * @ProjectName: member-marketing
 * @ClassName: LabelTypeEnum
 * @Author: pantao
 * @Description: 会员标签枚举
 * @Date: 2021/8/24 11:23
 * @Version: 1.0
 */
public enum ConnectionLabelTypeEnum {

    /**
     * 已取关
     */
    MANUAL(0, "已取关"),

    /**
     * 关联中
     */
    AUTOMATION(1, "关联中");


    /**
     * 编号
     */
    private int code;

    /**
     * 描述
     */
    private String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    ConnectionLabelTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    /**
     * 根据标号获取描述
     *
     * @param code 编号
     * @return
     */
    public static String getDesByCode(Integer code) {
        if (Objects.isNull(code)) {
            return "";
        }
        for (ConnectionLabelTypeEnum labelTypeEnum : ConnectionLabelTypeEnum.values()) {
            if (labelTypeEnum.getCode() == code) {
                return labelTypeEnum.getDes();
            }
        }
        return "";
    }
}
