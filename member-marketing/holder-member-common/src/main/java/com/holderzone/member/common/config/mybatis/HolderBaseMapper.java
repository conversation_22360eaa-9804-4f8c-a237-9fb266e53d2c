package com.holderzone.member.common.config.mybatis;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;


/**
 * mapper 通过方法
 * @date 2020/05/09 14:38
 * @description 自定义mybatis-plus-BaseMapper
 */
public interface HolderBaseMapper<T> extends BaseMapper<T> {

    /**
     * 通过GUID查询
     *
     * @param guid guid
     * @return T
     */
    T queryByGuid(@Param("guid") String guid);

    /**
     * 通过多个GUID查询
     *
     * @param guids guids
     * @return List<T>
     */
    List<T> queryByGuids(@Param("guids") Collection<String> guids);

    /**
     * 根据GUID修改
     *
     * @param t update model
     * @return boolean
     */
    boolean updateByGuid(@Param("et") T t);

    /**
     * 根据GUID修改
     *
     * @param guids update model
     * @return boolean
     */
    boolean updateByGuids(@Param("guids") Collection<String> guids);

    /**
     * 根据GUID删除
     *
     * @param guid guid
     * @return boolean
     */
    boolean removeByGuid(@Param("guid") String guid);

    /**
     * 根据多个GUID删除
     *
     * @param guids guids
     * @return boolean
     */
    boolean removeByGuids(@Param("guids") Collection<String> guids);
}
