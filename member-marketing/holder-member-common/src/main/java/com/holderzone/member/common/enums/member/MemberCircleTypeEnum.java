package com.holderzone.member.common.enums.member;

/**
 * <AUTHOR>
 */

public enum MemberCircleTypeEnum {
    ALUMNI_CIRCLE(1,"校友圈"),

    INDUSTRY_CIRCLE(2,"行业圈"),

    ACTIVITY_CIRCLE(3,"活动圈"),

    ;

    private int type;

    private String desc;

    MemberCircleTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static MemberCircleTypeEnum getMemberCircle(Integer type) {
        if(type == null){
            return null;
        }
        for(MemberCircleTypeEnum item : MemberCircleTypeEnum.values()){
            if(type == item.type){
                return item;
            }
        }
        return null;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
