package com.holderzone.member.common.enums.member;

import lombok.Getter;

import java.util.Objects;

/**
 * 会员日常记录类型枚举
 */
@Getter
public enum MemberEverydayTypeEnum {

    /**
     * 注册
     */
    REGISTER(0, "注册"),

    /**
     * 完善个人信息
     */
    PERFECT_PERSONAL_DETAILS(1, "完善个人信息"),

    /**
     * 任务分享
     */
    SHARE(8, "分享"),

    /**
     * 登录
     */
    LOGIN(9, "登录"),

    /**
     * 签到
     */
    SIGN_IN(10, "签到"),
    ;

    /**
     * 编号
     */
    private final int code;

    /**
     * 描述
     */
    private final String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    MemberEverydayTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public static String getNameByCode(Integer code) {
        if (Objects.isNull(code)) {
            return "";
        }
        for (MemberEverydayTypeEnum type : MemberEverydayTypeEnum.values()) {
            if (type.code == code) {
                return type.getDes();
            }
        }
        return "";
    }

    public static MemberEverydayTypeEnum getEnum(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (MemberEverydayTypeEnum type : MemberEverydayTypeEnum.values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static MemberEverydayTypeEnum getEnumByCode(Integer code){
        if (Objects.isNull(code)) {
            return null;
        }
        MemberEverydayTypeEnum[] values = MemberEverydayTypeEnum.values();
        for (MemberEverydayTypeEnum value : values) {
            // code值不能超过127
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
