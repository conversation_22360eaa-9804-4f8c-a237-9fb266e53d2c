package com.holderzone.member.common.util.verify;

import jodd.util.StringUtil;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 检查对象属性值
 */
public class ObjectUtil {

    private ObjectUtil() {
    }

    /**
     * 检测对象，转相应类型
     *
     * @param obj 对象属性
     * @return 相应类型
     */
    public static String objToString(Object obj) {
        return (obj == null) ? "" : obj.toString();
    }

    public static long objToLong(Object obj) {
        return (null == obj || "".equals(obj)) ? 0 : toLong(obj);
    }

    public static double objToDouble(Object obj) {
        return (null == obj) ? 0 : toDouble(obj);
    }

    public static int objToInt(Object obj) {
        return (null == obj) ? 0 : toInt(obj);
    }

    /**
     * 对象转相应类型
     *
     * @param obj 对象属性
     * @return 相应类型
     */
    public static long toLong(Object obj) {
        return Long.parseLong(obj.toString());
    }

    public static int toInt(Object obj) {
        return Integer.parseInt(obj.toString());
    }

    public static double toDouble(Object obj) {
        return Double.parseDouble(obj.toString());
    }


    /**
     * 参数判断
     *
     * @param objects 参数集
     * @return 判断
     */
    public static boolean checkParam(Object... objects) {
        for (Object object : objects) {
            if (null == object || object.toString().trim().length() == 0) {
                return true;
            }
        }
        return false;
    }

    public static String strValue(String value) {
        String[] strs = value.split("/");
        StringBuilder sb = new StringBuilder();
        for (String str : strs) {
            sb.append(str).append("/");
        }
        return strs.length > 0 ? sb.deleteCharAt(sb.length() - 1).toString() : "";
    }

    /**
     * 判断基本数据是否为null或者0
     *
     * @param val 值
     * @return 判断结果
     */

    public static boolean isEmpty(String val) {
        return StringUtil.isEmpty(val);
    }

    public static boolean isEmpty(Long val) {
        return null == val || val.equals(0L);
    }

    public static boolean isEmpty(Integer val) {
        return null == val || val.equals(0);
    }

    /**
     * 判断对象是否为空
     *
     * @param val 对象
     * @return 判断结果
     */
    public static boolean isEmpty(Object val) {
        return null == val;
    }

    /**
     * 判断集合是否无值
     *
     * @param coll 集合
     * @return 判断结果
     */
    public static <T> boolean isEmpty(Collection<T> coll) {
        return coll == null || coll.isEmpty();
    }

    public static <T> boolean isNotEmpty(Collection<T> coll) {
        return !isEmpty(coll);
    }

    public static boolean isEmpty(Map map) {
        return map == null || map.isEmpty();
    }

    public static boolean isNotEmpty(Map map) {
        return !isEmpty(map);
    }

    /**
     * 判断数组是否无值
     *
     * @param array 数组
     * @return 判断结果
     */
    public static boolean isEmpty(Object[] array) {
        return array == null || array.length == 0;
    }

    public static boolean isEmpty(long[] array) {
        return array == null || array.length == 0;
    }

    public static boolean isEmpty(int[] array) {
        return array == null || array.length == 0;
    }

    public static boolean isEmpty(short[] array) {
        return array == null || array.length == 0;
    }

    public static boolean isEmpty(char[] array) {
        return array == null || array.length == 0;
    }

    public static boolean isEmpty(byte[] array) {
        return array == null || array.length == 0;
    }

    public static boolean isEmpty(double[] array) {
        return array == null || array.length == 0;
    }

    public static boolean isEmpty(float[] array) {
        return array == null || array.length == 0;
    }

    public static boolean isEmpty(boolean[] array) {
        return array == null || array.length == 0;
    }

    public static <T> boolean isNotEmpty(T[] array) {
        return array != null && array.length != 0;
    }

    public static boolean isNotEmpty(long[] array) {
        return array != null && array.length != 0;
    }

    public static boolean isNotEmpty(int[] array) {
        return array != null && array.length != 0;
    }

    public static boolean isNotEmpty(short[] array) {
        return array != null && array.length != 0;
    }

    public static boolean isNotEmpty(char[] array) {
        return array != null && array.length != 0;
    }

    public static boolean isNotEmpty(byte[] array) {
        return array != null && array.length != 0;
    }

    public static boolean isNotEmpty(double[] array) {
        return array != null && array.length != 0;
    }

    public static boolean isNotEmpty(float[] array) {
        return array != null && array.length != 0;
    }

    public static boolean isNotEmpty(boolean[] array) {
        return array != null && array.length != 0;
    }

    /**
     * 获取百分率
     *
     * @param obj1 除数
     * @param obj2 被除数
     * @param code 保留几位小数
     * @param way  1显示用，2计算用（如33/100，前者返回33，后者返回0.33）
     * @return 百分率
     */
    public static double getPercentPoint(Object obj1, Object obj2, int code, int way) {
        if (judgeType(obj1, obj2) && code >= 0) {
            if (1 == way) {
                return Math.round(Double.parseDouble(obj1.toString()) / Double.parseDouble(obj2.toString()) *
                        Math.pow(10, code + 2.0)) / Math.pow(10, code);
            }
            return Math.round(Double.parseDouble(obj1.toString()) / Double.parseDouble(obj2.toString()) *
                    Math.pow(10, code)) / Math.pow(10, code);
        }
        return 0;
    }

    /**
     * 判断两个对象是否为基本数据类型
     *
     * @param obj1 对象1
     * @param obj2 对象2
     * @return 正确与否
     */
    private static boolean judgeType(Object obj1, Object obj2) {
        if (null != obj1 && null != obj2) {
            return (!(obj1 instanceof Boolean) && !(obj2 instanceof Boolean) && !(obj1 instanceof String) &&
                    !(obj2 instanceof String) && !"0".equals(obj2.toString()));
        }
        return false;
    }

    /**
     * 数组去除重复再合并
     *
     * @param oldList 合并前/合并后
     * @param newList 要合并的数据
     */
    public static <T> void joinDistinct(List<T> oldList, List<T> newList) {
        //去除重复的
        oldList.removeAll(newList);
        //再合并
        oldList.addAll(newList);
    }

    /**
     * 集合转数组
     *
     * @param idList
     * @return
     */
    public static Long[] toArray(List<Long> idList) {
        return isNotEmpty(idList) ? idList.toArray(new Long[0]) : new Long[0];
    }

    /**
     * 数组转集合
     *
     * @param array
     * @return
     */
    public static List<Long> toArrayList(Long[] array) {
        return isNotEmpty(array) ? Stream.of(array).collect(Collectors.toList()) : Collections.emptyList();
    }

    /**
     * List集合去重
     *
     * @param list 集合
     * @return 去重后的集合
     */
    public static <T> List<T> listDistinct(List<T> list) {
        return isNotEmpty(list) ? list.stream().distinct().collect(Collectors.toList()) : list;
    }

    /**
     * 比较两个类型相同的是否相等
     *
     * @param one 第一个
     * @param two 第二个
     * @return 是否
     */
    public static boolean compare(Object one, Object two) {
        Optional<Object> oneObj = Optional.ofNullable(one);
        Optional<Object> twoObj = Optional.ofNullable(two);
        return oneObj.equals(twoObj);
    }

    /**
     * 关键字查询【多符号传入】
     *
     * @param strs #,1,# 适用于组织、任务链
     * @return
     */
    public static String sqlLike(String... strs) {
        if (strs != null && strs.length > 0) {
            StringBuilder sb = new StringBuilder();
            sb.append("%");
            for (String str : strs) {
                sb.append(str.trim().replace("%", "\\%"));
            }
            sb.append("%");
            return sb.toString();
        }
        return "%";
    }
}
