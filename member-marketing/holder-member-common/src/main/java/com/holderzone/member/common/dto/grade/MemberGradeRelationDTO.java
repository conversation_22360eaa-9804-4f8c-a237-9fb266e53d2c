package com.holderzone.member.common.dto.grade;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/4 下午2:40
 * @description 会员和等级关联实体
 */
@Data
public class MemberGradeRelationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "会员GUID")
    private List<String> memberGuidList;;

    @ApiModelProperty(value = "会员等级GUID")
    private String memberInfoGradeGuid;

    @ApiModelProperty(value = "角色类型")
    private String roleType;

    /**
     * 有效期数量
     */
    private Integer num;

    /**
     * 有效期单位：3月 4年
     */
    private Integer unit;

    private Integer sourceType;

    private String operSubjectGuid;
}