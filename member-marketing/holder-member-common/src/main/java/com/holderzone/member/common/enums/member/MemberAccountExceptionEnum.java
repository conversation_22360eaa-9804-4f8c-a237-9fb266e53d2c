package com.holderzone.member.common.enums.member;


import com.holderzone.member.common.enums.ResponseBase;

/**
 * @version 1.0
 * <AUTHOR>
 * @className MemberMerchantExceptionEnum
 * @date 2019/05/16 15:21
 * @description 会员异常枚举类
 * @program holder-member-merchant
 */
public enum MemberAccountExceptionEnum implements ResponseBase {
    /**
     * 异常提示信息
     */
//    业务异常枚举定义在业务中抛出异常时使用(100001, "业务异常枚举定义，在业务中抛出异常时使用"),

    ERROR_METHOD_ARGUMENT_NOT_VALID(100002, "ERROR_METHOD_ARGUMENT_NOT_VALID"),
    ERROR_ILLEGAL_ARGUMENT(100003, "ILLEGAL_ARGUMENT"),
    ERROR_NOT_FOUND(100004, "NOT_FOUND"),
    ERROR_HYSTRIX_BAD_REQUEST(100005, "HYSTRIX_BAD_REQUEST"),
    ERROR_HYSTRIX_RUNTIME(100006, "HYSTRIX_RUNTIME"),
    ERROR_FEIGN(100007, "FEIGN"),
    ERROR_EXCEPTION(100008, "EXCEPTION"),
    ERROR_VERIFY(100009, "参数错误"),
    ERROR_HEADER_EMPTY(100010, "头部信息不能为空！"),
    ERROR_HEADER_VALID(100011, "头部信息解析错误！"),
    ERROR_HEADER_OPERATION_GUID(100012, "运营主体不能为空！"),
    ERROR_HEADER_OPERATION_STATUS(100013, "会员体系已被禁用,如有疑问请联系客服人员！"),
    ERROR_NO_OPERATION(100014, "运营主体不存在！"),
    ERROR_TIMEOUT(100015, "超时请重试！"),
    ERROR_ENTERPRISE_IS_NULL(100016, "企业guid不能为空"),

    ERROR_NOT_EXISTS_DATA(100017,"数据不存在"),
    /**
     * member model
     */
    ERROR_EXIST_MAIN_OPERSUBJECT(110001, "联盟中已存在此手机号码"),
    ERROR_EXIST_OPERSUBJECT(110002, "会员账户已存在"),
    ERROR_NOT_EXIST_MEMBER(110003, "账户不存在"),
    //ERROR_MEMBER_DISABLE(110004, "顾客被禁用"),//统一用这个 ERROR_ACCOUNT_IS_DISABLED
    ERROR_MEMBER_HAS_MAIN_CARD(110005, "会员已开通主卡"),
    ERROR_MEMBER_GET_UPLOAD_EXCEL(110006, "获取上传文件失败"),
    ERROR_MEMBER_EMPTY_UPLOAD_EXCEL(110007, "导入文件为空或格式不正确"),
    ERROR_MEMBER_MAX_UPLOAD_EXCEL(110008, "导入数量超过5000条,请小于等于5000条数据!!!"),
    ERROR_MEMBER_PHONE(110009, "手机号码格式错误"),
    ERROR_MEMBER_EXIST_EMAIL(110010, "邮箱已存在"),
    ERROR_MEMBER_CARD_NOT_EXIST(110011, "会员卡不存在"),
    ERROR_CARD_LEVEL_NOT_EXIST(110012, "模板卡等级不存在"),
    ERROR_MEMBER_EQUITY_CARD_NOT_EXIST(110013, "会员权益卡不存在"),
    ERROR_EQUITY_CARD_NOT_EXIST(110014, "未设置权益卡模板"),
    ERROR_FILE_UPLOAD(110015, "文件上传失败"),
    ERROR_MEMBER_EMPTY_GROWTH(110016, "会员成长值不足"),
    ERROR_MEMBER_DEL_AUTO_LABEL(110017, "不允许删除会员自动标签"),
    ERROR_EQUITY_CARD_SPECIFICATIONS(110018, "未配置权益卡规格"),
    ERROR_CHANNEL_OPEN_EQUITY_CARD(110019, "卡规则配置不允许此通道开通权益卡"),
    ERROR_NUMBER_CARDS_ISSUED(110020, "已达到发卡数量限制"),
    ERROR_NUMBER_EQUITY_CARDS_EXIST(110021, "会员已存在此卡"),
    ERROR_CHANNEL_OPEN_CARD(110022, "卡规则配置不允许此通道开通此卡"),
    THIS_ID_ALREADY_EXISTS(110023, "证件号码已存在"),
    CERTIFICATE_NOT_NAME(110024, "自定义证件未命名"),
    ERROR_FILE(110025, "文件格式必须为 xls/xlsx/XLS/XLSX 格式！！！"),
    ERROR_NOT_TO_RECEIVE(110025, "当前会员卡并未领取"),
    ERROR_CARD_PHONE_REPETITION(110026, "手机号已存在此会员卡，不可重复绑定"),
    ERROR_USER_NOT_REGISTERED(110027, "用户未注册"),
    ERROR_MEMBER_MAX_EXPORT_EXCEL(110028, "导出数量超过20000条,不能导出"),


    ERROR_CARD_PLEASE_SELECT_AGAIN(110029, "会员卡不支持生成实体卡，请重新选择 "),
    ERROR_ACCOUNT_IS_DISABLED(110030, "会员账户已被禁用，请联系管理员"),
    ERROR_SUBSIDY_MAX_EXPORT_EXCEL(110031, "导出数据不可超过20000条"),
    ERROR_CARD_CEILING_RE_ELECTION(110032, "当前会员卡已达到今日开卡上限，请选择其他会员卡"),
    ERROR_CARD_NOT_BOUND_MEMBER(110033, "会员卡未绑定账户，请先绑定再登录"),
    ERROR_GROWTH_VALUE_EXPORT(110034, "导出成长值任务完成明细记录表异常"),
    ERROR_GROWTH_VALUE_LIST(110035, "成长值任务完成明细记录表异常"),
    ERROR_EXPORT_WAIT(110036, "正在导出数据请稍等"),
    ERROR_EXPORT_INPUT(110037, "请输入正确的手机号/卡号"),

    ERROR_MEMBER_MAX_EXPORT_EXCEL_5000(110038, "导出数量超过5000条,不能导出"),
    GIFT_CARD_NOT_EXIST(110038, "赠送会员卡不存在"),

    ERROR_NOT_MEMBER(110039, "账户不存在/未注册"),

    ERROR_MEMBER_NOT_REGISTER(110040, "您还未注册，请联系管理员"),


    ERROR_NOT_MEMBER_LOGIN(110041, "账户未注册，请联系管理员"),

    ERROR_PASSWORD(110042, "密码错误，请确认"),

    /**
     * 验证码错误
     */
    ERROR_VERIFICATION_CODE(110043, "短信验证码不正确"),

    /**
     * 密码长度需为6位
     */
    ERROR_PASSWORD_LENGTH(110044, "密码长度需为6位"),

    /**
     * 手机号未注册
     */
    ERROR_PHONE_NOT_REGISTER(110045, "手机号未注册"),


    ERROR_CONTENT_REQUEST(100046,"输入内容不合法"),

    /**
     * 企微图片格式错误
     * 图片（image）：10MB，支持JPG,PNG格式
     *
     */
    ERROR_ENTERPRISE_WECHAT_IMAGE_TYPE(110047, "图片支持JPG,PNG格式"),
    ERROR_ENTERPRISE_WECHAT_IMAGE_SIZE(110047, "图片不能超过10MB"),

    /**
     * 企微语音格式错误
     * 语音（voice） ：2MB，播放长度不超过60s，仅支持AMR格式
     */
    ERROR_ENTERPRISE_WECHAT_VOICE_TYPE(110048, "语音仅支持AMR格式"),
    ERROR_ENTERPRISE_WECHAT_VOICE_SIZE(110047, "语音不能超过2MB"),

    /**
     * 企微视频格式错误
     * 视频（video） ：10MB，支持MP4格式
     */
    ERROR_ENTERPRISE_WECHAT_VIDEO_TYPE(110049, "视频支持MP4格式"),
    ERROR_ENTERPRISE_WECHAT_VIDEO_SIZE(110047, "视频不能超过10MB"),

    /**
     * 企微普通文件格式错误
     * 普通文件（file）：20MB
     */
    ERROR_ENTERPRISE_WECHAT_FILE_TYPE(110050, "普通文件"),
    ERROR_ENTERPRISE_WECHAT_FILE_SIZE(110047, "普通文件不能超过20MB"),


    /**
     * label model
     */
    ERROR_NOT_LABEL(120001, "标签不存在"),
    ERROR_BAD_LABEL(120005, "请传入正确的标签名"),
    DUPLICATE_LABEL(120004, "重复标签"),
    ERROR_DATA_REQUEST(120003, "请求数据错误"),
    ERROR_REFRESH_LABEL(120002, "30分钟内只能刷新一次"),

    /**
     * card model
     */
    ERROR_NOT_MAIN_CARD(130001, "请配置主卡信息"),
    ERROR_NOT_MAIN_CARD_LEVEL(130002, "请配置主卡等级信息"),
    ERROR_NOT_MAIN_CARD_ALLIANCE(130003, "该运营主体或联盟下没有此卡配置"),
    ERROR_NOT_NULL_SEND_STATUS(130004, "参数错误：发卡状态不能为空"),
    ERROR_NOT_NULL_CARD_INFO(130005, "权益规则信息为空"),
    ERROR_NOT_NULL_CARD_STATUS(130006, "参数错误：卡状态不能为空"),
    ERROR_NOT_NULL_CARD_DETAIL(130007, "参数错误：查询权益卡详情参数错误"),
    ERROR_NOT_NULL_CARD_NAME(130008, "会员卡名称不能为空"),
    ERROR_NOT_FIND_CARD(130009, "实体卡绑定码错误"),
    ERROR_NOT_NULL_STORE_CARD(130010, "适用门店列表不能为空"),
    ERROR_HAS_EXISTS_MAIN_CARD(130011, "该主体下已存在会员卡，无法新增会员卡"),
    ERROR_NOT_FIND_CARD_LEVEL(130012, "该会员卡等级不存在"),
    ERROR_NOT_DEL_EXISTS_MEMBER(130013, "该等级已有会员存在，无法删除"),
    ERROR_NOT_DEL_DEFAULT(130014, "默认等级无法删除"),
    ERROR_LEVEL_TARGET_MUST_BIGGER(130015, "等级的目标成长值和保级成长值必须大于上一级"),
    ERROR_LEVEL_TARGET_MUST_SMALLER(130016, "等级的目标成长值和保级成长值必须小于下一级"),
    ERROR_NOT_NULL_CARD_GUID(130017, "会员卡guid不能为空"),
    ERROR_NOT_FIND_CARD_RECHARGE_RULE(130018, "卡充值规则不存在"),
    ERROR_NOT_NULL_CARD_RECHARGE_ACTIVITY_GUID(130019, "充值活动guid不能为空"),
    ERROR_NOT_FIND_CARD_RECHARGE_ACTIVITY(130020, "充值活动不存在"),
    ERROR_CANT_PUBLISH_CARD_RECHARGE_ACTIVITY(130021, "当前活动状态不可发布"),
    ERROR_CANT_DISABLE_CARD_RECHARGE_ACTIVITY(130022, "当前活动状态不可禁用"),
    ERROR_CANT_DELETE_CARD_RECHARGE_ACTIVITY(130023, "当前活动状态不可删除"),
    ERROR_LEVEL_REPEATED_NAME(130024, "会员等级名称不能重复！"),
    ERROR_ACTIVITY_REPEATED_NAME(130025, "活动名称不能重复"),
    ERROR_CARD_IMAGE_TYPE(130026, "图片必须为jpeg/png/jpg/webp/gif格式"),
    ERROR_CARD_IMAGE_SIZE(130027, "文件大小不能超过5M"),
    ERROR_LENGTH_OPER_SUBJECT_NAME(130028, "运营主体名称长度不能超过50个字"),
    ERROR_LENGTH_CARD_NAME(130029, "会员卡名称长度不能超过50个字"),
    ERROR_EQUITY_CARD_EXIST(130030, "权益卡已存在"),
    ERROR_INTEGRAL_TO_LARGE(130031, "调整积分大于卡剩余积分，请重新选择"),
    ERROR_BALANCE_RECHARGE_TO_LARGE(130032, "调整余额大于卡剩余余额，请重新选择"),
    ERROR_BALANCE_GIFT_TO_LARGE(130033, "调整余额大于卡剩余卡余额，请重新选择"),
    ERROR_EXIST_CARD_NAME(130034, "会员卡名称在其他运营主体中已存在！"),
    ERROR_ACTIVITY_EXIST_CARD_LEVEL(130035, "会员卡一个等级只能存在一个充值活动！"),
    ERROR_ACTIVITY_EXIST_CARD(130036, "同一个权益卡只能存在一个充值活动！！"),
    ERROR_CARD_NAME(130037, "会员卡名称已存在，请修改！"),
    ERROR_NOT_FIND_CARD_RULE(130038, "开卡规则不存在"),
    ERROR_REFUND_LESS_THAN_DEPOSIT(130037, "可退金额需≤押金金额"),
    ERROR_MIN_LESS_THAN_DEPOSIT(130037, "单次最少充值金额＜单次最多充值金额"),
    ERROR_DEPOSIT_NAME_EXISTS(130038, "押金策略名称已存在"),
    ERROR_AMOUNT_INSUFFICIENT_BALANCE_NOTIFICATION(130039, "开启余额不足通知,那么必须输入金额：0≤金额≤999999.99"),
    ERROR_TYPE_INSUFFICIENT_BALANCE_NOTIFICATION(130039, "开启余额不足通知,那么至少选择一个通知方式：短信通知、微信公众号通知、小程序通知"),
    ERROR_CARD_INSUFFICIENT_BALANCE_NOTIFICATION(130039, "开启卡支付密码,那么小程序端校验状态不能为空"),
    ERROR_NOT_OPEN_CARD(130040, "请开启电子卡/实体卡"),
    ERROR_NOT_CARD_BALANCE(130041, "扣减金额需小于会员卡余额，请重新设置"),
    CARD_TIME_TYPE_DOES_NOT_EXIST(130042, "会员卡有效时间类型出错，请检查有效时间类型"),
    ERROR_NOT_MATE_CARD(130043, "实体卡与电子卡不匹配，请重新绑定"),
    ERROR_BINDING_CARD(130044, "卡已存在绑定账户"),
    ERROR_FREEZE_CARD(130045, "卡已禁用/卡已冻结/卡已过期"),
    ERROR_CARD_NUM(130046, "卡号错误"),
    ERROR_NOT_NULL_MEMBER_GUID(130047, "会员guid不能为空"),
    ERROR_CARD_MUSIC_TYPE(130048, "音乐必须为MP3格式"),
    ERROR_CARD_MUSIC_SIZE(130049, "文件大小不能超过10M"),
    ERROR_OPEN_CARD_AMOUNT(130050, "开卡方式,金额输入框限制：0<999999.99"),
    ERROR_CARD_NOT_APPLY(130051, "会员卡不适用该门店"),
    ERROR_CARD_NOT_OPEN(130052, "会员卡未激活"),
    ERROR_CARD_FREEZE(130053, "会员卡已冻结"),
    ERROR_CARD_OUT(130054, "会员卡已过期"),
    ERROR_CARD_NOT_RETURN(130055, "当前会员卡不可退"),
    ERROR_OPEN_PHYSICAL_CARD_CHANNEL(130056, "当前渠道不支持开卡"),
    ERROR_CARD_REPEAT(130056, "您已有相同类型卡，不可重复绑定"),
    ERROR_DISABLE_CARD(130057, "卡已禁用"),
    ERROR_FROZEN_CARD(130058, "卡已冻结"),
    ERROR_EXPIRE_CARD(130059, "卡已过期"),
    ERROR_CARD_FREEZE_NEW(130060, "会员卡已冻结,请联系管理员"),
    ERROR_CARD_NOT_OPEN_NEW(130061, "会员卡未激活,请先激活使用"),

    /**
     * activity model
     */
    ERROR_ACTIVITY_CONTENT_LENGTH(140001, "活动内容只能为0-2000个字符"),
    ERROR_ACTIVITY_TITLE_LENGTH(140002, "活动标题最多20个字符"),
    ERROR_ACTIVITY_CARD_EMPTY(140003, "选择会员,会员卡不能为空"),
    ERROR_ACTIVITY_TERMINAL_EMPTY(140004, "参与终端对象类型不能为空"),
    ERROR_ACTIVITY_TIME(140005, "活动时间错误"),
    ERROR_ACTIVITY_BEGIN_TIME(140006, "活动开始时间不能小于当前时间"),
    ERROR_ACTIVITY_END_TIME(140007, "活动结束时间不能小于当前时间"),
    ERROR_ACTIVITY_STORE_EMPTY(140008, "活动门店不能为空"),
    ERROR_ACTIVITY_PRODUCT_EMPTY(140009, "至少选择一个商品"),
    ERROR_ACTIVITY_RECHARGE_STORE_ONLY(140010, "一个门店下只能有一个充值活动"),
    ERROR_ACTIVITY_NOT_EXIST(140011, "活动不存在"),
    ERROR_ACTIVITY_NOT_DELETE(140012, "进行中的活动不能修改"),
    ERROR_ACTIVITY_RECHARGE_EMPTY(140013, "充值活动信息不能为空"),
    ERROR_ACTIVITY_RECHARGE_REPEAT(140014, "不能有同样金额档次的充值活动"),
    ERROR_ACTIVITY_TITLE_REPEAT(140015, "活动名称不能重复"),

    /**
     * 小程序
     */
    ERROR_APP_FUNDING_QUERY(150000, "资金来往明细查询异常"),
    ERROR_APP_ADJUST_ERROR(150001, "小程序商户立刻调整操作错误"),
    ERROR_NOT_DATA(150002, "数据为空，不能导出"),
    ERROR_NOT_AUTH(40029, "授权调用失败"),
    ERROR_COMMODITY_GET_STORE_ERROR(40030, "通过商品获取门店信息报错:{}"),
    ERROR_KINSFOLK_COUNT_ERROR(40031, "亲属管理最多添加20个"),
    ERROR_FACE_ENTRY_ERROR(40032, "百度云人脸录入失败"),

    /**
     * order model
     */
    ERROR_MEMBER_PASSWORD_EMPTY(150003, "支付密码不能为空"),
    ERROR_MEMBER_PASSWORD_INCORRECT(150004, "支付密码不正确"),
    ERROR_INTEGRAL_DEDUCTION_DISABLE(150005, "未启用积分抵扣"),

    /**
     * 成长值
     */
    BASE_TASK_MORE_THAN_ONE(200000, "基础任务充值任务，相同运营主体只能有一个相同的活动"),
    TASK_NOT_EXIST(200001, "成长值任务不存在"),
    NAME_MORE_THAN_ONE(200002, "成长值任务名称重复"),
    TASK_ACTION_MORE_THAN_ONE(200003, "存在相同动作的任务正在进行中，当前任务不可启动"),
    ERROR_END(999999, "END"),
    MEMBER_GROWTH_EXPORT_ERROR(160001, "会员账户导出成长值失败"),
    RESOURCE_NOT_EXIST(200004, "查询的信息不存在"),
    RESOURCE_UPDATE_ERROR(200005, "修改的信息不存在"),
    ROLL_EQUITIES_GUID_IS_NULL(200006, "回退翻倍成长值或积分，等级权益guid为空"),
    ROLL_MEMBER_GUID_IS_NULL(200007, "回退翻倍成长值或积分，会员guid为空"),


    /**
     * 权益中心
     */
    EQUITIES_REQUEST_ERROR(210001, "查询权益基础权益信息失败"),

    /**
     * 通过guid未查询到权益信息
     */
    EQUITIES_REQUEST_BASE_ERROR(210002, "当前企业下未查询到该权益信息"),

    /**
     * 权益已调用不能删除
     */
    EQUITIES_DELETE_ERROR(210003, "当前权益已调用，不能删除"),

    ORDER_REFUNDED(220000,"该订单已退款"),

    ORDER_REFUNDED_REPEAT(220001,"正在退款中，请勿重复操作"),

    //TODO 会员等级
    MEMBER_GRADE_NOT_EXIST(300000, "会员等级不存在"),
    MEMBER_GRADE_MORE_THAN_NINE(300001, "会员等级已达到9，不能新增"),
    GROWTH_VALUE_LESS_THAN(300002, "升级成长值需大于上一级成长值"),
    GROWTH_VALUE_MORE_THAN(300003, "升级成长值需小于下一级成长值"),
    MEMBER_GRADE_RECEIVE_BAG(300004, "该等级升级礼包已领取"),
    MEMBER_GRADE_BAG_NOT_EXIST(300005, "会员等级升级礼包不存在"),
    MEMBER_GRADE_NAME_REPETITION(300006, "会员等级名称重复"),
    CURRENT_MEMBER_NOT_GRADE(300007, "当前会员没有等级"),
    INITIALIZE_SUBJECT_DATA_ERROR(300008, "初始化运营主体数据错误"),
    REPETITION_EDIT(300009, "存在重复编辑，请重新操作"),
    QUERY_COMMODITY_ERROR(300010, "查询商品失败"),
    CAN_NOT_DELETE(300011, "vip1等级不能删除"),

    PAY_GRADE_NOT_DELETE(300012, "当前等级下存在人员，不可删除！"),
    PAY_GRADE_RULE_DUPLICATE(300013, "请检查是否有重复设置付费会员有效时长"),
    MEMBER_GRADE_PURCHASE_RECORD_NOT_EXIST(300014, "会员等级购买流水不存在"),

    //TODO 挂账账户
    CREDIT_ACCOUNT_NOT_EXIST(400001, "挂账账户不存在"),
    CREDIT_ACCOUNT_NAME_REPETITION(400002, "挂账账户名称重复"),
    PHONE_ERROR(400003, "联系人手机号格式错误"),
    AMOUNT_OF_OVERFLOW_ERROR(400004, "设置的单人可挂账金额上限总和，不能大于限制上限的金额"),
    CLEARING_STATEMENT_NOT_EXIST(400005, "结算单不存在"),
    CREDIT_ACCOUNT_NOT_PERMISSION(400006, "当前账户没有确认权限"),
    CLEARING_STATEMENT_CHANGE(400007, "结算单状态或订单状态已被改变，请刷新重试"),
    CLEARING_STATEMENT_STATUS_CHANGE(400008, "结算状态已被改变，请刷新重试"),
    WALLET_AMOUNT_NOT_ENOUGH(400009, "金额不足,请刷新重试"),
    MOUNT_ERROR(400010, "扣减金额不能大于应结金额"),

    BACK_AMOUNT_NOT_EXIST(400011, "退款金额不能大于实付金额"),

    //随行红包
    ERROR_RED_PACKET_ACTIVITY_INVALID(420001, "活动失效"),
    ERROR_RED_PACKET_ORDER_END(420002, "订单完结"),
    ERROR_RED_PACKET_NUM_OVER(420003, "您的参与次数已用完"),
    ERROR_RED_PACKET_ORDER_NUM_OVER(420004, "您的参与订单数已达上限"),
    ERROR_RED_PACKET_GAME_OVER(420005, "本单游戏已结束"),
    ERROR_RED_PACKET_MEMBER_COMPLETE(420006, "未完善个人信息"),

    //认证有礼
    DATA_CHANGE(430001, "活动数据有变动，请刷新重试"),
    CERTIFIED_ACTIVITY_NOT_EXIST(430002, "活动不存在"),
    CERTIFIED_RECORD_NOT_EXIST(430003, "未查询到活动申请"),
    CERTIFIED_STATUS_ERROR(430004, "该驳回申请已经重新申请了"),
    QUERY_PHONE_ERROR(430005, "请求失败，请联系管理人员"),

    /**
     * 积分
     */
    INTEGRAL_TASK_NOT_FOUND(440000, "积分任务未查询到"),
    INTEGRAL_OVERDUE_DAY_NUM(440001, "请填写提醒时间"),
    INTEGRAL_PROTECT_DAY_NUM(440002, "请填写保护时间"),
    INTEGRAL_RULE_UNINITIALIZED(440003, "当前运营主体,未初始化积分通用规则,请联系管理"),
    INTEGRAL_RULE_REFRESH(440004, "请重新刷新页面"),
    INTEGRAL_NAME_MORE_THAN_ONE(440005, "积分任务名称重复"),
    INTEGRAL_VALUE_NOT_FOUND(440006, "积分值不能为空"),
    INTEGRAL_INFO_TYPE_NOT_FOUND(440007, "积分信息类型,不能为空"),
    INTEGRAL_REGISTER_NOT_FOUND(440008, "注册渠道,不能为空"),
    INTEGRAL_PERFECT_PARAM_AMOUNT_ERROR(440009, "消费金额不能为空"),
    INTEGRAL_APPLY_BUSINESS_ERROR(440010, "积分任务，适用业务不能为空"),
    INTEGRAL_RECHARGE_NUM_TYPE_ERROR(440011, "积分任务，充值获取次数类型不能为空"),
    INTEGRAL_ACTION_TYPE_ERROR(440012, "任务动作类型不存在"),
    INTEGRAL_OVERDUE_WAYS_ERROR(440013, "积分过期提醒方式不能为空"),
    INTEGRAL_TASK_ENABLE_ERROR(440014, "进行中的积分任务不能被删除"),
    INTEGRAL_TASK_EDIT_ERROR(440015, "进行中的积分任务不能被编辑"),
    INTEGRAL_TASK_FINISHED_ERROR(440016, "已结束或者未开始的积分任务不能被启用"),
    INTEGRAL_TASK_UNPLAYED_ERROR(440017, "未开始的积分任务不能被删除"),
    INTEGRAL_TASK_PERSONAL_TYPE_ERROR(440018, "会员积分任务-完善个人信息，限制规则不可编辑"),
    INTEGRAL_REDUCE_NOT_BE_ZERO(440019, "积分变化不能为0"),
    INTEGRAL_DETAIL_RECORD_ERROR(440020, "积分明细记录数据异常，请联系管理人员"),
    INTEGRAL_NO_COMMODITY_ERROR(440021, "商品未选择"),
    INTEGRAL_TOTAL_PERIOD_ERROR(440022, "累计周期不能为空"),

    /**
     * 商家
     */
    ERROR_MERCHANT_IS_DISABLED(600001, "商家已被禁用，请联系管理员"),
    ERROR_CURRENT_MERCHANT_IS_DISABLED(600002, "当前商家已被禁用，请联系管理员"),

    // todo 会员商城
    PRODUCT_NOT_EXIST(500001, "商品不存在"),

    PRODUCT_NOT_PRICING(500002, "已勾选商品存在未定价，请先定价"),

    PRODUCT_INFO_PERFECT(500003, "已勾选商品存在信息未完善"),

    CLOSE_ERROR(500004, "关闭失败：已有商品选择快递发货，不可关闭"),

    DISTRIBUTION_SET_NOT_EXIST(500005, "商品设置不存在"),

    ERROR_MALL_ORDER_MAX_UPLOAD_EXCEL(500006, "导出数量超过5000条,请小于等于5000条数据!"),

    ACCOUNT_RULE_UNINITIALIZED(510001, "当前运营主体,未初始化账户设置,请联系管理"),

    ACCOUNT_NONEXISTENT(510002, "您还不是平台会员，请联系管理员"),

    ACCOUNT_ALI_NOT_PUBLIC_KEY(510003, "当前运营主体未配置支付宝小程序商户"),

    LOGIN_INFORMATION_CANNOT_BE_EMPTY(510004, "登录信息不能为空"),


    COUPON_EXPIRE(610004, "优惠券已过期"),

    COUPON_APPLY(610005, "优惠券已使用"),

    COUPON_OVER(610006, "优惠券已失效"),

    COUPON_NOT_USABLE(610007, "优惠券未到可用时间，请稍后使用"),

    COUPON_NOT_FOUND(610008, "请输入正确券码"),

    BEFORE_LOGIN_MEMBER(610009, "请先登录会员账户"),
    ;

    public int code;
    public String des;

    MemberAccountExceptionEnum(final int code, final String des) {
        this.code = code;
        this.des = des;
    }


    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDes() {
        return des;
    }
}
