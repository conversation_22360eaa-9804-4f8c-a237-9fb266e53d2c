package com.holderzone.member;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;

@SpringBootApplication
@EnableFeignClients(basePackages="com.holderzone.member.common.feign")
@ComponentScans(value = {
        @ComponentScan("com.holderzone.member.common"),
        @ComponentScan("com.holderzone.member.openapi")
})
public class HolderMemberOpenApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(HolderMemberOpenApiApplication.class, args);
    }

}
