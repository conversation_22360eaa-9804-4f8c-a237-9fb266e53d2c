package com.holderzone.member.queue.service.grade.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.equities.BusinessTypeEnum;
import com.holderzone.member.common.enums.equities.EquitiesRuleTypeEnum;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.queue.dto.grade.DoubleValueRequest;
import com.holderzone.member.queue.entity.HsaBusinessEquities;
import com.holderzone.member.queue.entity.HsaMemberEquitiesReceiveRecord;
import com.holderzone.member.queue.entity.HsaMemberGradeChangeDetail;
import com.holderzone.member.queue.mapper.HsaGradeEquitiesMapper;
import com.holderzone.member.queue.mapper.HsaMemberEquitiesReceiveRecordMapper;
import com.holderzone.member.queue.service.grade.AbstractHandler;
import com.holderzone.member.queue.service.grade.HsaGradeEquitiesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


/**
 * @program: member-marketing
 * @description: 等级权益service
 * @author: pan tao
 * @create: 2022-01-18 18:22
 */
@Slf4j
@Service
public class HsaGradeEquitiesServiceImpl extends HolderBaseServiceImpl<HsaGradeEquitiesMapper, HsaBusinessEquities>
        implements HsaGradeEquitiesService {

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private HsaGradeEquitiesMapper hsaGradeEquitiesMapper;

    @Resource
    private HsaMemberEquitiesReceiveRecordMapper hsaMemberEquitiesReceiveRecordMapper;

    @Resource
    private List<AbstractHandler> abstractHandleList;

    private AbstractHandler abstractHandler;

    @PostConstruct
    public void initializeChainFilter() {

        for (int i = 0; i < abstractHandleList.size(); i++) {
            if (i == 0) {
                abstractHandler = abstractHandleList.get(0);
            } else {
                AbstractHandler currentHander = abstractHandleList.get(i - 1);
                AbstractHandler nextHander = abstractHandleList.get(i);
                currentHander.setNextHandler(nextHander);
            }
        }
    }


    @Override
    public Long isDoubleGrowthValue(DoubleValueRequest request) {
        log.info("翻倍成长值权益请求参数：{}", JSON.toJSONString(request));
        Long doubleValue = null;

        //查询当前会员是否拥有翻倍成长值权益
        HsaBusinessEquities hsaBusinessEquities;
        if (request.getBusinessType() == BusinessTypeEnum.GRADE_EQUITIES.getCode()) {
            hsaBusinessEquities = hsaGradeEquitiesMapper.selectOne(
                    new LambdaQueryWrapper<HsaBusinessEquities>()
                            .eq(HsaBusinessEquities::getGuid, request.getGradeEquitiesGuid())
                            .eq(HsaBusinessEquities::getEquitiesRuleType, EquitiesRuleTypeEnum.DOUBLE_GROWTH_VALUE.getCode())
                            .eq(HsaBusinessEquities::getBusinessType, request.getBusinessType())
                            .in(HsaBusinessEquities::getIsDelete, NumberConstant.NUMBER_0, NumberConstant.NUMBER_2)
                            .eq(HsaBusinessEquities::getEffective, NumberConstant.NUMBER_1));
        } else {
            hsaBusinessEquities = hsaGradeEquitiesMapper.selectOne(
                    new LambdaQueryWrapper<HsaBusinessEquities>()
                            .eq(HsaBusinessEquities::getGuid, request.getGradeEquitiesGuid())
                            .eq(HsaBusinessEquities::getEquitiesRuleType, EquitiesRuleTypeEnum.DOUBLE_GROWTH_VALUE.getCode())
                            .eq(HsaBusinessEquities::getBusinessType, request.getBusinessType())
                            .in(HsaBusinessEquities::getIsDelete, NumberConstant.NUMBER_0, NumberConstant.NUMBER_2));
        }

        log.info("当前会员翻倍成长值权益hsaGradeEquities:{}", JSON.toJSONString(hsaBusinessEquities));

        //查询会员是否拥有会员卡翻倍
        if (Objects.isNull(hsaBusinessEquities)) {
            return doubleValue;
        }
        //查询当前会员是否使用过翻倍成长值记录
        HsaMemberEquitiesReceiveRecord equitiesReceiveRecord = getHsaMemberEquitiesReceiveRecord(request);
        doubleValue = this.exec(request, hsaBusinessEquities, equitiesReceiveRecord);
        if (Objects.isNull(doubleValue)) {
            return doubleValue;
        }
        log.info("equitiesReceiveRecord:{}", JSON.toJSONString(equitiesReceiveRecord));
        if (Objects.nonNull(equitiesReceiveRecord)) {
            equitiesReceiveRecord.setDoubleValueCount(equitiesReceiveRecord.getDoubleValueCount() + NumberConstant.NUMBER_1);

            Integer totalDoubleUpperValue = equitiesReceiveRecord.getTotalDoubleUpperValue();
            equitiesReceiveRecord.setTotalDoubleUpperValue((int) (totalDoubleUpperValue + doubleValue));
            hsaMemberEquitiesReceiveRecordMapper.updateByGuid(equitiesReceiveRecord);
        } else {
            insertMemberEquitiesReceiveRecord(request, hsaBusinessEquities, doubleValue);
        }
        return doubleValue;
    }

    private void insertMemberEquitiesReceiveRecord(DoubleValueRequest request, HsaBusinessEquities hsaBusinessEquities, Long doubleValue) {
        HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord = new HsaMemberEquitiesReceiveRecord();
        String guid = guidGeneratorUtil.getStringGuid(HsaMemberGradeChangeDetail.class.getSimpleName());
        hsaMemberEquitiesReceiveRecord.setGuid(guid);
        hsaMemberEquitiesReceiveRecord.setGradeEquitiesGuid(hsaBusinessEquities.getGuid());
        hsaMemberEquitiesReceiveRecord.setMemberGradeGuid(hsaBusinessEquities.getMemberGradeInfoGuid());
        hsaMemberEquitiesReceiveRecord.setMemberInfoGuid(request.getMemberInfoGuid());
        hsaMemberEquitiesReceiveRecord.setDoubleValueCount(NumberConstant.NUMBER_1);
        hsaMemberEquitiesReceiveRecord.setTotalDoubleUpperValue(Math.toIntExact(doubleValue));
        hsaMemberEquitiesReceiveRecord.setOperSubjectGuid(request.getOperSubjectGuid());
        hsaMemberEquitiesReceiveRecord.setType(BooleanEnum.FALSE.getCode());
        hsaMemberEquitiesReceiveRecordMapper.insert(hsaMemberEquitiesReceiveRecord);
        log.info("hsaMemberEquitiesReceiveRecord：{}", hsaMemberEquitiesReceiveRecord);
    }

    private HsaMemberEquitiesReceiveRecord getHsaMemberEquitiesReceiveRecord(DoubleValueRequest request) {
        return hsaMemberEquitiesReceiveRecordMapper.selectOne(
                new LambdaQueryWrapper<HsaMemberEquitiesReceiveRecord>()
                        .eq(HsaMemberEquitiesReceiveRecord::getOperSubjectGuid, request.getOperSubjectGuid())
                        .eq(HsaMemberEquitiesReceiveRecord::getMemberInfoGuid, request.getMemberInfoGuid())
                        .eq(HsaMemberEquitiesReceiveRecord::getType, BooleanEnum.FALSE.getCode())
                        .isNull(HsaMemberEquitiesReceiveRecord::getGiveGrowthValueNumber)
                        .eq(HsaMemberEquitiesReceiveRecord::getGradeEquitiesGuid, request.getGradeEquitiesGuid()));
    }

    /**
     * 执行可以翻倍的成长值
     *
     * @param request               请求参数
     * @param hsaBusinessEquities   权益信息
     * @param equitiesReceiveRecord 会员使用的权益记录
     * @return 可以翻倍的成长值
     */
    public Long exec(DoubleValueRequest request, HsaBusinessEquities hsaBusinessEquities,
                     HsaMemberEquitiesReceiveRecord equitiesReceiveRecord) {
        return abstractHandler.filter(request, hsaBusinessEquities, equitiesReceiveRecord);
    }

    public AbstractHandler getAbstractHandler() {
        return abstractHandler;
    }

    public void setAbstractHandler(AbstractHandler abstractHandler) {
        this.abstractHandler = abstractHandler;
    }

    /**
     * 翻倍成长值计算
     *
     * @param countLimit        翻倍次数
     * @param singleDoubleLimit 单次翻倍上限
     * @param totalDoubleLimit  累计翻倍上限
     * @param growthValue       需要翻倍成长值
     * @param doubleNum         翻倍倍数
     * @return
     */
    private long calculateDoubleGrowthValue(Long countLimit,
                                            Long singleDoubleLimit, Long totalDoubleLimit,
                                            long growthValue, Integer doubleNum) {
        long doubleValue = NumberConstant.NUMBER_0;
        if (Objects.isNull(countLimit)) {
            doubleValue = totalDoubleLimit(totalDoubleLimit, singleDoubleLimit, doubleNum, growthValue);
        }
        if (countLimit >= NumberConstant.NUMBER_1) {
            doubleValue = totalDoubleLimit(totalDoubleLimit, singleDoubleLimit, doubleNum, growthValue);
        }
        return doubleValue;
    }

    private long totalDoubleLimit(Long totalDoubleLimit, Long singleDoubleLimit, Integer doubleNum, long growthValue) {
        long doubleValue;

        //如果没有累计翻倍成长值限制
        if (Objects.isNull(totalDoubleLimit)) {
            doubleValue = singleLimit(singleDoubleLimit, doubleNum, growthValue);
        } else if (Objects.nonNull(singleDoubleLimit) || totalDoubleLimit > singleDoubleLimit) {
            doubleValue = singleLimit(singleDoubleLimit, doubleNum, growthValue);
        } else {
            doubleValue = singleLimit(totalDoubleLimit, doubleNum, growthValue);
        }
        return doubleValue;
    }

    /**
     * 计算可以翻倍多少成长值
     *
     * @param singleDoubleLimit 单次翻倍成长值最大限制
     * @param doubleNum         翻倍倍数
     * @param growthValue       成长值
     * @return 可以翻倍的成长值
     */
    private long singleLimit(Long singleDoubleLimit, int doubleNum, long growthValue) {
        //翻倍成长值
        long doubleValue = doubleNum * growthValue;

        //可以翻倍增加的成长值
        long increaseValue = doubleValue - growthValue;
        //如果没有单次最大成长值限制 或者 可以增加翻倍的成长值小于单次最大翻倍值
        if (Objects.isNull(singleDoubleLimit) || increaseValue < singleDoubleLimit) {
            return increaseValue;
        } else {
            return singleDoubleLimit;
        }
    }
}
