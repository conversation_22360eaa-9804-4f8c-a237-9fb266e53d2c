package com.holderzone.member.queue.service.member.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.QueryOrderCommodity;
import com.holderzone.member.common.dto.base.ResOrderCommodity;
import com.holderzone.member.common.dto.growth.ClassifyGoodsDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.equities.BusinessTypeEnum;
import com.holderzone.member.common.enums.equities.EquitiesRuleTypeEnum;
import com.holderzone.member.common.enums.growth.*;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.MemberMallFeign;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.util.verify.ObjectUtil;
import com.holderzone.member.queue.client.ShopBaseService;
import com.holderzone.member.queue.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.queue.dto.grade.DoubleValueRequest;
import com.holderzone.member.queue.dto.grade.GrowthCommodityTouchDetailsDTO;
import com.holderzone.member.queue.dto.grade.MemberGradeChangeDTO;
import com.holderzone.member.queue.dto.grade.gradeEquitiesVo;
import com.holderzone.member.queue.dto.member.GrowthCommodityQuantityTouchDetailDTO;
import com.holderzone.member.queue.dto.member.MemberGrowthValueEvent;
import com.holderzone.member.queue.entity.*;
import com.holderzone.member.queue.mapper.*;
import com.holderzone.member.queue.service.assembler.GradeChangeAssembler;
import com.holderzone.member.queue.service.grade.HsaGradeEquitiesService;
import com.holderzone.member.queue.service.grade.HsaMemberEquitiesReceiveRecordService;
import com.holderzone.member.queue.service.grade.HsaMemberGradeChangeDetailService;
import com.holderzone.member.queue.service.member.HsaGrowthCommodityTouchDetailService;
import com.holderzone.member.queue.service.member.HsaGrowthValueDetailService;
import com.holderzone.member.queue.service.member.HsaOperationMemberInfoService;
import com.holderzone.member.queue.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 成长值明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-12
 */
@Service
@Slf4j
public class HsaGrowthValueDetailServiceImp extends HolderBaseServiceImpl<HsaGrowthValueDetailMapper, HsaGrowthValueDetail> implements HsaGrowthValueDetailService {

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private HsaGrowthValueTaskMapper hsaGrowthValueTaskMapper;

    @Resource
    private HsaGrowthValueCommodityRuleMapper hsaGrowthValueCommodityRuleMapper;

    @Resource
    private HsaGrowthValueDetailMapper hsaGrowthValueDetailMapper;

    @Resource
    private HsaMemberConsumptionMapper hsaMemberConsumptionMapper;

    @Resource
    private HsaExtraAwardRuleMapper HsaExtraAwardRuleMapper;

    @Resource
    private HsaStoreRuleInfoMapper hsaStoreRuleInfoMapper;

    @Resource
    private HsaGrowthCommodityTouchDetailMapper hsaGrowthCommodityTouchDetailMapper;

    @Resource
    private HsaGrowthCommodityTouchDetailService hsaGrowthCommodityTouchDetailService;

    @Resource
    private MemberMallFeign memberMallFeign;

    @Resource
    private HsaMemberGradeInfoMapper hsaMemberGradeInfoMapper;

    @Resource
    private HsaGradeEquitiesMapper hsaEquitiesMapper;

    @Resource
    private ExternalSupport externalSupport;
    @Autowired
    @Lazy
    private HsaOperationMemberInfoService hsaOperationMemberInfoService;

    @Autowired
    @Lazy
    private HsaGrowthValueDetailService hsaGrowthValueDetailService;

    @Resource
    private ShopBaseService shopBaseService;

    @Resource
    private HsaMemberGradeChangeDetailService hsaMemberGradeChangeDetailService;

    @Resource
    private Executor memberQueueThreadExecutor;

    @Resource
    private HsaGradeEquitiesService hsaGradeEquitiesService;

    @Resource
    private HsaMemberEquitiesReceiveRecordService hsaMemberEquitiesReceiveRecordService;

    @Resource
    private HsaSuspendTaskTimeQuantumMapper hsaSuspendTaskTimeQuantumMapper;

    @Resource
    private GradeChangeAssembler gradeChangeAssembler;

    /**
     * 逗号
     */
    private static final String COMMA = ",";



    /**
     * 基础任务处理
     *
     * @param event event
     */
    @Override
    public void baseTaskBusinessProcessor(MemberGrowthValueEvent event) {
        log.info("成长值基础任务调整>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", event);
        HsaGrowthValueTask hsaGrowthValueTask = hsaGrowthValueTaskMapper.queryByGuid(event.getGrowthValueTaskGuid());
        addMemberGrowthValueProcessor(event, hsaGrowthValueTask, hsaGrowthValueTask.getGrowthValue());
        //等级权益变化
        memberQueueThreadExecutor.execute(() -> sendMemberGradeChange(event));
    }

    /**
     * 成长值任务后置处理
     *
     * @param event              event
     * @param hsaGrowthValueTask hsaGrowthValueTask
     * @param growthValue        growthValue
     */
    private void addMemberGrowthValueProcessor(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask, int growthValue) {
        String growthDetailGuid = event.getGrowthDetailGuid();
        if (StringUtils.isBlank(growthDetailGuid)) {
            growthDetailGuid = guidGeneratorUtil.getStringGuid(HsaGrowthValueDetail.class.getSimpleName());
        }
        log.info("addMemberGrowthValueProcessor>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
        Map<String, HsaOperationMemberInfo> hsaOperationMemberInfoMap = event.getHsaOperationMemberInfoMap();
        LocalDateTime dateTime = event.getDateTime();
        //获取会员等级
        Map<String, HsaMemberGradeInfo> hsaMemberGradeInfoMap = getStringHsaMemberGradeInfoMap(event.getMemberInfoGuidList());
        //有效期计算
        LocalDateTime localDateTime = getLocalDateTime(hsaGrowthValueTask, dateTime);
        //成长值明细记录
        List<HsaGrowthValueDetail> hsaGrowthValueDetailList = Lists.newArrayList();
        //会员信息
        List<HsaOperationMemberInfo> hsaOperationMemberInfoList = Lists.newArrayList();
        for (String memberGuid : event.getMemberInfoGuidList()) {
            HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMap.get(memberGuid);
            log.info("hsaOperationMemberInfo>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", JSON.toJSONString(hsaOperationMemberInfo));
            HsaMemberGradeInfo hsaMemberGradeInfo = hsaMemberGradeInfoMap.get(hsaOperationMemberInfo.getMemberGradeInfoGuid());

            //获取成长值记录
            HsaGrowthValueDetail hsaGrowthValueDetail = getHsaGrowthValueDetail(event, hsaGrowthValueTask, growthValue, growthDetailGuid, hsaOperationMemberInfo, growthValue, hsaMemberGradeInfo);

            setValidityDate(hsaGrowthValueTask, hsaGrowthValueDetail, localDateTime, dateTime);

            hsaGrowthValueDetailList.add(hsaGrowthValueDetail);
            hsaOperationMemberInfoList.add(hsaOperationMemberInfo);
        }
        if (CollUtil.isNotEmpty(hsaGrowthValueDetailList)) {
            this.saveBatch(hsaGrowthValueDetailList);
            hsaOperationMemberInfoMapper.batchUpdateGrowth(hsaOperationMemberInfoList);
        }
    }

    private static void setValidityDate(HsaGrowthValueTask hsaGrowthValueTask, HsaGrowthValueDetail hsaGrowthValueDetail, LocalDateTime localDateTime, LocalDateTime dateTime) {
        if (hsaGrowthValueTask.getGrowthValueValidityType() == 0) {
            hsaGrowthValueDetail.setGrowthValidity(NumberConstant.NUMBER_0);
        } else {
            hsaGrowthValueDetail.setGrowthValidity(NumberConstant.NUMBER_1);
            hsaGrowthValueDetail.setGrowthValidityDate(localDateTime);
        }
        hsaGrowthValueDetail.setTaskFinishDate(dateTime);
        hsaGrowthValueDetail.setGmtCreate(dateTime);
        hsaGrowthValueDetail.setGmtModified(dateTime);
    }

    private HsaGrowthValueDetail getHsaGrowthValueDetail(MemberGrowthValueEvent event,
                                                         HsaGrowthValueTask hsaGrowthValueTask,
                                                         int growthValue,
                                                         String growthDetailGuid,
                                                         HsaOperationMemberInfo hsaOperationMemberInfo,
                                                         int growthValueVo,
                                                         HsaMemberGradeInfo hsaMemberGradeInfo) {
        HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        hsaGrowthValueDetail.setTaskName(hsaGrowthValueTask.getTaskName());
        hsaGrowthValueDetail.setTaskNumber(hsaGrowthValueTask.getTaskNumber());
        hsaGrowthValueDetail.setGuid(growthDetailGuid);
        hsaGrowthValueDetail.setGrowthValueSourceType(event.getSource());
        hsaGrowthValueDetail.setOperatorAccountName(event.getOperatorAccountName());
        hsaGrowthValueDetail.setOperSubjectGuid(hsaOperationMemberInfo.getOperSubjectGuid());
        hsaGrowthValueDetail.setChangeType(SumValueChangeEnum.TASK.getCode());
        hsaGrowthValueDetail.setRecordDeclaration(hsaGrowthValueTask.getTaskName() + StringConstant.STR_BIAS_TWO + hsaGrowthValueTask.getTaskNumber());

        hsaGrowthValueDetail.setGrowthValueTaskId(String.valueOf(hsaGrowthValueTask.getId()));
        //基础任务只能单次
        if (event.getTaskType() == TaskTypeEnum.BASE_TASK.getCode()) {
            hsaGrowthValueDetail.setTaskFinishTime(NumberConstant.NUMBER_1);
        } else {
            Integer num = hsaGrowthValueDetailMapper.selectCount(new LambdaQueryWrapper<HsaGrowthValueDetail>()
                    .eq(HsaGrowthValueDetail::getGrowthValueTaskId, hsaGrowthValueTask.getId())
                    .eq(HsaGrowthValueDetail::getMemberInfoGuid, hsaOperationMemberInfo.getGuid())
                    .eq(HsaGrowthValueDetail::getTaskAction, hsaGrowthValueTask.getTaskAction())
                    .gt(HsaGrowthValueDetail::getTaskFinishTime, 0));
            hsaGrowthValueDetail.setTaskFinishTime(num + NumberConstant.NUMBER_1);
        }
        hsaGrowthValueDetail.setGrowthValueType(NumberConstant.NUMBER_0);
        hsaGrowthValueDetail.setTaskAction(hsaGrowthValueTask.getTaskAction());

        //是否获取翻倍成长值
        growthValueVo = isDoubleGrowthValue(growthValueVo, hsaOperationMemberInfo, hsaGrowthValueDetail);

        //获取会员卡翻倍成长值
        growthValueVo = isDoubleCardGrowthValue(growthValue, hsaOperationMemberInfo, hsaGrowthValueDetail, event, growthValueVo);
        hsaGrowthValueDetail.setGrowthValue(growthValueVo);
        hsaGrowthValueDetail.setMemberInfoGuid(hsaOperationMemberInfo.getGuid());
        hsaOperationMemberInfo.setMemberGrowthValue(hsaOperationMemberInfo.getMemberGrowthValue() + growthValueVo);
        hsaGrowthValueDetail.setRemainGrowthValue(hsaOperationMemberInfo.getMemberGrowthValue());

        hsaGrowthValueDetail.setStoreGuid(event.getStoreGuid());
        hsaGrowthValueDetail.setStoreName(event.getStoreName());
        hsaGrowthValueDetail.setOrderNumber(event.getOrderNumber());
        hsaGrowthValueDetail.setMemberConsumptionGuid(event.getMemberConsumptionGuid());
        hsaGrowthValueDetail.setRecordRemainGrowthValue(growthValueVo);
        gradeChangeAssembler.setCurrentMemberLevel(hsaOperationMemberInfo, hsaMemberGradeInfo, hsaGrowthValueDetail);
        return hsaGrowthValueDetail;
    }

    /**
     * 是否获取翻倍成长值
     *
     * @param growthValue            成长值
     * @param hsaOperationMemberInfo 操作会员
     * @param hsaGrowthValueDetail   成长值详情记录
     * @return 获取的成长值
     */
    private int isDoubleGrowthValue(Integer growthValue, HsaOperationMemberInfo hsaOperationMemberInfo,
                                    HsaGrowthValueDetail hsaGrowthValueDetail) {
        if (growthValue <= BigDecimal.ROUND_UP) {
            return growthValue;
        }
        //会员guid
        String memberInfoGuid = hsaOperationMemberInfo.getGuid();
        //查询等级权益基础信息
        gradeEquitiesVo equitiesInfo = hsaMemberGradeInfoMapper.getGradeEquitiesGuid(
                hsaOperationMemberInfo.getOperSubjectGuid(), hsaOperationMemberInfo.getMemberGradeInfoGuid(),
                EquitiesRuleTypeEnum.DOUBLE_GROWTH_VALUE.getCode());
        if (Objects.isNull(equitiesInfo) || Objects.isNull(equitiesInfo.getEquitiesGuid())) {
            return growthValue;
        }

        DoubleValueRequest doubleValueRequest = setDoubleValueRequest(
                hsaOperationMemberInfo, growthValue, equitiesInfo.getEquitiesGuid(), BusinessTypeEnum.GRADE_EQUITIES.getCode());
        Long doubleGrowthValue = hsaGradeEquitiesService.isDoubleGrowthValue(doubleValueRequest);
        if (Objects.isNull(doubleGrowthValue) || doubleGrowthValue < 0) {
            return growthValue;
        }

        growthValue = (int) (doubleGrowthValue + growthValue);
        hsaGrowthValueDetail.setMultipleGrowthValue(doubleGrowthValue.intValue());
        hsaGrowthValueDetail.setMultipleGrowth(equitiesInfo.getDoubleNumber());
        hsaGrowthValueDetail.setMultipleEquitiesGuid(equitiesInfo.getEquitiesGuid());
        log.info("memberInfoGuid：{},获得翻倍成长值:{}", memberInfoGuid, growthValue);
        return growthValue;
    }

    /**
     * 是否获取会员卡翻倍成长值
     *
     * @param growthValue            成长值
     * @param hsaOperationMemberInfo 操作会员
     * @param hsaGrowthValueDetail   成长值详情记录
     * @return 获取的成长值
     */
    private int isDoubleCardGrowthValue(Integer oldGrowthValue, HsaOperationMemberInfo hsaOperationMemberInfo,
                                        HsaGrowthValueDetail hsaGrowthValueDetail, MemberGrowthValueEvent event,
                                        int growthValue) {
        if (growthValue <= BigDecimal.ROUND_UP || StringUtils.isEmpty(event.getCardGuid())) {
            return growthValue;
        }
        //会员guid
        String memberInfoGuid = hsaOperationMemberInfo.getGuid();
        //查询等级权益基础信息
        HsaBusinessEquities hsaBusinessEquities = hsaEquitiesMapper.selectOne(new LambdaQueryWrapper<HsaBusinessEquities>()
                .eq(HsaBusinessEquities::getMemberGradeInfoGuid, event.getCardGuid())
                .eq(HsaBusinessEquities::getEquitiesRuleType, EquitiesRuleTypeEnum.DOUBLE_GROWTH_VALUE.getCode())
                .eq(HsaBusinessEquities::getIsDelete, BooleanEnum.FALSE.getCode()));
        if (Objects.isNull(hsaBusinessEquities)) {
            return growthValue;
        }

        DoubleValueRequest doubleValueRequest = setDoubleValueRequest(
                hsaOperationMemberInfo, oldGrowthValue, hsaBusinessEquities.getGuid(), BusinessTypeEnum.CARD_EQUITIES.getCode());
        Long doubleGrowthValue = hsaGradeEquitiesService.isDoubleGrowthValue(doubleValueRequest);
        if (Objects.isNull(doubleGrowthValue) || doubleGrowthValue < 0) {
            return growthValue;
        }
        growthValue = (int) (doubleGrowthValue + growthValue);
        hsaGrowthValueDetail.setMultipleCardGrowthValue(doubleGrowthValue.intValue());
        hsaGrowthValueDetail.setMultipleCardGrowth(hsaBusinessEquities.getGradeValueDoubleNumber());
        hsaGrowthValueDetail.setMultipleCardEquitiesGuid(hsaBusinessEquities.getGuid());
        log.info("memberInfoGuid：{},获得翻倍成长值:{}", memberInfoGuid, growthValue);
        return growthValue;
    }



    /**
     * 封装翻倍成长值请求参数
     *
     * @param hsaOperationMemberInfo 会员信息
     * @param growthValue            增加的成长值
     * @param equitiesGuid           权益guid
     * @return 封装数据
     */
    private DoubleValueRequest setDoubleValueRequest(HsaOperationMemberInfo hsaOperationMemberInfo,
                                                     Integer growthValue,
                                                     String equitiesGuid,
                                                     Integer businessType) {
        if (Objects.isNull(growthValue) || Objects.isNull(hsaOperationMemberInfo)) {
            return null;
        }
        DoubleValueRequest doubleValueRequest = new DoubleValueRequest();
        doubleValueRequest.setGrowthValue(growthValue);
        doubleValueRequest.setMemberInfoGuid(hsaOperationMemberInfo.getGuid());
        doubleValueRequest.setOperSubjectGuid(hsaOperationMemberInfo.getOperSubjectGuid());
        doubleValueRequest.setGradeEquitiesGuid(equitiesGuid);
        doubleValueRequest.setBusinessType(businessType);
        return doubleValueRequest;
    }

    private void sendMemberGradeChange(MemberGrowthValueEvent event) {
        if (CollectionUtil.isEmpty(event.getMemberInfoGuidList())) {
            return;
        }
        List<HsaOperationMemberInfo> hsaOperationMemberInfos = hsaOperationMemberInfoService.checkMemberGuid(event.getMemberInfoGuidList());
        log.info("等级变更hsaOperationMemberInfos>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", hsaOperationMemberInfos);
        MemberGradeChangeDTO changeDTO = MemberGradeChangeDTO.getMemberGradeChangeDTO(event);
        changeDTO.setHsaOperationMemberInfos(hsaOperationMemberInfos);
        hsaMemberGradeChangeDetailService.memberGradeChange(changeDTO);
    }

    private Map<String, HsaMemberGradeInfo> getStringHsaMemberGradeInfoMap(List<String> memberInfoGuidList) {
        List<String> memberGradeInfoGuidLists = hsaOperationMemberInfoService.checkMemberGuid(memberInfoGuidList)
                .stream().map(HsaOperationMemberInfo::getMemberGradeInfoGuid).collect(Collectors.toList());
        return hsaMemberGradeInfoMapper.selectList(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                        .in(HsaMemberGradeInfo::getGuid, memberGradeInfoGuidLists)
                        .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                        .eq(HsaMemberGradeInfo::getEffective, NumberConstant.NUMBER_1))
                .stream()
                .collect(Collectors.toMap(HsaMemberGradeInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));
    }

    private LocalDateTime getLocalDateTime(HsaGrowthValueTask hsaGrowthValueTask, LocalDateTime dateTime) {
        if (hsaGrowthValueTask.getGrowthValueValidityType() == 0) {
            return null;
        }
        return DateUtil.loseDateTimeProcessor(hsaGrowthValueTask.getGrowthValueValidityType(),
                hsaGrowthValueTask.getFixedGrowthValueValidityDate(), hsaGrowthValueTask.getDynamicValidityType(),
                hsaGrowthValueTask.getDynamicValidityNumber(),
                dateTime);
    }


    /**
     * 消费任务处理
     *
     * @param event event
     */
    @Override
    public void consumptionTaskBusinessProcessor(MemberGrowthValueEvent event) {
        log.info("消费任务处理>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
        List<HsaGrowthValueTask> hsaGrowthValueTasks = hsaGrowthValueTaskMapper.selectList(new LambdaQueryWrapper<HsaGrowthValueTask>()
                .eq(HsaGrowthValueTask::getOperSubjectGuid, event.getOperSubjectGuid())
                .eq(HsaGrowthValueTask::getTaskType, TaskTypeEnum.CONSUMPTION_TASK.getCode())
                .eq(HsaGrowthValueTask::getIsEnable, BooleanEnum.TRUE.getCode())
                .eq(HsaGrowthValueTask::getIsDelete, BooleanEnum.FALSE.getCode())
        );
        if (CollectionUtil.isEmpty(hsaGrowthValueTasks)) {
            return;
        }
        log.info("hsaGrowthValueTasks>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", JSONArray.toJSONString(hsaGrowthValueTasks));

        Map<String, List<HsaSuspendTaskTimeQuantum>> suspendMap = hsaSuspendTaskTimeQuantumMapper
                .selectList(new LambdaQueryWrapper<HsaSuspendTaskTimeQuantum>()
                        .eq(HsaSuspendTaskTimeQuantum::getType, TaskTypeEnum.CONSUMPTION_TASK.getCode())
                        .eq(HsaSuspendTaskTimeQuantum::getTaskType, GrowthValueOrIntegralEnum.GROWTH_VALUE.getCode())
                        .isNotNull(HsaSuspendTaskTimeQuantum::getSuspendStartTime)
                        .isNotNull(HsaSuspendTaskTimeQuantum::getSuspendEndTime))
                .stream()
                .collect(Collectors.groupingBy(HsaSuspendTaskTimeQuantum::getTaskGuid));
        for (HsaGrowthValueTask hsaGrowthValueTask : hsaGrowthValueTasks) {
            if (hsaGrowthValueTask.getTaskValidityType() == TaskValidityTypeEnum.FIXED_VALIDITY.getCode()
                    && (LocalDateTime.now().isBefore(hsaGrowthValueTask.getStartFixedTaskValidityDate())
                    || LocalDateTime.now().isAfter(hsaGrowthValueTask.getEndFixedTaskValidityDate()))) {
                    continue;

            }
            log.info("成长值任务循环>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", hsaGrowthValueTask);
            TaskActionEnum taskActionEnum = TaskActionEnum.Enum(hsaGrowthValueTask.getTaskAction());
            switch (Objects.requireNonNull(taskActionEnum)) {
                case SINGLE_CONSUMPTION_AMOUNT:
                    log.info("成长值单笔消费金额任务>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", event);
                    taskConsumption(event, hsaGrowthValueTask);
                    break;
                //消费指定商品
                case CONSUMPTION_SPECIFIED_GOODS:
                    boolean goodsClassify = hsaGrowthValueTask.getConsumptionGoodsType() == GoodsTypeEnum.APPOINT_CLASSIFY_GOODS.getCode();
                    if (goodsClassify) {
                        log.info("成长值消费指定商品分类>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", event);
                        taskConsumptionCommodityType(event, hsaGrowthValueTask);
                        break;
                    }
                    log.info("成长值消费指定商品>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", event);
                    taskConsumptionCommodity(event, hsaGrowthValueTask);
                    break;
                //累计消费金额
                case TOTAL_CONSUMPTION_AMOUNT:
                    log.info("成长值累计消费金额任务====>", JSONObject.toJSONString(event));
                    addTotalConsumption(event, hsaGrowthValueTask, suspendMap.get(hsaGrowthValueTask.getGuid()));
                    break;
                //累计消费笔数
                case TOTAL_CONSUMPTION_COUNT:
                    log.info("成长值累计消费笔数任务>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", event);
                    addTotalConsumptionCount(event, hsaGrowthValueTask, suspendMap.get(hsaGrowthValueTask.getGuid()));
                    break;
            }
        }
        //等级权益变化
        memberQueueThreadExecutor.execute(() -> sendMemberGradeChange(event));
    }

    /**
     * 单笔消费任务处理
     *
     * @param event              event
     * @param hsaGrowthValueTask hsaGrowthValueTask
     */
    @Transactional(rollbackFor = Exception.class)
    public void taskConsumption(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask) {
        if (checkTaskCondition(event, hsaGrowthValueTask)) {
            //计算可赠送成长值
            int sumGrowth = calculateSumGrowth(hsaGrowthValueTask, event);
            if (sumGrowth <= 0) {
                return;
            }
            //去重
            HsaGrowthValueDetail hsaGrowthValueDetail = hsaGrowthValueDetailMapper.selectOne(new LambdaQueryWrapper<HsaGrowthValueDetail>()
                    .eq(HsaGrowthValueDetail::getMemberConsumptionGuid, event.getMemberConsumptionGuid())
                    .eq(HsaGrowthValueDetail::getTaskAction, hsaGrowthValueTask.getTaskAction())
                    .eq(HsaGrowthValueDetail::getGrowthValueType, NumberConstant.NUMBER_1)
                    .eq(HsaGrowthValueDetail::getGrowthValueTaskId, hsaGrowthValueTask.getId())
                    .eq(HsaGrowthValueDetail::getMemberInfoGuid, event.getMemberInfoGuidList().get(0)));
            if (Objects.nonNull(hsaGrowthValueDetail)) {
                log.info("该成长值任务已完成过>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", hsaGrowthValueDetail);
                return;
            }
            addMemberGrowthValueProcessor(event, hsaGrowthValueTask, sumGrowth);
        }
    }


    private void taskConsumptionCommodityType(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask) {
        //根据门店和任务guid和业务查询商品分类信息
        List<ClassifyGoodsDTO> classifyGoods = hsaGrowthValueTaskMapper.listAppointGoodsType(hsaGrowthValueTask.getGuid(), event.getStoreGuid(), String.valueOf(event.getOrderType()));
        if (CollUtil.isEmpty(classifyGoods)) {
            log.info("此任务无匹配商品分类信息！任务guid ：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", hsaGrowthValueTask.getGuid());
            return;
        }
        //此笔订单商品
        List<ResOrderCommodity> resOrderCommodities = getResOrderCommodities(event);
        if (CollUtil.isEmpty(resOrderCommodities)) {
            log.info("此笔订单无商品数据！订单号 ：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", event.getOrderNumber());
            return;
        }
        List<String> collect = classifyGoods.stream().map(ClassifyGoodsDTO::getCategoryIds).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isEmpty(collect)) {
            log.info("此任务商品分类信息有误！任务guid ：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", hsaGrowthValueTask.getGuid());
            return;
        }
        Set<Integer> categoryIdSet = Sets.newHashSet();
        collect.forEach(e -> categoryIdSet.addAll(JSONArray.parseArray(e, Integer.class)));
        //过滤出满足当前任务的商品
        resOrderCommodities.removeIf(e -> !categoryIdSet.contains(e.getType_id()));
        if (CollUtil.isEmpty(resOrderCommodities)) {
            log.info("此笔订单商品不满足！订单号 ：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", event.getOrderNumber());
            return;
        }

        //次数校验
        if (!checkBuyNumber(event, hsaGrowthValueTask)) {
            return;
        }
        processRecordSave(event, hsaGrowthValueTask, resOrderCommodities);
    }


    /**
     * 消费指定商品任务处理
     *
     * @param event              event
     * @param hsaGrowthValueTask hsaGrowthValueTask
     */
    @Transactional(rollbackFor = Exception.class)
    public void taskConsumptionCommodity(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask) {
        //适用业务
        List<String> apply = Arrays.asList(hsaGrowthValueTask.getApplyBusinessJson().split(StringConstant.COMMA));
        if (!apply.contains(ObjectUtil.objToString(event.getOrderType()))) {
            log.info("订单业务类型校验不通过！订单类型 ：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", event.getOrderType());
            return;
        }

        //此笔订单商品
        List<ResOrderCommodity> resOrderCommodities = getResOrderCommodities(event);
        List<HsaGrowthValueCommodityRule> hsaGrowthValueCommodityRules = getHsaGrowthValueCommodityRules(event, hsaGrowthValueTask, resOrderCommodities);
        if (hsaGrowthValueCommodityRules == null) {
            return;
        }


        //用于获取实际订单商品
        List<ResOrderCommodity> orderCommodities = Lists.newArrayList();

        //商品数量完成件数
        if (checkCommodityNum(hsaGrowthValueTask, hsaGrowthValueCommodityRules, resOrderCommodities, event, orderCommodities)) {
            return;
        }
        //门店校验
        if (checkStore(event, hsaGrowthValueTask)) return;

        //次数校验
        if (!checkBuyNumber(event, hsaGrowthValueTask)) return;

        processRecordSave(event, hsaGrowthValueTask, orderCommodities);

    }

    private void processRecordSave(MemberGrowthValueEvent event,
                                   HsaGrowthValueTask hsaGrowthValueTask,
                                   List<ResOrderCommodity> orderCommodities) {
        int num = event.getBusinessNum();
        GrowthCommodityTouchDetailsDTO growthCommodityTouchDetailsDTO = new GrowthCommodityTouchDetailsDTO();
        //保存任务累计触发记录
        List<HsaGrowthCommodityTouchDetail> saveGrowthCommodityTouchDetails = Lists.newArrayList();
        //更新任务累计触发记录
        List<HsaGrowthCommodityTouchDetail> updateGrowthCommodityTouchDetails = Lists.newArrayList();
        //更新任务累计触发记录
        List<HsaGrowthCommodityTouchDetail> deleteGrowthCommodityTouchDetails = Lists.newArrayList();
        //以上校验通过则持久化（周期校验需要）
        gradeChangeAssembler.saveSatisfyTouchDetail(event, hsaGrowthValueTask, saveGrowthCommodityTouchDetails);
        growthCommodityTouchDetailsDTO.setSaveGrowthCommodityTouchDetails(saveGrowthCommodityTouchDetails);
        growthCommodityTouchDetailsDTO.setUpdateGrowthCommodityTouchDetails(updateGrowthCommodityTouchDetails);
        growthCommodityTouchDetailsDTO.setDeleteGrowthCommodityTouchDetails(deleteGrowthCommodityTouchDetails);

        checkBuyType(event, hsaGrowthValueTask, orderCommodities, num, growthCommodityTouchDetailsDTO);
        dealDB(growthCommodityTouchDetailsDTO);
    }

    private boolean checkCommodityNum(HsaGrowthValueTask hsaGrowthValueTask,
                                      List<HsaGrowthValueCommodityRule> hsaGrowthValueCommodityRules,
                                      List<ResOrderCommodity> resOrderCommodities,
                                      MemberGrowthValueEvent event,
                                      List<ResOrderCommodity> orderCommodities) {
        event.setBusinessNum(0);
        int num = event.getBusinessNum();
        GrowthCommodityQuantityTouchDetailDTO quantityTouchDetailDTO = new GrowthCommodityQuantityTouchDetailDTO();

        Map<String, ResOrderCommodity> resOrderCommodityMap = resOrderCommodities
                .stream()
                .collect(Collectors.toMap(in -> String.valueOf(in.getProductCode()), Function.identity(), (entity1, entity2) -> entity1));
        if (hsaGrowthValueTask.getConsumptionGoodsType() == GoodsTypeEnum.APPOINT_GOODS.getCode() && hsaGrowthValueTask.getChooseGoodsType() == NumberConstant.NUMBER_1) {
            num = checkAllCommodityRule(hsaGrowthValueCommodityRules, resOrderCommodities, hsaGrowthValueTask, event, num, quantityTouchDetailDTO);
            if (num == NumberConstant.NUMBER_0) {
                log.info("消费指定全部商品校验不通过！订单商品 ：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", resOrderCommodityMap);
                event.setBusinessNum(num);
                return true;
            }
        } else if (Boolean.FALSE.equals(checkCommodityRule(hsaGrowthValueCommodityRules, resOrderCommodityMap, orderCommodities))) {
            event.setBusinessNum(num);
            log.info("指定/分类商品校验不通过！订单商品 ：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", resOrderCommodityMap);
            return true;
        }
        event.setBusinessNum(num);
        return false;
    }

    private void dealDB(GrowthCommodityTouchDetailsDTO growthCommodityTouchDetailsDTO) {
        List<HsaGrowthCommodityTouchDetail> saveGrowthCommodityTouchDetails = growthCommodityTouchDetailsDTO.getSaveGrowthCommodityTouchDetails();
        List<HsaGrowthCommodityTouchDetail> deleteGrowthCommodityTouchDetails = growthCommodityTouchDetailsDTO.getDeleteGrowthCommodityTouchDetails();
        List<HsaGrowthCommodityTouchDetail> updateGrowthCommodityTouchDetails = growthCommodityTouchDetailsDTO.getUpdateGrowthCommodityTouchDetails();
        if (CollUtil.isNotEmpty(saveGrowthCommodityTouchDetails)) {
            hsaGrowthCommodityTouchDetailService.saveBatch(saveGrowthCommodityTouchDetails);
        }
        if (CollUtil.isNotEmpty(updateGrowthCommodityTouchDetails)) {
            updateGrowthCommodityTouchDetails.forEach(in -> hsaGrowthCommodityTouchDetailMapper.updateByGuid(in));
        }

        //周期天/周已触发更新
        if (CollUtil.isNotEmpty(deleteGrowthCommodityTouchDetails)) {
            deleteGrowthCommodityTouchDetails.forEach(in -> hsaGrowthCommodityTouchDetailMapper.updateByGuid(in));
        }
    }

    private void checkBuyType(MemberGrowthValueEvent event,
                              HsaGrowthValueTask hsaGrowthValueTask,
                              List<ResOrderCommodity> orderCommodities,
                              int num,
                              GrowthCommodityTouchDetailsDTO growthCommodityTouchDetailsDTO) {
        //获取累计次数、数量校验
        HsaGrowthCommodityTouchDetail hsaGrowthCommodityTouchDetail = hsaGrowthCommodityTouchDetailMapper.selectOne(new LambdaQueryWrapper<HsaGrowthCommodityTouchDetail>()
                .eq(HsaGrowthCommodityTouchDetail::getGrowthValueTaskGuid, hsaGrowthValueTask.getGuid())
                .eq(HsaGrowthCommodityTouchDetail::getMemberInfoGuid, event.getMemberInfoGuidList().get(0))
                .isNull(HsaGrowthCommodityTouchDetail::getOrderNumber));
        List<HsaGrowthCommodityTouchDetail> saveGrowthCommodityTouchDetails = growthCommodityTouchDetailsDTO.getSaveGrowthCommodityTouchDetails();
        List<HsaGrowthCommodityTouchDetail> deleteGrowthCommodityTouchDetails = growthCommodityTouchDetailsDTO.getDeleteGrowthCommodityTouchDetails();
        List<HsaGrowthCommodityTouchDetail> updateGrowthCommodityTouchDetails = growthCommodityTouchDetailsDTO.getUpdateGrowthCommodityTouchDetails();
        if (hsaGrowthValueTask.getBuyType() == BuyTypeEnum.BUY_COUNT.getCode()) {
            timeProcessor(event, hsaGrowthValueTask, saveGrowthCommodityTouchDetails, updateGrowthCommodityTouchDetails, hsaGrowthCommodityTouchDetail);
        } else if (hsaGrowthValueTask.getBuyType() == BuyTypeEnum.BUY_NUMBER.getCode()) {
            dealBuyNumber(event, hsaGrowthValueTask, orderCommodities, num);
        } else {
            dealBuyPeriod(event, hsaGrowthValueTask, saveGrowthCommodityTouchDetails, deleteGrowthCommodityTouchDetails);
        }
    }

    private void dealBuyNumber(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask, List<ResOrderCommodity> orderCommodities, int num) {
        if (hsaGrowthValueTask.getChooseGoodsType() != null && hsaGrowthValueTask.getChooseGoodsType() == ChooseGoodsTypeEnum.CONSUMPTION_APPOINT_ALL_GOODS.getCode()) {
            quantitySingleProcessor(event, hsaGrowthValueTask, num);
        } else {
            quantityGrandProcessor(event, hsaGrowthValueTask, orderCommodities);
        }
    }

    private void dealBuyPeriod(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask, List<HsaGrowthCommodityTouchDetail> saveGrowthCommodityTouchDetails, List<HsaGrowthCommodityTouchDetail> deleteGrowthCommodityTouchDetails) {
        List<HsaGrowthCommodityTouchDetail> touchDetailList = getHsaGrowthCommodityTouchDetails(event, hsaGrowthValueTask);
        //连续一天如果为空则直接赠送
        if (CollectionUtil.isEmpty(touchDetailList) && hsaGrowthValueTask.getBuyPeriodType() == DataUnitEnum.DAY.getCode()
                && hsaGrowthValueTask.getBuyNumber() == 1) {
            addMemberGrowthValueProcessor(event, hsaGrowthValueTask, hsaGrowthValueTask.getGrowthValue());
            saveGrowthCommodityTouchDetails.get(0).setIsDelete(BooleanEnum.TRUE.getCode());
            return;
        }
        if (CollectionUtil.isNotEmpty(touchDetailList)) {
            if (hsaGrowthValueTask.getBuyPeriodType() == DataUnitEnum.DAY.getCode()) {
                dealBuyPeriodDay(event, hsaGrowthValueTask, saveGrowthCommodityTouchDetails, deleteGrowthCommodityTouchDetails, touchDetailList);
            } else {
                dealBuyPeriodWeek(event, hsaGrowthValueTask, saveGrowthCommodityTouchDetails, deleteGrowthCommodityTouchDetails, touchDetailList);
            }
        }
    }

    private void dealBuyPeriodWeek(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask, List<HsaGrowthCommodityTouchDetail> saveGrowthCommodityTouchDetails, List<HsaGrowthCommodityTouchDetail> deleteGrowthCommodityTouchDetails, List<HsaGrowthCommodityTouchDetail> touchDetailList) {
        //当周是否触发过
        List<HsaGrowthValueDetail> hsaGrowthValueDetails = getHsaGrowthValueDetailsWeek(event, hsaGrowthValueTask);
        if (CollectionUtil.isNotEmpty(hsaGrowthValueDetails)) {
            log.info("本周已完成触发周期任务 ：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", saveGrowthCommodityTouchDetails);
        }
        if (!checkWeekContinuous(hsaGrowthValueTask, touchDetailList)) {
            log.info("周期连续每周校验不满足touchDetailList ：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", touchDetailList);
        } else {
            touchDetailList.forEach(in -> in.setIsDelete(BooleanEnum.TRUE.getCode()));
            deleteGrowthCommodityTouchDetails.addAll(touchDetailList);
            addMemberGrowthValueProcessor(event, hsaGrowthValueTask, hsaGrowthValueTask.getGrowthValue());
        }
    }

    private void dealBuyPeriodDay(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask,
                                  List<HsaGrowthCommodityTouchDetail> saveGrowthCommodityTouchDetails,
                                  List<HsaGrowthCommodityTouchDetail> deleteGrowthCommodityTouchDetails,
                                  List<HsaGrowthCommodityTouchDetail> touchDetailList) {
        //当天是否触发过
        List<HsaGrowthValueDetail> hsaGrowthValueDetails = getHsaGrowthValueDetailsDay(event, hsaGrowthValueTask);
        if (CollUtil.isNotEmpty(hsaGrowthValueDetails)) {
            log.info("当天已完成触发周期任务 ：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", saveGrowthCommodityTouchDetails);
        } else {
            if (!checkDayContinuous(hsaGrowthValueTask, touchDetailList)) {
                log.info("周期连续每天校验不满足touchDetailList ：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", touchDetailList);
            } else {
                touchDetailList.forEach(in -> in.setIsDelete(BooleanEnum.TRUE.getCode()));
                deleteGrowthCommodityTouchDetails.addAll(touchDetailList);
                addMemberGrowthValueProcessor(event, hsaGrowthValueTask, hsaGrowthValueTask.getGrowthValue());

            }
        }
    }

    private List<HsaGrowthValueCommodityRule> getHsaGrowthValueCommodityRules(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask, List<ResOrderCommodity> resOrderCommodities) {
        if (CollUtil.isEmpty(resOrderCommodities)) {
            log.info("此笔订单无商品数据！订单号 ：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", event.getOrderNumber());
            return Collections.emptyList();
        }
        //获取规则商品
        List<HsaGrowthValueCommodityRule> hsaGrowthValueCommodityRules;
        hsaGrowthValueCommodityRules = getHsaGrowthValueCommodityRules(hsaGrowthValueTask);
        if (CollUtil.isEmpty(hsaGrowthValueCommodityRules)) {
            log.info("规则商品为空！成长值任务Guid ：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", hsaGrowthValueTask.getGuid());
            return Collections.emptyList();
        }
        return hsaGrowthValueCommodityRules;
    }

    private List<ResOrderCommodity> getResOrderCommodities(MemberGrowthValueEvent event) {
        String enterpriseGuid = event.getHsaOperationMemberInfoMap().get(event.getMemberInfoGuidList().get(0)).getEnterpriseGuid();
        List<ResOrderCommodity> resOrderCommodities;
        if (event.getOrderType() == ApplyBusinessEnum.MALL_ORDER.getCode()) {
            resOrderCommodities = memberMallFeign.findMallBaseOrderProduct(event.getOrderNumber());
        } else {
            QueryOrderCommodity query = new QueryOrderCommodity();
            List<String> orderList = new ArrayList<>();
            orderList.add(event.getOrderNumber());
            query.setCodes(orderList);
            resOrderCommodities = externalSupport.storeServer(ThreadLocalCache.getSystem()).queryOrderCommodity(query);
            /**
             *
             * resOrderCommodities = orderBaseService.queryOrderCommodity(Collections.singletonList(event.
             *                     getOrderNumber()), getStoreHost(event.getOperSubjectGuid()), enterpriseGuid);
             **/
        }
        return resOrderCommodities;
    }

    private List<HsaGrowthCommodityTouchDetail> getHsaGrowthCommodityTouchDetails(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask) {
        return hsaGrowthCommodityTouchDetailMapper.selectList(new LambdaQueryWrapper<HsaGrowthCommodityTouchDetail>()
                .eq(HsaGrowthCommodityTouchDetail::getGrowthValueTaskGuid, hsaGrowthValueTask.getGuid())
                .eq(HsaGrowthCommodityTouchDetail::getMemberInfoGuid, event.getMemberInfoGuidList().get(0))
                .ge(hsaGrowthValueTask.getTaskValidityType() == NumberConstant.NUMBER_1, HsaGrowthCommodityTouchDetail::getOrderTime, hsaGrowthValueTask.getStartFixedTaskValidityDate())
                .isNotNull(HsaGrowthCommodityTouchDetail::getOrderNumber));
    }

    private List<HsaGrowthValueCommodityRule> getHsaGrowthValueCommodityRules(HsaGrowthValueTask hsaGrowthValueTask) {
        List<HsaGrowthValueCommodityRule> hsaGrowthValueCommodityRules;
        if (hsaGrowthValueTask.getConsumptionGoodsType() == GoodsTypeEnum.APPOINT_GOODS.getCode()) {
            //规则指定商品
            hsaGrowthValueCommodityRules = hsaGrowthValueCommodityRuleMapper.selectList(new LambdaQueryWrapper<HsaGrowthValueCommodityRule>()
                    .eq(HsaGrowthValueCommodityRule::getGrowthValueTaskGuid, hsaGrowthValueTask.getGuid()));
        } else {
            //规则分类下商品
            List<Integer> categoryId = JSONArray.parseArray(hsaGrowthValueTask.getCategoryId(), String.class).
                    stream().map(Integer::parseInt).collect(Collectors.toList());
            /**
             *
             * hsaGrowthValueCommodityRules = shopBaseService.queryCommodityCategoryIdList(enterpriseGuid, getStoreHost(hsaGrowthValueTask.getOperSubjectGuid()),
             *                     Integer.parseInt(hsaGrowthValueTask.getStrategyId()), categoryId);
             **/
            hsaGrowthValueCommodityRules = shopBaseService.queryCommodityCategoryIdListNew(Integer.parseInt(hsaGrowthValueTask.getStrategyId()), categoryId);

        }
        return hsaGrowthValueCommodityRules;
    }

    private boolean checkStore(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask) {
        if (hsaGrowthValueTask.getApplicableAllStore() == NumberConstant.NUMBER_0) {
            Map<String, HsaStoreRuleInfo> hsaGrowthValueStoreRuleMap = hsaStoreRuleInfoMapper
                    .selectList(new LambdaQueryWrapper<HsaStoreRuleInfo>()
                            .eq(HsaStoreRuleInfo::getTypeGuid, hsaGrowthValueTask.getGuid()))
                    .stream()
                    .collect(Collectors.toMap(HsaStoreRuleInfo::getStoreGuid, Function.identity(), (entity1, entity2) -> entity1));
            if (!hsaGrowthValueStoreRuleMap.containsKey(event.getStoreGuid())) {
                log.info("门店校验不通过！消费门店Guid ：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", event.getStoreGuid());
                return true;
            }
        }
        return false;
    }




    /**
     * 消费任意指定商品  要累计
     */
    private void quantityGrandProcessor(MemberGrowthValueEvent event,
                                        HsaGrowthValueTask hsaGrowthValueTask,
                                        List<ResOrderCommodity> orderCommodities) {
        //此数量只取满足成长值任务商品
        int sumNumber = orderCommodities.stream().mapToInt(ResOrderCommodity::getQuantity).sum();
        log.info("消费任意指定商品，满足件数：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", sumNumber);
        if (sumNumber >= hsaGrowthValueTask.getBuyNumber()) {
            getAbatementNum(event, hsaGrowthValueTask, sumNumber);
        }
    }


    /**
     * 消费所有指定商品  无需累计
     *
     * @param event
     * @param hsaGrowthValueTask
     */
    private void quantitySingleProcessor(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask, Integer sumNumber) {
        log.info("消费所有指定商品,满足件数：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", sumNumber);
        int num = sumNumber;
        if (num >= hsaGrowthValueTask.getBuyNumber()) {
            getAbatementNum(event, hsaGrowthValueTask, num);
        }
    }

    private void timeProcessor(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask, List<HsaGrowthCommodityTouchDetail> saveGrowthCommodityTouchDetails, List<HsaGrowthCommodityTouchDetail> updateGrowthCommodityTouchDetails, HsaGrowthCommodityTouchDetail hsaGrowthCommodityTouchDetail) {
        Integer sumNumber = 1;
        Integer taskNumber = hsaGrowthValueTask.getBuyNumber();
        log.info("购买次数累计满足完成：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", hsaGrowthCommodityTouchDetail);
        if (Objects.nonNull(hsaGrowthCommodityTouchDetail)) {
            sumNumber = hsaGrowthCommodityTouchDetail.getNumber() + sumNumber;
        }
        //根据数量取余判断是否满足购买次数 余数是0表示需要赠送 大于0表示不满足
        int flagSend = sumNumber % taskNumber;

        if (flagSend == 0) {
            addMemberGrowthValueProcessor(event, hsaGrowthValueTask, hsaGrowthValueTask.getGrowthValue());
        }
        if (Objects.nonNull(hsaGrowthCommodityTouchDetail)) {
            hsaGrowthCommodityTouchDetail.setNumber(sumNumber);
            updateGrowthCommodityTouchDetails.add(hsaGrowthCommodityTouchDetail);
            return;
        }
        saveGrowthTouchDetail(event, hsaGrowthValueTask, saveGrowthCommodityTouchDetails, sumNumber);
//        if (Objects.nonNull(hsaGrowthCommodityTouchDetail)) {
//            log.info("购买次数累计满足完成：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", hsaGrowthCommodityTouchDetail);
//            int num = hsaGrowthCommodityTouchDetail.getNumber() + sumNumber;
//            if (num >= hsaGrowthValueTask.getBuyNumber()) {
//                addMemberGrowthValueProcessor(event, hsaGrowthValueTask, hsaGrowthValueTask.getGrowthValue());
//                addUpdate(updateGrowthCommodityTouchDetails, hsaGrowthCommodityTouchDetail, hsaGrowthValueTask.getBuyNumber(), NumberConstant.NUMBER_1);
//            } else {
//                //不满足发放次数，更新触发明细
//                addUpdate(updateGrowthCommodityTouchDetails, hsaGrowthCommodityTouchDetail, sumNumber, NumberConstant.NUMBER_0);
//            }
//        } else {
//            if (sumNumber == hsaGrowthValueTask.getBuyNumber()) {
//                addMemberGrowthValueProcessor(event, hsaGrowthValueTask, hsaGrowthValueTask.getGrowthValue());
//                saveGrowthTouchDetail(event, hsaGrowthValueTask, saveGrowthCommodityTouchDetails, NumberConstant.NUMBER_0);
//            } else {
//                //不满足发放次数，但满足条件存入任务触发明细
//                saveGrowthTouchDetail(event, hsaGrowthValueTask, saveGrowthCommodityTouchDetails, sumNumber);
//            }
//        }
    }

    private List<HsaGrowthValueDetail> getHsaGrowthValueDetailsWeek(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask) {
        LocalDate currentLocalDate = LocalDate.now();
        LocalDateTime currentTodayStart = DateUtil.getStartOrEndDayOfWeek(currentLocalDate, true);
        LocalDateTime currentTodayEnd = DateUtil.getStartOrEndDayOfWeek(currentLocalDate, false);
        return hsaGrowthValueDetailMapper.selectList(new LambdaQueryWrapper<HsaGrowthValueDetail>()
                .eq(HsaGrowthValueDetail::getGrowthValueTaskId, hsaGrowthValueTask.getId())
                .eq(HsaGrowthValueDetail::getMemberInfoGuid, event.getMemberInfoGuidList().get(0))
                .ge(HsaGrowthValueDetail::getGmtCreate, currentTodayStart)
                .le(HsaGrowthValueDetail::getGmtCreate, currentTodayEnd));
    }

    private List<HsaGrowthValueDetail> getHsaGrowthValueDetailsDay(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask) {
        LocalDate currentLocalDate = LocalDate.now();
        LocalDateTime currentTodayStart = LocalDateTime.of(currentLocalDate, LocalTime.MIN);
        LocalDateTime currentTodayEnd = LocalDateTime.of(currentLocalDate, LocalTime.MAX);
        return hsaGrowthValueDetailMapper.selectList(new LambdaQueryWrapper<HsaGrowthValueDetail>()
                .eq(HsaGrowthValueDetail::getGrowthValueTaskId, hsaGrowthValueTask.getId())
                .eq(HsaGrowthValueDetail::getMemberInfoGuid, event.getMemberInfoGuidList().get(0))
                .ge(HsaGrowthValueDetail::getGmtCreate, currentTodayStart)
                .le(HsaGrowthValueDetail::getGmtCreate, currentTodayEnd));
    }

    /**
     * 计算剩余数量
     *
     * @param event
     * @param hsaGrowthValueTask
     * @param sumNumber
     * @return
     */
    private void getAbatementNum(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask, double sumNumber) {
        double divisorNum = sumNumber / (double) hsaGrowthValueTask.getBuyNumber();
        divisorNum = Math.floor(divisorNum);
        //累计赠送成长值
        int growthValue = (int) Math.round(divisorNum) * hsaGrowthValueTask.getGrowthValue();
        addMemberGrowthValueProcessor(event, hsaGrowthValueTask, growthValue);
    }

    /**
     * 校验是否每天存在满足此数据
     *
     * @param hsaGrowthValueTask hsaGrowthValueTask
     * @param touchDetailList    touchDetailList
     * @return boolean
     */
    private boolean checkDayContinuous(HsaGrowthValueTask hsaGrowthValueTask, List<HsaGrowthCommodityTouchDetail> touchDetailList) {
        for (int i = 1; i <= hsaGrowthValueTask.getBuyNumber(); i++) {
            LocalDate localDate = LocalDate.now().minusDays(i);
            LocalDateTime todayStart = LocalDateTime.of(localDate, LocalTime.MIN);
            LocalDateTime todayEnd = LocalDateTime.of(localDate, LocalTime.MAX);
            List<HsaGrowthCommodityTouchDetail> details = touchDetailList
                    .stream()
                    .filter(in -> in.getOrderTime().isAfter(todayStart) && in.getOrderTime().isBefore(todayEnd))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(details)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 校验是否每周存在满足此数据
     *
     * @param hsaGrowthValueTask hsaGrowthValueTask
     * @param touchDetailList    touchDetailList
     * @return boolean
     */
    private boolean checkWeekContinuous(HsaGrowthValueTask hsaGrowthValueTask, List<HsaGrowthCommodityTouchDetail> touchDetailList) {
        for (int i = 1; i <= hsaGrowthValueTask.getBuyNumber() - 1; i++) {
            LocalDate localDate = LocalDate.now().minusWeeks(i);
            LocalDateTime todayStart = DateUtil.getStartOrEndDayOfWeek(localDate, true);
            LocalDateTime todayEnd = DateUtil.getStartOrEndDayOfWeek(localDate, false);
            List<HsaGrowthCommodityTouchDetail> details = touchDetailList
                    .stream()
                    .filter(in -> in.getOrderTime().isAfter(todayStart) && in.getOrderTime().isBefore(todayEnd))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(details)) {
                return false;
            }
        }
        return true;
    }


    private void saveGrowthTouchDetail(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask, List<HsaGrowthCommodityTouchDetail> hsaGrowthCommodityTouchDetails, Integer num) {
        HsaGrowthCommodityTouchDetail touchDetail = new HsaGrowthCommodityTouchDetail();
        touchDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaGrowthCommodityTouchDetail.class.getSimpleName()))
                .setOperSubjectGuid(event.getOperSubjectGuid())
                .setNumber(num)
                .setMemberInfoGuid(event.getMemberInfoGuidList().get(0))
                .setGrowthValueTaskGuid(hsaGrowthValueTask.getGuid())
                .setBuyType(hsaGrowthValueTask.getBuyType());
        hsaGrowthCommodityTouchDetails.add(touchDetail);
    }

    private Boolean checkCommodityRule(List<HsaGrowthValueCommodityRule> hsaGrowthValueCommodityRules, Map<String, ResOrderCommodity> resOrderCommodityMap, List<ResOrderCommodity> orderCommodities) {
        for (HsaGrowthValueCommodityRule hsaGrowthValueCommodityRule : hsaGrowthValueCommodityRules) {
            if (resOrderCommodityMap.containsKey(String.valueOf(hsaGrowthValueCommodityRule.getCommodityCode()))) {
                orderCommodities.add(resOrderCommodityMap.get(String.valueOf(hsaGrowthValueCommodityRule.getCommodityCode())));
            }
        }
        return !CollUtil.isEmpty(orderCommodities);
    }

    /**
     * 累计商品 满足所有商品计算处理
     *
     * @param hsaGrowthValueCommodityRules hsaGrowthValueCommodityRules
     * @param resOrderCommodities          resOrderCommodities
     * @param hsaGrowthValueTask           hsaGrowthValueTask
     * @param event                        event
     * @param num                          num
     * @param quantityTouchDetailDTO       quantityTouchDetailDTO
     * @return Integer 完成件数
     */
    private Integer checkAllCommodityRule(List<HsaGrowthValueCommodityRule> hsaGrowthValueCommodityRules, List<ResOrderCommodity> resOrderCommodities, HsaGrowthValueTask hsaGrowthValueTask, MemberGrowthValueEvent event, int num, GrowthCommodityQuantityTouchDetailDTO quantityTouchDetailDTO) {
        //清洗获取累计商品
        List<HsaGrowthCommodityQuantityTouchDetail> touchDetails = getHsaGrowthCommodityQuantityTouchDetails(resOrderCommodities, hsaGrowthValueTask, event);
        //保存累计商品
        List<HsaGrowthCommodityQuantityTouchDetail> saveCommodityQuantityTouchDetails = new ArrayList<>();
        //修改累计商品
        List<HsaGrowthCommodityQuantityTouchDetail> updateCommodityQuantityTouchDetails = new ArrayList<>();
        //涉及到的商品
        List<String> quantityTouchDetails = new ArrayList<>();
        Map<String, HsaGrowthCommodityQuantityTouchDetail> hsaGrowthCommodityQuantityTouchDetailMap = touchDetails.stream().collect(Collectors.toMap(HsaGrowthCommodityQuantityTouchDetail::getCommodityCode, Function.identity(), (entity1, entity2) -> entity1));

        //循环计算数量
        List<HsaGrowthCommodityQuantityTouchDetail> commodityQuantityTouchDetails = Lists.newArrayList();
        getCommodityQuantityTouchDetails(hsaGrowthValueCommodityRules, hsaGrowthCommodityQuantityTouchDetailMap, commodityQuantityTouchDetails);
        if (CollectionUtil.isEmpty(commodityQuantityTouchDetails)) {
            return NumberConstant.NUMBER_0;
        }
        int minGiftAmount = commodityQuantityTouchDetails.stream().map(HsaGrowthCommodityQuantityTouchDetail::getCommodityNum).min(Integer::compareTo).orElse(NumberConstant.NUMBER_0);
        num = getNum(hsaGrowthValueCommodityRules, num, quantityTouchDetails, hsaGrowthCommodityQuantityTouchDetailMap, minGiftAmount);

        //计算消耗数量
        addCommodityQuantityTouchDetails(num, touchDetails, saveCommodityQuantityTouchDetails, updateCommodityQuantityTouchDetails, quantityTouchDetails, hsaGrowthCommodityQuantityTouchDetailMap);
        quantityTouchDetailDTO.setSaveCommodityQuantityTouchDetails(saveCommodityQuantityTouchDetails);
        quantityTouchDetailDTO.setUpdateCommodityQuantityTouchDetails(updateCommodityQuantityTouchDetails);
        return num;
    }

    private static void getCommodityQuantityTouchDetails(List<HsaGrowthValueCommodityRule> hsaGrowthValueCommodityRules, Map<String, HsaGrowthCommodityQuantityTouchDetail> hsaGrowthCommodityQuantityTouchDetailMap, List<HsaGrowthCommodityQuantityTouchDetail> commodityQuantityTouchDetails) {
        for (HsaGrowthValueCommodityRule touchDetail : hsaGrowthValueCommodityRules) {
            if (hsaGrowthCommodityQuantityTouchDetailMap.containsKey(touchDetail.getCommodityCode())) {
                HsaGrowthCommodityQuantityTouchDetail commodityQuantityTouchDetail = hsaGrowthCommodityQuantityTouchDetailMap.get(touchDetail.getCommodityCode());
                commodityQuantityTouchDetails.add(commodityQuantityTouchDetail);
            }
        }
    }

    private static int getNum(List<HsaGrowthValueCommodityRule> hsaGrowthValueCommodityRules, int num, List<String> quantityTouchDetails, Map<String, HsaGrowthCommodityQuantityTouchDetail> hsaGrowthCommodityQuantityTouchDetailMap, int minGiftAmount) {
        for (int i = 0; i < minGiftAmount; i++) {
            for (int x = 0; x < hsaGrowthValueCommodityRules.size(); x++) {
                HsaGrowthValueCommodityRule hsaGrowthValueCommodityRule = hsaGrowthValueCommodityRules.get(x);
                String commodityCode = String.valueOf(hsaGrowthValueCommodityRule.getCommodityCode());
                if (!hsaGrowthCommodityQuantityTouchDetailMap.containsKey(commodityCode)) {
                    break;
                } else {
                    quantityTouchDetails.add(commodityCode);
                }
                if (x == hsaGrowthValueCommodityRules.size() - 1) {
                    num = num + 1;
                }
            }
            if (num == 0) {
                break;
            }
        }
        return num;
    }

    private void addCommodityQuantityTouchDetails(int num, List<HsaGrowthCommodityQuantityTouchDetail> touchDetails, List<HsaGrowthCommodityQuantityTouchDetail> saveCommodityQuantityTouchDetails, List<HsaGrowthCommodityQuantityTouchDetail> updateCommodityQuantityTouchDetails, List<String> quantityTouchDetails, Map<String, HsaGrowthCommodityQuantityTouchDetail> hsaGrowthCommodityQuantityTouchDetailMap) {
        if (num > 0) {
            froTouchDetails(num, touchDetails, saveCommodityQuantityTouchDetails, updateCommodityQuantityTouchDetails, quantityTouchDetails, hsaGrowthCommodityQuantityTouchDetailMap);
        } else {
            froTouchDetails(touchDetails, saveCommodityQuantityTouchDetails, updateCommodityQuantityTouchDetails);
        }
    }

    private void froTouchDetails(List<HsaGrowthCommodityQuantityTouchDetail> touchDetails, List<HsaGrowthCommodityQuantityTouchDetail> saveCommodityQuantityTouchDetails, List<HsaGrowthCommodityQuantityTouchDetail> updateCommodityQuantityTouchDetails) {
        for (HsaGrowthCommodityQuantityTouchDetail touchDetail : touchDetails) {
            if (StringUtils.isNotBlank(touchDetail.getGuid())) {
                updateCommodityQuantityTouchDetails.add(touchDetail);
            } else {
                touchDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaGrowthCommodityQuantityTouchDetail.class.getSimpleName()));
                saveCommodityQuantityTouchDetails.add(touchDetail);
            }
        }
    }

    private void froTouchDetails(int num, List<HsaGrowthCommodityQuantityTouchDetail> touchDetails, List<HsaGrowthCommodityQuantityTouchDetail> saveCommodityQuantityTouchDetails, List<HsaGrowthCommodityQuantityTouchDetail> updateCommodityQuantityTouchDetails, List<String> quantityTouchDetails, Map<String, HsaGrowthCommodityQuantityTouchDetail> hsaGrowthCommodityQuantityTouchDetailMap) {
        for (HsaGrowthCommodityQuantityTouchDetail touchDetail : touchDetails) {
            if (quantityTouchDetails.contains(touchDetail.getCommodityCode())) {
                HsaGrowthCommodityQuantityTouchDetail hsaGrowthCommodityQuantityTouchDetail = hsaGrowthCommodityQuantityTouchDetailMap.get(touchDetail.getCommodityCode());
                hsaGrowthCommodityQuantityTouchDetail.setCommodityNum(hsaGrowthCommodityQuantityTouchDetail.getCommodityNum() - num);
            }
            if (StringUtils.isNotBlank(touchDetail.getGuid())) {
                updateCommodityQuantityTouchDetails.add(touchDetail);
            } else {
                touchDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaGrowthCommodityQuantityTouchDetail.class.getSimpleName()));
                saveCommodityQuantityTouchDetails.add(touchDetail);
            }
        }
    }

    /**
     * 清洗数据
     *
     * @param resOrderCommodities resOrderCommodities
     * @param hsaGrowthValueTask  hsaGrowthValueTask
     * @param event               event
     * @return List<HsaGrowthCommodityQuantityTouchDetail>
     */
    private List<HsaGrowthCommodityQuantityTouchDetail> getHsaGrowthCommodityQuantityTouchDetails(List<ResOrderCommodity> resOrderCommodities,
                                                                                                  HsaGrowthValueTask hsaGrowthValueTask,
                                                                                                  MemberGrowthValueEvent event) {
        List<HsaGrowthCommodityQuantityTouchDetail> touchDetails = Lists.newArrayList();
        resOrderCommodities.forEach(in -> gradeChangeAssembler.getHsaGrowthCommodityQuantityTouchDetailList(hsaGrowthValueTask, event, touchDetails, in));
        return touchDetails;
    }



    private boolean checkBuyNumber(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask) {
        Integer count = hsaGrowthValueDetailMapper.selectCount(new LambdaQueryWrapper<HsaGrowthValueDetail>()
                .eq(HsaGrowthValueDetail::getGrowthValueTaskId, hsaGrowthValueTask.getId())
                .eq(HsaGrowthValueDetail::getTaskAction, hsaGrowthValueTask.getTaskAction())
                .gt(HsaGrowthValueDetail::getTaskFinishTime, 0)
                .eq(HsaGrowthValueDetail::getMemberInfoGuid, event.getMemberInfoGuidList().get(0)));
        //最多完成次数
        int limitedNumber = hsaGrowthValueTask.getLimitedNumber();
        //次数是否超限
        if (limitedNumber > count || limitedNumber == NumberConstant.NUMBER_0) {
            return true;
        } else {
            log.info("次数校验不通过！已完成次数 ：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", count);
            return false;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void addTotalConsumptionCount(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask,
                                         List<HsaSuspendTaskTimeQuantum> hsaSuspendTaskTimeQuantums) {
        log.info(hsaGrowthValueTask.getTaskName() + " 成长值任务排除时间段===>" + JSONObject.toJSONString(hsaSuspendTaskTimeQuantums));
        //适用业务
        List<String> apply = Arrays.asList(hsaGrowthValueTask.getApplyBusinessJson().split(COMMA));
        if (!apply.contains(ObjectUtil.objToString(event.getOrderType()))) {
            return;
        }
        HsaMemberConsumption hsaMemberConsumption = hsaMemberConsumptionMapper.queryByGuid(event.getMemberConsumptionGuid());
        //本次消费金额不足，不记录成长值
        if (Objects.nonNull(hsaGrowthValueTask.getConsumptionIgnoreAmount())) {
            if (BigDecimalUtil.lessThan(hsaMemberConsumption.getOrderPaidAmount(), hsaGrowthValueTask.getConsumptionIgnoreAmount())) {
                return;
            }
        }
        //成长值明细记录
        List<HsaGrowthValueDetail> hsaGrowthValueDetailList = Lists.newArrayList();
        //会员信息
        List<HsaOperationMemberInfo> hsaOperationMemberInfoList = Lists.newArrayList();
        //获取会员等级
        Map<String, HsaMemberGradeInfo> hsaMemberGradeInfoMap = getStringHsaMemberGradeInfoMap(event.getMemberInfoGuidList());
        Map<String, HsaOperationMemberInfo> memberMap = event.getHsaOperationMemberInfoMap();
        LocalDateTime beginTime = buildBeginTime(hsaGrowthValueTask);
        log.info("任务" + hsaGrowthValueTask.getTaskName() + "开始时间>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}",
                com.holderzone.member.common.util.date.DateUtil.formatLocalDateTime(beginTime, DateUtil.DATETIME_FORMAT));
        for (String memberGuid : event.getMemberInfoGuidList()) {
            HsaOperationMemberInfo hsaOperationMemberInfo = memberMap.get(memberGuid);
            if (Objects.isNull(hsaOperationMemberInfo)) {
                continue;
            }
            HsaMemberGradeInfo hsaMemberGradeInfo = hsaMemberGradeInfoMap.get(hsaOperationMemberInfo.getMemberGradeInfoGuid());
            //之前所有消费次数（未退款的）
            int beforeConsumptionCount = hsaMemberConsumptionMapper.selectConsumptionCount(memberGuid,
                    event.getMemberConsumptionGuid(), beginTime, hsaGrowthValueTask.getConsumptionIgnoreAmount(), apply,
                    hsaSuspendTaskTimeQuantums);
            log.info("任务" + hsaGrowthValueTask.getTaskName() + "之前所有消费次数>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}",
                    beforeConsumptionCount);
            //额外金额赠送成长值
            List<HsaExtraAwardRule> hsaExtraAwardRules = HsaExtraAwardRuleMapper
                    .selectList(new LambdaQueryWrapper<HsaExtraAwardRule>()
                            .eq(HsaExtraAwardRule::getGrowthValueTaskGuid, hsaGrowthValueTask.getGuid())
                            .orderByAsc(HsaExtraAwardRule::getConsumptionFrequency));
            int growthValue = calculateTotalConsumptionCount(hsaExtraAwardRules, beforeConsumptionCount);
            log.info("累计消费次数共计赠送成长值：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", growthValue);
            if (growthValue == NumberConstant.NUMBER_0) {
                continue;
            }
            HsaGrowthValueDetail hsaGrowthValueDetail = packageHsaGrowthValueDetail(event, hsaGrowthValueTask,
                    hsaOperationMemberInfo, growthValue, hsaMemberGradeInfo);
            hsaGrowthValueDetailList.add(hsaGrowthValueDetail);
            hsaOperationMemberInfoList.add(hsaOperationMemberInfo);
        }
        if (CollectionUtil.isNotEmpty(hsaGrowthValueDetailList)) {
            this.saveBatch(hsaGrowthValueDetailList);
            hsaOperationMemberInfoMapper.batchUpdateGrowth(hsaOperationMemberInfoList);
        }
    }

    /**
     * 累计消费金额
     *
     * @param event              event
     * @param hsaGrowthValueTask hsaGrowthValueTask
     */
    @Transactional(rollbackFor = Exception.class)
    public void addTotalConsumption(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask,
                                    List<HsaSuspendTaskTimeQuantum> hsaSuspendTaskTimeQuantums) {
        log.info(hsaGrowthValueTask.getTaskName() + " 成长值任务排除时间段===>" + JSONObject.toJSONString(hsaSuspendTaskTimeQuantums));
        //适用业务
        List<String> apply = Arrays.asList(hsaGrowthValueTask.getApplyBusinessJson().split(COMMA));
        if (!apply.contains(ObjectUtil.objToString(event.getOrderType()))) {
            return;
        }
        //成长值明细记录
        List<HsaGrowthValueDetail> hsaGrowthValueDetailList = Lists.newArrayList();
        //会员信息
        List<HsaOperationMemberInfo> hsaOperationMemberInfoList = Lists.newArrayList();
        //获取会员等级
        Map<String, HsaMemberGradeInfo> hsaMemberGradeInfoMap = getStringHsaMemberGradeInfoMap(event.getMemberInfoGuidList());
        Map<String, HsaOperationMemberInfo> memberMap = event.getHsaOperationMemberInfoMap();
        LocalDateTime beginTime = buildBeginTime(hsaGrowthValueTask);
        log.info("任务" + hsaGrowthValueTask.getTaskName() + "开始时间>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}",
                com.holderzone.member.common.util.date.DateUtil.formatLocalDateTime(beginTime, DateUtil.DATETIME_FORMAT));
        for (String memberGuid : event.getMemberInfoGuidList()) {
            HsaOperationMemberInfo hsaOperationMemberInfo = memberMap.get(memberGuid);
            if (Objects.isNull(hsaOperationMemberInfo)) {
                continue;
            }
            HsaMemberGradeInfo hsaMemberGradeInfo = hsaMemberGradeInfoMap.get(hsaOperationMemberInfo.getMemberGradeInfoGuid());
            //之前所有消费（未退款的）
            BigDecimal beforeConsumption = hsaMemberConsumptionMapper.selectByMemberGuid(memberGuid,
                    event.getMemberConsumptionGuid(), beginTime, apply, hsaSuspendTaskTimeQuantums);
            log.info("任务" + hsaGrowthValueTask.getTaskName() + "之前所有消费>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}",
                    beforeConsumption);
            //本次消费
            HsaMemberConsumption hsaMemberConsumption = hsaMemberConsumptionMapper.queryByGuid(event.getMemberConsumptionGuid());
            //TODO 现在暂时去订单金额 两个地方需要修改
            if (Objects.isNull(hsaMemberConsumption) || Objects.isNull(hsaMemberConsumption.getOrderPaidAmount())) {
                continue;
            }
            //本次消费金额
            BigDecimal currentConsumption = hsaMemberConsumption.getOrderPaidAmount();
            //额外金额赠送成长值
            List<HsaExtraAwardRule> hsaExtraAwardRules = HsaExtraAwardRuleMapper
                    .selectList(new LambdaQueryWrapper<HsaExtraAwardRule>()
                            .eq(HsaExtraAwardRule::getGrowthValueTaskGuid, hsaGrowthValueTask.getGuid())
                            .orderByAsc(HsaExtraAwardRule::getConsumptionAmount));
            if (CollectionUtil.isEmpty(hsaExtraAwardRules)) {
                continue;
            }
            //赠送的成长值
            List<Integer> growthValue = calculateTotalConsumption(hsaExtraAwardRules, BigDecimalUtil.nonNullValue(beforeConsumption),
                    currentConsumption);
            if (CollUtil.isEmpty(growthValue)) {
                continue;
            }

            dealGrowthValueList(event, hsaGrowthValueTask, growthValue, hsaOperationMemberInfo, hsaMemberGradeInfo, hsaGrowthValueDetailList);

            hsaOperationMemberInfoList.add(hsaOperationMemberInfo);
        }
        if (CollUtil.isNotEmpty(hsaGrowthValueDetailList)) {
            this.saveBatch(hsaGrowthValueDetailList);
            hsaOperationMemberInfoMapper.batchUpdateGrowth(hsaOperationMemberInfoList);
        }
    }

    private void dealGrowthValueList(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask, List<Integer> growthValue, HsaOperationMemberInfo hsaOperationMemberInfo, HsaMemberGradeInfo hsaMemberGradeInfo, List<HsaGrowthValueDetail> hsaGrowthValueDetailList) {
        for (int i = 0; i < growthValue.size(); i++) {
            HsaGrowthValueDetail hsaGrowthValueDetail = packageHsaGrowthValueDetail(event, hsaGrowthValueTask,
                    hsaOperationMemberInfo, growthValue.get(i), hsaMemberGradeInfo);
            //需要排序
            if (CollUtil.isNotEmpty(hsaGrowthValueDetailList)) {
                HsaGrowthValueDetail growthValueDetail = hsaGrowthValueDetailList.get(i - 1);

                hsaGrowthValueDetail.setGmtCreate(growthValueDetail.getGmtCreate().plusMinutes(1));
            }
            hsaGrowthValueDetailList.add(hsaGrowthValueDetail);
        }
    }

    /**
     * 封装成长值任务明细
     *
     * @param event                  系统成长值批量调整Event
     * @param hsaGrowthValueTask     成长值任务
     * @param hsaOperationMemberInfo 运营主体会员信息表
     * @param growthValue
     * @return
     */
    private HsaGrowthValueDetail packageHsaGrowthValueDetail(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask,
                                                             HsaOperationMemberInfo hsaOperationMemberInfo, int growthValue,
                                                             HsaMemberGradeInfo hsaMemberGradeInfo) {
        LocalDateTime dateTime = event.getDateTime();
        //有效期计算
        LocalDateTime localDateTime = getLocalDateTime(hsaGrowthValueTask, dateTime);
        //已完成次数
        Integer num = hsaGrowthValueDetailMapper.selectCount(new LambdaQueryWrapper<HsaGrowthValueDetail>()
                .eq(HsaGrowthValueDetail::getGrowthValueTaskId, hsaGrowthValueTask.getId())
                .eq(HsaGrowthValueDetail::getMemberInfoGuid, hsaOperationMemberInfo.getGuid())
                .eq(HsaGrowthValueDetail::getTaskAction, hsaGrowthValueTask.getTaskAction()));

        HsaGrowthValueDetail hsaGrowthValueDetail = gradeChangeAssembler.getHsaGrowthValueDetail(event, hsaGrowthValueTask, hsaOperationMemberInfo, localDateTime, dateTime, num);

        int oldGrowthValue = growthValue;
        //是否获取翻倍成长值
        growthValue = isDoubleGrowthValue(oldGrowthValue, hsaOperationMemberInfo, hsaGrowthValueDetail);

        //获取会员卡翻倍成长值
        growthValue = isDoubleCardGrowthValue(oldGrowthValue, hsaOperationMemberInfo, hsaGrowthValueDetail, event, growthValue);
        hsaGrowthValueDetail.setGrowthValue(growthValue);
        hsaGrowthValueDetail.setMemberInfoGuid(hsaOperationMemberInfo.getGuid());
        hsaOperationMemberInfo.setMemberGrowthValue(hsaOperationMemberInfo.getMemberGrowthValue() + growthValue);
        hsaGrowthValueDetail.setRemainGrowthValue(hsaOperationMemberInfo.getMemberGrowthValue());

        hsaGrowthValueDetail.setRecordRemainGrowthValue(growthValue);
        gradeChangeAssembler.setCurrentMemberLevel(hsaOperationMemberInfo, hsaMemberGradeInfo, hsaGrowthValueDetail);
        return hsaGrowthValueDetail;
    }

    /**
     * 累计消费计算赠送多少成长值
     *
     * @param hsaExtraAwardRules 额外奖励规则
     * @param beforeConsumption  之前消费金额
     * @param currentConsumption 本次消费金额
     * @return 操作结果
     */
    private List<Integer> calculateTotalConsumption(List<HsaExtraAwardRule> hsaExtraAwardRules, BigDecimal beforeConsumption,
                                                    BigDecimal currentConsumption) {

        List<Integer> growthValueList = new ArrayList<>();
        for (HsaExtraAwardRule hsaExtraAwardRule : hsaExtraAwardRules) {
            int growthValue = 0;
            //之前消费的金额大于等于当前赠送成长值规则，表示该规则下已经赠送过了
            if (BigDecimalUtil.greaterEqual(beforeConsumption, hsaExtraAwardRule.getConsumptionAmount())) {
                log.info("已经赠送过规则：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", hsaExtraAwardRule);
                continue;
            }
            //累计消费金额大于等于当前规则下的金额，赠送成长值
            if (BigDecimalUtil.lessEqual(hsaExtraAwardRule.getConsumptionAmount(), beforeConsumption.add(currentConsumption))) {
                log.info("赠送成长值规则：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", hsaExtraAwardRule);
                growthValue = growthValue + hsaExtraAwardRule.getGrowthValue();
                log.info("赠送成长值数量：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", growthValue);
                growthValueList.add(growthValue);
            }
        }
        log.info("总计赠送成长值数量：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", growthValueList.stream().mapToInt(Integer::intValue).sum());
        return growthValueList;
    }

    private int calculateTotalConsumptionCount(List<HsaExtraAwardRule> hsaExtraAwardRules, int beforeConsumptionCount) {
        int growthValue = 0;
        for (HsaExtraAwardRule hsaExtraAwardRule : hsaExtraAwardRules) {
            //之前消费的次数大于等于当前赠送成长值次数规则，表示该规则下已经赠送过了
            if (beforeConsumptionCount >= hsaExtraAwardRule.getConsumptionFrequency()) {
                log.info("累计消费次数已经赠送过规则：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", hsaExtraAwardRule);
                continue;
            }
            //累计消费金额大于等于当前规则下的金额，赠送成长值
            if ((beforeConsumptionCount + NumberConstant.NUMBER_1) >= hsaExtraAwardRule.getConsumptionFrequency()) {
                growthValue = growthValue + hsaExtraAwardRule.getGrowthValue();
                log.info("累计消费次数获得成长值：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", growthValue);
                break;
            }
        }
        return growthValue;
    }


    private LocalDateTime buildBeginTime(HsaGrowthValueTask hsaGrowthValueTask) {
        LocalDateTime beginTime = LocalDateTime.now();
        LocalDate beginDate = beginTime.toLocalDate();
        switch (Objects.requireNonNull(DataUnitEnum.Enum(hsaGrowthValueTask.getTotalPeriodType()))) {
            case FOREVER:
                beginTime = hsaGrowthValueTask.getGmtCreate();
                break;
            case DAY:
                beginTime = com.holderzone.member.common.util.date.DateUtil.formatDayPeriod(beginDate, hsaGrowthValueTask.getTotalPeriod());
                break;
            case WEEK:
                beginTime = com.holderzone.member.common.util.date.DateUtil.formatWeekPeriod(beginDate, hsaGrowthValueTask.getTotalPeriod());
                beginTime = LocalDateTime.of(beginTime.getYear(), beginTime.getMonth(), beginTime.getDayOfMonth(), 0, 0, 0);
                break;
            case MONTH:
                beginTime = com.holderzone.member.common.util.date.DateUtil.formatMonthPeriod(beginDate, hsaGrowthValueTask.getTotalPeriod());
                beginTime = LocalDateTime.of(beginTime.getYear(), beginTime.getMonth(), beginTime.getDayOfMonth(), 0, 0, 0);
                break;
            case YEAR:
                beginTime = com.holderzone.member.common.util.date.DateUtil.formatYearPeriod(beginDate, hsaGrowthValueTask.getTotalPeriod());
                beginTime = LocalDateTime.of(beginTime.getYear(), beginTime.getMonth(), beginTime.getDayOfMonth(), 0, 0, 0);
                break;
            default:
                return hsaGrowthValueTask.getGmtCreate();
        }
        //成长值创建时间大于计算的开始时间
        if (hsaGrowthValueTask.getGmtCreate().isAfter(beginTime)) {
            beginTime = hsaGrowthValueTask.getGmtCreate();
        } else if (hsaGrowthValueTask.getTotalPeriodType() == DataUnitEnum.DAY.getCode() &&
                beginTime.toLocalDate().equals(hsaGrowthValueTask.getGmtCreate().toLocalDate()) && LocalDateTime.now().isBefore(beginTime)) {
            beginTime = hsaGrowthValueTask.getGmtCreate();
        }
        return beginTime;
    }


    /**
     * 计算可赠送成长值
     *
     * @param hsaGrowthValueTask hsaGrowthValueTask
     * @return int
     */
    private int calculateSumGrowth(HsaGrowthValueTask hsaGrowthValueTask, MemberGrowthValueEvent event) {
        //基础赠送成长值
        int baseGrowthValue = 0;
        //额外赠送成长值
        int extraGrowthValue = 0;
        //消费金额
        BigDecimal orderPaidAmount = event.getOrderAmount();
        //基础金额赠送成长值
        if (orderPaidAmount.compareTo(hsaGrowthValueTask.getAmount()) >= 0) {
            int num = orderPaidAmount.divide(hsaGrowthValueTask.getAmount(), 3, RoundingMode.HALF_UP).intValue();
            if (num > 0) {
                baseGrowthValue = num * hsaGrowthValueTask.getGrowthValue();
            }

        }
        //额外金额赠送成长值
        List<HsaExtraAwardRule> hsaExtraAwardRules = HsaExtraAwardRuleMapper.selectList(new LambdaQueryWrapper<HsaExtraAwardRule>()
                .eq(HsaExtraAwardRule::getGrowthValueTaskGuid, hsaGrowthValueTask.getGuid()));
        if (CollUtil.isNotEmpty(hsaExtraAwardRules)) {
            HsaExtraAwardRule hsaExtraAwardRule = calculateExtraAmount(orderPaidAmount, hsaExtraAwardRules);
            if (Objects.nonNull(hsaExtraAwardRule) && StringUtils.isNotEmpty(hsaExtraAwardRule.getGuid())) {
                extraGrowthValue = hsaExtraAwardRule.getGrowthValue();
            }
        }
        return baseGrowthValue + extraGrowthValue;
    }

    /**
     * 获取额外满足条件
     *
     * @param orderPaidAmount    orderPaidAmount
     * @param hsaExtraAwardRules hsaExtraAwardRules
     * @return HsaExtraAwardRule
     */
    private HsaExtraAwardRule calculateExtraAmount(BigDecimal orderPaidAmount, List<HsaExtraAwardRule> hsaExtraAwardRules) {
        HsaExtraAwardRule awardRule = null;
        for (int i = 0; i < hsaExtraAwardRules.size(); i++) {
            if (orderPaidAmount.compareTo(hsaExtraAwardRules.get(i).getConsumptionAmount()) < 0) {
                if (i == 0) {
                    awardRule = new HsaExtraAwardRule();
                } else {
                    awardRule = hsaExtraAwardRules.get(i - 1);
                }
                break;
            }
            if (i == hsaExtraAwardRules.size() - 1) {
                awardRule = hsaExtraAwardRules.get(i);
            }
        }
        return awardRule;
    }


    /**
     * 是否满足任务条件 单笔消费
     *
     * @param event              event
     * @param hsaGrowthValueTask hsaGrowthValueTask
     * @return boolean
     */
    private boolean checkTaskCondition(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask) {
        //适用业务
        List<String> apply = Arrays.asList(hsaGrowthValueTask.getApplyBusinessJson().split(","));
        if (!apply.contains(event.getOrderType() + "")) {
            return false;
        }
        //次数校验
        return getTaskNum(hsaGrowthValueTask, event);
    }

    /**
     * 成长值任务校验
     *
     * @param hsaGrowthValueTask hsaGrowthValueTask
     * @return Boolean
     */
    private Boolean getTaskNum(HsaGrowthValueTask hsaGrowthValueTask, MemberGrowthValueEvent event) {
        if (hsaGrowthValueTask.getGetCountType() == GetCountTypeEnum.UN_LIMITED.getCode()) {
            return true;
        } else if (hsaGrowthValueTask.getGetCountType() == GetCountTypeEnum.FIXED_LIMITED.getCode()) {
            //固定次数
            Integer count = hsaGrowthValueDetailMapper.selectCount(new LambdaQueryWrapper<HsaGrowthValueDetail>()
                    .eq(HsaGrowthValueDetail::getGrowthValueTaskId, hsaGrowthValueTask.getId())
                    .eq(HsaGrowthValueDetail::getTaskAction, hsaGrowthValueTask.getTaskAction())
                    .eq(HsaGrowthValueDetail::getMemberInfoGuid, event.getMemberInfoGuidList().get(0)));
            return hsaGrowthValueTask.getLimitedNumber() > count;
        } else {
            //周期
            if (hsaGrowthValueTask.getPeriodLimitedType() == DataUnitEnum.DAY.getCode()) {
                return checkTime(hsaGrowthValueTask,
                        DateUtil.getDayStartTime(),
                        DateUtil.getDayEndTime(),
                        hsaGrowthValueTask.getTaskAction(),
                        event);
            } else if (hsaGrowthValueTask.getPeriodLimitedType() == DataUnitEnum.WEEK.getCode()) {
                return checkTime(hsaGrowthValueTask,
                        DateUtil.getWeekStartTime(),
                        DateUtil.getWeekEndTime(),
                        hsaGrowthValueTask.getTaskAction(),
                        event);
            } else if (hsaGrowthValueTask.getPeriodLimitedType() == DataUnitEnum.MONTH.getCode()) {
                return checkTime(hsaGrowthValueTask,
                        DateUtil.getMonthStartTime(),
                        DateUtil.getMonthEndTime(),
                        hsaGrowthValueTask.getTaskAction(),
                        event);
            }
        }
        return false;
    }

    /**
     * 是否满足任务条件 单笔充值
     *
     * @param event              event
     * @param hsaGrowthValueTask hsaGrowthValueTask
     * @return boolean
     */
    private boolean checkTaskSingleRecharge(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask) {
        //次数校验
        return getTaskNum(hsaGrowthValueTask, event);
    }

    private boolean checkTime(HsaGrowthValueTask hsaGrowthValueTask, LocalDateTime sta, LocalDateTime end, Integer taskAction, MemberGrowthValueEvent event) {
        Integer num = hsaGrowthValueDetailMapper.selectCount(new LambdaQueryWrapper<HsaGrowthValueDetail>()
                .eq(HsaGrowthValueDetail::getGrowthValueTaskId, hsaGrowthValueTask.getId())
                .eq(HsaGrowthValueDetail::getMemberInfoGuid, event.getMemberInfoGuidList().get(0))
                .eq(HsaGrowthValueDetail::getTaskAction, taskAction)
                .ge(HsaGrowthValueDetail::getGmtCreate, sta)
                .le(HsaGrowthValueDetail::getGmtCreate, end));
        return hsaGrowthValueTask.getLimitedNumber() > num;
    }

    /**
     * 充值任务处理
     *
     * @param event event
     */
    @Override
    public void rechargeTaskBusinessProcessor(MemberGrowthValueEvent event) {
        List<HsaGrowthValueTask> hsaGrowthValueTasks = hsaGrowthValueTaskMapper.selectList(new LambdaQueryWrapper<HsaGrowthValueTask>()
                .eq(HsaGrowthValueTask::getOperSubjectGuid, event.getOperSubjectGuid())
                .eq(HsaGrowthValueTask::getTaskType, TaskTypeEnum.RECHARGE_TASK.getCode())
                .eq(HsaGrowthValueTask::getIsEnable, BooleanEnum.TRUE.getCode())
                .eq(HsaGrowthValueTask::getIsDelete, BooleanEnum.FALSE.getCode()));
        if (CollUtil.isEmpty(hsaGrowthValueTasks)) {
            return;
        }
        for (HsaGrowthValueTask hsaGrowthValueTask : hsaGrowthValueTasks) {
            if (hsaGrowthValueTask.getTaskValidityType() == TaskValidityTypeEnum.FIXED_VALIDITY.getCode()
                    && (LocalDateTime.now().isBefore(hsaGrowthValueTask.getStartFixedTaskValidityDate())
                    || LocalDateTime.now().isAfter(hsaGrowthValueTask.getEndFixedTaskValidityDate()))) {
                    continue;

            }
            dealGrowthValueProcessor(event, hsaGrowthValueTask);

        }
        //等级权益变化
        memberQueueThreadExecutor.execute(() -> sendMemberGradeChange(event));
    }

    private void dealGrowthValueProcessor(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask) {
        if (hsaGrowthValueTask.getTaskAction() == TaskActionEnum.SINGLE_RECHARGE_AMOUNT.getCode()) {
            log.info("成长值单笔消费金额任务event：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", event);
            if (checkTaskSingleRecharge(event, hsaGrowthValueTask)) {
                //计算可赠送成长值
                int sumGrowth = calculateSumGrowth(hsaGrowthValueTask, event);
                if (sumGrowth > 0) {
                    addMemberGrowthValueProcessor(event, hsaGrowthValueTask, sumGrowth);
                }
            }
        }
        if (hsaGrowthValueTask.getTaskAction() == TaskActionEnum.TOTAL_RECHARGE_AMOUNT.getCode()) {
            log.info("成长值累计消费金额任务名称：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", hsaGrowthValueTask.getTaskName());
            log.info("成长值累计消费金额任务event：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", event);
            addTotalRechargeTask(event, hsaGrowthValueTask);
        }
    }


    /**
     * 添加累计充值成长值任务
     *
     * @param event              系统成长值批量调整Event
     * @param hsaGrowthValueTask 成长值任务
     */
    private void addTotalRechargeTask(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask) {
        List<HsaSuspendTaskTimeQuantum> suspendTaskTimeQuantums = hsaSuspendTaskTimeQuantumMapper
                .selectList(new LambdaQueryWrapper<HsaSuspendTaskTimeQuantum>()
                        .eq(HsaSuspendTaskTimeQuantum::getTaskType, GrowthValueOrIntegralEnum.GROWTH_VALUE.getCode())
                        .eq(HsaSuspendTaskTimeQuantum::getTaskGuid, hsaGrowthValueTask.getGuid())
                        .isNotNull(HsaSuspendTaskTimeQuantum::getSuspendStartTime)
                        .isNotNull(HsaSuspendTaskTimeQuantum::getSuspendEndTime));
        log.info("成长值累计充值任务排除时间段=======>" + JSONObject.toJSONString(suspendTaskTimeQuantums));
        LocalDateTime beginTime = buildBeginTime(hsaGrowthValueTask);
        log.info("任务" + hsaGrowthValueTask.getTaskName() + "开始时间>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}",
                com.holderzone.member.common.util.date.DateUtil.formatLocalDateTime(beginTime, DateUtil.DATETIME_FORMAT));
        //成长值明细记录
        List<HsaGrowthValueDetail> hsaGrowthValueDetailList = Lists.newArrayList();
        //会员信息
        List<HsaOperationMemberInfo> hsaOperationMemberInfoList = Lists.newArrayList();
        //获取会员等级
        Map<String, HsaMemberGradeInfo> hsaMemberGradeInfoMap = getStringHsaMemberGradeInfoMap(event.getMemberInfoGuidList());
        Map<String, HsaOperationMemberInfo> memberMap = event.getHsaOperationMemberInfoMap();
        for (String memberGuid : event.getMemberInfoGuidList()) {
            HsaOperationMemberInfo hsaOperationMemberInfo = memberMap.get(memberGuid);
            if (Objects.isNull(hsaOperationMemberInfo)) {
                continue;
            }
            HsaMemberGradeInfo hsaMemberGradeInfo = hsaMemberGradeInfoMap.get(hsaOperationMemberInfo.getMemberGradeInfoGuid());
            //之前充值金额
            BigDecimal beforeRechargeAmount = hsaMemberConsumptionMapper.selectRechargeAmountMemberGuid(memberGuid,
                    event.getMemberConsumptionGuid(), beginTime, suspendTaskTimeQuantums);
            log.info("之前充值金额：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", beforeRechargeAmount);
            //额外金额赠送成长值
            List<HsaExtraAwardRule> hsaExtraAwardRules = HsaExtraAwardRuleMapper
                    .selectList(new LambdaQueryWrapper<HsaExtraAwardRule>()
                            .eq(HsaExtraAwardRule::getGrowthValueTaskGuid, hsaGrowthValueTask.getGuid())
                            .orderByAsc(HsaExtraAwardRule::getConsumptionAmount));
            List<Integer> growthValue = calculateTotalConsumption(hsaExtraAwardRules, BigDecimalUtil.nonNullValue(beforeRechargeAmount),
                    event.getOrderAmount());
            log.info("最终赠送成长值数量：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", growthValue.stream().mapToInt(Integer::intValue).sum());
            if (CollUtil.isEmpty(growthValue)) {
                continue;
            }
            dealGrowthValueList(event, hsaGrowthValueTask, growthValue, hsaOperationMemberInfo, hsaMemberGradeInfo, hsaGrowthValueDetailList);


            hsaOperationMemberInfoList.add(hsaOperationMemberInfo);
        }

        if (CollectionUtil.isNotEmpty(hsaGrowthValueDetailList)) {
            this.saveBatch(hsaGrowthValueDetailList);
            hsaOperationMemberInfoMapper.batchUpdateGrowth(hsaOperationMemberInfoList);
        }
    }

    /**
     * 成长值回收
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void growthValueRecycle() {
        LocalDateTime now = LocalDateTime.now();
        List<HsaGrowthValueDetail> hsaGrowthValueDetails = hsaGrowthValueDetailMapper.selectList(new LambdaQueryWrapper<HsaGrowthValueDetail>()
                .eq(HsaGrowthValueDetail::getGrowthValidity, NumberConstant.NUMBER_1)
                .gt(HsaGrowthValueDetail::getRecordRemainGrowthValue, NumberConstant.NUMBER_0)
                .lt(HsaGrowthValueDetail::getGrowthValidityDate, now));
        log.info("成长值失效记录：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", hsaGrowthValueDetails);
        if (CollectionUtil.isEmpty(hsaGrowthValueDetails)) {
            return;
        }
        //数据整理
        Map<String, List<HsaGrowthValueDetail>> hsaGrowthValueDetailMap = hsaGrowthValueDetails.stream().collect(Collectors.groupingBy(HsaGrowthValueDetail::getMemberInfoGuid));
        List<String> memberInfoGuid = hsaGrowthValueDetails.stream().map(HsaGrowthValueDetail::getMemberInfoGuid).collect(Collectors.toList());
        log.info("会员信息Guid：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", memberInfoGuid);
        Map<String, HsaOperationMemberInfo> hsaOperationMemberInfoMap = hsaOperationMemberInfoService.checkMemberGuid(memberInfoGuid)
                .stream()
                .collect(Collectors.toMap(HsaOperationMemberInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));
        Set<Long> idLong = hsaGrowthValueDetails.stream().map(HsaGrowthValueDetail::getGrowthValueTaskId).map(Long::valueOf).collect(Collectors.toSet());
        Map<Long, HsaGrowthValueTask> hsaGrowthValueTaskMap = hsaGrowthValueTaskMapper.selectList(new LambdaQueryWrapper<HsaGrowthValueTask>()
                        .in(HsaGrowthValueTask::getId, idLong))
                .stream()
                .collect(Collectors.toMap(HsaGrowthValueTask::getId, Function.identity(), (entity1, entity2) -> entity1));

        //获取会员等级
        Map<String, HsaMemberGradeInfo> hsaMemberGradeInfoMap = getStringHsaMemberGradeInfoMap(memberInfoGuid);
        //记录持久化
        List<HsaGrowthValueDetail> updateGrowthValueDetailList = Lists.newArrayList();
        List<HsaOperationMemberInfo> hsaOperationMemberInfos = Lists.newArrayList();
        List<HsaGrowthValueDetail> saveGrowthValueDetailList = Lists.newArrayList();
        hsaGrowthValueDetailMap.forEach((key, value) -> {
            HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMap.get(key);
            if (Objects.nonNull(hsaOperationMemberInfo)) {
                HsaMemberGradeInfo hsaMemberGradeInfo = hsaMemberGradeInfoMap.get(hsaOperationMemberInfo.getMemberGradeInfoGuid());
                Integer sum = value.stream().mapToInt(HsaGrowthValueDetail::getRecordRemainGrowthValue).sum();
                value.forEach(in -> in.setRecordRemainGrowthValue(NumberConstant.NUMBER_0));
                hsaOperationMemberInfo.setMemberGrowthValue(Math.max(hsaOperationMemberInfo.getMemberGrowthValue() - sum, NumberConstant.NUMBER_0));
                if (CollUtil.isNotEmpty(hsaGrowthValueTaskMap) && hsaGrowthValueTaskMap.containsKey(Long.valueOf(value.get(0).getGrowthValueTaskId()))) {
                    HsaGrowthValueTask hsaGrowthValueTask = hsaGrowthValueTaskMap.get(Long.valueOf(value.get(0).getGrowthValueTaskId()));
                    //产生记录
                    HsaGrowthValueDetail hsaGrowthValueDetail = gradeChangeAssembler.getHsaGrowthValueDetail(hsaGrowthValueTask, sum, hsaOperationMemberInfo, hsaMemberGradeInfo);
                    saveGrowthValueDetailList.add(hsaGrowthValueDetail);
                }
                updateGrowthValueDetailList.addAll(value);
                hsaOperationMemberInfos.add(hsaOperationMemberInfo);
            }
        });
        log.info("saveGrowthValueDetailList：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", JSON.toJSONString(saveGrowthValueDetailList));
        if (CollUtil.isNotEmpty(saveGrowthValueDetailList)) {
            hsaGrowthValueDetailService.saveBatch(saveGrowthValueDetailList);
            hsaOperationMemberInfoMapper.batchUpdateGrowth(hsaOperationMemberInfos);
            hsaGrowthValueDetailMapper.batchUpdate(updateGrowthValueDetailList);
            //等级权益变化
            MemberGrowthValueEvent event = new MemberGrowthValueEvent();
            event.setMemberInfoGuidList(hsaOperationMemberInfos.stream().map(HsaOperationMemberInfo::getGuid).collect(Collectors.toList()));
            event.setSource(SourceTypeEnum.ADD_BACKGROUND.getCode());
            event.setOperSubjectGuid(hsaOperationMemberInfos.get(0).getOperSubjectGuid());
            memberQueueThreadExecutor.execute(() -> sendMemberGradeChange(event));
        }
    }


}
