package com.holderzone.member.queue.dto.grade;


import lombok.*;

import java.io.Serializable;


/**
 * @description: 判断是否满足翻倍成长值
 * @author: z<PERSON><PERSON>
 * @create: 2021-12-31 11:15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DoubleValueRequest implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 成长值
     */
    private Integer growthValue;

    /**
     * 等级权益guid
     */
    private String gradeEquitiesGuid;

    /**
     * 会员guid
     */
    private String memberInfoGuid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 翻倍的成长值
     */
    private Long doubleValue;

    /**
     * 用户剩余可以的累计翻倍值
     */
    private Integer remainingValue;

    private Integer businessType;

}
