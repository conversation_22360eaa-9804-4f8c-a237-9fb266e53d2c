package com.holderzone.member.queue.dto.label;

import com.holderzone.member.common.dto.user.HeaderUserInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2021-12-23 11:27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RelationLabelDTO  {

    /**
     * 用户信息
     */
    private HeaderUserInfo userInfo;

    private Set<String> memberGuids;
}
