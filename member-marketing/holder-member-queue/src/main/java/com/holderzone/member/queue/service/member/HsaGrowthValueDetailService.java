package com.holderzone.member.queue.service.member;


import com.holderzone.member.queue.config.mybatis.IHolderBaseService;
import com.holderzone.member.queue.dto.member.MemberGrowthValueEvent;
import com.holderzone.member.queue.entity.HsaGrowthValueDetail;

/**
 * <p>
 * 成长值明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-12
 */
public interface HsaGrowthValueDetailService extends IHolderBaseService<HsaGrowthValueDetail> {

    /**
     * 基础任务处理
     *
     * @param event event
     */
    void baseTaskBusinessProcessor(MemberGrowthValueEvent event);

    /**
     * 消费任务处理
     *
     * @param event event
     */
    void consumptionTaskBusinessProcessor(MemberGrowthValueEvent event);

    /**
     * 充值任务处理
     *
     * @param event event
     */
    void rechargeTaskBusinessProcessor(MemberGrowthValueEvent event);

    /**
     * 成长值回收
     */
    void growthValueRecycle();
}
