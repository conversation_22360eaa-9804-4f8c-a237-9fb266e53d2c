package com.holderzone.member.queue.dto.activity;

import com.holderzone.member.common.dto.event.SubsidyActivityEvent;
import com.holderzone.member.queue.entity.HsaCardBaseInfo;
import com.holderzone.member.queue.entity.HsaSubsidyActivity;
import com.holderzone.member.queue.entity.HsaSubsidyActivityRecord;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

@Data
public class SendCheckSubsidyDTO {

    //补贴活动
    private Map<String, HsaSubsidyActivity> hsaSubsidyActivityMap;

    //发放记录
    private Map<String, HsaSubsidyActivityRecord> hsaSubsidyActivityRecordMap;

    //卡基础数据
    private Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap;

    //获取对应活动
    private HsaSubsidyActivity hsaSubsidyActivity;

    private SubsidyActivityEvent subsidyActivityEvent;

    private LocalDateTime now;
}
