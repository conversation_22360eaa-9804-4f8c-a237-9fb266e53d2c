package com.holderzone.member.queue.event.processor;

import com.google.common.base.Verify;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.event.PushCrmOrderEvent;
import com.holderzone.member.common.util.transaction.SpringContextUtils;
import com.holderzone.member.queue.service.order.CrmOrderService;
import lombok.extern.slf4j.Slf4j;

/**
 * 推送订单到crm 事件处理
 */
@Slf4j
public class PushCrmOrderProcessor extends AbstractProcessor<PushCrmOrderEvent> {

    private CrmOrderService crmOrderService;

    public PushCrmOrderProcessor(PushCrmOrderEvent event) {
        super(event);
        this.crmOrderService = SpringContextUtils.getBean(CrmOrderService.class);
    }

    @Override
    protected String getLogTitle() {
        return "pushCrmOrder";
    }

    @Override
    protected void validate() {
        Verify.verify(event != null);
    }


    @Override
    protected void process() {
        ThreadLocalCache.put(JacksonUtils.writeValueAsString(event.getUserInfo()));
        crmOrderService.pushCrmOrder(event.getOrder(), event.getDetails(), event.getOrderReceiverAd(), event.getPushStock());
    }
}
