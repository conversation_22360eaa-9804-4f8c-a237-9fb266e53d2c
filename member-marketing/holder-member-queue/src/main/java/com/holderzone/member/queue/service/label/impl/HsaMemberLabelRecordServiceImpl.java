package com.holderzone.member.queue.service.label.impl;

import com.holderzone.member.queue.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.queue.entity.HsaMemberLabelRecord;
import com.holderzone.member.queue.mapper.HsaMemberLabelRecordMapper;
import com.holderzone.member.queue.service.label.HsaMemberLabelRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2021-12-22 19:20
 */
@Slf4j
@Service
public class HsaMemberLabelRecordServiceImpl  extends HolderBaseServiceImpl<HsaMemberLabelRecordMapper, HsaMemberLabelRecord>
        implements HsaMemberLabelRecordService {

    @Override
    public void saveBatch(List<HsaMemberLabelRecord> saveMemberLabelRecordList){

        this.saveBatch(saveMemberLabelRecordList);
    }
}
