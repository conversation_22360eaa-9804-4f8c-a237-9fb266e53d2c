package com.holderzone.member.queue.dto.member;

import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.growth.SumValueChangeEnum;
import com.holderzone.member.queue.entity.HsaOperationMemberInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 系统成长值批量调整Event
 *
 * <AUTHOR>
 * @since 2021-11-23 11:14:31
 */
@Data
@ApiModel(value = "MemberGrowthValueEvent", description = "会员成长值调整请求模型")
public class MemberGrowthValueEvent implements Serializable {

    @ApiModelProperty(value = "会员GUID", required = true)
    private List<String> memberInfoGuidList;

    /**
     * 运营主体
     */
    private String operSubjectGuid;

    @ApiModelProperty(value = "成长值类型，0增加，1减少", required = true)
    private Integer growthValueType;

    @ApiModelProperty(value = "成长值", required = true)
    private Integer growthValue;

    @ApiModelProperty(value = "备注", required = true)
    private String remark;

    @ApiModelProperty(value = "headerUserInfo", required = true)
    private HeaderUserInfo headerUserInfo;

    /**
     * 来源
     */
    @ApiModelProperty(value = "来源", required = true)
    private Integer source;

    /**
     * 成长值变动类型（1初始成长值，2系统调整，3成长值失效，4消费退款，5成长值任务
     *
     * @see SumValueChangeEnum
     */
    private Integer changeType;

    /**
     * 任务类型 0:基础任务 1:消费任务 2:充值任务
     *
     * @see com.holderzone.member.common.enums.growth.TaskTypeEnum
     */
    private Integer taskType;

    /**
     * 任务guid (来源为成长值任务时存在)
     */
    private String GrowthValueTaskGuid;

    /**
     * 会员
     */
    private Map<String, HsaOperationMemberInfo> hsaOperationMemberInfoMap;

    /**
     * 时间固定
     */
    private LocalDateTime dateTime;

    /**
     * 会员消费记录GUID
     */
    private String memberConsumptionGuid;

    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 订单金额
     */
    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;

    /**
     * 门店来源guid
     */
    private String storeGuid;

    /**
     * 会员卡
     */
    @ApiModelProperty("会员卡")
    private String cardGuid;

    /**
     * 门店来源名称
     */
    private String storeName;

    /**
     * 操作人员账号名字
     */
    private String operatorAccountName;

    /**
     * 角色类型
     *
     * @see com.holderzone.member.common.enums.member.RoleTypeEnum
     */
    private String roleType;

    /**
     * 会员等级GUID，-1为无等级
     */
    private String memberInfoGradeGuid;

    /**
     * 有效期数量
     */
    private Integer num;

    /**
     * 有效期单位：3月 4年
     */
    private Integer unit;

    private Integer businessNum;

    /**
     * 业务字段  提前生成长值明细guid
     */
    private String growthDetailGuid;

}