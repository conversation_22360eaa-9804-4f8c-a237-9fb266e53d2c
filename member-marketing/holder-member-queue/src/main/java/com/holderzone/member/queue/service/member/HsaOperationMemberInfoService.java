package com.holderzone.member.queue.service.member;

import com.holderzone.member.queue.dto.member.MemberGrowthValueEvent;
import com.holderzone.member.queue.entity.HsaOperationMemberInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 会员相关接口
 * @date 2021/11/23 11:26
 */
public interface HsaOperationMemberInfoService {

    /**
     * 系统批量调整会员成长值
     *
     * @param event event
     * @return
     */
    void systemUpdateMemberGrowth(MemberGrowthValueEvent event);

    /**
     * 获取会员信息
     *
     * @param memberGuid memberGuid
     * @return
     */
    List<HsaOperationMemberInfo> checkMemberGuid(List<String> memberGuid);
}
