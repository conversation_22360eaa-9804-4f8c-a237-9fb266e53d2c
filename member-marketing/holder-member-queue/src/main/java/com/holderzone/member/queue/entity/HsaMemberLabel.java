package com.holderzone.member.queue.entity;

/**
 * @ProjectName: member-marketing
 * @ClassName: HsaMemberLabel
 * @Author: pantao
 * @Description: 会员标签表
 * @Date: 2021/8/24 18:06
 * @Version: 1.0
 */

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberLabel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * GUID
     */
    private String guid;

    /**
     * 标签设置GUID
     */
    private String labelSettingGuid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 运营主体会员GUID
     */
    private String operationMemberInfoGuid;

    /**
     * 操作人guid
     */
    private String operatorGuid;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 关联类型 0 手动关联 1 自动关联
     */
    private int connectionType;

}
