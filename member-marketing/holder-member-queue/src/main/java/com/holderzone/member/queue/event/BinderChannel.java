package com.holderzone.member.queue.event;

import com.holderzone.member.common.constant.BinderConstant;
import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.SubscribableChannel;

/**
 * <AUTHOR>
 * @description 队列通道
 * @date 2021/8/11
 */
public interface BinderChannel {

    /**
     * 发送消息通道定义
     */
    @Input(BinderConstant.INPUT_SEND_MESSAGE)
    SubscribableChannel inputSendMessage();

    /**
     * 开通电子卡通道
     *
     * @return
     */
    @Input(BinderConstant.INPUT_SEND_OPEN_ELECTRONIC_CARD)
    SubscribableChannel inputSendOpenElectronicCard();

    /**
     * 补贴活动发送通道定义
     *
     * @return
     */
    @Input(BinderConstant.INPUT_SUBSIDY_SEND_RIGHTS)
    SubscribableChannel inputSubsidySendRights();

    /**
     * 补贴活动回收通道定义
     *
     * @return
     */
    @Input(BinderConstant.INPUT_SUBSIDY_SEND_BACK_RIGHTS)
    SubscribableChannel inputSubsidySendBackRights();

    /**
     * 成长值回收通道定义
     *
     * @return
     */
    @Input(BinderConstant.INPUT_GROWTH_SEND_BACK_RIGHTS)
    SubscribableChannel inputGrowthSendBackRights();

    /**
     * 成长值变动通道定义
     *
     * @return
     */
    @Input(BinderConstant.INPUT_CHANGE_GROWTH_VALUE)
    SubscribableChannel inputChangeGrowthValue();


    /**
     * 成长值变动通道定义
     *
     * @return
     */
    @Input(BinderConstant.INPUT_RELATION_LABEL)
    SubscribableChannel inputRelationLabel();

    /**
     * 会员等级变化
     *
     * @return
     */
    @Input(BinderConstant.INPUT_MEMBER_GRADE_CHANGE)
    SubscribableChannel inputMemberGradeChange();


    /**
     * 推送订单到crm
     *
     * @return
     */
    @Input(BinderConstant.INPUT_PUSH_CRM_ORDER)
    SubscribableChannel inputPushCrmOrder();

    /**
     * 券包发放
     *
     * @return
     */
    @Input(BinderConstant.INPUT_MEMBER_COUPON_PACKAGE)
    SubscribableChannel inputMemberCouponPackage();

    /**
     * 短信发送
     */
    @Input(BinderConstant.INPUT_PUSH_SHORT_MESSAGE_SEND)
    SubscribableChannel inputPushShortMessageSend();

    /**
     * 会员消费分销记录
     */
    @Input(BinderConstant.INPUT_MEMBER_CONSUMPTION_DISTRIBUTE)
    SubscribableChannel inputMemberConsumptionDistribute();
}
