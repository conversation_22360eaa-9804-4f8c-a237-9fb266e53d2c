package com.holderzone.member.queue.config.mybatis;

import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.extension.injector.AbstractLogicMethod;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.scripting.defaults.RawSqlSource;

/**
 * @date 2020/05/09 14:46
 * @description QueryByGuid-sql拼接
 */
public class LogicQueryByGuid extends AbstractLogicMethod {
    private static final String MAPPER_METHOD = "queryByGuid";
    private static final String MAPPER_SQL = "SELECT %s FROM %s WHERE %s=#{%s}";
    private static final String GUID_NAME = "guid";

    public LogicQueryByGuid() {
    }

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        SqlSource sqlSource = new RawSqlSource(this.configuration,
                String.format(MAPPER_SQL, this.sqlSelectColumns(tableInfo, false),
                        tableInfo.getTableName(), GUID_NAME, GUID_NAME,
                        tableInfo.getLogicDeleteSql(true, false)), Object.class);
        return this.addSelectMappedStatement(mapperClass, MAPPER_METHOD, sqlSource, modelClass, tableInfo);
    }
}
