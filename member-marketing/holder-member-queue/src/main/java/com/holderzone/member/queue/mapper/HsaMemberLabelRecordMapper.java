package com.holderzone.member.queue.mapper;

import com.holderzone.member.common.dto.label.CancelConnectionTypeDTO;
import com.holderzone.member.common.qo.member.MemberLabelRecordQO;
import com.holderzone.member.common.vo.member.MemberLabelRecordVO;
import com.holderzone.member.queue.config.mybatis.HolderBaseMapper;
import com.holderzone.member.queue.entity.HsaMemberLabelRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 会员标签记录
 * @date 2021/9/27 10:06
 */
public interface HsaMemberLabelRecordMapper extends HolderBaseMapper<HsaMemberLabelRecord> {

    void updateIsConnectionByGuid(@Param("request") List<HsaMemberLabelRecord> updateMemberLabelRecordList);

    List<MemberLabelRecordVO> queryMemberLabelRecord(@Param("request") MemberLabelRecordQO memberLabelRecordQO);

   int queryMemberLabelRecordCount(@Param("request") MemberLabelRecordQO memberLabelRecordQO);

   List<CancelConnectionTypeDTO> findLastCancelConnectionType(@Param("labelSettingGuid") String labelSettingGuid);

    List<CancelConnectionTypeDTO> findLastCancelConnectionTypeInMember(@Param("labelSettingGuid") String labelSettingGuid,
                                                                       @Param("memberInfoGuids") Set<String> memberInfoGuids);
}
