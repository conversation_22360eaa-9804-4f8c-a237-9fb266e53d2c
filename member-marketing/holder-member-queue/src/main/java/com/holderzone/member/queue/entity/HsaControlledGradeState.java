package com.holderzone.member.queue.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @program: member-marketing
 * @description: 成长值和等级控制是否启用
 * @author: 张林
 * @create: 2022-1-4 09:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaControlledGradeState implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * guid
     */
    private String guid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 0启用 1禁用
     * @see com.holderzone.member.common.enums.member.EnableEnum
     */
    private Integer state;

    @ApiModelProperty(value = "是否删除,0未删除,1已删除")
    private Boolean isDelete;

    /**
     * 操作人名称
     */
    private String operatorName;


    @ApiModelProperty(value = "角色类型")
    private String roleType;

    @ApiModelProperty(value = "体系名称")
    private String name;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;
}
