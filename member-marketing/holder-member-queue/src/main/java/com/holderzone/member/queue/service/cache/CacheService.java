package com.holderzone.member.queue.service.cache;


/**
 * 缓存服务
 *
 * <AUTHOR>
 * @since 2020-08-12
 */
public interface CacheService {

    /**
     * 缓存等级刷新标识
     *
     * @param cacheKey
     * @param operSubjectGuid
     */
    void createGradeRefresh(String cacheKey, String operSubjectGuid);

    /**
     * 获取缓存等级刷新标识
     *
     * @param cacheKey
     */
    boolean getGradeRefresh(String cacheKey);

    /**
     * @param cacheKey key
     */
    Boolean cleanToken(String cacheKey);

    void delete(String key);

    void updateInventoryNum(String key, String code);

    int getInventoryNum(String key, String code);

    void lockInventoryNum(String key, String code, Integer type, Integer num);

    Boolean setLock(String key, String code);

    Boolean setLock(String key);

    void addInventoryNum(String key, String code);
}
