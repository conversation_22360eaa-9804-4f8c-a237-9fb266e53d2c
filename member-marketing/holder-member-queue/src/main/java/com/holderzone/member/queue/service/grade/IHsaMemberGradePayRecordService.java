package com.holderzone.member.queue.service.grade;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.common.vo.grade.MemberGradePayRecordReqVO;
import com.holderzone.member.queue.entity.HsaMemberGradePayRecord;

/**
 * <p>
 * 会员等级付费记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
public interface IHsaMemberGradePayRecordService extends IService<HsaMemberGradePayRecord> {

    void batchAdd(MemberGradePayRecordReqVO gradePayRecordReqVO);
}
