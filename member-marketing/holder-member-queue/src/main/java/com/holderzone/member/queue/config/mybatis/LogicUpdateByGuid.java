package com.holderzone.member.queue.config.mybatis;

import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.extension.injector.AbstractLogicMethod;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

/**
 * @date 2020/05/09 14:46
 * @description UpdateByGuid-sql拼接
 */
public class LogicUpdateByGuid extends AbstractLogicMethod {
    private static final String MAPPER_METHOD = "updateByGuid";
    private static final String MAPPER_SQL = "<script>\nUPDATE %s %s WHERE %s=#{%s}\n</script>";
    private static final String GUID_NAME = "guid";
    private static final String DEL_SQL = "<if test=\"et.guid != null and et.guid != ''\">guid=#{et.guid},</if>";

    public LogicUpdateByGuid() {
    }

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        String sql = String.format(MAPPER_SQL, tableInfo.getTableName(),
                this.sqlSet(false, false, tableInfo, "et."), GUID_NAME, "et." + GUID_NAME);
        if (sql.contains(DEL_SQL)) {
            sql = sql.replace(DEL_SQL, "");
        }
        SqlSource sqlSource = this.languageDriver.createSqlSource(this.configuration, sql, modelClass);
        return this.addUpdateMappedStatement(mapperClass, modelClass, MAPPER_METHOD, sqlSource);
    }


}
