package com.holderzone.member.queue.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员等级权益适用商品
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaGradeRightsCommodityRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * guid
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 权益guid
     */
    private String gradeEquitiesGuid;

    /**
     * 商品编码
     */
    private String commodityCode;

    /**
     * 单品1 固定2 可选3
     *
     * @see com.holderzone.member.common.enums.growth.CommodityComboTypeEnum
     */
    private Integer comboType;

    /**
     * 商品id
     */
    private String commodityId;

    /**
     * 商品名称
     */
    private String commodityName;

    /**
     * 商品售价
     */
    private String commodityPrice;

    /**
     * 是否适用
     */
    private String isApplicable;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

}
