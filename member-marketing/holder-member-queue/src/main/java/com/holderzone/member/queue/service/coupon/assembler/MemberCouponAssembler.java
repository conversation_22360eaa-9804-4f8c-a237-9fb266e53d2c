package com.holderzone.member.queue.service.coupon.assembler;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.card.MemberPhoneDTO;
import com.holderzone.member.common.dto.coupon.ResponseCouponDTO;
import com.holderzone.member.common.dto.coupon.ResponseCouponNumDTO;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponPackageLink;
import com.holderzone.member.common.enums.coupon.CouponMemberStateEnum;
import com.holderzone.member.common.enums.coupon.CouponPackageTypeEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.qo.coupon.CouponPackageSpecificDTO;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.number.NumberUtil;
import com.holderzone.member.common.vo.coupon.EditCouponPackageActivityVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MemberCouponAssembler {

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    private MemberCouponAssembler() {

    }


    private static void checkEffectiveTime(LocalDateTime now, ResponseCouponDTO responseCouponDTO, HsaMemberCouponLink hsaMemberCouponLink) {
        if (responseCouponDTO.getEffectiveType() == 0) {
            hsaMemberCouponLink.setCouponEffectiveStartTime(responseCouponDTO.getCouponEffectiveStartTime());
            hsaMemberCouponLink.setCouponEffectiveEndTime(responseCouponDTO.getCouponEffectiveEndTime());
        } else if (responseCouponDTO.getEffectiveType() == 1) {
            if (responseCouponDTO.getAfterUnit() == 0) {
                hsaMemberCouponLink.setCouponEffectiveStartTime(now.plusHours(responseCouponDTO.getAfterValue()));
            } else {
                hsaMemberCouponLink.setCouponEffectiveStartTime(now.plusDays(responseCouponDTO.getAfterValue()));
            }

            if (responseCouponDTO.getEffectiveUnit() == 0) {
                hsaMemberCouponLink.setCouponEffectiveEndTime(hsaMemberCouponLink.getCouponEffectiveStartTime()
                        .plusHours(responseCouponDTO.getEffectiveValue()));
            } else {
                hsaMemberCouponLink.setCouponEffectiveEndTime(hsaMemberCouponLink.getCouponEffectiveStartTime()
                        .plusDays(responseCouponDTO.getEffectiveValue()));
            }

        } else {
            if (responseCouponDTO.getAfterUnit() == 0) {
                hsaMemberCouponLink.setCouponEffectiveStartTime(now.plusHours(responseCouponDTO.getAfterValue()));
            } else {
                hsaMemberCouponLink.setCouponEffectiveStartTime(now.plusDays(responseCouponDTO.getAfterValue()));
            }

            hsaMemberCouponLink.setCouponEffectiveEndTime(responseCouponDTO.getCouponEffectiveEndTime());
        }

        //判断是否过期
        if (hsaMemberCouponLink.getCouponEffectiveEndTime().isAfter(now)) {
            hsaMemberCouponLink.setState(CouponMemberStateEnum.UN_EXPIRE.getCode());
        } else {
            hsaMemberCouponLink.setState(CouponMemberStateEnum.EXPIRE.getCode());
        }
    }


    public static HsaMemberCouponPackageLink newMemberCouponPackageLink(
            HsaMemberCouponPackageLink hsaMemberCouponPackageLink,
            EditCouponPackageActivityVO activityVO,
            MemberPhoneDTO memberPhoneDTO,
            LocalDateTime now,
            Integer result,
            String reason) {
        if (activityVO == null) {
            return null;
        }
        hsaMemberCouponPackageLink.setOperSubjectGuid(activityVO.getOperSubjectGuid());
        hsaMemberCouponPackageLink.setActivityName(activityVO.getActivityName());
        hsaMemberCouponPackageLink.setActivityGuid(activityVO.getGuid());
        hsaMemberCouponPackageLink.setActivityCode(activityVO.getActivityCode());

        List<ResponseCouponNumDTO> couponNumDTOS = Lists.newArrayList();
        activityVO.getCouponGuidList().forEach(in -> {
            ResponseCouponNumDTO responseCoupon = new ResponseCouponNumDTO();
            responseCoupon.setCouponName(in.getCouponName())
                    .setCouponCode(in.getCouponCode())
                    .setDiscountAmount(in.getDiscountAmount())
                    .setNum(in.getNum());
            couponNumDTOS.add(responseCoupon);
        });
        hsaMemberCouponPackageLink.setCouponPackageJson(JSON.toJSONString(couponNumDTOS));

        hsaMemberCouponPackageLink.setMemberGuid(memberPhoneDTO.getGuid());
        hsaMemberCouponPackageLink.setMemberPhone(memberPhoneDTO.getPhoneNum());
        hsaMemberCouponPackageLink.setSource(SourceTypeEnum.ADD_BACKGROUND.getCode());
        hsaMemberCouponPackageLink.setReachTime(now);
        hsaMemberCouponPackageLink.setState(result);
        hsaMemberCouponPackageLink.setReason(reason);
        hsaMemberCouponPackageLink.setUserName(memberPhoneDTO.getUserName());

        return hsaMemberCouponPackageLink;
    }


    public List<HsaMemberCouponLink> newMemberCouponLink(EditCouponPackageActivityVO activityVO,
                                                         MemberPhoneDTO memberPhoneDTO,
                                                         LocalDateTime now,
                                                         Map<String, String> codeMap,
                                                         Set<String> hsaMemberLabelSet) {

        List<HsaMemberCouponLink> hsaMemberCouponLinkList = Lists.newArrayList();
        for (ResponseCouponDTO responseCouponDTO : activityVO.getCouponGuidList()) {
            log.info("responseCouponDTO:{}", responseCouponDTO);
            for (int i = 0; i < responseCouponDTO.getNum(); i++) {
                HsaMemberCouponLink hsaMemberCouponLink = new HsaMemberCouponLink();
                hsaMemberCouponLink.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberCouponLink.class.getSimpleName()));
                hsaMemberCouponLink.setOperSubjectGuid(activityVO.getOperSubjectGuid());
                hsaMemberCouponLink.setCouponName(responseCouponDTO.getCouponName())
                        .setUserName(memberPhoneDTO.getUserName())
                        .setMemberGuid(memberPhoneDTO.getGuid())
                        .setMemberPhone(memberPhoneDTO.getPhoneNum())
                        .setSource(SourceTypeEnum.ADD_BACKGROUND.getCode())
                        .setReachTime(now)
                        .setCouponPackageType(CouponPackageTypeEnum.COUPON_PACKAGE_DIRECTIONAL.getCode())
                        .setCouponPackageCode(activityVO.getActivityCode())
                        .setCouponPackageName(activityVO.getActivityName())
                        .setCode(createCouponCode(codeMap, hsaMemberCouponLink.getGuid()))
                        .setCouponCode(responseCouponDTO.getCouponCode())
                        .setCouponType(responseCouponDTO.getCouponType())
                        .setThresholdType(responseCouponDTO.getThresholdType())
                        .setThresholdAmount(responseCouponDTO.getThresholdAmount())
                        .setDiscountAmount(responseCouponDTO.getDiscountAmount())
                        .setRemark(responseCouponDTO.getRemark())
                        .setApplyDateLimited(responseCouponDTO.getApplyDateLimited())
                        .setApplyTimeLimitedType(responseCouponDTO.getApplyTimeLimitedType())
                        .setApplyTerminal(responseCouponDTO.getApplyTerminal())
                        .setApplyTimeLimitedJson(responseCouponDTO.getApplyTimeLimitedJson())
                        .setApplyBusiness(responseCouponDTO.getApplyBusiness())
                        .setApplyTerminalJson(JSON.toJSONString(responseCouponDTO.getApplyTerminalList()))
                        .setApplicableAllStore(responseCouponDTO.getApplicableAllStore())
                        .setEnterpriseGuid(memberPhoneDTO.getEnterpriseGuid())
                        .setApplyCommodity(responseCouponDTO.getApplyCommodity())
                        .setDiscountAmountLimit(responseCouponDTO.getDiscountAmountLimit())
                        .setSingleOrderUsedLimit(responseCouponDTO.getSingleOrderUsedLimit())
                        .setShareRelation(responseCouponDTO.getShareRelation())
                        .setExchangeLimit(responseCouponDTO.getExchangeLimit())
                        .setExchangeTimes(responseCouponDTO.getExchangeTimes());


                // 时间判断
                checkEffectiveTime(now, responseCouponDTO, hsaMemberCouponLink);

                if (CollUtil.isNotEmpty(responseCouponDTO.getApplyLabelGuidList())) {
                    hsaMemberCouponLink.setApplyLabelGuidJson(JSON.toJSONString(responseCouponDTO.getApplyLabelGuidList()));

                    hsaMemberLabelSet.addAll(responseCouponDTO.getApplyLabelGuidList());
                }

                if (CollUtil.isNotEmpty(responseCouponDTO.getApplyBusinessList())) {
                    hsaMemberCouponLink.setApplyBusinessJson(JSON.toJSONString(responseCouponDTO.getApplyBusinessList()));
                }

                //门店
                if (CollUtil.isNotEmpty(responseCouponDTO.getRequestCouponStoreQOList())) {
                    hsaMemberCouponLink.setApplicableAllStoreJson(JSON.toJSONString(responseCouponDTO.getRequestCouponStoreQOList()));
                }

                //商品
                if (CollUtil.isNotEmpty(responseCouponDTO.getRequestCouponCommodityQOList())) {
                    hsaMemberCouponLink.setApplyCommodityJson(JSON.toJSONString(responseCouponDTO.getRequestCouponCommodityQOList()));
                }
                hsaMemberCouponLinkList.add(hsaMemberCouponLink);
            }
        }

        return hsaMemberCouponLinkList;
    }


    /**
     * 生成编码
     *
     * @return String
     */
    public String createCouponCode(Map<String, String> codeMap, String guid) {
        String code = guid.substring(7, 19);
        if (codeMap.containsKey(code)) {
            code = NumberUtil.buildNumToStr(12);
        }
        codeMap.put(code, code);
        return code;
    }


    public static void forGetCouponPackageGrantList(Date date, String presentDate,
                                                    List<EditCouponPackageActivityVO> couponPackageList,
                                                    Set<EditCouponPackageActivityVO> couponPackageGrantList) {
        //当前分
        String minuteDate = DateUtil.getTmpDate(date, StringConstant.MM);
        for (EditCouponPackageActivityVO vo : couponPackageList) {
            if (vo.getActivityType() != CouponPackageTypeEnum.COUPON_PACKAGE_DIRECTIONAL.getCode()) {
                continue;
            }
            List<CouponPackageSpecificDTO> couponPackageSpecificDTOList = vo.getCouponPackageSpecificDTO();
            List<String> times = couponPackageSpecificDTOList.stream()
                    .map(CouponPackageSpecificDTO::getTime)
                    .collect(Collectors.toList());

            if (vo.getGiveFrequencyType() == 0) {
                checkSingle(date, couponPackageGrantList, vo, couponPackageSpecificDTOList);

            }
            //每天
            else if (vo.getGiveFrequencyType() == 1) {
                if (times.contains(presentDate) && minuteDate.equals("00")) {
                    couponPackageGrantList.add(vo);
                }
            }
            //每周
            else if (vo.getGiveFrequencyType() == 2) {
                String current = DateUtil.dateToWeek(DateUtil.getTmpDate(date, StringConstant.FORMAT_YYYY_MM_DD));
                checkDate(presentDate, couponPackageGrantList, vo, current, minuteDate, couponPackageSpecificDTOList);

            }
            //每月
            else if (vo.getGiveFrequencyType() == 3) {
                String current = DateUtil.getTmpDate(date, StringConstant.FORMAT_D);
                checkDate(presentDate, couponPackageGrantList, vo, current, minuteDate, couponPackageSpecificDTOList);
            }
            //每年
            else if (vo.getGiveFrequencyType() == 4) {
                String current = DateUtil.getTmpDate(date, StringConstant.M_D);
                checkDate(presentDate, couponPackageGrantList, vo, current, minuteDate, couponPackageSpecificDTOList);
            }
        }
    }

    private static void checkDate(String presentDate,
                                  Set<EditCouponPackageActivityVO> couponPackageGrantList,
                                  EditCouponPackageActivityVO vo,
                                  String current,
                                  String minuteDate,
                                  List<CouponPackageSpecificDTO> couponPackageSpecificDTOList) {
        for (CouponPackageSpecificDTO couponPackageSpecificDTO : couponPackageSpecificDTOList) {
            if (couponPackageSpecificDTO.getTimeList().contains(current)
                    && couponPackageSpecificDTO.getTime().equals(presentDate) && minuteDate.equals("00")) {
                couponPackageGrantList.add(vo);
                break;
            }
        }

    }

    //校验单次时间
    private static void checkSingle(Date date, Set<EditCouponPackageActivityVO> couponPackageGrantList, EditCouponPackageActivityVO vo, List<CouponPackageSpecificDTO> couponPackageSpecificDTOList) {
        String time = couponPackageSpecificDTOList.get(0).getTime();
        String[] singleTime = time.split(StringConstant.COLON);
        String hour = singleTime[0];
        String minute = singleTime[1];

        //当前时
        String hourDate = DateUtil.getTmpDate(date, StringConstant.HH);
        //当前分
        String minuteDate = DateUtil.getTmpDate(date, StringConstant.MM);

        if (hour.equals(hourDate) && minute.equals(minuteDate)) {
            couponPackageGrantList.add(vo);
        }
    }
}
