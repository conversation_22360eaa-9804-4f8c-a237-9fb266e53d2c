package com.holderzone.member.queue.service.activity.impl;


import com.holderzone.member.queue.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.queue.entity.HsaSubsidyActivityDetailRecord;
import com.holderzone.member.queue.mapper.HsaSubsidyActivityDetailRecordMapper;
import com.holderzone.member.queue.service.activity.HsaSubsidyActivityDetailRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 补贴活动记录明细
 * @date 2021/10/26 9:42
 */
@Service
@Slf4j
public class HsaSubsidyActivityDetailRecordServiceImpl extends HolderBaseServiceImpl<HsaSubsidyActivityDetailRecordMapper, HsaSubsidyActivityDetailRecord> implements HsaSubsidyActivityDetailRecordService {

}
