package com.holderzone.member.queue.service.card.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.collect.Lists;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.dto.event.SendOpenCardEvent;
import com.holderzone.member.common.dto.event.SendOpenElectronicCardEvent;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.DateUnitEnum;
import com.holderzone.member.common.enums.card.*;
import com.holderzone.member.common.enums.member.AmountSourceTypeEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.qo.card.CardEquitiesQO;
import com.holderzone.member.common.qo.card.SendMemberEquityCard;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.number.SerialNumberBuilder;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.queue.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.queue.entity.*;
import com.holderzone.member.queue.mapper.*;
import com.holderzone.member.queue.service.card.HsaElectronicCardService;
import com.holderzone.member.queue.service.card.HsaStoreCardRuleService;
import com.holderzone.member.queue.service.card.MemberInfoCardService;
import com.holderzone.member.queue.service.card.assembler.MemberCardAssembler;
import com.holderzone.member.queue.service.member.impl.HsaMemberFundingDetailServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 会员卡业务
 * @date 2021/10/14 11:28
 */
@Slf4j
@Service
public class MemberInfoCardServiceImpl extends HolderBaseServiceImpl<HsaMemberInfoCardMapper, HsaMemberInfoCard> implements MemberInfoCardService {

    @Resource
    private HsaMemberInfoCardMapper hsaMemberInfoCardMapper;

    @Resource
    private HsaStoreCardRuleMapper hsaStoreCardRuleMapper;

    @Resource
    private HsaElectronicCardService hsaElectronicCardService;

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Resource
    private HsaCardOpenRuleMapper hsaCardOpenRuleMapper;

    @Resource
    private HsaCardBaseInfoMapper hsaCardBaseInfoMapper;

    @Resource
    private HsaStoreCardRuleService hsaStoreCardRuleService;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private MemberBaseFeign memberBaseFeign;

    @Resource
    private HsaMemberFundingDetailServiceImpl hsaMemberFundingDetailService;

    @Resource
    private MemberCardAssembler memberCardAssembler;



    /**
     * 批量发卡
     *
     * @param event event
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendOpenElectronicCard(SendOpenElectronicCardEvent event) {
        log.info("queue指定发卡队列实现>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>：{}", event);
        List<String> cardGuidList = event.getSendOpenCardEvents().stream().map(SendOpenCardEvent::getCardGuid).collect(Collectors.toList());
        List<String> memberGuidList = Lists.newArrayList();
        event.getSendOpenCardEvents().forEach(in -> memberGuidList.addAll(in.getMemberInfoGuid()));

        Map<String, HsaCardOpenRule> hsaCardOpenRuleMap = getStringHsaCardOpenRuleMap(cardGuidList);

        Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap = getStringHsaCardBaseInfoMap(cardGuidList);

        Map<String, HsaOperationMemberInfo> hsaOperationMemberInfoMap = getStringHsaOperationMemberInfoMap(memberGuidList);
        List<SendOpenCardEvent> sendOpenCardEvents = event.getSendOpenCardEvents();
        //持久化
        List<HsaMemberInfoCard> hsaMemberInfoCardList = Lists.newArrayList();
        List<HsaElectronicCard> hsaElectronicCardList = Lists.newArrayList();
        List<HsaCardOpenRule> hsaCardOpenRuleList = Lists.newArrayList();
        List<HsaCardBaseInfo> hsaCardBaseInfoList = Lists.newArrayList();
        //卡面值map
        Map<String, BigDecimal> faceValueMap = new HashMap<>();
        for (SendOpenCardEvent sendOpenCardEvent : sendOpenCardEvents) {
            List<String> memberInfoGuidList = sendOpenCardEvent.getMemberInfoGuid();
            Map<String, HsaMemberInfoCard> hsaMemberInfoCardMap = getStringHsaMemberInfoCardMap(sendOpenCardEvent, memberInfoGuidList);
            HsaCardOpenRule hsaCardOpenRule = hsaCardOpenRuleMap.get(sendOpenCardEvent.getCardGuid());
            HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMap.get(sendOpenCardEvent.getCardGuid());
            memberInfoGuidList.forEach(memberInfoGuid -> {
                faceValueMap.put(hsaCardBaseInfo.getGuid(), hsaCardBaseInfo.getCardValueMoney());
                HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMap.get(memberInfoGuid);

                checkSendOpenCard(memberInfoGuid, hsaMemberInfoCardMap, hsaCardOpenRule, hsaCardBaseInfo, hsaOperationMemberInfo, hsaMemberInfoCardList, hsaElectronicCardList);
                hsaCardOpenRule.setSendStatus(SendCardStateEnum.CARD_STATE_OVER.getCode());
                hsaCardOpenRule.setSendOpenCountLimit(memberInfoGuidList.size());
                hsaCardOpenRule.setSurplusSendOpenCountLimit(0);
            });
            hsaCardOpenRule.setSendStatus(SendCardStateEnum.CARD_STATE_OVER.getCode());
            hsaCardOpenRuleList.add(hsaCardOpenRule);
            hsaCardBaseInfoList.add(hsaCardBaseInfo);
        }
        List<HsaMemberFundingDetail> hsaMemberFundingDetails = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(hsaMemberInfoCardList)) {
            dealMemberInfoCardSave(hsaMemberInfoCardList, faceValueMap, hsaMemberFundingDetails);
        }
        if (CollectionUtils.isNotEmpty(hsaMemberFundingDetails)) {
            hsaMemberFundingDetailService.saveBatch(hsaMemberFundingDetails);
        }
        if (CollectionUtils.isNotEmpty(hsaElectronicCardList)) {
            hsaElectronicCardService.saveBatch(hsaElectronicCardList);
        }
        if (CollectionUtils.isNotEmpty(hsaCardOpenRuleList)) {
            hsaCardOpenRuleList.forEach(in -> hsaCardOpenRuleMapper.updateByGuid(in));
        }
        if (CollectionUtils.isNotEmpty(hsaCardBaseInfoList)) {
            hsaCardBaseInfoMapper.batchUpdateStatus(hsaCardBaseInfoList);
        }

    }

    private void dealMemberInfoCardSave(List<HsaMemberInfoCard> hsaMemberInfoCardList, Map<String, BigDecimal> faceValueMap, List<HsaMemberFundingDetail> hsaMemberFundingDetails) {
        hsaMemberInfoCardList.forEach(in -> {
            if (Objects.nonNull(hsaMemberInfoCardMapper.queryByGuid(in.getGuid()))) {
                hsaMemberInfoCardMapper.updateByGuid(in);
            } else {
                //卡面值不为0，添加消费明细记录
                if (BigDecimalUtil.greaterThanZero(faceValueMap.get(in.getCardGuid()))) {
                    hsaMemberFundingDetails.add(memberCardAssembler.addOpenCardBalanceRecord(in, faceValueMap.get(in.getCardGuid())));
                }
                hsaMemberInfoCardMapper.insert(in);
            }
        });

        //发送会员卡权益
        CardEquitiesQO cardEquitiesQO = new CardEquitiesQO();
        List<SendMemberEquityCard> sendMemberEquitiesCards = Lists.newArrayList();
        for (HsaMemberInfoCard hsaMemberInfoCard : hsaMemberInfoCardList) {
            SendMemberEquityCard sendMemberEquityCard = new SendMemberEquityCard();
            BeanUtils.copyProperties(hsaMemberInfoCard, sendMemberEquityCard);
            sendMemberEquitiesCards.add(sendMemberEquityCard);
        }
        cardEquitiesQO.setSendMemberEquityCard(sendMemberEquitiesCards);
        log.info("指定时间开卡发送会员卡权益赠送:{}", JSON.toJSONString(cardEquitiesQO));
        memberBaseFeign.grantFeignCardRights(cardEquitiesQO);
    }

    private void checkSendOpenCard(String memberInfoGuid,
                                   Map<String, HsaMemberInfoCard> hsaMemberInfoCardMap,
                                   HsaCardOpenRule hsaCardOpenRule,
                                   HsaCardBaseInfo hsaCardBaseInfo,
                                   HsaOperationMemberInfo hsaOperationMemberInfo,
                                   List<HsaMemberInfoCard> hsaMemberInfoCardList,
                                   List<HsaElectronicCard> hsaElectronicCardList) {
        HsaMemberInfoCard hsaMemberInfoCard;
        if (CollectionUtils.isNotEmpty(hsaMemberInfoCardMap) && hsaMemberInfoCardMap.containsKey(memberInfoGuid)) {
            hsaMemberInfoCard = hsaMemberInfoCardMap.get(memberInfoGuid);
            if (StringUtils.isEmpty(hsaMemberInfoCard.getElectronicCardGuid())) {
                HsaElectronicCard electronicCard = new HsaElectronicCard();
                memberCardAssembler.openCardProcessor(electronicCard, hsaMemberInfoCard, hsaCardOpenRule, hsaCardBaseInfo, hsaOperationMemberInfo, hsaMemberInfoCardList, hsaElectronicCardList);
                //合并门店
                hsaStoreCardRuleService.mergeStoreProcessor(hsaMemberInfoCard, hsaMemberInfoCard.getCardGuid());
            }
        } else {
            hsaMemberInfoCard = new HsaMemberInfoCard();
            HsaElectronicCard electronicCard = new HsaElectronicCard();
            hsaMemberInfoCard.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberInfoCard.class.getSimpleName()));
            hsaMemberInfoCard.setCardAmount(hsaCardBaseInfo.getCardValueMoney());
            hsaMemberInfoCard.setCardImage(hsaCardBaseInfo.getCardImage());
            hsaMemberInfoCard.setCardPayPassword(hsaCardBaseInfo.getCardPayPassword());
            //是否超额
            if (hsaCardBaseInfo.getIsExcess() == BooleanEnum.TRUE.getCode()) {
                if (hsaCardBaseInfo.getExcessType() == 0) {
                    hsaMemberInfoCard.setExcessTimes(hsaCardBaseInfo.getExcessMoney().intValue());
                } else {
                    hsaMemberInfoCard.setExcessAmount(hsaCardBaseInfo.getExcessMoney());
                }
            }
            memberCardAssembler.openCardProcessor(electronicCard, hsaMemberInfoCard, hsaCardOpenRule, hsaCardBaseInfo, hsaOperationMemberInfo, hsaMemberInfoCardList, hsaElectronicCardList);
            //独立已领卡门店
            saveStoreCardRule(hsaMemberInfoCard);
        }
    }

    public void saveStoreCardRule(HsaMemberInfoCard hsaMemberInfoCard) {
        List<HsaStoreCardRule> hsaStoreCardRuleArrayList = Lists.newArrayList();
        // 去保存适用门店信息
        List<HsaStoreCardRule> hsaStoreCardRuleList = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .eq(HsaStoreCardRule::getCardGuid, hsaMemberInfoCard.getCardGuid())
                .isNull(HsaStoreCardRule::getMemberInfoCardGuid).isNull(HsaStoreCardRule::getParentGuid));
        //档口
        Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                        .in(HsaStoreCardRule::getParentGuid, hsaStoreCardRuleList.stream().map(HsaStoreCardRule::getGuid).collect(Collectors.toList()))
                        .isNotNull(HsaStoreCardRule::getParentGuid))
                .stream()
                .collect(Collectors.groupingBy(HsaStoreCardRule::getParentGuid));
        if (CollUtil.isNotEmpty(hsaStoreCardRuleList)) {
            memberCardAssembler.addStoreCardRuleArray(hsaMemberInfoCard, hsaStoreCardRuleList, hsaStoreCardRuleMap, hsaStoreCardRuleArrayList);

        }

        if (CollUtil.isNotEmpty(hsaStoreCardRuleArrayList)) {
            hsaStoreCardRuleService.saveBatch(hsaStoreCardRuleArrayList);
        }
    }


    private Map<String, HsaMemberInfoCard> getStringHsaMemberInfoCardMap(SendOpenCardEvent sendOpenCardEvent, List<String> memberInfoGuidList) {
        return hsaMemberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                        .eq(HsaMemberInfoCard::getCardGuid, sendOpenCardEvent.getCardGuid())
                        .in(HsaMemberInfoCard::getMemberInfoGuid, memberInfoGuidList))
                .stream()
                .collect(Collectors.toMap(HsaMemberInfoCard::getMemberInfoGuid, Function.identity(), (entity1, entity2) -> entity1));
    }

    private Map<String, HsaCardOpenRule> getStringHsaCardOpenRuleMap(List<String> cardGuidList) {
        return hsaCardOpenRuleMapper.selectList(new LambdaQueryWrapper<HsaCardOpenRule>()
                        .in(HsaCardOpenRule::getCardGuid, cardGuidList))
                .stream()
                .collect(Collectors.toMap(HsaCardOpenRule::getCardGuid, Function.identity(), (entity1, entity2) -> entity1));
    }

    private Map<String, HsaCardBaseInfo> getStringHsaCardBaseInfoMap(List<String> cardGuidList) {
        return hsaCardBaseInfoMapper.selectList(new LambdaQueryWrapper<HsaCardBaseInfo>()
                        .in(HsaCardBaseInfo::getGuid, cardGuidList))
                .stream()
                .collect(Collectors.toMap(HsaCardBaseInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));
    }

    private Map<String, HsaOperationMemberInfo> getStringHsaOperationMemberInfoMap(List<String> memberGuidList) {
        return hsaOperationMemberInfoMapper.selectList(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                        .in(HsaOperationMemberInfo::getGuid, memberGuidList))
                .stream()
                .collect(Collectors.toMap(HsaOperationMemberInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));
    }
}
