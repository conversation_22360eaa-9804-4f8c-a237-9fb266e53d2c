package com.holderzone.member.queue.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 补贴活动记录明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaSubsidyActivityDetailRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * guid
     */
    private String guid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 活动编号
     */
    private String activityNum;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 补贴金额
     */
    private BigDecimal subsidyMoney;

    /**
     * 补贴活动guid
     */
    private String subsidyActivityGuid;

    /**
     * 补贴记录guid
     */
    private String subsidyActivityRecordGuid;

    /**
     * 补贴机制时间(整点触发时间或补发时间)
     */
    private LocalDateTime subsidyTime;

    /**
     * 补贴到账时间(以此时间结算有效期)
     */
    private LocalDateTime subsidyArriveTime;

    /**
     * 补贴金到期时间
     */
    private LocalDateTime validityExpireTime;

    /**
     * 补贴金是否可退 0 不可退  1 可退
     */
    private Integer isRetreat;

    /**
     * 补贴会员卡guid
     */
    private String cardGuid;

    /**
     * 补贴会员guid
     */
    private String memberInfoGuid;

    /**
     * memberInfoCardGuid
     */
    private String memberInfoCardGuid;


    /**
     * 补贴结果 0 失败  1 成功
     */
    private Integer subsidyResult;

    /**
     * 补贴失败原因
     */
    private String subsidyError;

    /**
     * 是否已回收
     */
    private Integer isWithdraw;

    /**
     * 补贴剩余金额
     */
    private BigDecimal subsidyResidueMoney;

    /**
     * 操作人员账号
     */
    @ApiModelProperty("操作人员账号")
    private String operatorTelName;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;
}
