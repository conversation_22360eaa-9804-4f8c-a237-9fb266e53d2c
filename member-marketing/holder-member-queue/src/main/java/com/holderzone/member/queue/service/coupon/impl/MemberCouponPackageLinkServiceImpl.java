package com.holderzone.member.queue.service.coupon.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.card.MemberPhoneDTO;
import com.holderzone.member.common.dto.coupon.ResponseCouponDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponPackageLink;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.qo.coupon.CouponPackageSpecificDTO;
import com.holderzone.member.common.qo.member.AddMemberLabelCorrelationQO;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.coupon.CouponPackageGrantVO;
import com.holderzone.member.common.vo.coupon.EditCouponPackageActivityVO;
import com.holderzone.member.common.vo.coupon.MemberCouponPackageVO;
import com.holderzone.member.queue.entity.HsaMemberLabel;
import com.holderzone.member.queue.mapper.HsaMemberLabelMapper;
import com.holderzone.member.queue.mapper.HsaOperationMemberInfoMapper;
import com.holderzone.member.queue.mapper.coupon.HsaMemberCouponPackageLinkMapper;
import com.holderzone.member.queue.service.cache.CacheService;
import com.holderzone.member.queue.service.coupon.MemberCouponLinkService;
import com.holderzone.member.queue.service.coupon.MemberCouponPackageLinkService;
import com.holderzone.member.queue.service.coupon.assembler.MemberCouponAssembler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
public class MemberCouponPackageLinkServiceImpl
        extends HolderBaseServiceImpl<HsaMemberCouponPackageLinkMapper, HsaMemberCouponPackageLink>
        implements MemberCouponPackageLinkService {


    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private HsaMemberCouponPackageLinkMapper mapper;

    @Resource
    private MemberMarketingFeign memberMarketingFeign;

    @Resource
    private MemberBaseFeign memberBaseFeign;

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationInfoMapper;

    @Resource
    @Lazy
    private MemberCouponLinkService hsaMemberCouponLinkService;

    @Resource
    private CacheService cacheService;

    @Resource
    private HsaMemberLabelMapper hsaMemberLabelMapper;

    @Resource
    private Executor memberQueueThreadExecutor;

    @Resource
    private MemberCouponAssembler memberCouponAssembler;

    @Override
    public void xxlJobCouponPackageGrant(Date date) {
        log.info("xxlJobCouponPackageGrant->>>>>>>>>>>当前执行时间：{}", LocalDateTime.now());

        String current = DateUtil.getTmpDate(date, StringConstant.FORMAT);

        if (Boolean.TRUE.equals(cacheService.setLock(StringConstant.XXL_JOB_COUPON_PACKAGE + current))) {
            log.info("执行时间重复跳出，当前时间：{}", current);
            return;
        }
        //当前整时
        String presentDate = DateUtil.getTmpDate(date, StringConstant.H);

        List<EditCouponPackageActivityVO> couponPackageList = memberMarketingFeign.queryRunActivity(new ArrayList<>());
        log.info("满足发放活动数组：{}", JSON.toJSONString(couponPackageList));
        if (CollUtil.isEmpty(couponPackageList)) {
            cacheService.delete(StringConstant.XXL_JOB_COUPON_PACKAGE + current);
            return;
        }

        Set<EditCouponPackageActivityVO> couponPackageGrantList = new HashSet<>();

        //获取可发放活动
        MemberCouponAssembler.forGetCouponPackageGrantList(date, presentDate, couponPackageList, couponPackageGrantList);

        if (CollUtil.isNotEmpty(couponPackageGrantList)) {
            log.info("满足发放活动数组：{}", JSON.toJSONString(couponPackageGrantList));
            log.info("满足发放活动数组名称：{}", JSON.toJSONString(couponPackageGrantList.stream().
                    map(EditCouponPackageActivityVO::getActivityName).collect(Collectors.toList())));
            MemberCouponPackageLinkService hsaMemberCouponPackageLinkService = (MemberCouponPackageLinkService) AopContext.currentProxy();
            hsaMemberCouponPackageLinkService.couponPackageGrant(new CouponPackageGrantVO()
                    .setCouponPackageGrantList(new ArrayList<>(couponPackageGrantList))
                    .setCurrentTimeMillis(current));
        } else {
            cacheService.delete(StringConstant.XXL_JOB_COUPON_PACKAGE + current);
        }

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void couponPackageGrant(CouponPackageGrantVO couponPackageGrantVO) {
        List<EditCouponPackageActivityVO> couponPackageGrantList = couponPackageGrantVO.getCouponPackageGrantList();
        //注册会员人群
        Map<String, Set<MemberPhoneDTO>> allOperationMemberInfoMap = new HashMap<>();

        //统一发放时间
        LocalDateTime now = LocalDateTime.now();

        log.info("即将发放活动名称：{}", couponPackageGrantList.stream().map(EditCouponPackageActivityVO::getActivityName).collect(Collectors.toList()));

        //记录会员券包数量
        Map<String, MemberCouponPackageVO> memberCouponPackageVOMap = new HashMap<>();


        for (EditCouponPackageActivityVO activityVO : couponPackageGrantList) {

            //库存上锁
            cacheService.lockInventoryNum(StringConstant.LOCK_COUPON_PACKAGE_INVENTORY_NUM + activityVO.getActivityCode(),
                    activityVO.getActivityCode(), 1, 1);

            Long startTime = System.currentTimeMillis();
            //校验人群
            Set<MemberPhoneDTO> memberPhoneDTOSet = checkMemberCrowd(allOperationMemberInfoMap, activityVO);

            //校验优惠券异常
            List<ResponseCouponDTO> responseCouponDTOS =
                    activityVO.getCouponGuidList().stream().filter(in -> in.getIsExist() == 0).collect(Collectors.toList());

            //券码去重
            Map<String, String> codeMap = new HashMap<>();

            //打标签
            Set<String> labelSetGuid = new HashSet<>();

            //处理优惠券发放
            multiThread(now, activityVO, new ArrayList<>(memberPhoneDTOSet), responseCouponDTOS, codeMap, labelSetGuid, memberCouponPackageVOMap);

            Long endTime = System.currentTimeMillis();
            log.info(">>>>>>>>>>>>>>>>>>活动名称：{},耗时：{}", activityVO.getActivityName(), (endTime - startTime));
        }

        if (CollUtil.isNotEmpty(memberCouponPackageVOMap)) {
            List<MemberCouponPackageVO> memberCouponPackageVOS = Lists.newArrayList();
            for (Map.Entry<String, MemberCouponPackageVO> entry : memberCouponPackageVOMap.entrySet()) {
                memberCouponPackageVOS.add(entry.getValue());
            }
            memberQueueThreadExecutor.execute(() -> memberBaseFeign.sendMemberCouponNotice(memberCouponPackageVOS));

        }
        cacheService.delete(StringConstant.XXL_JOB_COUPON_PACKAGE + couponPackageGrantVO.getCurrentTimeMillis());
    }

    private void multiThread(LocalDateTime now,
                             EditCouponPackageActivityVO activityVO,
                             List<MemberPhoneDTO> memberPhoneDTOSet,
                             List<ResponseCouponDTO> responseCouponDTOS,
                             Map<String, String> codeMap,
                             Set<String> labelSetGuid,
                             Map<String, MemberCouponPackageVO> memberCouponPackageVOMap) {
        //发券包记录
        List<HsaMemberCouponPackageLink> hsaMemberCouponPackageLinks = Lists.newArrayList();

        //发券记录
        List<HsaMemberCouponLink> hsaMemberCouponLinks = Lists.newArrayList();
        int memberCouponPackageNum = 0;
        int memberCouponNum = 0;
        String batchId = String.valueOf(now.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        for (MemberPhoneDTO memberPhoneDTO : memberPhoneDTOSet) {
            memberCouponPackageNum = memberCouponPackageNum + 1;
            //校验库存与会员状态
            if (checkCondition(now, activityVO, hsaMemberCouponPackageLinks, memberPhoneDTO, responseCouponDTOS)) {
                continue;
            }
            hsaMemberCouponLinks.addAll(memberCouponAssembler.newMemberCouponLink(activityVO, memberPhoneDTO, now, codeMap, labelSetGuid));
            dealMemberCouponPackageVOMap(activityVO, memberCouponPackageVOMap, memberPhoneDTO, hsaMemberCouponLinks);

            memberCouponNum = memberCouponNum + activityVO.getCouponGuidList().size();

            log.info(">>>>>>>>>>>>>活动名称：{}，当前线程名称：{}，会员：{}，券包已发放：{}，券已发放：{}", activityVO.getActivityName(), Thread.currentThread().getName(), memberPhoneDTO.getGuid(), memberCouponPackageNum, memberCouponNum);


            if (hsaMemberCouponLinks.size() > 3000) {
                List<HsaMemberCouponPackageLink> memberCouponPackageLinkList = hsaMemberCouponPackageLinks;
                List<HsaMemberCouponLink> memberCouponLinks = hsaMemberCouponLinks;

                memberQueueThreadExecutor.execute(() -> this.saveBatch(memberCouponPackageLinkList, memberCouponPackageLinkList.size()));

                memberQueueThreadExecutor.execute(() -> hsaMemberCouponLinkService.saveBatch(memberCouponLinks, memberCouponLinks.size()));

                Set<String> memberGuid = memberCouponLinks.stream()
                        .map(HsaMemberCouponLink::getMemberGuid).collect(Collectors.toSet());
                //为参与活动用户打标签
                dealLabel(new ArrayList<>(labelSetGuid), new ArrayList<>(memberGuid), memberCouponLinks.get(0).getOperSubjectGuid(), batchId);

                hsaMemberCouponPackageLinks = new ArrayList<>();

                hsaMemberCouponLinks = new ArrayList<>();
            }

        }

        dealDB(hsaMemberCouponPackageLinks, hsaMemberCouponLinks, labelSetGuid, batchId);
        //解锁库存
        cacheService.lockInventoryNum(StringConstant.LOCK_COUPON_PACKAGE_INVENTORY_NUM + activityVO.getActivityCode(),
                activityVO.getActivityCode(), 0, 1);
    }

    /**
     * 处理会员领券推送记录
     */
    private static void dealMemberCouponPackageVOMap(EditCouponPackageActivityVO activityVO,
                                                     Map<String, MemberCouponPackageVO> memberCouponPackageVOMap,
                                                     MemberPhoneDTO memberPhoneDTO,
                                                     List<HsaMemberCouponLink> hsaMemberCouponLinks) {
        if (Objects.nonNull(memberPhoneDTO.getWechatState())
                && memberPhoneDTO.getWechatState() == 1) {
            MemberCouponPackageVO memberCouponPackageVO = memberCouponPackageVOMap.get(memberPhoneDTO.getGuid());
            if (Objects.isNull(memberCouponPackageVO)) {
                memberCouponPackageVO = new MemberCouponPackageVO();
            }
            memberCouponPackageVO.setOperSubjectGuid(hsaMemberCouponLinks.get(0).getOperSubjectGuid())
                    .setEnterpriseGuid(hsaMemberCouponLinks.get(0).getEnterpriseGuid());

            memberCouponPackageVO.setCouponPackageNum(memberCouponPackageVO.getCouponPackageNum() + 1);


            //券数量
            int couponNum = 0;
            for (ResponseCouponDTO responseCouponDTO : activityVO.getCouponGuidList()) {
                couponNum = responseCouponDTO.getNum() + couponNum;
            }

            memberCouponPackageVO.setCouponNum(couponNum + memberCouponPackageVO.getCouponNum());
            memberCouponPackageVO.setCouponPackageName(activityVO.getActivityName());

            memberCouponPackageVO.setMemberGuid(memberPhoneDTO.getGuid());
            memberCouponPackageVO.setPhoneNum(memberPhoneDTO.getPhoneNum());

            HsaMemberCouponLink memberCouponLink = hsaMemberCouponLinks.stream()
                    .sorted(Comparator.comparing(HsaMemberCouponLink::getCouponEffectiveEndTime))
                    .collect(Collectors.toList()).get(0);
            //时间比对
            if (Objects.nonNull(memberCouponPackageVO.getCouponEffectiveEndTime())) {
                if (memberCouponLink.getCouponEffectiveEndTime().isBefore(memberCouponPackageVO.getCouponEffectiveEndTime())) {
                    setMemberCoupon(memberCouponPackageVOMap, memberPhoneDTO, memberCouponPackageVO, memberCouponLink);
                }
            } else {
                memberCouponPackageVO.setCouponEffectiveEndTime(memberCouponLink.getCouponEffectiveEndTime());

                setMemberCoupon(memberCouponPackageVOMap, memberPhoneDTO, memberCouponPackageVO, memberCouponLink);
            }

        }
    }

    private static void setMemberCoupon(Map<String, MemberCouponPackageVO> memberCouponPackageVOMap, MemberPhoneDTO memberPhoneDTO, MemberCouponPackageVO memberCouponPackageVO, HsaMemberCouponLink memberCouponLink) {
        memberCouponPackageVO.setCouponName(memberCouponLink.getCouponName());
        memberCouponPackageVO.setCouponType(memberCouponLink.getCouponType());
        memberCouponPackageVO.setCouponEffectiveEndTime(memberCouponLink.getCouponEffectiveEndTime());
        memberCouponPackageVOMap.put(memberPhoneDTO.getGuid(), memberCouponPackageVO);
    }

    private void dealDB(List<HsaMemberCouponPackageLink> hsaMemberCouponPackageLinks, List<HsaMemberCouponLink> hsaMemberCouponLinks, Set<String> labelSetGuid, String batchId) {
        if (CollUtil.isNotEmpty(hsaMemberCouponPackageLinks)) {
            memberQueueThreadExecutor.execute(() -> this.saveBatch(hsaMemberCouponPackageLinks));
        }

        if (CollUtil.isNotEmpty(hsaMemberCouponLinks)) {
            memberQueueThreadExecutor.execute(() -> hsaMemberCouponLinkService.saveBatch(hsaMemberCouponLinks));
            dealLabel(new ArrayList<>(labelSetGuid),
                    new ArrayList<>(hsaMemberCouponLinks.stream().map(HsaMemberCouponLink::getMemberGuid).collect(Collectors.toSet()))
                    , hsaMemberCouponLinks.get(0).getOperSubjectGuid(), batchId);
        }
    }

    private void dealLabel(List<String> labelSetGuid, List<String> memberGuid, String operSubjectGuid, String batchId) {
        if (CollUtil.isNotEmpty(labelSetGuid)) {


            memberQueueThreadExecutor.execute(() -> {
                HeaderUserInfo headerUserInfo = new HeaderUserInfo();
                headerUserInfo.setOperSubjectGuid(operSubjectGuid);
                String userInfo = JSONUtil.toJsonStr(headerUserInfo);
                try {
                    ThreadLocalCache.put(URLDecoder.decode(userInfo, "UTF-8"));
                } catch (UnsupportedEncodingException e) {
                    throw new MemberBaseException(e.getMessage());
                }

                AddMemberLabelCorrelationQO labelCorrelationQo = new AddMemberLabelCorrelationQO();
                labelCorrelationQo.setLabelGuid(labelSetGuid);
                labelCorrelationQo.setMemberInfoGuid(memberGuid);
                labelCorrelationQo.setBatchId(batchId);
                memberBaseFeign.addMemberInfoLabel(labelCorrelationQo);
            });
        }
    }

    private boolean checkCondition(LocalDateTime now,
                                   EditCouponPackageActivityVO activityVO,
                                   List<HsaMemberCouponPackageLink> hsaMemberCouponPackageLinks,
                                   MemberPhoneDTO memberPhoneDTO,
                                   List<ResponseCouponDTO> responseCouponDTOS) {

        HsaMemberCouponPackageLink hsaMemberCouponPackageLink = new HsaMemberCouponPackageLink();
        hsaMemberCouponPackageLink.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberCouponPackageLink.class.getSimpleName()));

        //校验优惠券是否异常
        if (CollUtil.isNotEmpty(responseCouponDTOS)) {
            String reason = "部分优惠券状态异常";
            if (responseCouponDTOS.size() == activityVO.getCouponGuidList().size()) {
                reason = "优惠券状态异常";
            }
            hsaMemberCouponPackageLinks.add(MemberCouponAssembler.newMemberCouponPackageLink(
                    hsaMemberCouponPackageLink,
                    activityVO,
                    memberPhoneDTO,
                    now,
                    BooleanEnum.FALSE.getCode(),
                    reason));
            return true;
        }

        //todo 校验库存  需要后台编辑保存更新
        int num = cacheService.getInventoryNum(StringConstant.COUPON_PACKAGE_INVENTORY_NUM + activityVO.getActivityCode(),
                activityVO.getActivityCode());

        log.info("活动名称:{},当前库存：{}", activityVO.getActivityName(), num);

        if (num != -100 && num <= 0) {
            hsaMemberCouponPackageLinks.add(
                    MemberCouponAssembler.newMemberCouponPackageLink(
                            hsaMemberCouponPackageLink,
                            activityVO,
                            memberPhoneDTO,
                            now,
                            BooleanEnum.FALSE.getCode(),
                            "活动券包库存不足"));
            return true;
        }

        //会员校验
        if (memberPhoneDTO.getAccountState() == 1) {
            hsaMemberCouponPackageLinks.add(
                    MemberCouponAssembler.newMemberCouponPackageLink(
                            hsaMemberCouponPackageLink,
                            activityVO,
                            memberPhoneDTO,
                            now,
                            BooleanEnum.FALSE.getCode(),
                            "会员账户已禁用"));
            return true;
        }

        hsaMemberCouponPackageLinks.add(
                MemberCouponAssembler.newMemberCouponPackageLink(
                        hsaMemberCouponPackageLink,
                        activityVO,
                        memberPhoneDTO,
                        now,
                        BooleanEnum.TRUE.getCode(),
                        ""));

        if (num != -100) {
            log.info("活动名称:{},更新库存：{}", activityVO.getActivityName(), num - 1);
            cacheService.updateInventoryNum(StringConstant.COUPON_PACKAGE_INVENTORY_NUM + activityVO.getActivityCode(),
                    activityVO.getActivityCode());
        }

        //todo  测试是否能够准确记录发放券包数量
        cacheService.addInventoryNum(StringConstant.SUCCESS_COUPON_PACKAGE_NUM + activityVO.getActivityCode(), activityVO.getActivityCode());
        return false;
    }

    private Set<MemberPhoneDTO> checkMemberCrowd(Map<String, Set<MemberPhoneDTO>> allOperationMemberInfoMap,
                                                 EditCouponPackageActivityVO activityVO) {
        Set<MemberPhoneDTO> partOperationMemberInfo = new HashSet<>();
        //获取发放人群
        if (activityVO.getGiveMemberType() == 0) {
            partOperationMemberInfo = allOperationMemberInfoMap.get(activityVO.getOperSubjectGuid());
            if (CollUtil.isEmpty(partOperationMemberInfo)) {
                getAllOperationMemberSet(activityVO, allOperationMemberInfoMap);
                partOperationMemberInfo = allOperationMemberInfoMap.get(activityVO.getOperSubjectGuid());
            }
            //校验是否单次发放  需筛选掉已发放过的用户
            return filterSingle(activityVO, partOperationMemberInfo);
        } else {
            return getPartCrowd(activityVO, partOperationMemberInfo);
        }
    }

    /**
     * 过滤掉已发放的用户
     *
     * @param activityVO 活动
     * @param partOperationMemberInfo 用户列表
     * @return 未发放用户
     */
    private Set<MemberPhoneDTO> filterSingle(EditCouponPackageActivityVO activityVO, Set<MemberPhoneDTO> partOperationMemberInfo) {
        if (activityVO.getGiveFrequencyType() == 0) {
            List<String> guidList = mapper.queueMemberGuidByGuid(activityVO.getGuid());
            if (CollUtil.isNotEmpty(guidList)) {
                // 差集 (list2 - list1)
                final HashSet<MemberPhoneDTO> memberPhones = new HashSet<>(partOperationMemberInfo);
                memberPhones.removeIf(memberPhoneDTO -> guidList.contains(memberPhoneDTO.getGuid()));
                return memberPhones;
            }
        }
        return partOperationMemberInfo;
    }

    private Set<MemberPhoneDTO> getPartCrowd(EditCouponPackageActivityVO activityVO, Set<MemberPhoneDTO> partOperationMemberInfo) {
        Set<String> guidSet = new HashSet<>();
        //标签人群
        if (Objects.nonNull(activityVO.getLabelList()) && CollUtil.isNotEmpty(activityVO.getLabelList().getGuidList())) {
            Set<String> labelMemberList = hsaMemberLabelMapper.selectList(new LambdaQueryWrapper<HsaMemberLabel>()
                            .in(HsaMemberLabel::getLabelSettingGuid, activityVO.getLabelList().getGuidList()))
                    .stream()
                    .map(HsaMemberLabel::getOperationMemberInfoGuid)
                    .collect(Collectors.toSet());
            guidSet.addAll(labelMemberList);
        }

        //等级人群
        if (Objects.nonNull(activityVO.getGradeList()) && CollUtil.isNotEmpty(activityVO.getGradeList().getGuidList())) {
            List<String> memberGradeList = hsaOperationInfoMapper.queryMemberGrade(activityVO.getGradeList().getGuidList());
            guidSet.addAll(memberGradeList);
        }

        //指定会员人群
        if (Objects.nonNull(activityVO.getMemberList()) && CollUtil.isNotEmpty(activityVO.getMemberList().getGuidList())) {
            guidSet.addAll(activityVO.getMemberList().getGuidList());
        }
        //校验是否单次发放  需筛选掉已发放过的用户
        Set<String> finalGuid = checkSingle(activityVO, guidSet);

        //获取会员
        partOperationMemberInfo.addAll(checkMemberGuid(new ArrayList<>(finalGuid)));
        return partOperationMemberInfo;
    }

    private Set<String> checkSingle(EditCouponPackageActivityVO activityVO, Set<String> guidSet) {
        if (activityVO.getGiveFrequencyType() == 0) {
            List<String> guidList = mapper.queueMemberGuidByGuid(activityVO.getGuid());
            if (CollUtil.isNotEmpty(guidList)) {
                // 差集 (list2 - list1)
                return subList2(new ArrayList<>(guidSet), guidList);
            }
        }
        return guidSet;
    }

    /**
     * 差集(基于java8新特性)优化解法2 适用于大数据量
     * 求List1中有的但是List2中没有的元素
     */
    public static Set<String> subList2(List<String> list1, List<String> list2) {
        Map<String, String> tempMap = list2.parallelStream()
                .collect(Collectors.toMap(Function.identity(), Function.identity(), (oldData, newData) -> newData));
        return list1.parallelStream().filter(str -> !tempMap.containsKey(str)).collect(Collectors.toSet());
    }

    private void getAllOperationMemberSet(EditCouponPackageActivityVO activityVO,
                                          Map<String, Set<MemberPhoneDTO>> allOperationMemberInfoMap) {
        Set<String> guidSet = hsaOperationInfoMapper.queueSubjectGuid(activityVO.getOperSubjectGuid());

        Set<MemberPhoneDTO> partOperationMemberInfo = checkMemberGuid(new ArrayList<>(guidSet));

        allOperationMemberInfoMap.put(activityVO.getOperSubjectGuid(), partOperationMemberInfo);
    }


    /**
     * 分批发放
     */
    public Set<MemberPhoneDTO> checkMemberGuid(List<String> memberGuid) {
        Set<MemberPhoneDTO> operationMemberInfoSet = new HashSet<>();
        if (CollUtil.isEmpty(memberGuid)) {
            return operationMemberInfoSet;
        }
        int pointsDataLimit = 5000;//限制条数
        int size = memberGuid.size();
        //判断是否有必要分批
        if (pointsDataLimit < size) {
            int part = size / pointsDataLimit;//分批数
            log.info("总条数：{}，分为:{}批", size, part);
            for (int i = 0; i < part; i++) {
                //500条
                List<String> guid = memberGuid.subList(0, pointsDataLimit);

                operationMemberInfoSet.addAll(hsaOperationInfoMapper.listByGuid(guid));
                //剔除
                memberGuid.subList(0, pointsDataLimit).clear();
            }
            if (!memberGuid.isEmpty()) {
                log.info("最后剩下的数据:{}", memberGuid);
                //获取数据
                operationMemberInfoSet.addAll(hsaOperationInfoMapper.listByGuid(memberGuid));
            }
        } else {
            //获取数据
            operationMemberInfoSet.addAll(hsaOperationInfoMapper.listByGuid(memberGuid));
        }
        return operationMemberInfoSet;
    }


}
