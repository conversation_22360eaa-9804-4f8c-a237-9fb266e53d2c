package com.holderzone.member.queue.service.card;


import com.holderzone.member.queue.config.mybatis.IHolderBaseService;
import com.holderzone.member.queue.entity.HsaMemberInfoCard;
import com.holderzone.member.queue.entity.HsaStoreCardRule;

/**
 * <p>
 * 会员卡门店适用表接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-31
 */
public interface HsaStoreCardRuleService extends IHolderBaseService<HsaStoreCardRule> {

    Integer deleteByCardGuid(String cardGuid);

    void saveStoreCardRule(String cardGuid, String newMemberCardGuid, String memberCardGuid);

    /**
     * 门店合并处理
     *
     * @param hsaMemberInfoCard
     * @param memberPhysicalCard
     */
    void mergeStoreProcessor(HsaMemberInfoCard hsaMemberInfoCard, HsaMemberInfoCard memberPhysicalCard);

    /**
     * 会员卡范围合并处理
     *
     * @param hsaMemberInfoCard
     * @param cardGuid
     */
    void mergeStoreProcessor(HsaMemberInfoCard hsaMemberInfoCard, String cardGuid);
}
