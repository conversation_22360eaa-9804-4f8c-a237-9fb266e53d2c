package com.holderzone.member.queue.service.member.impl;

import com.holderzone.member.queue.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.queue.entity.HsaGrowthCommodityTouchDetail;
import com.holderzone.member.queue.mapper.HsaGrowthCommodityTouchDetailMapper;
import com.holderzone.member.queue.service.member.HsaGrowthCommodityTouchDetailService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订单任务商品触发次数、数量累计表
 * @date 2021/12/11 16:05
 */
@Service
public class HsaGrowthCommodityTouchDetailServiceImpl extends HolderBaseServiceImpl<HsaGrowthCommodityTouchDetailMapper, HsaGrowthCommodityTouchDetail> implements HsaGrowthCommodityTouchDetailService {
}
