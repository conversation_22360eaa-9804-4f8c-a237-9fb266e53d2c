package com.holderzone.member.queue.dto.grade;

import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.queue.dto.member.MemberGrowthValueEvent;
import com.holderzone.member.queue.entity.HsaOperationMemberInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023年04月06日 下午6:35
 * @description 等级变化实体
 */
@Data
public class MemberGradeChangeDTO implements Serializable {

    private static final long serialVersionUID = 9149847575104422909L;

    private List<HsaOperationMemberInfo> hsaOperationMemberInfos;

    private String operSubjectGuid;

    private Integer sourceType;

    private Integer isRefresh;

    private String roleType;

    private Integer system;

    private String memberInfoGradeGuid;

    private Integer unit;

    private Integer num;

    @ApiModelProperty(value = "备注", required = true)
    private String remark;

    /**
     * 强制更新标识
     */
    private Integer issuerType;

    public static MemberGradeChangeDTO getMemberGradeChangeDTO(MemberGrowthValueEvent event) {
        MemberGradeChangeDTO changeDTO = new MemberGradeChangeDTO();
        changeDTO.setOperSubjectGuid(event.getOperSubjectGuid());
        changeDTO.setSourceType(event.getSource());
        changeDTO.setIsRefresh(BooleanEnum.FALSE.getCode());
        changeDTO.setRoleType(event.getRoleType());
        if (Objects.nonNull(event.getHeaderUserInfo())){
            changeDTO.setSystem(event.getHeaderUserInfo().getSystem());
        }
        changeDTO.setMemberInfoGradeGuid(event.getMemberInfoGradeGuid());
        changeDTO.setUnit(event.getUnit());
        changeDTO.setNum(event.getNum());
        changeDTO.setRemark(event.getRemark());
        return changeDTO;
    }
}
