package com.holderzone.member.queue.event.consumer;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.member.common.constant.BinderConstant;
import com.holderzone.member.common.dto.event.SendBackSubsidyRightsEvent;
import com.holderzone.member.queue.event.BinderChannel;
import com.holderzone.member.queue.event.processor.SubsidySendBackRightsProcessor;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.messaging.handler.annotation.Header;

import java.io.IOException;

/**
 * 补贴活动权益发送
 *
 * <AUTHOR>
 * @description 补贴活动权益发送
 * @date 2021/10/27 11:32
 */
@Slf4j
@EnableBinding(BinderChannel.class)
public class SubsidySendBackRightsBinder {

    /**
     * 补贴活动权益发送
     */
    @StreamListener(BinderConstant.INPUT_SUBSIDY_SEND_BACK_RIGHTS)
    public void execute(Message<SendBackSubsidyRightsEvent> message,
                        @Header(AmqpHeaders.CHANNEL) Channel channel,
                        @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) throws IOException {
        SendBackSubsidyRightsEvent requestEvent = message.getPayload();
        if (StringUtils.isEmpty(requestEvent.getSessionId())) {
            requestEvent.setSessionId(String.valueOf(System.currentTimeMillis()));
        }
        log.info("[{}] [SendSubsidyRightsEvent] 参数={}", requestEvent.getSessionId(), JacksonUtils.writeValueAsString(requestEvent));
        try {
            new SubsidySendBackRightsProcessor(requestEvent).execute();
        } finally {
            //手动确认
            channel.basicAck(deliveryTag, false);
        }

    }

}