package com.holderzone.member.queue.entity;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 运营主体会员信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaOperationMemberInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会员GUID
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 会员账号，运营主体账号以OXXX123456789012，前缀不足补充在后面
     */
    private String memberAccount;

    /**
     * 会员等级guid
     */
    private String memberGradeInfoGuid;

    /**
     * 会员等级name
     */
    private String memberGradeInfoName;

    /**
     * 手机国家编码
     */
    private String phoneCountryCode;

    /**
     * 手机号码
     */
    private String phoneNum;

    /**
     * 顾客姓名
     */
    private String userName;

    /**
     * 微信是否授权 0未授权，1授权
     */
    private int wechatState;

    /**
     * 顾客昵称
     */
    private String nickName;

    /**
     * 性别，值为1时是男性，值为2时是女性，值为0时是未知
     */
    private Integer sex;

    /**
     * 出生日期
     */
    private LocalDate birthday;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 所属单位
     */
    private String workName;

    /**
     * 所属部门
     */
    private String departmentName;

    /**
     * 省编码
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String provinceCode;

    /**
     * 省名字
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String provinceName;

    /**
     * 市编码
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String cityCode;

    /**
     * 市名字
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String cityName;

    /**
     * 区编码
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String areaCode;

    /**
     * 区名字
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String areaName;

    /**
     * 联系地址
     */
    private String contactAddress;

    /**
     * 用户密码
     */
    private String password;

    /**
     * 支付密码
     */
    private String payPassword;

    /**
     * 禁用原因
     */
    private String reasonForDisable;

    /**
     * 最后消费时间
     */
    private LocalDateTime lastConsumptionTime;

    /**
     * 操作人guid
     */
    private String operatorGuid;

    /**
     * 操作人名字
     */
    private String operatorName;

    /**
     * 企业GUID
     */
    private String enterpriseGuid;

    /**
     * 会员来源企业名字
     */
    private String enterpriseName;

    /**
     * 品牌GUID
     */
    private String brandGuid;

    /**
     * 会员来源品牌名字
     */
    private String brandName;

    /**
     * 门店GUID
     */
    private String storeGuid;

    /**
     * 会员来源门店名称
     */
    private String storeName;

    /**
     * 会员来源归属，0门店，1品牌，2企业，3运营主体，4掌控者平台
     */
    private Integer sourceAscription;

    /**
     * 会员注册来源,0后台添加,1POS机注册,2一体机注册,3后台导入，微网站(21微信扫码点餐，24微信注册.25微信C端后台注册)，微信小程序(51和惠多，52翼惠天下，53赚餐)
     */
    private Integer sourceType;

    /**
     * 账号状态,0正常,1冻结
     */
    private Integer accountState;

    /**
     * 是否删除,0未删除,1已删除
     */
    private Integer isDelete;

    /**
     * 头像url
     */
    private String headImgUrl;

    /**
     * 会员账户余额
     */
    private BigDecimal memberAccountMoney;

    /**
     * 会员账户赠送余额
     */
    private BigDecimal memberGiftAccountMoney;

    /**
     * 会员积分
     */
    private Integer memberIntegral;

    /**
     * 会员成长值
     */
    private Integer memberGrowthValue;

    /**
     * 会员补贴总金额
     */
    private BigDecimal memberSubsidyAmountMoney;

    /**
     * 完善个人信息dto
     * @see com.holderzone.member.common.dto.integral.PerfectPersonalDetailDTO
     */
    private String perfectPersonalDetailJson;


    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    /**
     * 会员等级升级时间
     */
    private LocalDateTime upgradeTime;

    /**
     * 是否开启积分购物默认抵扣（0：未开启  1：开启）默认为未开启
     */
    private Integer integralDeduction;

    /**
     * 支付宝是否授权 0未授权，1授权
     */
    private int aliState;

    /**
     * 顾客昵称
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String aliNickName;

    /**
     * 头像url
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String aliHeadImgUrl;
}
