package com.holderzone.member.queue.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @program: member-marketing
 * @description: 会员等级基础信息
 * @author: pan tao
 * @create: 2021-12-30 17:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberGradeInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 会员等级guid
     */
    private String guid;

    /**
     * 运营主体
     */
    private String operSubjectGuid;

    /**
     * 等级类型
     */
    private Integer type;


    /**
     * 会员等级
     */
    private int vipGrade;

    /**
     * 等级名称
     */
    private String name;

    /**
     * 等级图标
     */
    private String gradeIcon;

    /**
     * 背景颜色
     */
    private String backgroundColor;

    /**
     * 升级成长值
     */
    private int growthValue;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

    /**
     * 是否删除,0未删除,1已删除,2删除但有效
     */
//    @TableLogic//逻辑删除标识
//    @TableField(select = false)//查询的时候不显示
    private Integer isDelete;

    /**
     * 默认等级 0:不是 1:是
     *
     * @see com.holderzone.member.common.enums.grade.DefaultGradeEnum
     */
    private Integer defaultGrade;

    /**
     * 操作人 姓名/账号
     */
    private String operatorName;

    /**
     * 是否有效 0:无效 1:有效
     */
    private Integer effective;

    /**
     * 临时登记
     */
    private Integer temporaryVipGrade;

    /**
     * 角色类型
     *
     * @see com.holderzone.member.common.enums.member.RoleTypeEnum
     */
    private String roleType;

    /**
     * 升级机制：1成长值升级 2付费升级
     *
     * @see com.holderzone.member.common.enums.grade.UpgradeTypeEnum
     */
    private Integer upgradeType;

    /**
     * 付费规则
     *
     * @see com.holderzone.member.common.dto.grade.PayRuleDTO
     */
    private String payRule;

    /**
     * 付费限制：0关闭 1开启
     */
    private Integer payLimit;

    /**
     * 审核说明
     */
    private String auditDesc;
}
