package com.holderzone.member.queue.service.assembler;

import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.ResOrderCommodity;
import com.holderzone.member.common.dto.grade.MemberGradeRelationDTO;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.grade.GradeCardOpenTypeEnum;
import com.holderzone.member.common.enums.grade.GradeCardStatusEnum;
import com.holderzone.member.common.enums.grade.GradeRecordChangeEnum;
import com.holderzone.member.common.enums.growth.GrowthValueValidityTypeEnum;
import com.holderzone.member.common.enums.growth.SumValueChangeEnum;
import com.holderzone.member.common.enums.member.GradeChangeTypeEnum;
import com.holderzone.member.common.enums.member.GradeRightsTypeEnum;
import com.holderzone.member.common.enums.member.GradeTypeEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.grade.MemberGradePayRecordReqVO;
import com.holderzone.member.queue.dto.grade.GradeRightsProcessorDTO;
import com.holderzone.member.queue.dto.grade.MemberGradeChangeDTO;
import com.holderzone.member.queue.dto.member.MemberGrowthValueEvent;
import com.holderzone.member.queue.entity.*;
import com.holderzone.member.queue.service.grade.HsaMemberGradeChangeDetailService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 *
 * 公共方法抽取
 */
@Service
public class GradeChangeAssembler {

    public static final String NO_GRADE = "-1";

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private HsaMemberGradeChangeDetailService hsaMemberGradeChangeDetailService;


    public HsaGrowthValueDetail getHsaGrowthValueDetail(String memberGuid,
                                                         Integer sourceType,
                                                         HsaMemberGradeInfo hsaMemberGradeInfo,
                                                         HsaBusinessEquities hsaBusinessEquities,
                                                         int growthValue,
                                                         int totalGrowthValue,
                                                         MemberGradeChangeDTO gradeChangeDTO) {
        HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        hsaGrowthValueDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaGrowthValueDetail.class.getSimpleName()));
        hsaGrowthValueDetail.setGrowthValueSourceType(sourceType);
        hsaGrowthValueDetail.setOperatorAccountName(hsaMemberGradeInfo.getOperatorName());
        hsaGrowthValueDetail.setOperSubjectGuid(hsaMemberGradeInfo.getOperSubjectGuid());
        hsaGrowthValueDetail.setCurrentMemberLevel(
                hsaMemberGradeInfo.getName() +
                        StringConstant.LEFT_BRACKET +
                        StringConstant.VIP +
                        hsaMemberGradeInfo.getVipGrade() +
                        StringConstant.RIGHT_BRACKET);
        hsaGrowthValueDetail.setRecordDeclaration(hsaMemberGradeInfo.getName() + StringConstant.STR_BIAS_TWO +
                hsaBusinessEquities.getEquitiesName() + StringConstant.STR_BIAS_TWO +
                hsaBusinessEquities.getEquitiesNumber());
        hsaGrowthValueDetail.setGrowthValue(growthValue);
        hsaGrowthValueDetail.setChangeType(SumValueChangeEnum.GIVE_GROWTH_VALUE.getCode());
        hsaGrowthValueDetail.setMemberInfoGuid(memberGuid);
        hsaGrowthValueDetail.setGrowthValidity(NumberConstant.NUMBER_0);
        hsaGrowthValueDetail.setGrowthValueType(NumberConstant.NUMBER_0);
        hsaGrowthValueDetail.setRemainGrowthValue(totalGrowthValue);
        hsaGrowthValueDetail.setRecordRemainGrowthValue(growthValue);
        hsaGrowthValueDetail.setRoleType(gradeChangeDTO.getRoleType());
        hsaGrowthValueDetail.setNum(gradeChangeDTO.getNum());
        hsaGrowthValueDetail.setUnit(gradeChangeDTO.getUnit());
        if (!Objects.equals(NO_GRADE, gradeChangeDTO.getMemberInfoGradeGuid())) {
            hsaGrowthValueDetail.setExpireTime(DateUtil.getFirstExpireTime(gradeChangeDTO.getNum(), gradeChangeDTO.getUnit()));
        }
        hsaGrowthValueDetail.setMemberInfoGradeGuid(hsaMemberGradeInfo.getGuid());
        hsaGrowthValueDetail.setUpgradeType(hsaMemberGradeInfo.getUpgradeType());
        return hsaGrowthValueDetail;
    }


    public HsaIntegralDetail getHsaIntegralValueDetail(String memberGuid,
                                                        Integer sourceType,
                                                        String operSubjectGuid,
                                                        HsaMemberGradeInfo hsaMemberGradeInfo,
                                                        HsaBusinessEquities hsaBusinessEquities,
                                                        int growthValue,
                                                        int totalGrowthValue) {
        HsaIntegralDetail hsaIntegralDetail = new HsaIntegralDetail();
        hsaIntegralDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaIntegralDetail.class.getSimpleName()));
        hsaIntegralDetail.setIntegralSourceType(sourceType);
        hsaIntegralDetail.setOperatorAccountName(hsaMemberGradeInfo.getOperatorName());
        hsaIntegralDetail.setOperSubjectGuid(operSubjectGuid);
        hsaIntegralDetail.setCurrentMemberLevel(
                hsaMemberGradeInfo.getName() +
                        StringConstant.LEFT_BRACKET +
                        StringConstant.VIP +
                        hsaMemberGradeInfo.getVipGrade() +
                        StringConstant.RIGHT_BRACKET);
        hsaIntegralDetail.setDeclaration(hsaMemberGradeInfo.getName() + StringConstant.STR_BIAS_TWO +
                hsaBusinessEquities.getEquitiesName() + StringConstant.STR_BIAS_TWO +
                hsaBusinessEquities.getEquitiesNumber());
        hsaIntegralDetail.setIntegral(growthValue);
        hsaIntegralDetail.setRecordDeclaration(hsaMemberGradeInfo.getName() + StringConstant.STR_BIAS_TWO +
                hsaBusinessEquities.getEquitiesName() + StringConstant.STR_BIAS_TWO +
                hsaBusinessEquities.getEquitiesNumber());
        hsaIntegralDetail.setChangeType(SumValueChangeEnum.GIVE_GROWTH_VALUE.getCode());
        hsaIntegralDetail.setMemberInfoGuid(memberGuid);
        hsaIntegralDetail.setIntegralValidity(NumberConstant.NUMBER_0);
        hsaIntegralDetail.setIntegralType(NumberConstant.NUMBER_0);
        hsaIntegralDetail.setCurrentIntegral(totalGrowthValue);
        hsaIntegralDetail.setRecordRemainIntegral(growthValue);
        return hsaIntegralDetail;
    }

    public HsaMemberEquitiesReceiveRecord getHsaMemberEquitiesReceiveRecord(String operSubjectGuid,
                                                                             String memberGuid,
                                                                             String memberGradeGuid,
                                                                             String gradeEquitiesGuid,
                                                                             Integer giveGrowthValueNumber,
                                                                             Integer type) {
        HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord = new HsaMemberEquitiesReceiveRecord();
        hsaMemberEquitiesReceiveRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaGrowthValueDetail.class.getSimpleName()));
        hsaMemberEquitiesReceiveRecord.setOperSubjectGuid(operSubjectGuid);
        hsaMemberEquitiesReceiveRecord.setMemberGradeGuid(memberGradeGuid);
        hsaMemberEquitiesReceiveRecord.setMemberInfoGuid(memberGuid);
        hsaMemberEquitiesReceiveRecord.setGradeEquitiesGuid(gradeEquitiesGuid);
        hsaMemberEquitiesReceiveRecord.setGiveGrowthValueNumber(giveGrowthValueNumber);
        hsaMemberEquitiesReceiveRecord.setGmtCreate(LocalDateTime.now());
        hsaMemberEquitiesReceiveRecord.setGmtModified(LocalDateTime.now());
        hsaMemberEquitiesReceiveRecord.setType(type);
        return hsaMemberEquitiesReceiveRecord;
    }

    public void getMemberGradeChangeDetail(HsaOperationMemberInfo hsaOperationMemberInfo,
                                            HsaMemberGradeInfo afterMemberGradeInfo,
                                            HsaMemberGradeInfo beforeMemberGradeInfo,
                                            HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail,
                                            LocalDateTime localDateTime) {
        hsaMemberGradeChangeDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberGradeChangeDetail.class.getSimpleName()))
                .setMemberInfoGuid(hsaOperationMemberInfo.getGuid())
                .setOperSubjectGuid(hsaOperationMemberInfo.getOperSubjectGuid())
                .setAfterChangeGradeGuid(afterMemberGradeInfo.getGuid())
                .setAfterChangeGradeName(afterMemberGradeInfo.getName())
                .setAfterChangeGradeVipGrade(afterMemberGradeInfo.getVipGrade())
                .setGmtCreate(localDateTime)
                .setGmtModified(localDateTime)
                .setAfterChangeGradeIcon(afterMemberGradeInfo.getGradeIcon())
                .setAfterChangeBackgroundColor(afterMemberGradeInfo.getBackgroundColor())
                .setAfterChangeGrowthValue(afterMemberGradeInfo.getGrowthValue());
        if (Objects.isNull(beforeMemberGradeInfo)) {
            hsaMemberGradeChangeDetail.setIsShowApplet(BooleanEnum.TRUE.getCode())
                    .setGradeChangeType(GradeChangeTypeEnum.UPGRADE.getCode());
        } else {
            hsaMemberGradeChangeDetail.setBeforeChangeGradeGuid(beforeMemberGradeInfo.getGuid())
                    .setBeforeChangeGradeName(beforeMemberGradeInfo.getName())
                    .setBeforeChangeGradeVipGrade(beforeMemberGradeInfo.getVipGrade())
                    .setIsShowApplet(BooleanEnum.FALSE.getCode())
                    .setGradeChangeType(afterMemberGradeInfo.getGrowthValue() > beforeMemberGradeInfo.getGrowthValue() ?
                            GradeChangeTypeEnum.UPGRADE.getCode() : GradeChangeTypeEnum.DEMOTION.getCode());
        }
    }

    public HsaMemberGradeRightsRecord getHsaMemberGradeRightsRecord(GradeRightsProcessorDTO gradeRightsProcessorDTO,
                                                                     HsaOperationMemberInfo operationMemberInfo,
                                                                     HsaGradeGiftBag hsaGradeGiftBag) {
        HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberGradeRightsRecord.class.getSimpleName()))
                .setMemberInfoGuid(operationMemberInfo.getGuid())
                .setOperSubjectGuid(gradeRightsProcessorDTO.getOperSubjectGuid())
                .setMemberGradeGuid(hsaGradeGiftBag.getMemberGradeInfoGuid())
                .setRightsGuid(hsaGradeGiftBag.getGuid())
                .setGmtCreate(gradeRightsProcessorDTO.getNow())
                .setGmtModified(gradeRightsProcessorDTO.getNow())
                .setRightsType(GradeRightsTypeEnum.GRADE_RIGHTS_BAG.getCode());
        return hsaMemberGradeRightsRecord;
    }

    public HsaMemberGradeRightsRecord getMemberGradeRightsRecord(HsaOperationMemberInfo operationMemberInfo,
                                                                  String operSubjectGuid,
                                                                  LocalDateTime localDateTime,
                                                                  HsaBusinessEquities hsaGradeEquity) {
        Optional<HsaBusinessEquities> gradeEquity = Optional.ofNullable(hsaGradeEquity);
        HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberGradeRightsRecord.class.getSimpleName()))
                .setMemberInfoGuid(operationMemberInfo.getGuid())
                .setOperSubjectGuid(operSubjectGuid)
                .setMemberGradeGuid(gradeEquity.map(HsaBusinessEquities::getMemberGradeInfoGuid).orElse(null))
                .setRightsGuid(gradeEquity.map(HsaBusinessEquities::getGuid).orElse(""))
                .setEquitiesId(gradeEquity.map(HsaBusinessEquities::getId).orElse(-1L))
                .setGmtCreate(localDateTime)
                .setGmtModified(localDateTime)
                .setRightsType(GradeRightsTypeEnum.GRADE_RIGHTS.getCode());
        return hsaMemberGradeRightsRecord;
    }

    public HsaGrowthValueDetail getHsaGrowthValueDetail(HsaOperationMemberInfo operationMemberInfo,
                                                         Integer sourceType,
                                                         String operSubjectGuid,
                                                         HsaGradeGiftBag hsaGradeGiftBag,
                                                         HsaMemberGradeInfo hsaMemberGradeInfo,
                                                         HsaGiftBagBaseInfo hsaGiftBagBaseInfo,
                                                         HsaMemberGradeInfo afterMemberGradeInfo) {
        HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        hsaGrowthValueDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaGrowthValueDetail.class.getSimpleName()));
        hsaGrowthValueDetail.setGrowthValueSourceType(sourceType);
        hsaGrowthValueDetail.setOperatorAccountName(hsaMemberGradeInfo.getOperatorName());
        hsaGrowthValueDetail.setOperSubjectGuid(operSubjectGuid);
        hsaGrowthValueDetail.setCurrentMemberLevel(
                afterMemberGradeInfo.getName() +
                        StringConstant.LEFT_BRACKET +
                        StringConstant.VIP +
                        afterMemberGradeInfo.getVipGrade() +
                        StringConstant.RIGHT_BRACKET);
        hsaGrowthValueDetail.setRecordDeclaration(hsaMemberGradeInfo.getName() + StringConstant.STR_BIAS_TWO + hsaGiftBagBaseInfo.getEquitiesName());
        hsaGrowthValueDetail.setGrowthValue(hsaGradeGiftBag.getValue());
        hsaGrowthValueDetail.setChangeType(SumValueChangeEnum.BAG.getCode());
        hsaGrowthValueDetail.setMemberInfoGuid(operationMemberInfo.getGuid());
        hsaGrowthValueDetail.setGrowthValidity(NumberConstant.NUMBER_0);
        hsaGrowthValueDetail.setGrowthValueType(NumberConstant.NUMBER_0);
        hsaGrowthValueDetail.setRemainGrowthValue(operationMemberInfo.getMemberGrowthValue());
        hsaGrowthValueDetail.setRecordRemainGrowthValue(hsaGradeGiftBag.getValue());
        return hsaGrowthValueDetail;
    }




    public HsaIntegralDetail getHsaIntegralValueDetail(HsaOperationMemberInfo operationMemberInfo,
                                                        Integer sourceType,
                                                        String operSubjectGuid,
                                                        HsaGradeGiftBag hsaGradeGiftBag,
                                                        HsaMemberGradeInfo hsaMemberGradeInfo,
                                                        HsaGiftBagBaseInfo hsaGiftBagBaseInfo,
                                                        HsaMemberGradeInfo afterMemberGradeInfo) {
        HsaIntegralDetail hsaIntegralDetail = new HsaIntegralDetail();
        hsaIntegralDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaGrowthValueDetail.class.getSimpleName()));
        hsaIntegralDetail.setIntegralSourceType(sourceType);
        hsaIntegralDetail.setOperatorAccountName(hsaMemberGradeInfo.getOperatorName());
        hsaIntegralDetail.setOperSubjectGuid(operSubjectGuid);
        hsaIntegralDetail.setCurrentMemberLevel(
                afterMemberGradeInfo.getName() +
                        StringConstant.LEFT_BRACKET +
                        StringConstant.VIP +
                        afterMemberGradeInfo.getVipGrade() +
                        StringConstant.RIGHT_BRACKET);
        hsaIntegralDetail.setRecordDeclaration(hsaMemberGradeInfo.getName() + StringConstant.STR_BIAS_TWO + hsaGiftBagBaseInfo.getEquitiesName());
        hsaIntegralDetail.setIntegral(hsaGradeGiftBag.getValue());
        hsaIntegralDetail.setChangeType(SumValueChangeEnum.BAG.getCode());
        hsaIntegralDetail.setMemberInfoGuid(operationMemberInfo.getGuid());
        hsaIntegralDetail.setIntegralValidity(NumberConstant.NUMBER_0);
        hsaIntegralDetail.setIntegralType(NumberConstant.NUMBER_0);
        hsaIntegralDetail.setCurrentIntegral(operationMemberInfo.getMemberIntegral());
        hsaIntegralDetail.setRecordRemainIntegral(hsaGradeGiftBag.getValue());
        return hsaIntegralDetail;
    }

    public MemberGradePayRecordReqVO getMemberGradePayRecordReqVO(MemberGradeChangeDTO gradeChangeDTO,
                                                                         List<String> updateMemberGuidList) {
        MemberGradePayRecordReqVO recordReqVO = new MemberGradePayRecordReqVO();
        recordReqVO.setMemberGuidList(updateMemberGuidList);
        recordReqVO.setMemberInfoGradeGuid(gradeChangeDTO.getMemberInfoGradeGuid());
        recordReqVO.setRoleType(gradeChangeDTO.getRoleType());
        recordReqVO.setNum(gradeChangeDTO.getNum());
        recordReqVO.setChangeType(GradeRecordChangeEnum.ADJUST.getCode());
        recordReqVO.setUnit(gradeChangeDTO.getUnit());
        recordReqVO.setRemark(gradeChangeDTO.getRemark());
        recordReqVO.setOperSubjectGuid(gradeChangeDTO.getOperSubjectGuid());
        return recordReqVO;
    }

    public MemberGradeRelationDTO getMemberGradeRelationDTO(MemberGradeChangeDTO gradeChangeDTO,
                                                                   List<String> updateMemberGuidList) {
        MemberGradeRelationDTO relationDTO = new MemberGradeRelationDTO();
        relationDTO.setMemberGuidList(updateMemberGuidList);
        relationDTO.setMemberInfoGradeGuid(gradeChangeDTO.getMemberInfoGradeGuid());
        relationDTO.setRoleType(gradeChangeDTO.getRoleType());
        relationDTO.setNum(gradeChangeDTO.getNum());
        relationDTO.setUnit(gradeChangeDTO.getUnit());
        relationDTO.setOperSubjectGuid(gradeChangeDTO.getOperSubjectGuid());
        relationDTO.setSourceType(gradeChangeDTO.getSourceType());
        return relationDTO;
    }

    public HsaGrowthValueDetail getHsaGrowthValueDetail(HsaGrowthValueTask hsaGrowthValueTask,
                                                         Integer sum,
                                                         HsaOperationMemberInfo hsaOperationMemberInfo,
                                                         HsaMemberGradeInfo hsaMemberGradeInfo) {
        HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        hsaGrowthValueDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaGrowthValueDetail.class.getSimpleName()));
        hsaGrowthValueDetail.setGrowthValueSourceType(SourceTypeEnum.ADD_BACKGROUND.getCode());
        hsaGrowthValueDetail.setOperatorAccountName(hsaGrowthValueTask.getOperatorTelName());
        hsaGrowthValueDetail.setOperSubjectGuid(hsaGrowthValueTask.getOperSubjectGuid());
        hsaGrowthValueDetail.setChangeType(SumValueChangeEnum.INVALIDATION.getCode());
        hsaGrowthValueDetail.setRecordDeclaration("成长值失效");
        hsaGrowthValueDetail.setGrowthValue(sum);
        hsaGrowthValueDetail.setMemberInfoGuid(hsaOperationMemberInfo.getGuid());
        hsaGrowthValueDetail.setGrowthValueType(NumberConstant.NUMBER_1);
        hsaGrowthValueDetail.setRemainGrowthValue(hsaOperationMemberInfo.getMemberGrowthValue());
        setCurrentMemberLevel(hsaOperationMemberInfo, hsaMemberGradeInfo, hsaGrowthValueDetail);
        return hsaGrowthValueDetail;
    }

    public void setCurrentMemberLevel(HsaOperationMemberInfo hsaOperationMemberInfo,
                                       HsaMemberGradeInfo hsaMemberGradeInfo,
                                       HsaGrowthValueDetail hsaGrowthValueDetail) {
        HsaMemberGradeInfo memberGradeInfo = hsaMemberGradeChangeDetailService.calculateExtraAmount(hsaOperationMemberInfo.getMemberGrowthValue(), hsaOperationMemberInfo.getOperSubjectGuid());
        if (!memberGradeInfo.getGuid().equals(hsaOperationMemberInfo.getMemberGradeInfoGuid())) {
            hsaGrowthValueDetail.setCurrentMemberLevel(memberGradeInfo.getName() +
                    StringConstant.LEFT_BRACKET +
                    StringConstant.VIP +
                    memberGradeInfo.getVipGrade() +
                    StringConstant.RIGHT_BRACKET);
        } else {
            hsaGrowthValueDetail.setCurrentMemberLevel(hsaMemberGradeInfo.getName() +
                    StringConstant.LEFT_BRACKET +
                    StringConstant.VIP +
                    hsaMemberGradeInfo.getVipGrade() +
                    StringConstant.RIGHT_BRACKET);
        }
    }

    public void saveSatisfyTouchDetail(MemberGrowthValueEvent event, HsaGrowthValueTask hsaGrowthValueTask,
                                        List<HsaGrowthCommodityTouchDetail> saveGrowthCommodityTouchDetails) {
        HsaGrowthCommodityTouchDetail touchDetail = new HsaGrowthCommodityTouchDetail();
        touchDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaGrowthCommodityTouchDetail.class.getSimpleName()))
                .setOperSubjectGuid(event.getOperSubjectGuid())
                .setMemberInfoGuid(event.getMemberInfoGuidList().get(0))
                .setGrowthValueTaskGuid(hsaGrowthValueTask.getGuid())
                .setBuyType(hsaGrowthValueTask.getBuyType())
                .setMemberConsumptionGuid(event.getMemberConsumptionGuid())
                .setOrderNumber(event.getOrderNumber())
                .setIsDelete(BooleanEnum.FALSE.getCode())
                .setOrderTime(LocalDateTime.now());
        saveGrowthCommodityTouchDetails.add(touchDetail);
    }

    public void getHsaGrowthCommodityQuantityTouchDetailList(HsaGrowthValueTask hsaGrowthValueTask, MemberGrowthValueEvent event, List<HsaGrowthCommodityQuantityTouchDetail> touchDetails, ResOrderCommodity in) {
        HsaGrowthCommodityQuantityTouchDetail hsaGrowthCommodityQuantityTouchDetail = new HsaGrowthCommodityQuantityTouchDetail();
        hsaGrowthCommodityQuantityTouchDetail
                .setCommodityId(String.valueOf(in.getProductId()))
                .setCommodityName(in.getProduct_name())
                .setCommodityNum(in.getQuantity())
                .setCommodityCode(in.getProductCode())
                .setStrategyId(String.valueOf(in.getStrategyId()))
                .setMemberConsumptionGuid(event.getMemberConsumptionGuid())
                .setMemberInfoGuid(event.getMemberInfoGuidList().get(0))
                .setOperSubjectGuid(event.getOperSubjectGuid())
                .setGrowthValueTaskGuid(hsaGrowthValueTask.getGuid());
        touchDetails.add(hsaGrowthCommodityQuantityTouchDetail);
    }

    public HsaGrowthValueDetail getHsaGrowthValueDetail(MemberGrowthValueEvent event,
                                                        HsaGrowthValueTask hsaGrowthValueTask,
                                                        HsaOperationMemberInfo hsaOperationMemberInfo,
                                                        LocalDateTime localDateTime,
                                                        LocalDateTime dateTime,
                                                        Integer num) {
        HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        hsaGrowthValueDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaGrowthValueDetail.class.getSimpleName()));
        hsaGrowthValueDetail.setGrowthValueSourceType(event.getSource());
        hsaGrowthValueDetail.setOperatorAccountName(event.getOperatorAccountName());

        hsaGrowthValueDetail.setOperSubjectGuid(hsaOperationMemberInfo.getOperSubjectGuid());
        hsaGrowthValueDetail.setChangeType(SumValueChangeEnum.TASK.getCode());
        hsaGrowthValueDetail.setRecordDeclaration(hsaGrowthValueTask.getTaskName() + "-" + hsaGrowthValueTask.getTaskNumber());
        if (hsaGrowthValueTask.getGrowthValueValidityType() == GrowthValueValidityTypeEnum.PERMANENT_VALIDITY.getCode()) {
            hsaGrowthValueDetail.setGrowthValidity(GrowthValueValidityTypeEnum.PERMANENT_VALIDITY.getCode());
        } else {
            hsaGrowthValueDetail.setGrowthValidity(NumberConstant.NUMBER_1);
            hsaGrowthValueDetail.setGrowthValidityDate(localDateTime);
        }
        hsaGrowthValueDetail.setTaskFinishDate(dateTime);
        hsaGrowthValueDetail.setGrowthValueTaskId(String.valueOf(hsaGrowthValueTask.getId()));

        hsaGrowthValueDetail.setTaskFinishTime(num + NumberConstant.NUMBER_1);
        hsaGrowthValueDetail.setGrowthValueType(NumberConstant.NUMBER_0);
        hsaGrowthValueDetail.setTaskAction(hsaGrowthValueTask.getTaskAction());

        hsaGrowthValueDetail.setGmtCreate(dateTime);
        hsaGrowthValueDetail.setGmtModified(dateTime);
        hsaGrowthValueDetail.setTaskName(hsaGrowthValueTask.getTaskName());
        hsaGrowthValueDetail.setTaskNumber(hsaGrowthValueTask.getTaskNumber());
        hsaGrowthValueDetail.setStoreGuid(event.getStoreGuid());
        hsaGrowthValueDetail.setStoreName(event.getStoreName());
        hsaGrowthValueDetail.setOrderNumber(event.getOrderNumber());
        hsaGrowthValueDetail.setMemberConsumptionGuid(event.getMemberConsumptionGuid());
        return hsaGrowthValueDetail;
    }

    public void getHsaMemberGradeCard (HsaOperationMemberInfo hsaOperationMemberInfo, HsaMemberGradeInfo afterMemberGradeInfo,HsaMemberGradeCard hsaMemberGradeCard ) {
        String gradeCardGuid = guidGeneratorUtil.getStringGuid(HsaMemberGradeCard.class.getSimpleName());
        hsaMemberGradeCard
                                                        .setGradeGuid(afterMemberGradeInfo.getGuid())
                                                        .setGradeType(GradeTypeEnum.FREE.getCode())
                                                        .setVipGrade(afterMemberGradeInfo.getVipGrade())
                                                        .setOpenType(GradeCardOpenTypeEnum.PLATFORM_GIVE.getValue())
                                                        .setMemberCardStatus(GradeCardStatusEnum.NORMAL.getCode())
                                                        .setMemberInfoGuid(hsaOperationMemberInfo.getGuid())
                                                        .setOperSubjectGuid(hsaOperationMemberInfo.getOperSubjectGuid());
        hsaMemberGradeCard.setGuid(gradeCardGuid);
    }


}
