package com.holderzone.member.queue.service.activity.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.constant.WechatMsgParamConstant;
import com.holderzone.member.common.dto.event.SendBackSubsidyRightsEvent;
import com.holderzone.member.common.dto.event.SendSubsidyRightsEvent;
import com.holderzone.member.common.dto.event.SubsidyActivityEvent;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.DateUnitEnum;
import com.holderzone.member.common.enums.activity.ActivityStateEnum;
import com.holderzone.member.common.enums.card.CardStatusEnum;
import com.holderzone.member.common.enums.card.CardTypeEnum;
import com.holderzone.member.common.enums.member.AmountSourceTypeEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.enums.wechat.WechatMsgSendType;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.qo.activity.SubsidySpecific;
import com.holderzone.member.common.qo.card.CardFreezeBalanceAmountQO;
import com.holderzone.member.common.qo.member.MemberListQO;
import com.holderzone.member.common.qo.tool.MessagesSendQO;
import com.holderzone.member.common.qo.tool.MessagesSendbatchQO;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.date.ValidTimeUtils;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import com.holderzone.member.common.util.transaction.SpringContextUtils;
import com.holderzone.member.common.util.transaction.TransactionUtil;
import com.holderzone.member.common.vo.member.MemberInfoVO;
import com.holderzone.member.queue.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.queue.dto.activity.CreationSubsidyDetailRecordDTO;
import com.holderzone.member.queue.dto.activity.SendCheckSubsidyDTO;
import com.holderzone.member.queue.entity.*;
import com.holderzone.member.queue.mapper.*;
import com.holderzone.member.queue.service.activity.HsaSubsidyActivityDetailRecordService;
import com.holderzone.member.queue.service.activity.HsaSubsidyActivityService;
import com.holderzone.member.queue.service.activity.assembler.SubsidyActivityAssembler;
import com.holderzone.member.queue.service.gift.HsaCardRechargeGiftDetailService;
import com.holderzone.member.queue.service.member.HsaMemberFundingDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 会员食堂卡补贴活动
 * @date 2021/10/26 9:42
 */
@Service
@Slf4j
public class HsaSubsidyActivityServiceImpl extends HolderBaseServiceImpl<HsaSubsidyActivityMapper, HsaSubsidyActivity> implements HsaSubsidyActivityService {

    @Resource
    private HsaSubsidyActivityRecordMapper hsaSubsidyActivityRecordMapper;

    @Resource
    private HsaMemberLabelMapper hsaMemberLabelMapper;

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Resource
    private HsaMemberInfoCardMapper hsaMemberInfoCardMapper;

    @Resource
    private HsaSubsidyActivityMapper hsaSubsidyActivityMapper;

    @Resource
    private HsaCardBaseInfoMapper hsaCardBaseInfoMapper;

    @Resource
    private SystemRoleHelper systemRoleHelper;

    @Resource
    private HsaSubsidyActivityDetailRecordMapper hsaSubsidyActivityDetailRecordMapper;

    @Resource
    private HsaSubsidyActivityDetailRecordService hsaSubsidyActivityDetailRecordService;

    @Resource
    private HsaMemberFundingDetailService hsaMemberFundingDetailService;

    @Resource
    private HsaCardRechargeGiftDetailService hsaCardRechargeGiftDetailService;

    @Resource
    private MemberBaseFeign memberBaseFeign;

    @Resource
    private Executor memberQueueThreadExecutor;

    @Resource
    private TransactionUtil transactionUtil;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private SubsidyActivityAssembler subsidyActivityAssembler;

    //锁等待时间
    private static final Integer LOCK_WAIT_TIME = 60;

    //锁的持有时间
    private static final Integer LOCK_LEASE_TIME = 60;

    @Override
    public void sendSubsidyRights(SendSubsidyRightsEvent event, Integer retryCount) {
        log.info("queue处理补贴发放开始>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", event);

        if (Objects.isNull(event.getDate())) {
            event.setDate(new Date());
            log.info("new Date");
        }

        String current = DateUtil.getTmpDate(event.getDate(), StringConstant.FORMAT);

        log.info("补贴发放->>>>>>>>>>>当前执行时间：{}", current);

        RLock lock = redissonClient.getLock(StringConstant.XXL_JOB_SUBSIDY + current);
        try {
            if (!lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS)) {
                log.info("sendSubsidyRights开始轮训={}，剩余次数={}", JSON.toJSONString(event), retryCount);
                if (retryCount > BigDecimal.ROUND_UP) {
                    sendSubsidyRights(event, retryCount - 1);
                }
            }
            dealSubsidyRight(event);
        } catch (Exception e) {
            log.info("sendSubsidyRights 锁异常");
            Thread.currentThread().interrupt();
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.info("补贴发放锁已释放");
            }
        }
    }

    private void dealSubsidyRight(SendSubsidyRightsEvent event) {
        //补贴活动
        Map<String, HsaSubsidyActivity> hsaSubsidyActivityMap = getStringHsaSubsidyActivityMap(event);
        //统一到账时间 (以此时间为基础计算补贴金到期时间)
        LocalDateTime now = LocalDateTime.now();
        for (SubsidyActivityEvent subsidyActivityEvent : event.getSubsidyActivityEventList()) {
            //补贴记录明细
            List<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords = Lists.newArrayList();
            //补贴记录
            List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords = Lists.newArrayList();
            //补贴活动
            List<HsaSubsidyActivity> hsaSubsidyActivities = Lists.newArrayList();
            //会员持卡
            List<HsaMemberInfoCard> hsaMemberInfoCards = Lists.newArrayList();
            //资金记录明细
            List<HsaMemberFundingDetail> hsaMemberFundingDetails = Lists.newArrayList();

            //整合校验资源
            SendCheckSubsidyDTO sendCheckSubsidyDTO = getSendCheckSubsidyDTO(hsaSubsidyActivityMap, subsidyActivityEvent);
            sendCheckSubsidyDTO.setNow(now);

            //处理校验发放逻辑
            checkSendSubsidyDeal(subsidyActivityEvent,
                    hsaSubsidyActivityRecords,
                    hsaSubsidyActivities,
                    sendCheckSubsidyDTO,
                    hsaSubsidyActivityDetailRecords,
                    hsaMemberInfoCards,
                    hsaMemberFundingDetails);

            log.info("处理补贴发放持久化>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", JSON.toJSONString(hsaSubsidyActivities));
            dataPersistence(hsaSubsidyActivityDetailRecords, hsaSubsidyActivityRecords, hsaSubsidyActivities, hsaMemberInfoCards, hsaMemberFundingDetails);
            // 发送小程序、公众号消息通知
            sendChangeCardAmountMsg(hsaMemberInfoCards, hsaMemberFundingDetails);
        }
    }

    private void checkSendSubsidyDeal(SubsidyActivityEvent subsidyActivityEvent,
                                      List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords,
                                      List<HsaSubsidyActivity> hsaSubsidyActivities,
                                      SendCheckSubsidyDTO sendCheckSubsidyDTO,
                                      List<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords,
                                      List<HsaMemberInfoCard> hsaMemberInfoCards,
                                      List<HsaMemberFundingDetail> hsaMemberFundingDetails) {
        //发放关联标签用户
        if (subsidyActivityEvent.getSubsidyFilterType() == 0) {
            //获取发放用户
            List<String> labelGuidList = JSON.parseArray(subsidyActivityEvent.getLabelGuidJson(), String.class);
            List<HsaMemberLabel> hsaMemberLabelList = hsaMemberLabelMapper.selectList(new LambdaQueryWrapper<HsaMemberLabel>()
                    .in(HsaMemberLabel::getLabelSettingGuid, labelGuidList));
            //若为空则提前处理数据
            if (CollectionUtils.isEmpty(hsaMemberLabelList)) {
                checkNotMember(hsaSubsidyActivityRecords, hsaSubsidyActivities, sendCheckSubsidyDTO);
            } else {
                Set<String> memberGuidList = hsaMemberLabelList.stream().map(HsaMemberLabel::getOperationMemberInfoGuid).collect(Collectors.toSet());
                integrate(hsaSubsidyActivityDetailRecords,
                        hsaSubsidyActivityRecords,
                        hsaSubsidyActivities,
                        hsaMemberInfoCards,
                        hsaMemberFundingDetails,
                        memberGuidList,
                        sendCheckSubsidyDTO);
            }
        }
        //发放指定用户
        else if (subsidyActivityEvent.getSubsidyFilterType() == 1) {
            Set<String> member = new HashSet<>(JSON.parseArray(subsidyActivityEvent.getMemberInfoGuidJson(), String.class));
            //若为空则提前处理数据
            if (CollectionUtils.isEmpty(member)) {
                checkNotMember(hsaSubsidyActivityRecords, hsaSubsidyActivities, sendCheckSubsidyDTO);
            } else {
                integrate(hsaSubsidyActivityDetailRecords,
                        hsaSubsidyActivityRecords,
                        hsaSubsidyActivities,
                        hsaMemberInfoCards,
                        hsaMemberFundingDetails,
                        member,
                        sendCheckSubsidyDTO);
            }
        }
        //发放主体下所有启用用户
        else {
            Set<String> guidList = hsaOperationMemberInfoMapper.listByOperSubjectGuid(subsidyActivityEvent.getOperSubjectGuid());
            //若为空则提前处理数据
            if (CollectionUtils.isEmpty(guidList)) {
                checkNotMember(hsaSubsidyActivityRecords, hsaSubsidyActivities, sendCheckSubsidyDTO);
            } else {
                integrate(hsaSubsidyActivityDetailRecords,
                        hsaSubsidyActivityRecords,
                        hsaSubsidyActivities,
                        hsaMemberInfoCards,
                        hsaMemberFundingDetails,
                        guidList,
                        sendCheckSubsidyDTO);
            }
        }
    }

    public SendCheckSubsidyDTO getSendCheckSubsidyDTO(Map<String, HsaSubsidyActivity> hsaSubsidyActivityMap,
                                                      SubsidyActivityEvent subsidyActivityEvent) {
        SendCheckSubsidyDTO sendCheckSubsidyDTO = new SendCheckSubsidyDTO();

        //发放记录
        Map<String, HsaSubsidyActivityRecord> hsaSubsidyActivityRecordMap = getStringHsaSubsidyActivityRecordMap(subsidyActivityEvent);
        //卡基础数据
        Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap = getStringHsaCardBaseInfoMap(subsidyActivityEvent);
        //获取对应活动
        HsaSubsidyActivity hsaSubsidyActivity = hsaSubsidyActivityMap.get(subsidyActivityEvent.getGuid());

        sendCheckSubsidyDTO.setHsaSubsidyActivityMap(hsaSubsidyActivityMap);
        sendCheckSubsidyDTO.setHsaSubsidyActivityRecordMap(hsaSubsidyActivityRecordMap);
        sendCheckSubsidyDTO.setHsaCardBaseInfoMap(hsaCardBaseInfoMap);
        sendCheckSubsidyDTO.setHsaSubsidyActivity(hsaSubsidyActivity);
        sendCheckSubsidyDTO.setSubsidyActivityEvent(subsidyActivityEvent);
        return sendCheckSubsidyDTO;
    }


    private void dataPersistence(List<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords,
                                 List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords,
                                 List<HsaSubsidyActivity> hsaSubsidyActivities,
                                 List<HsaMemberInfoCard> hsaMemberInfoCards,
                                 List<HsaMemberFundingDetail> hsaMemberFundingDetails) {
        transactionUtil.transactional((s) -> {
            if (CollUtil.isNotEmpty(hsaSubsidyActivityDetailRecords)) {
                hsaSubsidyActivityDetailRecordService.saveBatch(hsaSubsidyActivityDetailRecords);
            }
            if (CollUtil.isNotEmpty(hsaSubsidyActivityRecords)) {
                hsaSubsidyActivityRecordMapper.batchUpdateStatus(hsaSubsidyActivityRecords);
            }
            if (CollUtil.isNotEmpty(hsaSubsidyActivities)) {
                hsaSubsidyActivityMapper.batchUpdateStatus(hsaSubsidyActivities);
            }
            if (CollUtil.isNotEmpty(hsaMemberInfoCards)) {
                hsaMemberInfoCardMapper.batchUpdate(hsaMemberInfoCards);
            }
            if (CollUtil.isNotEmpty(hsaMemberFundingDetails)) {
                hsaMemberFundingDetailService.saveBatch(hsaMemberFundingDetails);
            }
        });

        log.info("处理补贴发放结束>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", JSON.toJSONString(hsaSubsidyActivities));
    }

    /**
     * 补贴发送小程序公众号消息
     */
    private void sendChangeCardAmountMsg(List<HsaMemberInfoCard> hsaMemberInfoCards, List<HsaMemberFundingDetail> hsaMemberFundingDetails) {
        if (CollectionUtils.isEmpty(hsaMemberInfoCards) || CollectionUtils.isEmpty(hsaMemberFundingDetails)) {
            return;
        }
        memberQueueThreadExecutor.execute(() -> {
            List<MessagesSendQO> messagesSendList = buildMessagesSendList(hsaMemberInfoCards, hsaMemberFundingDetails);
            log.info("补贴发送小程序公众号消息：{}", JacksonUtils.writeValueAsString(messagesSendList));
            if (!CollectionUtils.isEmpty(messagesSendList)) {
                // 根据企业分组
                Map<String, List<MessagesSendQO>> messageSendGroupByEnterpriseGuidMap = messagesSendList.stream()
                        .collect(Collectors.groupingBy(MessagesSendQO::getEnterpriseGuid));
                for (Map.Entry<String, List<MessagesSendQO>> entry : messageSendGroupByEnterpriseGuidMap.entrySet()) {
                    HeaderUserInfo headerUserInfo = new HeaderUserInfo();
                    headerUserInfo.setEnterpriseGuid(entry.getKey());
                    ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));
                    memberBaseFeign.wechatMessageSendBatch(new MessagesSendbatchQO(entry.getValue()));
                    memberBaseFeign.shortMessageSendBatch(new MessagesSendbatchQO(entry.getValue()));
                }
            }
        });
    }

    /**
     * 构建发送消息模板 请求
     */
    private List<MessagesSendQO> buildMessagesSendList(List<HsaMemberInfoCard> hsaMemberInfoCards, List<HsaMemberFundingDetail> hsaMemberFundingDetails) {
        // 批量查询卡赠送冻结金额
        List<CardFreezeBalanceAmountQO> balanceAmountQueryList = hsaMemberInfoCards.stream().map(e -> {
            CardFreezeBalanceAmountQO balanceAmountQuery = new CardFreezeBalanceAmountQO();
            balanceAmountQuery.setMemberInfoCardGuid(e.getGuid());
            balanceAmountQuery.setIsRefresh(BooleanEnum.FALSE.getCode());
            return balanceAmountQuery;
        }).collect(Collectors.toList());
        Map<String, BigDecimal> freightAmountMap = hsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(balanceAmountQueryList);
        log.info("批量查询卡赠送冻结金额, freightAmountMap:{}", JacksonUtils.writeValueAsString(freightAmountMap));

        // 批量查询卡信息
        List<String> cardGuids = hsaMemberInfoCards.stream().map(HsaMemberInfoCard::getCardGuid).distinct().collect(Collectors.toList());
        List<HsaCardBaseInfo> cardBaseInfoList = hsaCardBaseInfoMapper.queryByGuids(cardGuids);
        Map<String, HsaCardBaseInfo> cardBaseInfoMap = cardBaseInfoList.stream()
                .collect(Collectors.toMap(HsaCardBaseInfo::getGuid, Function.identity(), (key1, key2) -> key1));

        // 批量查询会员
        List<String> memberInfoGuids = hsaMemberFundingDetails.stream().map(HsaMemberFundingDetail::getMemberInfoGuid).distinct().collect(Collectors.toList());
        MemberListQO queryMember = new MemberListQO();
        queryMember.setMemberGuids(memberInfoGuids);
        List<MemberInfoVO> memberPhoneList = hsaOperationMemberInfoMapper.listMemberInfo(queryMember);
        Map<String, MemberInfoVO> memberMap = memberPhoneList.stream()
                .collect(Collectors.toMap(MemberInfoVO::getMemberGuid, Function.identity(), (key1, key2) -> key1));

        Map<String, HsaMemberInfoCard> memberInfoCardMap = hsaMemberInfoCards.stream()
                .collect(Collectors.toMap(HsaMemberInfoCard::getGuid, Function.identity(), (key1, key2) -> key1));

        // 批量发送消息请求
        List<MessagesSendQO> messagesSendList = Lists.newArrayList();
        for (HsaMemberFundingDetail fundingDetail : hsaMemberFundingDetails) {
            HsaMemberInfoCard memberInfoCard = memberInfoCardMap.get(fundingDetail.getMemberInfoCardGuid());
            if (Objects.isNull(memberInfoCard)) {
                continue;
            }
            HsaCardBaseInfo cardBaseInfo = cardBaseInfoMap.getOrDefault(memberInfoCard.getCardGuid(), new HsaCardBaseInfo());
            MemberInfoVO memberInfo = memberMap.getOrDefault(fundingDetail.getMemberInfoGuid(), new MemberInfoVO());
            // 总余额
            BigDecimal amount = memberInfoCard.getCardAmount().add(memberInfoCard.getGiftAmount()).add(memberInfoCard.getSubsidyAmount());
            BigDecimal freightAmount = freightAmountMap.getOrDefault(memberInfoCard.getGuid(), BigDecimal.ZERO);
            BigDecimal totalAmount = amount.add(freightAmount);

            MessagesSendQO messagesSendQO = new MessagesSendQO();
            messagesSendQO.setTemplateName(WechatMsgSendType.AMOUNT_CHANGE.getMsgTitle());
            messagesSendQO.setCardName(cardBaseInfo.getCardName());
            messagesSendQO.setCardImage(cardBaseInfo.getCardImage());
            messagesSendQO.setPrams(generateAppletsParam(fundingDetail, totalAmount));
            messagesSendQO.setMpParams(generateMpParam(fundingDetail, totalAmount));
            messagesSendQO.setOrderNum(Strings.EMPTY);
            messagesSendQO.setMemberInfoCardGuid(fundingDetail.getMemberInfoCardGuid());
            messagesSendQO.setPhone(memberInfo.getPhoneNum());
            messagesSendQO.setOperSubjectGuid(memberInfo.getOperSubjectGuid());
            messagesSendQO.setEnterpriseGuid(memberInfo.getEnterpriseGuid());

            messagesSendQO.setMemberName(memberInfo.getUserName());
            messagesSendQO.setSubsidyAmount(fundingDetail.getSubsidyAmount());
            messagesSendQO.setCardBalance(totalAmount);
            messagesSendQO.setAmountSourceType(fundingDetail.getAmountSourceType());
            messagesSendList.add(messagesSendQO);
        }
        return messagesSendList;
    }

    /**
     * 构建小程序资金变动模板消息参数
     */
    private Map<String, String> generateAppletsParam(HsaMemberFundingDetail hsaMemberFundingDetail, BigDecimal totalAmount) {
        Map<String, String> params = new HashMap<>();
        params.put(WechatMsgParamConstant.InnerAmountChangeConstant.APPLET_CHANGE_TIME, ValidTimeUtils.getDate());
        params.put(WechatMsgParamConstant.InnerAmountChangeConstant.APPLET_CHANGE_REASON, "补贴");
        params.put(WechatMsgParamConstant.InnerAmountChangeConstant.APPLET_CHANGE_AMOUNT, "+" + hsaMemberFundingDetail.getSubsidyAmount().toPlainString());
        params.put(WechatMsgParamConstant.InnerAmountChangeConstant.APPLET_CURRENT_AMOUNT, totalAmount.toPlainString());
        return params;
    }

    /**
     * 构建公众号资金变动模板消息参数
     */
    private Map<String, String> generateMpParam(HsaMemberFundingDetail hsaMemberFundingDetail, BigDecimal totalAmount) {
        Map<String, String> params = new HashMap<>();
        // 门店名称
        params.put(WechatMsgParamConstant.InnerAmountChangeConstant.MP_STORE_NAME, hsaMemberFundingDetail.getStoreName());
        params.put(WechatMsgParamConstant.InnerAmountChangeConstant.MP_CHANGE_TIME, ValidTimeUtils.getDate());
        params.put(WechatMsgParamConstant.InnerAmountChangeConstant.MP_CHANGE_AMOUNT, "+" + hsaMemberFundingDetail.getSubsidyAmount().toPlainString());
        params.put(WechatMsgParamConstant.InnerAmountChangeConstant.MP_CURRENT_AMOUNT, totalAmount.toPlainString());
        return params;
    }

    /**
     * 分批获取数据
     */
    public Set<HsaMemberInfoCard> checkMemberInfoCard(List<String> memberInfoGuid, Set<String> cardGuid) {
        Set<HsaMemberInfoCard> hsaMemberInfoCards = new HashSet<>();
        int pointsDataLimit = 500;//限制条数
        int size = memberInfoGuid.size();
        //判断是否有必要分批
        if (pointsDataLimit < size) {
            int part = size / pointsDataLimit;//分批数
            for (int i = 0; i < part; i++) {
                //500条
                List<String> guid = memberInfoGuid.subList(0, pointsDataLimit);

                hsaMemberInfoCards.addAll(hsaMemberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                        .in(HsaMemberInfoCard::getMemberInfoGuid, guid)
                        .in(HsaMemberInfoCard::getCardGuid, cardGuid)));
                //剔除
                memberInfoGuid.subList(0, pointsDataLimit).clear();
            }
            if (!memberInfoGuid.isEmpty()) {
                //获取数据
                hsaMemberInfoCards.addAll(hsaMemberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                        .in(HsaMemberInfoCard::getMemberInfoGuid, memberInfoGuid)
                        .in(HsaMemberInfoCard::getCardGuid, cardGuid)));
            }
        } else {
            //获取数据
            hsaMemberInfoCards.addAll(hsaMemberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                    .in(HsaMemberInfoCard::getMemberInfoGuid, memberInfoGuid)
                    .in(HsaMemberInfoCard::getCardGuid, cardGuid)));
        }
        return hsaMemberInfoCards;
    }

    /**
     * 分批回收
     */
    public Set<HsaSubsidyActivityDetailRecord> checkSubsidyActivityDetailRecord(List<String> activityDetailRecordGuid) {
        Set<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords = new HashSet<>();
        int pointsDataLimit = 500;//限制条数
        int size = activityDetailRecordGuid.size();
        //判断是否有必要分批
        if (pointsDataLimit < size) {
            int part = size / pointsDataLimit;//分批数
            log.info("共有 ： " + size + "条，！" + " 分为 ：" + part + "批");
            for (int i = 0; i < part; i++) {
                //500条
                List<String> guid = activityDetailRecordGuid.subList(0, pointsDataLimit);

                hsaSubsidyActivityDetailRecords.addAll(hsaSubsidyActivityDetailRecordMapper.selectList(new LambdaQueryWrapper<HsaSubsidyActivityDetailRecord>()
                        .in(HsaSubsidyActivityDetailRecord::getGuid, guid)));
                //剔除
                activityDetailRecordGuid.subList(0, pointsDataLimit).clear();
            }
            if (!activityDetailRecordGuid.isEmpty()) {
                //获取数据
                hsaSubsidyActivityDetailRecords.addAll(hsaSubsidyActivityDetailRecordMapper.selectList(new LambdaQueryWrapper<HsaSubsidyActivityDetailRecord>()
                        .in(HsaSubsidyActivityDetailRecord::getGuid, activityDetailRecordGuid)));
            }
        } else {
            //获取数据
            hsaSubsidyActivityDetailRecords.addAll(hsaSubsidyActivityDetailRecordMapper.selectList(new LambdaQueryWrapper<HsaSubsidyActivityDetailRecord>()
                    .in(HsaSubsidyActivityDetailRecord::getGuid, activityDetailRecordGuid)));
        }
        return hsaSubsidyActivityDetailRecords;
    }

    /**
     * 分批发放
     */
    public Set<HsaOperationMemberInfo> checkMemberGuid(List<String> memberGuid) {
        Set<HsaOperationMemberInfo> hsaOperationMemberInfos = new HashSet<>();
        int pointsDataLimit = 1000;//限制条数
        int size = memberGuid.size();
        //判断是否有必要分批
        if (pointsDataLimit < size) {
            int part = size / pointsDataLimit;//分批数
            log.info("共有 ： " + size + "条，！" + " 分为 ：" + part + "批");
            for (int i = 0; i < part; i++) {
                //500条
                List<String> guid = memberGuid.subList(0, pointsDataLimit);

                hsaOperationMemberInfos.addAll(hsaOperationMemberInfoMapper.selectList(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                        .in(HsaOperationMemberInfo::getGuid, guid)));
                //剔除
                memberGuid.subList(0, pointsDataLimit).clear();
            }
            if (!memberGuid.isEmpty()) {
                //获取数据
                hsaOperationMemberInfos.addAll(hsaOperationMemberInfoMapper.selectList(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                        .in(HsaOperationMemberInfo::getGuid, memberGuid)));
            }
        } else {
            //获取数据
            hsaOperationMemberInfos.addAll(hsaOperationMemberInfoMapper.selectList(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                    .in(HsaOperationMemberInfo::getGuid, memberGuid)));
        }
        return hsaOperationMemberInfos;
    }

    /**
     * 回收补贴
     *
     * @param event event
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendSubsidyBackRights(SendBackSubsidyRightsEvent event, Integer retryCount) {

        log.info("queue处理补贴回收开始>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", event);

        String current = DateUtil.getTmpDate(event.getDate(), StringConstant.FORMAT);

        log.info("补贴回收->>>>>>>>>>>当前执行时间：{}", current);

        RLock lock = redissonClient.getLock(StringConstant.XXL_JOB_SUBSIDY + current);
        try {
            if (!lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS)) {
                log.info("sendSubsidyBackRights 锁超时={} 开始轮训,剩余次数={}", JSON.toJSONString(event), retryCount);
                HsaSubsidyActivityService subsidyActivityService = SpringContextUtils.getBean(HsaSubsidyActivityService.class);
                if (retryCount > BigDecimal.ROUND_UP) {
                    subsidyActivityService.sendSubsidyBackRights(event, retryCount - 1);
                }
            }
            dealSubsidyBackRights(event);
        } catch (Exception e) {
            log.info("sendSubsidyBackRights 锁异常");
            Thread.currentThread().interrupt();
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.info("补贴回收锁已释放");
            }
        }
    }


    private void dealSubsidyBackRights(SendBackSubsidyRightsEvent event) {
        log.info("queue回收补贴开始>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", event);
        Set<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecordList = checkSubsidyActivityDetailRecord(event.getSubsidyActivityEventList());
        //获取会员持卡信息
        List<String> memberInfoGuidList = hsaSubsidyActivityDetailRecordList.stream()
                .map(HsaSubsidyActivityDetailRecord::getMemberInfoGuid).collect(Collectors.toList());

        Set<String> cardGuidList = hsaSubsidyActivityDetailRecordList.stream()
                .map(HsaSubsidyActivityDetailRecord::getCardGuid)
                .collect(Collectors.toSet());

        //获取发放记录
        List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords = hsaSubsidyActivityRecordMapper.selectList(new LambdaQueryWrapper<HsaSubsidyActivityRecord>()
                .in(HsaSubsidyActivityRecord::getGuid, hsaSubsidyActivityDetailRecordList.stream().map(HsaSubsidyActivityDetailRecord::getSubsidyActivityRecordGuid).collect(Collectors.toList())));
        Map<String, HsaSubsidyActivityRecord> hsaSubsidyActivityRecordMap = hsaSubsidyActivityRecords
                .stream()
                .collect(Collectors.toMap(HsaSubsidyActivityRecord::getGuid, Function.identity(), (entity1, entity2) -> entity1));

        List<HsaSubsidyActivityDetailRecord> subsidyActivityDetailRecords = Lists.newArrayList();
        List<HsaMemberFundingDetail> hsaMemberFundingDetails = Lists.newArrayList();
        List<HsaMemberInfoCard> hsaMemberInfoCardList = Lists.newArrayList();
        Map<String, HsaMemberInfoCard> memberInfoCardMap = new HashMap<>();

        //获取持卡信息
        Map<String, HsaMemberInfoCard> hsaMemberInfoCardMap = checkMemberInfoCard(memberInfoGuidList, cardGuidList)
                .stream()
                .collect(Collectors.toMap(HsaMemberInfoCard::getGuid, Function.identity(), (entity1, entity2) -> entity1));
        //回收发放
        for (HsaSubsidyActivityDetailRecord detailRecord : hsaSubsidyActivityDetailRecordList) {
            HsaMemberInfoCard hsaMemberInfoCard;
            if (CollUtil.isNotEmpty(memberInfoCardMap) && memberInfoCardMap.containsKey(detailRecord.getMemberInfoCardGuid())) {
                hsaMemberInfoCard = memberInfoCardMap.get(detailRecord.getMemberInfoCardGuid());
            } else {
                hsaMemberInfoCard = hsaMemberInfoCardMap.get(detailRecord.getMemberInfoCardGuid());
            }
            //若账户余额为空则跳出循环
            if (hsaMemberInfoCard.getSubsidyAmount().compareTo(BigDecimal.ZERO) < 1 || detailRecord.getSubsidyResidueMoney().compareTo(BigDecimal.ZERO) < 1) {
                detailRecord.setIsWithdraw(BooleanEnum.TRUE.getCode());
                subsidyActivityDetailRecords.add(detailRecord);
                memberInfoCardMap.put(hsaMemberInfoCard.getGuid(), hsaMemberInfoCard);
                continue;
            }
            HsaSubsidyActivityRecord hsaSubsidyActivityRecord = hsaSubsidyActivityRecordMap.get(detailRecord.getSubsidyActivityRecordGuid());
            detailRecord.setIsWithdraw(BooleanEnum.TRUE.getCode());
            //可退补贴处理
            if (detailRecord.getIsRetreat() == BooleanEnum.TRUE.getCode()) {
                hsaMemberInfoCard.setRetreatSubsidyAmount(hsaMemberInfoCard.getRetreatSubsidyAmount().subtract(detailRecord.getSubsidyMoney()).compareTo(BigDecimal.ZERO) > 0 ? hsaMemberInfoCard.getRetreatSubsidyAmount().subtract(detailRecord.getSubsidyMoney()) : BigDecimal.ZERO);
            }
            hsaMemberInfoCard.setSubsidyAmount(hsaMemberInfoCard.getSubsidyAmount().subtract(detailRecord.getSubsidyResidueMoney()).compareTo(BigDecimal.ZERO) > 0 ? hsaMemberInfoCard.getSubsidyAmount().subtract(detailRecord.getSubsidyResidueMoney()) : BigDecimal.ZERO);
            //资金明细记录
            HsaMemberFundingDetail memberFundingDetail = subsidyActivityAssembler.getHsaMemberFundingDetail(detailRecord, hsaMemberInfoCard, hsaSubsidyActivityRecord);
            hsaMemberFundingDetails.add(memberFundingDetail);
            detailRecord.setSubsidyResidueMoney(BigDecimal.ZERO);
            subsidyActivityDetailRecords.add(detailRecord);
            memberInfoCardMap.put(hsaMemberInfoCard.getGuid(), hsaMemberInfoCard);
        }
        //持久化
        memberQueueThreadExecutor.execute(() -> {
            dataPersistenceBack(subsidyActivityDetailRecords, hsaMemberFundingDetails, hsaMemberInfoCardList, memberInfoCardMap);
            sendChangeCardAmountMsg(hsaMemberInfoCardList, hsaMemberFundingDetails);
        });
    }


    private void dataPersistenceBack(List<HsaSubsidyActivityDetailRecord> subsidyActivityDetailRecords, List<HsaMemberFundingDetail> hsaMemberFundingDetails, List<HsaMemberInfoCard> hsaMemberInfoCardList, Map<String, HsaMemberInfoCard> memberInfoCardMap) {
        if (CollUtil.isNotEmpty(subsidyActivityDetailRecords)) {
            hsaSubsidyActivityDetailRecordMapper.batchUpdateStatus(subsidyActivityDetailRecords);
        }
        if (CollUtil.isNotEmpty(memberInfoCardMap)) {
            for (Map.Entry<String, HsaMemberInfoCard> hsaMemberInfoCardEntry : memberInfoCardMap.entrySet()) {
                hsaMemberInfoCardList.add(hsaMemberInfoCardEntry.getValue());
            }
            hsaMemberInfoCardMapper.batchUpdate(hsaMemberInfoCardList);
        }
        if (CollUtil.isNotEmpty(hsaMemberFundingDetails)) {
            hsaMemberFundingDetailService.saveBatch(hsaMemberFundingDetails);
        }
    }

    private void checkNotMember(List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords,
                                List<HsaSubsidyActivity> hsaSubsidyActivities,
                                SendCheckSubsidyDTO sendCheckSubsidyDTO) {

        Map<String, HsaSubsidyActivityRecord> hsaSubsidyActivityRecordMap = sendCheckSubsidyDTO.getHsaSubsidyActivityRecordMap();

        HsaSubsidyActivity hsaSubsidyActivity = sendCheckSubsidyDTO.getHsaSubsidyActivity();

        SubsidyActivityEvent subsidyActivityEvent = sendCheckSubsidyDTO.getSubsidyActivityEvent();

        for (String recordGuid : subsidyActivityEvent.getRecordGuid()) {
            //获取对应记录
            HsaSubsidyActivityRecord hsaSubsidyActivityRecord = hsaSubsidyActivityRecordMap.get(recordGuid);
            hsaSubsidyActivityRecord.setSubsidySendState(1);
            hsaSubsidyActivityRecords.add(hsaSubsidyActivityRecord);
        }
        hsaSubsidyActivity.setSubsidyMemberNum(0);
        hsaSubsidyActivities.add(hsaSubsidyActivity);
    }

    /**
     * 补贴发放核心方法
     *
     * @param hsaSubsidyActivityDetailRecords hsaSubsidyActivityDetailRecords
     * @param hsaSubsidyActivityRecords       hsaSubsidyActivityRecords
     * @param hsaSubsidyActivities            hsaSubsidyActivities
     * @param hsaMemberInfoCards              持卡数据
     * @param hsaMemberFundingDetails         资金记录
     * @param memberGuidList                  会员guid
     */
    private void integrate(List<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords,
                           List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords,
                           List<HsaSubsidyActivity> hsaSubsidyActivities,
                           List<HsaMemberInfoCard> hsaMemberInfoCards,
                           List<HsaMemberFundingDetail> hsaMemberFundingDetails,
                           Set<String> memberGuidList,
                           SendCheckSubsidyDTO sendCheckSubsidyDTO) {
        Map<String, HsaSubsidyActivityRecord> hsaSubsidyActivityRecordMap = sendCheckSubsidyDTO.getHsaSubsidyActivityRecordMap();

        Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap = sendCheckSubsidyDTO.getHsaCardBaseInfoMap();

        SubsidyActivityEvent subsidyActivityEvent = sendCheckSubsidyDTO.getSubsidyActivityEvent();

        HsaSubsidyActivity hsaSubsidyActivity = sendCheckSubsidyDTO.getHsaSubsidyActivity();

        LocalDateTime now = sendCheckSubsidyDTO.getNow();

        //获取会员数据
        Set<HsaOperationMemberInfo> hsaOperationMemberInfos = checkMemberGuid(new ArrayList<>(memberGuidList));
        Map<String, HsaOperationMemberInfo> hsaOperationMemberInfoMap = hsaOperationMemberInfos.stream()
                .collect(Collectors.toMap(HsaOperationMemberInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));
        hsaSubsidyActivity.setSubsidyMemberNum(hsaOperationMemberInfos.size());
        hsaSubsidyActivities.add(hsaSubsidyActivity);
        //以卡维度分类处理
        for (String recordGuid : subsidyActivityEvent.getRecordGuid()) {
            if (hsaSubsidyActivityRecordMap.containsKey(recordGuid)) {
                //获取对应记录
                HsaSubsidyActivityRecord hsaSubsidyActivityRecord = hsaSubsidyActivityRecordMap.get(recordGuid);
                hsaSubsidyActivityRecord.setSubsidySendState(1);
                //获取会员持卡数据
                Map<String, HsaMemberInfoCard> hsaMemberInfoCardMap = getStringHsaMemberInfoCardMap(memberGuidList, hsaSubsidyActivityRecord);
                //开始处理发放
                for (HsaOperationMemberInfo hsaOperationMemberInfo : hsaOperationMemberInfos) {
                    HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMap.get(hsaSubsidyActivityRecord.getCardGuid());

                    if (checkMemberCard(hsaSubsidyActivityDetailRecords,
                            hsaSubsidyActivityRecords,
                            hsaOperationMemberInfo,
                            hsaOperationMemberInfoMap,
                            hsaSubsidyActivityRecord,
                            hsaMemberInfoCardMap,
                            sendCheckSubsidyDTO)) {
                        continue;
                    }

                    //走完验证流程进入发放流程
                    HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMap.get(hsaOperationMemberInfo.getGuid());
                    //填充负金额
                    //实际补贴金额
                    BigDecimal amount = hsaSubsidyActivity.getSubsidyMoney();

                    //计算实际补贴到账金额
                    amount = calculateSubsidyAmount(hsaMemberInfoCard, hsaSubsidyActivity, amount, hsaCardBaseInfo);

                    //资金明细记录
                    HsaMemberFundingDetail memberFundingDetail = new HsaMemberFundingDetail();
                    HsaMemberFundingDetail hsaMemberFundingDetail = subsidyActivityAssembler
                            .getRechargeDetail(hsaMemberInfoCard, hsaSubsidyActivity, now, memberFundingDetail);

                    //持卡记录
                    hsaMemberInfoCards.add(hsaMemberInfoCard);
                    setSubsidyActivityDetail(hsaSubsidyActivityRecords, hsaSubsidyActivity, hsaSubsidyActivityRecord);
                    //记录补贴明细
                    CreationSubsidyDetailRecordDTO recordDTO = getCreationSubsidyDetailRecordDTO(now, amount, hsaMemberInfoCard, memberFundingDetail);

                    creationSubsidyDetailRecord(hsaSubsidyActivityDetailRecords,
                            hsaSubsidyActivityRecord,
                            hsaSubsidyActivity,
                            hsaOperationMemberInfo,
                            hsaMemberFundingDetail,
                            recordDTO);
                    hsaMemberFundingDetails.add(hsaMemberFundingDetail);
                }
            }
        }
    }

    private static CreationSubsidyDetailRecordDTO getCreationSubsidyDetailRecordDTO(LocalDateTime now,
                                                                                    BigDecimal amount,
                                                                                    HsaMemberInfoCard hsaMemberInfoCard,
                                                                                    HsaMemberFundingDetail memberFundingDetail) {
        CreationSubsidyDetailRecordDTO recordDTO = new CreationSubsidyDetailRecordDTO();
        recordDTO.setNow(now);
        recordDTO.setAmount(amount);
        recordDTO.setMemberInfoCardGuid(hsaMemberInfoCard.getGuid());
        recordDTO.setError(null);
        recordDTO.setResult(BooleanEnum.TRUE.getCode());
        recordDTO.setOutTime(memberFundingDetail.getOutOfDate());
        return recordDTO;
    }

    private static BigDecimal calculateSubsidyAmount(HsaMemberInfoCard hsaMemberInfoCard,
                                                     HsaSubsidyActivity hsaSubsidyActivity, BigDecimal amount, HsaCardBaseInfo hsaCardBaseInfo) {

        //若剩余金额小于0，则优先冲正
        if (hsaMemberInfoCard.getCardAmount().compareTo(BigDecimal.ZERO) < 0) {
            amount = checkExcessCardAmount(hsaMemberInfoCard, hsaSubsidyActivity, hsaCardBaseInfo);
        } else {
            hsaMemberInfoCard.setSubsidyAmount(Objects.nonNull(hsaMemberInfoCard.getSubsidyAmount()) ? hsaMemberInfoCard.getSubsidyAmount().add(amount) : amount);
            if (hsaSubsidyActivity.getIsRetreat() == BooleanEnum.TRUE.getCode()) {
                hsaMemberInfoCard.setRetreatSubsidyAmount(Objects.nonNull(hsaMemberInfoCard.getRetreatSubsidyAmount()) ? amount.add(hsaMemberInfoCard.getRetreatSubsidyAmount()) : amount);
            }
        }
        return amount;
    }

    private static BigDecimal checkExcessCardAmount(HsaMemberInfoCard hsaMemberInfoCard, HsaSubsidyActivity hsaSubsidyActivity, HsaCardBaseInfo hsaCardBaseInfo) {
        BigDecimal amount;
        if (hsaSubsidyActivity.getSubsidyMoney().subtract(hsaMemberInfoCard.getCardAmount()).compareTo(BigDecimal.ZERO) >= 0) {
            amount = hsaSubsidyActivity.getSubsidyMoney().subtract(hsaMemberInfoCard.getCardAmount());
            hsaMemberInfoCard.setCardAmount(BigDecimal.ZERO);
        } else {
            amount = BigDecimal.ZERO;
            hsaMemberInfoCard.setCardAmount(hsaMemberInfoCard.getCardAmount().add(hsaSubsidyActivity.getSubsidyMoney()));
        }
        //恢复超额及次数
        if (hsaMemberInfoCard.getCardAmount().compareTo(BigDecimal.ZERO) >= 0 && hsaCardBaseInfo.getIsExcess() == BooleanEnum.TRUE.getCode()) {
            if (hsaCardBaseInfo.getExcessType() == 0) {
                hsaMemberInfoCard.setExcessTimes(hsaCardBaseInfo.getExcessMoney().intValue());
            } else {
                hsaMemberInfoCard.setExcessAmount(hsaCardBaseInfo.getExcessMoney());
            }
        }
        return amount;
    }

    private boolean checkMemberCard(List<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords,
                                    List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords,
                                    HsaOperationMemberInfo hsaOperationMemberInfo,
                                    Map<String, HsaOperationMemberInfo> hsaOperationMemberInfoMap,
                                    HsaSubsidyActivityRecord hsaSubsidyActivityRecord,
                                    Map<String, HsaMemberInfoCard> hsaMemberInfoCardMap,
                                    SendCheckSubsidyDTO sendCheckSubsidyDTO) {
        HsaSubsidyActivity hsaSubsidyActivity = sendCheckSubsidyDTO.getHsaSubsidyActivity();

        Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap = sendCheckSubsidyDTO.getHsaCardBaseInfoMap();

        LocalDateTime now = LocalDateTime.now();

        CreationSubsidyDetailRecordDTO recordDTO = new CreationSubsidyDetailRecordDTO();
        recordDTO.setNow(now);
        recordDTO.setAmount(null);
        recordDTO.setMemberInfoCardGuid(null);
        recordDTO.setResult(BooleanEnum.FALSE.getCode());
        recordDTO.setOutTime(null);
        //【账户】已禁用
        if (hsaOperationMemberInfoMap.get(hsaOperationMemberInfo.getGuid()).getAccountState() == 1) {
            recordDTO.setError("【账户】已禁用");
            creationSubsidyDetailRecord(hsaSubsidyActivityDetailRecords, hsaSubsidyActivityRecord, hsaSubsidyActivity, hsaOperationMemberInfo, null, recordDTO);
            setSubsidyActivityDetail(hsaSubsidyActivityRecords, hsaSubsidyActivity, hsaSubsidyActivityRecord);
            return true;
        }
        //【会员卡】不存在
        if (CollUtil.isEmpty(hsaMemberInfoCardMap) || !hsaMemberInfoCardMap.containsKey(hsaOperationMemberInfo.getGuid())) {
            recordDTO.setError(systemRoleHelper.getReplace("【会员卡】不存在", hsaSubsidyActivity.getOperSubjectGuid()));
            creationSubsidyDetailRecord(hsaSubsidyActivityDetailRecords, hsaSubsidyActivityRecord, hsaSubsidyActivity, hsaOperationMemberInfo, null, recordDTO);
            setSubsidyActivityDetail(hsaSubsidyActivityRecords, hsaSubsidyActivity, hsaSubsidyActivityRecord);
            return true;
        }
        //【会员卡】已禁用
        if (CollUtil.isNotEmpty(hsaCardBaseInfoMap)
                && hsaCardBaseInfoMap.containsKey(hsaSubsidyActivityRecord.getCardGuid())
                && hsaCardBaseInfoMap.get(hsaSubsidyActivityRecord.getCardGuid()).getCardStatus() == CardStatusEnum.FORBIDDEN.getCode()) {
            recordDTO.setError(systemRoleHelper.getReplace("【会员卡】已禁用", hsaSubsidyActivity.getOperSubjectGuid()));
            creationSubsidyDetailRecord(hsaSubsidyActivityDetailRecords, hsaSubsidyActivityRecord, hsaSubsidyActivity, hsaOperationMemberInfo, null, recordDTO);
            setSubsidyActivityDetail(hsaSubsidyActivityRecords, hsaSubsidyActivity, hsaSubsidyActivityRecord);
            return true;

        }
        //【会员卡】已过期
        if (CollUtil.isNotEmpty(hsaMemberInfoCardMap) && hsaMemberInfoCardMap.containsKey(hsaOperationMemberInfo.getGuid())) {
            HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMap.get(hsaOperationMemberInfo.getGuid());
            if (hsaMemberInfoCard.getCardValidity() == 1 && LocalDate.now().isAfter(hsaMemberInfoCard.getCardValidityDate())) {
                recordDTO.setError(systemRoleHelper.getReplace("【会员卡】已过期", hsaSubsidyActivity.getOperSubjectGuid()));
                creationSubsidyDetailRecord(hsaSubsidyActivityDetailRecords, hsaSubsidyActivityRecord, hsaSubsidyActivity, hsaOperationMemberInfo, null, recordDTO);
                setSubsidyActivityDetail(hsaSubsidyActivityRecords, hsaSubsidyActivity, hsaSubsidyActivityRecord);
                return true;
            }
        }
        return false;
    }

    private void setSubsidyActivityDetail(List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords, HsaSubsidyActivity hsaSubsidyActivity, HsaSubsidyActivityRecord hsaSubsidyActivityRecord) {
        //补贴活动
        if (hsaSubsidyActivity.getSubsidyFrequencyType() == 1) {
            hsaSubsidyActivity.setActivityState(ActivityStateEnum.ACTIVITY_STATE_CAN_SEND_STOP.getCode());
        }
        //补贴记录
        hsaSubsidyActivityRecords.add(hsaSubsidyActivityRecord);
    }


    private Map<String, HsaCardBaseInfo> getStringHsaCardBaseInfoMap(SubsidyActivityEvent subsidyActivityEvent) {
        return hsaCardBaseInfoMapper.selectList(new LambdaQueryWrapper<HsaCardBaseInfo>()
                        .in(HsaCardBaseInfo::getGuid, JSON.parseArray(subsidyActivityEvent.getCardGuidJson(), String.class)))
                .stream()
                .collect(Collectors.toMap(HsaCardBaseInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));
    }

    private Map<String, HsaMemberInfoCard> getStringHsaMemberInfoCardMap
            (Set<String> memberGuidList, HsaSubsidyActivityRecord hsaSubsidyActivityRecord) {
        return hsaMemberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                        .in(HsaMemberInfoCard::getMemberInfoGuid, memberGuidList)
                        .eq(HsaMemberInfoCard::getCardGuid, hsaSubsidyActivityRecord.getCardGuid()))
                .stream()
                .collect(Collectors.toMap(HsaMemberInfoCard::getMemberInfoGuid, Function.identity(), (entity1, entity2) -> entity1));
    }

    private Map<String, HsaSubsidyActivity> getStringHsaSubsidyActivityMap(SendSubsidyRightsEvent event) {
        return hsaSubsidyActivityMapper.selectList(new LambdaQueryWrapper<HsaSubsidyActivity>()
                        .in(HsaSubsidyActivity::getGuid, event.getSubsidyActivityEventList().stream().map(SubsidyActivityEvent::getGuid).collect(Collectors.toList())))
                .stream()
                .collect(Collectors.toMap(HsaSubsidyActivity::getGuid, Function.identity(), (entity1, entity2) -> entity1));
    }


    /**
     * 创建补贴明细记录
     *
     * @param hsaSubsidyActivityDetailRecords hsaSubsidyActivityDetailRecords
     * @param hsaSubsidyActivityRecord        hsaSubsidyActivityRecord
     * @param hsaSubsidyActivity              hsaSubsidyActivity
     * @param hsaOperationMemberInfo          hsaOperationMemberInfo
     */
    private void creationSubsidyDetailRecord(List<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords,
                                             HsaSubsidyActivityRecord hsaSubsidyActivityRecord,
                                             HsaSubsidyActivity hsaSubsidyActivity,
                                             HsaOperationMemberInfo hsaOperationMemberInfo,
                                             HsaMemberFundingDetail hsaMemberFundingDetail,
                                             CreationSubsidyDetailRecordDTO recordDTO) {
        Integer result = recordDTO.getResult();
        LocalDateTime now = recordDTO.getNow();
        HsaSubsidyActivityDetailRecord subsidyActivityDetailRecord = subsidyActivityAssembler
                .getHsaSubsidyActivityDetailRecord(hsaSubsidyActivityRecord, hsaSubsidyActivity, hsaOperationMemberInfo, recordDTO, result, now);
        if (result == BooleanEnum.FALSE.getCode()) {
            hsaSubsidyActivityRecord.setSubsidyErrorNum(hsaSubsidyActivityRecord.getSubsidyErrorNum() + 1);
        } else {
            subsidyActivityDetailRecord.setSubsidyArriveTime(now);
        }
        if (Objects.nonNull(hsaMemberFundingDetail)) {
            hsaMemberFundingDetail.setSubsidyDetailRecordGuid(subsidyActivityDetailRecord.getGuid());
        }
        hsaSubsidyActivityRecord.setSubsidyNum(hsaSubsidyActivityRecord.getSubsidyNum() + 1);
        hsaSubsidyActivityDetailRecords.add(subsidyActivityDetailRecord);
    }

    private Map<String, HsaSubsidyActivityRecord> getStringHsaSubsidyActivityRecordMap(SubsidyActivityEvent
                                                                                               subsidyActivityEvent) {
        return hsaSubsidyActivityRecordMapper.selectList(new LambdaQueryWrapper<HsaSubsidyActivityRecord>()
                        .in(HsaSubsidyActivityRecord::getGuid, subsidyActivityEvent.getRecordGuid()))
                .stream()
                .collect(Collectors.toMap(HsaSubsidyActivityRecord::getGuid, Function.identity(), (entity1, entity2) -> entity1));
    }
}
