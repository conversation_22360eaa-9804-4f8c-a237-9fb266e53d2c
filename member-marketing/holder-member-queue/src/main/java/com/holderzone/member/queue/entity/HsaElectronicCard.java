package com.holderzone.member.queue.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 电子卡实体
 * @date 2021/8/31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaElectronicCard implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 电子卡GUID
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 企业GUID
     */
    private String enterpriseGuid;

    /**
     * 会员GUID
     */
    private String memberInfoGuid;

    /**
     * 会员卡GUID
     */
    private String cardGuid;


    private Integer cardState;

    /**
     * 电子卡卡号
     */
    private String cardNum;

    /**
     * 来源,0后台添加,2一体机注册 53 小程序
     */
    private Integer source;

    /**
     * 电子卡开卡方式
     * 开卡方式(0 免费开通 1付费开通 2充值开通 3注册开通 4满足条件 5指定开通 6实体卡同步开通)
     */
    private Integer openWay;

    /**
     * 自主开通直接付款金额（电子卡相关）
     */
    private BigDecimal selfPaymentMoney;

    /**
     * 自主开通充值金额（电子卡相关）
     */
    private BigDecimal selfRechargeMoney;

    /**
     * 自主开通支付单号
     */
    private String paymentOrderNumber;

    /**
     * 满足条件，自动开通类型（0满足所有条件，1满足任意条件）（电子卡相关）
     */
    private Integer satisfyConditionType;

    /**
     * 累计消费金额（条件）
     */
    private BigDecimal consumeSumMoney;

    /**
     * 累计消费次数（条件）
     */
    private Integer consumeNum;

    /**
     * 是否有消费商品(外关联表)（条件）
     */
    private Integer isConsumeProduct;

    /**
     * 开卡操作人
     */
    private String operationName;

    /**
     * 开卡操作人手机号
     */
    private String operationPhone;

    /**
     * 开卡方式 0自主开通;1注册完成，自动发放；2指定用户直接开卡；3满足条件，自动发放
     */
    private Integer openCardWay;

    /**
     * 是否删除
     */
    private Integer isDelete;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

}
