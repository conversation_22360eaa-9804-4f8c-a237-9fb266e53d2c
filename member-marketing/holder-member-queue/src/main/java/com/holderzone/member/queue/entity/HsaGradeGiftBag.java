package com.holderzone.member.queue.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @program: member-marketing
 * @description: 等级升级礼包
 * @author: pan tao
 * @create: 2021-12-31 09:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaGradeGiftBag implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 礼包guid
     */
    private String guid;

    @ApiModelProperty("运营主体GUID")
    private String operSubjectGuid;

    /**
     * 会员等级guid
     */
    private String memberGradeInfoGuid;

    /**
     * 礼包类型 0：成长值 1：积分
     */
    private int type;

    /**
     * 赠送多少值
     */
    private int value;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

    /**
     * 是否删除,0未删除,1已删除
     */
//    @TableLogic//逻辑删除标识
//    @TableField(select = false)//查询的时候不显示
    private Integer isDelete;

    /**
     * 是否有效 0:无效 1:有效
     */
    private Integer effective;

}
