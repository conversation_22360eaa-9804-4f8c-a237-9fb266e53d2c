package com.holderzone.member.queue.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 会员等级权益记录表
 * @date 2022/1/4 11:57
 */
@ApiModel("会员等级权益记录表")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberGradeRightsRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 会员等级权益记录GUID
     */
    @ApiModelProperty("会员等级权益记录GUID")
    private String guid;

    /**
     * 运营主体GUID
     */
    @ApiModelProperty("运营主体GUID")
    private String operSubjectGuid;

    /**
     * 会员GUID
     */
    @ApiModelProperty("会员GUID")
    private String memberInfoGuid;

    /**
     * 等级GUID
     */
    @ApiModelProperty("等级guid")
    private String memberGradeGuid;

    /**
     * 权益GUID
     */
    @ApiModelProperty("权益GUID")
    private String rightsGuid;

    /**
     * 权益类型 0 升级礼包 1 等级权益
     *
     * @see com.holderzone.member.common.enums.member.GradeRightsTypeEnum
     */
    @ApiModelProperty("权益类型")
    private Integer rightsType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

    /**
     * 权益id
     */
    private Long equitiesId;
}
