package com.holderzone.member.queue.event.processor;


import com.google.common.base.Verify;
import com.holderzone.member.common.dto.event.SendBackSubsidyRightsEvent;
import com.holderzone.member.common.util.transaction.SpringContextUtils;
import com.holderzone.member.queue.service.activity.HsaSubsidyActivityService;
import lombok.extern.slf4j.Slf4j;

/**
 * 补贴活动回收事件处理
 *
 * <AUTHOR>
 * @version 1.0
 * @className SubsidySendRightsProcessor
 * @date 2021/10/27 14:33
 * @description 补贴活动回收事件处理
 * @program holder-member-queue
 */
@Slf4j
public class SubsidySendBackRightsProcessor extends AbstractProcessor<SendBackSubsidyRightsEvent> {

    private HsaSubsidyActivityService hsaSubsidyActivityService;

    public SubsidySendBackRightsProcessor(SendBackSubsidyRightsEvent event) {
        super(event);
        this.hsaSubsidyActivityService = SpringContextUtils.getBean(HsaSubsidyActivityService.class);
    }

    @Override
    protected String getLogTitle() {
        return "subsidySendRights";
    }

    @Override
    protected void validate() {
        Verify.verify(event != null);
    }


    @Override
    protected void process() {
        hsaSubsidyActivityService.sendSubsidyBackRights(event, 3);
    }
}
