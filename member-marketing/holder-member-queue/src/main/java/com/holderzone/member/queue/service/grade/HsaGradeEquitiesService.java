package com.holderzone.member.queue.service.grade;

import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.queue.dto.grade.DoubleValueRequest;
import com.holderzone.member.queue.entity.HsaBusinessEquities;


/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-01-18 18:21
 */
public interface HsaGradeEquitiesService extends IHolderBaseService<HsaBusinessEquities> {

    /**
     * 计算当前会员是否拥有翻倍成长值权益
     * @param request 请求参数
     * @return 翻倍成长值
     */
    Long isDoubleGrowthValue(DoubleValueRequest request);


}
