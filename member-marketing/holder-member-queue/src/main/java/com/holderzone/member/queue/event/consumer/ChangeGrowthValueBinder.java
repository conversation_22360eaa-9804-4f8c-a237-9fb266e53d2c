package com.holderzone.member.queue.event.consumer;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.member.common.constant.BinderConstant;
import com.holderzone.member.common.dto.event.GrowthValueChangeEvent;
import com.holderzone.member.queue.event.BinderChannel;
import com.holderzone.member.queue.event.processor.GrowthValueChangeProcessor;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.messaging.handler.annotation.Header;

import java.io.IOException;

/**
 * 成长值变更
 *
 * <AUTHOR>
 * @description 处理成长值变动记录
 * @date 2021/11/23 11:32
 */
@Slf4j
@EnableBinding(BinderChannel.class)
public class ChangeGrowthValueBinder {

    /**
     * 成长值变更
     */
    @StreamListener(BinderConstant.INPUT_CHANGE_GROWTH_VALUE)
    public void bind(Message<GrowthValueChangeEvent> message,
                     @Header(AmqpHeaders.CHANNEL) Channel channel,
                     @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) throws IOException {
        GrowthValueChangeEvent requestEvent = message.getPayload();
        if (StringUtils.isEmpty(requestEvent.getSessionId())) {
            requestEvent.setSessionId(String.valueOf(System.currentTimeMillis()));
        }
        log.info("[{}] [ChangeGrowthValueBinder] 参数={}", requestEvent.getSessionId(), JacksonUtils.writeValueAsString(requestEvent));
        try {
            new GrowthValueChangeProcessor(requestEvent).execute();
        } finally {
            //手动确认
            channel.basicAck(deliveryTag, false);
        }

    }

}