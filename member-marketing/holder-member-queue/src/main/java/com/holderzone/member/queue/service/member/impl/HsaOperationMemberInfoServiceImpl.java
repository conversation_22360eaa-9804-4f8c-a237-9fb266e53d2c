package com.holderzone.member.queue.service.member.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.grade.MemberGradeRelationDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.grade.UpgradeTypeEnum;
import com.holderzone.member.common.enums.growth.SumValueChangeEnum;
import com.holderzone.member.common.enums.member.GradeChangeTypeEnum;
import com.holderzone.member.common.enums.member.GradeTypeEnum;
import com.holderzone.member.common.enums.member.RoleTypeEnum;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.queue.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.queue.dto.grade.MemberGradeChangeDTO;
import com.holderzone.member.queue.dto.member.MemberGrowthValueEvent;
import com.holderzone.member.queue.entity.HsaGrowthValueDetail;
import com.holderzone.member.queue.entity.HsaMemberGradeInfo;
import com.holderzone.member.queue.entity.HsaOperationMemberInfo;
import com.holderzone.member.queue.mapper.HsaGrowthValueDetailMapper;
import com.holderzone.member.queue.mapper.HsaGrowthValueTaskMapper;
import com.holderzone.member.queue.mapper.HsaMemberGradeInfoMapper;
import com.holderzone.member.queue.mapper.HsaOperationMemberInfoMapper;
import com.holderzone.member.queue.service.grade.HsaMemberGradeChangeDetailService;
import com.holderzone.member.queue.service.grade.IHsaMemberGradeRelationService;
import com.holderzone.member.queue.service.member.HsaGrowthValueDetailService;
import com.holderzone.member.queue.service.member.HsaOperationMemberInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.member.common.constant.NumberConstant.NUMBER_0;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 会员信息相关实现
 * @date 2021/11/23 11:28
 */
@Service
@Slf4j
public class HsaOperationMemberInfoServiceImpl extends HolderBaseServiceImpl<HsaOperationMemberInfoMapper,
        HsaOperationMemberInfo> implements HsaOperationMemberInfoService {

    private static final String STRING_MINUS_1 = "-1";

    private static final int NUMBER_1 = 1;
    private static final int NUMBER_2 = 2;

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private HsaGrowthValueTaskMapper hsaGrowthValueTaskMapper;

    @Autowired
    @Lazy
    private HsaGrowthValueDetailService hsaGrowthValueDetailService;

    @Resource
    private HsaGrowthValueDetailMapper hsaGrowthValueDetailMapper;

    @Resource
    private HsaMemberGradeChangeDetailService hsaMemberGradeChangeDetailService;

    @Resource
    private HsaMemberGradeInfoMapper hsaMemberGradeInfoMapper;

    @Resource
    private Executor memberQueueThreadExecutor;

    @Resource
    private IHsaMemberGradeRelationService memberGradeRelationService;

    /**
     * 会员成长值调整
     *
     * @param event event
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void systemUpdateMemberGrowth(MemberGrowthValueEvent event) {
        log.info("系统批量调整成长值>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", event);
        if (CollUtil.isEmpty(event.getMemberInfoGuidList())) {
            return;
        }
        HeaderUserInfo headerUserInfo = event.getHeaderUserInfo();
        event.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid());
        Map<String, HsaOperationMemberInfo> hsaOperationMemberInfoMap = checkMemberGuid(event.getMemberInfoGuidList())
                .stream()
                .collect(Collectors.toMap(HsaOperationMemberInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));
        //成长值明细记录
        List<HsaGrowthValueDetail> hsaGrowthValueDetailList = Lists.newArrayList();
        //会员信息
        List<HsaOperationMemberInfo> hsaOperationMemberInfoList = Lists.newArrayList();
        //修改成长值信息
        List<HsaGrowthValueDetail> updateGrowthValueDetails = Lists.newArrayList();
        //获取会员等级
        Map<String, HsaMemberGradeInfo> hsaMemberGradeInfoMap = getStringHsaMemberGradeInfoMap(event);
        HsaMemberGradeInfo payGradeInfo = getHsaMemberGradeInfo(event, headerUserInfo);
        for (String memberGuid : event.getMemberInfoGuidList()) {
            HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMap.get(memberGuid);
            if (Objects.isNull(hsaOperationMemberInfo)) {
                continue;
            }
            HsaMemberGradeInfo hsaMemberGradeInfo = hsaMemberGradeInfoMap.get(hsaOperationMemberInfo.getMemberGradeInfoGuid());
            HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
            if (SystemEnum.PARTNER.getCode() == headerUserInfo.getSystem()) {
                fillGrowthDetailType(event, payGradeInfo, hsaOperationMemberInfo, hsaGrowthValueDetail);
                hsaMemberGradeInfo = payGradeInfo;
            }
            fillGowthDetailBase(event, headerUserInfo, hsaOperationMemberInfo, hsaMemberGradeInfo, hsaGrowthValueDetail);
            //增加
            if (event.getGrowthValueType() == NUMBER_0) {
                hsaGrowthValueDetail.setGrowthValidity(NUMBER_0);
                hsaGrowthValueDetail.setGrowthValueType(GradeChangeTypeEnum.UPGRADE.getCode());
                hsaOperationMemberInfo.setMemberGrowthValue(hsaOperationMemberInfo.getMemberGrowthValue() + event.getGrowthValue());
                hsaGrowthValueDetail.setRemainGrowthValue(hsaOperationMemberInfo.getMemberGrowthValue());
                hsaGrowthValueDetail.setRecordRemainGrowthValue(event.getGrowthValue());
                hsaGrowthValueDetail.setOrderNumber(event.getOrderNumber());
            } else {
                if (SystemEnum.PARTNER.getCode() != headerUserInfo.getSystem() && hsaOperationMemberInfo.getMemberGrowthValue() == NUMBER_0) {
                    continue;
                }
                handlerGrowthValue(event, updateGrowthValueDetails, memberGuid, hsaOperationMemberInfo, hsaGrowthValueDetail);
            }
            fillGrowthValueDetailLevel(event, headerUserInfo, hsaOperationMemberInfo, hsaMemberGradeInfo, hsaGrowthValueDetail);
            if (StringUtils.isEmpty(hsaGrowthValueDetail.getOperSubjectGuid())) {
                hsaGrowthValueDetail.setOperSubjectGuid(hsaOperationMemberInfo.getOperSubjectGuid());
            }
            hsaGrowthValueDetailList.add(hsaGrowthValueDetail);
            hsaOperationMemberInfoList.add(hsaOperationMemberInfo);
        }
        updateDetailGrade(event, hsaGrowthValueDetailList, hsaOperationMemberInfoList, updateGrowthValueDetails);
    }

    private void fillGowthDetailBase(MemberGrowthValueEvent event, HeaderUserInfo headerUserInfo,
                                     HsaOperationMemberInfo hsaOperationMemberInfo,
                                     HsaMemberGradeInfo hsaMemberGradeInfo,
                                     HsaGrowthValueDetail hsaGrowthValueDetail) {
        hsaGrowthValueDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaGrowthValueDetail.class.getSimpleName()));
        hsaGrowthValueDetail.setGrowthValueSourceType(event.getSource());
        if (event.getChangeType() == SumValueChangeEnum.REDEEM_CODE.getCode()
                || event.getChangeType() == SumValueChangeEnum.RECHARGE_GIFT.getCode()
                || event.getChangeType() == SumValueChangeEnum.RECHARGE_REFUND.getCode()
                || event.getChangeType() == SumValueChangeEnum.CERTIFIED.getCode()) {
            hsaGrowthValueDetail.setOperatorAccountName(event.getOperatorAccountName());
        } else {
            hsaGrowthValueDetail.setOperatorAccountName(headerUserInfo.getUserName() + StringConstant.STR_BIAS + headerUserInfo.getTel());
        }

        hsaGrowthValueDetail.setStoreGuid(event.getStoreGuid());
        hsaGrowthValueDetail.setStoreName(event.getStoreName());
        hsaGrowthValueDetail.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid());
        hsaGrowthValueDetail.setChangeType(event.getChangeType());
        hsaGrowthValueDetail.setRecordDeclaration(event.getRemark());
        hsaGrowthValueDetail.setGrowthValue(event.getGrowthValue());
        hsaGrowthValueDetail.setMemberInfoGuid(hsaOperationMemberInfo.getGuid());
        hsaGrowthValueDetail.setRoleType(StringUtils.isEmpty(event.getRoleType()) ? RoleTypeEnum.MEMBER.name() : event.getRoleType());
        hsaGrowthValueDetail.setNum(event.getNum());
        hsaGrowthValueDetail.setUnit(event.getUnit());
        if (!Objects.equals(STRING_MINUS_1, event.getMemberInfoGradeGuid())) {
            hsaGrowthValueDetail.setExpireTime(DateUtil.getFirstExpireTime(event.getNum(), event.getUnit()));
        }
        if (!ObjectUtils.isEmpty(hsaMemberGradeInfo)) {
            hsaGrowthValueDetail.setMemberInfoGradeGuid(hsaMemberGradeInfo.getGuid());
            hsaGrowthValueDetail.setUpgradeType(hsaMemberGradeInfo.getUpgradeType());
        }
    }

    private void fillGrowthDetailType(MemberGrowthValueEvent event, HsaMemberGradeInfo payGradeInfo,
                                      HsaOperationMemberInfo hsaOperationMemberInfo,
                                      HsaGrowthValueDetail hsaGrowthValueDetail) {
        MemberGradeRelationDTO relationDTO = new MemberGradeRelationDTO();
        relationDTO.setMemberGuidList(Lists.newArrayList(hsaOperationMemberInfo.getGuid()));
        relationDTO.setRoleType(event.getRoleType());
        HsaMemberGradeInfo currentGradeInfo = memberGradeRelationService.queryMemberCurrentGrade(relationDTO);
        if (!ObjectUtils.isEmpty(currentGradeInfo) && !ObjectUtils.isEmpty(payGradeInfo) &&
                currentGradeInfo.getTemporaryVipGrade() > payGradeInfo.getTemporaryVipGrade()) {
            hsaGrowthValueDetail.setGrowthValueType(GradeChangeTypeEnum.DEMOTION.getCode());
            event.setGrowthValueType(GradeChangeTypeEnum.DEMOTION.getCode());
        }
    }

    private void updateDetailGrade(MemberGrowthValueEvent event, List<HsaGrowthValueDetail> hsaGrowthValueDetailList,
                                   List<HsaOperationMemberInfo> hsaOperationMemberInfoList,
                                   List<HsaGrowthValueDetail> updateGrowthValueDetails) {
        if (!CollUtil.isEmpty(hsaGrowthValueDetailList)) {
            log.info("hsaGrowthValueDetailList>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", hsaGrowthValueDetailList);
            hsaGrowthValueDetailService.saveBatch(hsaGrowthValueDetailList);
            log.info("hsaOperationMemberInfoList>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", hsaOperationMemberInfoList);
            hsaOperationMemberInfoMapper.batchUpdateGrowth(hsaOperationMemberInfoList);
        }
        if (!CollUtil.isEmpty(updateGrowthValueDetails)) {
            hsaGrowthValueDetailMapper.batchUpdate(updateGrowthValueDetails);
        }
        //等级权益变化
        memberQueueThreadExecutor.execute(() -> sendMemberGradeChange(event, hsaOperationMemberInfoList));
    }

    private void fillGrowthValueDetailLevel(MemberGrowthValueEvent event, HeaderUserInfo headerUserInfo,
                                            HsaOperationMemberInfo hsaOperationMemberInfo, HsaMemberGradeInfo hsaMemberGradeInfo,
                                            HsaGrowthValueDetail hsaGrowthValueDetail) {
        if (!ObjectUtils.isEmpty(hsaMemberGradeInfo)) {
            hsaGrowthValueDetail.setCurrentMemberLevel(hsaMemberGradeInfo.getName() +
                    StringConstant.LEFT_BRACKET +
                    StringConstant.VIP +
                    hsaMemberGradeInfo.getVipGrade() +
                    StringConstant.RIGHT_BRACKET);
        }
        if (SystemEnum.PARTNER.getCode() != headerUserInfo.getSystem()) {
            HsaMemberGradeInfo memberGradeInfo = hsaMemberGradeChangeDetailService.calculateExtraAmount(
                    hsaOperationMemberInfo.getMemberGrowthValue(), hsaOperationMemberInfo.getOperSubjectGuid());
            if (!memberGradeInfo.getGuid().equals(hsaOperationMemberInfo.getMemberGradeInfoGuid())) {
                hsaGrowthValueDetail.setCurrentMemberLevel(memberGradeInfo.getName() +
                        StringConstant.LEFT_BRACKET +
                        StringConstant.VIP +
                        memberGradeInfo.getVipGrade() +
                        StringConstant.RIGHT_BRACKET);
            }
        }
        if (Objects.equals(STRING_MINUS_1, event.getMemberInfoGradeGuid())) {
            hsaGrowthValueDetail.setMemberInfoGradeGuid("");
            hsaGrowthValueDetail.setCurrentMemberLevel("");
            hsaGrowthValueDetail.setGrowthValueType(NUMBER_1);
            hsaGrowthValueDetail.setUpgradeType(NUMBER_2);
        }
    }

    private HsaMemberGradeInfo getHsaMemberGradeInfo(MemberGrowthValueEvent event, HeaderUserInfo headerUserInfo) {
        HsaMemberGradeInfo payGradeInfo = new HsaMemberGradeInfo();
        if (SystemEnum.PARTNER.getCode() == headerUserInfo.getSystem()) {
            payGradeInfo = hsaMemberGradeInfoMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                    .eq(HsaMemberGradeInfo::getGuid, event.getMemberInfoGradeGuid())
                    .eq(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode()));
        }
        return payGradeInfo;
    }

    private void handlerGrowthValue(MemberGrowthValueEvent event, List<HsaGrowthValueDetail> updateGrowthValueDetails,
                                    String memberGuid, HsaOperationMemberInfo hsaOperationMemberInfo,
                                    HsaGrowthValueDetail hsaGrowthValueDetail) {
        hsaGrowthValueDetail.setGrowthValueType(GradeChangeTypeEnum.DEMOTION.getCode());
        hsaGrowthValueDetail.setOrderNumber(event.getOrderNumber());
        //获取成长值明细记录
        Map<Integer, List<HsaGrowthValueDetail>> hsaGrowthValueDetailsMap = getIntegerListMap(memberGuid);
        //扣减成长值
        Integer growthValue = event.getGrowthValue();
        if (CollUtil.isNotEmpty(hsaGrowthValueDetailsMap)) {
            //固定失效
            List<HsaGrowthValueDetail> fixationGrowthValueDetails = hsaGrowthValueDetailsMap.get(NUMBER_1);
            //永久有效
            List<HsaGrowthValueDetail> perpetualGrowthValueDetails = hsaGrowthValueDetailsMap.get(NUMBER_0);
            if (CollUtil.isEmpty(fixationGrowthValueDetails)) {
                fixationGrowthValueDetails = Lists.newArrayList();
            }
            if (CollUtil.isEmpty(perpetualGrowthValueDetails)) {
                perpetualGrowthValueDetails = Lists.newArrayList();
            }
            fixationGrowthValueDetails.addAll(perpetualGrowthValueDetails);
            Integer count = calculateDeductionGrowth(updateGrowthValueDetails, growthValue, fixationGrowthValueDetails);
            if (hsaOperationMemberInfo.getMemberGrowthValue() >= count) {
                hsaGrowthValueDetail.setGrowthValue(count);
            } else {
                hsaGrowthValueDetail.setGrowthValue(hsaOperationMemberInfo.getMemberGrowthValue());
            }
            hsaOperationMemberInfo.setMemberGrowthValue(Math.max(hsaOperationMemberInfo.getMemberGrowthValue() - count, NUMBER_0));
            hsaGrowthValueDetail.setRemainGrowthValue(hsaOperationMemberInfo.getMemberGrowthValue());
        } else {
            hsaOperationMemberInfo.setMemberGrowthValue(Math.max(hsaOperationMemberInfo.getMemberGrowthValue() - growthValue, NUMBER_0));
            hsaGrowthValueDetail.setRemainGrowthValue(hsaOperationMemberInfo.getMemberGrowthValue());
            hsaGrowthValueDetail.setRecordRemainGrowthValue(NUMBER_0);
        }
    }

    private void sendMemberGradeChange(MemberGrowthValueEvent event, List<HsaOperationMemberInfo> hsaOperationMemberInfos) {
        if (!CollUtil.isEmpty(hsaOperationMemberInfos)) {
            MemberGradeChangeDTO changeDTO = MemberGradeChangeDTO.getMemberGradeChangeDTO(event);
            changeDTO.setHsaOperationMemberInfos(hsaOperationMemberInfos);
            hsaMemberGradeChangeDetailService.memberGradeChange(changeDTO);
        }
    }

    private Map<String, HsaMemberGradeInfo> getStringHsaMemberGradeInfoMap(MemberGrowthValueEvent event) {
        List<String> memberGradeInfoGuidLists = checkMemberGuid(event.getMemberInfoGuidList())
                .stream().map(HsaOperationMemberInfo::getMemberGradeInfoGuid).collect(Collectors.toList());
        return hsaMemberGradeInfoMapper.selectList(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                        .in(HsaMemberGradeInfo::getGuid, memberGradeInfoGuidLists)
                        .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                        .eq(HsaMemberGradeInfo::getType, GradeTypeEnum.FREE.getCode())
                        .eq(HsaMemberGradeInfo::getEffective, NumberConstant.NUMBER_1))
                .stream()
                .collect(Collectors.toMap(HsaMemberGradeInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));
    }

    private Map<Integer, List<HsaGrowthValueDetail>> getIntegerListMap(String memberGuid) {
        return hsaGrowthValueDetailMapper.selectList(new LambdaQueryWrapper<HsaGrowthValueDetail>()
                        .eq(HsaGrowthValueDetail::getMemberInfoGuid, memberGuid)
                        .gt(HsaGrowthValueDetail::getRecordRemainGrowthValue, NUMBER_0)
                        .orderByAsc(HsaGrowthValueDetail::getGrowthValidityDate))
                .stream()
                .collect(Collectors.groupingBy(HsaGrowthValueDetail::getGrowthValidity));
    }

    /**
     * 顺序循环扣减
     *
     * @param updateGrowthValueDetails    updateGrowthValueDetails
     * @param growthValue                 growthValue
     * @param perpetualGrowthValueDetails perpetualGrowthValueDetails
     * @return
     */
    private Integer calculateDeductionGrowth(List<HsaGrowthValueDetail> updateGrowthValueDetails, Integer growthValue, List<HsaGrowthValueDetail> perpetualGrowthValueDetails) {
        int count = 0;
        Integer sum = perpetualGrowthValueDetails.stream().mapToInt(HsaGrowthValueDetail::getRecordRemainGrowthValue).sum();
        if (growthValue >= sum) {
            perpetualGrowthValueDetails.forEach(in -> in.setRecordRemainGrowthValue(NUMBER_0));
            updateGrowthValueDetails.addAll(perpetualGrowthValueDetails);
            return growthValue;
        }
        for (HsaGrowthValueDetail fixationGrowthValueDetail : perpetualGrowthValueDetails) {
            if (fixationGrowthValueDetail.getRecordRemainGrowthValue() - growthValue >= 0) {
                count = count + growthValue;
                fixationGrowthValueDetail.setRecordRemainGrowthValue(fixationGrowthValueDetail.getRecordRemainGrowthValue() - growthValue);
                updateGrowthValueDetails.add(fixationGrowthValueDetail);
                break;
            } else {
                growthValue = growthValue - fixationGrowthValueDetail.getRecordRemainGrowthValue();
                count = count + fixationGrowthValueDetail.getRecordRemainGrowthValue();
                fixationGrowthValueDetail.setRecordRemainGrowthValue(0);
                updateGrowthValueDetails.add(fixationGrowthValueDetail);
            }
        }
        return count;
    }

    /**
     * 分批查询会员信息
     */
    @Override
    public List<HsaOperationMemberInfo> checkMemberGuid(List<String> memberGuid) {
        List<HsaOperationMemberInfo> hsaOperationMemberInfos = Lists.newArrayList();
        int pointsDataLimit = 500;//限制条数
        int size = memberGuid.size();
        //判断是否有必要分批
        if (pointsDataLimit < size) {
            int part = size / pointsDataLimit;//分批数
            log.info("共有 ： " + size + "条，！" + " 分为 ：" + part + "批");
            for (int i = 0; i < part; i++) {
                //500条
                List<String> guid = memberGuid.subList(0, pointsDataLimit);

                hsaOperationMemberInfos.addAll(hsaOperationMemberInfoMapper.selectList(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                        .in(HsaOperationMemberInfo::getGuid, guid)));
                //剔除
                memberGuid.subList(0, pointsDataLimit).clear();
            }
            if (!memberGuid.isEmpty()) {
                //获取数据
                hsaOperationMemberInfos.addAll(hsaOperationMemberInfoMapper.selectList(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                        .in(HsaOperationMemberInfo::getGuid, memberGuid)));
            }
        } else {
            //获取数据
            hsaOperationMemberInfos = hsaOperationMemberInfoMapper.selectList(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                    .in(HsaOperationMemberInfo::getGuid, memberGuid));
            log.info("直接获取结果hsaOperationMemberInfos>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", JacksonUtils.writeValueAsString(hsaOperationMemberInfos));
        }
        return hsaOperationMemberInfos;
    }
}
