package com.holderzone.member.queue.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Version 1.0
 * @description 异步工具
 */
@Configuration
public class ExecuteQueueThreadPool {

    @Bean(name = "memberQueueThreadExecutor")
    public Executor executorQueue() {
        return new ThreadPoolExecutor(5, 20,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(20),
                new ThreadFactoryBuilder().setNameFormat("memberBaseThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }
}
