package com.holderzone.member.queue.service.activity;

import com.holderzone.member.common.dto.event.SendBackSubsidyRightsEvent;
import com.holderzone.member.common.dto.event.SendSubsidyRightsEvent;
import com.holderzone.member.queue.config.mybatis.IHolderBaseService;
import com.holderzone.member.queue.entity.HsaSubsidyActivity;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 会员食堂卡补贴活动
 * @date 2021/10/26 9:42
 */
public interface HsaSubsidyActivityService extends IHolderBaseService<HsaSubsidyActivity> {

    /**
     * 补贴发放
     * @param event 处理对象
     * @param retryCount 锁超时  重试次数
     */
    void sendSubsidyRights(SendSubsidyRightsEvent event, Integer retryCount);

    /**
     * 补贴回收
     * @param event 处理对象
     * @param retryCount 锁超时  重试次数
     */
    void sendSubsidyBackRights(SendBackSubsidyRightsEvent event, Integer retryCount);
}
