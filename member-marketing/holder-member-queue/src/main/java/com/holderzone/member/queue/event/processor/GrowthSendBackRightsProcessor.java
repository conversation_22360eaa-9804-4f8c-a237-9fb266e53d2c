package com.holderzone.member.queue.event.processor;


import com.google.common.base.Verify;
import com.holderzone.member.common.dto.event.SendGrowthBackEvent;
import com.holderzone.member.common.util.transaction.SpringContextUtils;
import com.holderzone.member.queue.service.member.HsaGrowthValueDetailService;
import lombok.extern.slf4j.Slf4j;

/**
 * 成长值回收事件处理
 *
 * <AUTHOR>
 * @version 1.0
 * @className GrowthSendBackRightsProcessor
 * @date 2021/11/27 14:33
 * @description 成长值回收事件处理
 * @program holder-member-queue
 */
@Slf4j
public class GrowthSendBackRightsProcessor extends AbstractProcessor<SendGrowthBackEvent> {

    private final HsaGrowthValueDetailService hsaGrowthValueDetailService;

    public GrowthSendBackRightsProcessor(SendGrowthBackEvent event) {
        super(event);
        hsaGrowthValueDetailService = SpringContextUtils.getBean(HsaGrowthValueDetailService.class);
    }

    @Override
    protected String getLogTitle() {
        return "subsidySendRights";
    }

    @Override
    protected void validate() {
        Verify.verify(event != null);
    }


    @Override
    protected void process() {
        hsaGrowthValueDetailService.growthValueRecycle();
    }
}
