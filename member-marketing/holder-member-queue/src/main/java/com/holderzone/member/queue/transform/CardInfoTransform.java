//package com.holderzone.member.queue.transform;
//
//
//import com.holderzone.member.common.dto.label.LabelRechargeInfo;
//import com.holderzone.member.queue.entity.HsaMemberInfoCard;
//import org.mapstruct.Mapper;
//import org.mapstruct.factory.Mappers;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @description 对象转化类
// * @date 2021/8/9
// */
//@Mapper
//public interface CardInfoTransform {
//
//    CardInfoTransform INSTANCE = Mappers.getMapper( CardInfoTransform.class );
//
//    List<LabelRechargeInfo> memberCardTOLabel(List<HsaMemberInfoCard> hsaMemberInfoCardList);
//}
