package com.holderzone.member.queue.event.consumer;

import com.alibaba.fastjson.JSON;
import com.holderzone.member.common.constant.BinderConstant;
import com.holderzone.member.common.dto.event.SendMessageEvent;
import com.holderzone.member.queue.event.BinderChannel;
import com.holderzone.member.queue.event.processor.SendMessageProcessor;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.util.StringUtils;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description 接收消息
 * @date 2021/8/11
 */
@Slf4j
@EnableBinding(BinderChannel.class)
public class SendMessageBinder {
    /**
     * 监听消息
     */
    @StreamListener(BinderConstant.INPUT_SEND_MESSAGE)
    public void bind(Message<SendMessageEvent> message,
                     @Header(AmqpHeaders.CHANNEL) Channel channel,
                     @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) throws IOException {
        SendMessageEvent requestEvent = message.getPayload();
        if (StringUtils.isEmpty(requestEvent.getSessionId())) {
            requestEvent.setSessionId(String.valueOf(System.currentTimeMillis()));
        }
        log.info("[{}] [SendMessageBinder] 参数={}", requestEvent.getSessionId(), JSON.toJSONString(requestEvent));
        try {
            new SendMessageProcessor(requestEvent).execute();
        } finally {
            //手动确认
            channel.basicAck(deliveryTag, false);
        }
    }
}
