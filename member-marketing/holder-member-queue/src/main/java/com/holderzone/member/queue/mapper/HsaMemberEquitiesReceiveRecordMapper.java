package com.holderzone.member.queue.mapper;

import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.vo.growth.MemberGrowthValueRelationVO;
import com.holderzone.member.queue.entity.HsaMemberEquitiesReceiveRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface HsaMemberEquitiesReceiveRecordMapper extends HolderBaseMapper<HsaMemberEquitiesReceiveRecord> {

    List<MemberGrowthValueRelationVO> selectAllGrowthValue(@Param("gradeEquitiesGuid") String gradeEquitiesGuid, @Param("type") Integer type);
}
