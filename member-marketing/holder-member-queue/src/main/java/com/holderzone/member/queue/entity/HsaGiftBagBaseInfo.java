package com.holderzone.member.queue.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @program: member-marketing
 * @description: 等级升级礼包基础信息
 * @author: zhanglin
 * @create: 2022-1-4 09:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaGiftBagBaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * guid
     */
    private String guid;

    /**
     * 运营主体
     */
    private String operSubjectGuid;

    /**
     * 权益类型
     * @see com.holderzone.member.common.enums.grade.GiftBagEquitiesTypeEnum
     */
    private Integer equitiesType;

    /**
     * 权益名称
     */
    private String equitiesName;

    /**
     * 权益图标
     */
    private String equitiesIcon;

    /**
     * 权益说明
     */
    private String equitiesDescribe;

    /**
     * 权益规则
     * @see com.holderzone.member.common.enums.grade.GiftBagEquitiesRuleEnum
     */
    private Integer equitiesRule;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;
}
