package com.holderzone.member.queue.entity;


import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-03-11 10:49
 */
@Data
@Accessors(chain = true)
public class HsaStoreRuleInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * storeGuid
     */
    private String storeGuid;

    /**
     * storeName
     */
    private String storeName;

    /**
     * 门店编号
     */
    private String storeNumber;

    /**
     * 营业时间
     */
    private String time;

    /**
     * 营业地址
     */
    private String address;

    /**
     * 父guid
     */
    private String parentGuid;

    /**
     * 经纬度
     */
    private String addressPoint;

    /**
     * 类型 0:挂账 1:成长值
     */
    private Integer type;

    /**
     * 类型guid
     */
    private String typeGuid;

    private String storeLogo;

}
