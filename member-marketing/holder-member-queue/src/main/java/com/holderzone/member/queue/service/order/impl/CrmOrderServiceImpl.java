package com.holderzone.member.queue.service.order.impl;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.mall.OrderReceiverAddressDTO;
import com.holderzone.member.common.dto.order.CrmOrderDTO;
import com.holderzone.member.common.dto.order.CrmOrderStockDTO;
import com.holderzone.member.common.dto.order.MallBaseOrderDTO;
import com.holderzone.member.common.dto.order.MallBaseOrderDetailDTO;
import com.holderzone.member.common.enums.mall.order.OrderConditionEnum;
import com.holderzone.member.common.enums.mall.order.PayStateEnum;
import com.holderzone.member.common.enums.order.CrmOrderStatusEnum;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.CrmFeign;
import com.holderzone.member.queue.service.order.CrmOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;


/**
 * crm 订单
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CrmOrderServiceImpl implements CrmOrderService {

    // TODO 需要改造（已改）
    private final ExternalSupport externalSupport;

    /**
     * 推送订单到crm
     * ('1', '待付款'),
     * ('2', '已取消'),
     * ('3', '已付款'),
     * ('7', '已退款'),
     * ('8', '订单已完成')
     */
    @Override
    public void pushCrmOrder(MallBaseOrderDTO order, List<MallBaseOrderDetailDTO> details, OrderReceiverAddressDTO orderReceiverAd, boolean pushStock) {
        String status = transferToCrmOrderStatus(order);
        log.info("推送订单单号:{},状态为:{},是否推送库存:{}", order.getOrderNumber(), status, pushStock);
        if (Integer.parseInt(status) == Integer.parseInt(CrmOrderStatusEnum.UN_PAY.getCode())) {
            // 创建订单
            createCrmOrder(order, details, orderReceiverAd);
            return;
        }
        // 同步状态 | 同步库存
        if (pushStock) {
            // 同步库存
            pushCrmOrderStock(order, details);
        } else {
            // 同步状态
            pushCrmOrderStatus(order);
        }
    }

    public void createCrmOrder(MallBaseOrderDTO order, List<MallBaseOrderDetailDTO> details, OrderReceiverAddressDTO orderReceiverAd) {
        CrmOrderDTO crmOrderDTO = transferToCrmOrderDTO(order, details, orderReceiverAd);
        log.info("推送订单信息[创建][{}]:{}", crmOrderDTO.getName(), JacksonUtils.writeValueAsString(crmOrderDTO));
        JSONObject crmReturnVo = externalSupport.storeServer(ThreadLocalCache.getSystem()).pushOrder(crmOrderDTO);
        log.info("推送订单返回信息[创建][{}]:{}", crmOrderDTO.getName(), JacksonUtils.writeValueAsString(crmReturnVo));
    }


    public void pushCrmOrderStock(MallBaseOrderDTO order, List<MallBaseOrderDetailDTO> details) {
        CrmOrderStockDTO crmOrderStockDTO = transferToCrmOrderStockDTO(order, details);
        log.info("推送订单信息[库存][{}]:{}", crmOrderStockDTO.getOrder_name(), JacksonUtils.writeValueAsString(crmOrderStockDTO));
        JSONObject crmReturnVo = externalSupport.storeServer(ThreadLocalCache.getSystem()).pushOrderStock(crmOrderStockDTO);
        log.info("推送订单返回信息[库存][{}]:{}", crmOrderStockDTO.getOrder_name(), crmReturnVo);
    }


    public void pushCrmOrderStatus(MallBaseOrderDTO order) {
        CrmOrderStockDTO crmOrderStatusDTO = transferToCrmOrderStatusDTO(order);
        log.info("推送订单信息[状态][{}]:{}", crmOrderStatusDTO.getOrder_name(), JacksonUtils.writeValueAsString(crmOrderStatusDTO));
        JSONObject crmReturnVo = externalSupport.storeServer(ThreadLocalCache.getSystem()).pushOrderStock(crmOrderStatusDTO);
        log.info("推送订单返回信息[状态][{}]:{}", crmOrderStatusDTO.getOrder_name(), JacksonUtils.writeValueAsString(crmReturnVo));
    }

    /**
     * 转换crm订单请求DTO
     */
    private CrmOrderDTO transferToCrmOrderDTO(MallBaseOrderDTO order, List<MallBaseOrderDetailDTO> orderDetails, OrderReceiverAddressDTO orderReceiverAd) {
        CrmOrderDTO crmOrderDTO = new CrmOrderDTO();
        crmOrderDTO.setName(order.getOrderNumber());
        crmOrderDTO.setOrder_time(order.getGmtCreate());
        crmOrderDTO.setSetMealSource("会员商城");
        crmOrderDTO.setTotal_order_amount(order.getOrderPaymentAmount());
        crmOrderDTO.setDiscount_amount(order.getOrderDiscountAmount());
        crmOrderDTO.setExpress_fee(order.getFreightAmount());
        crmOrderDTO.setActual_payment_amount(order.getOrderPaidAmount());
        crmOrderDTO.setConnection(order.getMemberPhone());
        crmOrderDTO.setStatus(transferToCrmOrderStatus(order));
        crmOrderDTO.setCommodity_list(Lists.newArrayList());
        orderDetails.forEach(e -> {
            CrmOrderDTO.InnerCommodity innerCommodity = new CrmOrderDTO.InnerCommodity();
            innerCommodity.setChannel_commodity_info(e.getProductId());
            innerCommodity.setNumber(e.getProductNum());
            innerCommodity.setBase_price(new BigDecimal(e.getProductUnitPrice()));
            crmOrderDTO.getCommodity_list().add(innerCommodity);
        });

        crmOrderDTO.setMember_number(order.getMemberInfoGuid());
        if (StringUtils.isNotBlank(ThreadLocalCache.getHeaderUserInfo().getUserAccount())){
            crmOrderDTO.setMember_account(ThreadLocalCache.getHeaderUserInfo().getUserAccount());
        }

        String sb = orderReceiverAd.getProvince() +
                orderReceiverAd.getCity() +
                orderReceiverAd.getArea() +
                orderReceiverAd.getReceiverAddress();
        if (StringUtils.isNotBlank(orderReceiverAd.getHouseNumber())) {
            sb = sb + orderReceiverAd.getHouseNumber();
        }
        crmOrderDTO.setConsignee_address(sb);
        crmOrderDTO.setConsignee_name(orderReceiverAd.getReceiverName());
        crmOrderDTO.setConsignee_phone(orderReceiverAd.getReceiverPhone());
        crmOrderDTO.setMember_name(order.getMemberName());
        crmOrderDTO.setStore_id(order.getStoreGuid());
        return crmOrderDTO;
    }

    /**
     * 转换crm订单库存请求DTO
     */
    private CrmOrderStockDTO transferToCrmOrderStockDTO(MallBaseOrderDTO order, List<MallBaseOrderDetailDTO> orderDetails) {
        CrmOrderStockDTO crmOrderStockDTO = new CrmOrderStockDTO();
        crmOrderStockDTO.setOrder_name(order.getOrderNumber());
        crmOrderStockDTO.setState(transferToCrmOrderStatus(order));
        crmOrderStockDTO.setIs_update(false);
        crmOrderStockDTO.setIs_inventory(true);
        crmOrderStockDTO.setStore_id(order.getStoreGuid());
        crmOrderStockDTO.setTotal_order_amount(order.getOrderPaymentAmount());
        crmOrderStockDTO.setActual_payment_amount(order.getOrderPaidAmount());
        crmOrderStockDTO.setRefund_amount(BigDecimal.ZERO);
        crmOrderStockDTO.setAmount_paid(order.getOrderPaidAmount());
        crmOrderStockDTO.setCommodity_list(Lists.newArrayList());
        if (PayStateEnum.REFUNDED.getCode() == order.getPayCondition()) {
            crmOrderStockDTO.setIs_inventory(false);
            crmOrderStockDTO.setRefund_amount(order.getActualRefundAmount());
        }
        orderDetails.forEach(e -> {
            CrmOrderStockDTO.InnerCommodity innerCommodity = new CrmOrderStockDTO.InnerCommodity();
            innerCommodity.setChannel_commodity_info(e.getProductId());
            innerCommodity.setNumber(e.getProductNum());
            innerCommodity.setBase_price(new BigDecimal(e.getProductUnitPrice()));
            crmOrderStockDTO.getCommodity_list().add(innerCommodity);
        });
        crmOrderStockDTO.setSource("member");
        return crmOrderStockDTO;
    }

    /**
     * 转换crm订单更新状态请求DTO
     */
    private CrmOrderStockDTO transferToCrmOrderStatusDTO(MallBaseOrderDTO order) {
        CrmOrderStockDTO crmOrderStockDTO = new CrmOrderStockDTO();
        crmOrderStockDTO.setOrder_name(order.getOrderNumber());
        crmOrderStockDTO.setState(transferToCrmOrderStatus(order));
        crmOrderStockDTO.setIs_update(true);
        crmOrderStockDTO.setAmount_paid(order.getOrderPaidAmount());
        crmOrderStockDTO.setRefund_amount(order.getActualRefundAmount());
        crmOrderStockDTO.setSource("member");
        return crmOrderStockDTO;
    }


    private String transferToCrmOrderStatus(MallBaseOrderDTO order) {
        Integer mallOrderStatus = order.getOrderCondition();
        Integer mallOrderPayStatus = order.getPayCondition();
        String crmOrderStatus = "";
        OrderConditionEnum orderCondition = OrderConditionEnum.getByCode(mallOrderStatus);
        if (Objects.isNull(orderCondition)) {
            log.error("订单状态异常,order:{}", JacksonUtils.writeValueAsString(order));
            throw new BusinessException("订单状态异常");
        }
        PayStateEnum payState = PayStateEnum.getByCode(mallOrderPayStatus);
        if (Objects.isNull(payState)) {
            log.error("订单支付状态异常,order:{}", JacksonUtils.writeValueAsString(order));
            throw new BusinessException("订单支付状态异常");
        }
        // 支付状态
        switch (payState) {
            case SUCCESS:
                crmOrderStatus = CrmOrderStatusEnum.PAY.getCode();
                break;
            case REFUNDED:
                return CrmOrderStatusEnum.REFUNDED.getCode();
            default:

        }
        // 订单状态
        switch (orderCondition) {
            case TREAT_PAYMENT:
                crmOrderStatus = CrmOrderStatusEnum.UN_PAY.getCode();
                break;
            case CANCEL:
                crmOrderStatus = CrmOrderStatusEnum.CANCEL.getCode();
                break;
            case FINISH:
                crmOrderStatus = CrmOrderStatusEnum.FINISH.getCode();
                break;
            default:

        }
        return crmOrderStatus;
    }

}
