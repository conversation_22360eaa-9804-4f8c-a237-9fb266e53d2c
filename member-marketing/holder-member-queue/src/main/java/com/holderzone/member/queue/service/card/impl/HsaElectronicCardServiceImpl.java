package com.holderzone.member.queue.service.card.impl;

import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.queue.entity.HsaElectronicCard;
import com.holderzone.member.queue.mapper.HsaElectronicCardMapper;
import com.holderzone.member.queue.service.card.HsaElectronicCardService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 电子卡业务相关
 * @date 2021/10/14 15:44
 */
@Service
public class HsaElectronicCardServiceImpl extends HolderBaseServiceImpl<HsaElectronicCardMapper, HsaElectronicCard> implements HsaElectronicCardService {
}
