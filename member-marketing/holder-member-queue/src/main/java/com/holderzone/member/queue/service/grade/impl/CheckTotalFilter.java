package com.holderzone.member.queue.service.grade.impl;

import com.alibaba.fastjson.JSON;
import com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum;
import com.holderzone.member.queue.dto.grade.DoubleValueRequest;
import com.holderzone.member.queue.entity.HsaBusinessEquities;
import com.holderzone.member.queue.entity.HsaMemberEquitiesReceiveRecord;
import com.holderzone.member.queue.service.grade.AbstractHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 过滤当前会员累计翻倍成长值是否达到上限
 * <AUTHOR>
 */
@Component
@Order(2) //校验顺序排第2
@Slf4j
public class CheckTotalFilter extends AbstractHandler {

    @Override
    public boolean doFilter(DoubleValueRequest request, HsaBusinessEquities hsaBusinessEquities,
                            HsaMemberEquitiesReceiveRecord equitiesReceiveRecord) {
        log.info("进入CheckTotalFilter过滤 equitiesReceiveRecord:{}", JSON.toJSONString(equitiesReceiveRecord));
        Optional<HsaBusinessEquities> gradeEquities = Optional.of(hsaBusinessEquities);
        request.setRemainingValue(null);

        //累计翻倍限制 0:不限制 1:限制
        long totalDoubleCountLimited = gradeEquities
                .map(HsaBusinessEquities::getTotalDoubleCountLimited).orElse(EquitiesLimitedTypeEnum.UN_LIMITED.getCode());
        if (EquitiesLimitedTypeEnum.UN_LIMITED.getCode() == totalDoubleCountLimited) {
            return true;
        }
        Optional<HsaMemberEquitiesReceiveRecord> receiveRecord = Optional.ofNullable(equitiesReceiveRecord);
        //当前会员累计翻倍成长值记录
        Integer totalValue = receiveRecord.map(HsaMemberEquitiesReceiveRecord::getTotalDoubleUpperValue).orElse(0);
        //翻倍成长值累计上限
        Integer doubleValueLimit = gradeEquities.map(HsaBusinessEquities::getTotalDoubleCountUpperLimit).orElse(0);
        if (doubleValueLimit > totalValue) {
            //计算当前用户剩余可以翻倍的成长值
            request.setRemainingValue(doubleValueLimit - totalValue);
            log.info("用户：{} 剩余累计翻倍成长值：{}", request.getMemberInfoGuid(),request.getRemainingValue());
            return true;
        }
        //当前用户累计翻倍成长值已达上限
        return false;
    }
}
