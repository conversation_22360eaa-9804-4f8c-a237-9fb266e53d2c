package com.holderzone.member.queue.service.card.assembler;


import cn.hutool.core.collection.CollUtil;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.DateUnitEnum;
import com.holderzone.member.common.enums.card.CardValidityPeriodTypeEnum;
import com.holderzone.member.common.enums.card.ElectronicCardOpenTypeEnum;
import com.holderzone.member.common.enums.card.ElectronicCardOpenWayEnum;
import com.holderzone.member.common.enums.card.EletronicCardStateEnum;
import com.holderzone.member.common.enums.member.AmountSourceTypeEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.number.SerialNumberBuilder;
import com.holderzone.member.queue.entity.*;
import com.holderzone.member.queue.service.grade.HsaMemberGradeChangeDetailService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 *
 * 公共方法抽取
 */
@Service
public class MemberCardAssembler {

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private SerialNumberBuilder serialNumberBuilderUtil;

    /**
     * 资金明细，增加
     */
    private static final int ADD = 0;


    public void openCardProcessor(HsaElectronicCard electronicCard,
                                   HsaMemberInfoCard hsaMemberInfoCard,
                                   HsaCardOpenRule hsaCardOpenRule,
                                   HsaCardBaseInfo hsaCardBaseInfo,
                                   HsaOperationMemberInfo hsaOperationMemberInfo,
                                   List<HsaMemberInfoCard> hsaMemberInfoCardList,
                                   List<HsaElectronicCard> hsaElectronicCardList) {

        LocalDateTime now = LocalDateTime.now();
        electronicCard.setCardGuid(hsaCardOpenRule.getCardGuid());
        electronicCard.setCardNum(serialNumberBuilderUtil.getSequence(null));
        electronicCard.setEnterpriseGuid(hsaCardOpenRule.getEnterpriseGuid());
        electronicCard.setOperSubjectGuid(hsaCardOpenRule.getOperSubjectGuid());
        electronicCard.setGuid(guidGeneratorUtil.getStringGuid(HsaElectronicCard.class.getSimpleName()));
        electronicCard.setIsDelete(BooleanEnum.FALSE.getCode());
        electronicCard.setMemberInfoGuid(hsaOperationMemberInfo.getGuid());
        electronicCard.setSource(SourceTypeEnum.ADD_BACKGROUND.getCode());
        electronicCard.setCardState(EletronicCardStateEnum.NORMAL.getCode());
        electronicCard.setOpenCardWay(ElectronicCardOpenWayEnum.SPECIFY_USER_OPEN.getCode());
        electronicCard.setOpenWay(ElectronicCardOpenTypeEnum.DESIGNATED_OPEN.getCode());
        electronicCard.setOperationPhone("指定用户发卡");
        electronicCard.setOperationName("指定用户发卡");
        electronicCard.setGmtCreate(now);
        electronicCard.setGmtModified(now);
        //合并
        hsaMemberInfoCard.setApplicableAllStore(hsaCardBaseInfo.getApplicableAllStore());
        hsaMemberInfoCard.setCardName(hsaCardBaseInfo.getCardName());
        hsaMemberInfoCard.setCardGuid(hsaCardOpenRule.getCardGuid());
        hsaMemberInfoCard.setEnterpriseGuid(hsaCardOpenRule.getEnterpriseGuid());
        hsaMemberInfoCard.setOperSubjectGuid(hsaCardOpenRule.getOperSubjectGuid());
        hsaMemberInfoCard.setElectronicCardGuid(electronicCard.getGuid());
        hsaMemberInfoCard.setElectronicCardNum(electronicCard.getCardNum());
        hsaMemberInfoCard.setElectronicOpenTime(now);
        hsaMemberInfoCard.setCardValidity(hsaCardBaseInfo.getCardValidity() == 0 ? 0 : 1);
        hsaMemberInfoCard.setGmtModified(now);
        hsaMemberInfoCard.setElectronicCardState(EletronicCardStateEnum.NORMAL.getCode());
        hsaMemberInfoCard.setMemberInfoGuid(hsaOperationMemberInfo.getGuid());
        hsaMemberInfoCard.setGmtCreate(now);
        hsaMemberInfoCard.setElectronicCardNum(electronicCard.getCardNum());
        hsaMemberInfoCard.setMemberPhoneNum(hsaOperationMemberInfo.getPhoneNum());
        hsaMemberInfoCard.setElectronicCardState(electronicCard.getCardState());
        hsaMemberInfoCard.setElectronicOpenTime(now);
        //有效期
        hsaMemberInfoCard.setCardValidity(hsaCardBaseInfo.getCardValidity() == CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_ALWAYS.getCode() ? CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_ALWAYS.getCode() : 1);
        if (hsaCardBaseInfo.getCardValidity() == CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_DAY.getCode()) {
            if (hsaCardBaseInfo.getValidityUnit().equals(DateUnitEnum.DAY.getCode())) {
                hsaMemberInfoCard.setCardValidityDate(LocalDate.now().plusDays(hsaCardBaseInfo.getCardValidityTime()));
            } else if (hsaCardBaseInfo.getValidityUnit().equals(DateUnitEnum.WEEK.getCode())) {
                hsaMemberInfoCard.setCardValidityDate(LocalDate.now().plusWeeks(hsaCardBaseInfo.getCardValidityTime()));
            } else if (hsaCardBaseInfo.getValidityUnit().equals(DateUnitEnum.MONTH.getCode())) {
                hsaMemberInfoCard.setCardValidityDate(LocalDate.now().plusMonths(hsaCardBaseInfo.getCardValidityTime()));
            } else if (hsaCardBaseInfo.getValidityUnit().equals(DateUnitEnum.YEAR.getCode())) {
                hsaMemberInfoCard.setCardValidityDate(LocalDate.now().plusYears(hsaCardBaseInfo.getCardValidityTime()));
            }
        } else if (hsaCardBaseInfo.getCardValidity() == CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_YEAR.getCode()) {
            hsaMemberInfoCard.setCardValidityDate(hsaCardBaseInfo.getCardValidityDate());
        }

        hsaMemberInfoCardList.add(hsaMemberInfoCard);
        hsaElectronicCardList.add(electronicCard);

    }

    public void addStoreCardRuleArray(HsaMemberInfoCard hsaMemberInfoCard,
                                       List<HsaStoreCardRule> hsaStoreCardRuleList,
                                       Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap,
                                       List<HsaStoreCardRule> hsaStoreCardRuleArrayList) {
        hsaStoreCardRuleList.forEach(in -> {
            HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
            BeanUtils.copyProperties(in, hsaStoreCardRule);
            hsaStoreCardRule.setGuid(guidGeneratorUtil.getStringGuid(HsaStoreCardRule.class.getSimpleName()))
                    .setMemberInfoCardGuid(hsaMemberInfoCard.getGuid())
                    .setCardGuid(hsaMemberInfoCard.getCardGuid())
                    .setGmtCreate(LocalDateTime.now())
                    .setGmtModified(LocalDateTime.now());
            if (CollUtil.isNotEmpty(hsaStoreCardRuleMap) && hsaStoreCardRuleMap.containsKey(in.getGuid())) {
                List<HsaStoreCardRule> storeCardRule = hsaStoreCardRuleMap.get(in.getGuid());
                storeCardRule.forEach(on -> {
                    HsaStoreCardRule rule = new HsaStoreCardRule();
                    BeanUtils.copyProperties(on, rule);
                    rule.setParentGuid(hsaStoreCardRule.getGuid())
                            .setGuid(guidGeneratorUtil.getStringGuid(HsaStoreCardRule.class.getSimpleName()))
                            .setMemberInfoCardGuid(hsaMemberInfoCard.getGuid())
                            .setCardGuid(hsaMemberInfoCard.getCardGuid());
                    hsaStoreCardRuleArrayList.add(rule);
                });
            }
            hsaStoreCardRuleArrayList.add(hsaStoreCardRule);
        });
    }

    /**
     * 添加开卡余额明细
     *
     * @param memberInfoCard 会员关联会员卡实体
     * @param faceValue      卡面值
     * @return 操作结果
     */
    public HsaMemberFundingDetail addOpenCardBalanceRecord(HsaMemberInfoCard memberInfoCard, BigDecimal faceValue) {
        HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberFundingDetail.class.getSimpleName()));
        hsaMemberFundingDetail.setOperSubjectGuid(memberInfoCard.getOperSubjectGuid());
        hsaMemberFundingDetail.setMemberInfoGuid(memberInfoCard.getMemberInfoGuid());
        hsaMemberFundingDetail.setCardNum(!org.apache.commons.lang3.StringUtils.isEmpty(memberInfoCard.getElectronicCardNum())
                ? memberInfoCard.getElectronicCardNum() : memberInfoCard.getPhysicalCardNum());
        hsaMemberFundingDetail.setCardName(memberInfoCard.getCardName());
        hsaMemberFundingDetail.setCardGuid(memberInfoCard.getCardGuid());
        hsaMemberFundingDetail.setCardType(!org.apache.commons.lang3.StringUtils.isEmpty(memberInfoCard.getElectronicCardNum()) ? 1 : 0);
        hsaMemberFundingDetail.setMemberInfoCardGuid(memberInfoCard.getGuid());
        hsaMemberFundingDetail.setAmountSourceType(AmountSourceTypeEnum.PRESTORE_BALANCE.getCode());
        hsaMemberFundingDetail.setAmountRechargeFundingType(ADD);
        hsaMemberFundingDetail.setRechargeAmount(faceValue);
        hsaMemberFundingDetail.setCardRechargeResidualBalance(memberInfoCard.getCardAmount());
        hsaMemberFundingDetail.setCardGiftResidualBalance(memberInfoCard.getGiftAmount());
        hsaMemberFundingDetail.setCardSubsidyResidualBalance(memberInfoCard.getSubsidyAmount());
        hsaMemberFundingDetail.setRemark(AmountSourceTypeEnum.PRESTORE_BALANCE.getDes());
        hsaMemberFundingDetail.setOperatorTelName("指定发卡");
        hsaMemberFundingDetail.setIsValid(NumberConstant.NUMBER_1);
        hsaMemberFundingDetail.setChangeSource(SourceTypeEnum.ADD_BACKGROUND.getCode());
        hsaMemberFundingDetail.setGmtCreate(LocalDateTime.now());
        hsaMemberFundingDetail.setGmtModified(LocalDateTime.now());

        return hsaMemberFundingDetail;
    }


    public void addStoreCardRuleArrayList(String cardGuid,
                                           String newMemberCardGuid,
                                           List<HsaStoreCardRule> hsaStoreCardRuleList,
                                           Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap,
                                           List<HsaStoreCardRule> hsaStoreCardRuleArrayList) {
        if (CollUtil.isNotEmpty(hsaStoreCardRuleList)) {
            hsaStoreCardRuleList.forEach(in -> {
                HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
                BeanUtils.copyProperties(in, hsaStoreCardRule);
                hsaStoreCardRule.setGuid(guidGeneratorUtil.getStringGuid(HsaStoreCardRule.class.getSimpleName()))
                        .setMemberInfoCardGuid(newMemberCardGuid)
                        .setAddress(in.getAddress())
                        .setTime(in.getTime())
                        .setAddressPoint(in.getAddressPoint())
                        .setCardGuid(cardGuid)
                        .setGmtCreate(LocalDateTime.now())
                        .setGmtModified(LocalDateTime.now());
                if (CollUtil.isNotEmpty(hsaStoreCardRuleMap) && hsaStoreCardRuleMap.containsKey(in.getGuid())) {
                    List<HsaStoreCardRule> storeCardRule = hsaStoreCardRuleMap.get(in.getGuid());
                    storeCardRule.forEach(on -> {
                        HsaStoreCardRule rule = new HsaStoreCardRule();
                        BeanUtils.copyProperties(on, rule);
                        rule.setParentGuid(hsaStoreCardRule.getGuid())
                                .setGuid(guidGeneratorUtil.getStringGuid(HsaStoreCardRule.class.getSimpleName()))
                                .setMemberInfoCardGuid(newMemberCardGuid)
                                .setAddress(on.getAddress())
                                .setTime(on.getTime())
                                .setAddressPoint(on.getAddressPoint())
                                .setCardGuid(cardGuid);
                        hsaStoreCardRuleArrayList.add(rule);
                    });
                }
                hsaStoreCardRuleArrayList.add(hsaStoreCardRule);
            });

        }
    }

    public void setStall(HsaMemberInfoCard hsaMemberInfoCard,
                         Set<HsaStoreCardRule> hsaStoreCardRuleSet,
                         Map<String, String> map,
                         HsaStoreCardRule rule,
                         List<HsaStoreCardRule> storeCardRule,
                         HsaStoreCardRule in) {
        HsaStoreCardRule storeCardRule1 = new HsaStoreCardRule();
        BeanUtils.copyProperties(storeCardRule, storeCardRule1);
        storeCardRule1.setGuid(guidGeneratorUtil.getStringGuid(HsaStoreCardRule.class.getSimpleName()));
        storeCardRule1.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        storeCardRule1.setMemberInfoCardGuid(hsaMemberInfoCard.getGuid());
        storeCardRule1.setParentGuid(map.get(rule.getStoreGuid()));
        storeCardRule1.setStoreGuid(in.getStoreGuid());
        storeCardRule1.setStoreNumber(in.getStoreNumber());
        storeCardRule1.setAddressPoint(in.getAddressPoint());
        storeCardRule1.setStoreName(in.getStoreName());
        storeCardRule1.setAddress(in.getAddress());
        storeCardRule1.setTime(in.getTime());
        hsaStoreCardRuleSet.add(storeCardRule1);
    }

    public void seStore(HsaMemberInfoCard hsaMemberInfoCard,
                        Set<HsaStoreCardRule> hsaStoreCardRuleSet,
                        Map<String, String> map, HsaStoreCardRule rule) {
        HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        BeanUtils.copyProperties(rule, hsaStoreCardRule);
        hsaStoreCardRule.setGuid(guidGeneratorUtil.getStringGuid(HsaStoreCardRule.class.getSimpleName()));
        hsaStoreCardRule.setMemberInfoCardGuid(hsaMemberInfoCard.getGuid());
        hsaStoreCardRule.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        hsaStoreCardRule.setStoreGuid(rule.getStoreGuid());
        hsaStoreCardRule.setStoreNumber(rule.getStoreNumber());
        hsaStoreCardRule.setStoreName(rule.getStoreName());
        hsaStoreCardRule.setAddressPoint(rule.getAddressPoint());
        hsaStoreCardRule.setAddress(rule.getAddress());
        hsaStoreCardRule.setTime(rule.getTime());
        hsaStoreCardRuleSet.add(hsaStoreCardRule);
        map.put(hsaStoreCardRule.getStoreGuid(), hsaStoreCardRule.getGuid());
    }
}