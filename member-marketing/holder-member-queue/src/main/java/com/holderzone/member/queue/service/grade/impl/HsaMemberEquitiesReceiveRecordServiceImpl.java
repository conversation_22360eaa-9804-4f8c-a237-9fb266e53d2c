package com.holderzone.member.queue.service.grade.impl;

import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.queue.entity.HsaMemberEquitiesReceiveRecord;
import com.holderzone.member.queue.mapper.HsaMemberEquitiesReceiveRecordMapper;
import com.holderzone.member.queue.service.grade.HsaMemberEquitiesReceiveRecordService;
import org.springframework.stereotype.Service;


/**
 * 记录会员已经使用的权益信息
 * <AUTHOR>
 */
@Service
public class HsaMemberEquitiesReceiveRecordServiceImpl extends HolderBaseServiceImpl<HsaMemberEquitiesReceiveRecordMapper, HsaMemberEquitiesReceiveRecord>
        implements HsaMemberEquitiesReceiveRecordService {


}
