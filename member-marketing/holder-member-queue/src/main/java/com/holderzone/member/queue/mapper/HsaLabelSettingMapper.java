package com.holderzone.member.queue.mapper;

import com.holderzone.member.queue.config.mybatis.HolderBaseMapper;
import com.holderzone.member.queue.entity.HsaLabelSetting;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2021-12-22 18:37
 */
public interface HsaLabelSettingMapper extends HolderBaseMapper<HsaLabelSetting> {


    List<HsaLabelSetting> getHsaLabelSetting(@Param("operSubjectGuid")String operSubjectGuid);
}
