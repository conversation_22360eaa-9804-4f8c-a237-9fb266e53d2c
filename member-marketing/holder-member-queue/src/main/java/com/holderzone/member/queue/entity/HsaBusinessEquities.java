package com.holderzone.member.queue.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.enums.equities.ApplyCommodityTypeEnum;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @program: member-marketing
 * @description: 成长权益
 * @author: pan tao
 * @create: 2021-12-31 11:15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class HsaBusinessEquities implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 等级权益guid
     */
    private String guid;

    /**
     * 等级guid
     */
    private String memberGradeInfoGuid;

    /**
     * 权益名称
     */
    private String equitiesName;

    /**
     * 权益编号
     */
    private String equitiesNumber;

    /**
     * 权益类型 0:折扣权益 1:成长值权益 2:线下权益 3:积分权益 4:新品优先权 5:消费项权益
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private int equitiesType;

    /**
     * 权益规则类型 0：商品折扣 1：满减 2：直减 3：会员价 4：赠送成长值 5：翻倍成长值 6：赠送积分 7：翻倍积分
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesRuleTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private int equitiesRuleType;

    /**
     * 权益guid
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String equitiesGuid;

    /**
     * 成长值翻倍倍数
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal gradeValueDoubleNumber;

    /**
     * 成长值翻倍次数限制 0:不限制 1:限制
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer doubleCountLimited;

    /**
     * 翻倍次数上限
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer doubleCountUpperLimit;


    /**
     * 成长值单次翻倍限制 0:不限制 1:限制
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer singleDoubleLimited;

    /**
     * 单次翻倍成长值上限
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer singleDoubleUpperLimit;

    /**
     * 累计翻倍限制 0:不限制 1:限制
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer totalDoubleCountLimited;

    /**
     * 累计翻倍次数上限
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer totalDoubleCountUpperLimit;


    /**
     * 累计赠送成长值数量
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer totalGiveNumber;

    /**
     * 设置周期 -1:一次性  0:天  1:周  2:月  3:年
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer setPeriod;

    /**
     * 周期赠送数量
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer periodGiveNumber;

    /**
     * 折扣力度
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal discountDynamics;

    /**
     * 单次优惠限制 0：不限制 1：限制
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer singleDiscountsLimited;

    /**
     * 单次优惠限制金额
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal singleDiscountsLimitedAmount;

    /**
     * 周期优惠限制 0：不限制 1：限制
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer periodDiscountLimited;

    /**
     * 周期优惠限制类型 -1 不限制 0：天 1：周 2：月 3：天
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String periodDiscountType;

    /**
     * 累计优惠限制 0：不限制 1：限制
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer totalDiscountLimited;

    /**
     * 累计优惠限制金额
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal totalDiscountLimitedAmount;

    /**
     * 优惠叠加限制 0：不可叠加 1：可叠加
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer discountsSuperposition;

    /**
     * 权益生效时段限制 0：不限制 1：限制
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer equitiesEffectiveDateLimited;

    /**
     * 限制时段类型 -1:自定义 0：日 1：周 2：月 3：年
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer equitiesTimeLimitedType;

    /**
     * 限制时段限制类型json
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String equitiesTimeLimitedJson;


    /**
     * -1：全部商品适用 0：适用商品 1：不适用商品
     *
     * @see ApplyCommodityTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer applyGoodsType;

    /**
     * 适用终端 0:全部终端 1：部分终端
     *
     * @see com.holderzone.member.common.enums.equities.ApplyTerminalTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer applyTerminal;

    /**
     * 适用终端json
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String applyTerminalJson;

    /**
     * 适用业务 0:全部业务 1：部分业务
     *
     * @see com.holderzone.member.common.enums.equities.ApplyBusinessTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer applyBusiness;

    /**
     * 适用业务json
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String applyBusinessJson;

    /**
     * 适用渠道 0:全部渠道 1：部分渠道
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer applyChannel;

    /**
     * 适用渠道json
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String applyChannelJson;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

    /**
     * 是否删除,0未删除,1已删除 2：删除但未生效
     */
    private Integer isDelete;

    /**
     * 是否有效 0:无效 1:有效
     */
    private Integer effective;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 0 等级  1 会员卡
     * @see com.holderzone.member.common.enums.equities.BusinessTypeEnum
     */
    private Integer businessType;

    /**
     * 是否适用于所有门店(1:全部门店；0:部分门店(外关联表))
     *
     * @see com.holderzone.member.common.enums.growth.GoodsApplicableStoreEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer applicableAllStore;
}
