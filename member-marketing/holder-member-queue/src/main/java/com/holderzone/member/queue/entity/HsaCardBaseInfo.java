package com.holderzone.member.queue.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员卡基础信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Data
public class HsaCardBaseInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 会员GUID
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 企业GUID
     */
    private String enterpriseGuid;

    /**
     * 卡名称
     */
    private String cardName;

    /**
     * 是否适用于所有门店(1:全部门店；0:部分门店(外关联表))
     */
    private Integer applicableAllStore;

    /**
     * 卡颜色
     */
    private String cardColor;

    /**
     * 卡图片
     */
    private String cardImage;

    /**
     * 二维码
     */
    private String qrCode;

    /**
     * 卡面值金额
     */
    private BigDecimal cardValueMoney;

    /**
     * 卡默认支付密码
     */
    private String cardPayPassword;

    /**
     * 是否开启充值功能
     */
    private Integer isPreStored;

    /**
     * 小程序充值功能
     */
    private Integer appletRecharge;

    /**
     * 固定充值金额列表(JSON)
     */
    private String rechargeMoneys;

    /**
     * 是否开启任意充值
     */
    private Integer isRechargeAny;

    /**
     * 是否开启固定充值
     */
    private Integer isRechargeStipulate;

    /**
     * 充值限制（0不限制，1限制）
     */
    private Integer isRechargeUpperLimit;

    /**
     * 单次最少充值
     */
    private BigDecimal tinyRechargeMoney;

    /**
     * 单次最大充值
     */
    private BigDecimal maxRechargeMoney;

    /**
     * 会员卡使用状态(2:启用；3：禁用；4：已过期；5：草稿)
     */
    private Integer cardStatus;

    /**
     * 充值说明
     */
    private String cardRechargeExplain;

    /**
     * 卡有效期（0永久有效；1领取之日起；2固定日期）
     */
    private Integer cardValidity;

    /**
     * 领取之日起单位 1日、2周、3月、4年
     */
    private Integer validityUnit;

    /**
     * 领取之日时间
     */
    private Integer cardValidityTime;

    /**
     * 有效固定日期
     */
    private LocalDate cardValidityDate;

    /**
     * 是否超额
     */
    private Integer isExcess;

    /**
     * 0超额次数；1超额金额
     */
    private Integer excessType;

    /**
     * 超额次数
     */
    private Integer excessNum;

    /**
     * 超额金额
     */
    private BigDecimal excessMoney;

    /**
     * 使用须知
     */
    private String cardEmployExplain;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;
}
