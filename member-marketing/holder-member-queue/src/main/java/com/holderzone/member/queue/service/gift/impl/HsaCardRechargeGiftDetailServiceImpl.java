package com.holderzone.member.queue.service.gift.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.gift.GiftEffectiveEnum;
import com.holderzone.member.common.qo.card.CardFreezeBalanceAmountQO;
import com.holderzone.member.queue.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.queue.entity.HsaCardRechargeGiftDetail;
import com.holderzone.member.queue.entity.HsaMemberInfoCard;
import com.holderzone.member.queue.mapper.HsaCardRechargeGiftDetailMapper;
import com.holderzone.member.queue.mapper.HsaMemberInfoCardMapper;
import com.holderzone.member.queue.service.gift.HsaCardRechargeGiftDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Slf4j
@Service
public class HsaCardRechargeGiftDetailServiceImpl extends HolderBaseServiceImpl<HsaCardRechargeGiftDetailMapper, HsaCardRechargeGiftDetail>
        implements HsaCardRechargeGiftDetailService {

    private final HsaMemberInfoCardMapper hsaMemberInfoCardMapper;

    @Override
    public Map<String, BigDecimal> getMemberCardFreezeAmountMap(List<CardFreezeBalanceAmountQO> cardList) {
        getMemberInfoCards(cardList);
        Map<String, BigDecimal> memberCardFreezeAmountMap = Maps.newHashMap();
        for (CardFreezeBalanceAmountQO amount : cardList) {
            if (StringUtils.isEmpty(amount.getElectronicCardGuid()) && StringUtils.isEmpty(amount.getPhysicalCardGuid())) {
                continue;
            }
            List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetails = new ArrayList<>();
            getHsaCardRechargeGiftDetails(amount, hsaCardRechargeGiftDetails);
            if (CollectionUtils.isEmpty(hsaCardRechargeGiftDetails)) {
                memberCardFreezeAmountMap.put(amount.getMemberInfoCardGuid(), BigDecimal.ZERO);
            } else {
                BigDecimal totalAmount = hsaCardRechargeGiftDetails.stream()
                        .map(HsaCardRechargeGiftDetail::getMoneyAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                memberCardFreezeAmountMap.put(amount.getMemberInfoCardGuid(), totalAmount);
            }
        }
        return memberCardFreezeAmountMap;
    }

    /**
     * 查询实体卡、电子卡充值赠送明细
     */
    private void getHsaCardRechargeGiftDetails(CardFreezeBalanceAmountQO card, List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetails) {
        if (StringUtils.isNotEmpty(card.getElectronicCardGuid())) {
            // 电子卡
            List<HsaCardRechargeGiftDetail> getElectronicCardList = baseMapper.selectList(new LambdaQueryWrapper<HsaCardRechargeGiftDetail>()
                    .eq(HsaCardRechargeGiftDetail::getIsRefresh, BooleanEnum.FALSE.getCode())
                    .eq(StringUtils.isNotEmpty(card.getMemberInfoGuid()), HsaCardRechargeGiftDetail::getMemberGuid, card.getMemberInfoGuid())
                    .eq(HsaCardRechargeGiftDetail::getMoneyEffectiveType, GiftEffectiveEnum.FIXED_EFFECTIVE.getCode())
                    .eq(HsaCardRechargeGiftDetail::getElectronicCardGuid, card.getElectronicCardGuid())
                    //排除退款
                    .ne(Objects.nonNull(card.getExcludeMemberFundingDetail()), HsaCardRechargeGiftDetail::getMemberFundingDetailGuid, card.getExcludeMemberFundingDetail())
            );
            hsaCardRechargeGiftDetails.addAll(getElectronicCardList);

        }

        if (StringUtils.isNotEmpty(card.getPhysicalCardGuid())) {
            // 实体卡
            List<HsaCardRechargeGiftDetail> getPhysicalCardList = baseMapper.selectList(new LambdaQueryWrapper<HsaCardRechargeGiftDetail>()
                    .eq(HsaCardRechargeGiftDetail::getIsRefresh, BooleanEnum.FALSE.getCode())
                    .eq(StringUtils.isNotEmpty(card.getMemberInfoGuid()), HsaCardRechargeGiftDetail::getMemberGuid, card.getMemberInfoGuid())
                    .eq(HsaCardRechargeGiftDetail::getMoneyEffectiveType, GiftEffectiveEnum.FIXED_EFFECTIVE.getCode())
                    .eq(HsaCardRechargeGiftDetail::getPhysicalCardGuid, card.getPhysicalCardGuid())
                    //排除退款
                    .ne(Objects.nonNull(card.getExcludeMemberFundingDetail()),
                            HsaCardRechargeGiftDetail::getMemberFundingDetailGuid, card.getExcludeMemberFundingDetail())
            );
            hsaCardRechargeGiftDetails.addAll(getPhysicalCardList);
        }
    }


    private void getMemberInfoCards(List<CardFreezeBalanceAmountQO> cardList) {
        List<CardFreezeBalanceAmountQO> filterCardList = cardList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getMemberInfoCardGuid())).collect(Collectors.toList());
        List<String> memberInfoCardGuidList = filterCardList.stream()
                .map(CardFreezeBalanceAmountQO::getMemberInfoCardGuid)
                .distinct()
                .collect(Collectors.toList());
        List<HsaMemberInfoCard> memberInfoCardList = hsaMemberInfoCardMapper.queryByGuids(memberInfoCardGuidList);
        Map<String, HsaMemberInfoCard> memberInfoCardMap = memberInfoCardList.stream()
                .collect(Collectors.toMap(HsaMemberInfoCard::getGuid, Function.identity(), (key1, key2) -> key2));
        for (CardFreezeBalanceAmountQO amount : filterCardList) {
            HsaMemberInfoCard memberInfoCard = memberInfoCardMap.get(amount.getMemberInfoCardGuid());
            if (Objects.nonNull(memberInfoCard)) {
                amount.setElectronicCardGuid(memberInfoCard.getElectronicCardGuid());
                amount.setPhysicalCardGuid(memberInfoCard.getPhysicalCardGuid());
                amount.setMemberInfoGuid(memberInfoCard.getMemberInfoGuid());
            }
        }
    }

}
