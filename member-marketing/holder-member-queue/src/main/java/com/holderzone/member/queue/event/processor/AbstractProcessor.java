package com.holderzone.member.queue.event.processor;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.VerifyException;
import com.holderzone.member.common.dto.event.BaseEvent;
import lombok.extern.slf4j.Slf4j;

/**
 * 通道绑定后事件处理模板
 *
 * <AUTHOR>
 * @date 2020/07/07 11:24
 * @date 2021/8/11
 */
@Slf4j
public abstract class AbstractProcessor<T extends BaseEvent> {

    public T event;

    public AbstractProcessor(T event) {
        this.event = event;
    }

    protected String getLogTitle() {
        return "";
    }

    /**
     * 参数验证
     */
    protected void validate() {

    }

    /**
     * 业务处理
     */
    protected abstract void process();

    public void execute() {
        long time = System.currentTimeMillis();
        log.info("[{}] [{}]开始执行,参数={}", event.getSessionId(), getLogTitle(), JSONObject.toJSONString(event));
        try {
            validate();
            process();
        } catch (VerifyException e) {
            log.warn("[{}] [{}]执行异常,异常={}", event.getSessionId(), getLogTitle(), e.getMessage());
        } catch (Exception e) {
            log.error("[" + event.getSessionId() + "] [" + getLogTitle() + "] 执行错误,e:{}", e);
        }
        log.info("[{}] [{}] 执行完成,耗时={}ms", event.getSessionId(), getLogTitle(), System.currentTimeMillis() - time);
    }
}
