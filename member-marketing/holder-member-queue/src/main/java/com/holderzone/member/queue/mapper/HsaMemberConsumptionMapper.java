package com.holderzone.member.queue.mapper;


import com.holderzone.member.queue.config.mybatis.HolderBaseMapper;
import com.holderzone.member.queue.entity.HsaMemberConsumption;
import com.holderzone.member.queue.entity.HsaSuspendTaskTimeQuantum;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员消费记录表
 */
public interface HsaMemberConsumptionMapper extends HolderBaseMapper<HsaMemberConsumption> {

    /**
     * 查询所有消费（未退款的以及排除本次消费）
     *
     * @param memberInfoGuid 会员guid
     * @param guid           本次消费guid
     * @param beginTime      统计开始时间
     * @return 查询结果
     */
    BigDecimal selectByMemberGuid(@Param("memberInfoGuid") String memberInfoGuid, @Param("guid") String guid,
                                  @Param("beginTime") LocalDateTime beginTime,
                                  @Param("apply") List<String> apply,
                                  @Param("suspendTaskTime") List<HsaSuspendTaskTimeQuantum> suspendTaskTime);

    /**
     * 查询所有消费次数（未退款的以及排除本次消费）
     *
     * @param memberInfoGuid 会员guid
     * @param guid           本次消费guid
     * @param beginTime      统计开始时间
     * @return 查询结果
     */
    Integer selectConsumptionCount(@Param("memberInfoGuid") String memberInfoGuid, @Param("guid") String guid,
                                   @Param("beginTime") LocalDateTime beginTime, @Param("consumptionAmount") BigDecimal consumptionAmount,
                                   @Param("apply") List<String> apply,
                                   @Param("suspendTaskTime") List<HsaSuspendTaskTimeQuantum> suspendTaskTime);


    BigDecimal selectRechargeAmountMemberGuid(@Param("memberInfoGuid") String memberInfoGuid, @Param("guid") String guid,
                                              @Param("beginTime") LocalDateTime beginTime,
                                              @Param("suspendTaskTime") List<HsaSuspendTaskTimeQuantum> suspendTaskTime);
}
