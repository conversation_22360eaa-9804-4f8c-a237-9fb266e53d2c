package com.holderzone.member.queue.service.label.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.label.CancelConnectionTypeDTO;
import com.holderzone.member.common.dto.label.MemberLabelRecordDTO;
import com.holderzone.member.common.dto.label.RequestLabelQuery;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.member.ConditionSetEnum;
import com.holderzone.member.common.enums.member.ConnectionLabelTypeEnum;
import com.holderzone.member.common.enums.member.LabelCancelConnectTypeEnum;
import com.holderzone.member.common.enums.member.LabelConnectTypeEnum;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.queue.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.queue.dto.label.RelationLabelDTO;
import com.holderzone.member.queue.entity.HsaLabelSetting;
import com.holderzone.member.queue.entity.HsaLabelSettingBaseInfo;
import com.holderzone.member.queue.entity.HsaMemberLabel;
import com.holderzone.member.queue.entity.HsaMemberLabelRecord;
import com.holderzone.member.queue.mapper.*;
import com.holderzone.member.queue.service.label.HsaLabelSettingService;
import com.holderzone.member.queue.service.label.HsaMemberLabelRecordService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: member-marketing
 * @description: 标签service
 * @author: pan tao
 * @create: 2021-12-22 18:19
 */
@Service
@Slf4j
public class HsaLabelSettingServiceImpl extends HolderBaseServiceImpl<HsaLabelSettingMapper, HsaLabelSetting> implements HsaLabelSettingService {

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private HsaMemberLabelMapper hsaMemberLabelMapper;

    @Resource
    private HsaMemberLabelRecordMapper hsaMemberLabelRecordMapper;

    @Resource
    private HsaMemberLabelRecordService hsaMemberLabelRecordService;

    @Resource
    private HsaLabelSettingBaseInfoMapper hsaLabelSettingBaseInfoMapper;

    @Resource
    private HsaGrowthValueDetailMapper hsaGrowthValueDetailMapper;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private HsaLabelSettingMapper hsaLabelSettingMapper;

    @Override
    public void relationLabel(RelationLabelDTO event) {
        log.info("新增成长值刷标签===>" + JSONObject.toJSONString(event));
        HeaderUserInfo headerUserInfo = event.getUserInfo();
        // 查询所有标签(如新增会员)
        List<HsaLabelSetting> labelSettings = hsaLabelSettingMapper.getHsaLabelSetting(headerUserInfo.getOperSubjectGuid());
        if (CollectionUtils.isEmpty(labelSettings)) {
            return;
        }
        try {
            //休眠一秒再去判断
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
        }
        for (HsaLabelSetting labelSetting : labelSettings) {
            try {
                refreshLabelHandle(event.getMemberGuids(), labelSetting, headerUserInfo);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                log.error("关联失败标签guid：{}", labelSetting.getGuid());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchSaveMemberLabelRecord(List<MemberLabelRecordDTO> memberLabelRecordQOList, HeaderUserInfo headerUserInfo) {
        if (CollUtil.isNotEmpty(memberLabelRecordQOList)) {

            List<HsaMemberLabelRecord> saveMemberLabelRecordList = Lists.newArrayList();
            List<HsaMemberLabelRecord> updateMemberLabelRecordList = Lists.newArrayList();
            List<String> memberLabelGuids = memberLabelRecordQOList.stream().map(MemberLabelRecordDTO::getMemberLabelGuid).collect(Collectors.toList());
            List<HsaMemberLabelRecord> hsaMemberLabelRecords = hsaMemberLabelRecordMapper.selectList(new LambdaQueryWrapper<HsaMemberLabelRecord>()
                    .in(HsaMemberLabelRecord::getMemberLabelGuid, memberLabelGuids)
                    .eq(HsaMemberLabelRecord::getIsConnection, BooleanEnum.TRUE.getCode()));
            Map<String, HsaMemberLabelRecord> hsaMemberLabelRecordMap = hsaMemberLabelRecords.stream().collect(Collectors.
                    toMap(HsaMemberLabelRecord::getMemberLabelGuid, Function.identity(), (entity1, entity2) -> entity1));
            for (MemberLabelRecordDTO memberLabelRecordQO : memberLabelRecordQOList) {

                HsaMemberLabelRecord memberLabelRecord = hsaMemberLabelRecordMap.get(memberLabelRecordQO.getMemberLabelGuid());
                if (ObjectUtil.isNotNull(memberLabelRecord)) {
                    addUpdateMemberLabelRecord(headerUserInfo, memberLabelRecordQO, memberLabelRecord, updateMemberLabelRecordList);
                } else {
                    //此通道默认新增关联
                    addSaveMemberLabelRecord(headerUserInfo, memberLabelRecordQO, saveMemberLabelRecordList);
                }
            }
            if (CollUtil.isNotEmpty(updateMemberLabelRecordList)) {
                hsaMemberLabelRecordMapper.updateIsConnectionByGuid(updateMemberLabelRecordList);
            }
            if (CollUtil.isNotEmpty(saveMemberLabelRecordList)) {
                for (HsaMemberLabelRecord labelRecord : saveMemberLabelRecordList) {
                    hsaMemberLabelRecordMapper.insert(labelRecord);
                }
            }
        }
    }

    private static void addUpdateMemberLabelRecord(HeaderUserInfo headerUserInfo, MemberLabelRecordDTO memberLabelRecordQO, HsaMemberLabelRecord memberLabelRecord, List<HsaMemberLabelRecord> updateMemberLabelRecordList) {
        if (memberLabelRecordQO.getIsConnection() == BooleanEnum.FALSE.getCode()) {
            memberLabelRecord.setIsConnection(BooleanEnum.FALSE.getCode())
                    .setCancelConnectionTime(memberLabelRecordQO.getCancelConnectionTime())
                    .setCancelConnectionType(memberLabelRecordQO.getCancelConnectionType());
            if (memberLabelRecordQO.getCancelConnectionType() == LabelCancelConnectTypeEnum.MANUAL.getCode()) {
                memberLabelRecord.setCancelConnectionOperator(getConnectionOperator(memberLabelRecordQO.getCancelConnectionOperator(), headerUserInfo));
            }
            updateMemberLabelRecordList.add(memberLabelRecord);
        } else {
            if (!memberLabelRecord.getConnectionType().equals(memberLabelRecordQO.getConnectionType())) {
                memberLabelRecord.setConnectionType(memberLabelRecordQO.getConnectionType());
                updateMemberLabelRecordList.add(memberLabelRecord);
            }
        }
    }

    private void addSaveMemberLabelRecord(HeaderUserInfo headerUserInfo, MemberLabelRecordDTO memberLabelRecordQO, List<HsaMemberLabelRecord> saveMemberLabelRecordList) {
        HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberLabelRecord.class.getSimpleName()))
                .setConnectionTime(memberLabelRecordQO.getConnectionTime())
                .setConnectionType(memberLabelRecordQO.getConnectionType())
                .setIsConnection(BooleanEnum.TRUE.getCode())
                .setOperSubjectGuid(memberLabelRecordQO.getOperSubjectGuid())
                .setMemberInfoGuid(memberLabelRecordQO.getMemberInfoGuid())
                .setLabelType(memberLabelRecordQO.getLabelType())
                .setLabelSettingGuid(memberLabelRecordQO.getLabelSettingGuid())
                .setLabelName(memberLabelRecordQO.getLabelName())
                .setMemberLabelGuid(memberLabelRecordQO.getMemberLabelGuid());
        if (memberLabelRecordQO.getConnectionType() == LabelConnectTypeEnum.MANUAL.getCode()) {
            hsaMemberLabelRecord.setConnectionOperator(getConnectionOperator(memberLabelRecordQO.getConnectionOperator(), headerUserInfo));
        }
        saveMemberLabelRecordList.add(hsaMemberLabelRecord);
    }

    private static String getConnectionOperator(String memberLabelRecordQO, HeaderUserInfo headerUserInfo) {
        if (StringUtil.isNotEmpty(memberLabelRecordQO)) {
            return memberLabelRecordQO;
        } else {
            return StringUtil.isNotEmpty(headerUserInfo.getUserName()) ? headerUserInfo.getUserName() + StringConstant.STR_BIAS + headerUserInfo.getTel() : null;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void refreshLabelHandle(Set<String> memberInfoGuids, HsaLabelSetting labelSetting, HeaderUserInfo headerUserInfo) {

        //log.info("企业会员人数：" + memberInfoGuids.size());
        HsaLabelSettingBaseInfo baseInfo = hsaLabelSettingBaseInfoMapper.selectOne(new LambdaQueryWrapper<HsaLabelSettingBaseInfo>()
                .eq(HsaLabelSettingBaseInfo::getLabelSettingGuid, labelSetting.getGuid()));
        RequestLabelQuery query = new RequestLabelQuery();
        query.setMemberGuid(memberInfoGuids);
        // 设置query参数
        querySetting(query, labelSetting, baseInfo, headerUserInfo);
        // 获取标签条件设置： 0满足以下任一条件，1满足以下所有条件
        Integer conditionSet = labelSetting.getConditionSet();
        Set<String> members;
        //满足以下任一条件
        if (conditionSet == ConditionSetEnum.ONE_CONDITION.getCode()) {
            //成长值信息
            members = growthValueFilter(query);
            log.info("满足条件会员数量===================>" + members.size());
            // 保存会员标签
            saveLabelMember(labelSetting, members, headerUserInfo, memberInfoGuids);
        } else {
            //是否进行过条件筛选 进行过的members就要去交集
            members = growthValueFilter(query);
            // 保存会员标签
            saveLabelMember(labelSetting, members, headerUserInfo, memberInfoGuids);
        }
    }

    private void querySetting(RequestLabelQuery query, HsaLabelSetting labelSetting, HsaLabelSettingBaseInfo baseInfo,
                              HeaderUserInfo headerUserInfo) {
        BeanUtils.copyProperties(baseInfo, query);
        BeanUtils.copyProperties(labelSetting, query, "gmtCreate", "gmtModified");
        query.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid());
        //成长值
        query.setCardMemberLevelGuid(StringUtils.isEmpty(baseInfo.getCardMemberLevelGuid()) ? null :
                baseInfo.getCardMemberLevelGuid().split(StringConstant.COMMA));
        query.setGrowValueFixedDateJson(StringUtils.isEmpty(baseInfo.getGrowValueFixedDateJson()) ? null :
                baseInfo.getGrowValueFixedDateJson().split(StringConstant.COMMA));
    }

    /**
     * 成长值过滤
     *
     * @param query sql请求参数
     * @return 查询结果
     */
    private Set<String> growthValueFilter(RequestLabelQuery query) {

        return hsaGrowthValueDetailMapper.findRelationLabelMemberGuid(query).stream().collect(Collectors.toSet());
    }

    @Transactional(rollbackFor = Exception.class)
    void saveLabelMember(HsaLabelSetting label, Set<String> members, HeaderUserInfo headerUserInfo, Set<String> memberInfoGuids) {
        RLock lock = redissonClient.getLock("SAVE_LABEL_MEMBER");
        try {
            if (!lock.tryLock(NumberConstant.NUMBER_20, NumberConstant.NUMBER_20, TimeUnit.MINUTES)) {
                return;
            }
            List<HsaMemberLabel> memberLabels = new ArrayList<>();
            List<MemberLabelRecordDTO> memberLabelRecordDTOArrayList = Lists.newArrayList();
            List<String> removeids = new ArrayList<>();
            List<String> addids = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();
            // 比较当前标签顾客是否在存在以及是否新增
            // 对传入顾客先判断是否在标签里,如果在,判断过滤后是否还在,如果不在了就删除此顾客
            // 如果不在标签里,过滤后还在,那么则追加
            List<HsaMemberLabel> labels = hsaMemberLabelMapper.selectList(new LambdaQueryWrapper<HsaMemberLabel>()
                    .eq(HsaMemberLabel::getLabelSettingGuid, label.getGuid())
                    .eq(HsaMemberLabel::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid())
                    .in(!CollectionUtils.isEmpty(memberInfoGuids), HsaMemberLabel::getOperationMemberInfoGuid, memberInfoGuids));
            //标签下会员guid对应关联类型map
            Map<String, Integer> map = labels.stream().collect(Collectors.toMap(HsaMemberLabel::getOperationMemberInfoGuid,
                    HsaMemberLabel::getConnectionType, (entity1, entity2) -> entity1));

            List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS;
            if (CollectionUtils.isEmpty(memberInfoGuids)) {
                cancelConnectionTypeDTOS = hsaMemberLabelRecordMapper.findLastCancelConnectionType(label.getGuid());
            } else {
                cancelConnectionTypeDTOS = hsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember(label.getGuid(),
                        memberInfoGuids);
            }
            Map<String, CancelConnectionTypeDTO> connectionTypeMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(members)) {
                connectionTypeMap = cancelConnectionTypeDTOS.stream().collect(Collectors.
                        toMap(CancelConnectionTypeDTO::getMemberInfoGuid, Function.identity(),
                                (entity1, entity2) -> entity1));
            }
            for (String member : members) {
                if (Objects.isNull(map.get(member))) {
                    //人工取关的 不添加
                    if (Objects.nonNull(connectionTypeMap.get(member)) && Objects.nonNull(connectionTypeMap.get(member).
                            getCancelConnectionType()) && connectionTypeMap.get(member).getCancelConnectionType() ==
                            LabelCancelConnectTypeEnum.MANUAL.getCode()) {
                        continue;
                    }
                    addids.add(member);
                }
                map.remove(member);
            }
            //不是指定会员，需要移除不满足添加的关联
            Set<Map.Entry<String, Integer>> entrySet = map.entrySet();
            for (Map.Entry<String, Integer> entry : entrySet) {
                String key = entry.getKey();
                if (map.get(key) == LabelConnectTypeEnum.AUTOMATION.getCode()) {
                    //如果是指定会员,只移除指定会员
                    if (!CollectionUtils.isEmpty(memberInfoGuids)) {
                        if (memberInfoGuids.contains(key)) {
                            removeids.add(key);
                        }
                    } else {
                        removeids.add(key);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(removeids)) {
                //取关记录
                getCancelMemberLabelList(label, memberLabelRecordDTOArrayList, removeids, headerUserInfo);
                hsaMemberLabelMapper.delete(new LambdaQueryWrapper<HsaMemberLabel>()
                        .eq(HsaMemberLabel::getLabelSettingGuid, label.getGuid())
                        .in(HsaMemberLabel::getOperationMemberInfoGuid, removeids));
                log.info("删除的标签个数{}", removeids.size());
            }
            if (!CollectionUtils.isEmpty(addids)) {
                for (String memberInfoGuid : addids) {
                    HsaMemberLabel hsmMemberLabel = new HsaMemberLabel()
                            .setOperSubjectGuid(label.getOperSubjectGuid())
                            .setLabelSettingGuid(com.holderzone.member.common.util.verify.ObjectUtil.objToString(label.getGuid()))
                            .setGuid(guidGeneratorUtil.getStringGuid(HsaMemberLabel.class.getSimpleName()).toString())
                            .setConnectionType(LabelConnectTypeEnum.AUTOMATION.getCode())
                            .setOperationMemberInfoGuid(memberInfoGuid)
                            .setGmtCreate(now)
                            .setGmtModified(now);
                    memberLabels.add(hsmMemberLabel);
                    //关联记录
                    getMemberLabelRecord(label, memberLabelRecordDTOArrayList, now, hsmMemberLabel, headerUserInfo);
                }
                if (!CollectionUtils.isEmpty(memberLabels)) {
                    log.info("标签:{},追加的人数为:{}", label.getLabelName(), addids.size());
                    hsaMemberLabelMapper.batchSave(memberLabels);
                    //保存关联记录
                    batchSaveMemberLabelRecord(memberLabelRecordDTOArrayList, headerUserInfo);
                }
            }

        } catch (Exception e) {
            log.info("标签关联失败，失败标签，{}", label.getLabelName());
        } finally {
            if (lock != null && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    private void getMemberLabelRecord(HsaLabelSetting label, List<MemberLabelRecordDTO> memberLabelRecordDTOArrayList, LocalDateTime now, HsaMemberLabel hsmMemberLabel, HeaderUserInfo headerUserInfo) {
        MemberLabelRecordDTO labelRecordQO = new MemberLabelRecordDTO();
        labelRecordQO.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid())
                .setIsConnection(ConnectionLabelTypeEnum.AUTOMATION.getCode())
                .setMemberLabelGuid(hsmMemberLabel.getGuid())
                .setConnectionTime(now)
                .setConnectionType(LabelConnectTypeEnum.AUTOMATION.getCode())
                .setMemberInfoGuid(hsmMemberLabel.getOperationMemberInfoGuid())
                .setLabelSettingGuid(hsmMemberLabel.getLabelSettingGuid())
                .setLabelType(label.getLabelType())
                .setLabelName(label.getLabelName());
        memberLabelRecordDTOArrayList.add(labelRecordQO);
    }

    private void getCancelMemberLabelList(HsaLabelSetting label, List<MemberLabelRecordDTO> memberLabelRecordDTOArrayList, List<String> removeids, HeaderUserInfo headerUserInfo) {
        List<HsaMemberLabel> hsaMemberLabelList = hsaMemberLabelMapper.selectList(new LambdaQueryWrapper<HsaMemberLabel>()
                .eq(HsaMemberLabel::getLabelSettingGuid, label.getGuid())
                .in(HsaMemberLabel::getOperationMemberInfoGuid, removeids));
        if (CollUtil.isNotEmpty(hsaMemberLabelList)) {
            hsaMemberLabelList.forEach(in -> {
                MemberLabelRecordDTO labelRecordQO = new MemberLabelRecordDTO();
                labelRecordQO.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid())
                        .setLabelSettingGuid(in.getLabelSettingGuid())
                        .setMemberInfoGuid(in.getOperationMemberInfoGuid())
                        .setIsConnection(ConnectionLabelTypeEnum.MANUAL.getCode())
                        .setMemberLabelGuid(in.getGuid())
                        .setCancelConnectionTime(LocalDateTime.now())
                        .setConnectionType(LabelConnectTypeEnum.AUTOMATION.getCode())
                        .setCancelConnectionType(LabelCancelConnectTypeEnum.AUTOMATION.getCode());
                memberLabelRecordDTOArrayList.add(labelRecordQO);
            });
            batchSaveMemberLabelRecord(memberLabelRecordDTOArrayList, headerUserInfo);
        }
    }


}
