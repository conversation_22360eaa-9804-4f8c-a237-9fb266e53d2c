package com.holderzone.member.queue.config.mybatis;

import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlScriptUtils;
import com.baomidou.mybatisplus.extension.injector.AbstractLogicMethod;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

/**
 * @date 2020/05/09 14:46
 * @description QueryByGuid-sql拼接
 */
public class LogicQueryByGuids extends AbstractLogicMethod {
    private static final String MAPPER_METHOD = "queryByGuids";
    private static final String MAPPER_SQL = "<script>\nSELECT %s FROM %s WHERE %s IN (%s)\n</script>";
    private static final String GUID_NAME = "guid";

    public LogicQueryByGuids() {
    }

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        SqlSource sqlSource = this.languageDriver.createSqlSource(this.configuration,
                String.format(MAPPER_SQL, this.sqlSelectColumns(tableInfo, false),
                        tableInfo.getTableName(), GUID_NAME,
                        SqlScriptUtils.convertForeach("#{guid}", "guids", (String) null, "guid", ","),
                        tableInfo.getLogicDeleteSql(true, false)), modelClass);
        return this.addSelectMappedStatement(mapperClass, MAPPER_METHOD, sqlSource, modelClass, tableInfo);
    }
}
