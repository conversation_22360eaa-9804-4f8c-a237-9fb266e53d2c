package com.holderzone.member.queue.config.mybatis;

import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.extension.injector.AbstractLogicMethod;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

/**
 * @date 2020/05/09 14:46
 * @description RemoveByGuid-sql拼接
 */
public class LogicRemoveByGuid extends AbstractLogicMethod {
    private static final String MAPPER_METHOD = "removeByGuid";
    private static final String MAPPER_SQL = "DELETE FROM %s WHERE %s=#{%s}";
    private static final String GUID_NAME = "guid";

    public LogicRemoveByGuid() {
    }

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        String sql = String.format(MAPPER_SQL, tableInfo.getTableName(), GUID_NAME, GUID_NAME);
        SqlSource sqlSource = this.languageDriver.createSqlSource(this.configuration, sql, modelClass);
        return this.addUpdateMappedStatement(mapperClass, modelClass, MAPPER_METHOD, sqlSource);
    }
}
