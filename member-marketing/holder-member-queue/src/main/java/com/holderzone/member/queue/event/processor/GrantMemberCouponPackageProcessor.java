package com.holderzone.member.queue.event.processor;

import com.google.common.base.Verify;
import com.holderzone.member.common.dto.event.GrantMemberCouponPackageEvent;
import com.holderzone.member.common.util.transaction.SpringContextUtils;
import com.holderzone.member.queue.service.coupon.MemberCouponPackageLinkService;
import lombok.extern.slf4j.Slf4j;


/**
 * 券包发放
 *
 * <AUTHOR>
 * @version 1.0
 * @className GrowthValueChangProcessor
 * @description 券包发放
 */
@Slf4j
public class GrantMemberCouponPackageProcessor extends AbstractProcessor<GrantMemberCouponPackageEvent> {

    private final MemberCouponPackageLinkService memberCouponPackageLinkService;

    public GrantMemberCouponPackageProcessor(GrantMemberCouponPackageEvent event) {
        super(event);
        memberCouponPackageLinkService = SpringContextUtils.getBean(MemberCouponPackageLinkService.class);
    }

    @Override
    protected String getLogTitle() {
        return "GrantMemberCouponPackageProcessor";
    }

    /**
     * 参数验证
     */
    @Override
    protected void validate() {
        Verify.verify(event != null, "参数错误");
    }

    @Override
    protected void process() {
        try {
            memberCouponPackageLinkService.xxlJobCouponPackageGrant(event.getDate());
        }catch (Exception e) {
            log.info("券包发放失败", e);
        }

    }
}
