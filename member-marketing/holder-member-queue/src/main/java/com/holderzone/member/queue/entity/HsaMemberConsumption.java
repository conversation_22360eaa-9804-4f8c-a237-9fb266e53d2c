package com.holderzone.member.queue.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员消费记录表
 *
 * <AUTHOR>
 * @since 2021-11-25 16:45:11
 */
@ApiModel("会员消费记录表")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberConsumption implements Serializable {
    private static final long serialVersionUID = 506734846772812837L;

    /**
     * 会员消费记录GUID
     */
    @ApiModelProperty("会员消费记录GUID")
    private String guid;

    /**
     * 运营主体GUID
     */
    @ApiModelProperty("运营主体GUID")
    private String operSubjectGuid;

    /**
     * 会员GUID
     */
    @ApiModelProperty("会员GUID")
    private String memberInfoGuid;

    /**
     * 会员持卡GUID
     */
    @ApiModelProperty("会员持卡GUID")
    private String memberInfoCardGuid;

    /**
     * 主卡编号
     */
    @ApiModelProperty("卡编号")
    private String cardNum;

    /**
     * 消费企业GUID
     */
    @ApiModelProperty("消费企业GUID")
    private String enterpriseGuid;

    /**
     * 消费门店GUID
     */
    @ApiModelProperty("消费门店GUID")
    private String storeGuid;

    /**
     * 消费门店名称
     */
    @ApiModelProperty("消费门店名称")
    private String storeName;

    /**
     * 订单金额
     */
    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;

    /**
     * 订单实付金额
     */
    @ApiModelProperty("订单实付金额")
    private BigDecimal orderPaidAmount;

    /**
     * 订单优惠金额，无优惠为0
     */
    @ApiModelProperty("订单优惠金额，无优惠为0")
    private BigDecimal orderDiscountAmount;

    /**
     * 卡支付金额
     */
    private BigDecimal cardBalancePayAmount;

    /**
     * 卡剩余余额
     */
    @ApiModelProperty("卡剩余余额")
    private BigDecimal cardResidualBalance;

    /**
     * 消费类型，0充值，1消费
     */
    @ApiModelProperty("消费类型，0充值，1消费")
    private Integer consumptionType;

    /**
     * 消费时间
     */
    @ApiModelProperty("消费时间")
    private LocalDateTime consumptionTime;

    /**
     * 订单guid（外部订单guid）
     */
    @ApiModelProperty("订单guid（外部订单guid）")
    private String orderGuid;

    /**
     * 银行交易流水号（赚餐储值卡充值为交易号）
     */
    @ApiModelProperty("银行交易流水号")
    private String bankTransactionId;

    /**
     * 订单编号（外部订单编号）
     */
    @ApiModelProperty("订单编号（外部订单编号）")
    private String orderNumber;

    /**
     * 下单时间
     */
    @ApiModelProperty("下单时间")
    private LocalDateTime orderTime;

    /**
     * 来源，0微信，2 一体机,1POS
     */
    @ApiModelProperty("来源，53微信，2 一体机,1POS")
    private Integer orderSource;

    /**
     * 是否被撤销:0 否，1 是
     */
    @ApiModelProperty("是否被撤销:0 否，1 是")
    private Integer isCancel;

    /**
     * 操作人员账号
     */
    @ApiModelProperty("操作人员账号")
    private String operatorTelName;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    private LocalDateTime gmtCreate;


    private LocalDateTime gmtModified;

    /**
     * 订单类型 0：预定订单 1：点餐订单 2：自助餐 3：快速收款
     * @see com.holderzone.member.common.enums.member.ConsumptionOrderTypeEnum
     */
    private Integer orderType;

    /**
     * 是否超额
     **/
    @ApiModelProperty("是否超额")
    private Integer excess;

    /**
     * 0超额次数；1超额金额
     */
    @ApiModelProperty("0超额次数；1超额金额")
    private Integer excessType;

    /**
     * 超额金额/次数
     */
    @ApiModelProperty("超额金额/次数")
    private BigDecimal excessValue;

    /**
     * 订单是否已经完成 0：未完成 1：已完成
     */
    @ApiModelProperty("订单是否已经完成")
    private int isComplete;

    /**
     * 退款时间
     */
    private LocalDateTime refundTime;

    /**
     * 所属单位
     */
    private String workName;

    /**
     * 所属部门
     */
    private String departmentName;
}