package com.holderzone.member.queue.event.processor;

import com.google.common.base.Verify;
import com.holderzone.member.common.dto.event.SendOpenElectronicCardEvent;
import com.holderzone.member.common.util.transaction.SpringContextUtils;
import com.holderzone.member.queue.service.card.MemberInfoCardService;
import org.springframework.util.CollectionUtils;

/**
 * 开通电子卡
 *
 * <AUTHOR>
 * @version 1.0
 * @className OpenElectronicCardProcessor
 * @date 2021/09/16 14:15
 * @description 开通电子卡
 * @program holder-member-queue
 */
public class OpenElectronicCardProcessor extends AbstractProcessor<SendOpenElectronicCardEvent> {

    private MemberInfoCardService memberInfoCardService;


    public OpenElectronicCardProcessor(SendOpenElectronicCardEvent event) {
        super(event);
        this.memberInfoCardService = SpringContextUtils.getBean(MemberInfoCardService.class);
    }

    @Override
    protected String getLogTitle() {
        return "SendOpenElectronicCard";
    }

    /**
     * 参数验证
     */
    @Override
    protected void validate() {
        Verify.verify(!CollectionUtils.isEmpty(event.getSendOpenCardEvents()), "参数错误");
    }

    @Override
    protected void process() {
        memberInfoCardService.sendOpenElectronicCard(event);
    }
}
