package com.holderzone.member.queue.dto.grade;

import com.beust.jcommander.internal.Maps;
import com.holderzone.member.queue.entity.HsaGiftBagBaseInfo;
import com.holderzone.member.queue.entity.HsaGrowthValueDetail;
import com.holderzone.member.queue.entity.HsaIntegralDetail;
import com.holderzone.member.queue.entity.HsaMemberGradeRightsRecord;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
public class GradeRightsProcessorDTO {

    // 更新会员积分
    private Map<String, Integer> updateMemberIntegralMap = Maps.newHashMap();

    private LocalDateTime now;

    private  HsaGiftBagBaseInfo hsaGiftBagBaseInfo;

    private List<HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecords;

    private List<HsaGrowthValueDetail> hsaGrowthValueDetails;

    private List<HsaIntegralDetail> hsaIntegralDetails;

    private  Integer sourceType;

    private String operSubjectGuid;
}
