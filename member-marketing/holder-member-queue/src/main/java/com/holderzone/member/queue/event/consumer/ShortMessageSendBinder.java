package com.holderzone.member.queue.event.consumer;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.member.common.constant.BinderConstant;
import com.holderzone.member.common.dto.event.PushShortMessageEvent;
import com.holderzone.member.queue.event.BinderChannel;
import com.holderzone.member.queue.event.processor.ShortMessageSendProcessor;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.messaging.handler.annotation.Header;

import java.io.IOException;

/**
 * 推送优惠项
 *
 * <AUTHOR>
 */
@Slf4j
@EnableBinding(BinderChannel.class)
public class ShortMessageSendBinder {

    /**
     * 推送
     */
    @StreamListener(BinderConstant.INPUT_PUSH_SHORT_MESSAGE_SEND)
    public void execute(Message<PushShortMessageEvent> message,
                        @Header(AmqpHeaders.CHANNEL) Channel channel,
                        @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) throws IOException {
        PushShortMessageEvent requestEvent = message.getPayload();
        if (StringUtils.isEmpty(requestEvent.getSessionId())) {
            requestEvent.setSessionId(String.valueOf(System.currentTimeMillis()));
        }
        log.info("[{}] [PushShortMessageEvent] 参数={}", requestEvent.getSessionId(), JacksonUtils.writeValueAsString(requestEvent));
        try {
            new ShortMessageSendProcessor(requestEvent).execute();
        } finally {
            //手动确认
            channel.basicAck(deliveryTag, false);
        }

    }

}