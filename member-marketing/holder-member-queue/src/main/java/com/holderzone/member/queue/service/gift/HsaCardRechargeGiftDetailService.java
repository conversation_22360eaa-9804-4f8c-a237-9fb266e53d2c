package com.holderzone.member.queue.service.gift;

import com.holderzone.member.common.qo.card.CardFreezeBalanceAmountQO;
import com.holderzone.member.queue.config.mybatis.IHolderBaseService;
import com.holderzone.member.queue.entity.HsaCardRechargeGiftDetail;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * HsaCardRechargeGiftRuleService
 */
public interface HsaCardRechargeGiftDetailService extends IHolderBaseService<HsaCardRechargeGiftDetail> {

    /**
     * 批量获取卡剩余冻结金额
     */
    Map<String, BigDecimal> getMemberCardFreezeAmountMap(List<CardFreezeBalanceAmountQO> cardList);

}