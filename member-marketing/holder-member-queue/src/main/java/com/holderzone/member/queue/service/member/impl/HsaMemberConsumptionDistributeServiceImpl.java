package com.holderzone.member.queue.service.member.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.member.common.constant.CommonConstant;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.dto.event.SendMemberConsumptionDistributeEvent;
import com.holderzone.member.common.dto.s2b2c.MemberConsumptionDistributeDTO;
import com.holderzone.member.common.enums.member.DistributeTypeEnum;
import com.holderzone.member.common.feign.S2b2cMallFeign;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.constant.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.queue.entity.HsaMemberConsumptionDistribute;
import com.holderzone.member.queue.mapper.HsaMemberConsumptionDistributeMapper;
import com.holderzone.member.queue.service.member.HsaMemberConsumptionDistributeService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 会员消费分销记录服务实现
 *
 * <AUTHOR>
 * @version 1.0 2025/06/04
 *
 */
@Slf4j
@Service
public class HsaMemberConsumptionDistributeServiceImpl
        extends ServiceImpl<HsaMemberConsumptionDistributeMapper, HsaMemberConsumptionDistribute> implements HsaMemberConsumptionDistributeService {

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private S2b2cMallFeign s2b2cMallFeign;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processDistributeRecord(SendMemberConsumptionDistributeEvent event) {
        log.info("处理会员消费分销记录开始，运营主体：{}, 订单号：{}, 消费GUID：{}, 分销类型：{}", 
                event.getOperSubjectGuid(), event.getOrderNumber(), 
                event.getConsumptionGuid(), event.getDistributeRecordType());

        try {
            // 根据分销记录类型进行不同的处理
            switch (event.getDistributeRecordType()) {
                case NOT_REFUND:
                    handleCompletedDistribute(event);
                    break;
                case ALL_REFUND:
                    handleFullRefundDistribute(event);
                    break;
                case PART_REFUND:
                    handlePartialRefundDistribute(event);
                    break;
                default:
                    log.warn("未知的分销记录类型：{}", event.getDistributeRecordType());
                    break;
            }
            
            log.info("处理会员消费分销记录完成，订单号：{}", event.getOrderNumber());
            
        } catch (Exception e) {
            log.error("处理会员消费分销记录异常，订单号：{}, 异常信息：{}", 
                    event.getOrderNumber(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理订单完成的分销记录
     */
    private void handleCompletedDistribute(SendMemberConsumptionDistributeEvent event) {
        log.info("处理订单完成分销记录，订单号：{}", event.getOrderNumber());
        
        // 检查是否已经存在相同consumptionGuid的记录
        HsaMemberConsumptionDistribute existingDistribute = checkExistingDistribute(event.getConsumptionGuid(), DistributeTypeEnum.NOT_REFUND);
        
        HsaMemberConsumptionDistribute distribute;
        if (existingDistribute != null) {
            if (existingDistribute.getIsComplete() != null && existingDistribute.getIsComplete() == 1) {
                log.warn("消费记录已经推送完成，跳过处理，订单号：{}, 消费GUID：{}", 
                        event.getOrderNumber(), event.getConsumptionGuid());
                return;
            } else {
                log.info("发现未推送的分销记录，直接使用现有记录进行推送，订单号：{}, 记录GUID：{}", 
                        event.getOrderNumber(), existingDistribute.getGuid());
                distribute = existingDistribute;
            }
        } else {
            // 保存新的分销记录
            distribute = saveDistributeRecord(event);
        }

        if (StrUtil.isEmpty(event.getMemberInfoGuid()))return;

        MemberConsumptionDistributeDTO distributeDTO = BeanUtil.copyProperties(event, MemberConsumptionDistributeDTO.class,"distributeRecordType");
        distributeDTO.setDistributeRecordType(DistributeTypeEnum.NOT_REFUND);
        
        // 打印请求参数日志
        log.info("调用s2b2cMallFeign.orderCompleteDistribute开始，订单号：{}, 请求参数：{}", 
                event.getOrderNumber(), JacksonUtils.writeValueAsString(distributeDTO));
        
        try {
            Result<Void> result = s2b2cMallFeign.orderCompleteDistribute(distributeDTO);
            
            // 打印响应结果日志
            log.info("调用s2b2cMallFeign.orderCompleteDistribute成功，订单号：{}, 响应结果：{}", 
                    event.getOrderNumber(), JacksonUtils.writeValueAsString(result));
            
            // 更新推送完成状态
            updateDistributeCompleteStatus(distribute.getGuid(), true);
            log.info("更新分销记录推送状态为已完成，订单号：{}, 记录GUID：{}", 
                    event.getOrderNumber(), distribute.getGuid());
                    
        } catch (Exception e) {
            log.error("调用s2b2cMallFeign.orderCompleteDistribute异常，订单号：{}, 异常信息：{}", 
                    event.getOrderNumber(), e.getMessage(), e);
            // 更新推送失败状态
            updateDistributeCompleteStatus(distribute.getGuid(), false);
            throw e;
        }
        
        log.info("订单完成分销记录处理完成，订单号：{}", event.getOrderNumber());
    }

    /**
     * 处理整单退款的分销记录
     */
    private void handleFullRefundDistribute(SendMemberConsumptionDistributeEvent event) {
        log.info("处理整单退款分销记录，订单号：{}", event.getOrderNumber());
        
        // 检查是否已经存在全额退款的分销记录
        boolean existsFullRefund = checkExistsFullRefundRecord(event.getOrderNumber(), event.getOperSubjectGuid());
        if (existsFullRefund) {
            log.warn("订单号：{} 已存在全额退款分销记录，跳过处理", event.getOrderNumber());
            return;
        }
        
        // 检查是否已经存在相同newConsumptionGuid的记录
        HsaMemberConsumptionDistribute existingDistribute = checkExistingDistribute(event.getNewConsumptionGuid(), DistributeTypeEnum.ALL_REFUND);
        
        HsaMemberConsumptionDistribute distribute;
        if (existingDistribute != null) {
            if (existingDistribute.getIsComplete() != null && existingDistribute.getIsComplete() == 1) {
                log.warn("退款消费记录已经推送完成，跳过处理，订单号：{}, 新消费GUID：{}", 
                        event.getOrderNumber(), event.getNewConsumptionGuid());
                return;
            } else {
                log.info("发现未推送的退款分销记录，直接使用现有记录进行推送，订单号：{}, 记录GUID：{}", 
                        event.getOrderNumber(), existingDistribute.getGuid());
                distribute = existingDistribute;
            }
        } else {
            // 保存新的分销记录
            distribute = saveDistributeRecord(event);
        }
        
        MemberConsumptionDistributeDTO distributeDTO = BeanUtil.copyProperties(event, MemberConsumptionDistributeDTO.class);
        distributeDTO.setNewConsumptionGuid(event.getNewConsumptionGuid());
        distributeDTO.setDistributeRecordType(DistributeTypeEnum.ALL_REFUND);
        distributeDTO.setRefundType(NumberConstant.NUMBER_1);

        // 打印请求参数日志
        log.info("调用s2b2cMallFeign.orderRefundDistribute开始（全额退款），订单号：{}, 请求参数：{}",
                event.getOrderNumber(), JacksonUtils.writeValueAsString(distributeDTO));
        
        try {
            Result<Void> result = s2b2cMallFeign.orderRefundDistribute(distributeDTO);
            
            // 打印响应结果日志
            log.info("调用s2b2cMallFeign.orderRefundDistribute成功（全额退款），订单号：{}, 响应结果：{}",
                    event.getOrderNumber(), JacksonUtils.writeValueAsString(result));
            
            // 更新推送完成状态
            updateDistributeCompleteStatus(distribute.getGuid(), true);
            log.info("更新分销记录推送状态为已完成（全额退款），订单号：{}, 记录GUID：{}", 
                    event.getOrderNumber(), distribute.getGuid());
                    
        } catch (Exception e) {
            log.error("调用s2b2cMallFeign.orderRefundDistribute异常（全额退款），订单号：{}, 异常信息：{}",
                    event.getOrderNumber(), e.getMessage(), e);
            // 更新推送失败状态
            updateDistributeCompleteStatus(distribute.getGuid(), false);
            throw e;
        }
        
        log.info("整单退款分销记录处理完成，订单号：{}", event.getOrderNumber());
    }

    /**
     * 处理部分退款的分销记录
     */
    private void handlePartialRefundDistribute(SendMemberConsumptionDistributeEvent event) {
        log.info("处理部分退款分销记录，订单号：{}", event.getOrderNumber());
        
        // 检查是否已经存在相同newConsumptionGuid的记录
        HsaMemberConsumptionDistribute existingDistribute = checkExistingDistribute(event.getNewConsumptionGuid(), DistributeTypeEnum.PART_REFUND);
        
        HsaMemberConsumptionDistribute distribute;
        if (existingDistribute != null) {
            if (existingDistribute.getIsComplete() != null && existingDistribute.getIsComplete() == 1) {
                log.warn("部分退款消费记录已经推送完成，跳过处理，订单号：{}, 新消费GUID：{}", 
                        event.getOrderNumber(), event.getNewConsumptionGuid());
                return;
            } else {
                log.info("发现未推送的部分退款分销记录，直接使用现有记录进行推送，订单号：{}, 记录GUID：{}", 
                        event.getOrderNumber(), existingDistribute.getGuid());
                distribute = existingDistribute;
            }
        } else {
            // 保存新的分销记录
            distribute = saveDistributeRecord(event);
        }
        
        MemberConsumptionDistributeDTO distributeDTO = BeanUtil.copyProperties(event, MemberConsumptionDistributeDTO.class);
        distributeDTO.setNewConsumptionGuid(event.getNewConsumptionGuid());
        distributeDTO.setDistributeRecordType(DistributeTypeEnum.PART_REFUND);
        distributeDTO.setRefundType(NumberConstant.NUMBER_0);

        // 打印请求参数日志
        log.info("调用s2b2cMallFeign.orderRefundDistribute开始（部分退款），订单号：{}, 请求参数：{}",
                event.getOrderNumber(), JacksonUtils.writeValueAsString(distributeDTO));
        
        try {
            Result<Void> result = s2b2cMallFeign.orderRefundDistribute(distributeDTO);
            
            // 打印响应结果日志
            log.info("调用s2b2cMallFeign.orderRefundDistribute成功（部分退款），订单号：{}, 响应结果：{}",
                    event.getOrderNumber(), JacksonUtils.writeValueAsString(result));
            
            // 更新推送完成状态
            updateDistributeCompleteStatus(distribute.getGuid(), true);
            log.info("更新分销记录推送状态为已完成（部分退款），订单号：{}, 记录GUID：{}", 
                    event.getOrderNumber(), distribute.getGuid());
                    
        } catch (Exception e) {
            log.error("调用s2b2cMallFeign.orderRefundDistribute异常（部分退款），订单号：{}, 异常信息：{}",
                    event.getOrderNumber(), e.getMessage(), e);
            // 更新推送失败状态
            updateDistributeCompleteStatus(distribute.getGuid(), false);
            throw e;
        }
        
        log.info("部分退款分销记录处理完成，订单号：{}", event.getOrderNumber());
    }

    /**
     * 保存分销记录
     */
    private HsaMemberConsumptionDistribute saveDistributeRecord(SendMemberConsumptionDistributeEvent event) {
        HsaMemberConsumptionDistribute distribute = new HsaMemberConsumptionDistribute();
        distribute.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberConsumptionDistribute.class.getSimpleName()));
        
        // 如果是退款的分销消息（全额退款或部分退款），使用newConsumptionGuid作为ConsumptionGuid
        // 如果是非退款的分销消息，使用consumptionGuid
        if (event.getDistributeRecordType() == DistributeTypeEnum.ALL_REFUND || 
            event.getDistributeRecordType() == DistributeTypeEnum.PART_REFUND) {
            distribute.setConsumptionGuid(event.getNewConsumptionGuid());
            log.info("退款分销记录，使用newConsumptionGuid：{}", event.getNewConsumptionGuid());
        } else {
            distribute.setConsumptionGuid(event.getConsumptionGuid());
            log.info("非退款分销记录，使用consumptionGuid：{}", event.getConsumptionGuid());
        }
        
        distribute.setOrderNumber(event.getOrderNumber());
        distribute.setOperSubjectGuid(event.getOperSubjectGuid());
        distribute.setOperatorTelName(event.getOperatorTelName());
        distribute.setDistributeRecordType(event.getDistributeRecordType().getCode());
        // 初始状态为未完成
        distribute.setIsComplete(NumberConstant.NUMBER_0);
        
        int result = baseMapper.insert(distribute);
        if (result > NumberConstant.NUMBER_0) {
            log.info("分销记录保存成功，订单号：{}, 记录GUID：{}, 消费GUID：{}, 分销类型：{}, 推送状态：未完成", 
                    event.getOrderNumber(), distribute.getGuid(), distribute.getConsumptionGuid(), 
                    event.getDistributeRecordType());
            return distribute;
        } else {
            log.error("分销记录保存失败，订单号：{}", event.getOrderNumber());
            throw new BusinessException("分销记录保存失败");
        }
    }

    /**
     * 检查是否已存在全额退款的分销记录
     */
    private boolean checkExistsFullRefundRecord(String orderNumber, String operSubjectGuid) {
        LambdaQueryWrapper<HsaMemberConsumptionDistribute> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HsaMemberConsumptionDistribute::getOrderNumber, orderNumber)
                   .eq(HsaMemberConsumptionDistribute::getOperSubjectGuid, operSubjectGuid)
                   .eq(HsaMemberConsumptionDistribute::getDistributeRecordType, DistributeTypeEnum.ALL_REFUND.getCode())
                   .eq(HsaMemberConsumptionDistribute::getIsDelete, Boolean.FALSE);
        
        Integer count = baseMapper.selectCount(queryWrapper);
        boolean exists = count != null && count > 0;
        
        log.info("检查全额退款记录，订单号：{}, 运营主体：{}, 存在记录数：{}", orderNumber, operSubjectGuid, count);
        
        return exists;
    }

    /**
     * 检查是否已经存在相同consumptionGuid的分销记录
     */
    private HsaMemberConsumptionDistribute checkExistingDistribute(String consumptionGuid, DistributeTypeEnum distributeType) {
        LambdaQueryWrapper<HsaMemberConsumptionDistribute> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HsaMemberConsumptionDistribute::getConsumptionGuid, consumptionGuid)
                   .eq(HsaMemberConsumptionDistribute::getDistributeRecordType, distributeType.getCode())
                   .eq(HsaMemberConsumptionDistribute::getIsDelete, Boolean.FALSE)
                   .orderByDesc(HsaMemberConsumptionDistribute::getGmtCreate)
                   .last("LIMIT 1");
        
        HsaMemberConsumptionDistribute distribute = baseMapper.selectOne(queryWrapper);
        
        if (distribute != null) {
            log.info("找到现有分销记录，消费GUID：{}, 记录GUID：{}, 推送状态：{}", 
                    consumptionGuid, distribute.getGuid(), 
                    distribute.getIsComplete() == 1 ? "已完成" : "未完成");
        } else {
            log.info("未找到现有分销记录，消费GUID：{}", consumptionGuid);
        }
        
        return distribute;
    }

    /**
     * 更新分销记录推送状态
     */
    private void updateDistributeCompleteStatus(String guid, boolean completed) {
        LambdaQueryWrapper<HsaMemberConsumptionDistribute> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HsaMemberConsumptionDistribute::getGuid, guid)
                   .eq(HsaMemberConsumptionDistribute::getIsDelete, Boolean.FALSE);
        
        HsaMemberConsumptionDistribute distribute = new HsaMemberConsumptionDistribute();
        distribute.setIsComplete(completed ? NumberConstant.NUMBER_1 : NumberConstant.NUMBER_0);
        
        int result = baseMapper.update(distribute, queryWrapper);
        if (result > 0) {
            log.info("更新分销记录推送状态成功，记录GUID：{}, 推送状态：{}", guid, completed ? "已完成" : "未完成");
        } else {
            log.error("更新分销记录推送状态失败，记录GUID：{}", guid);
            throw new BusinessException("更新分销记录推送状态失败");
        }
    }

} 