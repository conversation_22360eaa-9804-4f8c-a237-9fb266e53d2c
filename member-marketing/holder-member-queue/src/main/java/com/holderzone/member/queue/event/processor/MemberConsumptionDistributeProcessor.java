package com.holderzone.member.queue.event.processor;

import com.google.common.base.Verify;
import com.holderzone.member.common.dto.event.SendMemberConsumptionDistributeEvent;
import com.holderzone.member.common.util.transaction.SpringContextUtils;
import com.holderzone.member.queue.service.member.HsaMemberConsumptionDistributeService;
import lombok.extern.slf4j.Slf4j;

/**
 * 会员消费分销记录处理器
 *
 * <AUTHOR>
 * @version  2025/06/04
 */
@Slf4j
public class MemberConsumptionDistributeProcessor extends AbstractProcessor<SendMemberConsumptionDistributeEvent> {

    private final HsaMemberConsumptionDistributeService hsaMemberConsumptionDistributeService;

    public MemberConsumptionDistributeProcessor(SendMemberConsumptionDistributeEvent event) {
        super(event);
        hsaMemberConsumptionDistributeService = SpringContextUtils.getBean(HsaMemberConsumptionDistributeService.class);
    }

    @Override
    protected String getLogTitle() {
        return "SendMemberConsumptionDistributeEvent";
    }

    @Override
    protected void validate() {
        Verify.verify(event != null);
        Verify.verify(event.getOperSubjectGuid() != null, "运营主体不能为空");
        Verify.verify(event.getOrderNumber() != null, "订单号不能为空");
        Verify.verify(event.getDistributeRecordType() != null, "分销记录类型不能为空");
    }

    @Override
    protected void process() {
        log.info("开始处理会员消费分销记录，订单号：{}, 分销类型：{}", 
                event.getOrderNumber(), event.getDistributeRecordType());
        
        hsaMemberConsumptionDistributeService.processDistributeRecord(event);
        
        log.info("会员消费分销记录处理完成，订单号：{}", event.getOrderNumber());
    }
} 