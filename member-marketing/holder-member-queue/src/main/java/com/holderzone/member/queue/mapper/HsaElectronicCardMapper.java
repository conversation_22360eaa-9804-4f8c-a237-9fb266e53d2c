package com.holderzone.member.queue.mapper;



import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.queue.entity.HsaElectronicCard;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface HsaElectronicCardMapper extends HolderBaseMapper<HsaElectronicCard> {
    /**
     * 批量更新状态
     * @param activityList 活动列表
     */
    void batchUpdateStatus(@Param("list") List<HsaElectronicCard> activityList);

}
