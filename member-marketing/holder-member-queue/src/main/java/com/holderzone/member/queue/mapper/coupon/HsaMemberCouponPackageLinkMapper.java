package com.holderzone.member.queue.mapper.coupon;

import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponPackageLink;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 会员券包发放 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
public interface HsaMemberCouponPackageLinkMapper extends HolderBaseMapper<HsaMemberCouponPackageLink> {

    List<String> queueMemberGuidByGuid(@Param("guid") String guid);
}
