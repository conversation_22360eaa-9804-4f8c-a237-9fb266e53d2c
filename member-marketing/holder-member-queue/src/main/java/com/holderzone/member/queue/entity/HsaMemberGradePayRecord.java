package com.holderzone.member.queue.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2023/4/20 下午6:56
 * @description 会员等级付费记录表
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "HsaMemberGradePayRecord对象", description = "会员等级付费记录表")
public class HsaMemberGradePayRecord implements Serializable {

    private static final long serialVersionUID = -6829499357064953454L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "运营主体GUID")
    private String operSubjectGuid;

    @ApiModelProperty(value = "guid")
    private String guid;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @TableLogic
    @ApiModelProperty(value = "是否删除,0未删除,1已删除")
    private Boolean isDelete;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "会员GUID")
    private String memberInfoGuid;

    @ApiModelProperty(value = "会员名称")
    private String memberInfoName;

    /**
     * @see com.holderzone.member.common.enums.mall.order.PayStateEnum
     */
    private Integer state;

    @ApiModelProperty(value = "会员手机号")
    private String phoneNum;

    @ApiModelProperty(value = "会员等级名称")
    private String memberInfoGradeName;

    @ApiModelProperty(value = "会员等级GUID")
    private String memberInfoGradeGuid;

    @ApiModelProperty(value = "付费时间")
    private LocalDateTime payTime;

    /**
     * 角色类型
     *
     * @see com.holderzone.member.common.enums.member.RoleTypeEnum
     */
    @ApiModelProperty(value = "角色类型")
    private String roleType;

    @ApiModelProperty(value = "到期时间")
    private LocalDateTime expireTime;

    @ApiModelProperty(value = "有效数量")
    private Integer num;

    /**
     * 变动类型
     *
     * @see com.holderzone.member.common.enums.grade.GradeRecordChangeEnum
     */
    @ApiModelProperty(value = "变动类型")
    private Integer changeType;

    @ApiModelProperty(value = "有效期单位：3月 4年")
    private Integer unit;

    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 门店名
     */
    private String storeName;

    /**
     * 付费金额
     */
    private BigDecimal payAmount;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 银行流水号
     */
    private String bankTransactionId;

    /**
     * 聚合支付单号
     */
    private String orderHolderNo;

}
