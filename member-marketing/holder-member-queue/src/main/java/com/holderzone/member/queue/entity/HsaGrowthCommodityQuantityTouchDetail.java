package com.holderzone.member.queue.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 成长值任务商品数量累计赠送
 *
 * <AUTHOR>
 * @since 2021-12-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaGrowthCommodityQuantityTouchDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * guid
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * growthValueTaskGuid
     */
    private String growthValueTaskGuid;

    /**
     * memberInfoGuid
     */
    private String memberInfoGuid;

    /**
     * 消费记录GUID
     */
    private String memberConsumptionGuid;

    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 商品id
     */
    private String commodityId;

    /**
     * 商品名称
     */
    private String commodityName;

    /**
     * 商品数量
     */
    private Integer commodityNum;

    private String commodityCode;

    /**
     * 策略单id
     */
    private String strategyId;

    /**
     * 订单消费时间
     */
    private LocalDateTime orderTime;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

}
