package com.holderzone.member.queue.mapper;


import com.holderzone.member.queue.config.mybatis.HolderBaseMapper;
import com.holderzone.member.queue.entity.HsaMemberLabel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ProjectName: member-marketing
 * @ClassName: HsaMemberLabelMapper
 * @Author: pantao
 * @Description:
 * @Date: 2021/8/24 20:15
 * @Version: 1.0
 */
public interface HsaMemberLabelMapper extends HolderBaseMapper<HsaMemberLabel> {

    void batchSave(@Param("hsaMemberLabels") List<HsaMemberLabel> hsaMemberLabels);

}
