package com.holderzone.member.queue.mapper;


import com.holderzone.member.common.dto.label.RequestLabelQuery;
import com.holderzone.member.queue.config.mybatis.HolderBaseMapper;
import com.holderzone.member.queue.entity.HsaGrowthValueDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 会员卡基础信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-12
 */
public interface HsaGrowthValueDetailMapper extends HolderBaseMapper<HsaGrowthValueDetail> {

//    List<HsaGrowthValueDetail> getHsaGrowthValueDetailByTime(@Param(value = "memberInfoGuid") String memberInfoGuid);

    void batchUpdate(@Param("list") List<HsaGrowthValueDetail> activityList);

    List<String> findRelationLabelMemberGuid(@Param("query") RequestLabelQuery query);

}
