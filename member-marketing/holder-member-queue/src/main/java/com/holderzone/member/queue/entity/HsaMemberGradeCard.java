package com.holderzone.member.queue.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.member.common.base.HsaBaseEntity;
import com.holderzone.member.common.enums.grade.GradeCardOpenTypeEnum;

import java.io.Serializable;
import java.time.LocalDate;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户等级卡
 * <AUTHOR>
 * @TableName hsa_member_grade_card
 */

@Data
@Accessors (chain = true)
@EqualsAndHashCode (callSuper = true)
@TableName ("hsa_member_grade_card")
public class HsaMemberGradeCard extends HsaBaseEntity implements Serializable {
    private static final long serialVersionUID = -3754343888860868571L;
    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 会员级别id
     */
    private String gradeGuid;

    /**
     * 用户id
     */
    private String memberInfoGuid;

    /**
     * 会员等级
     */
    private Integer vipGrade;

    /**
     * 会员类型:0免费，1付费
     */
    private Integer gradeType;

    /**
     * 会员卡有效时间
     */
    private LocalDate memberCardValidTime;

    /**
     * 会员卡状态
     * @see com.holderzone.member.common.enums.grade.GradeCardStatusEnum
     */
    private Integer memberCardStatus;

    /**
     * 开卡方式
     * @see GradeCardOpenTypeEnum
     */
    private Integer openType;

    /**
     * 乐观锁版本号
     */
    private Integer version;

}