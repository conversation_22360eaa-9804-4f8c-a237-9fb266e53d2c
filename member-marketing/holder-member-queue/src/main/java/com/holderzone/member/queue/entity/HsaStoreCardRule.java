package com.holderzone.member.queue.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员卡门店适用表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaStoreCardRule implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * guid
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * storeGuid
     */
    private String storeGuid;

    /**
     * storeName
     */
    private String storeName;

    private String storeNumber;

    /**
     * 营业时间
     */
    private String time;

    /**
     * 营业地址
     */
    private String address;

    /**
     * cardGuid
     */
    private String cardGuid;

    private String parentGuid;

    private String memberInfoCardGuid;


    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    /**
     * 经纬度
     */
    private String addressPoint;

}
