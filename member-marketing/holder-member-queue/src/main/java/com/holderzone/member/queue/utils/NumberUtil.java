package com.holderzone.member.queue.utils;

import com.holderzone.framework.util.StringUtils;

import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2019/6/13 15:28
 */
public class NumberUtil {
    static final Pattern p = Pattern.compile("^((13[0-9])|(15[^4,\\D])|(18[0-9]))\\d{8}$");

    /**
     * 随机生成6位纯数字短信验证码
     *
     * @return
     */
    public static String buildMessageCode() {
        Random random = new Random();
        String code = null;
        int randomNum = random.nextInt(1000000);
        code = String.format("%06d", randomNum);
        return code;
    }


    /**
     * 生成自定义位数的纯数字字符串
     *
     * @param count
     * @return
     */
    public synchronized static String buildNumToStr(int count) {
        String val = "";
        Random random = new Random();
        for (int i = 0; i < count; i++) {
            val += String.valueOf(random.nextInt(10));
        }
        return val;
    }

    public static boolean isPhoneNum(String phone) {
        String regex = "^^(?:\\+?86)?1(?:3\\d{3}|5[^4\\D]\\d{2}|8\\d{3}|7(?:[35678]\\d{2}|4(?:0\\d|1[0-2]|9\\d))|9[189]\\d{2}|66\\d{2})\\d{6}$";
        if (phone.length() != 11) {
            return false;
        } else {
            Pattern p = Pattern.compile(regex);
            Matcher m = p.matcher(phone);
            boolean isMatch = m.matches();
            return isMatch;
        }
    }

    public static boolean isPhoneNum11(String phone) {
        if (StringUtils.hasText(phone)) {
            String regex = "^1[\\d]{10}";
            return Pattern.matches(regex, phone);
        }
        return false;
    }

}
