package com.holderzone.member.queue.mapper;


import com.holderzone.member.queue.config.mybatis.HolderBaseMapper;
import com.holderzone.member.queue.entity.HsaMemberInfoCard;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 */
public interface HsaMemberInfoCardMapper extends HolderBaseMapper<HsaMemberInfoCard> {
    /**
     * 批量卡状态状态
     *
     * @param activityList 活动列表
     */
    void batchUpdateStatus(@Param("list") Set<HsaMemberInfoCard> activityList, @Param("cardState") Integer cardState);

    /**
     * 批量更新
     * @param activityList 活动列表
     */
    void batchUpdate(@Param("list") List<HsaMemberInfoCard> activityList);
}
