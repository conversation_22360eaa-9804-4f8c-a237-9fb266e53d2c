package com.holderzone.member.queue.service.grade.impl;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.beust.jcommander.internal.Maps;
import com.google.common.collect.Lists;
import com.holderzone.member.common.annotation.RedissonLock;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.RedisKeyConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.event.SendMemberGradeChangeEvent;
import com.holderzone.member.common.dto.grade.MemberGradeRelationDTO;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.equities.EquitiesRuleTypeEnum;
import com.holderzone.member.common.enums.grade.GiftBagEquitiesRuleEnum;
import com.holderzone.member.common.enums.grade.GradeRecordChangeEnum;
import com.holderzone.member.common.enums.grade.UpgradeTypeEnum;
import com.holderzone.member.common.enums.growth.DataUnitEnum;
import com.holderzone.member.common.enums.growth.SumValueChangeEnum;
import com.holderzone.member.common.enums.member.*;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.verify.ObjectUtil;
import com.holderzone.member.common.util.verify.VerifyUtil;
import com.holderzone.member.common.vo.grade.MemberGradePayRecordReqVO;
import com.holderzone.member.common.vo.growth.MemberGrowthValueRelationVO;
import com.holderzone.member.queue.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.queue.dto.grade.GradeRightsProcessorDTO;
import com.holderzone.member.queue.dto.grade.MemberGradeChangeDTO;
import com.holderzone.member.queue.dto.member.UpdateIntegral;
import com.holderzone.member.queue.entity.*;
import com.holderzone.member.queue.event.processor.MemberGradeChangeProcessor;
import com.holderzone.member.queue.mapper.*;
import com.holderzone.member.queue.service.assembler.GradeChangeAssembler;
import com.holderzone.member.queue.service.cache.CacheService;
import com.holderzone.member.queue.service.grade.*;
import com.holderzone.member.queue.service.member.HsaGrowthValueDetailService;
import com.holderzone.member.queue.service.member.HsaIntegralDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 会员等级变化明细表
 *
 * <AUTHOR>
 * @since 2022-01-04
 */
@Service
@Slf4j
public class HsaMemberGradeChangeDetailServiceImpl extends HolderBaseServiceImpl<HsaMemberGradeChangeDetailMapper, HsaMemberGradeChangeDetail>
        implements HsaMemberGradeChangeDetailService {

    public static final String NO_GRADE = "-1";

    public static final int BATH_SAVE_SIZE = 1000;

    @Resource
    private HsaGradeGiftBagMapper hsaGradeGiftBagMapper;

    @Resource
    private HsaMemberGradeChangeDetailMapper hsaMemberGradeChangeDetailMapper;

    @Resource
    private HsaMemberGradeInfoMapper hsaMemberGradeInfoMapper;

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Resource
    private HsaMemberGradeRightsRecordMapper hsaMemberGradeRightsRecordMapper;

    @Resource
    private HsaGiftBagBaseInfoMapper hsaGiftBagBaseInfoMapper;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Autowired
    @Lazy
    private HsaMemberGradeChangeDetailService hsaMemberGradeChangeDetailService;

    @Resource
    private HsaMemberGradeCardService hsaMemberGradeCardService;

    @Resource
    private HsaMemberGradeRightsRecordService hsaMemberGradeRightsRecordService;

    @Resource
    private HsaGrowthValueDetailService hsaGrowthValueDetailService;

    @Resource
    private HsaIntegralDetailService hsaIntegralDetailService;

    @Resource
    private HsaControlledGradeStateMapper hsaControlledGradeStateMapper;

    @Resource
    private Executor memberQueueThreadExecutor;

    @Resource
    private CacheService cacheService;

    @Resource
    private HsaGradeEquitiesService hsaGradeEquitiesService;

    @Resource
    private HsaGradeEquitiesMapper hsaGradeEquitiesMapper;

    @Resource
    private HsaMemberEquitiesReceiveRecordMapper hsaMemberEquitiesReceiveRecordMapper;

    @Resource
    private HsaMemberEquitiesReceiveRecordServiceImpl hsaMemberEquitiesReceiveRecordService;

    @Resource
    private IHsaMemberGradeRelationService memberGradeRelationService;

    @Resource
    private IHsaMemberGradePayRecordService memberGradePayRecordService;

    @Resource
    private GradeChangeAssembler gradeChangeAssembler;

    /**
     * 会员等级变化
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedissonLock(lockName = "MEMBER_GRADE_CHANGE", tryLock = true, leaseTime = 50)
    public void memberGradeChange(MemberGradeChangeDTO gradeChangeDTO) {
        try {
            GradeRightsProcessorDTO gradeRightsProcessorDTO = new GradeRightsProcessorDTO();
            VerifyUtil.isTure(ObjectUtils.isEmpty(gradeChangeDTO.getRoleType()))
                    .trueHandle(() -> gradeChangeDTO.setRoleType(RoleTypeEnum.MEMBER.name()));

            HsaControlledGradeState hsaControlledGradeState = hsaControlledGradeStateMapper.selectOne(
                    new LambdaQueryWrapper<HsaControlledGradeState>()
                            .eq(HsaControlledGradeState::getRoleType, gradeChangeDTO.getRoleType())
                            .eq(HsaControlledGradeState::getOperSubjectGuid, gradeChangeDTO.getOperSubjectGuid())
                            .eq(HsaControlledGradeState::getIsDelete, BooleanEnum.FALSE.getCode())
            );
            if (hsaControlledGradeState.getState() == EnableEnum.NOT_ENABLE.getCode()) {
                log.info("会员等级体系已关闭：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", hsaControlledGradeState);
                return;
            }

            //不要打印会员了，5w以上 oom
            log.info("会员数量：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", gradeChangeDTO.getHsaOperationMemberInfos().size());

            //获取主体下等级信息  当前生效的等级
            List<HsaMemberGradeInfo> currentMemberGradeInfoList = getHsaMemberGradeInfos(gradeChangeDTO.getOperSubjectGuid(), gradeChangeDTO.getRoleType());
            log.info("获取主体下等级信息：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", currentMemberGradeInfoList);
            //获取会员等级信息  历史等级
            Map<String, HsaMemberGradeInfo> oldMemberGradeInfoMap = getStringHsaMemberGradeInfoMap(
                    gradeChangeDTO.getHsaOperationMemberInfos(), gradeChangeDTO.getRoleType());
            log.info("获取会员等级信息：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", oldMemberGradeInfoMap);
            // 更新会员等级卡
            List<HsaMemberGradeCard> hsaMemberGradeCards = Lists.newArrayList();
            //持久化等级变化
            List<HsaMemberGradeChangeDetail> hsaMemberGradeChangeDetails = Lists.newArrayList();
            //持久化会员等级
            List<HsaOperationMemberInfo> hsaOperationMemberInfos = Lists.newArrayList();
            //更新会员等级
            List<HsaOperationMemberInfo> updateOperationMemberInfos = Lists.newArrayList();
            //持久化权益记录
            gradeRightsProcessorDTO.setHsaMemberGradeRightsRecords(Lists.newArrayList());
            //会员成长值变更记录
            gradeRightsProcessorDTO.setHsaGrowthValueDetails(Lists.newArrayList());
            //会员积分变更记录
            gradeRightsProcessorDTO.setHsaIntegralDetails(Lists.newArrayList());
            //当前运营主体所有会员等级权益信息
            Map<String, List<HsaBusinessEquities>> hsaGradeEquitiesMap = getGradeEquitiesMap(gradeChangeDTO.getOperSubjectGuid());
            //当前变更会员，已经领取等级权益记录信息
            Map<String, List<HsaMemberGradeRightsRecord>> equitiesRightsRecordMap = getEquitiesRightsRecordMap(
                    gradeChangeDTO.getOperSubjectGuid(), gradeChangeDTO.getHsaOperationMemberInfos(), gradeChangeDTO.getRoleType());
            //记录会员已经使用的权益信息
            List<HsaMemberEquitiesReceiveRecord> hsaMemberEquitiesReceiveRecords = new ArrayList<>();
            //周期赠送 一次性赠送成长值的权益
            List<HsaBusinessEquities> hsaBusinessEquitiesList = hsaGradeEquitiesMapper
                    .selectList(new LambdaQueryWrapper<HsaBusinessEquities>()
                            .eq(HsaBusinessEquities::getSetPeriod, DataUnitEnum.FOREVER.getCode())
                            .in(HsaBusinessEquities::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                            .eq(HsaBusinessEquities::getEffective, BooleanEnum.TRUE.getCode())
                            .eq(HsaBusinessEquities::getOperSubjectGuid, gradeChangeDTO.getOperSubjectGuid()));
            Map<String, List<HsaBusinessEquities>> equitiesMap = hsaBusinessEquitiesList.stream().collect(Collectors.
                    groupingBy(HsaBusinessEquities::getMemberGradeInfoGuid));
            HsaGiftBagBaseInfo hsaGiftBagBaseInfo = hsaGiftBagBaseInfoMapper.selectOne(new LambdaQueryWrapper<HsaGiftBagBaseInfo>()
                    .eq(HsaGiftBagBaseInfo::getOperSubjectGuid, gradeChangeDTO.getOperSubjectGuid()));
            //会议老等级Map
            Set<HsaMemberGradeInfo> hsaMemberGradeInfos = new HashSet<>();
            LocalDateTime now = LocalDateTime.now();


            //会员等级更新
            List<String> memberGuidList = gradeChangeDTO.getHsaOperationMemberInfos().stream()
                    .map(HsaOperationMemberInfo::getGuid)
                    .distinct()
                    .collect(Collectors.toList());
            //成长值等级
            List<HsaMemberGradeInfo> growthGradeList = currentMemberGradeInfoList.stream()
                    .filter(grade -> grade.getUpgradeType() == UpgradeTypeEnum.GROWTH_VALUE.getCode())
                    .collect(Collectors.toList());

            for (HsaOperationMemberInfo hsaOperationMemberInfo : gradeChangeDTO.getHsaOperationMemberInfos()) {
                //成长值所在等级
                HsaMemberGradeInfo nowMemberGradeInfo = calculateExtraAmount(hsaOperationMemberInfo.getMemberGrowthValue(), growthGradeList);
                String memberGradeInfoGuid = hsaOperationMemberInfo.getMemberGradeInfoGuid();
                //处理等级变化
                if (Objects.nonNull(gradeChangeDTO.getIssuerType()) || (!ObjectUtils.isEmpty(nowMemberGradeInfo) && !nowMemberGradeInfo.getGuid().equals(memberGradeInfoGuid))) {
                    HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail = new HsaMemberGradeChangeDetail();
                    HsaMemberGradeInfo beforeMemberGradeInfo = oldMemberGradeInfoMap.get(hsaOperationMemberInfo.getMemberGradeInfoGuid());
                    //等级变更
                    gradeChangeBusiness(hsaOperationMemberInfo, nowMemberGradeInfo, beforeMemberGradeInfo, hsaMemberGradeChangeDetail, now);
                    hsaMemberGradeChangeDetails.add(hsaMemberGradeChangeDetail);

                    //等级卡变更
                    HsaMemberGradeCard hsaMemberGradeCard = new HsaMemberGradeCard();
                    gradeCardChange(hsaOperationMemberInfo, nowMemberGradeInfo, hsaMemberGradeCard);
                    hsaMemberGradeCards.add(hsaMemberGradeCard);

                    // 更新会员积分
                    Map<String, Integer> updateMemberIntegralMap = Maps.newHashMap();
                    gradeRightsProcessorDTO.setUpdateMemberIntegralMap(updateMemberIntegralMap);
                    gradeRightsProcessorDTO.setNow(now);
                    gradeRightsProcessorDTO.setHsaGiftBagBaseInfo(hsaGiftBagBaseInfo);
                    gradeRightsProcessorDTO.setOperSubjectGuid(gradeChangeDTO.getOperSubjectGuid());
                    gradeRightsProcessorDTO.setSourceType(gradeChangeDTO.getSourceType());
                    //等级变更礼包权益校验发放
                    memberGradeRightsProcessor(
                            gradeRightsProcessorDTO,
                            hsaOperationMemberInfo,//会员信息
                            beforeMemberGradeInfo,//变更前等级
                            nowMemberGradeInfo,//变更后等级
                            currentMemberGradeInfoList
                    );

                    List<HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecords = gradeRightsProcessorDTO.getHsaMemberGradeRightsRecords();
                    //todo 等级权益待定
                    String afterGradeGuid = nowMemberGradeInfo.getGuid();
                    VerifyUtil.isTure(Objects.nonNull(hsaGradeEquitiesMap) &&
                            hsaGradeEquitiesMap.containsKey(afterGradeGuid)).trueHandle(() -> {
                        //当前变更会员等级所拥有的权益信息
                        List<HsaBusinessEquities> hsaBusinessEquities = hsaGradeEquitiesMap.get(afterGradeGuid);
                        List<HsaMemberGradeRightsRecord> equitiesRightsRecords = getMemberEquitiesRecourd(
                                afterGradeGuid, equitiesRightsRecordMap);
                        //等级变更给其他权益发放
                        memberGradeRightsProcessor(hsaOperationMemberInfo, hsaBusinessEquities, equitiesRightsRecords,
                                gradeChangeDTO.getOperSubjectGuid(), hsaMemberGradeRightsRecords, now);
                    });
                    hsaOperationMemberInfos.add(hsaOperationMemberInfo);
                    hsaMemberGradeInfos.add(nowMemberGradeInfo);
                } else {
                    // 等级不变化，只调整过期时间
                    hsaOperationMemberInfo.setMemberGradeInfoGuid(Optional.ofNullable(nowMemberGradeInfo)
                            .map(HsaMemberGradeInfo::getGuid).orElse(null));
                    hsaOperationMemberInfo.setMemberGradeInfoName(Optional.ofNullable(nowMemberGradeInfo)
                            .map(HsaMemberGradeInfo::getName).orElse(null));
                    updateOperationMemberInfos.add(hsaOperationMemberInfo);
                }
            }

            List<HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecords = gradeRightsProcessorDTO.getHsaMemberGradeRightsRecords();

            List<HsaGrowthValueDetail> hsaGrowthValueDetails = gradeRightsProcessorDTO.getHsaGrowthValueDetails();

            List<HsaIntegralDetail> hsaIntegralDetails = gradeRightsProcessorDTO.getHsaIntegralDetails();

            Map<String, Integer> updateMemberIntegralMap = gradeRightsProcessorDTO.getUpdateMemberIntegralMap();

            VerifyUtil.isTure(CollUtil.isNotEmpty(hsaMemberGradeCards))
                    .trueHandle(() -> hsaMemberGradeCardService.saveBatch(hsaMemberGradeCards, BATH_SAVE_SIZE));

            VerifyUtil.isTure(CollUtil.isNotEmpty(hsaMemberGradeChangeDetails))
                    .trueHandle(() -> hsaMemberGradeChangeDetailService.saveBatch(hsaMemberGradeChangeDetails, BATH_SAVE_SIZE));

            VerifyUtil.isTure(CollUtil.isNotEmpty(hsaMemberGradeRightsRecords))
                    .trueHandle(() -> hsaMemberGradeRightsRecordService.saveBatch(hsaMemberGradeRightsRecords, BATH_SAVE_SIZE));

            VerifyUtil.isTure(CollUtil.isNotEmpty(hsaGrowthValueDetails))
                    .trueHandle(() -> hsaGrowthValueDetailService.saveBatch(hsaGrowthValueDetails, BATH_SAVE_SIZE));

            VerifyUtil.isTure(CollUtil.isNotEmpty(hsaIntegralDetails))
                    .trueHandle(() -> hsaIntegralDetailService.saveBatch(hsaIntegralDetails, BATH_SAVE_SIZE));

            VerifyUtil.isTure(CollUtil.isNotEmpty(updateOperationMemberInfos)).trueHandle(() -> {
                DealingWithGradeInterests(updateOperationMemberInfos, hsaMemberEquitiesReceiveRecords, equitiesMap,
                        hsaMemberGradeInfos, updateMemberIntegralMap, gradeChangeDTO);
                log.info("变更成长值会员hsaOperationMemberInfos.length：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", hsaOperationMemberInfos.size());
                hsaOperationMemberInfoMapper.batchUpdateGrowth(updateOperationMemberInfos);
            });

            VerifyUtil.isTureOrFalse(CollUtil.isNotEmpty(hsaOperationMemberInfos)).trueOrFalseHandle(() -> {
                partnerGradeHandle(gradeChangeDTO, hsaOperationMemberInfos, memberGuidList);
                DealingWithGradeInterests(hsaOperationMemberInfos, hsaMemberEquitiesReceiveRecords, equitiesMap,
                        hsaMemberGradeInfos, updateMemberIntegralMap, gradeChangeDTO);
                log.info("变更成长值会员hsaOperationMemberInfos.length：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", hsaOperationMemberInfos.size());
                hsaOperationMemberInfoMapper.batchUpdateGrowth(hsaOperationMemberInfos);
                //监听会员等级变化
                monitorGradeChange(gradeChangeDTO, hsaOperationMemberInfos, currentMemberGradeInfoList);
            }, () -> checkGradeRefresh(gradeChangeDTO.getOperSubjectGuid(), gradeChangeDTO.getIsRefresh(), gradeChangeDTO.getRoleType()));

            // 批量更新会员增加的积分值
            batchUpdateIntegral(updateMemberIntegralMap);
        } catch (Exception e) {
            checkGradeRefresh(gradeChangeDTO.getOperSubjectGuid(), gradeChangeDTO.getIsRefresh(), gradeChangeDTO.getRoleType());
            e.printStackTrace();
            log.info("等级刷新失败强制释放：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }

    }

    private void gradeCardChange(HsaOperationMemberInfo hsaOperationMemberInfo,
                                 HsaMemberGradeInfo nowMemberGradeInfo,
                                 HsaMemberGradeCard hsaMemberGradeCard) {
        gradeChangeAssembler.getHsaMemberGradeCard(hsaOperationMemberInfo, nowMemberGradeInfo, hsaMemberGradeCard);
    }

    private void batchUpdateIntegral(Map<String, Integer> updateMemberIntegralMap) {
        if (!updateMemberIntegralMap.isEmpty()) {
            List<UpdateIntegral> updateMemberIntegralList = Lists.newArrayList();
            updateMemberIntegralMap.forEach((k, v) -> {
                UpdateIntegral updateIntegral = new UpdateIntegral();
                updateIntegral.setGuid(k);
                updateIntegral.setIntegralValue(v);
                updateMemberIntegralList.add(updateIntegral);
            });
            hsaOperationMemberInfoMapper.batchUpdateIntegral(updateMemberIntegralList);
        }
    }

    private void partnerGradeHandle(MemberGradeChangeDTO gradeChangeDTO, List<HsaOperationMemberInfo> hsaOperationMemberInfos, List<String> memberGuidList) {
        if (!ObjectUtils.isEmpty(gradeChangeDTO.getSystem()) && SystemEnum.PARTNER.getCode() == gradeChangeDTO.getSystem()
                && !StringUtils.isEmpty(gradeChangeDTO.getMemberInfoGradeGuid())) {
            // 无等级
            if (Objects.equals(NO_GRADE, gradeChangeDTO.getMemberInfoGradeGuid())) {
                memberGradeRelationService.batchDeleteMemberGrade(memberGuidList, gradeChangeDTO.getRoleType());
            } else {
                // 会员和等级关联
                List<String> updateMemberGuidList = hsaOperationMemberInfos.stream()
                        .map(HsaOperationMemberInfo::getGuid)
                        .distinct()
                        .collect(Collectors.toList());
                MemberGradeRelationDTO relationDTO = gradeChangeAssembler.getMemberGradeRelationDTO(gradeChangeDTO, updateMemberGuidList);
                memberGradeRelationService.batchSaveAndUpdateRelation(relationDTO);

                // 会员付费记录
                MemberGradePayRecordReqVO recordReqVO = gradeChangeAssembler.getMemberGradePayRecordReqVO(gradeChangeDTO, updateMemberGuidList);
                memberGradePayRecordService.batchAdd(recordReqVO);
            }
        }
    }

    private void DealingWithGradeInterests(List<HsaOperationMemberInfo> hsaOperationMemberInfos,
                                           List<HsaMemberEquitiesReceiveRecord> hsaMemberEquitiesReceiveRecords,
                                           Map<String, List<HsaBusinessEquities>> equitiesMap,
                                           Set<HsaMemberGradeInfo> hsaMemberGradeInfos,
                                           Map<String, Integer> updateMemberIntegralMap, MemberGradeChangeDTO gradeChangeDTO) {
        log.info("等级变化的hsaMemberGradeInfos==================》" + JSON.toJSONString(hsaMemberGradeInfos));
        if (hsaMemberGradeInfos.size() > 0) {
            periodGiveGrowthValue(hsaOperationMemberInfos, hsaMemberEquitiesReceiveRecords, hsaMemberGradeInfos,
                    equitiesMap, updateMemberIntegralMap, gradeChangeDTO);
        }
    }

    private List<HsaMemberGradeRightsRecord> getMemberEquitiesRecourd(String afterGradeGuid,
                                                                      Map<String, List<HsaMemberGradeRightsRecord>> equitiesRightsRecordMap) {
        if (Objects.isNull(equitiesRightsRecordMap) || !equitiesRightsRecordMap.containsKey(afterGradeGuid)) {
            return Collections.emptyList();
        }
        List<HsaMemberGradeRightsRecord> memberGradeRightsRecords = equitiesRightsRecordMap.get(afterGradeGuid);
        return memberGradeRightsRecords.stream().filter(x -> afterGradeGuid.equals(x.getMemberGradeGuid())).collect(Collectors.toList());
    }

    private Map<String, List<HsaMemberGradeRightsRecord>> getEquitiesRightsRecordMap(String operSubjectGuid,
                                                                                     List<HsaOperationMemberInfo> hsaOperationMemberInfoList,
                                                                                     String roleType) {
        if (CollUtil.isEmpty(hsaOperationMemberInfoList)) {
            return Collections.emptyMap();
        }
        List<String> memberGradeInfoGuidList = hsaOperationMemberInfoList.stream()
                .map(HsaOperationMemberInfo::getMemberGradeInfoGuid).collect(Collectors.toList());
        memberGradeInfoGuidList.addAll(makeGradeGuidList(hsaOperationMemberInfoList, roleType));
        return hsaMemberGradeRightsRecordMapper.selectList(new LambdaQueryWrapper<HsaMemberGradeRightsRecord>()
                        .eq(HsaMemberGradeRightsRecord::getOperSubjectGuid, operSubjectGuid)
                        .in(HsaMemberGradeRightsRecord::getMemberInfoGuid, memberGradeInfoGuidList)
                        .eq(HsaMemberGradeRightsRecord::getRightsType, GradeRightsTypeEnum.GRADE_RIGHTS.getCode()))
                .stream().collect(Collectors.groupingBy(HsaMemberGradeRightsRecord::getMemberInfoGuid));
    }

    private Map<String, List<HsaBusinessEquities>> getGradeEquitiesMap(String operSubjectGuid) {
        if (StringUtils.isEmpty(operSubjectGuid)) {
            return Collections.emptyMap();
        }
        //查询当前会员等级权益信息
        return hsaGradeEquitiesService.list(new LambdaQueryWrapper<HsaBusinessEquities>()
                        .eq(HsaBusinessEquities::getOperSubjectGuid, operSubjectGuid)
                        .eq(HsaBusinessEquities::getEffective, NumberConstant.NUMBER_1)
                        .in(HsaBusinessEquities::getIsDelete, NumberConstant.NUMBER_0, NumberConstant.NUMBER_2))
                .stream().collect(Collectors.groupingBy(HsaBusinessEquities::getMemberGradeInfoGuid));
    }

    private void periodGiveGrowthValue(List<HsaOperationMemberInfo> hsaOperationMemberInfoList,
                                       List<HsaMemberEquitiesReceiveRecord> hsaMemberEquitiesReceiveRecords,
                                       Set<HsaMemberGradeInfo> hsaMemberGradeInfos,
                                       Map<String, List<HsaBusinessEquities>> equitiesMap,
                                       Map<String, Integer> updateMemberIntegralMap,
                                       MemberGradeChangeDTO gradeChangeDTO) {
        List<HsaGrowthValueDetail> hsaGrowthValueDetails = new ArrayList<>();
        List<HsaIntegralDetail> hsaIntegralDetails = new ArrayList<>();
        for (HsaMemberGradeInfo hsaMemberGradeInfo : hsaMemberGradeInfos) {
            List<HsaBusinessEquities> hsaBusinessEquitiesList = equitiesMap.get(hsaMemberGradeInfo.getGuid());
            log.info("等级权益=================={}", JSON.toJSONString(hsaBusinessEquitiesList));
            if (CollUtil.isEmpty(hsaBusinessEquitiesList)) {
                continue;
            }
            for (HsaBusinessEquities hsaBusinessEquities : hsaBusinessEquitiesList) {
                int type = hsaBusinessEquities.getEquitiesRuleType() == EquitiesRuleTypeEnum.GIVE_INTEGRAL.getCode() ?
                        NumberConstant.NUMBER_1 : NumberConstant.NUMBER_0;

                List<MemberGrowthValueRelationVO> memberGrowthValueList = hsaMemberEquitiesReceiveRecordMapper.
                        selectAllGrowthValue(hsaBusinessEquities.getGuid(), type);
                Map<String, Integer> memberGrowthValueMap = memberGrowthValueList
                        .stream()
                        .collect(Collectors.toMap(MemberGrowthValueRelationVO::getMemberGuid, MemberGrowthValueRelationVO::getGrowthValue));
                for (HsaOperationMemberInfo hsaOperationMemberInfo : hsaOperationMemberInfoList) {
                    //获取当前会员当前权益下已经赠送的成长值
                    Integer growthValue = ObjectUtil.objToInt(memberGrowthValueMap.get(hsaOperationMemberInfo.getGuid()));
                    //已经赠送超过相应成长值 如升级后降级
                    if (growthValue >= hsaBusinessEquities.getTotalGiveNumber()) {
                        continue;
                    }
                    //本次赠送成长值
                    int giveGrowthValue = hsaBusinessEquities.getTotalGiveNumber() - growthValue;
                    if (hsaBusinessEquities.getEquitiesRuleType() == EquitiesRuleTypeEnum.GIVE_INTEGRAL.getCode()) {
                        putUpdateMemberIntegralMap(updateMemberIntegralMap, hsaMemberGradeInfo, hsaBusinessEquities, hsaOperationMemberInfo, giveGrowthValue, hsaIntegralDetails);
                    } else {
                        hsaOperationMemberInfo.setMemberGrowthValue(giveGrowthValue + hsaOperationMemberInfo.getMemberGrowthValue());
                        hsaGrowthValueDetails.add(gradeChangeAssembler.getHsaGrowthValueDetail(hsaOperationMemberInfo.getGuid(),
                                SourceTypeEnum.ADD_BACKGROUND.getCode(),
                                hsaMemberGradeInfo,
                                hsaBusinessEquities,
                                giveGrowthValue,
                                hsaOperationMemberInfo.getMemberGrowthValue(),
                                gradeChangeDTO));
                    }
                    hsaMemberEquitiesReceiveRecords.add(gradeChangeAssembler.getHsaMemberEquitiesReceiveRecord(hsaOperationMemberInfo.getOperatorGuid(),
                            hsaOperationMemberInfo.getGuid(), hsaMemberGradeInfo.getGuid(), hsaBusinessEquities.getGuid(),
                            giveGrowthValue, type));
                }
            }
        }

        if (CollUtil.isNotEmpty(hsaGrowthValueDetails)) {
            hsaGrowthValueDetailService.saveBatch(hsaGrowthValueDetails, BATH_SAVE_SIZE);
        }
        if (CollUtil.isNotEmpty(hsaIntegralDetails)) {
            hsaIntegralDetailService.saveBatch(hsaIntegralDetails, BATH_SAVE_SIZE);
        }
        if (CollUtil.isNotEmpty(hsaMemberEquitiesReceiveRecords)) {
            hsaMemberEquitiesReceiveRecordService.saveBatch(hsaMemberEquitiesReceiveRecords, BATH_SAVE_SIZE);
        }
    }

    private void putUpdateMemberIntegralMap(Map<String, Integer> updateMemberIntegralMap,
                                            HsaMemberGradeInfo hsaMemberGradeInfo,
                                            HsaBusinessEquities hsaBusinessEquities,
                                            HsaOperationMemberInfo hsaOperationMemberInfo,
                                            int giveGrowthValue, List<HsaIntegralDetail> hsaIntegralDetails) {
        log.info("会员当前积分==================》" + hsaOperationMemberInfo.getMemberIntegral());
        log.info("积分权益赠送==================》" + giveGrowthValue);
        hsaOperationMemberInfo.setMemberIntegral(giveGrowthValue + hsaOperationMemberInfo.getMemberIntegral());
        hsaIntegralDetails.add(gradeChangeAssembler.getHsaIntegralValueDetail(hsaOperationMemberInfo.getGuid(),
                SourceTypeEnum.ADD_BACKGROUND.getCode(), hsaOperationMemberInfo.getOperSubjectGuid(),
                hsaMemberGradeInfo,
                hsaBusinessEquities, giveGrowthValue, hsaOperationMemberInfo.getMemberGrowthValue()));
        if (updateMemberIntegralMap.containsKey(hsaOperationMemberInfo.getGuid())) {
            Integer integral = updateMemberIntegralMap.get(hsaOperationMemberInfo.getGuid());
            integral = giveGrowthValue + integral;
            updateMemberIntegralMap.put(hsaOperationMemberInfo.getGuid(), integral);
        } else {
            updateMemberIntegralMap.put(hsaOperationMemberInfo.getGuid(), giveGrowthValue);
        }
    }


    private void checkGradeRefresh(String operSubjectGuid, Integer isRefresh, String roleType) {
        if (isRefresh == BooleanEnum.TRUE.getCode()) {
            try {
                String gradeRefreshKey = String.join(StringConstant.COLON, RedisKeyConstant.GRADE_REFRESH, operSubjectGuid, roleType);
                String gradeInfoChangeKey = String.join(StringConstant.COLON, RedisKeyConstant.GRADE_INFO_CHANGE, operSubjectGuid, roleType);
                Boolean isCleanGradeRefresh = cacheService.cleanToken(gradeRefreshKey);
                Boolean isCleanGradeInfoChangeKey = cacheService.cleanToken(gradeInfoChangeKey);
                log.info("缓存释放结果：>>>>isCleanGradeRefresh={},isCleanGradeInfoChangeKey={}", isCleanGradeRefresh, isCleanGradeInfoChangeKey);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void monitorGradeChange(MemberGradeChangeDTO gradeChangeDTO, List<HsaOperationMemberInfo> hsaOperationMemberInfos,
                                    List<HsaMemberGradeInfo> hsaMemberGradeInfoList) {
        List<HsaOperationMemberInfo> operationMemberInfos = Lists.newArrayList();
        hsaOperationMemberInfos.forEach(operationMemberInfo -> {
            if (!ObjectUtils.isEmpty(gradeChangeDTO.getSystem()) && SystemEnum.PARTNER.getCode() == gradeChangeDTO.getSystem()
                    && !StringUtils.isEmpty(gradeChangeDTO.getMemberInfoGradeGuid())) {
                // 不监听
                return;
            }
            HsaMemberGradeInfo hsaMemberGradeInfo = calculateExtraAmount(operationMemberInfo.getMemberGrowthValue(), hsaMemberGradeInfoList);
            if (!hsaMemberGradeInfo.getGuid().equals(operationMemberInfo.getMemberGradeInfoGuid())) {
                operationMemberInfos.add(operationMemberInfo);
            }
        });
        if (CollUtil.isNotEmpty(operationMemberInfos)) {
            memberQueueThreadExecutor.execute(() -> {
                SendMemberGradeChangeEvent event = new SendMemberGradeChangeEvent();
                event.setMemberGuidList(operationMemberInfos.stream().map(HsaOperationMemberInfo::getGuid).collect(Collectors.toList()));
                event.setOperSubjectGuid(gradeChangeDTO.getOperSubjectGuid());
                event.setSourceType(gradeChangeDTO.getSourceType());
                event.setIsRefresh(gradeChangeDTO.getIsRefresh());
                log.info("持续监听会员等级变化：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", event);
                new MemberGradeChangeProcessor(event).execute();
            });
        } else {
            checkGradeRefresh(gradeChangeDTO.getOperSubjectGuid(), gradeChangeDTO.getIsRefresh(), gradeChangeDTO.getRoleType());
        }
    }

    /**
     * 封装业务数据
     *
     * @param hsaOperationMemberInfo     hsaOperationMemberInfo
     * @param afterMemberGradeInfo       会员之后等级
     * @param beforeMemberGradeInfo      会员之前等级
     * @param hsaMemberGradeChangeDetail hsaMemberGradeDetail
     */
    private void gradeChangeBusiness(HsaOperationMemberInfo hsaOperationMemberInfo, HsaMemberGradeInfo afterMemberGradeInfo,
                                     HsaMemberGradeInfo beforeMemberGradeInfo, HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail,
                                     LocalDateTime localDateTime) {
        gradeChangeAssembler.getMemberGradeChangeDetail(hsaOperationMemberInfo, afterMemberGradeInfo, beforeMemberGradeInfo, hsaMemberGradeChangeDetail, localDateTime);
        hsaOperationMemberInfo.setMemberGradeInfoGuid(afterMemberGradeInfo.getGuid())
                .setMemberGradeInfoName(afterMemberGradeInfo.getName());
        hsaMemberGradeChangeDetail.setCurrentGrowthValue(hsaOperationMemberInfo.getMemberGrowthValue());
        hsaOperationMemberInfo.setUpgradeTime(localDateTime);
    }

    private Map<String, HsaMemberGradeInfo> getStringHsaMemberGradeInfoMap(List<HsaOperationMemberInfo> hsaOperationMemberInfoList, String roleType) {
        List<String> memberGradeInfoGuidList = hsaOperationMemberInfoList.stream()
                .map(HsaOperationMemberInfo::getMemberGradeInfoGuid)
                .collect(Collectors.toList());
        memberGradeInfoGuidList.addAll(makeGradeGuidList(hsaOperationMemberInfoList, roleType));
        return hsaMemberGradeInfoMapper.selectList(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                        .in(HsaMemberGradeInfo::getGuid, memberGradeInfoGuidList))
                .stream()
                .collect(Collectors.toMap(HsaMemberGradeInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));
    }

    private List<String> makeGradeGuidList(List<HsaOperationMemberInfo> hsaOperationMemberInfoList, String roleType) {
        List<String> memberGuidList = hsaOperationMemberInfoList.stream()
                .map(HsaOperationMemberInfo::getGuid)
                .distinct()
                .collect(Collectors.toList());
        List<HsaMemberGradeRelation> relationList = memberGradeRelationService.listByMemberGuidList(memberGuidList, roleType);
        if (CollectionUtils.isEmpty(relationList)) {
            return new ArrayList<>();
        }
        return relationList.stream()
                .map(HsaMemberGradeRelation::getMemberInfoGradeGuid)
                .distinct()
                .collect(Collectors.toList());
    }

    private List<HsaMemberGradeInfo> getHsaMemberGradeInfos(String operSubjectGuid, String roleType) {
        return hsaMemberGradeInfoMapper.selectList(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                .eq(HsaMemberGradeInfo::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaMemberGradeInfo::getRoleType, roleType)
                //todo 只查询生效的？
                .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                .eq(HsaMemberGradeInfo::getEffective, BooleanEnum.TRUE.getCode())
                //只查询免费的
                .eq(HsaMemberGradeInfo::getType, GradeTypeEnum.FREE.getCode())
                .orderByAsc(HsaMemberGradeInfo::getVipGrade));
    }


    /**
     * 定位当前成长值所在等级
     *
     * @param memberGrowthValue      memberGrowthValue
     * @param hsaMemberGradeInfoList hsaMemberGradeInfoList
     * @return HsaExtraAwardRule
     */
    private HsaMemberGradeInfo calculateExtraAmount(Integer memberGrowthValue, List<HsaMemberGradeInfo> hsaMemberGradeInfoList) {
        HsaMemberGradeInfo awardRule = null;
        for (int i = 0; i < hsaMemberGradeInfoList.size(); i++) {
            //hsaMemberGradeInfoList.get(i)
            if (memberGrowthValue < hsaMemberGradeInfoList.get(i).getGrowthValue()) {
                if (i == 0) {
                    awardRule = hsaMemberGradeInfoList.get(i);
                } else {
                    awardRule = hsaMemberGradeInfoList.get(i - 1);
                }
                break;
            }
            if (i == hsaMemberGradeInfoList.size() - 1) {
                awardRule = hsaMemberGradeInfoList.get(i);
            }
        }
        return awardRule;
    }

    /**
     * 等级权益升级礼包赠送
     *
     * @param operationMemberInfo       memberInfoGuid
     * @param beforeChangeGradeVipGrade beforeChangeGradeVipGrade
     * @param afterChangeGradeVipGrade  afterChangeGradeVipGrade
     */
    public void memberGradeRightsProcessor(GradeRightsProcessorDTO gradeRightsProcessorDTO,
                                           HsaOperationMemberInfo operationMemberInfo,
                                           HsaMemberGradeInfo beforeChangeGradeVipGrade,
                                           HsaMemberGradeInfo afterChangeGradeVipGrade,
                                           List<HsaMemberGradeInfo> hsaMemberGradeInfoList) {
        Map<String, Integer> updateMemberIntegralMap = gradeRightsProcessorDTO.getUpdateMemberIntegralMap();

        HsaGiftBagBaseInfo hsaGiftBagBaseInfo = gradeRightsProcessorDTO.getHsaGiftBagBaseInfo();

        List<HsaIntegralDetail> hsaIntegralDetails = gradeRightsProcessorDTO.getHsaIntegralDetails();

        List<HsaGrowthValueDetail> hsaGrowthValueDetails = gradeRightsProcessorDTO.getHsaGrowthValueDetails();

        List<HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecords = gradeRightsProcessorDTO.getHsaMemberGradeRightsRecords();
        List<HsaMemberGradeInfo> scopeMemberGradeInfoList = getHsaMemberGradeInfos(gradeRightsProcessorDTO, beforeChangeGradeVipGrade, afterChangeGradeVipGrade, hsaMemberGradeInfoList, hsaGiftBagBaseInfo);
        if (CollUtil.isEmpty(scopeMemberGradeInfoList)) {
            return;
        }
        List<String> memberGradeInfoGuidList = scopeMemberGradeInfoList
                .stream()
                .map(HsaMemberGradeInfo::getGuid)
                .collect(Collectors.toList());

        Map<String, HsaMemberGradeInfo> hsaMemberGradeInfoMap = scopeMemberGradeInfoList.stream()
                .collect(Collectors.toMap(HsaMemberGradeInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));
        //获取礼包
        List<HsaGradeGiftBag> hsaGradeGiftBags = hsaGradeGiftBagMapper.selectList(new LambdaQueryWrapper<HsaGradeGiftBag>()
                .in(HsaGradeGiftBag::getMemberGradeInfoGuid, memberGradeInfoGuidList)
                .in(HsaGradeGiftBag::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                .eq(HsaGradeGiftBag::getEffective, NumberConstant.NUMBER_1));
        log.info("升级礼包：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>等级guid{}", hsaGradeGiftBags);
        if (CollUtil.isEmpty(hsaGradeGiftBags)) {
            return;
        }

        List<String> gradeGiftBagGuid = hsaGradeGiftBags
                .stream()
                .map(HsaGradeGiftBag::getGuid)
                .collect(Collectors.toList());
        //获取会员已赠送权益记录
        Map<String, HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecordMap = getStringHsaMemberGradeRightsRecordMap(operationMemberInfo, gradeGiftBagGuid);

        //礼包赠送
        for (HsaGradeGiftBag hsaGradeGiftBag : hsaGradeGiftBags) {
            HsaMemberGradeInfo hsaMemberGradeInfo = hsaMemberGradeInfoMap.get(hsaGradeGiftBag.getMemberGradeInfoGuid());
            List<String> rightsGuidList = hsaMemberGradeRightsRecords.stream().map(HsaMemberGradeRightsRecord::getRightsGuid).collect(Collectors.toList());
            //双重校验
            if (CollUtil.isEmpty(hsaMemberGradeRightsRecordMap) || !hsaMemberGradeRightsRecordMap.containsKey(hsaGradeGiftBag.getMemberGradeInfoGuid())
                    && (CollUtil.isEmpty(rightsGuidList) || !rightsGuidList.contains(hsaGradeGiftBag.getGuid()))) {
                HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord = gradeChangeAssembler.getHsaMemberGradeRightsRecord(gradeRightsProcessorDTO, operationMemberInfo, hsaGradeGiftBag);
                hsaMemberGradeRightsRecords.add(hsaMemberGradeRightsRecord);

                if (hsaGradeGiftBag.getType() == NumberConstant.NUMBER_0) {
                    operationMemberInfo.setMemberGrowthValue(operationMemberInfo.getMemberGrowthValue() + hsaGradeGiftBag.getValue());
                    //会员成长值明细记录
                    HsaGrowthValueDetail hsaGrowthValueDetail = gradeChangeAssembler.getHsaGrowthValueDetail(operationMemberInfo, gradeRightsProcessorDTO.getSourceType(), gradeRightsProcessorDTO.getOperSubjectGuid(), hsaGradeGiftBag, hsaMemberGradeInfo, hsaGiftBagBaseInfo, afterChangeGradeVipGrade);
                    hsaGrowthValueDetails.add(hsaGrowthValueDetail);
                } else {
                    //todo 积分待定

                    operationMemberInfo.setMemberIntegral(operationMemberInfo.getMemberIntegral() + hsaGradeGiftBag.getValue());

                    // 升级礼包赠送积分值
                    Integer giftIntegral = Optional.ofNullable(hsaGradeGiftBag.getValue()).orElse(0);
                    putUpdateMemberIntegralMap(operationMemberInfo, updateMemberIntegralMap, giftIntegral);

                    //会员积分明细记录
                    HsaIntegralDetail hsaIntegralDetail = gradeChangeAssembler.getHsaIntegralValueDetail(operationMemberInfo, gradeRightsProcessorDTO.getSourceType(), gradeRightsProcessorDTO.getOperSubjectGuid(), hsaGradeGiftBag, hsaMemberGradeInfo, hsaGiftBagBaseInfo, afterChangeGradeVipGrade);
                    hsaIntegralDetails.add(hsaIntegralDetail);
                }

            }
        }
    }

    private static List<HsaMemberGradeInfo> getHsaMemberGradeInfos(GradeRightsProcessorDTO gradeRightsProcessorDTO, HsaMemberGradeInfo beforeChangeGradeVipGrade, HsaMemberGradeInfo afterChangeGradeVipGrade, List<HsaMemberGradeInfo> hsaMemberGradeInfoList, HsaGiftBagBaseInfo hsaGiftBagBaseInfo) {
        if (Objects.isNull(hsaGiftBagBaseInfo)) {
            log.info("等级升级礼包基础信息不存在：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>operSubjectGuid{}", gradeRightsProcessorDTO.getOperSubjectGuid());
            return Collections.emptyList();
        }
        if (hsaGiftBagBaseInfo.getEquitiesRule() == GiftBagEquitiesRuleEnum.SELF_COLLECTION.getCode()) {
            log.info("该主体下升级礼包为自主领取：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>operSubjectGuid{}", gradeRightsProcessorDTO.getOperSubjectGuid());
            return Collections.emptyList();
        }
        List<HsaMemberGradeInfo> scopeMemberGradeInfoList;
        //获取范围内等级
        scopeMemberGradeInfoList = getHsaMemberGradeInfos(beforeChangeGradeVipGrade, afterChangeGradeVipGrade, hsaMemberGradeInfoList);
        log.info("获取范围内等级：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{},{}", beforeChangeGradeVipGrade, afterChangeGradeVipGrade);
        if (CollUtil.isEmpty(scopeMemberGradeInfoList)) {
            log.info("此等级区间不存在等级：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>等级guid{}", scopeMemberGradeInfoList);
            return Collections.emptyList();
        }
        return scopeMemberGradeInfoList;
    }

    private static void putUpdateMemberIntegralMap(HsaOperationMemberInfo operationMemberInfo, Map<String, Integer> updateMemberIntegralMap, Integer giftIntegral) {
        if (updateMemberIntegralMap.containsKey(operationMemberInfo.getGuid())) {
            Integer integral = updateMemberIntegralMap.get(operationMemberInfo.getGuid());
            integral = integral + giftIntegral;
            updateMemberIntegralMap.put(operationMemberInfo.getGuid(), integral);
        } else {
            updateMemberIntegralMap.put(operationMemberInfo.getGuid(), giftIntegral);
        }
    }

    private static List<HsaMemberGradeInfo> getHsaMemberGradeInfos(HsaMemberGradeInfo beforeChangeGradeVipGrade, HsaMemberGradeInfo afterChangeGradeVipGrade, List<HsaMemberGradeInfo> hsaMemberGradeInfoList) {
        List<HsaMemberGradeInfo> scopeMemberGradeInfoList;
        if (Objects.nonNull(beforeChangeGradeVipGrade)) {
            scopeMemberGradeInfoList = hsaMemberGradeInfoList.stream()
                    .filter(in -> in.getVipGrade() > beforeChangeGradeVipGrade.getVipGrade() &&
                            in.getVipGrade() <= afterChangeGradeVipGrade.getVipGrade())
                    .collect(Collectors.toList());
        } else {
            scopeMemberGradeInfoList = hsaMemberGradeInfoList.stream()
                    .filter(in -> in.getVipGrade() > NumberConstant.NUMBER_1 &&
                            in.getVipGrade() <= afterChangeGradeVipGrade.getVipGrade())
                    .collect(Collectors.toList());
        }
        return scopeMemberGradeInfoList;
    }

    /**
     * 会员等级权益领取
     *
     * @param operationMemberInfo memberInfoGuid
     * @param operSubjectGuid     operSubjectGuid
     */
    public void memberGradeRightsProcessor(HsaOperationMemberInfo operationMemberInfo, List<HsaBusinessEquities> hsaBusinessEquities,
                                           List<HsaMemberGradeRightsRecord> equitiesRightsRecords, String operSubjectGuid,
                                           List<HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecords,
                                           LocalDateTime localDateTime) {
        if (CollUtil.isEmpty(hsaBusinessEquities)) {
            return;
        }
        //验证是否需要给当前会员发放权益
        if (!verifyGradeEquity(hsaBusinessEquities, equitiesRightsRecords)) {
            return;
        }

        for (HsaBusinessEquities hsaGradeEquity : hsaBusinessEquities) {
            HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord =
                    gradeChangeAssembler.getMemberGradeRightsRecord(operationMemberInfo, operSubjectGuid, localDateTime, hsaGradeEquity);
            hsaMemberGradeRightsRecords.add(hsaMemberGradeRightsRecord);
        }
    }

    /**
     * 1.验证会员是否领取过当前会员权益
     * 2.验证当前会员等级是否修改过权益
     *
     * @param hsaBusinessEquities      当前会员拥有的权益
     * @param equitiesRightsRecords 当前会员已经领取过的权益信息
     * @return 未领取过 和 修改过会员权益  返回true  否则返回false
     */
    private boolean verifyGradeEquity(List<HsaBusinessEquities> hsaBusinessEquities, List<HsaMemberGradeRightsRecord> equitiesRightsRecords) {
        /*List<HsaMemberGradeRightsRecord> equitiesRightsRecords = hsaMemberGradeRightsRecordMapper.selectList(
                new LambdaQueryWrapper<HsaMemberGradeRightsRecord>()
                        .eq(HsaMemberGradeRightsRecord::getOperSubjectGuid, operSubjectGuid)
                        .eq(HsaMemberGradeRightsRecord::getMemberGradeGuid, afterChangeGradeVipGrade.getGuid())
                        .eq(HsaMemberGradeRightsRecord::getMemberInfoGuid, operationMemberInfo.getGuid())
                        .eq(HsaMemberGradeRightsRecord::getRightsType, GradeRightsTypeEnum.GRADE_RIGHTS.getCode()));*/
        //验证会员是否领取过当前会员权益
        if (CollUtil.isNotEmpty(equitiesRightsRecords)) {
            //权益记录guid
            List<String> equitiesRightsRecordGuids = equitiesRightsRecords.stream()
                    .map(HsaMemberGradeRightsRecord::getRightsGuid).collect(Collectors.toList());
            //验证当前会员等级是否修改过权益
            List<HsaBusinessEquities> collect = hsaBusinessEquities.stream()
                    .filter(x -> !equitiesRightsRecordGuids.contains(x.getGuid())).collect(Collectors.toList());
            return !CollUtil.isEmpty(collect);
        }
        return true;
    }


    private Map<String, HsaMemberGradeRightsRecord> getStringHsaMemberGradeRightsRecordMap(HsaOperationMemberInfo operationMemberInfo, List<String> gradeGiftBagGuid) {
        return hsaMemberGradeRightsRecordMapper.selectList(new LambdaQueryWrapper<HsaMemberGradeRightsRecord>()
                        .in(HsaMemberGradeRightsRecord::getRightsGuid, gradeGiftBagGuid)
                        .eq(HsaMemberGradeRightsRecord::getMemberInfoGuid, operationMemberInfo.getGuid())
                        .eq(HsaMemberGradeRightsRecord::getRightsType, GradeRightsTypeEnum.GRADE_RIGHTS_BAG.getCode()))
                .stream()
                .collect(Collectors.toMap(HsaMemberGradeRightsRecord::getMemberGradeGuid, Function.identity(), (entity1, entity2) -> entity1));
    }

    /**
     * 定位当前成长值所在等级
     *
     * @param memberGrowthValue memberGrowthValue
     * @param operSubjectGuid   operSubjectGuid
     * @return HsaExtraAwardRule
     */
    public HsaMemberGradeInfo calculateExtraAmount(Integer memberGrowthValue, String operSubjectGuid) {
        // 获取当前会员等级信息 成长值只跟免费会员有关系 所以这里加了一个type过滤
        List<HsaMemberGradeInfo> hsaMemberGradeInfoList = hsaMemberGradeInfoMapper.selectList(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                .eq(HsaMemberGradeInfo::getOperSubjectGuid, operSubjectGuid)
                .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                .eq(HsaMemberGradeInfo::getEffective, NumberConstant.NUMBER_1)
                .eq(HsaMemberGradeInfo::getType, GradeTypeEnum.FREE.getCode())
                .orderByAsc(HsaMemberGradeInfo::getVipGrade));
        HsaMemberGradeInfo awardRule = null;
        for (int i = 0; i < hsaMemberGradeInfoList.size(); i++) {
            if (memberGrowthValue < hsaMemberGradeInfoList.get(i).getGrowthValue()) {
                if (i == 0) {
                    awardRule = hsaMemberGradeInfoList.get(i);
                } else {
                    awardRule = hsaMemberGradeInfoList.get(i - 1);
                }
                break;
            }
            if (i == hsaMemberGradeInfoList.size() - 1) {
                awardRule = hsaMemberGradeInfoList.get(i);
            }
        }
        return awardRule;
    }
}
