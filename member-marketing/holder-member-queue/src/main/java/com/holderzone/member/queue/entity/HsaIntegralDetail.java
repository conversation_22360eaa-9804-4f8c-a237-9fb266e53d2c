package com.holderzone.member.queue.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 积分明细表
 * @date 2022/5/6
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaIntegralDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 积分明细id
     */
    private Long id;

    /**
     * 积分明细GUID
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 会员GUID
     */
    private String memberInfoGuid;

    /**
     * 积分任务guid
     */
    private String integralTaskGuid;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务动作 0:注册 1:完善个人信息 2:单笔消费金额 3:消费指定商品 4:累计消费金额 5:累计消费笔数 6:单笔充值金额 7:累计充值金额
     *
     * @see com.holderzone.member.common.enums.growth.TaskActionEnum
     */
    private Integer taskAction;

    /**
     * 任务编号
     */
    private String taskNumber;

    /**
     * 记录说明
     */
    private String recordDeclaration;

    /**
     * 说明
     */
    private String declaration;

    /**
     * 任务完成第次数
     */
    private Integer taskFinishTime;

    /**
     * 积分变化类型,0增加,1减少
     */
    private Integer integralType;

    /**
     * 积分
     */
    private Integer integral;

    /**
     * 翻倍积分权益（翻了多少倍）
     */
    private BigDecimal multipleIntegral;

    /**
     * 0永久有效1固定时间
     */
    private Integer integralValidity;

    /**
     * 失效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime integralValidityDate;

    /**
     * 任务完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime taskFinishDate;

    /**
     * 当前积分
     */
    private Integer currentIntegral;

    /**
     * 操作人员账号名字
     */
    private String operatorAccountName;

    /**
     * 当前会员等级
     */
    private String currentMemberLevel;

    /**
     * 积分记录剩余积分
     */
    private Integer recordRemainIntegral;

    /**
     * 积分保护期
     */
    private LocalDateTime integralProtectDate;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

    /**
     * 积分变动类型（1初始积分，2系统调整，3积分失效，4消费退款，5积分任务，6升级礼包 7赠送积分 8积分抵现）
     */
    private Integer changeType;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 积分变动来源
     */
    private Integer integralSourceType;

    /**
     * 会员消费记录GUID
     */
    private String memberConsumptionGuid;

    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 积分说明
     */
    private String integralDeclaration;

}
