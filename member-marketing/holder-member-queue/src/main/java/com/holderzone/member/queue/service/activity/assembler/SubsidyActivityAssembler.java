package com.holderzone.member.queue.service.activity.assembler;

import com.alibaba.fastjson.JSON;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.DateUnitEnum;
import com.holderzone.member.common.enums.card.CardTypeEnum;
import com.holderzone.member.common.enums.member.AmountSourceTypeEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.qo.activity.SubsidySpecific;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.queue.dto.activity.CreationSubsidyDetailRecordDTO;
import com.holderzone.member.queue.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

@Service
@Slf4j
public class SubsidyActivityAssembler {

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;


    /**
     * 获取充值/消费记录详情
     */
    public HsaMemberFundingDetail getRechargeDetail(HsaMemberInfoCard memberInfoCard, HsaSubsidyActivity
            hsaSubsidyActivity, LocalDateTime now, HsaMemberFundingDetail memberFundingDetail) {
        memberFundingDetail.setOperatorTelName(hsaSubsidyActivity.getOperatorTelName());
        memberFundingDetail.setOperSubjectGuid(memberInfoCard.getOperSubjectGuid());
        memberFundingDetail.setEnterpriseGuid(memberInfoCard.getEnterpriseGuid());
        memberFundingDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberFundingDetail.class.getSimpleName()));
        memberFundingDetail.setMemberInfoCardGuid(memberInfoCard.getGuid());
        memberFundingDetail.setCardType(!StringUtils.isEmpty(memberInfoCard.getElectronicCardGuid()) ? CardTypeEnum.CARD_TYPE_EQUITY.getCode() : CardTypeEnum.CARD_TYPE_MAIN.getCode());
        memberFundingDetail.setCardNum(memberFundingDetail.getCardType() == CardTypeEnum.CARD_TYPE_MAIN.getCode() ? memberInfoCard.getPhysicalCardNum() : memberInfoCard.getElectronicCardNum());
        memberFundingDetail.setCardName(memberInfoCard.getCardName());
        memberFundingDetail.setMemberInfoGuid(memberInfoCard.getMemberInfoGuid());
        memberFundingDetail.setAmountSourceType(AmountSourceTypeEnum.SUBSIDY_GRANT.getCode());
        memberFundingDetail.setAmountSubsidyFundingType(NumberConstant.NUMBER_0);
        memberFundingDetail.setCardGuid(memberInfoCard.getCardGuid());
        memberFundingDetail.setSubsidyAmount(hsaSubsidyActivity.getSubsidyMoney());
        memberFundingDetail.setCardRechargeResidualBalance(memberInfoCard.getCardAmount());
        memberFundingDetail.setCardSubsidyResidualBalance(memberInfoCard.getSubsidyAmount());
        memberFundingDetail.setCardGiftResidualBalance(memberInfoCard.getGiftAmount());
        memberFundingDetail.setRemark("补贴活动 " + "(" + hsaSubsidyActivity.getActivityName() + ")");
        memberFundingDetail.setChangeSource(SourceTypeEnum.ADD_BACKGROUND.getCode());
        memberFundingDetail.setIsValid(NumberConstant.NUMBER_1);
        memberFundingDetail.setSourceName(hsaSubsidyActivity.getActivityName());
        //过期时间
        checkSubsidyOutTime(hsaSubsidyActivity, now, memberFundingDetail);
        return memberFundingDetail;
    }

    public HsaSubsidyActivityDetailRecord getHsaSubsidyActivityDetailRecord(HsaSubsidyActivityRecord hsaSubsidyActivityRecord,
                                                                             HsaSubsidyActivity hsaSubsidyActivity,
                                                                             HsaOperationMemberInfo hsaOperationMemberInfo,
                                                                             CreationSubsidyDetailRecordDTO recordDTO,
                                                                             Integer result,
                                                                             LocalDateTime now) {
        BigDecimal amount = recordDTO.getAmount();
        String error = recordDTO.getError();

        LocalDateTime outTime = recordDTO.getOutTime();
        String memberInfoCardGuid = recordDTO.getMemberInfoCardGuid();

        HsaSubsidyActivityDetailRecord subsidyActivityDetailRecord = new HsaSubsidyActivityDetailRecord();
        subsidyActivityDetailRecord
                .setGuid(guidGeneratorUtil.getStringGuid(HsaSubsidyActivityDetailRecord.class.getSimpleName()))
                .setSubsidyError(error)
                .setSubsidyActivityGuid(hsaSubsidyActivity.getGuid())
                .setActivityName(hsaSubsidyActivity.getActivityName())
                .setActivityNum(hsaSubsidyActivity.getActivityNum())
                .setCardGuid(hsaSubsidyActivityRecord.getCardGuid())
                .setSubsidyResidueMoney(amount)
                .setIsRetreat(hsaSubsidyActivity.getIsRetreat())
                .setMemberInfoGuid(hsaOperationMemberInfo.getGuid())
                .setOperSubjectGuid(hsaSubsidyActivityRecord.getOperSubjectGuid())
                .setMemberInfoCardGuid(memberInfoCardGuid)
                .setValidityExpireTime(outTime)
                .setIsWithdraw(BooleanEnum.FALSE.getCode())
                .setSubsidyMoney(hsaSubsidyActivity.getSubsidyMoney())
                .setSubsidyTime(hsaSubsidyActivityRecord.getSubsidyTime())
                .setSubsidyResult(result)
                .setOperatorTelName(hsaSubsidyActivity.getOperatorTelName())
                .setSubsidyActivityRecordGuid(hsaSubsidyActivityRecord.getGuid())
                .setGmtCreate(now)
                .setGmtModified(now);
        return subsidyActivityDetailRecord;
    }


    public HsaMemberFundingDetail getHsaMemberFundingDetail(HsaSubsidyActivityDetailRecord detailRecord,
                                                            HsaMemberInfoCard hsaMemberInfoCard,
                                                            HsaSubsidyActivityRecord hsaSubsidyActivityRecord) {
        HsaMemberFundingDetail memberFundingDetail = new HsaMemberFundingDetail();
        memberFundingDetail.setOperatorTelName(hsaSubsidyActivityRecord.getOperatorTelName());
        memberFundingDetail.setOperSubjectGuid(hsaSubsidyActivityRecord.getOperSubjectGuid());
        memberFundingDetail.setEnterpriseGuid(hsaSubsidyActivityRecord.getEnterpriseGuid());
        memberFundingDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberFundingDetail.class.getSimpleName()));
        memberFundingDetail.setMemberInfoCardGuid(hsaMemberInfoCard.getGuid());
        memberFundingDetail.setCardType(!StringUtils.isEmpty(hsaMemberInfoCard.getElectronicCardGuid()) ?
                CardTypeEnum.CARD_TYPE_EQUITY.getCode() : CardTypeEnum.CARD_TYPE_MAIN.getCode());
        memberFundingDetail.setCardNum(memberFundingDetail.getCardType() == CardTypeEnum.CARD_TYPE_MAIN.getCode() ?
                hsaMemberInfoCard.getPhysicalCardNum() : hsaMemberInfoCard.getElectronicCardNum());
        memberFundingDetail.setCardName(hsaMemberInfoCard.getCardName());
        memberFundingDetail.setMemberInfoGuid(detailRecord.getMemberInfoGuid());
        memberFundingDetail.setAmountSourceType(AmountSourceTypeEnum.SUBSIDY_EXPIRED.getCode());
        memberFundingDetail.setAmountSubsidyFundingType(NumberConstant.NUMBER_1);
        memberFundingDetail.setCardGuid(hsaMemberInfoCard.getCardGuid());
        memberFundingDetail.setRemark("补贴活动金额过期");
        memberFundingDetail.setSubsidyAmount(detailRecord.getSubsidyResidueMoney());
        memberFundingDetail.setCardRechargeResidualBalance(hsaMemberInfoCard.getCardAmount());
        memberFundingDetail.setCardSubsidyResidualBalance(hsaMemberInfoCard.getSubsidyAmount());
        memberFundingDetail.setCardGiftResidualBalance(hsaMemberInfoCard.getGiftAmount());
        memberFundingDetail.setChangeSource(SourceTypeEnum.ADD_BACKGROUND.getCode());
        memberFundingDetail.setIsValid(NumberConstant.NUMBER_1);
        memberFundingDetail.setSourceName(hsaSubsidyActivityRecord.getActivityName());
        return memberFundingDetail;
    }


    /**
     * 计算过期时间
     *
     * @param hsaSubsidyActivity  hsaSubsidyActivity
     * @param now                 now
     * @param memberFundingDetail memberFundingDetail
     */
    private void checkSubsidyOutTime(HsaSubsidyActivity hsaSubsidyActivity,
                                     LocalDateTime now,
                                     HsaMemberFundingDetail memberFundingDetail) {
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1;
        int day = cal.get(Calendar.DAY_OF_MONTH);
        //时
        String presentDate = getTmpDate(date, "H");
        if (hsaSubsidyActivity.getSubsidyMoneyPeriodType() == 0) {
            memberFundingDetail.setOutOfDate(null);
        } else if (hsaSubsidyActivity.getSubsidyMoneyPeriodType() == 1 && Objects.isNull(hsaSubsidyActivity.getSubsidyRecycleAppointTime())) {
            immobilizationTime(hsaSubsidyActivity, memberFundingDetail, date, year, month, day, presentDate);
        } else if (hsaSubsidyActivity.getSubsidyMoneyPeriodType() == 1) {
            memberFundingDetail.setOutOfDate(hsaSubsidyActivity.getSubsidyRecycleAppointTime());
        } else {
            if (hsaSubsidyActivity.getValidityUnit().equals(DateUnitEnum.HOUR.getCode())) {
                memberFundingDetail.setOutOfDate(now.plusHours(hsaSubsidyActivity.getSubsidyRecycle()));
            } else if (hsaSubsidyActivity.getValidityUnit().equals(DateUnitEnum.DAY.getCode())) {
                memberFundingDetail.setOutOfDate(now.plusDays(hsaSubsidyActivity.getSubsidyRecycle()));
            } else if (hsaSubsidyActivity.getValidityUnit().equals(DateUnitEnum.WEEK.getCode())) {
                memberFundingDetail.setOutOfDate(now.plusWeeks(hsaSubsidyActivity.getSubsidyRecycle()));
            } else if (hsaSubsidyActivity.getValidityUnit().equals(DateUnitEnum.MONTH.getCode())) {
                memberFundingDetail.setOutOfDate(now.plusMonths(hsaSubsidyActivity.getSubsidyRecycle()));
            } else {
                memberFundingDetail.setOutOfDate(now.plusYears(hsaSubsidyActivity.getSubsidyRecycle()));
            }
        }
    }

    /**
     * 过期时间简单算法
     */
    private void immobilizationTime(HsaSubsidyActivity hsaSubsidyActivity,
                                    HsaMemberFundingDetail memberFundingDetail,
                                    Date date,
                                    int year,
                                    int month,
                                    int day,
                                    String presentDate) {
        List<SubsidySpecific> subsidySpecifics = JSON.parseArray(hsaSubsidyActivity.getSubsidyRecycleJson(), SubsidySpecific.class);
        SubsidySpecific subsidySpecific = subsidySpecifics.get(0);
        //当日过期时间
        if (hsaSubsidyActivity.getSubsidyRecycleFrequencyType().equals(DateUnitEnum.DAY.getCode())) {
            checkHour(memberFundingDetail, presentDate, subsidySpecific, year, month, day);
        } else if (hsaSubsidyActivity.getSubsidyRecycleFrequencyType().equals(DateUnitEnum.WEEK.getCode())) {
            checkWeek(memberFundingDetail, date, year, month, day, presentDate, subsidySpecific);
        } else if (hsaSubsidyActivity.getSubsidyRecycleFrequencyType().equals(DateUnitEnum.MONTH.getCode())) {
            checkMonth(memberFundingDetail, date, year, month, day, presentDate, subsidySpecific);
        } else if (hsaSubsidyActivity.getSubsidyRecycleFrequencyType().equals(DateUnitEnum.YEAR.getCode())) {
            checkYear(memberFundingDetail, date, year, month, day, presentDate, subsidySpecific);
        } else {
            memberFundingDetail.setOutOfDate(hsaSubsidyActivity.getSubsidyRecycleAppointTime());
        }
    }

    private void checkYear(HsaMemberFundingDetail memberFundingDetail,
                           Date date,
                           int year,
                           int month,
                           int day,
                           String presentDate,
                           SubsidySpecific subsidySpecific) {
        //当前月日
        String current = getTmpDate(date, "M-d");
        String[] strCurrent = current.split("-");
        List<String> currentList = new ArrayList<>(Arrays.asList(strCurrent));
        int monthCurrent = Integer.parseInt(currentList.get(0));
        int dayCurrent = Integer.parseInt(currentList.get(1));
        String in = subsidySpecific.getTimeList().get(0);
        String[] str = in.split("-");
        List<String> resultList = new ArrayList<>(Arrays.asList(str));
        int monthInt = Integer.parseInt(resultList.get(0));
        int dayInt = Integer.parseInt(resultList.get(1));
        if (monthCurrent > monthInt) {
            //明年
            LocalDateTime dateTime = LocalDateTime.of(year + 1, monthInt, dayInt, Integer.parseInt(subsidySpecific.getTime()), 0);
            memberFundingDetail.setOutOfDate(dateTime);
        } else if (monthCurrent < monthInt) {
            //今年
            LocalDateTime dateTime = LocalDateTime.of(year, monthInt, dayInt, Integer.parseInt(subsidySpecific.getTime()), 0);
            memberFundingDetail.setOutOfDate(dateTime);
        } else {
            //同月则判断天
            if (dayCurrent > dayInt) {
                //明年
                LocalDateTime dateTime = LocalDateTime.of(year + 1, monthInt, dayInt, Integer.parseInt(subsidySpecific.getTime()), 0);
                memberFundingDetail.setOutOfDate(dateTime);
            } else if (dayInt > dayCurrent) {
                //今年
                LocalDateTime dateTime = LocalDateTime.of(year, monthInt, dayInt, Integer.parseInt(subsidySpecific.getTime()), 0);
                memberFundingDetail.setOutOfDate(dateTime);
            } else {
                checkHour(memberFundingDetail, presentDate, subsidySpecific, year, month, day);
            }
        }
    }

    private void checkMonth(HsaMemberFundingDetail memberFundingDetail, Date date, int year, int month, int day, String presentDate, SubsidySpecific subsidySpecific) {
        //当前日期
        int current = Integer.parseInt(getTmpDate(date, "d"));
        int in = Integer.parseInt(subsidySpecific.getTimeList().get(0));
        //若大于当前日期则是本月
        if (in > current) {
            LocalDateTime dateTime = LocalDateTime.of(year, month, in, Integer.parseInt(subsidySpecific.getTime()), 0);
            memberFundingDetail.setOutOfDate(dateTime);
        }
        //若小于当前日期则是下月
        else if (current > in) {
            LocalDateTime dateTime = LocalDateTime.of(year, month + 1, in, Integer.parseInt(subsidySpecific.getTime()), 0);
            memberFundingDetail.setOutOfDate(dateTime);
        } else {
            //同天则判断时
            checkHour(memberFundingDetail, presentDate, subsidySpecific, year, month, day);
        }
    }

    private void checkWeek(HsaMemberFundingDetail memberFundingDetail,
                           Date date,
                           int year,
                           int month,
                           int day,
                           String presentDate,
                           SubsidySpecific subsidySpecific) {
        //当前星期几为0则是周天
        String currentWeek = dateToWeek(getTmpDate(date, "yyyy-MM-dd"));
        String in = subsidySpecific.getTimeList().get(0);
        int checkNum = Integer.parseInt(in);
        if (checkNum == 0) {
            checkNum = 7;
        }
        int checkNum2 = Integer.parseInt(currentWeek);
        if (checkNum2 == 0) {
            checkNum2 = 7;
        }
        //若大于当前星期则是本周
        if (checkNum > checkNum2) {
            //相差天数
            int num = checkNum - checkNum2;
            LocalDateTime dateTime = LocalDateTime.of(year, month, day + num, Integer.parseInt(subsidySpecific.getTime()), 0);
            memberFundingDetail.setOutOfDate(dateTime);
        }
        //若小于当前星期则是下周
        else if (checkNum < checkNum2) {
            int num = 7 - (checkNum2 - checkNum);
            LocalDateTime dateTime = LocalDateTime.of(year, month, day + num, Integer.parseInt(subsidySpecific.getTime()), 0);
            memberFundingDetail.setOutOfDate(dateTime);
        }
        //若等于则判断时
        else {
            checkHour(memberFundingDetail, presentDate, subsidySpecific, year, month, day);
        }
    }

    private void checkHour(HsaMemberFundingDetail memberFundingDetail, String presentDate, SubsidySpecific subsidySpecific, int year, int month, int day) {
        int parseInt = Integer.parseInt(presentDate);
        int parseInt1 = Integer.parseInt(subsidySpecific.getTime());
        if (parseInt < parseInt1) {
            LocalDateTime dateTime = LocalDateTime.of(year, month, day, parseInt1, 0);
            memberFundingDetail.setOutOfDate(dateTime);
        } else if (parseInt > parseInt1) {
            //次日过期
            LocalDateTime dateTime = LocalDateTime.of(year, month, day + 1, parseInt1, 0);
            memberFundingDetail.setOutOfDate(dateTime);
        } else {
            //次日过期
            memberFundingDetail.setOutOfDate(LocalDateTime.of(year, month, day + 1, parseInt1, 0));
        }
    }


    /**
     * 日期转星期
     *
     * @param datetime datetime
     * @return String
     */
    public static String dateToWeek(String datetime) {
        SimpleDateFormat f = new SimpleDateFormat("yyyy-MM-dd");
        String[] weekDays = {"0", "1", "2", "3", "4", "5", "6"};
        // 获得一个日历
        Calendar cal = Calendar.getInstance();
        Date date;
        try {
            date = f.parse(datetime);
            cal.setTime(date);
        } catch (ParseException e) {
            log.error("日期转星期出错", e);
        }
        // 指示一个星期中的某天。
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) {
            w = 0;
        }
        return weekDays[w];
    }

    private String getTmpDate(Date date, String s) {
        SimpleDateFormat format2 = new SimpleDateFormat(s);
        return format2.format(date);
    }
}
