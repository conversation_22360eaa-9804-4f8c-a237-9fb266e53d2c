package com.holderzone.member.queue.service.member;

import com.holderzone.member.common.dto.event.SendMemberConsumptionDistributeEvent;

/**
 * 会员消费分销记录服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/04
 */
public interface HsaMemberConsumptionDistributeService {

    /**
     * 处理分销记录
     *
     * @param event 分销事件
     */
    void processDistributeRecord(SendMemberConsumptionDistributeEvent event);
} 