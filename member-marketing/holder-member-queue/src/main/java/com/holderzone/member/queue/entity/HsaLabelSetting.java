package com.holderzone.member.queue.entity;

import com.holderzone.member.common.enums.member.ConditionSetEnum;
import com.holderzone.member.common.enums.member.EnableEnum;
import com.holderzone.member.common.enums.member.LabelTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ProjectName: member-marketing
 * @ClassName: HsaLabelSetting
 * @Author: pantao
 * @Description: 会员标签设置表
 * @Date: 2021/8/24 11:09
 * @Version: 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaLabelSetting implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * GUID
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 标签类型,0手动标签,1自动标签
     * @see LabelTypeEnum
     */
    private Integer labelType;

    /**
     * 标签名
     */
    private String labelName;

    /**
     * 会员人数
     */
    private Integer memberNum;

    /**
     * 最近标记时间
     */
    private LocalDateTime recentlyMarkedTime;

    /**
     * 条件设置，0满足以下任一条件，1满足以下所有条件
     * @see ConditionSetEnum
     */
    private Integer conditionSet;

    /**
     * 操作人guid
     */
    private String operatorGuid;

    /**
     * 操作人名字
     */
    private String operatorName;

    /**
     * 操作人账号
     */
    private String operatorAccount;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否禁用(0:启用 1:禁用)
     * @see EnableEnum
     */
    private Integer isEnable;

}
