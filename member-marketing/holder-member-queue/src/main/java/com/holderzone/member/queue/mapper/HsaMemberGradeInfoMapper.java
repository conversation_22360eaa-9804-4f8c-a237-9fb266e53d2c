package com.holderzone.member.queue.mapper;


import com.holderzone.member.common.enums.equities.EquitiesRuleTypeEnum;
import com.holderzone.member.queue.config.mybatis.HolderBaseMapper;
import com.holderzone.member.queue.dto.grade.gradeEquitiesVo;
import com.holderzone.member.queue.entity.HsaMemberGradeInfo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 会员等级基础信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-04
 */
public interface HsaMemberGradeInfoMapper extends HolderBaseMapper<HsaMemberGradeInfo> {

    /**
     * 通过会员等级guid查询，翻倍成长值权益的guid
     * @param operSubjectGuid  运营主体
     * @param memberGradeGuid 会员等级Guid
     * @param equitiesRuleType 权益规则类型 {@link EquitiesRuleTypeEnum}
     * @return 翻倍成长值权益的guid
     */
    gradeEquitiesVo getGradeEquitiesGuid(@Param("operSubjectGuid") String operSubjectGuid,
                                         @Param("memberGradeGuid") String memberGradeGuid,
                                         @Param("equitiesRuleType") Integer equitiesRuleType);

    /**
     * 通过会员等级guid查询，翻倍成长值权益的guid集合
     * @param operSubjectGuid  运营主体
     * @param memberGradeGuid 会员等级Guid
     * @param equitiesRuleType 权益规则类型 {@link EquitiesRuleTypeEnum}
     * @return 翻倍成长值权益的guid
     */
    List<gradeEquitiesVo> getGradeEquitiesGuids(@Param("operSubjectGuid")String operSubjectGuid,
                                                @Param("memberGradeGuids") List<String> memberGradeGuid,
                                                @Param("equitiesRuleType") Integer equitiesRuleType);

    void batchUpdateExpireTime(@Param("expireTimeMap") Map<String, LocalDateTime> expireTimeMap);
}
