package com.holderzone.member.queue.event.processor;

import com.holderzone.member.common.dto.event.SendMessageEvent;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description 发送消息事件处理
 * @date 2021/8/11
 */
@Slf4j
public class SendMessageProcessor extends AbstractProcessor<SendMessageEvent> {


    public SendMessageProcessor(SendMessageEvent event) {
        super(event);
    }

    @Override
    protected String getLogTitle() {
        return "SendMessage";
    }

    @Override
    protected void validate() {
    }

    @Override
    protected void process() {
    }
}
