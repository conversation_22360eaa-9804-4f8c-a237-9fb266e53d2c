package com.holderzone.member.queue.event.processor;


import cn.hutool.core.collection.CollectionUtil;
import com.google.common.base.Verify;
import com.holderzone.member.common.dto.event.SendMemberGradeChangeEvent;
import com.holderzone.member.common.util.transaction.SpringContextUtils;
import com.holderzone.member.queue.dto.grade.MemberGradeChangeDTO;
import com.holderzone.member.queue.entity.HsaOperationMemberInfo;
import com.holderzone.member.queue.service.grade.HsaMemberGradeChangeDetailService;
import com.holderzone.member.queue.service.member.HsaOperationMemberInfoService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 会员等级变化刷新
 *
 * <AUTHOR>
 * @version 1.0
 * @className GrowthSendBackRightsProcessor
 * @date 2022/01/04 14:33
 * @description 会员等级变化刷新
 * @program holder-member-queue
 */
@Slf4j
public class MemberGradeChangeProcessor extends AbstractProcessor<SendMemberGradeChangeEvent> {

    private final HsaMemberGradeChangeDetailService hsaMemberGradeChangeDetailService;
    private final HsaOperationMemberInfoService hsaOperationMemberInfoService;

    public MemberGradeChangeProcessor(SendMemberGradeChangeEvent event) {
        super(event);
        hsaMemberGradeChangeDetailService = SpringContextUtils.getBean(HsaMemberGradeChangeDetailService.class);
        hsaOperationMemberInfoService = SpringContextUtils.getBean(HsaOperationMemberInfoService.class);
    }

    @Override
    protected String getLogTitle() {
        return "SendMemberGradeChangeEvent";
    }

    @Override
    protected void validate() {
        Verify.verify(event != null);
    }


    @Override
    protected void process() {
        //todo 有毒
        List<HsaOperationMemberInfo> hsaOperationMemberInfos = hsaOperationMemberInfoService.checkMemberGuid(event.getMemberGuidList());
        if (CollectionUtil.isNotEmpty(hsaOperationMemberInfos)) {
            MemberGradeChangeDTO changeDTO = new MemberGradeChangeDTO();
            changeDTO.setOperSubjectGuid(event.getOperSubjectGuid());
            changeDTO.setSourceType(event.getSourceType());
            changeDTO.setIsRefresh(event.getIsRefresh());
            changeDTO.setRoleType(event.getRoleType());
            changeDTO.setSystem(null);
            changeDTO.setMemberInfoGradeGuid(null);
            changeDTO.setUnit(null);
            changeDTO.setNum(null);
            changeDTO.setHsaOperationMemberInfos(hsaOperationMemberInfos);
            hsaMemberGradeChangeDetailService.memberGradeChange(changeDTO);
        }
    }
}
