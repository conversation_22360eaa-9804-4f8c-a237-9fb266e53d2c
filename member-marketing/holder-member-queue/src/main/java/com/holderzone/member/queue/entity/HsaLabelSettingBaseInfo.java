package com.holderzone.member.queue.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.member.common.enums.growth.DataUnitEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 会员标签基础信息表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "hsa_label_setting_base_info")
public class HsaLabelSettingBaseInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 标签设置GUID
     */
    private String labelSettingGuid;

    /**
     * 值为1时是男性，值为2时是女性，值为0时是未知，多个用英文逗号隔开
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String sex;

    /**
     * 设置基础信息,0未设置,1已设置
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Boolean setBaseInfo;

    /**
     * 出生时间类型,0按年月日，1月份
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer birthdayTimeType;

    /**
     * 生日月范围，多个用英文逗号分隔
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String birthdayRangeMonth;

    /**
     * 生日日期范围JSON.eg:[{startDate:xxx,endDate:yyy},...]
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String birthdayRangeDateJson;

    /**
     * 电子邮箱，0存在，1不存在
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer email;

    /**
     * 年龄范围JSON
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String ageRange;

    /**
     * 所属部门，多个用英文逗号分隔
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String departmentName;

    /**
     * 所属单位，多个用英文逗号分隔
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String workName;

    /**
     * 设置注册信息,0未设置,1已设置
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Boolean setRegisterInfo;

    /**
     * 注册时间类型，0相对时间，1固定时间
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer registerTimeType;

    /**
     * 注册相对时间天
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer registerRelativeTimeDay;

    /**
     * 注册相对时间符号，0等于，1大于，2小于
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer registerRelativeTimeSymbol;

    /**
     * 注册固定时间范围JSON
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String registerFixedTimeRangeJson;

    /**
     * 注册门店，多个用因为逗号隔开
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String registerStores;

    /**
     * 注册会员注册来源,0后台添加,1POS机注册,2一体机注册,3后台导入，微网站(21微信扫码点餐，24微信注册.25微信C端后台注册)，
     * 微信小程序(51和惠多，52翼惠天下，53赚餐)，多个用英文逗号隔开
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String registerSourceType;

    /**
     * 是否绑定微信，0未绑定，1绑定
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer isBindWechat;

    /**
     * 证件类型，多个用英文逗号分隔
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String memberCardType;

    /**
     * 证件类型，多个用逗号隔开
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String certificateType;

    /**
     * 注册时间类型，0相对时间，1固定时间
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer openCardTimeType;

    /**
     * 主卡相对时间天
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer cardRelativeTimeDay;

    /**
     * 主卡相对时间符号，0等于，1大于，2小于
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer cardRelativeTimeSymbol;

    /**
     * 区域json
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String labelAreaJson;

    /**
     * 主卡固定时间范围JSON
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String cardFixedTimeRangeJson;

    /**
     * 主卡开卡渠道,0后台添加,1POS机注册,2一体机注册,3后台导入，微网站(21微信扫码点餐，\n24微信注册.25微信C端后台注册)，
     * 微信小程序(51和惠多，52翼惠天下，53赚餐)，多个用英文逗号隔开
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String cardSourceType;


    /**
     * 身份会员卡开卡时间类型，0相对时间，1固定时间
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer openEquityCardTimeType;

    /**
     * 身份会员卡信息,0未设置,1已设置
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Boolean setEquityCardInfo;

    /**
     * 会员卡相对时间符号，0等于，1大于，2小于
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer equityCardDueDateSymbol;

    /**
     * 身份会员卡多个用逗号隔开
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String ownedEquityCardJson;

    /**
     * 会员卡相对时间天
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer equityCardRelativeTimeDay;

    /**
     * 身份会员卡开卡渠道
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String equityCardSourceType;

    /**
     * 身份会员卡固定时间范围JSON
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String equityCardFixedTimeRangeJson;

    /**
     * 充值门店，多个用逗号隔开
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String rechargeStore;

    /**
     * 设置充值信息,0未设置,1已设置
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer setRechargeInfo;


    /**
     * 当前最小实充金额
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal minRechargeAmount;

    /**
     * 最小赠送金额
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal minGiftAmount;

    /**
     * 当前最大实充金额
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal maxRechargeAmount;

    /**
     * 最大赠送金额
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal maxGiftAmount;

    /**
     * 累计充值金额时间
     * 根据类型存储数据
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String cumulativeRechargeTime;

    /**
     * 累计充值金额时间类型（1：注册至今、2：最近一段时间、3固定时间）
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer cumulativeRechargeType;

    /**
     * 最小累计充值金额
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal minCumulativeRechargeAmount;

    /**
     * 累计充值金额次数类型（1：注册至今、2：最近一段时间、3固定时间）
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer cumulativeRechargeNumType;

    /**
     * 最大累计充值金额
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal maxCumulativeRechargeAmount;

    /**
     * 累计充值金额次数时间
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String cumulativeRechargeNumTime;

    /**
     * 最大累计充值次数
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer maxCumulativeRechargeNum;

    /**
     * 最小累计充值次数
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer minCumulativeRechargeNum;

    /**
     * 平均充值金额类型（1：注册至今、2：最近一段时间、3固定时间）
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer averageRechargeType;

    /**
     * 最小平均充值金额
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal minAverageRechargeAmount;

    /**
     * 平均充值金额时间
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String averageRechargeTime;

    /**
     * 最大平均充值金额
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal maxAverageRechargeAmount;

    /**
     * 消费门店，多个用因为逗号隔开
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String consumptionStoreJson;

    /**
     * 设置消费行为,0未设置,1已设置
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Boolean setConsumptionInfo;

    /**
     * 最小累计消费金额
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal minConsumptionAmount;

    /**
     * 累计消费金额类型 1：注册至今 2：最近一段时间 3：固定时间
     *
     * @see com.holderzone.member.common.enums.member.LabelConsumptionTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer consumptionAmountType;

    /**
     * 最大累计消费金额
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal maxConsumptionAmount;

    /**
     * 累计消费金额固定时间json，多个用因为逗号隔开
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String consumptionAmountFixedDateJson;

    /**
     * 累计消费金额最近多少天
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer consumptionAmountRecentDay;

    /**
     * 最大累计消费次数
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer maxConsumptionCount;

    /**
     * 累计消费次数类型 1：注册至今 2：最近一段时间 3：固定时间
     *
     * @see com.holderzone.member.common.enums.member.LabelConsumptionTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer consumptionCountType;

    /**
     * 最小累计消费次数
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer minConsumptionCount;

    /**
     * 累计消费次数最近多少天
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer consumptionCountRecentDay;

    /**
     * 累计消费均价类型 1：注册至今 2：最近一段时间 3：固定时间
     *
     * @see com.holderzone.member.common.enums.member.LabelConsumptionTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer consumptionAvgType;

    /**
     * 累计消费次数固定时间json
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String consumptionCountFixedDateJson;

    /**
     * 累计消费均价最近多少天
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer consumptionAvgRecentDay;

    /**
     * 最小累计消费均价
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal minConsumptionAvg;

    /**
     * 最大累计消费均价
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal maxConsumptionAvg;

    /**
     * 累计消费均价固定时间json
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String consumptionAvgFixedDateJson;


    /**
     * 设置会员卡信息,0未设置,1已设置
     */
    private Boolean setGrowthInfo;

    /**
     * 最小当前成长值
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer minCurrentGrowValue;

    /**
     * 会员等级GUID，多个用英文逗号隔开
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String cardMemberLevelGuid;

    /**
     * 最大当前成长值
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer maxCurrentGrowValue;

    /**
     * 累计成长值类型 1：注册至今 2：最近一段时间 3：固定时间
     *
     * @see com.holderzone.member.common.enums.member.LabelConsumptionTypeEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer growValueType;

    /**
     * 最大累计成长值
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal maxTotalGrowValue;

    /**
     * 最小累计成长值
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer minTotalGrowValue;

    /**
     * 最近多少天累计成长值
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer growValueRecentDay;

    /**
     * 累计成长值固定时间json，多个用因为逗号隔开
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String growValueFixedDateJson;

    /**
     * 成长信息-付费等级（可多选，英文逗号分隔）
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String paidMemberLevelGuid;

    /**
     * 成为付费会员时间范围-开始（格式如 yyyy-MM-dd）
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String paidMemberStartDateFrom;

    /**
     * 成为付费会员时间范围-结束（格式如 yyyy-MM-dd）
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String paidMemberStartDateTo;

    /**
     * 成为付费会员时长-最小值
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer paidMemberDurationMin;

    /**
     * 成为付费会员时长-最大值
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer paidMemberDurationMax;

    /**
     * 成为付费会员时长-单位（如"天"、"月"、"年"）
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private DataUnitEnum paidMemberDurationUnit;

    /**
     * 距离会员到期时间-最小值
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer paidMemberExpireRemainMin;

    /**
     * 距离会员到期时间-最大值
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer paidMemberExpireRemainMax;

    /**
     * 距离会员到期时间-单位（如"天"、"月"、"年"）
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private DataUnitEnum paidMemberExpireRemainUnit;

}



