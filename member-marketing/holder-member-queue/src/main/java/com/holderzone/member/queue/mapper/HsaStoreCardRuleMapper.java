package com.holderzone.member.queue.mapper;


import com.holderzone.member.queue.config.mybatis.HolderBaseMapper;
import com.holderzone.member.queue.entity.HsaStoreCardRule;

/**
 * <p>
 * 会员卡门店适用表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-12
 */
public interface HsaStoreCardRuleMapper extends HolderBaseMapper<HsaStoreCardRule> {


//    /**
//     * 根据会员卡获取适用门店
//     *
//     * @param request 请求参数
//     * @return 返回结果
//     */
//    List<StoreBaseInfo> findStoreCardRule(@Param("request") MemberCardStoreDataQO request);


}
