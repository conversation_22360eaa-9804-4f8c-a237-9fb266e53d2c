package com.holderzone.member.queue.mapper;


import com.holderzone.member.common.dto.growth.ClassifyGoodsDTO;
import com.holderzone.member.queue.config.mybatis.HolderBaseMapper;
import com.holderzone.member.queue.entity.HsaGrowthValueTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * author: pantao
 */
public interface HsaGrowthValueTaskMapper extends HolderBaseMapper<HsaGrowthValueTask> {

    List<ClassifyGoodsDTO> listAppointGoodsType(@Param("taskGuid") String taskGuid, @Param("storeGuid") String storeGuid, @Param("businessType") String businessType);
}
