package com.holderzone.member.queue.config;

import com.holderzone.member.queue.service.grade.HsaMemberGradeChangeDetailService;
import com.holderzone.member.queue.service.grade.impl.HsaMemberGradeChangeDetailServiceOptimizedImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 优化服务配置类
 * 
 * 通过配置文件控制是否使用优化版本的服务实现
 * 
 * <AUTHOR>
 * @since 2025-01-18
 */
@Configuration
@Slf4j
public class OptimizedServiceConfig {

    /**
     * 优化版本的会员等级变化服务
     * 
     * 通过配置 member.grade.service.optimized=true 启用
     * 默认情况下使用优化版本
     */
    @Bean("hsaMemberGradeChangeDetailServiceOptimized")
    @Primary
    @ConditionalOnProperty(
            name = "member.grade.service.optimized", 
            havingValue = "true", 
            matchIfMissing = true
    )
    public HsaMemberGradeChangeDetailService optimizedGradeChangeService() {
        log.info("启用优化版本的会员等级变化服务");
        return new HsaMemberGradeChangeDetailServiceOptimizedImpl();
    }

    /**
     * 批处理配置
     */
    @Bean
    public GradeChangeOptimizedProperties gradeChangeOptimizedProperties() {
        return new GradeChangeOptimizedProperties();
    }

    /**
     * 优化配置属性类
     */
    public static class GradeChangeOptimizedProperties {
        
        /**
         * 批处理大小，默认500
         */
        private int batchSize = 500;
        
        /**
         * 最大重试次数，默认3次
         */
        private int maxRetryTimes = 3;
        
        /**
         * 分布式锁超时时间（秒），默认30秒
         */
        private int lockTimeout = 30;
        
        /**
         * 是否启用异步处理，默认true
         */
        private boolean asyncEnabled = true;
        
        /**
         * 是否启用性能监控，默认true
         */
        private boolean performanceMonitorEnabled = true;

        // Getters and Setters
        public int getBatchSize() {
            return batchSize;
        }

        public void setBatchSize(int batchSize) {
            this.batchSize = batchSize;
        }

        public int getMaxRetryTimes() {
            return maxRetryTimes;
        }

        public void setMaxRetryTimes(int maxRetryTimes) {
            this.maxRetryTimes = maxRetryTimes;
        }

        public int getLockTimeout() {
            return lockTimeout;
        }

        public void setLockTimeout(int lockTimeout) {
            this.lockTimeout = lockTimeout;
        }

        public boolean isAsyncEnabled() {
            return asyncEnabled;
        }

        public void setAsyncEnabled(boolean asyncEnabled) {
            this.asyncEnabled = asyncEnabled;
        }

        public boolean isPerformanceMonitorEnabled() {
            return performanceMonitorEnabled;
        }

        public void setPerformanceMonitorEnabled(boolean performanceMonitorEnabled) {
            this.performanceMonitorEnabled = performanceMonitorEnabled;
        }
    }
}
