package com.holderzone.member.queue.service.member.impl;

import com.holderzone.member.queue.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.queue.entity.HsaGrowthCommodityQuantityTouchDetail;
import com.holderzone.member.queue.mapper.HsaGrowthCommodityQuantityTouchDetailMapper;
import com.holderzone.member.queue.service.member.HsaGrowthCommodityQuantityTouchDetailService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 成长值任务商品数量累计赠送
 * @date 2021/12/11 16:05
 */
@Service
public class HsaGrowthCommodityQuantityTouchDetailServiceImpl extends HolderBaseServiceImpl<HsaGrowthCommodityQuantityTouchDetailMapper, HsaGrowthCommodityQuantityTouchDetail> implements HsaGrowthCommodityQuantityTouchDetailService {
}
