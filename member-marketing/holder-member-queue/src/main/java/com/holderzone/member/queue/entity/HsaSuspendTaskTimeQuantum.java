package com.holderzone.member.queue.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @program: member-marketing
 * @description: 成长值任务、积分任务暂停时间段
 * @author: pan tao
 * @create: 2022-08-02 15:30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaSuspendTaskTimeQuantum implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * guid
     */
    private String guid;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务类型 0：成长值 1：积分
     * @see com.holderzone.member.common.enums.growth.GrowthValueOrIntegralEnum
     */
    private Integer taskType;

    /**
     * 任务guid
     */
    private String taskGuid;

    /**
     * 暂停开始时间
     */
    private LocalDateTime suspendStartTime;

    /**
     * 暂停结束时间
     */
    private LocalDateTime suspendEndTime;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 类型 1：消费 2：充值
     */
    private Integer type;

}
