package com.holderzone.member.queue.mapper;


import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.queue.entity.HsaCardOpenRule;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 会员卡开卡规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-31
 */
public interface HsaCardOpenRuleMapper extends HolderBaseMapper<HsaCardOpenRule> {

    /**
     * 批量更新状态
     *
     * @param activityList 活动列表
     */
    void batchUpdateStatus(@Param("list") List<HsaCardOpenRule> activityList);

    List<HsaCardOpenRule> selectOpenCardList(@Param("now") String now);

}
