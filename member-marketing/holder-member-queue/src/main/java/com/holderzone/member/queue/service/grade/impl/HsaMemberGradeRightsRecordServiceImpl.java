package com.holderzone.member.queue.service.grade.impl;


import com.holderzone.member.queue.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.queue.entity.HsaMemberGradeRightsRecord;
import com.holderzone.member.queue.mapper.HsaMemberGradeRightsRecordMapper;
import com.holderzone.member.queue.service.grade.HsaMemberGradeRightsRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 会员等级权益记录
 * @date 2022/01/04 16:05
 */
@Service
@Slf4j
public class HsaMemberGradeRightsRecordServiceImpl extends HolderBaseServiceImpl<HsaMemberGradeRightsRecordMapper, HsaMemberGradeRightsRecord> implements HsaMemberGradeRightsRecordService {

}
