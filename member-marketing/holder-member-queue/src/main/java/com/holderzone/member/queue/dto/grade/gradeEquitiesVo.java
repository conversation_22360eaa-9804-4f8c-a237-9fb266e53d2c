package com.holderzone.member.queue.dto.grade;


import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * @description: 等级权益DTO
 * @author: z<PERSON><PERSON>
 * @create: 2021-12-31 11:15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class gradeEquitiesVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 权益guid
     */
    private String equitiesGuid;

    /**
     * 成长值翻倍倍数
     */
    private BigDecimal doubleNumber;



}
