package com.holderzone.member.queue.service.grade.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.member.common.dto.grade.MemberGradeRelationDTO;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.queue.entity.HsaMemberGradeInfo;
import com.holderzone.member.queue.entity.HsaMemberGradeRelation;
import com.holderzone.member.queue.mapper.HsaMemberGradeInfoMapper;
import com.holderzone.member.queue.mapper.HsaMemberGradeRelationMapper;
import com.holderzone.member.queue.service.grade.IHsaMemberGradeRelationService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 会员和等级关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Service
@AllArgsConstructor
public class HsaMemberGradeRelationServiceImpl extends ServiceImpl<HsaMemberGradeRelationMapper, HsaMemberGradeRelation>
        implements IHsaMemberGradeRelationService {

    private GuidGeneratorUtil guidGeneratorUtil;

    private HsaMemberGradeInfoMapper memberGradeInfoMapper;

    @Override
    public List<HsaMemberGradeRelation> listByMemberGuidList(List<String> memberGuidList, String roleType) {
        return this.list(new LambdaQueryWrapper<HsaMemberGradeRelation>()
                .in(HsaMemberGradeRelation::getMemberInfoGuid, memberGuidList)
                .eq(HsaMemberGradeRelation::getIsDelete, BooleanEnum.FALSE.getCode())
                .eq(HsaMemberGradeRelation::getRoleType, roleType));
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchSaveAndUpdateRelation(MemberGradeRelationDTO relationDTO) {
        List<HsaMemberGradeRelation> updateList = this.listByMemberGuidList(relationDTO.getMemberGuidList(), relationDTO.getRoleType());
        if (!CollectionUtils.isEmpty(updateList)) {
            updateList.forEach(relation -> relation.setIsEnable(Boolean.FALSE));
            this.updateBatchById(updateList);
        }
        HsaMemberGradeInfo memberGradeInfo = memberGradeInfoMapper.selectOne(
                new LambdaQueryWrapper<HsaMemberGradeInfo>()
                        .eq(HsaMemberGradeInfo::getGuid, relationDTO.getMemberInfoGradeGuid())
                        .eq(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode()));
        LocalDateTime payTime = LocalDateTime.now();
        List<HsaMemberGradeRelation> relationList = new ArrayList<>();
        relationDTO.getMemberGuidList().forEach(member -> {
            HsaMemberGradeRelation queueRelation = new HsaMemberGradeRelation();
            queueRelation.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberGradeRelation.class.getSimpleName()));
            queueRelation.setOperSubjectGuid(relationDTO.getOperSubjectGuid());
            queueRelation.setMemberInfoGuid(member);
            queueRelation.setExpireTime(DateUtil.getFirstExpireTime(relationDTO.getNum(), relationDTO.getUnit()));
            queueRelation.setPayTime(payTime);
            queueRelation.setMemberInfoGradeGuid(relationDTO.getMemberInfoGradeGuid());
            queueRelation.setRoleType(relationDTO.getRoleType());
            queueRelation.setUnit(relationDTO.getUnit());
            queueRelation.setNum(relationDTO.getNum());
            queueRelation.setTemporaryVipGrade(memberGradeInfo.getTemporaryVipGrade());
            queueRelation.setSourceType(relationDTO.getSourceType());
            queueRelation.setIsEnable(Boolean.TRUE);
            relationList.add(queueRelation);
        });
        this.saveBatch(relationList);
    }

    @Override
    public void batchDeleteMemberGrade(List<String> memberGuidList, String roleType) {
        baseMapper.delete(new LambdaQueryWrapper<HsaMemberGradeRelation>()
                .in(HsaMemberGradeRelation::getMemberInfoGuid, memberGuidList)
                .eq(HsaMemberGradeRelation::getIsDelete, Boolean.FALSE)
                .eq(HsaMemberGradeRelation::getRoleType, roleType));
    }

    @Override
    public HsaMemberGradeInfo queryMemberCurrentGrade(MemberGradeRelationDTO relationDTO) {
        HsaMemberGradeRelation gradeRelation = this.getOne(new LambdaQueryWrapper<HsaMemberGradeRelation>()
                .eq(HsaMemberGradeRelation::getMemberInfoGuid, relationDTO.getMemberGuidList().get(0))
                .eq(HsaMemberGradeRelation::getRoleType, relationDTO.getRoleType())
                .eq(HsaMemberGradeRelation::getIsDelete, Boolean.FALSE)
                .eq(HsaMemberGradeRelation::getIsEnable, Boolean.TRUE)
        );
        if (ObjectUtils.isEmpty(gradeRelation)) {
            return null;
        }
        return memberGradeInfoMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                .eq(HsaMemberGradeInfo::getGuid, gradeRelation.getMemberInfoGradeGuid())
                .eq(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode()));
    }

    @Override
    public void batchUpdateExpireTime(Map<String, LocalDateTime> expireTimeMap){
        if(!expireTimeMap.isEmpty()) {
            memberGradeInfoMapper.batchUpdateExpireTime(expireTimeMap);
        }
    }
}
