package com.holderzone.member.queue.dto.activity;

import com.holderzone.member.queue.entity.HsaMemberFundingDetail;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class CreationSubsidyDetailRecordDTO {

    private String error;

    private LocalDateTime now;

    private Integer result;

    private LocalDateTime outTime;

    private String memberInfoCardGuid;

    private HsaMemberFundingDetail hsaMemberFundingDetail;

    private BigDecimal amount;

}
