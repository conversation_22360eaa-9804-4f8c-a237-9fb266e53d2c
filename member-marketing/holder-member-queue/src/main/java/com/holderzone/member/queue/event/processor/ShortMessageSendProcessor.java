package com.holderzone.member.queue.event.processor;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Verify;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.event.AbstractProcessor;
import com.holderzone.member.common.dto.event.PushSettlementDiscountEvent;
import com.holderzone.member.common.dto.event.PushShortMessageEvent;
import com.holderzone.member.common.feign.IPaasFeign;
import com.holderzone.member.common.util.transaction.SpringContextUtils;
import com.holderzone.member.common.vo.feign.IPaasFeignModel;
import lombok.extern.slf4j.Slf4j;
/**
 * 推送
 *
 */
@Slf4j
public class ShortMessageSendProcessor extends AbstractProcessor<PushShortMessageEvent> {

    private final IPaasFeign iPaasFeign;

    public ShortMessageSendProcessor(PushShortMessageEvent event) {
        super(event);
        this.iPaasFeign = SpringContextUtils.getBean(IPaasFeign.class);
    }

    @Override
    protected String getLogTitle() {
        return "ShortMessageSendProcessor";
    }

    @Override
    protected void validate() {
        Verify.verify(event != null);
    }


    @Override
    protected void process() {
        ThreadLocalCache.put(JacksonUtils.writeValueAsString(event.getUserInfo()));
        log.info("短信消息发送参数：{}", JSON.toJSONString(event.getMessagesSend()));

        IPaasFeignModel<String> modelResult = iPaasFeign.sendShortMessage(event.getMessagesSend());

        log.info("短信消息发送结果：{}", JSON.toJSONString(modelResult));
    }
}
