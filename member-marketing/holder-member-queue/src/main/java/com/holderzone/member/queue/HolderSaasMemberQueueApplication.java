package com.holderzone.member.queue;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;

@EnableFeignClients(basePackages = "com.holderzone.member.common.feign")
@ComponentScans(value = {
        @ComponentScan("com.holderzone.member.queue.*"),
        @ComponentScan("com.holderzone.member.common.external"),
        @ComponentScan("com.holderzone.member.common.config"),
        @ComponentScan("com.holderzone.member.common.client"),
        @ComponentScan("com.holderzone.member.common.util.number"),
        @ComponentScan("com.holderzone.member.common.util.replace"),
        @ComponentScan("com.holderzone.member.common.util.transaction"),
        @ComponentScan("com.holderzone.member.common.handler"),
        @ComponentScan("com.holderzone.member.common.config.mybatis")
})
@MapperScan(basePackages = "com.holderzone.member.queue.mapper")
@SpringBootApplication
public class HolderSaasMemberQueueApplication {

    public static void main(String[] args) {
        SpringApplication.run(HolderSaasMemberQueueApplication.class, args);
    }

}
