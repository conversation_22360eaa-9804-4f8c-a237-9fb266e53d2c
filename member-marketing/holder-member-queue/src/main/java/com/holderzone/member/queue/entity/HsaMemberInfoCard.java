package com.holderzone.member.queue.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 会员关联会员卡实体
 * @date 2021/8/31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberInfoCard implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 会员持卡GUID
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 企业GUID
     */
    private String enterpriseGuid;


    /**
     * 会员GUID
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String memberInfoGuid;


    /**
     * 实体卡GUID
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String physicalCardGuid;

    /**
     * 电子卡GUID
     */
    private String electronicCardGuid;


    /**
     * 会员卡GUID
     */
    private String cardGuid;

    /**
     * 卡名称
     */
    private String cardName;


    /**
     * 补贴余额
     */
    private BigDecimal subsidyAmount;

    /**
     * 可退补贴余额
     */
    private BigDecimal retreatSubsidyAmount;

    /**
     * 充值余额
     */
    private BigDecimal cardAmount;

    /**
     * 赠送余额
     */
    private BigDecimal giftAmount;

    /**
     * 超额使用余额
     */
    private BigDecimal excessAmount;

    /**
     * 超额使用次数
     */
    private Integer excessTimes;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    /**
     * 0永久有效1固定时间
     */
    private Integer cardValidity;

    private LocalDate cardValidityDate;

    /**
     * 是否适用于所有门店(1:全部门店；0:部分门店(外关联表))
     */
    private Integer applicableAllStore;

    /**
     * 卡颜色
     */
    private String cardColor;

    /**
     * 卡图片
     */
    private String cardImage;

    /**
     * 充值说明
     */
    private String cardRechargeExplain;

    /**
     * 使用须知
     */
    private String cardEmployExplain;

    /**
     * 卡默认支付密码
     */
    private String cardPayPassword;

    /**
     * 是否支持实体卡
     */
    private Integer isSupportPhysicalCard;


    /**
     * 实体卡是否可退（实体卡相关）
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer isPhysicalCardRetreat;

    /**
     * 是否是默认卡 1：是
     */
    private Integer defaultCard;

    /**
     * 电子卡号
     */
    private String electronicCardNum;

    /**
     * 实体卡号
     */
    private String physicalCardNum;

    /**
     * 绑定账户手机号
     */
    private String memberPhoneNum;

    /**
     * 实体卡激活时间
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime physicalCardActivationTime;

    /**
     * 实体卡状态 -1 未激活  0已冻结 1 正常 2 已过期 3 未激活->已冻结
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer physicalCardState;

    /**
     * 电子卡状态 0已冻结 1 正常 2 已过期
     */
    private Integer electronicCardState;

    /**
     * 电子卡开卡时间
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime electronicOpenTime;

    /**
     * 付款账户默认选择
     */
    private Integer defaultChoose;
}
