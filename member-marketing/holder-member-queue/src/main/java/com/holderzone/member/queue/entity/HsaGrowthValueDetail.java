package com.holderzone.member.queue.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 成长值明细表
 * @date 2021/11/23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "hsa_member_growth_value_detail")
public class HsaGrowthValueDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 账户成长值明细GUID
     */
    private String guid;

    /**
     * 成长值明细id
     */
    private Long id;


    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 成长值任务id
     */
    private String growthValueTaskId;

    /**
     * 会员GUID
     */
    private String memberInfoGuid;


    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 记录说明
     */
    private String recordDeclaration;

    /**
     * 任务编号
     */
    private String taskNumber;

    /**
     * 任务完成第次数
     */
    private Integer taskFinishTime;

    /**
     * 成长值
     */
    private Integer growthValue;

    /**
     * 成长值变化类型,0增加,1减少
     */
    private Integer growthValueType;

    /**
     * 翻倍成长值权益（翻了多少倍）
     */
    private BigDecimal multipleGrowth;

    /**
     * 翻倍增加的成长值
     */
    private Integer multipleGrowthValue;

    /**
     * 等级翻倍权益guid
     */
    private String multipleEquitiesGuid;

    /**
     * 会员卡翻倍成长值权益（翻了多少倍）
     */
    private BigDecimal multipleCardGrowth;

    /**
     * 会员卡翻倍增加的成长值
     */
    private Integer multipleCardGrowthValue;

    /**
     * 会员卡等级翻倍权益guid
     */
    private String multipleCardEquitiesGuid;

    /**
     * 0永久有效1固定时间
     */
    private Integer growthValidity;

    /**
     * 任务完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime taskFinishDate;

    /**
     * 失效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime growthValidityDate;

    /**
     * 剩余成长值
     */
    private Integer remainGrowthValue;

    /**
     * 成长值来源
     */
    private Integer growthValueSourceType;

    /**
     * 当前记录剩余成长值
     */
    private Integer recordRemainGrowthValue;

    /**
     * 会员消费记录GUID
     */
    private String memberConsumptionGuid;

    /**
     * 操作人员账号
     */
    private String operatorAccount;

    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 操作人员账号名字
     */
    private String operatorAccountName;

    /**
     * 门店来源名称
     */
    private String storeName;

    /**
     * 门店来源guid
     */
    private String storeGuid;

    /**
     * 成长值变动类型（1初始成长值，2系统调整，3成长值失效，4消费退款，5成长值任务，6等级权益
     * @see com.holderzone.member.common.enums.growth.SumValueChangeEnum
     */
    private Integer changeType;

    /**
     * 当前会员等级
     */
    private String currentMemberLevel;

    /**
     * 任务动作 0:注册 1:完善个人信息 2:单笔消费金额 3:消费指定商品 4:累计消费金额 5:累计消费笔数 6:单笔充值金额 7:累计充值金额
     *
     * @see com.holderzone.member.common.enums.growth.TaskActionEnum
     */
    private Integer taskAction;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 角色类型
     *
     * @see com.holderzone.member.common.enums.member.RoleTypeEnum
     */
    private String roleType;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

    /**
     * 到期时间
     */
    private LocalDateTime expireTime;

    /**
     * 会员等级GUID
     */
    private String memberInfoGradeGuid;

    /**
     * 有效期单位：3月 4年
     */
    private Integer unit;

    /**
     * 升级机制：1成长值升级 2付费升级
     *
     * @see com.holderzone.member.common.enums.grade.UpgradeTypeEnum
     */
    private Integer upgradeType;

    /**
     * 有效期数量
     */
    private Integer num;

}
