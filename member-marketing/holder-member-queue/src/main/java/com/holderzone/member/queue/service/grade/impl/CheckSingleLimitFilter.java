package com.holderzone.member.queue.service.grade.impl;

import com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum;
import com.holderzone.member.queue.dto.grade.DoubleValueRequest;
import com.holderzone.member.queue.entity.HsaBusinessEquities;
import com.holderzone.member.queue.entity.HsaMemberEquitiesReceiveRecord;
import com.holderzone.member.queue.service.grade.AbstractHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
@Order(3) //顺序排第1，最先校验
@Slf4j
public class CheckSingleLimitFilter extends AbstractHandler {

    @Override
    public boolean doFilter(DoubleValueRequest request, HsaBusinessEquities hsaBusinessEquities,
                            HsaMemberEquitiesReceiveRecord equitiesReceiveRecord) {
        Optional<HsaBusinessEquities> gradeEquities = Optional.of(hsaBusinessEquities);
        //翻倍成长值倍数
        BigDecimal doubleNumber = gradeEquities.map(HsaBusinessEquities::getGradeValueDoubleNumber).orElse(new BigDecimal(1));
        //任务成长值
        Integer growthValue = request.getGrowthValue();
        //用户剩余可以的累计翻倍值
        Integer remainingValue = request.getRemainingValue();
        //翻倍成长值
        int doubleValue = 0;
        doubleValue = doubleNumber.multiply(new BigDecimal(growthValue)).intValue();
        doubleValue = doubleValue - growthValue;

        //成长值单次翻倍限制 0:不限制 1:限制
        Integer singleLimit = gradeEquities
                .map(HsaBusinessEquities::getSingleDoubleLimited).orElse(EquitiesLimitedTypeEnum.UN_LIMITED.getCode());
        if (EquitiesLimitedTypeEnum.UN_LIMITED.getCode() == singleLimit) {
            if (Objects.isNull(remainingValue) || remainingValue > doubleValue) {
                request.setDoubleValue((long) doubleValue);
            } else {
                request.setDoubleValue((long) remainingValue);
            }
        } else {
            //翻倍成长值，单次不能超过的上限
            Integer singleValueLimit = gradeEquities.map(HsaBusinessEquities::getSingleDoubleUpperLimit).orElse(0);
            log.info("翻倍成长值倍数:{},任务成长值:{},用户剩余可以的累计翻倍值{},翻倍后成长值:{},单倍最大可以获取的成长值：{}"
                    ,doubleNumber,growthValue,remainingValue,doubleValue,singleValueLimit);
            //翻倍成长值，不能超过累计翻倍成长值
            if (Objects.nonNull(remainingValue) && remainingValue < singleValueLimit) {
                singleValueLimit = remainingValue;
            }
            if (singleValueLimit > doubleValue) {
                request.setDoubleValue((long) doubleValue);
            } else {
                request.setDoubleValue((long) singleValueLimit);
            }
        }
        log.info("用户：{} 使用了翻倍成长值权益,翻倍了：{}成长值", request.getMemberInfoGuid(),request.getDoubleValue());
        return false;
    }

}
