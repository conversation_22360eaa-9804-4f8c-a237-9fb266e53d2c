package com.holderzone.member.queue.service.grade;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.common.dto.grade.MemberGradeRelationDTO;
import com.holderzone.member.queue.entity.HsaMemberGradeInfo;
import com.holderzone.member.queue.entity.HsaMemberGradeRelation;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 会员和等级关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
public interface IHsaMemberGradeRelationService extends IService<HsaMemberGradeRelation> {

    List<HsaMemberGradeRelation> listByMemberGuidList(List<String> memberGuidList, String roleType);

    void batchSaveAndUpdateRelation(MemberGradeRelationDTO relationDTO);

    void batchDeleteMemberGrade(List<String> memberGuidList, String roleType);

    HsaMemberGradeInfo queryMemberCurrentGrade(MemberGradeRelationDTO relationDTO);

    void batchUpdateExpireTime(Map<String, LocalDateTime> expireTimeMap);
}
