package com.holderzone.member.queue.utils;

import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.enums.growth.DataUnitEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * date util
 */
public class DateUtil {

    // ---------------------- format parse ----------------------
    private static Logger logger = LoggerFactory.getLogger(DateUtil.class);

    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String MIN_TIME = "T00:00:00";
    public static final String MAX_TIME = "T23:59:59.999999999";

    public static final String DATE_00_FORMAT = "yyyy-MM-dd 00:00:00";

    private static final ThreadLocal<Map<String, DateFormat>> DATE_FORMAT_THREAD_LOCAL = new ThreadLocal<>();

    private static DateFormat getDateFormat(String pattern) {
        if (pattern == null || pattern.trim().length() == 0) {
            throw new IllegalArgumentException("pattern cannot be empty.");
        }

        Map<String, DateFormat> dateFormatMap = DATE_FORMAT_THREAD_LOCAL.get();
        if (dateFormatMap != null && dateFormatMap.containsKey(pattern)) {
            return dateFormatMap.get(pattern);
        }

        synchronized (DATE_FORMAT_THREAD_LOCAL) {
            if (dateFormatMap == null) {
                dateFormatMap = new HashMap<>();
            }
            dateFormatMap.put(pattern, new SimpleDateFormat(pattern));
            DATE_FORMAT_THREAD_LOCAL.set(dateFormatMap);
        }
        DATE_FORMAT_THREAD_LOCAL.remove();
        return dateFormatMap.get(pattern);
    }

    /**
     * format datetime. like "yyyy-MM-dd"
     *
     * @param date
     * @return
     * @throws ParseException
     */
    public static String formatDate(Date date) {
        return format(date, DATE_FORMAT);
    }

    /**
     * format date. like "yyyy-MM-dd HH:mm:ss"
     *
     * @param date
     * @return
     * @throws ParseException
     */
    public static String formatDateTime(Date date) {
        return format(date, DATETIME_FORMAT);
    }

    /**
     * format date
     *
     * @param date
     * @param patten
     * @return
     * @throws ParseException
     */
    public static String format(Date date, String patten) {
        return getDateFormat(patten).format(date);
    }

    /**
     * parse date string, like "yyyy-MM-dd HH:mm:s"
     *
     * @param dateString
     * @return
     * @throws ParseException
     */
    public static Date parseDate(String dateString) {
        return parse(dateString, DATE_FORMAT);
    }

    /**
     * parse datetime string, like "yyyy-MM-dd HH:mm:ss"
     *
     * @param dateString
     * @return
     * @throws ParseException
     */
    public static Date parseDateTime(String dateString) {
        return parse(dateString, DATETIME_FORMAT);
    }

    /**
     * parse date
     *
     * @param dateString
     * @param pattern
     * @return
     * @throws ParseException
     */
    public static Date parse(String dateString, String pattern) {
        try {
            Date date = getDateFormat(pattern).parse(dateString);
            return date;
        } catch (Exception e) {
            logger.warn("parse date error, dateString = {}, pattern={}; errorMsg = ", dateString, pattern, e.getMessage());
            return null;
        }
    }


    // ---------------------- add date ----------------------

    public static Date addDays(final Date date, final int amount) {
        return add(date, Calendar.DAY_OF_MONTH, amount);
    }

    public static Date addYears(final Date date, final int amount) {
        return add(date, Calendar.YEAR, amount);
    }

    public static Date addMonths(final Date date, final int amount) {
        return add(date, Calendar.MONTH, amount);
    }

    /**
     * 添加小时
     *
     * @param date
     * @param amount
     * @return
     */
    public static Date addHours(final Date date, final int amount) {
        return add(date, Calendar.HOUR, amount);
    }

    /**
     * 添加分
     *
     * @param date
     * @param amount
     * @return
     */
    public static Date addMinutes(final Date date, final int amount) {
        return add(date, Calendar.MINUTE, amount);
    }

    private static Date add(final Date date, final int calendarField, final int amount) {
        if (date == null) {
            return null;
        }
        final Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(calendarField, amount);
        return c.getTime();
    }


    public static String getFirstDayOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month - 1);
        //获取某月最小天数
        int firstDay = cal.getMinimum(Calendar.DATE);
        //设置日历中月份的最小天数
        cal.set(Calendar.DAY_OF_MONTH, firstDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(cal.getTime());
    }

    public static String getMinToday() {
        LocalDateTime today_start = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        String today = today_start.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        return today;
    }

    public static String getLastDayOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month - 1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DATE);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(cal.getTime());
    }

    public static String getPreYearDay(String day) {
        //day的格式为YYYY-MM-DD
        String[] dayArr = day.split("-");
        Integer year = Integer.valueOf(dayArr[0]);
        Integer preYear = year - 1;
        String preYearDay = preYear + "-" + dayArr[1] + "-" + dayArr[2];
        return preYearDay;

    }

    public static String getPreYearMonth(String month) {
        String[] dayArr = month.split("-");
        Integer year = Integer.valueOf(dayArr[0]);
        Integer preYear = year - 1;
        String preYearMonth = preYear + "-" + dayArr[1];
        return preYearMonth;

    }

    /**
     * LocalDate转Date
     *
     * @param localDate localDate
     * @return
     */
    public static Date localDate2Date(LocalDate localDate) {
        if (null == localDate) {
            return null;
        }

        ZonedDateTime zonedDateTime = localDate.atStartOfDay(ZoneId.systemDefault());
        return Date.from(zonedDateTime.toInstant());

    }

    /**
     * Date转LocalDate
     *
     * @param date
     */
    public static LocalDate date2LocalDate(Date date) {
        if (null == date) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static void main(String[] args) {
//        String today = getMinToday();
//        System.out.println(today);
//        String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
//        System.out.println(now);
        LocalDateTime sta = getStartOrEndDayOfWeek(LocalDate.now(), true);
        LocalDateTime end = getStartOrEndDayOfWeek(LocalDate.now(), true);
    }

    /**
     * 过期时间计算
     */
    public static LocalDateTime loseDateTimeProcessor(Integer validityType,
                                                      String fixedValueValidityDate,
                                                      Integer dynamicValidityType,
                                                      Integer dynamicValidityNumber,
                                                      LocalDateTime dateTime) {
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        LocalDateTime localDateTime;
        int year = cal.get(Calendar.YEAR);
        if (validityType == 2) {
            //当前月日
            String current = getTmpDate(date, "M-d");
            String[] strCurrent = current.split("-");
            List<String> currentList = new ArrayList<>(Arrays.asList(strCurrent));
            int monthCurrent = Integer.parseInt(currentList.get(0));
            int dayCurrent = Integer.parseInt(currentList.get(1));
            String[] str = fixedValueValidityDate.split("-");
            List<String> resultList = new ArrayList<>(Arrays.asList(str));
            int monthInt = Integer.parseInt(resultList.get(0));
            int dayInt = Integer.parseInt(resultList.get(1));
            if (monthCurrent > monthInt) {
                //明年
                localDateTime = LocalDateTime.of(year + 1, monthInt, dayInt, NumberConstant.NUMBER_23, NumberConstant.NUMBER_59, NumberConstant.NUMBER_59);
            } else if (monthCurrent < monthInt) {
                //今年
                localDateTime = LocalDateTime.of(year, monthInt, dayInt, NumberConstant.NUMBER_23, NumberConstant.NUMBER_59, NumberConstant.NUMBER_59);
            } else {
                //同月则判断天
                if (dayCurrent > dayInt) {
                    //明年
                    localDateTime = LocalDateTime.of(year + 1, monthInt, dayInt, NumberConstant.NUMBER_23, NumberConstant.NUMBER_59, NumberConstant.NUMBER_59);
                } else{
                    //今年
                    localDateTime = LocalDateTime.of(year, monthInt, dayInt, NumberConstant.NUMBER_23, NumberConstant.NUMBER_59, NumberConstant.NUMBER_59);
                }
            }
        } else {
            if (dynamicValidityType == DataUnitEnum.DAY.getCode()) {
                localDateTime = dateTime.plusDays(dynamicValidityNumber);
            } else if (dynamicValidityType == DataUnitEnum.WEEK.getCode()) {
                localDateTime = dateTime.plusWeeks(dynamicValidityNumber);
            } else if (dynamicValidityType == DataUnitEnum.MONTH.getCode()) {
                localDateTime = dateTime.plusMonths(dynamicValidityNumber);
            } else {
                localDateTime = dateTime.plusYears(dynamicValidityNumber);
            }
        }
        return localDateTime;
    }

    private static String getTmpDate(Date date, String s) {
        SimpleDateFormat format2 = new SimpleDateFormat(s);
        return format2.format(date);
    }

    /**
     * 当天开始时间
     *
     * @return
     */
    public static LocalDateTime getDayStartTime() {
        Calendar todayStart = Calendar.getInstance();
        todayStart.set(Calendar.HOUR_OF_DAY, 0);
        todayStart.set(Calendar.MINUTE, 0);
        todayStart.set(Calendar.SECOND, 0);
        return LocalDateTime.ofInstant(todayStart.getTime().toInstant(), ZoneId.systemDefault());
    }

    /**
     * 当天结束时间
     *
     * @return
     */
    public static LocalDateTime getDayEndTime() {
        Calendar todayEnd = Calendar.getInstance();
        todayEnd.set(Calendar.HOUR_OF_DAY, 23);
        todayEnd.set(Calendar.MINUTE, 59);
        todayEnd.set(Calendar.SECOND, 59);
        return LocalDateTime.ofInstant(todayEnd.getTime().toInstant(), ZoneId.systemDefault());
    }

    /**
     * 获得本周开始时间
     *
     * @return
     */
    public static LocalDateTime getWeekStartTime() {
        LocalDate today = LocalDate.now();
        return today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).atTime(0, 0);
    }

    /**
     * 获得本周结束点时间
     *
     * @return
     */
    public static LocalDateTime getWeekEndTime() {
        LocalDate today = LocalDate.now();
        return today.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY)).atTime(23, 59);
    }

    /**
     * 获得本月开始时间
     *
     * @return
     */
    public static LocalDateTime getMonthStartTime() {
        LocalDate today = LocalDate.now();
        //本月的第一天
        return LocalDate.of(today.getYear(), today.getMonth(), 1).atTime(0, 0);

    }

    /**
     * 获得本月结束时间
     *
     * @return
     */
    public static LocalDateTime getMonthEndTime() {
        LocalDate today = LocalDate.now();
        //本月的最后一天
        return today.with(TemporalAdjusters.lastDayOfMonth()).atTime(23, 59);
    }

    /**
     * @Description:本周的开始时间
     * @Param: [today, isFirst: true 表示开始时间，false表示结束时间]
     */
    public static LocalDateTime getStartOrEndDayOfWeek(LocalDate today, Boolean isFirst) {
        String time = MIN_TIME;
        LocalDate resDate = LocalDate.now();
        if (today == null) {
            today = resDate;
        }
        DayOfWeek week = today.getDayOfWeek();
        int value = week.getValue();
        if (isFirst) {
            resDate = today.minusDays(value - 1);
        } else {
            resDate = today.plusDays(7 - value);
            time = MAX_TIME;
        }
        LocalDateTime localDateTime = LocalDateTime.parse(resDate.toString() + time);
        return localDateTime;
    }

}