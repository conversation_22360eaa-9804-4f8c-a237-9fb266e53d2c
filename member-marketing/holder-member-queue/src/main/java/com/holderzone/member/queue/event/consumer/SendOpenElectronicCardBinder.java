package com.holderzone.member.queue.event.consumer;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.member.common.constant.BinderConstant;
import com.holderzone.member.common.dto.event.SendOpenElectronicCardEvent;
import com.holderzone.member.queue.event.BinderChannel;
import com.holderzone.member.queue.event.processor.OpenElectronicCardProcessor;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.messaging.handler.annotation.Header;

import java.io.IOException;

/**
 * 开通电子卡
 *
 * <AUTHOR>
 * @description 开通电子卡
 * @date 2021/6/15 11:32
 */
@Slf4j
@EnableBinding(BinderChannel.class)
public class SendOpenElectronicCardBinder {

    /**
     * 开通电子卡
     */
    @StreamListener(BinderConstant.INPUT_SEND_OPEN_ELECTRONIC_CARD)
    public void execute(Message<SendOpenElectronicCardEvent> message,
                        @Header(AmqpHeaders.CHANNEL) Channel channel,
                        @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) throws IOException {
        SendOpenElectronicCardEvent requestEvent = message.getPayload();
        if (StringUtils.isEmpty(requestEvent.getSessionId())) {
            requestEvent.setSessionId(String.valueOf(System.currentTimeMillis()));
        }
        log.info("[{}] [SendOpenElectronicCardEvent] 指定用户发卡参数={}", requestEvent.getSessionId(), JacksonUtils.writeValueAsString(requestEvent));
        try {
            new OpenElectronicCardProcessor(requestEvent).execute();
        } finally {
            //手动确认
            channel.basicAck(deliveryTag, false);
        }

    }

}