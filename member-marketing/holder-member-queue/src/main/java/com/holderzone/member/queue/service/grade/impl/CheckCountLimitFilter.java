package com.holderzone.member.queue.service.grade.impl;

import com.alibaba.fastjson.JSON;
import com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum;
import com.holderzone.member.queue.dto.grade.DoubleValueRequest;
import com.holderzone.member.queue.entity.HsaBusinessEquities;
import com.holderzone.member.queue.entity.HsaMemberEquitiesReceiveRecord;
import com.holderzone.member.queue.service.grade.AbstractHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * 过滤当前会员是否存在翻倍次数
 * <AUTHOR>
 */
@Component
@Order(1) //校验顺序排第1
@Slf4j
public class CheckCountLimitFilter extends AbstractHandler {

    @Override
    public boolean doFilter(DoubleValueRequest request, HsaBusinessEquities hsaBusinessEquities,
                            HsaMemberEquitiesReceiveRecord equitiesReceiveRecord) {
        log.info("进入CheckCountLimitFilter过滤 hsaBusinessEquities:{}", JSON.toJSONString(hsaBusinessEquities));
        if (Objects.isNull(hsaBusinessEquities)) {
            return false;
        }
        //成长值翻倍次数限制 0:不限制 1:限制 (如果没有配置，默认为：不限制)
        Integer doubleCountLimited = Optional.of(hsaBusinessEquities)
                .map(HsaBusinessEquities::getDoubleCountLimited).orElse(EquitiesLimitedTypeEnum.UN_LIMITED.getCode());
        if (EquitiesLimitedTypeEnum.UN_LIMITED.getCode() == doubleCountLimited) {
            return true;
        }
        Optional<HsaMemberEquitiesReceiveRecord> equitiesRecord = Optional.ofNullable(equitiesReceiveRecord);
        if (!equitiesRecord.isPresent()) {
            return true;
        }
        //翻倍成长值使用次数
        Integer count = equitiesRecord.map(HsaMemberEquitiesReceiveRecord::getDoubleValueCount).orElse(0);
        //权益最大翻倍次数上限
        Integer doubleCountUpperLimit = hsaBusinessEquities.getDoubleCountUpperLimit();
        log.info("doubleCountUpperLimit值:{},count值:{}",doubleCountUpperLimit,count);
        return doubleCountUpperLimit > count;
    }
}
