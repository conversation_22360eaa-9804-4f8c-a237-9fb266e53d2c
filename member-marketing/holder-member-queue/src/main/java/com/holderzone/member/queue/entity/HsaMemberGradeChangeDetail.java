package com.holderzone.member.queue.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 会员等级变化明细表
 * @date 2022/1/4 10:21
 */
@ApiModel("会员等级变化明细表")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberGradeChangeDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 会员等级变化明细GUID
     */
    @ApiModelProperty("会员等级变化明细GUID")
    private String guid;
    /**
     * 运营主体GUID
     */
    @ApiModelProperty("运营主体GUID")
    private String operSubjectGuid;

    /**
     * 会员GUID
     */
    @ApiModelProperty("会员GUID")
    private String memberInfoGuid;

    /**
     * 等级变化前名称
     */
    @ApiModelProperty("等级变化前名称")
    private String beforeChangeGradeName;

    /**
     * 等级变化前等级GUID
     */
    @ApiModelProperty("等级变化前等级GUID")
    private String beforeChangeGradeGuid;

    /**
     * 等级变化前等级排序
     */
    @ApiModelProperty("等级变化前等级GUID")
    private Integer beforeChangeGradeVipGrade;

    /**
     * 等级变化后等级名称
     */
    @ApiModelProperty("等级变化后等级名称")
    private String afterChangeGradeName;

    /**
     * 等级变化后等级GUID
     */
    @ApiModelProperty("等级变化后等级GUID")
    private String afterChangeGradeGuid;

    /**
     * 等级变化前等级GUID
     */
    @ApiModelProperty("等级变化后等级GUID")
    private Integer afterChangeGradeVipGrade;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 等级变化类型,0升级,1降级
     *
     * @see com.holderzone.member.common.enums.member.GradeChangeTypeEnum
     */
    @ApiModelProperty("等级变化类型,0升级,1降级")
    private Integer gradeChangeType;

    /**
     * 当前成长值
     */
    @ApiModelProperty("当前成长值")
    private Integer currentGrowthValue;

    /**
     * 是否展示在小程序
     */
    @ApiModelProperty("是否展示在小程序")
    private Integer isShowApplet;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    /**
     * 变更后的会员等级图标
     */
    private String afterChangeGradeIcon;

    /**
     * 变更后的会员等级背景颜色
     */
    private String afterChangeBackgroundColor;

    /**
     * 变更后的会员等级升级成长值
     */
    private Integer afterChangeGrowthValue;
}
