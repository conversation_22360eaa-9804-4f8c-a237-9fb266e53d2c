package com.holderzone.member.queue.dto.grade;

import com.google.common.collect.Lists;
import com.holderzone.member.queue.entity.HsaGrowthCommodityTouchDetail;
import lombok.Data;

import java.util.List;
@Data
public class GrowthCommodityTouchDetailsDTO {

    //保存任务累计触发记录
    private List<HsaGrowthCommodityTouchDetail> saveGrowthCommodityTouchDetails ;
    //更新任务累计触发记录
    private List<HsaGrowthCommodityTouchDetail> updateGrowthCommodityTouchDetails;
    //更新任务累计触发记录
    private List<HsaGrowthCommodityTouchDetail> deleteGrowthCommodityTouchDetails = Lists.newArrayList();
}
