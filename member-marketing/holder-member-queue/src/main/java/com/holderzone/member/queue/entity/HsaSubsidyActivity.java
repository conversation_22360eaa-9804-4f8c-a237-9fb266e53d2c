package com.holderzone.member.queue.entity;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员食堂卡补贴活动
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaSubsidyActivity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * guid
     */
    private String guid;

    /**
     * 企业guid
     */
    private String enterpriseGuid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 活动编号
     */
    private String activityNum;

    /**
     * 活动名称
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String activityName;

    /**
     * 会员卡guid
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String cardGuidJson;

    /**
     * 活动状态 0 禁用 1 启用(进行中)  2  已结束  3  草稿 4 未开始
     */
    private Integer activityState;

    /**
     * 补贴金额
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal subsidyMoney;

    /**
     * 补贴频率 0 单次 1 每天 2 每周 3 每月 4 每年
     */
    private Integer subsidyFrequencyType;

    /**
     * 补贴机制  具体日期时间
     */
    private String subsidyJson;

    /**
     * 补贴用户类型 0 标签筛选 1 指定用户 2 运营主体下所有会员
     */
    private Integer subsidyFilterType;

    /**
     * 指定用户会员guid（按标签筛选存在）
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String memberInfoGuidJson;

    /**
     * 补贴人数(补贴后为具体人数)
     */
    private Integer subsidyMemberNum;

    /**
     * 标签guid（按标签筛选存在）
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String labelGuidJson;

    /**
     * 打标标签guid
     */
    private String markingLabelGuidJson;

    /**
     * 指定发放时间 (单次发放时存在)
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private LocalDateTime subsidyAppointTime;

    /**
     * 活动开始时间
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private LocalDateTime activityStartTime;

    /**
     * 活动结束时间
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private LocalDateTime activityEndTime;


    /**************************以下为补贴金回收规则****************************/


    /**
     * 补贴金有效期类型 0 永久有效 1 固定时间清零 2 领取后过期
     */
    private Integer subsidyMoneyPeriodType;

    /**
     * 回收频率 1 每天 2 每周 3 每月 4 每年 5 指定日期
     */
    private Integer subsidyRecycleFrequencyType;

    /**
     * 指定日期
     */
    private LocalDateTime subsidyRecycleAppointTime;

    /**
     * 单位 1日、2周、3月、4年
     */
    private Integer validityUnit;

    /**
     * 领取后过期（数字标识）
     */
    private Integer subsidyRecycle;

    /**
     * 回收机制  具体日期时间
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String subsidyRecycleJson;

    /**
     * 补贴金是否可退 0 不可退  1 可退
     */
    private Integer isRetreat;

    /**
     * del
     */
    private Integer isDel;

    /**
     * 操作人员账号
     */
    @ApiModelProperty("操作人员账号")
    private String operatorTelName;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;
}
