package com.holderzone.member.queue.service.order;


import com.holderzone.member.common.dto.mall.OrderReceiverAddressDTO;
import com.holderzone.member.common.dto.order.MallBaseOrderDTO;
import com.holderzone.member.common.dto.order.MallBaseOrderDetailDTO;

import java.util.List;

public interface CrmOrderService {

    void pushCrmOrder(MallBaseOrderDTO order, List<MallBaseOrderDetailDTO> details, OrderReceiverAddressDTO orderReceiverAd, boolean pushStock);
}
