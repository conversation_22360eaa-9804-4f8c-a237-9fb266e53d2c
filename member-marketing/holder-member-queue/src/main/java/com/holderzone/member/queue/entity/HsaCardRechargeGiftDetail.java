package com.holderzone.member.queue.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.member.common.base.HsaBaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * <p>
 * 充值赠送记录表
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsa_card_recharge_gift_detail")
public class HsaCardRechargeGiftDetail extends HsaBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * memberCardGuid
     */
    @ApiModelProperty(value = "memberCardGuid")
    private String memberCardGuid;

    /**
     * cardGuid
     */
    @ApiModelProperty(value = "cardGuid")
    private String cardGuid;

    /**
     * memberGuid
     */
    @ApiModelProperty(value = "memberGuid")
    private String memberGuid;

    /**
     * 运营主体guid
     */
    @ApiModelProperty(value = "运营主体guid")
    private String operSubjectGuid;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    /**
     * 对应明细记录Guid
     */
    @ApiModelProperty(value = "对应明细记录Guid")
    private String memberFundingDetailGuid;

    /**
     * 赠送金额生效时间类型 0 小时 1 天
     *
     * @see com.holderzone.member.common.enums.gift.GiftEffectiveUnitEnum
     */
    @ApiModelProperty(value = "赠送金额生效时间类型 0 小时 1 天")
    private Integer moneyEffectiveUnitType;

    /**
     * 订单实充金额
     */
    @ApiModelProperty(value = "订单实充金额")
    private BigDecimal orderPaidAmount;

    /**
     * 活动guid
     */
    @ApiModelProperty(value = "活动guid")
    private String activityGuid;

    /**
     * 门槛guid
     */
    @ApiModelProperty(value = "门槛guid")
    private String thresholdGuid;

    /**
     * 打标标签guid
     */
    @ApiModelProperty(value = "打标标签guid")
    private String markingLabelGuidJson;

    /**
     * 门槛消耗次数：满1次，没满n次
     */
    @ApiModelProperty(value = "门槛消耗次数：满1次，没满n次")
    private Integer giftThresholdLimitSum;

    /**
     * 赠送积分
     */
    @ApiModelProperty(value = "赠送积分")
    private Integer giveIntegralValue;

    /**
     * 赠送成长值
     */
    @ApiModelProperty(value = "赠送成长值")
    private Integer giveGrowthValue;

    /**
     * 赠送会员卡guid
     */
    @ApiModelProperty(value = "赠送会员卡guid")
    private String giveCardGuid;


    /**
     * 赠送金额
     */
    @ApiModelProperty(value = "赠送金额")
    private BigDecimal moneyAmount;

    /**
     * 赠送金额生效类型 0 立即生效 1 定时生效
     *
     * @see com.holderzone.member.common.enums.gift.GiftEffectiveEnum
     */
    @ApiModelProperty(value = "赠送金额生效类型 0 立即生效 1 定时生效")
    private Integer moneyEffectiveType;

    /**
     * 赠送金额生效时间
     */
    @ApiModelProperty(value = "赠送金额生效时间")
    private LocalDateTime moneyEffectiveTime;

    /**
     * 实体卡GUID
     */
    @ApiModelProperty(value = "实体卡GUID")
    private String physicalCardGuid;

    /**
     * 电子卡GUID
     */
    @ApiModelProperty(value = "电子卡GUID")
    private String electronicCardGuid;


    /**
     * 是否完成定时刷新
     * 0未刷新 1已刷新 2已取消（不再刷新） 3已暂停（暂时不刷新）
     *
     * @see com.holderzone.member.common.enums.gift.GiftDetailRefreshEnum
     */
    @ApiModelProperty(value = "是否完成定时刷新")
    private Integer isRefresh;


}
