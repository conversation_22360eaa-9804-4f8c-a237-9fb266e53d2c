package com.holderzone.member.queue.client;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.QueryArrayShopBase;
import com.holderzone.member.common.dto.base.ResCommodityBase;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.queue.entity.HsaGrowthValueCommodityRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商品数据
 * @date 2021/12/7 15:39
 */
@Service
@Slf4j
public class ShopBaseService {

    @Resource
    private ExternalSupport externalSupport;

    public List<HsaGrowthValueCommodityRule> queryCommodityCategoryIdListNew(int strategyId, List<Integer> categoryId) {
        List<HsaGrowthValueCommodityRule> growthCommodityBaseVOS = Lists.newArrayList();
        QueryArrayShopBase queryStoreBasePage = new QueryArrayShopBase();
        queryStoreBasePage.setStrategy_id(strategyId);
        queryStoreBasePage.setCategory_id(categoryId);
        List<ResCommodityBase> resCommodityBases = externalSupport.itemServer(ThreadLocalCache.getSystem()).listCommodityBase(queryStoreBasePage);
        if (CollectionUtil.isEmpty(resCommodityBases)) {
            return growthCommodityBaseVOS;
        }
        growthCommodityBaseVOS = getGrowthCommodityBaseVOS(resCommodityBases);
        return growthCommodityBaseVOS;
    }

    /**
     * 重组数据
     *
     * @param resCommodityBases resCommodityBases
     * @return List<GrowthCommodityBaseVO>
     */
    private List<HsaGrowthValueCommodityRule> getGrowthCommodityBaseVOS(List<ResCommodityBase> resCommodityBases) {
        List<HsaGrowthValueCommodityRule> growthCommodityBaseVOS = Lists.newArrayList();
        if (CollectionUtil.isEmpty(resCommodityBases)) {
            return growthCommodityBaseVOS;
        }
        growthCommodityBaseVOS = resCommodityBases.stream().map(in -> {
            HsaGrowthValueCommodityRule growthCommodityBaseVO = new HsaGrowthValueCommodityRule();
            growthCommodityBaseVO
                    .setCommodityPrice(in.getBasePrice())
                    .setCategoryId(String.valueOf(in.getCategoryId()))
                    .setCategoryName(in.getCategoryName())
                    .setCommodityCode(in.getCommodityCode())
                    .setCommodityId(String.valueOf(in.getId()))
                    .setStoreState(Integer.parseInt(in.getStoreState()))
                    .setComboType(Integer.parseInt(in.getCommodityComboType()))
                    .setCommodityName(in.getName())
                    .setStrategyId(String.valueOf(in.getStrategyId()))
                    .setStrategyName(in.getStrategyName());
            return growthCommodityBaseVO;
        }).collect(Collectors.toList());
        return growthCommodityBaseVOS;
    }

}
