package com.holderzone.member.queue.event.consumer;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.member.common.constant.BinderConstant;
import com.holderzone.member.common.dto.event.SendMemberConsumptionDistributeEvent;
import com.holderzone.member.queue.event.BinderChannel;
import com.holderzone.member.queue.event.processor.MemberConsumptionDistributeProcessor;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.messaging.handler.annotation.Header;

import java.io.IOException;

/**
 * 会员消费分销记录事件消费者
 *
 * <AUTHOR>
 * @version 1.0 2025/06/04
 */
@Slf4j
@EnableBinding(BinderChannel.class)
public class MemberConsumptionDistributeBinder {

    /**
     * 会员消费分销记录处理
     */
    @StreamListener(BinderConstant.INPUT_MEMBER_CONSUMPTION_DISTRIBUTE)
    public void execute(Message<SendMemberConsumptionDistributeEvent> message,
                        @Header(AmqpHeaders.CHANNEL) Channel channel,
                        @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) throws IOException {
        SendMemberConsumptionDistributeEvent requestEvent = message.getPayload();
        if (StringUtils.isEmpty(requestEvent.getSessionId())) {
            requestEvent.setSessionId(String.valueOf(System.currentTimeMillis()));
        }
        log.info("[{}] [SendMemberConsumptionDistributeEvent] 会员消费分销记录处理参数={}", 
                requestEvent.getSessionId(), JacksonUtils.writeValueAsString(requestEvent));
        try {
            new MemberConsumptionDistributeProcessor(requestEvent).execute();
        } finally {
            //手动确认
            channel.basicAck(deliveryTag, false);
        }
    }
} 