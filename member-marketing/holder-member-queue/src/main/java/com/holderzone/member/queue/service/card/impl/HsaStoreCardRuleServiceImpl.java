package com.holderzone.member.queue.service.card.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.queue.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.queue.entity.HsaMemberInfoCard;
import com.holderzone.member.queue.entity.HsaStoreCardRule;
import com.holderzone.member.queue.mapper.HsaStoreCardRuleMapper;
import com.holderzone.member.queue.service.card.HsaStoreCardRuleService;
import com.holderzone.member.queue.service.card.assembler.MemberCardAssembler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 会员卡门店适用表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-17
 */
@Slf4j
@Service
public class HsaStoreCardRuleServiceImpl extends HolderBaseServiceImpl<HsaStoreCardRuleMapper, HsaStoreCardRule> implements HsaStoreCardRuleService {

    @Resource
    private HsaStoreCardRuleMapper hsaStoreCardRuleMapper;

    @Resource
    private HsaStoreCardRuleService hsaStoreCardRuleService;

    @Resource
    private MemberCardAssembler memberCardAssembler;

    @Override
    public Integer deleteByCardGuid(String cardGuid) {
        return hsaStoreCardRuleMapper.delete(new LambdaQueryWrapper<HsaStoreCardRule>()
                .eq(HsaStoreCardRule::getCardGuid, cardGuid));
    }

    /**
     * 实体卡退卡 复制出一份新门店范围
     *
     * @param cardGuid
     * @param newMemberCardGuid
     * @param memberCardGuid
     */
    @Override
    public void saveStoreCardRule(String cardGuid, String newMemberCardGuid, String memberCardGuid) {
        List<HsaStoreCardRule> hsaStoreCardRuleArrayList = Lists.newArrayList();
        // 去保存适用门店信息
        List<HsaStoreCardRule> hsaStoreCardRuleList = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .eq(HsaStoreCardRule::getMemberInfoCardGuid, memberCardGuid).isNull(HsaStoreCardRule::getParentGuid));
        //档口
        Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .in(HsaStoreCardRule::getParentGuid, hsaStoreCardRuleList.stream().map(HsaStoreCardRule::getGuid).collect(Collectors.toList()))
                .isNotNull(HsaStoreCardRule::getParentGuid))
                .stream()
                .collect(Collectors.groupingBy(HsaStoreCardRule::getParentGuid));
        memberCardAssembler.addStoreCardRuleArrayList(cardGuid, newMemberCardGuid, hsaStoreCardRuleList, hsaStoreCardRuleMap, hsaStoreCardRuleArrayList);

        if (CollUtil.isNotEmpty(hsaStoreCardRuleArrayList)) {
            hsaStoreCardRuleService.saveBatch(hsaStoreCardRuleArrayList);
        }
    }

    /**
     * 门店合并处理
     *
     * @param hsaMemberInfoCard
     * @param memberPhysicalCard
     */
    @Override
    public void mergeStoreProcessor(HsaMemberInfoCard hsaMemberInfoCard, HsaMemberInfoCard memberPhysicalCard) {
        //电子卡门店级
        List<HsaStoreCardRule> hsaStoreCardRuleList = (hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .eq(HsaStoreCardRule::getMemberInfoCardGuid, hsaMemberInfoCard.getGuid())
                .isNull(HsaStoreCardRule::getParentGuid)));
        List<String> parentGuidList = hsaStoreCardRuleList.stream().map(HsaStoreCardRule::getGuid).collect(Collectors.toList());

        //实体卡门店级
        List<HsaStoreCardRule> hsaStoreCardRuleList1 = (hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .eq(HsaStoreCardRule::getMemberInfoCardGuid, memberPhysicalCard.getGuid())
                .isNull(HsaStoreCardRule::getParentGuid)));
        List<String> parentGuidList1 = hsaStoreCardRuleList1.stream().map(HsaStoreCardRule::getGuid).collect(Collectors.toList());
        parentGuidList1.addAll(parentGuidList);
        //档口级
        Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap1 = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .in(HsaStoreCardRule::getParentGuid, parentGuidList1))
                .stream()
                .collect(Collectors.groupingBy(HsaStoreCardRule::getParentGuid));

        hsaStoreCardRuleList.addAll(hsaStoreCardRuleList1);

        //delete合并之前的数据
        List<String> memberInfoGuidList = new ArrayList<>();
        memberInfoGuidList.add(hsaMemberInfoCard.getGuid());
        memberInfoGuidList.add(memberPhysicalCard.getGuid());
        hsaStoreCardRuleMapper.delete(new LambdaQueryWrapper<HsaStoreCardRule>()
                .in(HsaStoreCardRule::getMemberInfoCardGuid, memberInfoGuidList));

        Set<HsaStoreCardRule> hsaStoreCardRuleSet = new HashSet<>();
        Map<String, String> map = new HashMap<>();
        addStoreCardRuleSet(hsaMemberInfoCard, hsaStoreCardRuleList, hsaStoreCardRuleSet, map, hsaStoreCardRuleMap1);
        if (CollUtil.isNotEmpty(hsaStoreCardRuleSet)) {
            hsaStoreCardRuleService.saveBatch(hsaStoreCardRuleSet);
        }
    }

    private void addStoreCardRuleSet(HsaMemberInfoCard hsaMemberInfoCard,
                                     List<HsaStoreCardRule> hsaStoreCardRuleList,
                                     Set<HsaStoreCardRule> hsaStoreCardRuleSet,
                                     Map<String, String> map,
                                     Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap1) {
        addStoreCardSet(hsaMemberInfoCard, hsaStoreCardRuleList, hsaStoreCardRuleSet, map, hsaStoreCardRuleMap1);
    }

    /**
     * 会员卡合并已领卡门店范围
     *
     * @param hsaMemberInfoCard
     * @param cardGuid
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void mergeStoreProcessor(HsaMemberInfoCard hsaMemberInfoCard, String cardGuid) {
        //电子卡门店级
        List<HsaStoreCardRule> hsaStoreCardRuleList = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .eq(HsaStoreCardRule::getMemberInfoCardGuid, hsaMemberInfoCard.getGuid())
                .isNull(HsaStoreCardRule::getParentGuid));
        List<String> parentGuidList = hsaStoreCardRuleList.stream().map(HsaStoreCardRule::getGuid).collect(Collectors.toList());

        //会员卡门店范围
        List<HsaStoreCardRule> hsaStoreCardRuleList1 = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .eq(HsaStoreCardRule::getCardGuid, cardGuid)
                .isNull(HsaStoreCardRule::getMemberInfoCardGuid)
                .isNull(HsaStoreCardRule::getParentGuid));
        if (CollUtil.isEmpty(hsaStoreCardRuleList1)) {
            return;
        }
        List<String> parentGuidList1 = hsaStoreCardRuleList1.stream().map(HsaStoreCardRule::getGuid).collect(Collectors.toList());
        parentGuidList1.addAll(parentGuidList);
        //档口级
        Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap1 = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .in(HsaStoreCardRule::getParentGuid, parentGuidList1))
                .stream()
                .collect(Collectors.groupingBy(HsaStoreCardRule::getParentGuid));

        hsaStoreCardRuleList.addAll(hsaStoreCardRuleList1);

        //delete合并之前的数据
        hsaStoreCardRuleMapper.delete(new LambdaQueryWrapper<HsaStoreCardRule>()
                .eq(HsaStoreCardRule::getMemberInfoCardGuid, hsaMemberInfoCard.getGuid()));

//        hsaStoreCardRuleMapper.delete(new LambdaQueryWrapper<HsaStoreCardRule>()
//                .in(HsaStoreCardRule::getCardGuid, cardGuid)
//                .isNull(HsaStoreCardRule::getMemberInfoCardGuid));

        Set<HsaStoreCardRule> hsaStoreCardRuleSet = new HashSet<>();
        Map<String, String> map = new HashMap<>();
        addStoreCardSet(hsaMemberInfoCard, hsaStoreCardRuleList, hsaStoreCardRuleSet, map, hsaStoreCardRuleMap1);
        if (CollUtil.isNotEmpty(hsaStoreCardRuleSet)) {
            hsaStoreCardRuleService.saveBatch(hsaStoreCardRuleSet);
        }
    }

    private void addStoreCardSet(HsaMemberInfoCard hsaMemberInfoCard, List<HsaStoreCardRule> hsaStoreCardRuleList, Set<HsaStoreCardRule> hsaStoreCardRuleSet, Map<String, String> map, Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap1) {
        if (CollUtil.isNotEmpty(hsaStoreCardRuleList)) {
            for (HsaStoreCardRule rule : hsaStoreCardRuleList) {
                List<String> checkStoreGuid = hsaStoreCardRuleSet.stream().map(HsaStoreCardRule::getStoreGuid).collect(Collectors.toList());
                if (!checkStoreGuid.contains(rule.getStoreGuid())) {
                    memberCardAssembler.seStore(hsaMemberInfoCard, hsaStoreCardRuleSet, map, rule);
                }
                if (CollUtil.isNotEmpty(hsaStoreCardRuleMap1) && hsaStoreCardRuleMap1.containsKey(rule.getGuid())) {
                    List<HsaStoreCardRule> storeCardRule = hsaStoreCardRuleMap1.get(rule.getGuid());
                    storeCardRule.forEach(in -> {
                        if (!checkStoreGuid.contains(in.getStoreGuid())) {
                            memberCardAssembler.setStall(hsaMemberInfoCard, hsaStoreCardRuleSet, map, rule, storeCardRule, in);
                        }
                    });
                }
            }
        }
    }


}
