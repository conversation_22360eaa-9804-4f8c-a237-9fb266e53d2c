package com.holderzone.member.queue.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员卡会员关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberCardRule implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * guid
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 规则guid
     */
    private String cardOpenRuleGuid;

    /**
     * userName
     */
    private String userName;

    /**
     * 会员
     */
    private String operationMemberInfoGuid;

    /**
     * 会员账号
     */
    private String memberAccount;

    /**
     * 手机号
     */
    private String memberPhoneNum;

    /**
     * 业务类型 0指定需要开卡会员；1指定只能开卡的会员范围
     */
    private Integer businessType;

    /**
     * cardGuid
     */
    private String cardGuid;


    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

}
