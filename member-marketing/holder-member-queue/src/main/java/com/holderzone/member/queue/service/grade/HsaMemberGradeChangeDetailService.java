package com.holderzone.member.queue.service.grade;


import com.holderzone.member.queue.config.mybatis.IHolderBaseService;
import com.holderzone.member.queue.dto.grade.MemberGradeChangeDTO;
import com.holderzone.member.queue.entity.HsaMemberGradeChangeDetail;
import com.holderzone.member.queue.entity.HsaMemberGradeInfo;

/**
 * <p>
 * 会员等级变化明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-04
 */
public interface HsaMemberGradeChangeDetailService extends IHolderBaseService<HsaMemberGradeChangeDetail> {

    /**
     * 会员等级变化
     */
    void memberGradeChange(MemberGradeChangeDTO gradeChangeDTO);

    /**
     * 定位当前成长值所在等级
     *
     * @param memberGrowthValue memberGrowthValue
     * @param operSubjectGuid   operSubjectGuid
     * @return HsaExtraAwardRule
     */
    HsaMemberGradeInfo calculateExtraAmount(Integer memberGrowthValue, String operSubjectGuid);
}
