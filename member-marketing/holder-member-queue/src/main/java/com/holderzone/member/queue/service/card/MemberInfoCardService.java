package com.holderzone.member.queue.service.card;

import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.dto.event.SendOpenElectronicCardEvent;
import com.holderzone.member.queue.entity.HsaMemberInfoCard;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 会员卡相关接口
 * @date 2021/10/14 11:27
 */
public interface MemberInfoCardService extends IHolderBaseService<HsaMemberInfoCard> {

    void sendOpenElectronicCard(SendOpenElectronicCardEvent event);
}
