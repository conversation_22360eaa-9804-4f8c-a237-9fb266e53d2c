package com.holderzone.member.queue.service.grade;

import com.holderzone.member.queue.dto.grade.DoubleValueRequest;
import com.holderzone.member.queue.entity.HsaBusinessEquities;
import com.holderzone.member.queue.entity.HsaMemberEquitiesReceiveRecord;

/**
 * <AUTHOR>
 */
public abstract class AbstractHandler {

    /**
     * 责任链中的下一个对象
     */
    private AbstractHandler nextHandler;

    /**
     * 责任链的下一个对象
     */
    public void setNextHandler(AbstractHandler nextHandler){
        this.nextHandler = nextHandler;
    }

    /**
     * 具体参数拦截逻辑,给子类去实现
     */
    public Long filter(DoubleValueRequest request, HsaBusinessEquities hsaBusinessEquities,
                       HsaMemberEquitiesReceiveRecord equitiesReceiveRecord) {
        boolean filter = doFilter(request, hsaBusinessEquities, equitiesReceiveRecord);
        if (getNextHandler() != null && filter) {
            getNextHandler().filter(request, hsaBusinessEquities,equitiesReceiveRecord);
        }
        return request.getDoubleValue();
    }

    public AbstractHandler getNextHandler() {
        return nextHandler;
    }

    /**
     * 翻倍成长值条件过滤
     * @param request 请求参数
     * @param hsaBusinessEquities 等级权益信息
     * @param equitiesReceiveRecord 会员等级权益记录
     * @return 是否过滤
     */
    protected abstract boolean doFilter(DoubleValueRequest request, HsaBusinessEquities hsaBusinessEquities,
                                        HsaMemberEquitiesReceiveRecord equitiesReceiveRecord);

}
