package com.holderzone.member.queue.mapper;


import com.holderzone.member.queue.config.mybatis.HolderBaseMapper;
import com.holderzone.member.queue.entity.HsaSubsidyActivityDetailRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 补贴活动记录明细表
 * @date 2021/10/26 9:42
 */
public interface HsaSubsidyActivityDetailRecordMapper extends HolderBaseMapper<HsaSubsidyActivityDetailRecord> {
    /**
     * 批量更新状态
     * @param activityList 活动列表
     */
    void batchUpdateStatus(@Param("list") List<HsaSubsidyActivityDetailRecord> activityList);
}
