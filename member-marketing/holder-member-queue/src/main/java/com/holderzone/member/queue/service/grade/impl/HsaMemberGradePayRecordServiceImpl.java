package com.holderzone.member.queue.service.grade.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.grade.MemberGradePayRecordReqVO;
import com.holderzone.member.queue.entity.HsaMemberGradeInfo;
import com.holderzone.member.queue.entity.HsaMemberGradePayRecord;
import com.holderzone.member.queue.entity.HsaOperationMemberInfo;
import com.holderzone.member.queue.mapper.HsaMemberGradeInfoMapper;
import com.holderzone.member.queue.mapper.HsaMemberGradePayRecordMapper;
import com.holderzone.member.queue.mapper.HsaOperationMemberInfoMapper;
import com.holderzone.member.queue.service.grade.IHsaMemberGradePayRecordService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/4/20 下午7:15
 * @description 会员等级付费记录表 服务实现类
 */
@Service
@Slf4j
@AllArgsConstructor
public class HsaMemberGradePayRecordServiceImpl extends ServiceImpl<HsaMemberGradePayRecordMapper, HsaMemberGradePayRecord>
        implements IHsaMemberGradePayRecordService {

    private HsaOperationMemberInfoMapper operationMemberInfoMapper;

    private final GuidGeneratorUtil guidGeneratorUtil;

    private HsaMemberGradePayRecordMapper memberGradePayRecordMapper;

    private HsaMemberGradeInfoMapper memberGradeInfoMapper;

    @Override
    public void batchAdd(MemberGradePayRecordReqVO gradePayRecordReqVO) {
        gradePayRecordReqVO.validatedSave();

        HsaMemberGradePayRecord memberGradePayRecord = new HsaMemberGradePayRecord();
        memberGradePayRecord.setOperSubjectGuid(gradePayRecordReqVO.getOperSubjectGuid());
        memberGradePayRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberGradePayRecord.class.getSimpleName()));
        HsaMemberGradeInfo memberGradeInfo = memberGradeInfoMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                .eq(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode())
                .eq(HsaMemberGradeInfo::getGuid, gradePayRecordReqVO.getMemberInfoGradeGuid())
        );
        if (ObjectUtils.isEmpty(memberGradeInfo)) {
            throw new BusinessException("未查询到等级信息");
        }
        memberGradePayRecord.setMemberInfoGradeName(memberGradeInfo.getName());
        memberGradePayRecord.setMemberInfoGradeGuid(gradePayRecordReqVO.getMemberInfoGradeGuid());
        memberGradePayRecord.setExpireTime(DateUtil.getFirstExpireTime(gradePayRecordReqVO.getNum(), gradePayRecordReqVO.getUnit()));
        memberGradePayRecord.setPayTime(LocalDateTime.now());
        memberGradePayRecord.setNum(gradePayRecordReqVO.getNum());
        memberGradePayRecord.setRoleType(gradePayRecordReqVO.getRoleType());
        memberGradePayRecord.setChangeType(gradePayRecordReqVO.getChangeType());
        memberGradePayRecord.setUnit(gradePayRecordReqVO.getUnit());
        memberGradePayRecord.setPayAmount(gradePayRecordReqVO.getPayAmount());
        memberGradePayRecord.setRemark(gradePayRecordReqVO.getRemark());
        List<HsaOperationMemberInfo> memberInfoList = operationMemberInfoMapper.selectList(
                new LambdaQueryWrapper<HsaOperationMemberInfo>()
                        .in(HsaOperationMemberInfo::getGuid, gradePayRecordReqVO.getMemberGuidList()));
        if (CollectionUtils.isEmpty(memberInfoList)) {
            throw new BusinessException("未查询到会员信息");
        }
        List<HsaMemberGradePayRecord> recordList = new ArrayList<>();
        memberInfoList.forEach(memberInfo -> {
            HsaMemberGradePayRecord toSave = new HsaMemberGradePayRecord();
            BeanUtils.copyProperties(memberGradePayRecord, toSave);
            toSave.setMemberInfoName(memberInfo.getUserName());
            toSave.setPhoneNum(memberInfo.getPhoneNum());
            toSave.setMemberInfoGuid(memberInfo.getGuid());
            recordList.add(toSave);
        });
        this.saveBatch(recordList);
    }
}
