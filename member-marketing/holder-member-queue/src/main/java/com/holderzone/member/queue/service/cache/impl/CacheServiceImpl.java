package com.holderzone.member.queue.service.cache.impl;


import com.alibaba.fastjson.JSON;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.queue.service.cache.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 会员缓存
 * @date 2021/8/19 15:04
 */
@Slf4j
@Service
public class CacheServiceImpl implements CacheService {

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private RedisTemplate<String, Object> stringObjectRedisTemplate;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RedisTemplate<String, String> stringRedisTemplate;

    private static final String GRADE_REFRESH = "GRADE_REFRESH";

    public long GRADE_REFRESH_TTL = 30;


    @Override
    public void createGradeRefresh(String cacheKey, String operSubjectGuid) {
        redisTemplate.opsForValue().set(cacheKey, operSubjectGuid, GRADE_REFRESH_TTL, TimeUnit.MINUTES);
    }

    @Override
    public boolean getGradeRefresh(String cacheKey) {
        Object serverToken = redisTemplate.opsForValue().get(cacheKey);
        return Objects.isNull(serverToken);
    }

    @Override
    public Boolean cleanToken(String cacheKey) {
        if (!getGradeRefresh(cacheKey)) {
            return redisTemplate.delete(cacheKey);
        }
        return Boolean.FALSE;
    }

    @Override
    public void delete(String key) {
        stringRedisTemplate.delete(key);
    }


    @Override
    public void updateInventoryNum(String key, String code) {

        RLock lock = redissonClient.getLock(StringConstant.COUPON_PACKAGE_INVENTORY + code);
        try {
            if (lock.isLocked()) {
                updateInventoryNum(key, code);
            }
            lock.lock();
            stringRedisTemplate.opsForValue().decrement(key, 1L);
        } catch (MemberBaseException baseException) {
            throw baseException;
        } catch (Exception exception) {
            log.error("更新券包库存失败exception=", exception);
            throw new MemberBaseException(exception.getMessage());
        } finally {
            //释放锁资源
            lock.unlock();
        }
    }

    @Override
    public int getInventoryNum(String key, String code) {
        RLock lock = redissonClient.getLock(StringConstant.COUPON_PACKAGE_INVENTORY + code);
        try {
            if (lock.isLocked()) {
                getInventoryNum(key, code);
            }
            lock.lock();
            return Integer.parseInt(stringObjectRedisTemplate.opsForValue().get(key) + "");
        } catch (MemberBaseException baseException) {
            throw baseException;
        } catch (Exception exception) {
            log.error("更新券包库存失败exception=", exception);
            throw new MemberBaseException(exception.getMessage());
        } finally {
            //释放锁资源
            lock.unlock();
        }
    }

    /**
     * @param type 1 加 0 减
     * @return
     */
    @Override
    public void lockInventoryNum(String key, String code, Integer type, Integer num) {
        RLock lock = redissonClient.getLock(StringConstant.LOCK_COUPON_PACKAGE_INVENTORY + code);
        try {
            if (lock.isLocked()) {
                lockInventoryNum(key, code, type, num);
            }
            lock.lock();
            Object object = stringObjectRedisTemplate.opsForValue().get(key);
            if (Objects.isNull(object)) {
                stringObjectRedisTemplate.opsForValue().set(key, num);
            } else {
                if (type == 1) {
                    stringObjectRedisTemplate.opsForValue().increment(key, num);
                } else {
                    if (Integer.parseInt(object + "") == 1) {
                        stringObjectRedisTemplate.delete(key);
                    } else {
                        stringObjectRedisTemplate.opsForValue().decrement(key, 1);
                    }
                }
            }
        } catch (MemberBaseException baseException) {
            throw baseException;
        } catch (Exception exception) {
            log.error("上锁库存失败exception=", exception);
            throw new MemberBaseException(exception.getMessage());
        } finally {
            //释放锁资源
            lock.unlock();
        }
    }


    /**
     * 活动防重锁
     *
     * @return
     */
    @Override
    public Boolean setLock(String key, String code) {
        RLock lock = redissonClient.getLock(StringConstant.LOCK_COUPON_PACKAGE_ACTIVITY + code);
        try {
            if (lock.isLocked()) {
                updateInventoryNum(key, code);
            }
            lock.lock();
            Object object = stringObjectRedisTemplate.opsForValue().get(key);
            if (Objects.nonNull(object)) {
                return true;
            } else {
                stringObjectRedisTemplate.opsForValue().set(key, -1, 1, TimeUnit.MINUTES);
                return false;
            }

        } catch (MemberBaseException baseException) {
            throw baseException;
        } catch (Exception exception) {
            log.error("防重锁失败exception=", exception);
            throw new MemberBaseException(exception.getMessage());
        } finally {
            //释放锁资源
            lock.unlock();
        }
    }


    /**
     * 定时器防重锁
     *
     * @return
     */
    @Override
    public Boolean setLock(String key) {
        RLock lock = redissonClient.getLock(StringConstant.LOCK_XXL_JOB_COUPON_PACKAGE);
        try {
            if (lock.isLocked()) {
                return true;
            }
            lock.lock();
            Object object = stringObjectRedisTemplate.opsForValue().get(key);
            if (Objects.isNull(object)) {
                stringObjectRedisTemplate.opsForValue().set(key, -1);
                return false;
            } else {
                log.info("XXL-JOB防重拦截，重复时间：{}", JSON.toJSONString(object));
                return true;
            }

        } catch (MemberBaseException baseException) {
            throw baseException;
        } catch (Exception exception) {
            log.error("XXL-JOB防重锁失败exception=", exception);
            throw new MemberBaseException(exception.getMessage());
        } finally {
            //释放锁资源
            lock.unlock();
        }
    }

    @Override
    public void addInventoryNum(String key, String code) {
        RLock lock = redissonClient.getLock(StringConstant.LOCK_SUCCESS_COUPON_PACKAGE_NUM + code);
        try {
            if (lock.isLocked()) {
                addInventoryNum(key, code);
            }
            lock.lock();
            Object object = stringObjectRedisTemplate.opsForValue().get(key);
            if (Objects.isNull(object)) {
                stringObjectRedisTemplate.opsForValue().set(key, 1);
            } else {
                stringObjectRedisTemplate.opsForValue().increment(key, 1);
            }
        } catch (MemberBaseException baseException) {
            throw baseException;
        } catch (Exception exception) {
            log.error("addInventoryNum失败exception=", exception);
            throw new MemberBaseException(exception.getMessage());
        } finally {
            //释放锁资源
            lock.unlock();
        }
    }

}
