package com.holderzone.member.queue.mapper;


import com.holderzone.member.common.dto.card.MemberPhoneDTO;
import com.holderzone.member.common.dto.label.LabelRechargeInfo;
import com.holderzone.member.common.dto.label.RequestLabelQuery;
import com.holderzone.member.common.qo.member.MemberListQO;
import com.holderzone.member.common.vo.member.MemberInfoVO;
import com.holderzone.member.queue.config.mybatis.HolderBaseMapper;
import com.holderzone.member.queue.dto.member.UpdateIntegral;
import com.holderzone.member.queue.entity.HsaOperationMemberInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 运营主体会员信息表
 * @date 2021/10/14 14:49
 */
public interface HsaOperationMemberInfoMapper extends HolderBaseMapper<HsaOperationMemberInfo> {

    /**
     * 分页查询会员列表
     *
     * @param request 查询会员列表QO
     * @return 查询结果
     */
    List<MemberInfoVO> listMemberInfo(@Param("request") MemberListQO request);

    /**
     * 分批查询
     *
     * @return MemberPhoneDTO
     */
    Set<String> listByOperSubjectGuid(@Param("operSubjectGuid") String operSubjectGuid);

    /**
     * 批量更新
     * @param hsaOperationMemberInfos
     */
    void batchUpdateGrowth(@Param("list") List<HsaOperationMemberInfo> hsaOperationMemberInfos);

    /**
     * 标签充值信息查询过滤
     *
     * @param query 查询条件
     * @return 操作结果
     */
    List<LabelRechargeInfo> memberLabelRechargeInfoFilter(@Param("query") RequestLabelQuery query);

    /**
     * 批量更新会员积分
     * @param  updateMemberIntegralList 更新的积分信息
     */
    void batchUpdateIntegral(@Param("list") List<UpdateIntegral> updateMemberIntegralList);

    /**
     * 查询会员等级对应会员Guid
     *
     * @param memberGradeInfoGuids 会员等级guid集合
     * @return 查询结果
     */
    List<String> queryMemberGrade(@Param("memberGradeInfoGuids") List<String> memberGradeInfoGuids);

    /**
     * 分批查询
     *
     * @return MemberPhoneDTO
     */
    Set<String> queueSubjectGuid(@Param("operSubjectGuid") String operSubjectGuid);

    /**
     * 分批查询
     *
     * @return MemberPhoneDTO
     */
    List<MemberPhoneDTO> listByGuid(@Param("request") List<String> request);
}
