package com.holderzone.member.queue.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员和等级关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="HsaMemberGradeRelation对象", description="会员和等级关联表")
public class HsaMemberGradeRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "运营主体GUID")
    private String operSubjectGuid;

    @ApiModelProperty(value = "guid")
    private String guid;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "会员GUID")
    private String memberInfoGuid;

    @TableLogic
    @ApiModelProperty(value = "是否删除,0未删除,1已删除")
    private Boolean isDelete;

    @ApiModelProperty(value = "会员等级GUID")
    private String memberInfoGradeGuid;

    @ApiModelProperty(value = "到期时间")
    private LocalDateTime expireTime;

    @ApiModelProperty(value = "付费时间")
    private LocalDateTime payTime;



    @ApiModelProperty(value = "角色类型")
    private String roleType;

    /**
     * 有效期单位：3月 4年
     */
    private Integer unit;

    /**
     * 有效期数量
     */
    private Integer num;

    /**
     * "是否启用：0未启用 1启用中
     */
    private Boolean isEnable;

    /**
     * 后台等级默认序号
     */
    private Integer temporaryVipGrade;

    /**
     * 会员注册来源,0后台添加,1POS机注册,2一体机注册,3后台导入，微网站(21微信扫码点餐，24微信注册.25微信C端后台注册)，微信小程序(51和惠多，52翼惠天下，53赚餐)
     */
    private Integer sourceType;
}
