package com.holderzone.member.queue.mapper;


import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.queue.entity.HsaCardBaseInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 会员卡基础信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-12
 */
public interface HsaCardBaseInfoMapper extends HolderBaseMapper<HsaCardBaseInfo> {


    /**
     * 批量更新状态
     * @param activityList 活动列表
     */
    void batchUpdateStatus(@Param("list") List<HsaCardBaseInfo> activityList);
}
