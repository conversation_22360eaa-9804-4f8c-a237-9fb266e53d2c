package com.holderzone.member.queue.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 实体卡实体
 * @date 2021/8/31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaPhysicalCard implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 实体卡GUID
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 企业GUID
     */
    private String enterpriseGuid;


    /**
     * 会员GUID
     */
    private String memberInfoGuid;

    /**
     * 会员卡GUID
     */
    private String cardGuid;

    /**
     * 实体卡卡号
     */
    private String cardNum;

    /**
     * 来源,0后台添加,2一体机注册
     */
    private Integer source;

    /**
     * 卡状态：-1 未激活  0已冻结 1 正常 2 已过期
     */
    private Integer cardState;

    /**
     * 是否已经绑定会员：  0 否 1 是
     */
    private Integer memberBindingState;

    /**
     * 实体卡绑定码
     */
    private String cardBindingNum;

    /**
     * 实体卡绑定号码
     */
    private String phoneNum;

    /**
     * 实体卡绑定用户名称
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String userName;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 补贴余额
     */
    private BigDecimal subsidyAmount;

    /**
     * 充值余额
     */
    private BigDecimal cardAmount;

    /**
     * 赠送余额
     */
    private BigDecimal giftAmount;


    /**
     * 押金名称
     */
    private String cashName;

    /**
     * 开卡押金
     */
    private BigDecimal cashPledge;

    /**
     * 可退押金
     */
    private BigDecimal returnableCashPledge;

    /**
     * 超额使用余额
     */
    private BigDecimal excessAmount;

    /**
     * 超额使用次数
     */
    private Integer excessTimes;

    /**
     * 实体卡激活时间
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activationTime;

    /**
     * 实体卡绑定用户时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime bindUserTime;


    private String makePhysicalCardRecordGuid;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

}
