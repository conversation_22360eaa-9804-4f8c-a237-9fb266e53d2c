package com.holderzone.member.queue.mapper;


import com.holderzone.member.queue.config.mybatis.HolderBaseMapper;
import com.holderzone.member.queue.entity.HsaGrowthCommodityQuantityTouchDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 成长值任务适用商品触发条件累计明细Mapper
 */
public interface HsaGrowthCommodityQuantityTouchDetailMapper extends HolderBaseMapper<HsaGrowthCommodityQuantityTouchDetail> {
    /**
     * 批量更新
     * @param hsaOperationMemberInfos
     */
    void batchUpdateCommodityNum(@Param("list") List<HsaGrowthCommodityQuantityTouchDetail> hsaOperationMemberInfos);
}
