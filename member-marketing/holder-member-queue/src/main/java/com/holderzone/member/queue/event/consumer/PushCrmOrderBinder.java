package com.holderzone.member.queue.event.consumer;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.member.common.constant.BinderConstant;
import com.holderzone.member.common.dto.event.PushCrmOrderEvent;
import com.holderzone.member.queue.event.BinderChannel;
import com.holderzone.member.queue.event.processor.PushCrmOrderProcessor;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.messaging.handler.annotation.Header;

import java.io.IOException;

/**
 * 推送订单到crm
 */
@Slf4j
@EnableBinding(BinderChannel.class)
public class PushCrmOrderBinder {

    /**
     * 推送订单到crm
     */
    @StreamListener(BinderConstant.INPUT_PUSH_CRM_ORDER)
    public void execute(Message<PushCrmOrderEvent> message,
                        @Header(AmqpHeaders.CHANNEL) Channel channel,
                        @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) throws IOException {
        PushCrmOrderEvent requestEvent = message.getPayload();
        if (StringUtils.isEmpty(requestEvent.getSessionId())) {
            requestEvent.setSessionId(String.valueOf(System.currentTimeMillis()));
        }
        log.info("[{}] [PushCrmOrderEvent] 参数={}", requestEvent.getSessionId(), JacksonUtils.writeValueAsString(requestEvent));
        try {
            new PushCrmOrderProcessor(requestEvent).execute();
        } finally {
            //手动确认
            channel.basicAck(deliveryTag, false);
        }

    }

}