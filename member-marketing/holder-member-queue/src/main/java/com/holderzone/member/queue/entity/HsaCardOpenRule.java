package com.holderzone.member.queue.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员卡开卡规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaCardOpenRule implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * guid
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 企业GUID
     */
    private String enterpriseGuid;

    /**
     * cardGuid
     */
    private String cardGuid;

    /**
     * 是否支持电子卡
     */
    private Integer isSupportElectronicCard;

    /**
     * 电子卡开卡方式：0自主开通;1注册完成，自动发放；2指定用户直接开卡；3满足条件，自动发放（电子卡相关）
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer electronicOpenWay;

    /**
     * 自主开通类型（0免费；1直接付款；2充值）（电子卡相关）
     */
    private Integer selfType;

    /**
     * 自主开通直接付款金额（电子卡相关）
     */
    private BigDecimal selfPaymentMoney;

    /**
     * 自主开通充值金额（电子卡相关）
     */
    private BigDecimal selfRechargeMoney;

    /**
     * 指定用户用户直接开卡时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(strategy = FieldStrategy.IGNORED)
    private LocalDateTime electronicDecideOpenDate;

    /**
     * 满足条件，自动开通类型（0满足所有条件，1满足任意条件）（电子卡相关）
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer satisfyConditionType;

    /**
     * 累计消费金额（条件）
     */
    private BigDecimal consumeSumMoney;

    /**
     * 累计消费次数（条件）
     */
    private Integer consumeNum;

    /**
     * 是否有消费商品(外关联表)（条件）
     */
    private Integer isConsumeProduct;

    /**
     * 发卡渠道(多个渠道适用英文逗号分隔)（电子卡相关）
     */
    private String sendChannel;

    /**
     * 电子卡发卡状态(1:未开始;2:发送中;3:停发;4已结束;5草稿)
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer sendStatus;

    /**
     * 发卡数量限制(0表示不限制1限制)（电子卡相关）
     */
    private Integer sendCountLimit;

    /**
     * 发卡数量（电子卡相关）
     */
    private Integer sendCountNum;

    /**
     * 已开卡数量（电子卡相关）
     */
    private Integer sendOpenCountLimit;

    /**
     * 剩余可发卡数量（电子卡相关）
     */
    private Integer surplusSendOpenCountLimit;

    /**
     * 开卡时效类型（0永久有效；1固定时间段）（开卡时效）
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer openCardTimeType;

    /**
     * 开卡开始时间（开卡时效）
     */
    private LocalDateTime openCardStartTime;

    /**
     * 开卡结束时间（开卡时效）
     */
    private LocalDateTime openCardEndTime;

    /**
     * 多路径逗号隔开：2一体机；53微信小程序；54二维码（开卡路径 用于控制会员卡生成二维码、会员开卡信息是否展示小程序或一体机）
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String openCardPath;

    /**
     * 0所有注册会员；1仅满足条件会员；2指定会员(外关联表) （电子卡相关）
     */
    private Integer openCardScopeType;

    /**
     * 仅满足条件会员条件下：0满足所有；1满足任意（电子卡相关）
     */
    private Integer openCardScopeConditionType;

    /**
     * 渠道条件 多渠道逗号隔开（开卡范围相关）
     */
    private String openCardRegisterChannel;

    /**
     * 标签条件 多个标签逗号隔开（开卡范围相关）
     */
    private String openCardMemberLabelGuid;

    /**
     * 是否支持实体卡
     */
    private Integer isSupportPhysicalCard;

    /**
     * 卡规则类型  1：纯数字   2：字母+数字
     */
    private Integer cardNumType;

    /**
     * 若是字母加数字则存在，否则不存在
     */
    private String letter;

    /**
     * 押金策略（实体卡相关）
     */
    private Long openPhysicalCardStrategyGuid;

    /**
     * 实体卡是否可退（实体卡相关）
     */
    private Integer isPhysicalCardRetreat;

    /**
     * 实体卡生成数量
     */
    private Integer openPhysicalNum;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;


}
