package com.holderzone.member.queue.service.activity.assembler;

import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.queue.dto.activity.CreationSubsidyDetailRecordDTO;
import com.holderzone.member.queue.entity.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SubsidyActivityAssemblerTest {

    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;

    @InjectMocks
    private SubsidyActivityAssembler subsidyActivityAssemblerUnderTest;

    @Test
    public void testGetRechargeDetail() {
        // Setup
        final HsaMemberInfoCard memberInfoCard = new HsaMemberInfoCard();
        memberInfoCard.setId(0L);
        memberInfoCard.setGuid("guid");
        memberInfoCard.setOperSubjectGuid("operSubjectGuid");
        memberInfoCard.setEnterpriseGuid("enterpriseGuid");
        memberInfoCard.setMemberInfoGuid("guid");
        memberInfoCard.setPhysicalCardGuid("physicalCardGuid");
        memberInfoCard.setElectronicCardGuid("electronicCardGuid");
        memberInfoCard.setCardGuid("cardGuid");
        memberInfoCard.setCardName("cardName");
        memberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        memberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        memberInfoCard.setCardAmount(new BigDecimal("0.00"));
        memberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        memberInfoCard.setElectronicCardNum("electronicCardNum");
        memberInfoCard.setPhysicalCardNum("physicalCardNum");

        final HsaSubsidyActivity hsaSubsidyActivity = new HsaSubsidyActivity();
        hsaSubsidyActivity.setGuid("guid");
        hsaSubsidyActivity.setEnterpriseGuid("enterpriseGuid");
        hsaSubsidyActivity.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity.setActivityNum("activityNum");
        hsaSubsidyActivity.setActivityName("activityName");
        hsaSubsidyActivity.setCardGuidJson("cardGuidJson");
        hsaSubsidyActivity.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity.setSubsidyMoneyPeriodType(0);
        hsaSubsidyActivity.setSubsidyRecycleFrequencyType(0);
        hsaSubsidyActivity.setSubsidyRecycleAppointTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivity.setValidityUnit(0);
        hsaSubsidyActivity.setSubsidyRecycle(0);
        hsaSubsidyActivity.setSubsidyRecycleJson("subsidyRecycleJson");
        hsaSubsidyActivity.setIsRetreat(0);
        hsaSubsidyActivity.setOperatorTelName("operatorTelName");

        final HsaMemberFundingDetail memberFundingDetail = new HsaMemberFundingDetail();
        memberFundingDetail.setGuid("9f75a034-ea1d-440d-83af-ebdc92f636b3");
        memberFundingDetail.setOperSubjectGuid("operSubjectGuid");
        memberFundingDetail.setMemberInfoGuid("guid");
        memberFundingDetail.setCardNum("cardNum");
        memberFundingDetail.setCardName("cardName");
        memberFundingDetail.setCardGuid("cardGuid");
        memberFundingDetail.setCardType(0);
        memberFundingDetail.setMemberInfoCardGuid("guid");
        memberFundingDetail.setEnterpriseGuid("enterpriseGuid");
        memberFundingDetail.setAmountSourceType(0);
        memberFundingDetail.setAmountSubsidyFundingType(0);
        memberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        memberFundingDetail.setCardRechargeResidualBalance(new BigDecimal("0.00"));
        memberFundingDetail.setCardGiftResidualBalance(new BigDecimal("0.00"));
        memberFundingDetail.setCardSubsidyResidualBalance(new BigDecimal("0.00"));
        memberFundingDetail.setRemark("补贴活动金额过期");
        memberFundingDetail.setOperatorTelName("operatorTelName");
        memberFundingDetail.setIsValid(0);
        memberFundingDetail.setSourceName("activityName");
        memberFundingDetail.setChangeSource(0);
        memberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final HsaMemberFundingDetail expectedResult = new HsaMemberFundingDetail();
        expectedResult.setGuid("9f75a034-ea1d-440d-83af-ebdc92f636b3");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setMemberInfoGuid("guid");
        expectedResult.setCardNum("cardNum");
        expectedResult.setCardName("cardName");
        expectedResult.setCardGuid("cardGuid");
        expectedResult.setCardType(0);
        expectedResult.setMemberInfoCardGuid("guid");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setAmountSourceType(0);
        expectedResult.setAmountSubsidyFundingType(0);
        expectedResult.setSubsidyAmount(new BigDecimal("0.00"));
        expectedResult.setCardRechargeResidualBalance(new BigDecimal("0.00"));
        expectedResult.setCardGiftResidualBalance(new BigDecimal("0.00"));
        expectedResult.setCardSubsidyResidualBalance(new BigDecimal("0.00"));
        expectedResult.setRemark("补贴活动金额过期");
        expectedResult.setOperatorTelName("operatorTelName");
        expectedResult.setIsValid(0);
        expectedResult.setSourceName("activityName");
        expectedResult.setChangeSource(0);
        expectedResult.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockGuidGeneratorUtil.getStringGuid("simpleName")).thenReturn("9f75a034-ea1d-440d-83af-ebdc92f636b3");

        // Run the test
        final HsaMemberFundingDetail result = subsidyActivityAssemblerUnderTest.getRechargeDetail(memberInfoCard,
                hsaSubsidyActivity, LocalDateTime.of(2020, 1, 1, 0, 0, 0), memberFundingDetail);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetHsaSubsidyActivityDetailRecord() {
        // Setup
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord.setGuid("guid");
        hsaSubsidyActivityRecord.setEnterpriseGuid("enterpriseGuid");
        hsaSubsidyActivityRecord.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivityRecord.setActivityNum("activityNum");
        hsaSubsidyActivityRecord.setActivityName("activityName");
        hsaSubsidyActivityRecord.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord.setSubsidyActivityGuid("subsidyActivityGuid");
        hsaSubsidyActivityRecord.setSubsidySendState(0);
        hsaSubsidyActivityRecord.setSubsidyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityRecord.setValidityTime(0);
        hsaSubsidyActivityRecord.setSubsidyNum(0);
        hsaSubsidyActivityRecord.setSubsidyErrorNum(0);
        hsaSubsidyActivityRecord.setSubsidyMoneyPeriodType(0);
        hsaSubsidyActivityRecord.setOperatorTelName("operatorTelName");

        final HsaSubsidyActivity hsaSubsidyActivity = new HsaSubsidyActivity();
        hsaSubsidyActivity.setGuid("guid");
        hsaSubsidyActivity.setEnterpriseGuid("enterpriseGuid");
        hsaSubsidyActivity.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity.setActivityNum("activityNum");
        hsaSubsidyActivity.setActivityName("activityName");
        hsaSubsidyActivity.setCardGuidJson("cardGuidJson");
        hsaSubsidyActivity.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity.setSubsidyMoneyPeriodType(0);
        hsaSubsidyActivity.setSubsidyRecycleFrequencyType(0);
        hsaSubsidyActivity.setSubsidyRecycleAppointTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivity.setValidityUnit(0);
        hsaSubsidyActivity.setSubsidyRecycle(0);
        hsaSubsidyActivity.setSubsidyRecycleJson("subsidyRecycleJson");
        hsaSubsidyActivity.setIsRetreat(0);
        hsaSubsidyActivity.setOperatorTelName("operatorTelName");

        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("guid");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setMemberGradeInfoName("memberGradeInfoName");
        hsaOperationMemberInfo.setPhoneCountryCode("phoneCountryCode");
        hsaOperationMemberInfo.setPhoneNum("phoneNum");
        hsaOperationMemberInfo.setUserName("userName");
        hsaOperationMemberInfo.setWechatState(0);
        hsaOperationMemberInfo.setNickName("nickName");
        hsaOperationMemberInfo.setSex(0);
        hsaOperationMemberInfo.setBirthday(LocalDate.of(2020, 1, 1));
        hsaOperationMemberInfo.setEmail("email");
        hsaOperationMemberInfo.setWorkName("workName");
        hsaOperationMemberInfo.setDepartmentName("departmentName");

        final CreationSubsidyDetailRecordDTO recordDTO = new CreationSubsidyDetailRecordDTO();
        recordDTO.setError("error");
        recordDTO.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recordDTO.setResult(0);
        recordDTO.setOutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recordDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setGuid("9f75a034-ea1d-440d-83af-ebdc92f636b3");
        hsaMemberFundingDetail.setOperSubjectGuid("operSubjectGuid");
        hsaMemberFundingDetail.setMemberInfoGuid("guid");
        hsaMemberFundingDetail.setCardNum("cardNum");
        hsaMemberFundingDetail.setCardName("cardName");
        hsaMemberFundingDetail.setCardGuid("cardGuid");
        hsaMemberFundingDetail.setCardType(0);
        hsaMemberFundingDetail.setMemberInfoCardGuid("guid");
        recordDTO.setHsaMemberFundingDetail(hsaMemberFundingDetail);
        recordDTO.setAmount(new BigDecimal("0.00"));

        final HsaSubsidyActivityDetailRecord expectedResult = new HsaSubsidyActivityDetailRecord();
        expectedResult.setGuid("a11bd1c2-c81f-478f-9dce-b41b62faa800");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setActivityNum("activityNum");
        expectedResult.setActivityName("activityName");
        expectedResult.setSubsidyMoney(new BigDecimal("0.00"));
        expectedResult.setSubsidyActivityGuid("guid");
        expectedResult.setSubsidyActivityRecordGuid("guid");
        expectedResult.setSubsidyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setValidityExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setIsRetreat(0);
        expectedResult.setCardGuid("cardGuid");
        expectedResult.setMemberInfoGuid("guid");
        expectedResult.setMemberInfoCardGuid("memberInfoCardGuid");
        expectedResult.setSubsidyResult(0);
        expectedResult.setSubsidyError("error");
        expectedResult.setIsWithdraw(0);
        expectedResult.setSubsidyResidueMoney(new BigDecimal("0.00"));
        expectedResult.setOperatorTelName("operatorTelName");
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockGuidGeneratorUtil.getStringGuid("simpleName")).thenReturn("a11bd1c2-c81f-478f-9dce-b41b62faa800");

        // Run the test
        final HsaSubsidyActivityDetailRecord result = subsidyActivityAssemblerUnderTest.getHsaSubsidyActivityDetailRecord(
                hsaSubsidyActivityRecord, hsaSubsidyActivity, hsaOperationMemberInfo, recordDTO, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetHsaMemberFundingDetail() {
        // Setup
        final HsaSubsidyActivityDetailRecord detailRecord = new HsaSubsidyActivityDetailRecord();
        detailRecord.setGuid("a11bd1c2-c81f-478f-9dce-b41b62faa800");
        detailRecord.setOperSubjectGuid("operSubjectGuid");
        detailRecord.setActivityNum("activityNum");
        detailRecord.setActivityName("activityName");
        detailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        detailRecord.setSubsidyActivityGuid("guid");
        detailRecord.setSubsidyActivityRecordGuid("guid");
        detailRecord.setSubsidyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        detailRecord.setValidityExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        detailRecord.setIsRetreat(0);
        detailRecord.setCardGuid("cardGuid");
        detailRecord.setMemberInfoGuid("guid");
        detailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        detailRecord.setSubsidyResult(0);
        detailRecord.setSubsidyError("error");
        detailRecord.setIsWithdraw(0);
        detailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        detailRecord.setOperatorTelName("operatorTelName");
        detailRecord.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        detailRecord.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setId(0L);
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setOperSubjectGuid("operSubjectGuid");
        hsaMemberInfoCard.setEnterpriseGuid("enterpriseGuid");
        hsaMemberInfoCard.setMemberInfoGuid("guid");
        hsaMemberInfoCard.setPhysicalCardGuid("physicalCardGuid");
        hsaMemberInfoCard.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setCardName("cardName");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setElectronicCardNum("electronicCardNum");
        hsaMemberInfoCard.setPhysicalCardNum("physicalCardNum");

        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord.setGuid("guid");
        hsaSubsidyActivityRecord.setEnterpriseGuid("enterpriseGuid");
        hsaSubsidyActivityRecord.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivityRecord.setActivityNum("activityNum");
        hsaSubsidyActivityRecord.setActivityName("activityName");
        hsaSubsidyActivityRecord.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord.setSubsidyActivityGuid("subsidyActivityGuid");
        hsaSubsidyActivityRecord.setSubsidySendState(0);
        hsaSubsidyActivityRecord.setSubsidyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityRecord.setValidityTime(0);
        hsaSubsidyActivityRecord.setSubsidyNum(0);
        hsaSubsidyActivityRecord.setSubsidyErrorNum(0);
        hsaSubsidyActivityRecord.setSubsidyMoneyPeriodType(0);
        hsaSubsidyActivityRecord.setOperatorTelName("operatorTelName");

        final HsaMemberFundingDetail expectedResult = new HsaMemberFundingDetail();
        expectedResult.setGuid("9f75a034-ea1d-440d-83af-ebdc92f636b3");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setMemberInfoGuid("guid");
        expectedResult.setCardNum("cardNum");
        expectedResult.setCardName("cardName");
        expectedResult.setCardGuid("cardGuid");
        expectedResult.setCardType(0);
        expectedResult.setMemberInfoCardGuid("guid");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setAmountSourceType(0);
        expectedResult.setAmountSubsidyFundingType(0);
        expectedResult.setSubsidyAmount(new BigDecimal("0.00"));
        expectedResult.setCardRechargeResidualBalance(new BigDecimal("0.00"));
        expectedResult.setCardGiftResidualBalance(new BigDecimal("0.00"));
        expectedResult.setCardSubsidyResidualBalance(new BigDecimal("0.00"));
        expectedResult.setRemark("补贴活动金额过期");
        expectedResult.setOperatorTelName("operatorTelName");
        expectedResult.setIsValid(0);
        expectedResult.setSourceName("activityName");
        expectedResult.setChangeSource(0);
        expectedResult.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockGuidGeneratorUtil.getStringGuid("simpleName")).thenReturn("9f75a034-ea1d-440d-83af-ebdc92f636b3");

        // Run the test
        final HsaMemberFundingDetail result = subsidyActivityAssemblerUnderTest.getHsaMemberFundingDetail(detailRecord,
                hsaMemberInfoCard, hsaSubsidyActivityRecord);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDateToWeek() {
        assertEquals("result", SubsidyActivityAssembler.dateToWeek("datetime"));
    }
}
