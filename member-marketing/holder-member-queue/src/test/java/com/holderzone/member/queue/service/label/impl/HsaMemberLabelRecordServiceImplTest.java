package com.holderzone.member.queue.service.label.impl;

import com.holderzone.member.queue.entity.HsaMemberLabelRecord;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

public class HsaMemberLabelRecordServiceImplTest {

    private HsaMemberLabelRecordServiceImpl hsaMemberLabelRecordServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        hsaMemberLabelRecordServiceImplUnderTest = new HsaMemberLabelRecordServiceImpl();
    }

    @Test
    public void testSaveBatch() {
        // Setup
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("0b9e5bac-f982-46db-bfa2-98137960725e");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        final List<HsaMemberLabelRecord> saveMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord);

        // Run the test
        hsaMemberLabelRecordServiceImplUnderTest.saveBatch(saveMemberLabelRecordList);

        // Verify the results
    }
}
