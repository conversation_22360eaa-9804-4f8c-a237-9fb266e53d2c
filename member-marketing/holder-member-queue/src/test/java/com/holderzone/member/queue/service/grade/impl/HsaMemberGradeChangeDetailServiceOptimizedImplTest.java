package com.holderzone.member.queue.service.grade.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.util.concurrent.MoreExecutors;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.grade.GradeTypeEnum;
import com.holderzone.member.common.enums.grade.UpgradeTypeEnum;
import com.holderzone.member.common.enums.member.EnableEnum;
import com.holderzone.member.common.enums.member.RoleTypeEnum;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.queue.dto.grade.MemberGradeChangeDTO;
import com.holderzone.member.queue.entity.*;
import com.holderzone.member.queue.mapper.*;
import com.holderzone.member.queue.service.assembler.GradeChangeAssembler;
import com.holderzone.member.queue.service.cache.CacheService;
import com.holderzone.member.queue.service.grade.*;
import com.holderzone.member.queue.service.member.HsaGrowthValueDetailService;
import com.holderzone.member.queue.service.member.HsaIntegralDetailService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Executor;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 优化版会员等级变化服务测试类
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
@RunWith(MockitoJUnitRunner.class)
public class HsaMemberGradeChangeDetailServiceOptimizedImplTest {

    @Mock
    private HsaGradeGiftBagMapper hsaGradeGiftBagMapper;
    @Mock
    private HsaMemberGradeChangeDetailMapper hsaMemberGradeChangeDetailMapper;
    @Mock
    private HsaMemberGradeInfoMapper hsaMemberGradeInfoMapper;
    @Mock
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;
    @Mock
    private HsaMemberGradeRightsRecordMapper hsaMemberGradeRightsRecordMapper;
    @Mock
    private HsaGiftBagBaseInfoMapper hsaGiftBagBaseInfoMapper;
    @Mock
    private GuidGeneratorUtil guidGeneratorUtil;
    @Mock
    private HsaControlledGradeStateMapper hsaControlledGradeStateMapper;
    @Mock
    private CacheService cacheService;
    @Mock
    private HsaGradeEquitiesMapper hsaGradeEquitiesMapper;
    @Mock
    private HsaMemberEquitiesReceiveRecordMapper hsaMemberEquitiesReceiveRecordMapper;
    @Mock
    private HsaMemberGradeChangeDetailService hsaMemberGradeChangeDetailService;
    @Mock
    private HsaMemberGradeCardService hsaMemberGradeCardService;
    @Mock
    private HsaMemberGradeRightsRecordService hsaMemberGradeRightsRecordService;
    @Mock
    private HsaGrowthValueDetailService hsaGrowthValueDetailService;
    @Mock
    private HsaIntegralDetailService hsaIntegralDetailService;
    @Mock
    private HsaGradeEquitiesService hsaGradeEquitiesService;
    @Mock
    private HsaMemberEquitiesReceiveRecordServiceImpl hsaMemberEquitiesReceiveRecordService;
    @Mock
    private IHsaMemberGradeRelationService memberGradeRelationService;
    @Mock
    private IHsaMemberGradePayRecordService memberGradePayRecordService;
    @Mock
    private GradeChangeAssembler gradeChangeAssembler;

    @InjectMocks
    private HsaMemberGradeChangeDetailServiceOptimizedImpl serviceOptimized;

    @Before
    public void setUp() {
        // 设置线程池为直接执行器，便于测试
        ReflectionTestUtils.setField(serviceOptimized, "memberQueueThreadExecutor", 
                MoreExecutors.directExecutor());
    }

    @Test
    public void testMemberGradeChange_Success() {
        // Setup
        MemberGradeChangeDTO gradeChangeDTO = createTestGradeChangeDTO();
        
        // Mock 等级体系状态检查
        HsaControlledGradeState gradeState = new HsaControlledGradeState();
        gradeState.setState(EnableEnum.ENABLE.getCode());
        when(hsaControlledGradeStateMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(gradeState);

        // Mock 等级信息查询
        List<HsaMemberGradeInfo> gradeInfos = createTestGradeInfos();
        when(hsaMemberGradeInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(gradeInfos);

        // Mock 礼包基础信息
        HsaGiftBagBaseInfo giftBagBaseInfo = new HsaGiftBagBaseInfo();
        when(hsaGiftBagBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(giftBagBaseInfo);

        // Mock 权益信息
        when(hsaGradeEquitiesService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Mock 关联关系
        when(memberGradeRelationService.listByMemberGuidList(anyList(), anyString()))
                .thenReturn(Collections.emptyList());

        // Mock 批量保存操作
        when(hsaMemberGradeChangeDetailService.saveBatch(anyList(), anyInt())).thenReturn(true);
        when(hsaMemberGradeCardService.saveBatch(anyList(), anyInt())).thenReturn(true);

        // Execute
        serviceOptimized.memberGradeChange(gradeChangeDTO);

        // Verify
        verify(hsaControlledGradeStateMapper, times(1)).selectOne(any(LambdaQueryWrapper.class));
        verify(hsaMemberGradeInfoMapper, atLeastOnce()).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testMemberGradeChange_GradeSystemDisabled() {
        // Setup
        MemberGradeChangeDTO gradeChangeDTO = createTestGradeChangeDTO();
        
        // Mock 等级体系关闭
        HsaControlledGradeState gradeState = new HsaControlledGradeState();
        gradeState.setState(EnableEnum.NOT_ENABLE.getCode());
        when(hsaControlledGradeStateMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(gradeState);

        // Execute
        serviceOptimized.memberGradeChange(gradeChangeDTO);

        // Verify - 应该直接返回，不进行后续处理
        verify(hsaMemberGradeInfoMapper, never()).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testMemberGradeChange_EmptyMemberList() {
        // Setup
        MemberGradeChangeDTO gradeChangeDTO = new MemberGradeChangeDTO();
        gradeChangeDTO.setOperSubjectGuid("test-subject");
        gradeChangeDTO.setRoleType(RoleTypeEnum.MEMBER.name());
        gradeChangeDTO.setHsaOperationMemberInfos(Collections.emptyList());

        // Execute
        serviceOptimized.memberGradeChange(gradeChangeDTO);

        // Verify - 应该直接返回，不进行后续处理
        verify(hsaControlledGradeStateMapper, never()).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testCalculateExtraAmount_WithValidGrowthValue() {
        // Setup
        List<HsaMemberGradeInfo> gradeInfos = createTestGradeInfos();
        Integer memberGrowthValue = 150;

        // Execute
        HsaMemberGradeInfo result = serviceOptimized.calculateExtraAmount(memberGrowthValue, gradeInfos);

        // Verify
        assertNotNull(result);
        assertEquals("grade-1", result.getGuid());
        assertEquals(100, result.getGrowthValue());
    }

    @Test
    public void testCalculateExtraAmount_WithHighGrowthValue() {
        // Setup
        List<HsaMemberGradeInfo> gradeInfos = createTestGradeInfos();
        Integer memberGrowthValue = 350;

        // Execute
        HsaMemberGradeInfo result = serviceOptimized.calculateExtraAmount(memberGrowthValue, gradeInfos);

        // Verify
        assertNotNull(result);
        assertEquals("grade-2", result.getGuid());
        assertEquals(300, result.getGrowthValue());
    }

    @Test
    public void testCalculateExtraAmount_WithLowGrowthValue() {
        // Setup
        List<HsaMemberGradeInfo> gradeInfos = createTestGradeInfos();
        Integer memberGrowthValue = 50;

        // Execute
        HsaMemberGradeInfo result = serviceOptimized.calculateExtraAmount(memberGrowthValue, gradeInfos);

        // Verify
        assertNotNull(result);
        assertEquals("grade-0", result.getGuid());
        assertEquals(100, result.getGrowthValue());
    }

    @Test
    public void testCalculateExtraAmount_WithEmptyGradeList() {
        // Setup
        List<HsaMemberGradeInfo> gradeInfos = Collections.emptyList();
        Integer memberGrowthValue = 150;

        // Execute
        HsaMemberGradeInfo result = serviceOptimized.calculateExtraAmount(memberGrowthValue, gradeInfos);

        // Verify
        assertNull(result);
    }

    @Test
    public void testCalculateExtraAmount_WithOperSubjectGuid() {
        // Setup
        String operSubjectGuid = "test-subject";
        Integer memberGrowthValue = 150;
        
        List<HsaMemberGradeInfo> gradeInfos = createTestGradeInfos();
        when(hsaMemberGradeInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(gradeInfos);

        // Execute
        HsaMemberGradeInfo result = serviceOptimized.calculateExtraAmount(memberGrowthValue, operSubjectGuid);

        // Verify
        assertNotNull(result);
        verify(hsaMemberGradeInfoMapper, times(1)).selectList(any(LambdaQueryWrapper.class));
    }

    @Test(expected = RuntimeException.class)
    public void testMemberGradeChange_WithException() {
        // Setup
        MemberGradeChangeDTO gradeChangeDTO = createTestGradeChangeDTO();
        
        // Mock 异常
        when(hsaControlledGradeStateMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenThrow(new RuntimeException("Database error"));

        // Execute - 应该抛出异常
        serviceOptimized.memberGradeChange(gradeChangeDTO);
    }

    // ==================== 辅助方法 ====================

    private MemberGradeChangeDTO createTestGradeChangeDTO() {
        MemberGradeChangeDTO dto = new MemberGradeChangeDTO();
        dto.setOperSubjectGuid("test-subject");
        dto.setRoleType(RoleTypeEnum.MEMBER.name());
        dto.setSourceType(1);
        dto.setIsRefresh(BooleanEnum.FALSE.getCode());
        
        // 创建测试会员信息
        HsaOperationMemberInfo memberInfo = new HsaOperationMemberInfo();
        memberInfo.setGuid("member-1");
        memberInfo.setOperSubjectGuid("test-subject");
        memberInfo.setMemberGradeInfoGuid("old-grade");
        memberInfo.setMemberGradeInfoName("Old Grade");
        memberInfo.setMemberGrowthValue(150);
        memberInfo.setMemberIntegral(100);
        memberInfo.setUpgradeTime(LocalDateTime.now());
        
        dto.setHsaOperationMemberInfos(Arrays.asList(memberInfo));
        
        return dto;
    }

    private List<HsaMemberGradeInfo> createTestGradeInfos() {
        HsaMemberGradeInfo grade0 = new HsaMemberGradeInfo();
        grade0.setGuid("grade-0");
        grade0.setName("Bronze");
        grade0.setVipGrade(1);
        grade0.setGrowthValue(100);
        grade0.setUpgradeType(UpgradeTypeEnum.GROWTH_VALUE.getCode());
        grade0.setType(GradeTypeEnum.FREE.getCode());
        grade0.setEffective(BooleanEnum.TRUE.getCode());
        grade0.setIsDelete(BooleanEnum.FALSE.getCode());

        HsaMemberGradeInfo grade1 = new HsaMemberGradeInfo();
        grade1.setGuid("grade-1");
        grade1.setName("Silver");
        grade1.setVipGrade(2);
        grade1.setGrowthValue(200);
        grade1.setUpgradeType(UpgradeTypeEnum.GROWTH_VALUE.getCode());
        grade1.setType(GradeTypeEnum.FREE.getCode());
        grade1.setEffective(BooleanEnum.TRUE.getCode());
        grade1.setIsDelete(BooleanEnum.FALSE.getCode());

        HsaMemberGradeInfo grade2 = new HsaMemberGradeInfo();
        grade2.setGuid("grade-2");
        grade2.setName("Gold");
        grade2.setVipGrade(3);
        grade2.setGrowthValue(300);
        grade2.setUpgradeType(UpgradeTypeEnum.GROWTH_VALUE.getCode());
        grade2.setType(GradeTypeEnum.FREE.getCode());
        grade2.setEffective(BooleanEnum.TRUE.getCode());
        grade2.setIsDelete(BooleanEnum.FALSE.getCode());

        return Arrays.asList(grade0, grade1, grade2);
    }

    @Test
    public void testProcessMemberGradeBatch_Success() {
        // Setup
        MemberGradeChangeDTO gradeChangeDTO = createTestGradeChangeDTO();
        List<HsaOperationMemberInfo> memberBatch = gradeChangeDTO.getHsaOperationMemberInfos();

        // Mock 等级信息查询
        List<HsaMemberGradeInfo> gradeInfos = createTestGradeInfos();
        when(hsaMemberGradeInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(gradeInfos);

        // Mock 礼包基础信息
        HsaGiftBagBaseInfo giftBagBaseInfo = new HsaGiftBagBaseInfo();
        when(hsaGiftBagBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(giftBagBaseInfo);

        // Mock 权益信息
        when(hsaGradeEquitiesService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Mock 关联关系
        when(memberGradeRelationService.listByMemberGuidList(anyList(), anyString()))
                .thenReturn(Collections.emptyList());

        // Mock 批量保存操作
        when(hsaMemberGradeChangeDetailService.saveBatch(anyList(), anyInt())).thenReturn(true);
        when(hsaMemberGradeCardService.saveBatch(anyList(), anyInt())).thenReturn(true);

        // Execute
        serviceOptimized.processMemberGradeBatch(gradeChangeDTO, memberBatch);

        // Verify
        verify(hsaMemberGradeInfoMapper, atLeastOnce()).selectList(any(LambdaQueryWrapper.class));
        verify(hsaOperationMemberInfoMapper, times(1)).batchUpdateGrowth(anyList());
    }

    @Test
    public void testBatchProcessing_LargeMemberList() {
        // Setup - 创建大量会员数据
        MemberGradeChangeDTO gradeChangeDTO = new MemberGradeChangeDTO();
        gradeChangeDTO.setOperSubjectGuid("test-subject");
        gradeChangeDTO.setRoleType(RoleTypeEnum.MEMBER.name());
        gradeChangeDTO.setSourceType(1);
        gradeChangeDTO.setIsRefresh(BooleanEnum.FALSE.getCode());

        // 创建1200个会员（超过批处理大小500）
        List<HsaOperationMemberInfo> memberInfos = createLargeMemberList(1200);
        gradeChangeDTO.setHsaOperationMemberInfos(memberInfos);

        // Mock 等级体系状态检查
        HsaControlledGradeState gradeState = new HsaControlledGradeState();
        gradeState.setState(EnableEnum.ENABLE.getCode());
        when(hsaControlledGradeStateMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(gradeState);

        // Mock 等级信息查询
        List<HsaMemberGradeInfo> gradeInfos = createTestGradeInfos();
        when(hsaMemberGradeInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(gradeInfos);

        // Mock 礼包基础信息
        HsaGiftBagBaseInfo giftBagBaseInfo = new HsaGiftBagBaseInfo();
        when(hsaGiftBagBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(giftBagBaseInfo);

        // Mock 权益信息
        when(hsaGradeEquitiesService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Mock 关联关系
        when(memberGradeRelationService.listByMemberGuidList(anyList(), anyString()))
                .thenReturn(Collections.emptyList());

        // Mock 批量保存操作
        when(hsaMemberGradeChangeDetailService.saveBatch(anyList(), anyInt())).thenReturn(true);
        when(hsaMemberGradeCardService.saveBatch(anyList(), anyInt())).thenReturn(true);

        // Execute
        serviceOptimized.memberGradeChange(gradeChangeDTO);

        // Verify - 应该分3批处理（500 + 500 + 200）
        verify(hsaMemberGradeInfoMapper, atLeast(3)).selectList(any(LambdaQueryWrapper.class));
        verify(hsaOperationMemberInfoMapper, atLeast(3)).batchUpdateGrowth(anyList());
    }

    @Test
    public void testCacheCleanup() {
        // Setup
        MemberGradeChangeDTO gradeChangeDTO = createTestGradeChangeDTO();
        gradeChangeDTO.setIsRefresh(BooleanEnum.TRUE.getCode());

        // Mock 缓存清理
        when(cacheService.cleanToken(anyString())).thenReturn(true);

        // Mock 等级体系状态检查
        HsaControlledGradeState gradeState = new HsaControlledGradeState();
        gradeState.setState(EnableEnum.ENABLE.getCode());
        when(hsaControlledGradeStateMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(gradeState);

        // Mock 等级信息查询
        List<HsaMemberGradeInfo> gradeInfos = createTestGradeInfos();
        when(hsaMemberGradeInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(gradeInfos);

        // Mock 礼包基础信息
        HsaGiftBagBaseInfo giftBagBaseInfo = new HsaGiftBagBaseInfo();
        when(hsaGiftBagBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(giftBagBaseInfo);

        // Mock 权益信息
        when(hsaGradeEquitiesService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Mock 关联关系
        when(memberGradeRelationService.listByMemberGuidList(anyList(), anyString()))
                .thenReturn(Collections.emptyList());

        // Mock 批量保存操作
        when(hsaMemberGradeChangeDetailService.saveBatch(anyList(), anyInt())).thenReturn(true);
        when(hsaMemberGradeCardService.saveBatch(anyList(), anyInt())).thenReturn(true);

        // Execute
        serviceOptimized.memberGradeChange(gradeChangeDTO);

        // Verify - 应该调用缓存清理
        verify(cacheService, atLeast(2)).cleanToken(anyString());
    }

    @Test
    public void testIdempotency_SameGradeNoChange() {
        // Setup - 会员当前等级与计算出的等级相同
        MemberGradeChangeDTO gradeChangeDTO = createTestGradeChangeDTO();
        HsaOperationMemberInfo memberInfo = gradeChangeDTO.getHsaOperationMemberInfos().get(0);
        memberInfo.setMemberGradeInfoGuid("grade-1"); // 设置为当前应该的等级
        memberInfo.setMemberGrowthValue(150); // 对应grade-1等级

        // Mock 等级体系状态检查
        HsaControlledGradeState gradeState = new HsaControlledGradeState();
        gradeState.setState(EnableEnum.ENABLE.getCode());
        when(hsaControlledGradeStateMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(gradeState);

        // Mock 等级信息查询
        List<HsaMemberGradeInfo> gradeInfos = createTestGradeInfos();
        when(hsaMemberGradeInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(gradeInfos);

        // Mock 礼包基础信息
        HsaGiftBagBaseInfo giftBagBaseInfo = new HsaGiftBagBaseInfo();
        when(hsaGiftBagBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(giftBagBaseInfo);

        // Mock 权益信息
        when(hsaGradeEquitiesService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Mock 关联关系
        when(memberGradeRelationService.listByMemberGuidList(anyList(), anyString()))
                .thenReturn(Collections.emptyList());

        // Execute
        serviceOptimized.memberGradeChange(gradeChangeDTO);

        // Verify - 等级没有变化，不应该保存变更明细
        verify(hsaMemberGradeChangeDetailService, never()).saveBatch(anyList(), anyInt());
        verify(hsaMemberGradeCardService, never()).saveBatch(anyList(), anyInt());
    }

    private List<HsaOperationMemberInfo> createLargeMemberList(int count) {
        List<HsaOperationMemberInfo> memberInfos = new java.util.ArrayList<>();
        for (int i = 0; i < count; i++) {
            HsaOperationMemberInfo memberInfo = new HsaOperationMemberInfo();
            memberInfo.setGuid("member-" + i);
            memberInfo.setOperSubjectGuid("test-subject");
            memberInfo.setMemberGradeInfoGuid("old-grade-" + i);
            memberInfo.setMemberGradeInfoName("Old Grade " + i);
            memberInfo.setMemberGrowthValue(100 + (i % 300)); // 变化的成长值
            memberInfo.setMemberIntegral(50 + (i % 100));
            memberInfo.setUpgradeTime(LocalDateTime.now());
            memberInfos.add(memberInfo);
        }
        return memberInfos;
    }
}
