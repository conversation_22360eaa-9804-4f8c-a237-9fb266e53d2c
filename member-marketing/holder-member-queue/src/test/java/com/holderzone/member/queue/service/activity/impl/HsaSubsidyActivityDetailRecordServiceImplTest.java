package com.holderzone.member.queue.service.activity.impl;

import com.holderzone.member.queue.entity.HsaSubsidyActivityDetailRecord;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class HsaSubsidyActivityDetailRecordServiceImplTest {

    private HsaSubsidyActivityDetailRecordServiceImpl hsaSubsidyActivityDetailRecordServiceImplUnderTest;

    @Before
    public void setUp() {
        hsaSubsidyActivityDetailRecordServiceImplUnderTest = new HsaSubsidyActivityDetailRecordServiceImpl();
    }

    @Test
    public void testQueryByGuid() {
        // Setup
        final HsaSubsidyActivityDetailRecord expectedResult = new HsaSubsidyActivityDetailRecord();
        expectedResult.setGuid("89ea5c64-7486-4e6d-9a00-017ace70a1de");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setActivityNum("activityNum");
        expectedResult.setActivityName("activityName");
        expectedResult.setSubsidyMoney(new BigDecimal("0.00"));

        // Run the test
        final HsaSubsidyActivityDetailRecord result = hsaSubsidyActivityDetailRecordServiceImplUnderTest.queryByGuid(
                "6e1caeea-0ab2-49e6-b0a0-096a8663d590");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryByGuids() {
        // Setup
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord.setGuid("89ea5c64-7486-4e6d-9a00-017ace70a1de");
        hsaSubsidyActivityDetailRecord.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivityDetailRecord.setActivityNum("activityNum");
        hsaSubsidyActivityDetailRecord.setActivityName("activityName");
        hsaSubsidyActivityDetailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> expectedResult = Arrays.asList(hsaSubsidyActivityDetailRecord);

        // Run the test
        final List<HsaSubsidyActivityDetailRecord> result = hsaSubsidyActivityDetailRecordServiceImplUnderTest.queryByGuids(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateByGuid() {
        // Setup
        final HsaSubsidyActivityDetailRecord t = new HsaSubsidyActivityDetailRecord();
        t.setGuid("89ea5c64-7486-4e6d-9a00-017ace70a1de");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setActivityNum("activityNum");
        t.setActivityName("activityName");
        t.setSubsidyMoney(new BigDecimal("0.00"));

        // Run the test
        final boolean result = hsaSubsidyActivityDetailRecordServiceImplUnderTest.updateByGuid(t);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemoveByGuid() {
        // Setup
        // Run the test
        final boolean result = hsaSubsidyActivityDetailRecordServiceImplUnderTest.removeByGuid(
                "18746a1c-2660-4146-85d1-998b444a566b");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemoveByGuids() {
        // Setup
        // Run the test
        final boolean result = hsaSubsidyActivityDetailRecordServiceImplUnderTest.removeByGuids(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isFalse();
    }
}
