package com.holderzone.member.queue.service.label.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.dto.label.CancelConnectionTypeDTO;
import com.holderzone.member.common.dto.label.MemberLabelRecordDTO;
import com.holderzone.member.common.dto.label.RequestLabelQuery;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.queue.dto.label.RelationLabelDTO;
import com.holderzone.member.queue.entity.HsaLabelSetting;
import com.holderzone.member.queue.entity.HsaLabelSettingBaseInfo;
import com.holderzone.member.queue.entity.HsaMemberLabel;
import com.holderzone.member.queue.entity.HsaMemberLabelRecord;
import com.holderzone.member.queue.mapper.*;
import com.holderzone.member.queue.service.label.HsaMemberLabelRecordService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaLabelSettingServiceImplTest {

    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private HsaMemberLabelMapper mockHsaMemberLabelMapper;
    @Mock
    private HsaMemberLabelRecordMapper mockHsaMemberLabelRecordMapper;
    @Mock
    private HsaMemberLabelRecordService mockHsaMemberLabelRecordService;
    @Mock
    private HsaLabelSettingBaseInfoMapper mockHsaLabelSettingBaseInfoMapper;
    @Mock
    private HsaGrowthValueDetailMapper mockHsaGrowthValueDetailMapper;
    @Mock
    private RedissonClient mockRedissonClient;
    @Mock
    private HsaLabelSettingMapper mockHsaLabelSettingMapper;

    @InjectMocks
    private HsaLabelSettingServiceImpl hsaLabelSettingServiceImplUnderTest;

    @Test
    public void testRelationLabel() {
        // Setup
        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");
        final RelationLabelDTO event = new RelationLabelDTO(headerUserInfo, new HashSet<>(Arrays.asList("value")));

        // Configure HsaLabelSettingMapper.getHsaLabelSetting(...).
        final HsaLabelSetting hsaLabelSetting = new HsaLabelSetting();
        hsaLabelSetting.setGuid("guid");
        hsaLabelSetting.setOperSubjectGuid("operSubjectGuid");
        hsaLabelSetting.setLabelType(0);
        hsaLabelSetting.setLabelName("labelName");
        hsaLabelSetting.setConditionSet(0);
        final List<HsaLabelSetting> hsaLabelSettings = Arrays.asList(hsaLabelSetting);
        when(mockHsaLabelSettingMapper.getHsaLabelSetting("operSubjectGuid")).thenReturn(hsaLabelSettings);

        // Configure HsaLabelSettingBaseInfoMapper.selectOne(...).
        final HsaLabelSettingBaseInfo hsaLabelSettingBaseInfo = new HsaLabelSettingBaseInfo();
        hsaLabelSettingBaseInfo.setLabelSettingGuid("labelSettingGuid");
        hsaLabelSettingBaseInfo.setSex("sex");
        hsaLabelSettingBaseInfo.setSetBaseInfo(false);
        hsaLabelSettingBaseInfo.setCardMemberLevelGuid("cardMemberLevelGuid");
        hsaLabelSettingBaseInfo.setGrowValueFixedDateJson("growValueFixedDateJson");
        when(mockHsaLabelSettingBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaLabelSettingBaseInfo);

        // Configure HsaGrowthValueDetailMapper.findRelationLabelMemberGuid(...).
        final RequestLabelQuery query = new RequestLabelQuery();
        query.setSex(new String[]{"sex"});
        query.setMemberGuid(new HashSet<>(Arrays.asList("value")));
        query.setOperSubjectGuid("operSubjectGuid");
        query.setCardMemberLevelGuid(new String[]{"cardMemberLevelGuid"});
        query.setGrowValueFixedDateJson(new String[]{"growValueFixedDateJson"});
        when(mockHsaGrowthValueDetailMapper.findRelationLabelMemberGuid(query)).thenReturn(Arrays.asList("value"));

        when(mockRedissonClient.getLock("SAVE_LABEL_MEMBER")).thenReturn(null);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionType(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS = Arrays.asList(cancelConnectionTypeDTO);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionType("guid")).thenReturn(cancelConnectionTypeDTOS);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO1 = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO1.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO1.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS1 = Arrays.asList(cancelConnectionTypeDTO1);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember("guid",
                new HashSet<>(Arrays.asList("value")))).thenReturn(cancelConnectionTypeDTOS1);

        // Configure HsaMemberLabelRecordMapper.selectList(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> hsaMemberLabelRecords = Arrays.asList(hsaMemberLabelRecord);
        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberLabelRecords);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.relationLabel(event);

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord1 = new HsaMemberLabelRecord();
        hsaMemberLabelRecord1.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord1.setLabelName("labelName");
        hsaMemberLabelRecord1.setLabelType(0);
        hsaMemberLabelRecord1.setConnectionType(0);
        hsaMemberLabelRecord1.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setIsConnection(0);
        hsaMemberLabelRecord1.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionType(0);
        hsaMemberLabelRecord1.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord1.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord1);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
        verify(mockHsaMemberLabelMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm HsaMemberLabelMapper.batchSave(...).
        final HsaMemberLabel hsaMemberLabel1 = new HsaMemberLabel();
        hsaMemberLabel1.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel1.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels1 = Arrays.asList(hsaMemberLabel1);
        verify(mockHsaMemberLabelMapper).batchSave(hsaMemberLabels1);
    }

    @Test
    public void testRelationLabel_HsaLabelSettingMapperReturnsNoItems() {
        // Setup
        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");
        final RelationLabelDTO event = new RelationLabelDTO(headerUserInfo, new HashSet<>(Arrays.asList("value")));
        when(mockHsaLabelSettingMapper.getHsaLabelSetting("operSubjectGuid")).thenReturn(Collections.emptyList());

        // Run the test
        hsaLabelSettingServiceImplUnderTest.relationLabel(event);

        // Verify the results
    }

    @Test
    public void testRelationLabel_HsaGrowthValueDetailMapperReturnsNoItems() {
        // Setup
        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");
        final RelationLabelDTO event = new RelationLabelDTO(headerUserInfo, new HashSet<>(Arrays.asList("value")));

        // Configure HsaLabelSettingMapper.getHsaLabelSetting(...).
        final HsaLabelSetting hsaLabelSetting = new HsaLabelSetting();
        hsaLabelSetting.setGuid("guid");
        hsaLabelSetting.setOperSubjectGuid("operSubjectGuid");
        hsaLabelSetting.setLabelType(0);
        hsaLabelSetting.setLabelName("labelName");
        hsaLabelSetting.setConditionSet(0);
        final List<HsaLabelSetting> hsaLabelSettings = Arrays.asList(hsaLabelSetting);
        when(mockHsaLabelSettingMapper.getHsaLabelSetting("operSubjectGuid")).thenReturn(hsaLabelSettings);

        // Configure HsaLabelSettingBaseInfoMapper.selectOne(...).
        final HsaLabelSettingBaseInfo hsaLabelSettingBaseInfo = new HsaLabelSettingBaseInfo();
        hsaLabelSettingBaseInfo.setLabelSettingGuid("labelSettingGuid");
        hsaLabelSettingBaseInfo.setSex("sex");
        hsaLabelSettingBaseInfo.setSetBaseInfo(false);
        hsaLabelSettingBaseInfo.setCardMemberLevelGuid("cardMemberLevelGuid");
        hsaLabelSettingBaseInfo.setGrowValueFixedDateJson("growValueFixedDateJson");
        when(mockHsaLabelSettingBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaLabelSettingBaseInfo);

        // Configure HsaGrowthValueDetailMapper.findRelationLabelMemberGuid(...).
        final RequestLabelQuery query = new RequestLabelQuery();
        query.setSex(new String[]{"sex"});
        query.setMemberGuid(new HashSet<>(Arrays.asList("value")));
        query.setOperSubjectGuid("operSubjectGuid");
        query.setCardMemberLevelGuid(new String[]{"cardMemberLevelGuid"});
        query.setGrowValueFixedDateJson(new String[]{"growValueFixedDateJson"});
        when(mockHsaGrowthValueDetailMapper.findRelationLabelMemberGuid(query)).thenReturn(Collections.emptyList());

        when(mockRedissonClient.getLock("SAVE_LABEL_MEMBER")).thenReturn(null);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionType(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS = Arrays.asList(cancelConnectionTypeDTO);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionType("guid")).thenReturn(cancelConnectionTypeDTOS);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO1 = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO1.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO1.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS1 = Arrays.asList(cancelConnectionTypeDTO1);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember("guid",
                new HashSet<>(Arrays.asList("value")))).thenReturn(cancelConnectionTypeDTOS1);

        // Configure HsaMemberLabelRecordMapper.selectList(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> hsaMemberLabelRecords = Arrays.asList(hsaMemberLabelRecord);
        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberLabelRecords);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.relationLabel(event);

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord1 = new HsaMemberLabelRecord();
        hsaMemberLabelRecord1.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord1.setLabelName("labelName");
        hsaMemberLabelRecord1.setLabelType(0);
        hsaMemberLabelRecord1.setConnectionType(0);
        hsaMemberLabelRecord1.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setIsConnection(0);
        hsaMemberLabelRecord1.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionType(0);
        hsaMemberLabelRecord1.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord1.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord1);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
        verify(mockHsaMemberLabelMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm HsaMemberLabelMapper.batchSave(...).
        final HsaMemberLabel hsaMemberLabel1 = new HsaMemberLabel();
        hsaMemberLabel1.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel1.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels1 = Arrays.asList(hsaMemberLabel1);
        verify(mockHsaMemberLabelMapper).batchSave(hsaMemberLabels1);
    }

    @Test
    public void testRelationLabel_RedissonClientReturnsNull() {
        // Setup
        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");
        final RelationLabelDTO event = new RelationLabelDTO(headerUserInfo, new HashSet<>(Arrays.asList("value")));

        // Configure HsaLabelSettingMapper.getHsaLabelSetting(...).
        final HsaLabelSetting hsaLabelSetting = new HsaLabelSetting();
        hsaLabelSetting.setGuid("guid");
        hsaLabelSetting.setOperSubjectGuid("operSubjectGuid");
        hsaLabelSetting.setLabelType(0);
        hsaLabelSetting.setLabelName("labelName");
        hsaLabelSetting.setConditionSet(0);
        final List<HsaLabelSetting> hsaLabelSettings = Arrays.asList(hsaLabelSetting);
        when(mockHsaLabelSettingMapper.getHsaLabelSetting("operSubjectGuid")).thenReturn(hsaLabelSettings);

        // Configure HsaLabelSettingBaseInfoMapper.selectOne(...).
        final HsaLabelSettingBaseInfo hsaLabelSettingBaseInfo = new HsaLabelSettingBaseInfo();
        hsaLabelSettingBaseInfo.setLabelSettingGuid("labelSettingGuid");
        hsaLabelSettingBaseInfo.setSex("sex");
        hsaLabelSettingBaseInfo.setSetBaseInfo(false);
        hsaLabelSettingBaseInfo.setCardMemberLevelGuid("cardMemberLevelGuid");
        hsaLabelSettingBaseInfo.setGrowValueFixedDateJson("growValueFixedDateJson");
        when(mockHsaLabelSettingBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaLabelSettingBaseInfo);

        // Configure HsaGrowthValueDetailMapper.findRelationLabelMemberGuid(...).
        final RequestLabelQuery query = new RequestLabelQuery();
        query.setSex(new String[]{"sex"});
        query.setMemberGuid(new HashSet<>(Arrays.asList("value")));
        query.setOperSubjectGuid("operSubjectGuid");
        query.setCardMemberLevelGuid(new String[]{"cardMemberLevelGuid"});
        query.setGrowValueFixedDateJson(new String[]{"growValueFixedDateJson"});
        when(mockHsaGrowthValueDetailMapper.findRelationLabelMemberGuid(query)).thenReturn(Arrays.asList("value"));

        when(mockRedissonClient.getLock("SAVE_LABEL_MEMBER")).thenReturn(null);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionType(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS = Arrays.asList(cancelConnectionTypeDTO);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionType("guid")).thenReturn(cancelConnectionTypeDTOS);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO1 = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO1.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO1.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS1 = Arrays.asList(cancelConnectionTypeDTO1);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember("guid",
                new HashSet<>(Arrays.asList("value")))).thenReturn(cancelConnectionTypeDTOS1);

        // Configure HsaMemberLabelRecordMapper.selectList(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> hsaMemberLabelRecords = Arrays.asList(hsaMemberLabelRecord);
        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberLabelRecords);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.relationLabel(event);

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord1 = new HsaMemberLabelRecord();
        hsaMemberLabelRecord1.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord1.setLabelName("labelName");
        hsaMemberLabelRecord1.setLabelType(0);
        hsaMemberLabelRecord1.setConnectionType(0);
        hsaMemberLabelRecord1.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setIsConnection(0);
        hsaMemberLabelRecord1.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionType(0);
        hsaMemberLabelRecord1.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord1.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord1);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
        verify(mockHsaMemberLabelMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm HsaMemberLabelMapper.batchSave(...).
        final HsaMemberLabel hsaMemberLabel1 = new HsaMemberLabel();
        hsaMemberLabel1.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel1.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels1 = Arrays.asList(hsaMemberLabel1);
        verify(mockHsaMemberLabelMapper).batchSave(hsaMemberLabels1);
    }

    @Test
    public void testRelationLabel_HsaMemberLabelMapperSelectListReturnsNoItems() {
        // Setup
        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");
        final RelationLabelDTO event = new RelationLabelDTO(headerUserInfo, new HashSet<>(Arrays.asList("value")));

        // Configure HsaLabelSettingMapper.getHsaLabelSetting(...).
        final HsaLabelSetting hsaLabelSetting = new HsaLabelSetting();
        hsaLabelSetting.setGuid("guid");
        hsaLabelSetting.setOperSubjectGuid("operSubjectGuid");
        hsaLabelSetting.setLabelType(0);
        hsaLabelSetting.setLabelName("labelName");
        hsaLabelSetting.setConditionSet(0);
        final List<HsaLabelSetting> hsaLabelSettings = Arrays.asList(hsaLabelSetting);
        when(mockHsaLabelSettingMapper.getHsaLabelSetting("operSubjectGuid")).thenReturn(hsaLabelSettings);

        // Configure HsaLabelSettingBaseInfoMapper.selectOne(...).
        final HsaLabelSettingBaseInfo hsaLabelSettingBaseInfo = new HsaLabelSettingBaseInfo();
        hsaLabelSettingBaseInfo.setLabelSettingGuid("labelSettingGuid");
        hsaLabelSettingBaseInfo.setSex("sex");
        hsaLabelSettingBaseInfo.setSetBaseInfo(false);
        hsaLabelSettingBaseInfo.setCardMemberLevelGuid("cardMemberLevelGuid");
        hsaLabelSettingBaseInfo.setGrowValueFixedDateJson("growValueFixedDateJson");
        when(mockHsaLabelSettingBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaLabelSettingBaseInfo);

        // Configure HsaGrowthValueDetailMapper.findRelationLabelMemberGuid(...).
        final RequestLabelQuery query = new RequestLabelQuery();
        query.setSex(new String[]{"sex"});
        query.setMemberGuid(new HashSet<>(Arrays.asList("value")));
        query.setOperSubjectGuid("operSubjectGuid");
        query.setCardMemberLevelGuid(new String[]{"cardMemberLevelGuid"});
        query.setGrowValueFixedDateJson(new String[]{"growValueFixedDateJson"});
        when(mockHsaGrowthValueDetailMapper.findRelationLabelMemberGuid(query)).thenReturn(Arrays.asList("value"));

        when(mockRedissonClient.getLock("SAVE_LABEL_MEMBER")).thenReturn(null);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionType(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS = Arrays.asList(cancelConnectionTypeDTO);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionType("guid")).thenReturn(cancelConnectionTypeDTOS);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO1 = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO1.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO1.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS1 = Arrays.asList(cancelConnectionTypeDTO1);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember("guid",
                new HashSet<>(Arrays.asList("value")))).thenReturn(cancelConnectionTypeDTOS1);

        // Configure HsaMemberLabelRecordMapper.selectList(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> hsaMemberLabelRecords = Arrays.asList(hsaMemberLabelRecord);
        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberLabelRecords);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.relationLabel(event);

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord1 = new HsaMemberLabelRecord();
        hsaMemberLabelRecord1.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord1.setLabelName("labelName");
        hsaMemberLabelRecord1.setLabelType(0);
        hsaMemberLabelRecord1.setConnectionType(0);
        hsaMemberLabelRecord1.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setIsConnection(0);
        hsaMemberLabelRecord1.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionType(0);
        hsaMemberLabelRecord1.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord1.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord1);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
        verify(mockHsaMemberLabelMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm HsaMemberLabelMapper.batchSave(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        verify(mockHsaMemberLabelMapper).batchSave(hsaMemberLabels);
    }

    @Test
    public void testRelationLabel_HsaMemberLabelRecordMapperFindLastCancelConnectionTypeReturnsNoItems() {
        // Setup
        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");
        final RelationLabelDTO event = new RelationLabelDTO(headerUserInfo, new HashSet<>(Arrays.asList("value")));

        // Configure HsaLabelSettingMapper.getHsaLabelSetting(...).
        final HsaLabelSetting hsaLabelSetting = new HsaLabelSetting();
        hsaLabelSetting.setGuid("guid");
        hsaLabelSetting.setOperSubjectGuid("operSubjectGuid");
        hsaLabelSetting.setLabelType(0);
        hsaLabelSetting.setLabelName("labelName");
        hsaLabelSetting.setConditionSet(0);
        final List<HsaLabelSetting> hsaLabelSettings = Arrays.asList(hsaLabelSetting);
        when(mockHsaLabelSettingMapper.getHsaLabelSetting("operSubjectGuid")).thenReturn(hsaLabelSettings);

        // Configure HsaLabelSettingBaseInfoMapper.selectOne(...).
        final HsaLabelSettingBaseInfo hsaLabelSettingBaseInfo = new HsaLabelSettingBaseInfo();
        hsaLabelSettingBaseInfo.setLabelSettingGuid("labelSettingGuid");
        hsaLabelSettingBaseInfo.setSex("sex");
        hsaLabelSettingBaseInfo.setSetBaseInfo(false);
        hsaLabelSettingBaseInfo.setCardMemberLevelGuid("cardMemberLevelGuid");
        hsaLabelSettingBaseInfo.setGrowValueFixedDateJson("growValueFixedDateJson");
        when(mockHsaLabelSettingBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaLabelSettingBaseInfo);

        // Configure HsaGrowthValueDetailMapper.findRelationLabelMemberGuid(...).
        final RequestLabelQuery query = new RequestLabelQuery();
        query.setSex(new String[]{"sex"});
        query.setMemberGuid(new HashSet<>(Arrays.asList("value")));
        query.setOperSubjectGuid("operSubjectGuid");
        query.setCardMemberLevelGuid(new String[]{"cardMemberLevelGuid"});
        query.setGrowValueFixedDateJson(new String[]{"growValueFixedDateJson"});
        when(mockHsaGrowthValueDetailMapper.findRelationLabelMemberGuid(query)).thenReturn(Arrays.asList("value"));

        when(mockRedissonClient.getLock("SAVE_LABEL_MEMBER")).thenReturn(null);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionType("guid")).thenReturn(Collections.emptyList());

        // Configure HsaMemberLabelRecordMapper.selectList(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> hsaMemberLabelRecords = Arrays.asList(hsaMemberLabelRecord);
        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberLabelRecords);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.relationLabel(event);

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord1 = new HsaMemberLabelRecord();
        hsaMemberLabelRecord1.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord1.setLabelName("labelName");
        hsaMemberLabelRecord1.setLabelType(0);
        hsaMemberLabelRecord1.setConnectionType(0);
        hsaMemberLabelRecord1.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setIsConnection(0);
        hsaMemberLabelRecord1.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionType(0);
        hsaMemberLabelRecord1.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord1.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord1);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
        verify(mockHsaMemberLabelMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm HsaMemberLabelMapper.batchSave(...).
        final HsaMemberLabel hsaMemberLabel1 = new HsaMemberLabel();
        hsaMemberLabel1.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel1.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels1 = Arrays.asList(hsaMemberLabel1);
        verify(mockHsaMemberLabelMapper).batchSave(hsaMemberLabels1);
    }

    @Test
    public void testRelationLabel_HsaMemberLabelRecordMapperFindLastCancelConnectionTypeInMemberReturnsNoItems() {
        // Setup
        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");
        final RelationLabelDTO event = new RelationLabelDTO(headerUserInfo, new HashSet<>(Arrays.asList("value")));

        // Configure HsaLabelSettingMapper.getHsaLabelSetting(...).
        final HsaLabelSetting hsaLabelSetting = new HsaLabelSetting();
        hsaLabelSetting.setGuid("guid");
        hsaLabelSetting.setOperSubjectGuid("operSubjectGuid");
        hsaLabelSetting.setLabelType(0);
        hsaLabelSetting.setLabelName("labelName");
        hsaLabelSetting.setConditionSet(0);
        final List<HsaLabelSetting> hsaLabelSettings = Arrays.asList(hsaLabelSetting);
        when(mockHsaLabelSettingMapper.getHsaLabelSetting("operSubjectGuid")).thenReturn(hsaLabelSettings);

        // Configure HsaLabelSettingBaseInfoMapper.selectOne(...).
        final HsaLabelSettingBaseInfo hsaLabelSettingBaseInfo = new HsaLabelSettingBaseInfo();
        hsaLabelSettingBaseInfo.setLabelSettingGuid("labelSettingGuid");
        hsaLabelSettingBaseInfo.setSex("sex");
        hsaLabelSettingBaseInfo.setSetBaseInfo(false);
        hsaLabelSettingBaseInfo.setCardMemberLevelGuid("cardMemberLevelGuid");
        hsaLabelSettingBaseInfo.setGrowValueFixedDateJson("growValueFixedDateJson");
        when(mockHsaLabelSettingBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaLabelSettingBaseInfo);

        // Configure HsaGrowthValueDetailMapper.findRelationLabelMemberGuid(...).
        final RequestLabelQuery query = new RequestLabelQuery();
        query.setSex(new String[]{"sex"});
        query.setMemberGuid(new HashSet<>(Arrays.asList("value")));
        query.setOperSubjectGuid("operSubjectGuid");
        query.setCardMemberLevelGuid(new String[]{"cardMemberLevelGuid"});
        query.setGrowValueFixedDateJson(new String[]{"growValueFixedDateJson"});
        when(mockHsaGrowthValueDetailMapper.findRelationLabelMemberGuid(query)).thenReturn(Arrays.asList("value"));

        when(mockRedissonClient.getLock("SAVE_LABEL_MEMBER")).thenReturn(null);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember("guid",
                new HashSet<>(Arrays.asList("value")))).thenReturn(Collections.emptyList());

        // Configure HsaMemberLabelRecordMapper.selectList(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> hsaMemberLabelRecords = Arrays.asList(hsaMemberLabelRecord);
        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberLabelRecords);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.relationLabel(event);

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord1 = new HsaMemberLabelRecord();
        hsaMemberLabelRecord1.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord1.setLabelName("labelName");
        hsaMemberLabelRecord1.setLabelType(0);
        hsaMemberLabelRecord1.setConnectionType(0);
        hsaMemberLabelRecord1.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setIsConnection(0);
        hsaMemberLabelRecord1.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionType(0);
        hsaMemberLabelRecord1.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord1.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord1);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
        verify(mockHsaMemberLabelMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm HsaMemberLabelMapper.batchSave(...).
        final HsaMemberLabel hsaMemberLabel1 = new HsaMemberLabel();
        hsaMemberLabel1.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel1.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels1 = Arrays.asList(hsaMemberLabel1);
        verify(mockHsaMemberLabelMapper).batchSave(hsaMemberLabels1);
    }

    @Test
    public void testRelationLabel_HsaMemberLabelRecordMapperSelectListReturnsNoItems() {
        // Setup
        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");
        final RelationLabelDTO event = new RelationLabelDTO(headerUserInfo, new HashSet<>(Arrays.asList("value")));

        // Configure HsaLabelSettingMapper.getHsaLabelSetting(...).
        final HsaLabelSetting hsaLabelSetting = new HsaLabelSetting();
        hsaLabelSetting.setGuid("guid");
        hsaLabelSetting.setOperSubjectGuid("operSubjectGuid");
        hsaLabelSetting.setLabelType(0);
        hsaLabelSetting.setLabelName("labelName");
        hsaLabelSetting.setConditionSet(0);
        final List<HsaLabelSetting> hsaLabelSettings = Arrays.asList(hsaLabelSetting);
        when(mockHsaLabelSettingMapper.getHsaLabelSetting("operSubjectGuid")).thenReturn(hsaLabelSettings);

        // Configure HsaLabelSettingBaseInfoMapper.selectOne(...).
        final HsaLabelSettingBaseInfo hsaLabelSettingBaseInfo = new HsaLabelSettingBaseInfo();
        hsaLabelSettingBaseInfo.setLabelSettingGuid("labelSettingGuid");
        hsaLabelSettingBaseInfo.setSex("sex");
        hsaLabelSettingBaseInfo.setSetBaseInfo(false);
        hsaLabelSettingBaseInfo.setCardMemberLevelGuid("cardMemberLevelGuid");
        hsaLabelSettingBaseInfo.setGrowValueFixedDateJson("growValueFixedDateJson");
        when(mockHsaLabelSettingBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaLabelSettingBaseInfo);

        // Configure HsaGrowthValueDetailMapper.findRelationLabelMemberGuid(...).
        final RequestLabelQuery query = new RequestLabelQuery();
        query.setSex(new String[]{"sex"});
        query.setMemberGuid(new HashSet<>(Arrays.asList("value")));
        query.setOperSubjectGuid("operSubjectGuid");
        query.setCardMemberLevelGuid(new String[]{"cardMemberLevelGuid"});
        query.setGrowValueFixedDateJson(new String[]{"growValueFixedDateJson"});
        when(mockHsaGrowthValueDetailMapper.findRelationLabelMemberGuid(query)).thenReturn(Arrays.asList("value"));

        when(mockRedissonClient.getLock("SAVE_LABEL_MEMBER")).thenReturn(null);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionType(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS = Arrays.asList(cancelConnectionTypeDTO);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionType("guid")).thenReturn(cancelConnectionTypeDTOS);

        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.relationLabel(event);

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
        verify(mockHsaMemberLabelMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm HsaMemberLabelMapper.batchSave(...).
        final HsaMemberLabel hsaMemberLabel1 = new HsaMemberLabel();
        hsaMemberLabel1.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel1.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels1 = Arrays.asList(hsaMemberLabel1);
        verify(mockHsaMemberLabelMapper).batchSave(hsaMemberLabels1);
    }

    @Test
    public void testBatchSaveMemberLabelRecord() {
        // Setup
        final MemberLabelRecordDTO memberLabelRecordDTO = new MemberLabelRecordDTO();
        memberLabelRecordDTO.setLabelSettingGuid("labelSettingGuid");
        memberLabelRecordDTO.setLabelName("labelName");
        memberLabelRecordDTO.setLabelType(0);
        memberLabelRecordDTO.setConnectionType(0);
        memberLabelRecordDTO.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberLabelRecordDTO.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberLabelRecordDTO.setIsConnection(0);
        memberLabelRecordDTO.setConnectionOperator("connectionOperator");
        memberLabelRecordDTO.setCancelConnectionOperator("cancelConnectionOperator");
        memberLabelRecordDTO.setCancelConnectionType(0);
        memberLabelRecordDTO.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        memberLabelRecordDTO.setOperSubjectGuid("operSubjectGuid");
        memberLabelRecordDTO.setMemberInfoGuid("memberInfoGuid");
        final List<MemberLabelRecordDTO> memberLabelRecordQOList = Arrays.asList(memberLabelRecordDTO);
        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");

        // Configure HsaMemberLabelRecordMapper.selectList(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> hsaMemberLabelRecords = Arrays.asList(hsaMemberLabelRecord);
        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberLabelRecords);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.batchSaveMemberLabelRecord(memberLabelRecordQOList, headerUserInfo);

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord1 = new HsaMemberLabelRecord();
        hsaMemberLabelRecord1.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord1.setLabelName("labelName");
        hsaMemberLabelRecord1.setLabelType(0);
        hsaMemberLabelRecord1.setConnectionType(0);
        hsaMemberLabelRecord1.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setIsConnection(0);
        hsaMemberLabelRecord1.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionType(0);
        hsaMemberLabelRecord1.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord1.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord1);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
    }

    @Test
    public void testBatchSaveMemberLabelRecord_HsaMemberLabelRecordMapperSelectListReturnsNoItems() {
        // Setup
        final MemberLabelRecordDTO memberLabelRecordDTO = new MemberLabelRecordDTO();
        memberLabelRecordDTO.setLabelSettingGuid("labelSettingGuid");
        memberLabelRecordDTO.setLabelName("labelName");
        memberLabelRecordDTO.setLabelType(0);
        memberLabelRecordDTO.setConnectionType(0);
        memberLabelRecordDTO.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberLabelRecordDTO.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberLabelRecordDTO.setIsConnection(0);
        memberLabelRecordDTO.setConnectionOperator("connectionOperator");
        memberLabelRecordDTO.setCancelConnectionOperator("cancelConnectionOperator");
        memberLabelRecordDTO.setCancelConnectionType(0);
        memberLabelRecordDTO.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        memberLabelRecordDTO.setOperSubjectGuid("operSubjectGuid");
        memberLabelRecordDTO.setMemberInfoGuid("memberInfoGuid");
        final List<MemberLabelRecordDTO> memberLabelRecordQOList = Arrays.asList(memberLabelRecordDTO);
        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");

        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.batchSaveMemberLabelRecord(memberLabelRecordQOList, headerUserInfo);

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
    }

    @Test
    public void testRefreshLabelHandle() {
        // Setup
        final HsaLabelSetting labelSetting = new HsaLabelSetting();
        labelSetting.setGuid("guid");
        labelSetting.setOperSubjectGuid("operSubjectGuid");
        labelSetting.setLabelType(0);
        labelSetting.setLabelName("labelName");
        labelSetting.setConditionSet(0);

        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");

        // Configure HsaLabelSettingBaseInfoMapper.selectOne(...).
        final HsaLabelSettingBaseInfo hsaLabelSettingBaseInfo = new HsaLabelSettingBaseInfo();
        hsaLabelSettingBaseInfo.setLabelSettingGuid("labelSettingGuid");
        hsaLabelSettingBaseInfo.setSex("sex");
        hsaLabelSettingBaseInfo.setSetBaseInfo(false);
        hsaLabelSettingBaseInfo.setCardMemberLevelGuid("cardMemberLevelGuid");
        hsaLabelSettingBaseInfo.setGrowValueFixedDateJson("growValueFixedDateJson");
        when(mockHsaLabelSettingBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaLabelSettingBaseInfo);

        // Configure HsaGrowthValueDetailMapper.findRelationLabelMemberGuid(...).
        final RequestLabelQuery query = new RequestLabelQuery();
        query.setSex(new String[]{"sex"});
        query.setMemberGuid(new HashSet<>(Arrays.asList("value")));
        query.setOperSubjectGuid("operSubjectGuid");
        query.setCardMemberLevelGuid(new String[]{"cardMemberLevelGuid"});
        query.setGrowValueFixedDateJson(new String[]{"growValueFixedDateJson"});
        when(mockHsaGrowthValueDetailMapper.findRelationLabelMemberGuid(query)).thenReturn(Arrays.asList("value"));

        when(mockRedissonClient.getLock("SAVE_LABEL_MEMBER")).thenReturn(null);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionType(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS = Arrays.asList(cancelConnectionTypeDTO);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionType("guid")).thenReturn(cancelConnectionTypeDTOS);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO1 = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO1.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO1.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS1 = Arrays.asList(cancelConnectionTypeDTO1);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember("guid",
                new HashSet<>(Arrays.asList("value")))).thenReturn(cancelConnectionTypeDTOS1);

        // Configure HsaMemberLabelRecordMapper.selectList(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> hsaMemberLabelRecords = Arrays.asList(hsaMemberLabelRecord);
        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberLabelRecords);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.refreshLabelHandle(new HashSet<>(Arrays.asList("value")), labelSetting,
                headerUserInfo);

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord1 = new HsaMemberLabelRecord();
        hsaMemberLabelRecord1.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord1.setLabelName("labelName");
        hsaMemberLabelRecord1.setLabelType(0);
        hsaMemberLabelRecord1.setConnectionType(0);
        hsaMemberLabelRecord1.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setIsConnection(0);
        hsaMemberLabelRecord1.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionType(0);
        hsaMemberLabelRecord1.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord1.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord1);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
        verify(mockHsaMemberLabelMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm HsaMemberLabelMapper.batchSave(...).
        final HsaMemberLabel hsaMemberLabel1 = new HsaMemberLabel();
        hsaMemberLabel1.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel1.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels1 = Arrays.asList(hsaMemberLabel1);
        verify(mockHsaMemberLabelMapper).batchSave(hsaMemberLabels1);
    }

    @Test
    public void testRefreshLabelHandle_HsaGrowthValueDetailMapperReturnsNoItems() {
        // Setup
        final HsaLabelSetting labelSetting = new HsaLabelSetting();
        labelSetting.setGuid("guid");
        labelSetting.setOperSubjectGuid("operSubjectGuid");
        labelSetting.setLabelType(0);
        labelSetting.setLabelName("labelName");
        labelSetting.setConditionSet(0);

        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");

        // Configure HsaLabelSettingBaseInfoMapper.selectOne(...).
        final HsaLabelSettingBaseInfo hsaLabelSettingBaseInfo = new HsaLabelSettingBaseInfo();
        hsaLabelSettingBaseInfo.setLabelSettingGuid("labelSettingGuid");
        hsaLabelSettingBaseInfo.setSex("sex");
        hsaLabelSettingBaseInfo.setSetBaseInfo(false);
        hsaLabelSettingBaseInfo.setCardMemberLevelGuid("cardMemberLevelGuid");
        hsaLabelSettingBaseInfo.setGrowValueFixedDateJson("growValueFixedDateJson");
        when(mockHsaLabelSettingBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaLabelSettingBaseInfo);

        // Configure HsaGrowthValueDetailMapper.findRelationLabelMemberGuid(...).
        final RequestLabelQuery query = new RequestLabelQuery();
        query.setSex(new String[]{"sex"});
        query.setMemberGuid(new HashSet<>(Arrays.asList("value")));
        query.setOperSubjectGuid("operSubjectGuid");
        query.setCardMemberLevelGuid(new String[]{"cardMemberLevelGuid"});
        query.setGrowValueFixedDateJson(new String[]{"growValueFixedDateJson"});
        when(mockHsaGrowthValueDetailMapper.findRelationLabelMemberGuid(query)).thenReturn(Collections.emptyList());

        when(mockRedissonClient.getLock("SAVE_LABEL_MEMBER")).thenReturn(null);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionType(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS = Arrays.asList(cancelConnectionTypeDTO);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionType("guid")).thenReturn(cancelConnectionTypeDTOS);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO1 = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO1.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO1.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS1 = Arrays.asList(cancelConnectionTypeDTO1);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember("guid",
                new HashSet<>(Arrays.asList("value")))).thenReturn(cancelConnectionTypeDTOS1);

        // Configure HsaMemberLabelRecordMapper.selectList(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> hsaMemberLabelRecords = Arrays.asList(hsaMemberLabelRecord);
        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberLabelRecords);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.refreshLabelHandle(new HashSet<>(Arrays.asList("value")), labelSetting,
                headerUserInfo);

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord1 = new HsaMemberLabelRecord();
        hsaMemberLabelRecord1.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord1.setLabelName("labelName");
        hsaMemberLabelRecord1.setLabelType(0);
        hsaMemberLabelRecord1.setConnectionType(0);
        hsaMemberLabelRecord1.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setIsConnection(0);
        hsaMemberLabelRecord1.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionType(0);
        hsaMemberLabelRecord1.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord1.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord1);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
        verify(mockHsaMemberLabelMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm HsaMemberLabelMapper.batchSave(...).
        final HsaMemberLabel hsaMemberLabel1 = new HsaMemberLabel();
        hsaMemberLabel1.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel1.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels1 = Arrays.asList(hsaMemberLabel1);
        verify(mockHsaMemberLabelMapper).batchSave(hsaMemberLabels1);
    }

    @Test
    public void testRefreshLabelHandle_RedissonClientReturnsNull() {
        // Setup
        final HsaLabelSetting labelSetting = new HsaLabelSetting();
        labelSetting.setGuid("guid");
        labelSetting.setOperSubjectGuid("operSubjectGuid");
        labelSetting.setLabelType(0);
        labelSetting.setLabelName("labelName");
        labelSetting.setConditionSet(0);

        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");

        // Configure HsaLabelSettingBaseInfoMapper.selectOne(...).
        final HsaLabelSettingBaseInfo hsaLabelSettingBaseInfo = new HsaLabelSettingBaseInfo();
        hsaLabelSettingBaseInfo.setLabelSettingGuid("labelSettingGuid");
        hsaLabelSettingBaseInfo.setSex("sex");
        hsaLabelSettingBaseInfo.setSetBaseInfo(false);
        hsaLabelSettingBaseInfo.setCardMemberLevelGuid("cardMemberLevelGuid");
        hsaLabelSettingBaseInfo.setGrowValueFixedDateJson("growValueFixedDateJson");
        when(mockHsaLabelSettingBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaLabelSettingBaseInfo);

        // Configure HsaGrowthValueDetailMapper.findRelationLabelMemberGuid(...).
        final RequestLabelQuery query = new RequestLabelQuery();
        query.setSex(new String[]{"sex"});
        query.setMemberGuid(new HashSet<>(Arrays.asList("value")));
        query.setOperSubjectGuid("operSubjectGuid");
        query.setCardMemberLevelGuid(new String[]{"cardMemberLevelGuid"});
        query.setGrowValueFixedDateJson(new String[]{"growValueFixedDateJson"});
        when(mockHsaGrowthValueDetailMapper.findRelationLabelMemberGuid(query)).thenReturn(Arrays.asList("value"));

        when(mockRedissonClient.getLock("SAVE_LABEL_MEMBER")).thenReturn(null);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionType(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS = Arrays.asList(cancelConnectionTypeDTO);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionType("guid")).thenReturn(cancelConnectionTypeDTOS);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO1 = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO1.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO1.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS1 = Arrays.asList(cancelConnectionTypeDTO1);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember("guid",
                new HashSet<>(Arrays.asList("value")))).thenReturn(cancelConnectionTypeDTOS1);

        // Configure HsaMemberLabelRecordMapper.selectList(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> hsaMemberLabelRecords = Arrays.asList(hsaMemberLabelRecord);
        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberLabelRecords);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.refreshLabelHandle(new HashSet<>(Arrays.asList("value")), labelSetting,
                headerUserInfo);

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord1 = new HsaMemberLabelRecord();
        hsaMemberLabelRecord1.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord1.setLabelName("labelName");
        hsaMemberLabelRecord1.setLabelType(0);
        hsaMemberLabelRecord1.setConnectionType(0);
        hsaMemberLabelRecord1.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setIsConnection(0);
        hsaMemberLabelRecord1.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionType(0);
        hsaMemberLabelRecord1.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord1.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord1);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
        verify(mockHsaMemberLabelMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm HsaMemberLabelMapper.batchSave(...).
        final HsaMemberLabel hsaMemberLabel1 = new HsaMemberLabel();
        hsaMemberLabel1.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel1.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels1 = Arrays.asList(hsaMemberLabel1);
        verify(mockHsaMemberLabelMapper).batchSave(hsaMemberLabels1);
    }

    @Test
    public void testRefreshLabelHandle_HsaMemberLabelMapperSelectListReturnsNoItems() {
        // Setup
        final HsaLabelSetting labelSetting = new HsaLabelSetting();
        labelSetting.setGuid("guid");
        labelSetting.setOperSubjectGuid("operSubjectGuid");
        labelSetting.setLabelType(0);
        labelSetting.setLabelName("labelName");
        labelSetting.setConditionSet(0);

        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");

        // Configure HsaLabelSettingBaseInfoMapper.selectOne(...).
        final HsaLabelSettingBaseInfo hsaLabelSettingBaseInfo = new HsaLabelSettingBaseInfo();
        hsaLabelSettingBaseInfo.setLabelSettingGuid("labelSettingGuid");
        hsaLabelSettingBaseInfo.setSex("sex");
        hsaLabelSettingBaseInfo.setSetBaseInfo(false);
        hsaLabelSettingBaseInfo.setCardMemberLevelGuid("cardMemberLevelGuid");
        hsaLabelSettingBaseInfo.setGrowValueFixedDateJson("growValueFixedDateJson");
        when(mockHsaLabelSettingBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaLabelSettingBaseInfo);

        // Configure HsaGrowthValueDetailMapper.findRelationLabelMemberGuid(...).
        final RequestLabelQuery query = new RequestLabelQuery();
        query.setSex(new String[]{"sex"});
        query.setMemberGuid(new HashSet<>(Arrays.asList("value")));
        query.setOperSubjectGuid("operSubjectGuid");
        query.setCardMemberLevelGuid(new String[]{"cardMemberLevelGuid"});
        query.setGrowValueFixedDateJson(new String[]{"growValueFixedDateJson"});
        when(mockHsaGrowthValueDetailMapper.findRelationLabelMemberGuid(query)).thenReturn(Arrays.asList("value"));

        when(mockRedissonClient.getLock("SAVE_LABEL_MEMBER")).thenReturn(null);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionType(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS = Arrays.asList(cancelConnectionTypeDTO);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionType("guid")).thenReturn(cancelConnectionTypeDTOS);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO1 = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO1.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO1.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS1 = Arrays.asList(cancelConnectionTypeDTO1);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember("guid",
                new HashSet<>(Arrays.asList("value")))).thenReturn(cancelConnectionTypeDTOS1);

        // Configure HsaMemberLabelRecordMapper.selectList(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> hsaMemberLabelRecords = Arrays.asList(hsaMemberLabelRecord);
        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberLabelRecords);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.refreshLabelHandle(new HashSet<>(Arrays.asList("value")), labelSetting,
                headerUserInfo);

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord1 = new HsaMemberLabelRecord();
        hsaMemberLabelRecord1.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord1.setLabelName("labelName");
        hsaMemberLabelRecord1.setLabelType(0);
        hsaMemberLabelRecord1.setConnectionType(0);
        hsaMemberLabelRecord1.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setIsConnection(0);
        hsaMemberLabelRecord1.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionType(0);
        hsaMemberLabelRecord1.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord1.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord1);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
        verify(mockHsaMemberLabelMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm HsaMemberLabelMapper.batchSave(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        verify(mockHsaMemberLabelMapper).batchSave(hsaMemberLabels);
    }

    @Test
    public void testRefreshLabelHandle_HsaMemberLabelRecordMapperFindLastCancelConnectionTypeReturnsNoItems() {
        // Setup
        final HsaLabelSetting labelSetting = new HsaLabelSetting();
        labelSetting.setGuid("guid");
        labelSetting.setOperSubjectGuid("operSubjectGuid");
        labelSetting.setLabelType(0);
        labelSetting.setLabelName("labelName");
        labelSetting.setConditionSet(0);

        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");

        // Configure HsaLabelSettingBaseInfoMapper.selectOne(...).
        final HsaLabelSettingBaseInfo hsaLabelSettingBaseInfo = new HsaLabelSettingBaseInfo();
        hsaLabelSettingBaseInfo.setLabelSettingGuid("labelSettingGuid");
        hsaLabelSettingBaseInfo.setSex("sex");
        hsaLabelSettingBaseInfo.setSetBaseInfo(false);
        hsaLabelSettingBaseInfo.setCardMemberLevelGuid("cardMemberLevelGuid");
        hsaLabelSettingBaseInfo.setGrowValueFixedDateJson("growValueFixedDateJson");
        when(mockHsaLabelSettingBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaLabelSettingBaseInfo);

        // Configure HsaGrowthValueDetailMapper.findRelationLabelMemberGuid(...).
        final RequestLabelQuery query = new RequestLabelQuery();
        query.setSex(new String[]{"sex"});
        query.setMemberGuid(new HashSet<>(Arrays.asList("value")));
        query.setOperSubjectGuid("operSubjectGuid");
        query.setCardMemberLevelGuid(new String[]{"cardMemberLevelGuid"});
        query.setGrowValueFixedDateJson(new String[]{"growValueFixedDateJson"});
        when(mockHsaGrowthValueDetailMapper.findRelationLabelMemberGuid(query)).thenReturn(Arrays.asList("value"));

        when(mockRedissonClient.getLock("SAVE_LABEL_MEMBER")).thenReturn(null);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionType("guid")).thenReturn(Collections.emptyList());

        // Configure HsaMemberLabelRecordMapper.selectList(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> hsaMemberLabelRecords = Arrays.asList(hsaMemberLabelRecord);
        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberLabelRecords);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.refreshLabelHandle(new HashSet<>(Arrays.asList("value")), labelSetting,
                headerUserInfo);

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord1 = new HsaMemberLabelRecord();
        hsaMemberLabelRecord1.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord1.setLabelName("labelName");
        hsaMemberLabelRecord1.setLabelType(0);
        hsaMemberLabelRecord1.setConnectionType(0);
        hsaMemberLabelRecord1.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setIsConnection(0);
        hsaMemberLabelRecord1.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionType(0);
        hsaMemberLabelRecord1.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord1.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord1);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
        verify(mockHsaMemberLabelMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm HsaMemberLabelMapper.batchSave(...).
        final HsaMemberLabel hsaMemberLabel1 = new HsaMemberLabel();
        hsaMemberLabel1.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel1.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels1 = Arrays.asList(hsaMemberLabel1);
        verify(mockHsaMemberLabelMapper).batchSave(hsaMemberLabels1);
    }

    @Test
    public void testRefreshLabelHandle_HsaMemberLabelRecordMapperFindLastCancelConnectionTypeInMemberReturnsNoItems() {
        // Setup
        final HsaLabelSetting labelSetting = new HsaLabelSetting();
        labelSetting.setGuid("guid");
        labelSetting.setOperSubjectGuid("operSubjectGuid");
        labelSetting.setLabelType(0);
        labelSetting.setLabelName("labelName");
        labelSetting.setConditionSet(0);

        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");

        // Configure HsaLabelSettingBaseInfoMapper.selectOne(...).
        final HsaLabelSettingBaseInfo hsaLabelSettingBaseInfo = new HsaLabelSettingBaseInfo();
        hsaLabelSettingBaseInfo.setLabelSettingGuid("labelSettingGuid");
        hsaLabelSettingBaseInfo.setSex("sex");
        hsaLabelSettingBaseInfo.setSetBaseInfo(false);
        hsaLabelSettingBaseInfo.setCardMemberLevelGuid("cardMemberLevelGuid");
        hsaLabelSettingBaseInfo.setGrowValueFixedDateJson("growValueFixedDateJson");
        when(mockHsaLabelSettingBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaLabelSettingBaseInfo);

        // Configure HsaGrowthValueDetailMapper.findRelationLabelMemberGuid(...).
        final RequestLabelQuery query = new RequestLabelQuery();
        query.setSex(new String[]{"sex"});
        query.setMemberGuid(new HashSet<>(Arrays.asList("value")));
        query.setOperSubjectGuid("operSubjectGuid");
        query.setCardMemberLevelGuid(new String[]{"cardMemberLevelGuid"});
        query.setGrowValueFixedDateJson(new String[]{"growValueFixedDateJson"});
        when(mockHsaGrowthValueDetailMapper.findRelationLabelMemberGuid(query)).thenReturn(Arrays.asList("value"));

        when(mockRedissonClient.getLock("SAVE_LABEL_MEMBER")).thenReturn(null);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember("guid",
                new HashSet<>(Arrays.asList("value")))).thenReturn(Collections.emptyList());

        // Configure HsaMemberLabelRecordMapper.selectList(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> hsaMemberLabelRecords = Arrays.asList(hsaMemberLabelRecord);
        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberLabelRecords);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.refreshLabelHandle(new HashSet<>(Arrays.asList("value")), labelSetting,
                headerUserInfo);

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord1 = new HsaMemberLabelRecord();
        hsaMemberLabelRecord1.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord1.setLabelName("labelName");
        hsaMemberLabelRecord1.setLabelType(0);
        hsaMemberLabelRecord1.setConnectionType(0);
        hsaMemberLabelRecord1.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setIsConnection(0);
        hsaMemberLabelRecord1.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionType(0);
        hsaMemberLabelRecord1.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord1.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord1);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
        verify(mockHsaMemberLabelMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm HsaMemberLabelMapper.batchSave(...).
        final HsaMemberLabel hsaMemberLabel1 = new HsaMemberLabel();
        hsaMemberLabel1.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel1.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels1 = Arrays.asList(hsaMemberLabel1);
        verify(mockHsaMemberLabelMapper).batchSave(hsaMemberLabels1);
    }

    @Test
    public void testRefreshLabelHandle_HsaMemberLabelRecordMapperSelectListReturnsNoItems() {
        // Setup
        final HsaLabelSetting labelSetting = new HsaLabelSetting();
        labelSetting.setGuid("guid");
        labelSetting.setOperSubjectGuid("operSubjectGuid");
        labelSetting.setLabelType(0);
        labelSetting.setLabelName("labelName");
        labelSetting.setConditionSet(0);

        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");

        // Configure HsaLabelSettingBaseInfoMapper.selectOne(...).
        final HsaLabelSettingBaseInfo hsaLabelSettingBaseInfo = new HsaLabelSettingBaseInfo();
        hsaLabelSettingBaseInfo.setLabelSettingGuid("labelSettingGuid");
        hsaLabelSettingBaseInfo.setSex("sex");
        hsaLabelSettingBaseInfo.setSetBaseInfo(false);
        hsaLabelSettingBaseInfo.setCardMemberLevelGuid("cardMemberLevelGuid");
        hsaLabelSettingBaseInfo.setGrowValueFixedDateJson("growValueFixedDateJson");
        when(mockHsaLabelSettingBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaLabelSettingBaseInfo);

        // Configure HsaGrowthValueDetailMapper.findRelationLabelMemberGuid(...).
        final RequestLabelQuery query = new RequestLabelQuery();
        query.setSex(new String[]{"sex"});
        query.setMemberGuid(new HashSet<>(Arrays.asList("value")));
        query.setOperSubjectGuid("operSubjectGuid");
        query.setCardMemberLevelGuid(new String[]{"cardMemberLevelGuid"});
        query.setGrowValueFixedDateJson(new String[]{"growValueFixedDateJson"});
        when(mockHsaGrowthValueDetailMapper.findRelationLabelMemberGuid(query)).thenReturn(Arrays.asList("value"));

        when(mockRedissonClient.getLock("SAVE_LABEL_MEMBER")).thenReturn(null);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionType(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS = Arrays.asList(cancelConnectionTypeDTO);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionType("guid")).thenReturn(cancelConnectionTypeDTOS);

        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.refreshLabelHandle(new HashSet<>(Arrays.asList("value")), labelSetting,
                headerUserInfo);

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
        verify(mockHsaMemberLabelMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm HsaMemberLabelMapper.batchSave(...).
        final HsaMemberLabel hsaMemberLabel1 = new HsaMemberLabel();
        hsaMemberLabel1.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel1.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels1 = Arrays.asList(hsaMemberLabel1);
        verify(mockHsaMemberLabelMapper).batchSave(hsaMemberLabels1);
    }

    @Test
    public void testSaveLabelMember() {
        // Setup
        final HsaLabelSetting label = new HsaLabelSetting();
        label.setGuid("guid");
        label.setOperSubjectGuid("operSubjectGuid");
        label.setLabelType(0);
        label.setLabelName("labelName");
        label.setConditionSet(0);

        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");

        when(mockRedissonClient.getLock("SAVE_LABEL_MEMBER")).thenReturn(null);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionType(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS = Arrays.asList(cancelConnectionTypeDTO);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionType("guid")).thenReturn(cancelConnectionTypeDTOS);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO1 = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO1.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO1.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS1 = Arrays.asList(cancelConnectionTypeDTO1);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember("guid",
                new HashSet<>(Arrays.asList("value")))).thenReturn(cancelConnectionTypeDTOS1);

        // Configure HsaMemberLabelRecordMapper.selectList(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> hsaMemberLabelRecords = Arrays.asList(hsaMemberLabelRecord);
        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberLabelRecords);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.saveLabelMember(label, new HashSet<>(Arrays.asList("value")),
                headerUserInfo, new HashSet<>(Arrays.asList("value")));

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord1 = new HsaMemberLabelRecord();
        hsaMemberLabelRecord1.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord1.setLabelName("labelName");
        hsaMemberLabelRecord1.setLabelType(0);
        hsaMemberLabelRecord1.setConnectionType(0);
        hsaMemberLabelRecord1.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setIsConnection(0);
        hsaMemberLabelRecord1.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionType(0);
        hsaMemberLabelRecord1.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord1.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord1);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
        verify(mockHsaMemberLabelMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm HsaMemberLabelMapper.batchSave(...).
        final HsaMemberLabel hsaMemberLabel1 = new HsaMemberLabel();
        hsaMemberLabel1.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel1.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels1 = Arrays.asList(hsaMemberLabel1);
        verify(mockHsaMemberLabelMapper).batchSave(hsaMemberLabels1);
    }

    @Test
    public void testSaveLabelMember_RedissonClientReturnsNull() {
        // Setup
        final HsaLabelSetting label = new HsaLabelSetting();
        label.setGuid("guid");
        label.setOperSubjectGuid("operSubjectGuid");
        label.setLabelType(0);
        label.setLabelName("labelName");
        label.setConditionSet(0);

        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");

        when(mockRedissonClient.getLock("SAVE_LABEL_MEMBER")).thenReturn(null);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionType(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS = Arrays.asList(cancelConnectionTypeDTO);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionType("guid")).thenReturn(cancelConnectionTypeDTOS);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO1 = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO1.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO1.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS1 = Arrays.asList(cancelConnectionTypeDTO1);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember("guid",
                new HashSet<>(Arrays.asList("value")))).thenReturn(cancelConnectionTypeDTOS1);

        // Configure HsaMemberLabelRecordMapper.selectList(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> hsaMemberLabelRecords = Arrays.asList(hsaMemberLabelRecord);
        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberLabelRecords);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.saveLabelMember(label, new HashSet<>(Arrays.asList("value")),
                headerUserInfo, new HashSet<>(Arrays.asList("value")));

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord1 = new HsaMemberLabelRecord();
        hsaMemberLabelRecord1.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord1.setLabelName("labelName");
        hsaMemberLabelRecord1.setLabelType(0);
        hsaMemberLabelRecord1.setConnectionType(0);
        hsaMemberLabelRecord1.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setIsConnection(0);
        hsaMemberLabelRecord1.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionType(0);
        hsaMemberLabelRecord1.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord1.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord1);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
        verify(mockHsaMemberLabelMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm HsaMemberLabelMapper.batchSave(...).
        final HsaMemberLabel hsaMemberLabel1 = new HsaMemberLabel();
        hsaMemberLabel1.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel1.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels1 = Arrays.asList(hsaMemberLabel1);
        verify(mockHsaMemberLabelMapper).batchSave(hsaMemberLabels1);
    }

    @Test
    public void testSaveLabelMember_HsaMemberLabelMapperSelectListReturnsNoItems() {
        // Setup
        final HsaLabelSetting label = new HsaLabelSetting();
        label.setGuid("guid");
        label.setOperSubjectGuid("operSubjectGuid");
        label.setLabelType(0);
        label.setLabelName("labelName");
        label.setConditionSet(0);

        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");

        when(mockRedissonClient.getLock("SAVE_LABEL_MEMBER")).thenReturn(null);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionType(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS = Arrays.asList(cancelConnectionTypeDTO);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionType("guid")).thenReturn(cancelConnectionTypeDTOS);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO1 = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO1.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO1.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS1 = Arrays.asList(cancelConnectionTypeDTO1);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember("guid",
                new HashSet<>(Arrays.asList("value")))).thenReturn(cancelConnectionTypeDTOS1);

        // Configure HsaMemberLabelRecordMapper.selectList(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> hsaMemberLabelRecords = Arrays.asList(hsaMemberLabelRecord);
        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberLabelRecords);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.saveLabelMember(label, new HashSet<>(Arrays.asList("value")),
                headerUserInfo, new HashSet<>(Arrays.asList("value")));

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord1 = new HsaMemberLabelRecord();
        hsaMemberLabelRecord1.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord1.setLabelName("labelName");
        hsaMemberLabelRecord1.setLabelType(0);
        hsaMemberLabelRecord1.setConnectionType(0);
        hsaMemberLabelRecord1.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setIsConnection(0);
        hsaMemberLabelRecord1.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionType(0);
        hsaMemberLabelRecord1.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord1.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord1);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
        verify(mockHsaMemberLabelMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm HsaMemberLabelMapper.batchSave(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        verify(mockHsaMemberLabelMapper).batchSave(hsaMemberLabels);
    }

    @Test
    public void testSaveLabelMember_HsaMemberLabelRecordMapperFindLastCancelConnectionTypeReturnsNoItems() {
        // Setup
        final HsaLabelSetting label = new HsaLabelSetting();
        label.setGuid("guid");
        label.setOperSubjectGuid("operSubjectGuid");
        label.setLabelType(0);
        label.setLabelName("labelName");
        label.setConditionSet(0);

        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");

        when(mockRedissonClient.getLock("SAVE_LABEL_MEMBER")).thenReturn(null);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionType("guid")).thenReturn(Collections.emptyList());

        // Configure HsaMemberLabelRecordMapper.selectList(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> hsaMemberLabelRecords = Arrays.asList(hsaMemberLabelRecord);
        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberLabelRecords);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.saveLabelMember(label, new HashSet<>(Arrays.asList("value")),
                headerUserInfo, new HashSet<>(Arrays.asList("value")));

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord1 = new HsaMemberLabelRecord();
        hsaMemberLabelRecord1.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord1.setLabelName("labelName");
        hsaMemberLabelRecord1.setLabelType(0);
        hsaMemberLabelRecord1.setConnectionType(0);
        hsaMemberLabelRecord1.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setIsConnection(0);
        hsaMemberLabelRecord1.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionType(0);
        hsaMemberLabelRecord1.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord1.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord1);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
        verify(mockHsaMemberLabelMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm HsaMemberLabelMapper.batchSave(...).
        final HsaMemberLabel hsaMemberLabel1 = new HsaMemberLabel();
        hsaMemberLabel1.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel1.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels1 = Arrays.asList(hsaMemberLabel1);
        verify(mockHsaMemberLabelMapper).batchSave(hsaMemberLabels1);
    }

    @Test
    public void testSaveLabelMember_HsaMemberLabelRecordMapperFindLastCancelConnectionTypeInMemberReturnsNoItems() {
        // Setup
        final HsaLabelSetting label = new HsaLabelSetting();
        label.setGuid("guid");
        label.setOperSubjectGuid("operSubjectGuid");
        label.setLabelType(0);
        label.setLabelName("labelName");
        label.setConditionSet(0);

        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");

        when(mockRedissonClient.getLock("SAVE_LABEL_MEMBER")).thenReturn(null);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionTypeInMember("guid",
                new HashSet<>(Arrays.asList("value")))).thenReturn(Collections.emptyList());

        // Configure HsaMemberLabelRecordMapper.selectList(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> hsaMemberLabelRecords = Arrays.asList(hsaMemberLabelRecord);
        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberLabelRecords);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.saveLabelMember(label, new HashSet<>(Arrays.asList("value")),
                headerUserInfo, new HashSet<>(Arrays.asList("value")));

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord1 = new HsaMemberLabelRecord();
        hsaMemberLabelRecord1.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord1.setLabelName("labelName");
        hsaMemberLabelRecord1.setLabelType(0);
        hsaMemberLabelRecord1.setConnectionType(0);
        hsaMemberLabelRecord1.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord1.setIsConnection(0);
        hsaMemberLabelRecord1.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord1.setCancelConnectionType(0);
        hsaMemberLabelRecord1.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord1.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord1);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
        verify(mockHsaMemberLabelMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm HsaMemberLabelMapper.batchSave(...).
        final HsaMemberLabel hsaMemberLabel1 = new HsaMemberLabel();
        hsaMemberLabel1.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel1.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels1 = Arrays.asList(hsaMemberLabel1);
        verify(mockHsaMemberLabelMapper).batchSave(hsaMemberLabels1);
    }

    @Test
    public void testSaveLabelMember_HsaMemberLabelRecordMapperSelectListReturnsNoItems() {
        // Setup
        final HsaLabelSetting label = new HsaLabelSetting();
        label.setGuid("guid");
        label.setOperSubjectGuid("operSubjectGuid");
        label.setLabelType(0);
        label.setLabelName("labelName");
        label.setConditionSet(0);

        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");

        when(mockRedissonClient.getLock("SAVE_LABEL_MEMBER")).thenReturn(null);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        // Configure HsaMemberLabelRecordMapper.findLastCancelConnectionType(...).
        final CancelConnectionTypeDTO cancelConnectionTypeDTO = new CancelConnectionTypeDTO();
        cancelConnectionTypeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        cancelConnectionTypeDTO.setMemberInfoGuid("memberInfoGuid");
        cancelConnectionTypeDTO.setCancelConnectionType(0);
        final List<CancelConnectionTypeDTO> cancelConnectionTypeDTOS = Arrays.asList(cancelConnectionTypeDTO);
        when(mockHsaMemberLabelRecordMapper.findLastCancelConnectionType("guid")).thenReturn(cancelConnectionTypeDTOS);

        when(mockHsaMemberLabelRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("368cd1cd-c752-433c-af5b-c9fd924f0c16");

        // Run the test
        hsaLabelSettingServiceImplUnderTest.saveLabelMember(label, new HashSet<>(Arrays.asList("value")),
                headerUserInfo, new HashSet<>(Arrays.asList("value")));

        // Verify the results
        // Confirm HsaMemberLabelRecordMapper.updateIsConnectionByGuid(...).
        final HsaMemberLabelRecord hsaMemberLabelRecord = new HsaMemberLabelRecord();
        hsaMemberLabelRecord.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        hsaMemberLabelRecord.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabelRecord.setLabelName("labelName");
        hsaMemberLabelRecord.setLabelType(0);
        hsaMemberLabelRecord.setConnectionType(0);
        hsaMemberLabelRecord.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabelRecord.setIsConnection(0);
        hsaMemberLabelRecord.setConnectionOperator("connectionOperator");
        hsaMemberLabelRecord.setCancelConnectionOperator("cancelConnectionOperator");
        hsaMemberLabelRecord.setCancelConnectionType(0);
        hsaMemberLabelRecord.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabelRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabelRecord.setMemberInfoGuid("memberInfoGuid");
        final List<HsaMemberLabelRecord> updateMemberLabelRecordList = Arrays.asList(hsaMemberLabelRecord);
        verify(mockHsaMemberLabelRecordMapper).updateIsConnectionByGuid(updateMemberLabelRecordList);

        // Confirm HsaMemberLabelRecordMapper.insert(...).
        final HsaMemberLabelRecord t = new HsaMemberLabelRecord();
        t.setGuid("368cd1cd-c752-433c-af5b-c9fd924f0c16");
        t.setLabelSettingGuid("labelSettingGuid");
        t.setLabelName("labelName");
        t.setLabelType(0);
        t.setConnectionType(0);
        t.setConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setCancelConnectionTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setIsConnection(0);
        t.setConnectionOperator("connectionOperator");
        t.setCancelConnectionOperator("cancelConnectionOperator");
        t.setCancelConnectionType(0);
        t.setMemberLabelGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        verify(mockHsaMemberLabelRecordMapper).insert(t);
        verify(mockHsaMemberLabelMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm HsaMemberLabelMapper.batchSave(...).
        final HsaMemberLabel hsaMemberLabel1 = new HsaMemberLabel();
        hsaMemberLabel1.setGuid("f2214c51-15e6-43f7-ac78-fa87dd51b762");
        hsaMemberLabel1.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel1.setOperationMemberInfoGuid("memberInfoGuid");
        hsaMemberLabel1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberLabel1.setConnectionType(0);
        final List<HsaMemberLabel> hsaMemberLabels1 = Arrays.asList(hsaMemberLabel1);
        verify(mockHsaMemberLabelMapper).batchSave(hsaMemberLabels1);
    }
}
