package com.holderzone.member.queue.service.grade.impl;

import com.holderzone.member.queue.dto.grade.DoubleValueRequest;
import com.holderzone.member.queue.entity.HsaBusinessEquities;
import com.holderzone.member.queue.entity.HsaMemberEquitiesReceiveRecord;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;

public class CheckSingleLimitFilterTest {

    private CheckSingleLimitFilter checkSingleLimitFilterUnderTest;

    @Before
    public void setUp() {
        checkSingleLimitFilterUnderTest = new CheckSingleLimitFilter();
    }

    @Test
    public void testDoFilter() {
        // Setup
        final DoubleValueRequest request = DoubleValueRequest.builder()
                .growthValue(0)
                .memberInfoGuid("memberInfoGuid")
                .doubleValue(0L)
                .remainingValue(0)
                .build();
        final HsaBusinessEquities hsaBusinessEquities = HsaBusinessEquities.builder()
                .gradeValueDoubleNumber(new BigDecimal("0.00"))
                .singleDoubleLimited(0)
                .singleDoubleUpperLimit(0)
                .build();
        final HsaMemberEquitiesReceiveRecord equitiesReceiveRecord = new HsaMemberEquitiesReceiveRecord();
        equitiesReceiveRecord.setGuid("4dca5563-b93b-46d9-b6cf-4c4036167eba");
        equitiesReceiveRecord.setOperSubjectGuid("operSubjectGuid");
        equitiesReceiveRecord.setMemberGradeGuid("memberGradeGuid");
        equitiesReceiveRecord.setMemberInfoGuid("memberInfoGuid");
        equitiesReceiveRecord.setGradeEquitiesGuid("gradeEquitiesGuid");

        // Run the test
        final boolean result = checkSingleLimitFilterUnderTest.doFilter(request, hsaBusinessEquities,
                equitiesReceiveRecord);

        // Verify the results
        assertThat(result).isFalse();
    }
}
