package com.holderzone.member.queue.service.grade.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.queue.dto.grade.DoubleValueRequest;
import com.holderzone.member.queue.entity.HsaBusinessEquities;
import com.holderzone.member.queue.entity.HsaMemberEquitiesReceiveRecord;
import com.holderzone.member.queue.mapper.HsaGradeEquitiesMapper;
import com.holderzone.member.queue.mapper.HsaMemberEquitiesReceiveRecordMapper;
import com.holderzone.member.queue.service.grade.AbstractHandler;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaGradeEquitiesServiceImplTest {

    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private HsaGradeEquitiesMapper mockHsaGradeEquitiesMapper;
    @Mock
    private HsaMemberEquitiesReceiveRecordMapper mockHsaMemberEquitiesReceiveRecordMapper;

    @InjectMocks
    private HsaGradeEquitiesServiceImpl hsaGradeEquitiesServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(hsaGradeEquitiesServiceImplUnderTest, "abstractHandleList", Arrays.asList());
    }

    @Test
    public void testInitializeChainFilter() {
        // Setup
        // Run the test
        hsaGradeEquitiesServiceImplUnderTest.initializeChainFilter();

        // Verify the results
    }

    @Test
    public void testIsDoubleGrowthValue() {
        // Setup
        final DoubleValueRequest request = DoubleValueRequest.builder()
                .gradeEquitiesGuid("gradeEquitiesGuid")
                .memberInfoGuid("memberInfoGuid")
                .operSubjectGuid("operSubjectGuid")
                .businessType(0)
                .build();

        // Configure HsaGradeEquitiesMapper.selectOne(...).
        final HsaBusinessEquities hsaBusinessEquities = HsaBusinessEquities.builder()
                .guid("guid")
                .memberGradeInfoGuid("memberGradeInfoGuid")
                .equitiesRuleType(0)
                .isDelete(0)
                .effective(0)
                .businessType(0)
                .build();
        when(mockHsaGradeEquitiesMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaBusinessEquities);

        // Configure HsaMemberEquitiesReceiveRecordMapper.selectOne(...).
        final HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord = new HsaMemberEquitiesReceiveRecord();
        hsaMemberEquitiesReceiveRecord.setGuid("18ada544-1bc2-46f3-84cc-13b9cdba4f07");
        hsaMemberEquitiesReceiveRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberEquitiesReceiveRecord.setMemberGradeGuid("memberGradeInfoGuid");
        hsaMemberEquitiesReceiveRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberEquitiesReceiveRecord.setGradeEquitiesGuid("guid");
        hsaMemberEquitiesReceiveRecord.setDoubleValueCount(0);
        hsaMemberEquitiesReceiveRecord.setTotalDoubleUpperValue(0);
        hsaMemberEquitiesReceiveRecord.setGiveGrowthValueNumber(0);
        hsaMemberEquitiesReceiveRecord.setType(0);
        when(mockHsaMemberEquitiesReceiveRecordMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberEquitiesReceiveRecord);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("18ada544-1bc2-46f3-84cc-13b9cdba4f07");

        // Run the test
        final Long result = hsaGradeEquitiesServiceImplUnderTest.isDoubleGrowthValue(request);

        // Verify the results
        assertThat(result).isEqualTo(0L);

        // Confirm HsaMemberEquitiesReceiveRecordMapper.updateByGuid(...).
        final HsaMemberEquitiesReceiveRecord t = new HsaMemberEquitiesReceiveRecord();
        t.setGuid("18ada544-1bc2-46f3-84cc-13b9cdba4f07");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberGradeGuid("memberGradeInfoGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setGradeEquitiesGuid("guid");
        t.setDoubleValueCount(0);
        t.setTotalDoubleUpperValue(0);
        t.setGiveGrowthValueNumber(0);
        t.setType(0);
        verify(mockHsaMemberEquitiesReceiveRecordMapper).updateByGuid(t);

        // Confirm HsaMemberEquitiesReceiveRecordMapper.insert(...).
        final HsaMemberEquitiesReceiveRecord t1 = new HsaMemberEquitiesReceiveRecord();
        t1.setGuid("18ada544-1bc2-46f3-84cc-13b9cdba4f07");
        t1.setOperSubjectGuid("operSubjectGuid");
        t1.setMemberGradeGuid("memberGradeInfoGuid");
        t1.setMemberInfoGuid("memberInfoGuid");
        t1.setGradeEquitiesGuid("guid");
        t1.setDoubleValueCount(0);
        t1.setTotalDoubleUpperValue(0);
        t1.setGiveGrowthValueNumber(0);
        t1.setType(0);
        verify(mockHsaMemberEquitiesReceiveRecordMapper).insert(t1);
    }

    @Test
    public void testExec() {
        // Setup
        final DoubleValueRequest request = DoubleValueRequest.builder()
                .gradeEquitiesGuid("gradeEquitiesGuid")
                .memberInfoGuid("memberInfoGuid")
                .operSubjectGuid("operSubjectGuid")
                .businessType(0)
                .build();
        final HsaBusinessEquities hsaBusinessEquities = HsaBusinessEquities.builder()
                .guid("guid")
                .memberGradeInfoGuid("memberGradeInfoGuid")
                .equitiesRuleType(0)
                .isDelete(0)
                .effective(0)
                .businessType(0)
                .build();
        final HsaMemberEquitiesReceiveRecord equitiesReceiveRecord = new HsaMemberEquitiesReceiveRecord();
        equitiesReceiveRecord.setGuid("18ada544-1bc2-46f3-84cc-13b9cdba4f07");
        equitiesReceiveRecord.setOperSubjectGuid("operSubjectGuid");
        equitiesReceiveRecord.setMemberGradeGuid("memberGradeInfoGuid");
        equitiesReceiveRecord.setMemberInfoGuid("memberInfoGuid");
        equitiesReceiveRecord.setGradeEquitiesGuid("guid");
        equitiesReceiveRecord.setDoubleValueCount(0);
        equitiesReceiveRecord.setTotalDoubleUpperValue(0);
        equitiesReceiveRecord.setGiveGrowthValueNumber(0);
        equitiesReceiveRecord.setType(0);

        // Run the test
        final Long result = hsaGradeEquitiesServiceImplUnderTest.exec(request, hsaBusinessEquities,
                equitiesReceiveRecord);

        // Verify the results
        assertThat(result).isEqualTo(0L);
    }

    @Test
    public void testAbstractHandlerGetterAndSetter() {
        final AbstractHandler abstractHandler = null;
        hsaGradeEquitiesServiceImplUnderTest.setAbstractHandler(abstractHandler);
        assertThat(hsaGradeEquitiesServiceImplUnderTest.getAbstractHandler()).isEqualTo(abstractHandler);
    }
}
