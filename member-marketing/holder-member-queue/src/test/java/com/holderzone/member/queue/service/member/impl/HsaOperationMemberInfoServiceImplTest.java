package com.holderzone.member.queue.service.member.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.util.concurrent.MoreExecutors;
import com.holderzone.member.common.dto.grade.MemberGradeRelationDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.queue.dto.grade.MemberGradeChangeDTO;
import com.holderzone.member.queue.dto.member.MemberGrowthValueEvent;
import com.holderzone.member.queue.entity.HsaGrowthValueDetail;
import com.holderzone.member.queue.entity.HsaMemberGradeInfo;
import com.holderzone.member.queue.entity.HsaOperationMemberInfo;
import com.holderzone.member.queue.mapper.HsaGrowthValueDetailMapper;
import com.holderzone.member.queue.mapper.HsaGrowthValueTaskMapper;
import com.holderzone.member.queue.mapper.HsaMemberGradeInfoMapper;
import com.holderzone.member.queue.mapper.HsaOperationMemberInfoMapper;
import com.holderzone.member.queue.service.grade.HsaMemberGradeChangeDetailService;
import com.holderzone.member.queue.service.grade.IHsaMemberGradeRelationService;
import com.holderzone.member.queue.service.member.HsaGrowthValueDetailService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaOperationMemberInfoServiceImplTest {

    @Mock
    private HsaOperationMemberInfoMapper mockHsaOperationMemberInfoMapper;
    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private HsaGrowthValueTaskMapper mockHsaGrowthValueTaskMapper;
    @Mock
    private HsaGrowthValueDetailService mockHsaGrowthValueDetailService;
    @Mock
    private HsaGrowthValueDetailMapper mockHsaGrowthValueDetailMapper;
    @Mock
    private HsaMemberGradeChangeDetailService mockHsaMemberGradeChangeDetailService;
    @Mock
    private HsaMemberGradeInfoMapper mockHsaMemberGradeInfoMapper;
    @Mock
    private IHsaMemberGradeRelationService mockMemberGradeRelationService;

    @InjectMocks
    private HsaOperationMemberInfoServiceImpl hsaOperationMemberInfoServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(hsaOperationMemberInfoServiceImplUnderTest, "memberQueueThreadExecutor",
                MoreExecutors.directExecutor());
    }

    @Test
    public void testSystemUpdateMemberGrowth() {
        // Setup
        final MemberGrowthValueEvent event = new MemberGrowthValueEvent();
        event.setMemberInfoGuidList(Arrays.asList("value"));
        event.setOperSubjectGuid("operSubjectGuid");
        event.setGrowthValueType(0);
        event.setGrowthValue(0);
        event.setRemark("recordDeclaration");
        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");
        headerUserInfo.setSystem(0);
        event.setHeaderUserInfo(headerUserInfo);
        event.setSource(0);
        event.setChangeType(0);
        event.setRoleType("roleType");
        event.setMemberInfoGradeGuid("memberInfoGradeGuid");
        event.setNum(0);
        event.setUnit(0);

        // Configure HsaOperationMemberInfoMapper.selectList(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("guid");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setMemberGrowthValue(0);
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo);
        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperationMemberInfos);

        // Configure HsaMemberGradeInfoMapper.selectList(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("name");
        hsaMemberGradeInfo.setIsDelete(0);
        hsaMemberGradeInfo.setEffective(0);
        hsaMemberGradeInfo.setTemporaryVipGrade(0);
        hsaMemberGradeInfo.setUpgradeType(0);
        final List<HsaMemberGradeInfo> hsaMemberGradeInfos = Arrays.asList(hsaMemberGradeInfo);
        when(mockHsaMemberGradeInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfos);

        // Configure HsaMemberGradeInfoMapper.selectOne(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo1 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo1.setGuid("");
        hsaMemberGradeInfo1.setVipGrade(0);
        hsaMemberGradeInfo1.setName("name");
        hsaMemberGradeInfo1.setIsDelete(0);
        hsaMemberGradeInfo1.setEffective(0);
        hsaMemberGradeInfo1.setTemporaryVipGrade(0);
        hsaMemberGradeInfo1.setUpgradeType(0);
        when(mockHsaMemberGradeInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfo1);

        // Configure IHsaMemberGradeRelationService.queryMemberCurrentGrade(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo2 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo2.setGuid("");
        hsaMemberGradeInfo2.setVipGrade(0);
        hsaMemberGradeInfo2.setName("name");
        hsaMemberGradeInfo2.setIsDelete(0);
        hsaMemberGradeInfo2.setEffective(0);
        hsaMemberGradeInfo2.setTemporaryVipGrade(0);
        hsaMemberGradeInfo2.setUpgradeType(0);
        final MemberGradeRelationDTO relationDTO = new MemberGradeRelationDTO();
        relationDTO.setMemberGuidList(Arrays.asList("value"));
        relationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        relationDTO.setRoleType("roleType");
        relationDTO.setNum(0);
        relationDTO.setUnit(0);
        when(mockMemberGradeRelationService.queryMemberCurrentGrade(relationDTO)).thenReturn(hsaMemberGradeInfo2);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("c15944f4-5186-4a45-add3-bf54d354534f");

        // Configure HsaGrowthValueDetailMapper.selectList(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        hsaGrowthValueDetail.setGuid("c15944f4-5186-4a45-add3-bf54d354534f");
        hsaGrowthValueDetail.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail.setMemberInfoGuid("guid");
        hsaGrowthValueDetail.setRecordDeclaration("recordDeclaration");
        hsaGrowthValueDetail.setGrowthValue(0);
        hsaGrowthValueDetail.setGrowthValueType(0);
        hsaGrowthValueDetail.setGrowthValidity(0);
        hsaGrowthValueDetail.setGrowthValidityDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail.setRemainGrowthValue(0);
        hsaGrowthValueDetail.setGrowthValueSourceType(0);
        hsaGrowthValueDetail.setRecordRemainGrowthValue(0);
        hsaGrowthValueDetail.setOperatorAccountName("operatorAccountName");
        hsaGrowthValueDetail.setChangeType(0);
        hsaGrowthValueDetail.setCurrentMemberLevel("");
        hsaGrowthValueDetail.setRoleType("roleType");
        hsaGrowthValueDetail.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail.setMemberInfoGradeGuid("");
        hsaGrowthValueDetail.setUnit(0);
        hsaGrowthValueDetail.setUpgradeType(0);
        hsaGrowthValueDetail.setNum(0);
        final List<HsaGrowthValueDetail> hsaGrowthValueDetails = Arrays.asList(hsaGrowthValueDetail);
        when(mockHsaGrowthValueDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaGrowthValueDetails);

        // Configure HsaMemberGradeChangeDetailService.calculateExtraAmount(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo3 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo3.setGuid("");
        hsaMemberGradeInfo3.setVipGrade(0);
        hsaMemberGradeInfo3.setName("name");
        hsaMemberGradeInfo3.setIsDelete(0);
        hsaMemberGradeInfo3.setEffective(0);
        hsaMemberGradeInfo3.setTemporaryVipGrade(0);
        hsaMemberGradeInfo3.setUpgradeType(0);
        when(mockHsaMemberGradeChangeDetailService.calculateExtraAmount(0, "operSubjectGuid"))
                .thenReturn(hsaMemberGradeInfo3);

        // Run the test
        hsaOperationMemberInfoServiceImplUnderTest.systemUpdateMemberGrowth(event);

        // Verify the results
        // Confirm HsaGrowthValueDetailService.saveBatch(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail1 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail1.setGuid("c15944f4-5186-4a45-add3-bf54d354534f");
        hsaGrowthValueDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail1.setMemberInfoGuid("guid");
        hsaGrowthValueDetail1.setRecordDeclaration("recordDeclaration");
        hsaGrowthValueDetail1.setGrowthValue(0);
        hsaGrowthValueDetail1.setGrowthValueType(0);
        hsaGrowthValueDetail1.setGrowthValidity(0);
        hsaGrowthValueDetail1.setGrowthValidityDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail1.setRemainGrowthValue(0);
        hsaGrowthValueDetail1.setGrowthValueSourceType(0);
        hsaGrowthValueDetail1.setRecordRemainGrowthValue(0);
        hsaGrowthValueDetail1.setOperatorAccountName("operatorAccountName");
        hsaGrowthValueDetail1.setChangeType(0);
        hsaGrowthValueDetail1.setCurrentMemberLevel("");
        hsaGrowthValueDetail1.setRoleType("roleType");
        hsaGrowthValueDetail1.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail1.setMemberInfoGradeGuid("");
        hsaGrowthValueDetail1.setUnit(0);
        hsaGrowthValueDetail1.setUpgradeType(0);
        hsaGrowthValueDetail1.setNum(0);
        final List<HsaGrowthValueDetail> entityList = Arrays.asList(hsaGrowthValueDetail1);
        verify(mockHsaGrowthValueDetailService).saveBatch(entityList);

        // Confirm HsaOperationMemberInfoMapper.batchUpdateGrowth(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("guid");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberAccount("memberAccount");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo1.setMemberGrowthValue(0);
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos1 = Arrays.asList(hsaOperationMemberInfo1);
        verify(mockHsaOperationMemberInfoMapper).batchUpdateGrowth(hsaOperationMemberInfos1);

        // Confirm HsaGrowthValueDetailMapper.batchUpdate(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail2 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail2.setGuid("c15944f4-5186-4a45-add3-bf54d354534f");
        hsaGrowthValueDetail2.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail2.setMemberInfoGuid("guid");
        hsaGrowthValueDetail2.setRecordDeclaration("recordDeclaration");
        hsaGrowthValueDetail2.setGrowthValue(0);
        hsaGrowthValueDetail2.setGrowthValueType(0);
        hsaGrowthValueDetail2.setGrowthValidity(0);
        hsaGrowthValueDetail2.setGrowthValidityDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail2.setRemainGrowthValue(0);
        hsaGrowthValueDetail2.setGrowthValueSourceType(0);
        hsaGrowthValueDetail2.setRecordRemainGrowthValue(0);
        hsaGrowthValueDetail2.setOperatorAccountName("operatorAccountName");
        hsaGrowthValueDetail2.setChangeType(0);
        hsaGrowthValueDetail2.setCurrentMemberLevel("");
        hsaGrowthValueDetail2.setRoleType("roleType");
        hsaGrowthValueDetail2.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail2.setMemberInfoGradeGuid("");
        hsaGrowthValueDetail2.setUnit(0);
        hsaGrowthValueDetail2.setUpgradeType(0);
        hsaGrowthValueDetail2.setNum(0);
        final List<HsaGrowthValueDetail> activityList = Arrays.asList(hsaGrowthValueDetail2);
        verify(mockHsaGrowthValueDetailMapper).batchUpdate(activityList);

        // Confirm HsaMemberGradeChangeDetailService.memberGradeChange(...).
        final MemberGradeChangeDTO gradeChangeDTO = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo2 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo2.setGuid("guid");
        hsaOperationMemberInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo2.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo2.setMemberGrowthValue(0);
        gradeChangeDTO.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo2));
        gradeChangeDTO.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO.setSourceType(0);
        gradeChangeDTO.setIsRefresh(0);
        gradeChangeDTO.setRoleType("roleType");
        gradeChangeDTO.setSystem(0);
        gradeChangeDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        gradeChangeDTO.setUnit(0);
        gradeChangeDTO.setNum(0);
        gradeChangeDTO.setRemark("recordDeclaration");
        verify(mockHsaMemberGradeChangeDetailService).memberGradeChange(gradeChangeDTO);
    }

    @Test
    public void testSystemUpdateMemberGrowth_HsaOperationMemberInfoMapperSelectListReturnsNoItems() {
        // Setup
        final MemberGrowthValueEvent event = new MemberGrowthValueEvent();
        event.setMemberInfoGuidList(Arrays.asList("value"));
        event.setOperSubjectGuid("operSubjectGuid");
        event.setGrowthValueType(0);
        event.setGrowthValue(0);
        event.setRemark("recordDeclaration");
        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");
        headerUserInfo.setSystem(0);
        event.setHeaderUserInfo(headerUserInfo);
        event.setSource(0);
        event.setChangeType(0);
        event.setRoleType("roleType");
        event.setMemberInfoGradeGuid("memberInfoGradeGuid");
        event.setNum(0);
        event.setUnit(0);

        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure HsaMemberGradeInfoMapper.selectList(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("name");
        hsaMemberGradeInfo.setIsDelete(0);
        hsaMemberGradeInfo.setEffective(0);
        hsaMemberGradeInfo.setTemporaryVipGrade(0);
        hsaMemberGradeInfo.setUpgradeType(0);
        final List<HsaMemberGradeInfo> hsaMemberGradeInfos = Arrays.asList(hsaMemberGradeInfo);
        when(mockHsaMemberGradeInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfos);

        // Configure HsaMemberGradeInfoMapper.selectOne(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo1 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo1.setGuid("");
        hsaMemberGradeInfo1.setVipGrade(0);
        hsaMemberGradeInfo1.setName("name");
        hsaMemberGradeInfo1.setIsDelete(0);
        hsaMemberGradeInfo1.setEffective(0);
        hsaMemberGradeInfo1.setTemporaryVipGrade(0);
        hsaMemberGradeInfo1.setUpgradeType(0);
        when(mockHsaMemberGradeInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfo1);

        // Configure IHsaMemberGradeRelationService.queryMemberCurrentGrade(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo2 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo2.setGuid("");
        hsaMemberGradeInfo2.setVipGrade(0);
        hsaMemberGradeInfo2.setName("name");
        hsaMemberGradeInfo2.setIsDelete(0);
        hsaMemberGradeInfo2.setEffective(0);
        hsaMemberGradeInfo2.setTemporaryVipGrade(0);
        hsaMemberGradeInfo2.setUpgradeType(0);
        final MemberGradeRelationDTO relationDTO = new MemberGradeRelationDTO();
        relationDTO.setMemberGuidList(Arrays.asList("value"));
        relationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        relationDTO.setRoleType("roleType");
        relationDTO.setNum(0);
        relationDTO.setUnit(0);
        when(mockMemberGradeRelationService.queryMemberCurrentGrade(relationDTO)).thenReturn(hsaMemberGradeInfo2);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("c15944f4-5186-4a45-add3-bf54d354534f");

        // Configure HsaGrowthValueDetailMapper.selectList(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        hsaGrowthValueDetail.setGuid("c15944f4-5186-4a45-add3-bf54d354534f");
        hsaGrowthValueDetail.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail.setMemberInfoGuid("guid");
        hsaGrowthValueDetail.setRecordDeclaration("recordDeclaration");
        hsaGrowthValueDetail.setGrowthValue(0);
        hsaGrowthValueDetail.setGrowthValueType(0);
        hsaGrowthValueDetail.setGrowthValidity(0);
        hsaGrowthValueDetail.setGrowthValidityDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail.setRemainGrowthValue(0);
        hsaGrowthValueDetail.setGrowthValueSourceType(0);
        hsaGrowthValueDetail.setRecordRemainGrowthValue(0);
        hsaGrowthValueDetail.setOperatorAccountName("operatorAccountName");
        hsaGrowthValueDetail.setChangeType(0);
        hsaGrowthValueDetail.setCurrentMemberLevel("");
        hsaGrowthValueDetail.setRoleType("roleType");
        hsaGrowthValueDetail.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail.setMemberInfoGradeGuid("");
        hsaGrowthValueDetail.setUnit(0);
        hsaGrowthValueDetail.setUpgradeType(0);
        hsaGrowthValueDetail.setNum(0);
        final List<HsaGrowthValueDetail> hsaGrowthValueDetails = Arrays.asList(hsaGrowthValueDetail);
        when(mockHsaGrowthValueDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaGrowthValueDetails);

        // Configure HsaMemberGradeChangeDetailService.calculateExtraAmount(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo3 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo3.setGuid("");
        hsaMemberGradeInfo3.setVipGrade(0);
        hsaMemberGradeInfo3.setName("name");
        hsaMemberGradeInfo3.setIsDelete(0);
        hsaMemberGradeInfo3.setEffective(0);
        hsaMemberGradeInfo3.setTemporaryVipGrade(0);
        hsaMemberGradeInfo3.setUpgradeType(0);
        when(mockHsaMemberGradeChangeDetailService.calculateExtraAmount(0, "operSubjectGuid"))
                .thenReturn(hsaMemberGradeInfo3);

        // Run the test
        hsaOperationMemberInfoServiceImplUnderTest.systemUpdateMemberGrowth(event);

        // Verify the results
        // Confirm HsaGrowthValueDetailService.saveBatch(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail1 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail1.setGuid("c15944f4-5186-4a45-add3-bf54d354534f");
        hsaGrowthValueDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail1.setMemberInfoGuid("guid");
        hsaGrowthValueDetail1.setRecordDeclaration("recordDeclaration");
        hsaGrowthValueDetail1.setGrowthValue(0);
        hsaGrowthValueDetail1.setGrowthValueType(0);
        hsaGrowthValueDetail1.setGrowthValidity(0);
        hsaGrowthValueDetail1.setGrowthValidityDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail1.setRemainGrowthValue(0);
        hsaGrowthValueDetail1.setGrowthValueSourceType(0);
        hsaGrowthValueDetail1.setRecordRemainGrowthValue(0);
        hsaGrowthValueDetail1.setOperatorAccountName("operatorAccountName");
        hsaGrowthValueDetail1.setChangeType(0);
        hsaGrowthValueDetail1.setCurrentMemberLevel("");
        hsaGrowthValueDetail1.setRoleType("roleType");
        hsaGrowthValueDetail1.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail1.setMemberInfoGradeGuid("");
        hsaGrowthValueDetail1.setUnit(0);
        hsaGrowthValueDetail1.setUpgradeType(0);
        hsaGrowthValueDetail1.setNum(0);
        final List<HsaGrowthValueDetail> entityList = Arrays.asList(hsaGrowthValueDetail1);
        verify(mockHsaGrowthValueDetailService).saveBatch(entityList);

        // Confirm HsaOperationMemberInfoMapper.batchUpdateGrowth(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("guid");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setMemberGrowthValue(0);
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo);
        verify(mockHsaOperationMemberInfoMapper).batchUpdateGrowth(hsaOperationMemberInfos);

        // Confirm HsaGrowthValueDetailMapper.batchUpdate(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail2 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail2.setGuid("c15944f4-5186-4a45-add3-bf54d354534f");
        hsaGrowthValueDetail2.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail2.setMemberInfoGuid("guid");
        hsaGrowthValueDetail2.setRecordDeclaration("recordDeclaration");
        hsaGrowthValueDetail2.setGrowthValue(0);
        hsaGrowthValueDetail2.setGrowthValueType(0);
        hsaGrowthValueDetail2.setGrowthValidity(0);
        hsaGrowthValueDetail2.setGrowthValidityDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail2.setRemainGrowthValue(0);
        hsaGrowthValueDetail2.setGrowthValueSourceType(0);
        hsaGrowthValueDetail2.setRecordRemainGrowthValue(0);
        hsaGrowthValueDetail2.setOperatorAccountName("operatorAccountName");
        hsaGrowthValueDetail2.setChangeType(0);
        hsaGrowthValueDetail2.setCurrentMemberLevel("");
        hsaGrowthValueDetail2.setRoleType("roleType");
        hsaGrowthValueDetail2.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail2.setMemberInfoGradeGuid("");
        hsaGrowthValueDetail2.setUnit(0);
        hsaGrowthValueDetail2.setUpgradeType(0);
        hsaGrowthValueDetail2.setNum(0);
        final List<HsaGrowthValueDetail> activityList = Arrays.asList(hsaGrowthValueDetail2);
        verify(mockHsaGrowthValueDetailMapper).batchUpdate(activityList);

        // Confirm HsaMemberGradeChangeDetailService.memberGradeChange(...).
        final MemberGradeChangeDTO gradeChangeDTO = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("guid");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo1.setMemberGrowthValue(0);
        gradeChangeDTO.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo1));
        gradeChangeDTO.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO.setSourceType(0);
        gradeChangeDTO.setIsRefresh(0);
        gradeChangeDTO.setRoleType("roleType");
        gradeChangeDTO.setSystem(0);
        gradeChangeDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        gradeChangeDTO.setUnit(0);
        gradeChangeDTO.setNum(0);
        gradeChangeDTO.setRemark("recordDeclaration");
        verify(mockHsaMemberGradeChangeDetailService).memberGradeChange(gradeChangeDTO);
    }

    @Test
    public void testSystemUpdateMemberGrowth_HsaMemberGradeInfoMapperSelectListReturnsNoItems() {
        // Setup
        final MemberGrowthValueEvent event = new MemberGrowthValueEvent();
        event.setMemberInfoGuidList(Arrays.asList("value"));
        event.setOperSubjectGuid("operSubjectGuid");
        event.setGrowthValueType(0);
        event.setGrowthValue(0);
        event.setRemark("recordDeclaration");
        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");
        headerUserInfo.setSystem(0);
        event.setHeaderUserInfo(headerUserInfo);
        event.setSource(0);
        event.setChangeType(0);
        event.setRoleType("roleType");
        event.setMemberInfoGradeGuid("memberInfoGradeGuid");
        event.setNum(0);
        event.setUnit(0);

        // Configure HsaOperationMemberInfoMapper.selectList(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("guid");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setMemberGrowthValue(0);
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo);
        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperationMemberInfos);

        when(mockHsaMemberGradeInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure HsaMemberGradeInfoMapper.selectOne(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("name");
        hsaMemberGradeInfo.setIsDelete(0);
        hsaMemberGradeInfo.setEffective(0);
        hsaMemberGradeInfo.setTemporaryVipGrade(0);
        hsaMemberGradeInfo.setUpgradeType(0);
        when(mockHsaMemberGradeInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfo);

        // Configure IHsaMemberGradeRelationService.queryMemberCurrentGrade(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo1 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo1.setGuid("");
        hsaMemberGradeInfo1.setVipGrade(0);
        hsaMemberGradeInfo1.setName("name");
        hsaMemberGradeInfo1.setIsDelete(0);
        hsaMemberGradeInfo1.setEffective(0);
        hsaMemberGradeInfo1.setTemporaryVipGrade(0);
        hsaMemberGradeInfo1.setUpgradeType(0);
        final MemberGradeRelationDTO relationDTO = new MemberGradeRelationDTO();
        relationDTO.setMemberGuidList(Arrays.asList("value"));
        relationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        relationDTO.setRoleType("roleType");
        relationDTO.setNum(0);
        relationDTO.setUnit(0);
        when(mockMemberGradeRelationService.queryMemberCurrentGrade(relationDTO)).thenReturn(hsaMemberGradeInfo1);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("c15944f4-5186-4a45-add3-bf54d354534f");

        // Configure HsaGrowthValueDetailMapper.selectList(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        hsaGrowthValueDetail.setGuid("c15944f4-5186-4a45-add3-bf54d354534f");
        hsaGrowthValueDetail.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail.setMemberInfoGuid("guid");
        hsaGrowthValueDetail.setRecordDeclaration("recordDeclaration");
        hsaGrowthValueDetail.setGrowthValue(0);
        hsaGrowthValueDetail.setGrowthValueType(0);
        hsaGrowthValueDetail.setGrowthValidity(0);
        hsaGrowthValueDetail.setGrowthValidityDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail.setRemainGrowthValue(0);
        hsaGrowthValueDetail.setGrowthValueSourceType(0);
        hsaGrowthValueDetail.setRecordRemainGrowthValue(0);
        hsaGrowthValueDetail.setOperatorAccountName("operatorAccountName");
        hsaGrowthValueDetail.setChangeType(0);
        hsaGrowthValueDetail.setCurrentMemberLevel("");
        hsaGrowthValueDetail.setRoleType("roleType");
        hsaGrowthValueDetail.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail.setMemberInfoGradeGuid("");
        hsaGrowthValueDetail.setUnit(0);
        hsaGrowthValueDetail.setUpgradeType(0);
        hsaGrowthValueDetail.setNum(0);
        final List<HsaGrowthValueDetail> hsaGrowthValueDetails = Arrays.asList(hsaGrowthValueDetail);
        when(mockHsaGrowthValueDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaGrowthValueDetails);

        // Configure HsaMemberGradeChangeDetailService.calculateExtraAmount(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo2 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo2.setGuid("");
        hsaMemberGradeInfo2.setVipGrade(0);
        hsaMemberGradeInfo2.setName("name");
        hsaMemberGradeInfo2.setIsDelete(0);
        hsaMemberGradeInfo2.setEffective(0);
        hsaMemberGradeInfo2.setTemporaryVipGrade(0);
        hsaMemberGradeInfo2.setUpgradeType(0);
        when(mockHsaMemberGradeChangeDetailService.calculateExtraAmount(0, "operSubjectGuid"))
                .thenReturn(hsaMemberGradeInfo2);

        // Run the test
        hsaOperationMemberInfoServiceImplUnderTest.systemUpdateMemberGrowth(event);

        // Verify the results
        // Confirm HsaGrowthValueDetailService.saveBatch(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail1 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail1.setGuid("c15944f4-5186-4a45-add3-bf54d354534f");
        hsaGrowthValueDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail1.setMemberInfoGuid("guid");
        hsaGrowthValueDetail1.setRecordDeclaration("recordDeclaration");
        hsaGrowthValueDetail1.setGrowthValue(0);
        hsaGrowthValueDetail1.setGrowthValueType(0);
        hsaGrowthValueDetail1.setGrowthValidity(0);
        hsaGrowthValueDetail1.setGrowthValidityDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail1.setRemainGrowthValue(0);
        hsaGrowthValueDetail1.setGrowthValueSourceType(0);
        hsaGrowthValueDetail1.setRecordRemainGrowthValue(0);
        hsaGrowthValueDetail1.setOperatorAccountName("operatorAccountName");
        hsaGrowthValueDetail1.setChangeType(0);
        hsaGrowthValueDetail1.setCurrentMemberLevel("");
        hsaGrowthValueDetail1.setRoleType("roleType");
        hsaGrowthValueDetail1.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail1.setMemberInfoGradeGuid("");
        hsaGrowthValueDetail1.setUnit(0);
        hsaGrowthValueDetail1.setUpgradeType(0);
        hsaGrowthValueDetail1.setNum(0);
        final List<HsaGrowthValueDetail> entityList = Arrays.asList(hsaGrowthValueDetail1);
        verify(mockHsaGrowthValueDetailService).saveBatch(entityList);

        // Confirm HsaOperationMemberInfoMapper.batchUpdateGrowth(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("guid");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberAccount("memberAccount");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo1.setMemberGrowthValue(0);
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos1 = Arrays.asList(hsaOperationMemberInfo1);
        verify(mockHsaOperationMemberInfoMapper).batchUpdateGrowth(hsaOperationMemberInfos1);

        // Confirm HsaGrowthValueDetailMapper.batchUpdate(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail2 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail2.setGuid("c15944f4-5186-4a45-add3-bf54d354534f");
        hsaGrowthValueDetail2.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail2.setMemberInfoGuid("guid");
        hsaGrowthValueDetail2.setRecordDeclaration("recordDeclaration");
        hsaGrowthValueDetail2.setGrowthValue(0);
        hsaGrowthValueDetail2.setGrowthValueType(0);
        hsaGrowthValueDetail2.setGrowthValidity(0);
        hsaGrowthValueDetail2.setGrowthValidityDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail2.setRemainGrowthValue(0);
        hsaGrowthValueDetail2.setGrowthValueSourceType(0);
        hsaGrowthValueDetail2.setRecordRemainGrowthValue(0);
        hsaGrowthValueDetail2.setOperatorAccountName("operatorAccountName");
        hsaGrowthValueDetail2.setChangeType(0);
        hsaGrowthValueDetail2.setCurrentMemberLevel("");
        hsaGrowthValueDetail2.setRoleType("roleType");
        hsaGrowthValueDetail2.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail2.setMemberInfoGradeGuid("");
        hsaGrowthValueDetail2.setUnit(0);
        hsaGrowthValueDetail2.setUpgradeType(0);
        hsaGrowthValueDetail2.setNum(0);
        final List<HsaGrowthValueDetail> activityList = Arrays.asList(hsaGrowthValueDetail2);
        verify(mockHsaGrowthValueDetailMapper).batchUpdate(activityList);

        // Confirm HsaMemberGradeChangeDetailService.memberGradeChange(...).
        final MemberGradeChangeDTO gradeChangeDTO = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo2 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo2.setGuid("guid");
        hsaOperationMemberInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo2.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo2.setMemberGrowthValue(0);
        gradeChangeDTO.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo2));
        gradeChangeDTO.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO.setSourceType(0);
        gradeChangeDTO.setIsRefresh(0);
        gradeChangeDTO.setRoleType("roleType");
        gradeChangeDTO.setSystem(0);
        gradeChangeDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        gradeChangeDTO.setUnit(0);
        gradeChangeDTO.setNum(0);
        gradeChangeDTO.setRemark("recordDeclaration");
        verify(mockHsaMemberGradeChangeDetailService).memberGradeChange(gradeChangeDTO);
    }

    @Test
    public void testSystemUpdateMemberGrowth_HsaGrowthValueDetailMapperSelectListReturnsNoItems() {
        // Setup
        final MemberGrowthValueEvent event = new MemberGrowthValueEvent();
        event.setMemberInfoGuidList(Arrays.asList("value"));
        event.setOperSubjectGuid("operSubjectGuid");
        event.setGrowthValueType(0);
        event.setGrowthValue(0);
        event.setRemark("recordDeclaration");
        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");
        headerUserInfo.setSystem(0);
        event.setHeaderUserInfo(headerUserInfo);
        event.setSource(0);
        event.setChangeType(0);
        event.setRoleType("roleType");
        event.setMemberInfoGradeGuid("memberInfoGradeGuid");
        event.setNum(0);
        event.setUnit(0);

        // Configure HsaOperationMemberInfoMapper.selectList(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("guid");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setMemberGrowthValue(0);
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo);
        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperationMemberInfos);

        // Configure HsaMemberGradeInfoMapper.selectList(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("name");
        hsaMemberGradeInfo.setIsDelete(0);
        hsaMemberGradeInfo.setEffective(0);
        hsaMemberGradeInfo.setTemporaryVipGrade(0);
        hsaMemberGradeInfo.setUpgradeType(0);
        final List<HsaMemberGradeInfo> hsaMemberGradeInfos = Arrays.asList(hsaMemberGradeInfo);
        when(mockHsaMemberGradeInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfos);

        // Configure HsaMemberGradeInfoMapper.selectOne(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo1 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo1.setGuid("");
        hsaMemberGradeInfo1.setVipGrade(0);
        hsaMemberGradeInfo1.setName("name");
        hsaMemberGradeInfo1.setIsDelete(0);
        hsaMemberGradeInfo1.setEffective(0);
        hsaMemberGradeInfo1.setTemporaryVipGrade(0);
        hsaMemberGradeInfo1.setUpgradeType(0);
        when(mockHsaMemberGradeInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfo1);

        // Configure IHsaMemberGradeRelationService.queryMemberCurrentGrade(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo2 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo2.setGuid("");
        hsaMemberGradeInfo2.setVipGrade(0);
        hsaMemberGradeInfo2.setName("name");
        hsaMemberGradeInfo2.setIsDelete(0);
        hsaMemberGradeInfo2.setEffective(0);
        hsaMemberGradeInfo2.setTemporaryVipGrade(0);
        hsaMemberGradeInfo2.setUpgradeType(0);
        final MemberGradeRelationDTO relationDTO = new MemberGradeRelationDTO();
        relationDTO.setMemberGuidList(Arrays.asList("value"));
        relationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        relationDTO.setRoleType("roleType");
        relationDTO.setNum(0);
        relationDTO.setUnit(0);
        when(mockMemberGradeRelationService.queryMemberCurrentGrade(relationDTO)).thenReturn(hsaMemberGradeInfo2);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("c15944f4-5186-4a45-add3-bf54d354534f");
        when(mockHsaGrowthValueDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure HsaMemberGradeChangeDetailService.calculateExtraAmount(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo3 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo3.setGuid("");
        hsaMemberGradeInfo3.setVipGrade(0);
        hsaMemberGradeInfo3.setName("name");
        hsaMemberGradeInfo3.setIsDelete(0);
        hsaMemberGradeInfo3.setEffective(0);
        hsaMemberGradeInfo3.setTemporaryVipGrade(0);
        hsaMemberGradeInfo3.setUpgradeType(0);
        when(mockHsaMemberGradeChangeDetailService.calculateExtraAmount(0, "operSubjectGuid"))
                .thenReturn(hsaMemberGradeInfo3);

        // Run the test
        hsaOperationMemberInfoServiceImplUnderTest.systemUpdateMemberGrowth(event);

        // Verify the results
        // Confirm HsaGrowthValueDetailService.saveBatch(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        hsaGrowthValueDetail.setGuid("c15944f4-5186-4a45-add3-bf54d354534f");
        hsaGrowthValueDetail.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail.setMemberInfoGuid("guid");
        hsaGrowthValueDetail.setRecordDeclaration("recordDeclaration");
        hsaGrowthValueDetail.setGrowthValue(0);
        hsaGrowthValueDetail.setGrowthValueType(0);
        hsaGrowthValueDetail.setGrowthValidity(0);
        hsaGrowthValueDetail.setGrowthValidityDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail.setRemainGrowthValue(0);
        hsaGrowthValueDetail.setGrowthValueSourceType(0);
        hsaGrowthValueDetail.setRecordRemainGrowthValue(0);
        hsaGrowthValueDetail.setOperatorAccountName("operatorAccountName");
        hsaGrowthValueDetail.setChangeType(0);
        hsaGrowthValueDetail.setCurrentMemberLevel("");
        hsaGrowthValueDetail.setRoleType("roleType");
        hsaGrowthValueDetail.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail.setMemberInfoGradeGuid("");
        hsaGrowthValueDetail.setUnit(0);
        hsaGrowthValueDetail.setUpgradeType(0);
        hsaGrowthValueDetail.setNum(0);
        final List<HsaGrowthValueDetail> entityList = Arrays.asList(hsaGrowthValueDetail);
        verify(mockHsaGrowthValueDetailService).saveBatch(entityList);

        // Confirm HsaOperationMemberInfoMapper.batchUpdateGrowth(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("guid");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberAccount("memberAccount");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo1.setMemberGrowthValue(0);
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos1 = Arrays.asList(hsaOperationMemberInfo1);
        verify(mockHsaOperationMemberInfoMapper).batchUpdateGrowth(hsaOperationMemberInfos1);

        // Confirm HsaGrowthValueDetailMapper.batchUpdate(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail1 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail1.setGuid("c15944f4-5186-4a45-add3-bf54d354534f");
        hsaGrowthValueDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail1.setMemberInfoGuid("guid");
        hsaGrowthValueDetail1.setRecordDeclaration("recordDeclaration");
        hsaGrowthValueDetail1.setGrowthValue(0);
        hsaGrowthValueDetail1.setGrowthValueType(0);
        hsaGrowthValueDetail1.setGrowthValidity(0);
        hsaGrowthValueDetail1.setGrowthValidityDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail1.setRemainGrowthValue(0);
        hsaGrowthValueDetail1.setGrowthValueSourceType(0);
        hsaGrowthValueDetail1.setRecordRemainGrowthValue(0);
        hsaGrowthValueDetail1.setOperatorAccountName("operatorAccountName");
        hsaGrowthValueDetail1.setChangeType(0);
        hsaGrowthValueDetail1.setCurrentMemberLevel("");
        hsaGrowthValueDetail1.setRoleType("roleType");
        hsaGrowthValueDetail1.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail1.setMemberInfoGradeGuid("");
        hsaGrowthValueDetail1.setUnit(0);
        hsaGrowthValueDetail1.setUpgradeType(0);
        hsaGrowthValueDetail1.setNum(0);
        final List<HsaGrowthValueDetail> activityList = Arrays.asList(hsaGrowthValueDetail1);
        verify(mockHsaGrowthValueDetailMapper).batchUpdate(activityList);

        // Confirm HsaMemberGradeChangeDetailService.memberGradeChange(...).
        final MemberGradeChangeDTO gradeChangeDTO = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo2 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo2.setGuid("guid");
        hsaOperationMemberInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo2.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo2.setMemberGrowthValue(0);
        gradeChangeDTO.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo2));
        gradeChangeDTO.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO.setSourceType(0);
        gradeChangeDTO.setIsRefresh(0);
        gradeChangeDTO.setRoleType("roleType");
        gradeChangeDTO.setSystem(0);
        gradeChangeDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        gradeChangeDTO.setUnit(0);
        gradeChangeDTO.setNum(0);
        gradeChangeDTO.setRemark("recordDeclaration");
        verify(mockHsaMemberGradeChangeDetailService).memberGradeChange(gradeChangeDTO);
    }

    @Test
    public void testCheckMemberGuid() {
        // Setup
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("guid");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setMemberGrowthValue(0);
        final List<HsaOperationMemberInfo> expectedResult = Arrays.asList(hsaOperationMemberInfo);

        // Configure HsaOperationMemberInfoMapper.selectList(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("guid");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberAccount("memberAccount");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo1.setMemberGrowthValue(0);
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo1);
        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperationMemberInfos);

        // Run the test
        final List<HsaOperationMemberInfo> result = hsaOperationMemberInfoServiceImplUnderTest.checkMemberGuid(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCheckMemberGuid_HsaOperationMemberInfoMapperReturnsNoItems() {
        // Setup
        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<HsaOperationMemberInfo> result = hsaOperationMemberInfoServiceImplUnderTest.checkMemberGuid(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
