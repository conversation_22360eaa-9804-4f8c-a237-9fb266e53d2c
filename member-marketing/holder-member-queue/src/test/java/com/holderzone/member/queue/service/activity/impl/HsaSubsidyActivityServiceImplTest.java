package com.holderzone.member.queue.service.activity.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.util.concurrent.MoreExecutors;
import com.holderzone.member.common.dto.event.SendBackSubsidyRightsEvent;
import com.holderzone.member.common.dto.event.SendSubsidyRightsEvent;
import com.holderzone.member.common.dto.event.SubsidyActivityEvent;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.qo.card.CardFreezeBalanceAmountQO;
import com.holderzone.member.common.qo.member.MemberListQO;
import com.holderzone.member.common.qo.tool.MessagesSendQO;
import com.holderzone.member.common.qo.tool.MessagesSendbatchQO;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import com.holderzone.member.common.util.transaction.TransactionUtil;
import com.holderzone.member.common.vo.member.MemberInfoVO;
import com.holderzone.member.queue.dto.activity.CreationSubsidyDetailRecordDTO;
import com.holderzone.member.queue.dto.activity.SendCheckSubsidyDTO;
import com.holderzone.member.queue.entity.*;
import com.holderzone.member.queue.mapper.*;
import com.holderzone.member.queue.service.activity.HsaSubsidyActivityDetailRecordService;
import com.holderzone.member.queue.service.activity.assembler.SubsidyActivityAssembler;
import com.holderzone.member.queue.service.gift.HsaCardRechargeGiftDetailService;
import com.holderzone.member.queue.service.member.HsaMemberFundingDetailService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaSubsidyActivityServiceImplTest {

    @Mock
    private HsaSubsidyActivityRecordMapper mockHsaSubsidyActivityRecordMapper;
    @Mock
    private HsaMemberLabelMapper mockHsaMemberLabelMapper;
    @Mock
    private HsaOperationMemberInfoMapper mockHsaOperationMemberInfoMapper;
    @Mock
    private HsaMemberInfoCardMapper mockHsaMemberInfoCardMapper;
    @Mock
    private HsaSubsidyActivityMapper mockHsaSubsidyActivityMapper;
    @Mock
    private HsaCardBaseInfoMapper mockHsaCardBaseInfoMapper;
    @Mock
    private SystemRoleHelper mockSystemRoleHelper;
    @Mock
    private HsaSubsidyActivityDetailRecordMapper mockHsaSubsidyActivityDetailRecordMapper;
    @Mock
    private HsaSubsidyActivityDetailRecordService mockHsaSubsidyActivityDetailRecordService;
    @Mock
    private HsaMemberFundingDetailService mockHsaMemberFundingDetailService;
    @Mock
    private HsaCardRechargeGiftDetailService mockHsaCardRechargeGiftDetailService;
    @Mock
    private MemberBaseFeign mockMemberBaseFeign;
    @Mock
    private TransactionUtil mockTransactionUtil;
    @Mock
    private RedissonClient mockRedissonClient;
    @Mock
    private SubsidyActivityAssembler mockSubsidyActivityAssembler;

    @InjectMocks
    private HsaSubsidyActivityServiceImpl hsaSubsidyActivityServiceImplUnderTest;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(hsaSubsidyActivityServiceImplUnderTest, "memberQueueThreadExecutor",
                MoreExecutors.directExecutor());
    }

    @Test
    public void testSendSubsidyRights() {
        // Setup
        final SendSubsidyRightsEvent event = new SendSubsidyRightsEvent();
        final SubsidyActivityEvent subsidyActivityEvent = new SubsidyActivityEvent();
        subsidyActivityEvent.setGuid("dd1459f7-d323-4a1d-8740-71a66b6aaf2d");
        subsidyActivityEvent.setOperSubjectGuid("operSubjectGuid");
        subsidyActivityEvent.setCardGuidJson("cardGuidJson");
        subsidyActivityEvent.setSubsidyFilterType(0);
        subsidyActivityEvent.setMemberInfoGuidJson("memberInfoGuidJson");
        subsidyActivityEvent.setLabelGuidJson("labelGuidJson");
        subsidyActivityEvent.setRecordGuid(Arrays.asList("value"));
        event.setSubsidyActivityEventList(Arrays.asList(subsidyActivityEvent));
        event.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure HsaSubsidyActivityMapper.selectList(...).
        final HsaSubsidyActivity hsaSubsidyActivity = new HsaSubsidyActivity();
        hsaSubsidyActivity.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity.setActivityState(0);
        hsaSubsidyActivity.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity.setSubsidyFrequencyType(0);
        hsaSubsidyActivity.setSubsidyMemberNum(0);
        hsaSubsidyActivity.setIsRetreat(0);
        final List<HsaSubsidyActivity> hsaSubsidyActivities = Arrays.asList(hsaSubsidyActivity);
        when(mockHsaSubsidyActivityMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaSubsidyActivities);

        // Configure HsaSubsidyActivityRecordMapper.selectList(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord.setSubsidySendState(0);
        hsaSubsidyActivityRecord.setSubsidyNum(0);
        hsaSubsidyActivityRecord.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords = Arrays.asList(hsaSubsidyActivityRecord);
        when(mockHsaSubsidyActivityRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityRecords);

        // Configure HsaCardBaseInfoMapper.selectList(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo.setCardName("cardName");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardStatus(0);
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCardBaseInfos);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f3a2c274-c46f-412f-b49b-d70eb56f71aa");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("operationMemberInfoGuid");
        hsaMemberLabel.setOperatorGuid("operatorGuid");
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        // Configure HsaOperationMemberInfoMapper.selectList(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("5b2bb102-6c7a-4604-b6bb-1c4f7269c56e");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setAccountState(0);
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo);
        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperationMemberInfos);

        // Configure HsaMemberInfoCardMapper.selectList(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardValidity(0);
        hsaMemberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> hsaMemberInfoCards = Arrays.asList(hsaMemberInfoCard);
        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberInfoCards);

        // Configure SubsidyActivityAssembler.getHsaSubsidyActivityDetailRecord(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord.setGuid("guid");
        hsaSubsidyActivityDetailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord1 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord1.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord1.setSubsidySendState(0);
        hsaSubsidyActivityRecord1.setSubsidyNum(0);
        hsaSubsidyActivityRecord1.setSubsidyErrorNum(0);
        final HsaSubsidyActivity hsaSubsidyActivity1 = new HsaSubsidyActivity();
        hsaSubsidyActivity1.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity1.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity1.setActivityState(0);
        hsaSubsidyActivity1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity1.setSubsidyFrequencyType(0);
        hsaSubsidyActivity1.setSubsidyMemberNum(0);
        hsaSubsidyActivity1.setIsRetreat(0);
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("5b2bb102-6c7a-4604-b6bb-1c4f7269c56e");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberAccount("memberAccount");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo1.setAccountState(0);
        final CreationSubsidyDetailRecordDTO recordDTO = new CreationSubsidyDetailRecordDTO();
        recordDTO.setError("【账户】已禁用");
        recordDTO.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recordDTO.setResult(0);
        recordDTO.setOutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recordDTO.setMemberInfoCardGuid("guid");
        recordDTO.setAmount(new BigDecimal("0.00"));
        when(mockSubsidyActivityAssembler.getHsaSubsidyActivityDetailRecord(hsaSubsidyActivityRecord1,
                hsaSubsidyActivity1, hsaOperationMemberInfo1, recordDTO, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0))).thenReturn(hsaSubsidyActivityDetailRecord);

        when(mockSystemRoleHelper.getReplace("【会员卡】不存在", "operSubjectGuid")).thenReturn("【账户】已禁用");

        // Configure SubsidyActivityAssembler.getRechargeDetail(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail.setStoreName("storeName");
        hsaMemberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail.setSubsidyDetailRecordGuid("guid");
        final HsaMemberInfoCard memberInfoCard = new HsaMemberInfoCard();
        memberInfoCard.setGuid("guid");
        memberInfoCard.setMemberInfoGuid("memberInfoGuid");
        memberInfoCard.setCardGuid("cardGuid");
        memberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        memberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        memberInfoCard.setCardAmount(new BigDecimal("0.00"));
        memberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessTimes(0);
        memberInfoCard.setCardValidity(0);
        memberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final HsaSubsidyActivity hsaSubsidyActivity2 = new HsaSubsidyActivity();
        hsaSubsidyActivity2.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity2.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity2.setActivityState(0);
        hsaSubsidyActivity2.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity2.setSubsidyFrequencyType(0);
        hsaSubsidyActivity2.setSubsidyMemberNum(0);
        hsaSubsidyActivity2.setIsRetreat(0);
        final HsaMemberFundingDetail memberFundingDetail = new HsaMemberFundingDetail();
        memberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        memberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        memberFundingDetail.setStoreName("storeName");
        memberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        memberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberFundingDetail.setSubsidyDetailRecordGuid("guid");
        when(mockSubsidyActivityAssembler.getRechargeDetail(memberInfoCard, hsaSubsidyActivity2,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), memberFundingDetail)).thenReturn(hsaMemberFundingDetail);

        when(mockHsaOperationMemberInfoMapper.listByOperSubjectGuid("operSubjectGuid"))
                .thenReturn(new HashSet<>(Arrays.asList("value")));

        // Configure HsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(...).
        final CardFreezeBalanceAmountQO cardFreezeBalanceAmountQO = new CardFreezeBalanceAmountQO();
        cardFreezeBalanceAmountQO.setPhysicalCardGuid("physicalCardGuid");
        cardFreezeBalanceAmountQO.setElectronicCardGuid("electronicCardGuid");
        cardFreezeBalanceAmountQO.setMemberInfoGuid("memberInfoGuid");
        cardFreezeBalanceAmountQO.setMemberInfoCardGuid("guid");
        cardFreezeBalanceAmountQO.setIsRefresh(0);
        final List<CardFreezeBalanceAmountQO> cardList = Arrays.asList(cardFreezeBalanceAmountQO);
        when(mockHsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(cardList)).thenReturn(new HashMap<>());

        // Configure HsaCardBaseInfoMapper.queryByGuids(...).
        final HsaCardBaseInfo hsaCardBaseInfo1 = new HsaCardBaseInfo();
        hsaCardBaseInfo1.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo1.setCardName("cardName");
        hsaCardBaseInfo1.setCardImage("cardImage");
        hsaCardBaseInfo1.setCardStatus(0);
        hsaCardBaseInfo1.setIsExcess(0);
        hsaCardBaseInfo1.setExcessType(0);
        hsaCardBaseInfo1.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos1 = Arrays.asList(hsaCardBaseInfo1);
        when(mockHsaCardBaseInfoMapper.queryByGuids(Arrays.asList("value"))).thenReturn(hsaCardBaseInfos1);

        // Configure HsaOperationMemberInfoMapper.listMemberInfo(...).
        final MemberInfoVO memberInfoVO = new MemberInfoVO();
        memberInfoVO.setOperSubjectGuid("operSubjectGuid");
        memberInfoVO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVO.setAccountState(0);
        memberInfoVO.setMemberGuid("memberGuid");
        memberInfoVO.setPhoneNum("phone");
        final List<MemberInfoVO> memberInfoVOS = Arrays.asList(memberInfoVO);
        final MemberListQO request = new MemberListQO();
        request.setOperSubjectGuid("operSubjectGuid");
        request.setIsDisable(0);
        request.setMemberGuids(Arrays.asList("value"));
        request.setKeywords("keywords");
        request.setSex(0);
        when(mockHsaOperationMemberInfoMapper.listMemberInfo(request)).thenReturn(memberInfoVOS);

        // Run the test
        hsaSubsidyActivityServiceImplUnderTest.sendSubsidyRights(event, 0);

        // Verify the results
        verify(mockTransactionUtil).transactional(any(Consumer.class));

        // Confirm HsaSubsidyActivityDetailRecordService.saveBatch(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord1 = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord1.setGuid("guid");
        hsaSubsidyActivityDetailRecord1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord1.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord1.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord1.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord1.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord1.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> entityList = Arrays.asList(hsaSubsidyActivityDetailRecord1);
        verify(mockHsaSubsidyActivityDetailRecordService).saveBatch(entityList);

        // Confirm HsaSubsidyActivityRecordMapper.batchUpdateStatus(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord2 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord2.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord2.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord2.setSubsidySendState(0);
        hsaSubsidyActivityRecord2.setSubsidyNum(0);
        hsaSubsidyActivityRecord2.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> activityList = Arrays.asList(hsaSubsidyActivityRecord2);
        verify(mockHsaSubsidyActivityRecordMapper).batchUpdateStatus(activityList);

        // Confirm HsaSubsidyActivityMapper.batchUpdateStatus(...).
        final HsaSubsidyActivity hsaSubsidyActivity3 = new HsaSubsidyActivity();
        hsaSubsidyActivity3.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity3.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity3.setActivityState(0);
        hsaSubsidyActivity3.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity3.setSubsidyFrequencyType(0);
        hsaSubsidyActivity3.setSubsidyMemberNum(0);
        hsaSubsidyActivity3.setIsRetreat(0);
        final List<HsaSubsidyActivity> activityList1 = Arrays.asList(hsaSubsidyActivity3);
        verify(mockHsaSubsidyActivityMapper).batchUpdateStatus(activityList1);

        // Confirm HsaMemberInfoCardMapper.batchUpdate(...).
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardValidity(0);
        hsaMemberInfoCard1.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> activityList2 = Arrays.asList(hsaMemberInfoCard1);
        verify(mockHsaMemberInfoCardMapper).batchUpdate(activityList2);

        // Confirm HsaMemberFundingDetailService.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail1 = new HsaMemberFundingDetail();
        hsaMemberFundingDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail1.setStoreName("storeName");
        hsaMemberFundingDetail1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail1.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail1.setSubsidyDetailRecordGuid("guid");
        final List<HsaMemberFundingDetail> entityList1 = Arrays.asList(hsaMemberFundingDetail1);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList1);

        // Confirm MemberBaseFeign.wechatMessageSendBatch(...).
        final MessagesSendQO messagesSendQO = new MessagesSendQO();
        messagesSendQO.setTemplateName("msgTitle");
        messagesSendQO.setPrams(new HashMap<>());
        messagesSendQO.setMpParams(new HashMap<>());
        messagesSendQO.setOperSubjectGuid("operSubjectGuid");
        messagesSendQO.setEnterpriseGuid("enterpriseGuid");
        messagesSendQO.setPhone("phone");
        messagesSendQO.setOrderNum("");
        messagesSendQO.setMemberInfoCardGuid("memberInfoCardGuid");
        messagesSendQO.setCardName("cardName");
        messagesSendQO.setCardImage("cardImage");
        final MessagesSendbatchQO messagesSendBatchQuery = new MessagesSendbatchQO(Arrays.asList(messagesSendQO));
        verify(mockMemberBaseFeign).wechatMessageSendBatch(messagesSendBatchQuery);
    }

    @Test
    public void testSendSubsidyRights_HsaSubsidyActivityMapperSelectListReturnsNoItems() {
        // Setup
        final SendSubsidyRightsEvent event = new SendSubsidyRightsEvent();
        final SubsidyActivityEvent subsidyActivityEvent = new SubsidyActivityEvent();
        subsidyActivityEvent.setGuid("dd1459f7-d323-4a1d-8740-71a66b6aaf2d");
        subsidyActivityEvent.setOperSubjectGuid("operSubjectGuid");
        subsidyActivityEvent.setCardGuidJson("cardGuidJson");
        subsidyActivityEvent.setSubsidyFilterType(0);
        subsidyActivityEvent.setMemberInfoGuidJson("memberInfoGuidJson");
        subsidyActivityEvent.setLabelGuidJson("labelGuidJson");
        subsidyActivityEvent.setRecordGuid(Arrays.asList("value"));
        event.setSubsidyActivityEventList(Arrays.asList(subsidyActivityEvent));
        event.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockHsaSubsidyActivityMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure HsaSubsidyActivityRecordMapper.selectList(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord.setSubsidySendState(0);
        hsaSubsidyActivityRecord.setSubsidyNum(0);
        hsaSubsidyActivityRecord.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords = Arrays.asList(hsaSubsidyActivityRecord);
        when(mockHsaSubsidyActivityRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityRecords);

        // Configure HsaCardBaseInfoMapper.selectList(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo.setCardName("cardName");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardStatus(0);
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCardBaseInfos);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f3a2c274-c46f-412f-b49b-d70eb56f71aa");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("operationMemberInfoGuid");
        hsaMemberLabel.setOperatorGuid("operatorGuid");
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        // Configure HsaOperationMemberInfoMapper.selectList(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("5b2bb102-6c7a-4604-b6bb-1c4f7269c56e");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setAccountState(0);
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo);
        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperationMemberInfos);

        // Configure HsaMemberInfoCardMapper.selectList(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardValidity(0);
        hsaMemberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> hsaMemberInfoCards = Arrays.asList(hsaMemberInfoCard);
        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberInfoCards);

        // Configure SubsidyActivityAssembler.getHsaSubsidyActivityDetailRecord(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord.setGuid("guid");
        hsaSubsidyActivityDetailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord1 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord1.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord1.setSubsidySendState(0);
        hsaSubsidyActivityRecord1.setSubsidyNum(0);
        hsaSubsidyActivityRecord1.setSubsidyErrorNum(0);
        final HsaSubsidyActivity hsaSubsidyActivity = new HsaSubsidyActivity();
        hsaSubsidyActivity.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity.setActivityState(0);
        hsaSubsidyActivity.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity.setSubsidyFrequencyType(0);
        hsaSubsidyActivity.setSubsidyMemberNum(0);
        hsaSubsidyActivity.setIsRetreat(0);
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("5b2bb102-6c7a-4604-b6bb-1c4f7269c56e");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberAccount("memberAccount");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo1.setAccountState(0);
        final CreationSubsidyDetailRecordDTO recordDTO = new CreationSubsidyDetailRecordDTO();
        recordDTO.setError("【账户】已禁用");
        recordDTO.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recordDTO.setResult(0);
        recordDTO.setOutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recordDTO.setMemberInfoCardGuid("guid");
        recordDTO.setAmount(new BigDecimal("0.00"));
        when(mockSubsidyActivityAssembler.getHsaSubsidyActivityDetailRecord(hsaSubsidyActivityRecord1,
                hsaSubsidyActivity, hsaOperationMemberInfo1, recordDTO, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0))).thenReturn(hsaSubsidyActivityDetailRecord);

        when(mockSystemRoleHelper.getReplace("【会员卡】不存在", "operSubjectGuid")).thenReturn("【账户】已禁用");

        // Configure SubsidyActivityAssembler.getRechargeDetail(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail.setStoreName("storeName");
        hsaMemberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail.setSubsidyDetailRecordGuid("guid");
        final HsaMemberInfoCard memberInfoCard = new HsaMemberInfoCard();
        memberInfoCard.setGuid("guid");
        memberInfoCard.setMemberInfoGuid("memberInfoGuid");
        memberInfoCard.setCardGuid("cardGuid");
        memberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        memberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        memberInfoCard.setCardAmount(new BigDecimal("0.00"));
        memberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessTimes(0);
        memberInfoCard.setCardValidity(0);
        memberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final HsaSubsidyActivity hsaSubsidyActivity1 = new HsaSubsidyActivity();
        hsaSubsidyActivity1.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity1.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity1.setActivityState(0);
        hsaSubsidyActivity1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity1.setSubsidyFrequencyType(0);
        hsaSubsidyActivity1.setSubsidyMemberNum(0);
        hsaSubsidyActivity1.setIsRetreat(0);
        final HsaMemberFundingDetail memberFundingDetail = new HsaMemberFundingDetail();
        memberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        memberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        memberFundingDetail.setStoreName("storeName");
        memberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        memberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberFundingDetail.setSubsidyDetailRecordGuid("guid");
        when(mockSubsidyActivityAssembler.getRechargeDetail(memberInfoCard, hsaSubsidyActivity1,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), memberFundingDetail)).thenReturn(hsaMemberFundingDetail);

        when(mockHsaOperationMemberInfoMapper.listByOperSubjectGuid("operSubjectGuid"))
                .thenReturn(new HashSet<>(Arrays.asList("value")));

        // Configure HsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(...).
        final CardFreezeBalanceAmountQO cardFreezeBalanceAmountQO = new CardFreezeBalanceAmountQO();
        cardFreezeBalanceAmountQO.setPhysicalCardGuid("physicalCardGuid");
        cardFreezeBalanceAmountQO.setElectronicCardGuid("electronicCardGuid");
        cardFreezeBalanceAmountQO.setMemberInfoGuid("memberInfoGuid");
        cardFreezeBalanceAmountQO.setMemberInfoCardGuid("guid");
        cardFreezeBalanceAmountQO.setIsRefresh(0);
        final List<CardFreezeBalanceAmountQO> cardList = Arrays.asList(cardFreezeBalanceAmountQO);
        when(mockHsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(cardList)).thenReturn(new HashMap<>());

        // Configure HsaCardBaseInfoMapper.queryByGuids(...).
        final HsaCardBaseInfo hsaCardBaseInfo1 = new HsaCardBaseInfo();
        hsaCardBaseInfo1.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo1.setCardName("cardName");
        hsaCardBaseInfo1.setCardImage("cardImage");
        hsaCardBaseInfo1.setCardStatus(0);
        hsaCardBaseInfo1.setIsExcess(0);
        hsaCardBaseInfo1.setExcessType(0);
        hsaCardBaseInfo1.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos1 = Arrays.asList(hsaCardBaseInfo1);
        when(mockHsaCardBaseInfoMapper.queryByGuids(Arrays.asList("value"))).thenReturn(hsaCardBaseInfos1);

        // Configure HsaOperationMemberInfoMapper.listMemberInfo(...).
        final MemberInfoVO memberInfoVO = new MemberInfoVO();
        memberInfoVO.setOperSubjectGuid("operSubjectGuid");
        memberInfoVO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVO.setAccountState(0);
        memberInfoVO.setMemberGuid("memberGuid");
        memberInfoVO.setPhoneNum("phone");
        final List<MemberInfoVO> memberInfoVOS = Arrays.asList(memberInfoVO);
        final MemberListQO request = new MemberListQO();
        request.setOperSubjectGuid("operSubjectGuid");
        request.setIsDisable(0);
        request.setMemberGuids(Arrays.asList("value"));
        request.setKeywords("keywords");
        request.setSex(0);
        when(mockHsaOperationMemberInfoMapper.listMemberInfo(request)).thenReturn(memberInfoVOS);

        // Run the test
        hsaSubsidyActivityServiceImplUnderTest.sendSubsidyRights(event, 0);

        // Verify the results
        verify(mockTransactionUtil).transactional(any(Consumer.class));

        // Confirm HsaSubsidyActivityDetailRecordService.saveBatch(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord1 = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord1.setGuid("guid");
        hsaSubsidyActivityDetailRecord1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord1.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord1.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord1.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord1.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord1.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> entityList = Arrays.asList(hsaSubsidyActivityDetailRecord1);
        verify(mockHsaSubsidyActivityDetailRecordService).saveBatch(entityList);

        // Confirm HsaSubsidyActivityRecordMapper.batchUpdateStatus(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord2 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord2.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord2.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord2.setSubsidySendState(0);
        hsaSubsidyActivityRecord2.setSubsidyNum(0);
        hsaSubsidyActivityRecord2.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> activityList = Arrays.asList(hsaSubsidyActivityRecord2);
        verify(mockHsaSubsidyActivityRecordMapper).batchUpdateStatus(activityList);

        // Confirm HsaSubsidyActivityMapper.batchUpdateStatus(...).
        final HsaSubsidyActivity hsaSubsidyActivity2 = new HsaSubsidyActivity();
        hsaSubsidyActivity2.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity2.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity2.setActivityState(0);
        hsaSubsidyActivity2.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity2.setSubsidyFrequencyType(0);
        hsaSubsidyActivity2.setSubsidyMemberNum(0);
        hsaSubsidyActivity2.setIsRetreat(0);
        final List<HsaSubsidyActivity> activityList1 = Arrays.asList(hsaSubsidyActivity2);
        verify(mockHsaSubsidyActivityMapper).batchUpdateStatus(activityList1);

        // Confirm HsaMemberInfoCardMapper.batchUpdate(...).
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardValidity(0);
        hsaMemberInfoCard1.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> activityList2 = Arrays.asList(hsaMemberInfoCard1);
        verify(mockHsaMemberInfoCardMapper).batchUpdate(activityList2);

        // Confirm HsaMemberFundingDetailService.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail1 = new HsaMemberFundingDetail();
        hsaMemberFundingDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail1.setStoreName("storeName");
        hsaMemberFundingDetail1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail1.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail1.setSubsidyDetailRecordGuid("guid");
        final List<HsaMemberFundingDetail> entityList1 = Arrays.asList(hsaMemberFundingDetail1);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList1);

        // Confirm MemberBaseFeign.wechatMessageSendBatch(...).
        final MessagesSendQO messagesSendQO = new MessagesSendQO();
        messagesSendQO.setTemplateName("msgTitle");
        messagesSendQO.setPrams(new HashMap<>());
        messagesSendQO.setMpParams(new HashMap<>());
        messagesSendQO.setOperSubjectGuid("operSubjectGuid");
        messagesSendQO.setEnterpriseGuid("enterpriseGuid");
        messagesSendQO.setPhone("phone");
        messagesSendQO.setOrderNum("");
        messagesSendQO.setMemberInfoCardGuid("memberInfoCardGuid");
        messagesSendQO.setCardName("cardName");
        messagesSendQO.setCardImage("cardImage");
        final MessagesSendbatchQO messagesSendBatchQuery = new MessagesSendbatchQO(Arrays.asList(messagesSendQO));
        verify(mockMemberBaseFeign).wechatMessageSendBatch(messagesSendBatchQuery);
    }

    @Test
    public void testSendSubsidyRights_HsaSubsidyActivityRecordMapperSelectListReturnsNoItems() {
        // Setup
        final SendSubsidyRightsEvent event = new SendSubsidyRightsEvent();
        final SubsidyActivityEvent subsidyActivityEvent = new SubsidyActivityEvent();
        subsidyActivityEvent.setGuid("dd1459f7-d323-4a1d-8740-71a66b6aaf2d");
        subsidyActivityEvent.setOperSubjectGuid("operSubjectGuid");
        subsidyActivityEvent.setCardGuidJson("cardGuidJson");
        subsidyActivityEvent.setSubsidyFilterType(0);
        subsidyActivityEvent.setMemberInfoGuidJson("memberInfoGuidJson");
        subsidyActivityEvent.setLabelGuidJson("labelGuidJson");
        subsidyActivityEvent.setRecordGuid(Arrays.asList("value"));
        event.setSubsidyActivityEventList(Arrays.asList(subsidyActivityEvent));
        event.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure HsaSubsidyActivityMapper.selectList(...).
        final HsaSubsidyActivity hsaSubsidyActivity = new HsaSubsidyActivity();
        hsaSubsidyActivity.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity.setActivityState(0);
        hsaSubsidyActivity.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity.setSubsidyFrequencyType(0);
        hsaSubsidyActivity.setSubsidyMemberNum(0);
        hsaSubsidyActivity.setIsRetreat(0);
        final List<HsaSubsidyActivity> hsaSubsidyActivities = Arrays.asList(hsaSubsidyActivity);
        when(mockHsaSubsidyActivityMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaSubsidyActivities);

        when(mockHsaSubsidyActivityRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure HsaCardBaseInfoMapper.selectList(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo.setCardName("cardName");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardStatus(0);
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCardBaseInfos);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f3a2c274-c46f-412f-b49b-d70eb56f71aa");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("operationMemberInfoGuid");
        hsaMemberLabel.setOperatorGuid("operatorGuid");
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        // Configure HsaOperationMemberInfoMapper.selectList(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("5b2bb102-6c7a-4604-b6bb-1c4f7269c56e");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setAccountState(0);
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo);
        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperationMemberInfos);

        // Configure HsaMemberInfoCardMapper.selectList(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardValidity(0);
        hsaMemberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> hsaMemberInfoCards = Arrays.asList(hsaMemberInfoCard);
        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberInfoCards);

        // Configure SubsidyActivityAssembler.getHsaSubsidyActivityDetailRecord(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord.setGuid("guid");
        hsaSubsidyActivityDetailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord.setSubsidySendState(0);
        hsaSubsidyActivityRecord.setSubsidyNum(0);
        hsaSubsidyActivityRecord.setSubsidyErrorNum(0);
        final HsaSubsidyActivity hsaSubsidyActivity1 = new HsaSubsidyActivity();
        hsaSubsidyActivity1.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity1.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity1.setActivityState(0);
        hsaSubsidyActivity1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity1.setSubsidyFrequencyType(0);
        hsaSubsidyActivity1.setSubsidyMemberNum(0);
        hsaSubsidyActivity1.setIsRetreat(0);
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("5b2bb102-6c7a-4604-b6bb-1c4f7269c56e");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberAccount("memberAccount");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo1.setAccountState(0);
        final CreationSubsidyDetailRecordDTO recordDTO = new CreationSubsidyDetailRecordDTO();
        recordDTO.setError("【账户】已禁用");
        recordDTO.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recordDTO.setResult(0);
        recordDTO.setOutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recordDTO.setMemberInfoCardGuid("guid");
        recordDTO.setAmount(new BigDecimal("0.00"));
        when(mockSubsidyActivityAssembler.getHsaSubsidyActivityDetailRecord(hsaSubsidyActivityRecord,
                hsaSubsidyActivity1, hsaOperationMemberInfo1, recordDTO, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0))).thenReturn(hsaSubsidyActivityDetailRecord);

        when(mockSystemRoleHelper.getReplace("【会员卡】不存在", "operSubjectGuid")).thenReturn("【账户】已禁用");

        // Configure SubsidyActivityAssembler.getRechargeDetail(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail.setStoreName("storeName");
        hsaMemberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail.setSubsidyDetailRecordGuid("guid");
        final HsaMemberInfoCard memberInfoCard = new HsaMemberInfoCard();
        memberInfoCard.setGuid("guid");
        memberInfoCard.setMemberInfoGuid("memberInfoGuid");
        memberInfoCard.setCardGuid("cardGuid");
        memberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        memberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        memberInfoCard.setCardAmount(new BigDecimal("0.00"));
        memberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessTimes(0);
        memberInfoCard.setCardValidity(0);
        memberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final HsaSubsidyActivity hsaSubsidyActivity2 = new HsaSubsidyActivity();
        hsaSubsidyActivity2.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity2.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity2.setActivityState(0);
        hsaSubsidyActivity2.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity2.setSubsidyFrequencyType(0);
        hsaSubsidyActivity2.setSubsidyMemberNum(0);
        hsaSubsidyActivity2.setIsRetreat(0);
        final HsaMemberFundingDetail memberFundingDetail = new HsaMemberFundingDetail();
        memberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        memberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        memberFundingDetail.setStoreName("storeName");
        memberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        memberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberFundingDetail.setSubsidyDetailRecordGuid("guid");
        when(mockSubsidyActivityAssembler.getRechargeDetail(memberInfoCard, hsaSubsidyActivity2,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), memberFundingDetail)).thenReturn(hsaMemberFundingDetail);

        when(mockHsaOperationMemberInfoMapper.listByOperSubjectGuid("operSubjectGuid"))
                .thenReturn(new HashSet<>(Arrays.asList("value")));

        // Configure HsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(...).
        final CardFreezeBalanceAmountQO cardFreezeBalanceAmountQO = new CardFreezeBalanceAmountQO();
        cardFreezeBalanceAmountQO.setPhysicalCardGuid("physicalCardGuid");
        cardFreezeBalanceAmountQO.setElectronicCardGuid("electronicCardGuid");
        cardFreezeBalanceAmountQO.setMemberInfoGuid("memberInfoGuid");
        cardFreezeBalanceAmountQO.setMemberInfoCardGuid("guid");
        cardFreezeBalanceAmountQO.setIsRefresh(0);
        final List<CardFreezeBalanceAmountQO> cardList = Arrays.asList(cardFreezeBalanceAmountQO);
        when(mockHsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(cardList)).thenReturn(new HashMap<>());

        // Configure HsaCardBaseInfoMapper.queryByGuids(...).
        final HsaCardBaseInfo hsaCardBaseInfo1 = new HsaCardBaseInfo();
        hsaCardBaseInfo1.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo1.setCardName("cardName");
        hsaCardBaseInfo1.setCardImage("cardImage");
        hsaCardBaseInfo1.setCardStatus(0);
        hsaCardBaseInfo1.setIsExcess(0);
        hsaCardBaseInfo1.setExcessType(0);
        hsaCardBaseInfo1.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos1 = Arrays.asList(hsaCardBaseInfo1);
        when(mockHsaCardBaseInfoMapper.queryByGuids(Arrays.asList("value"))).thenReturn(hsaCardBaseInfos1);

        // Configure HsaOperationMemberInfoMapper.listMemberInfo(...).
        final MemberInfoVO memberInfoVO = new MemberInfoVO();
        memberInfoVO.setOperSubjectGuid("operSubjectGuid");
        memberInfoVO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVO.setAccountState(0);
        memberInfoVO.setMemberGuid("memberGuid");
        memberInfoVO.setPhoneNum("phone");
        final List<MemberInfoVO> memberInfoVOS = Arrays.asList(memberInfoVO);
        final MemberListQO request = new MemberListQO();
        request.setOperSubjectGuid("operSubjectGuid");
        request.setIsDisable(0);
        request.setMemberGuids(Arrays.asList("value"));
        request.setKeywords("keywords");
        request.setSex(0);
        when(mockHsaOperationMemberInfoMapper.listMemberInfo(request)).thenReturn(memberInfoVOS);

        // Run the test
        hsaSubsidyActivityServiceImplUnderTest.sendSubsidyRights(event, 0);

        // Verify the results
        verify(mockTransactionUtil).transactional(any(Consumer.class));

        // Confirm HsaSubsidyActivityDetailRecordService.saveBatch(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord1 = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord1.setGuid("guid");
        hsaSubsidyActivityDetailRecord1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord1.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord1.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord1.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord1.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord1.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> entityList = Arrays.asList(hsaSubsidyActivityDetailRecord1);
        verify(mockHsaSubsidyActivityDetailRecordService).saveBatch(entityList);

        // Confirm HsaSubsidyActivityRecordMapper.batchUpdateStatus(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord1 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord1.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord1.setSubsidySendState(0);
        hsaSubsidyActivityRecord1.setSubsidyNum(0);
        hsaSubsidyActivityRecord1.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> activityList = Arrays.asList(hsaSubsidyActivityRecord1);
        verify(mockHsaSubsidyActivityRecordMapper).batchUpdateStatus(activityList);

        // Confirm HsaSubsidyActivityMapper.batchUpdateStatus(...).
        final HsaSubsidyActivity hsaSubsidyActivity3 = new HsaSubsidyActivity();
        hsaSubsidyActivity3.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity3.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity3.setActivityState(0);
        hsaSubsidyActivity3.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity3.setSubsidyFrequencyType(0);
        hsaSubsidyActivity3.setSubsidyMemberNum(0);
        hsaSubsidyActivity3.setIsRetreat(0);
        final List<HsaSubsidyActivity> activityList1 = Arrays.asList(hsaSubsidyActivity3);
        verify(mockHsaSubsidyActivityMapper).batchUpdateStatus(activityList1);

        // Confirm HsaMemberInfoCardMapper.batchUpdate(...).
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardValidity(0);
        hsaMemberInfoCard1.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> activityList2 = Arrays.asList(hsaMemberInfoCard1);
        verify(mockHsaMemberInfoCardMapper).batchUpdate(activityList2);

        // Confirm HsaMemberFundingDetailService.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail1 = new HsaMemberFundingDetail();
        hsaMemberFundingDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail1.setStoreName("storeName");
        hsaMemberFundingDetail1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail1.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail1.setSubsidyDetailRecordGuid("guid");
        final List<HsaMemberFundingDetail> entityList1 = Arrays.asList(hsaMemberFundingDetail1);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList1);

        // Confirm MemberBaseFeign.wechatMessageSendBatch(...).
        final MessagesSendQO messagesSendQO = new MessagesSendQO();
        messagesSendQO.setTemplateName("msgTitle");
        messagesSendQO.setPrams(new HashMap<>());
        messagesSendQO.setMpParams(new HashMap<>());
        messagesSendQO.setOperSubjectGuid("operSubjectGuid");
        messagesSendQO.setEnterpriseGuid("enterpriseGuid");
        messagesSendQO.setPhone("phone");
        messagesSendQO.setOrderNum("");
        messagesSendQO.setMemberInfoCardGuid("memberInfoCardGuid");
        messagesSendQO.setCardName("cardName");
        messagesSendQO.setCardImage("cardImage");
        final MessagesSendbatchQO messagesSendBatchQuery = new MessagesSendbatchQO(Arrays.asList(messagesSendQO));
        verify(mockMemberBaseFeign).wechatMessageSendBatch(messagesSendBatchQuery);
    }

    @Test
    public void testSendSubsidyRights_HsaCardBaseInfoMapperSelectListReturnsNoItems() {
        // Setup
        final SendSubsidyRightsEvent event = new SendSubsidyRightsEvent();
        final SubsidyActivityEvent subsidyActivityEvent = new SubsidyActivityEvent();
        subsidyActivityEvent.setGuid("dd1459f7-d323-4a1d-8740-71a66b6aaf2d");
        subsidyActivityEvent.setOperSubjectGuid("operSubjectGuid");
        subsidyActivityEvent.setCardGuidJson("cardGuidJson");
        subsidyActivityEvent.setSubsidyFilterType(0);
        subsidyActivityEvent.setMemberInfoGuidJson("memberInfoGuidJson");
        subsidyActivityEvent.setLabelGuidJson("labelGuidJson");
        subsidyActivityEvent.setRecordGuid(Arrays.asList("value"));
        event.setSubsidyActivityEventList(Arrays.asList(subsidyActivityEvent));
        event.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure HsaSubsidyActivityMapper.selectList(...).
        final HsaSubsidyActivity hsaSubsidyActivity = new HsaSubsidyActivity();
        hsaSubsidyActivity.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity.setActivityState(0);
        hsaSubsidyActivity.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity.setSubsidyFrequencyType(0);
        hsaSubsidyActivity.setSubsidyMemberNum(0);
        hsaSubsidyActivity.setIsRetreat(0);
        final List<HsaSubsidyActivity> hsaSubsidyActivities = Arrays.asList(hsaSubsidyActivity);
        when(mockHsaSubsidyActivityMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaSubsidyActivities);

        // Configure HsaSubsidyActivityRecordMapper.selectList(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord.setSubsidySendState(0);
        hsaSubsidyActivityRecord.setSubsidyNum(0);
        hsaSubsidyActivityRecord.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords = Arrays.asList(hsaSubsidyActivityRecord);
        when(mockHsaSubsidyActivityRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityRecords);

        when(mockHsaCardBaseInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f3a2c274-c46f-412f-b49b-d70eb56f71aa");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("operationMemberInfoGuid");
        hsaMemberLabel.setOperatorGuid("operatorGuid");
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        // Configure HsaOperationMemberInfoMapper.selectList(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("5b2bb102-6c7a-4604-b6bb-1c4f7269c56e");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setAccountState(0);
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo);
        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperationMemberInfos);

        // Configure HsaMemberInfoCardMapper.selectList(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardValidity(0);
        hsaMemberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> hsaMemberInfoCards = Arrays.asList(hsaMemberInfoCard);
        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberInfoCards);

        // Configure SubsidyActivityAssembler.getHsaSubsidyActivityDetailRecord(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord.setGuid("guid");
        hsaSubsidyActivityDetailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord1 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord1.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord1.setSubsidySendState(0);
        hsaSubsidyActivityRecord1.setSubsidyNum(0);
        hsaSubsidyActivityRecord1.setSubsidyErrorNum(0);
        final HsaSubsidyActivity hsaSubsidyActivity1 = new HsaSubsidyActivity();
        hsaSubsidyActivity1.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity1.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity1.setActivityState(0);
        hsaSubsidyActivity1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity1.setSubsidyFrequencyType(0);
        hsaSubsidyActivity1.setSubsidyMemberNum(0);
        hsaSubsidyActivity1.setIsRetreat(0);
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("5b2bb102-6c7a-4604-b6bb-1c4f7269c56e");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberAccount("memberAccount");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo1.setAccountState(0);
        final CreationSubsidyDetailRecordDTO recordDTO = new CreationSubsidyDetailRecordDTO();
        recordDTO.setError("【账户】已禁用");
        recordDTO.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recordDTO.setResult(0);
        recordDTO.setOutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recordDTO.setMemberInfoCardGuid("guid");
        recordDTO.setAmount(new BigDecimal("0.00"));
        when(mockSubsidyActivityAssembler.getHsaSubsidyActivityDetailRecord(hsaSubsidyActivityRecord1,
                hsaSubsidyActivity1, hsaOperationMemberInfo1, recordDTO, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0))).thenReturn(hsaSubsidyActivityDetailRecord);

        when(mockSystemRoleHelper.getReplace("【会员卡】不存在", "operSubjectGuid")).thenReturn("【账户】已禁用");

        // Configure SubsidyActivityAssembler.getRechargeDetail(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail.setStoreName("storeName");
        hsaMemberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail.setSubsidyDetailRecordGuid("guid");
        final HsaMemberInfoCard memberInfoCard = new HsaMemberInfoCard();
        memberInfoCard.setGuid("guid");
        memberInfoCard.setMemberInfoGuid("memberInfoGuid");
        memberInfoCard.setCardGuid("cardGuid");
        memberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        memberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        memberInfoCard.setCardAmount(new BigDecimal("0.00"));
        memberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessTimes(0);
        memberInfoCard.setCardValidity(0);
        memberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final HsaSubsidyActivity hsaSubsidyActivity2 = new HsaSubsidyActivity();
        hsaSubsidyActivity2.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity2.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity2.setActivityState(0);
        hsaSubsidyActivity2.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity2.setSubsidyFrequencyType(0);
        hsaSubsidyActivity2.setSubsidyMemberNum(0);
        hsaSubsidyActivity2.setIsRetreat(0);
        final HsaMemberFundingDetail memberFundingDetail = new HsaMemberFundingDetail();
        memberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        memberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        memberFundingDetail.setStoreName("storeName");
        memberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        memberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberFundingDetail.setSubsidyDetailRecordGuid("guid");
        when(mockSubsidyActivityAssembler.getRechargeDetail(memberInfoCard, hsaSubsidyActivity2,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), memberFundingDetail)).thenReturn(hsaMemberFundingDetail);

        when(mockHsaOperationMemberInfoMapper.listByOperSubjectGuid("operSubjectGuid"))
                .thenReturn(new HashSet<>(Arrays.asList("value")));

        // Configure HsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(...).
        final CardFreezeBalanceAmountQO cardFreezeBalanceAmountQO = new CardFreezeBalanceAmountQO();
        cardFreezeBalanceAmountQO.setPhysicalCardGuid("physicalCardGuid");
        cardFreezeBalanceAmountQO.setElectronicCardGuid("electronicCardGuid");
        cardFreezeBalanceAmountQO.setMemberInfoGuid("memberInfoGuid");
        cardFreezeBalanceAmountQO.setMemberInfoCardGuid("guid");
        cardFreezeBalanceAmountQO.setIsRefresh(0);
        final List<CardFreezeBalanceAmountQO> cardList = Arrays.asList(cardFreezeBalanceAmountQO);
        when(mockHsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(cardList)).thenReturn(new HashMap<>());

        // Configure HsaCardBaseInfoMapper.queryByGuids(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo.setCardName("cardName");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardStatus(0);
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.queryByGuids(Arrays.asList("value"))).thenReturn(hsaCardBaseInfos);

        // Configure HsaOperationMemberInfoMapper.listMemberInfo(...).
        final MemberInfoVO memberInfoVO = new MemberInfoVO();
        memberInfoVO.setOperSubjectGuid("operSubjectGuid");
        memberInfoVO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVO.setAccountState(0);
        memberInfoVO.setMemberGuid("memberGuid");
        memberInfoVO.setPhoneNum("phone");
        final List<MemberInfoVO> memberInfoVOS = Arrays.asList(memberInfoVO);
        final MemberListQO request = new MemberListQO();
        request.setOperSubjectGuid("operSubjectGuid");
        request.setIsDisable(0);
        request.setMemberGuids(Arrays.asList("value"));
        request.setKeywords("keywords");
        request.setSex(0);
        when(mockHsaOperationMemberInfoMapper.listMemberInfo(request)).thenReturn(memberInfoVOS);

        // Run the test
        hsaSubsidyActivityServiceImplUnderTest.sendSubsidyRights(event, 0);

        // Verify the results
        verify(mockTransactionUtil).transactional(any(Consumer.class));

        // Confirm HsaSubsidyActivityDetailRecordService.saveBatch(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord1 = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord1.setGuid("guid");
        hsaSubsidyActivityDetailRecord1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord1.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord1.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord1.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord1.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord1.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> entityList = Arrays.asList(hsaSubsidyActivityDetailRecord1);
        verify(mockHsaSubsidyActivityDetailRecordService).saveBatch(entityList);

        // Confirm HsaSubsidyActivityRecordMapper.batchUpdateStatus(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord2 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord2.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord2.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord2.setSubsidySendState(0);
        hsaSubsidyActivityRecord2.setSubsidyNum(0);
        hsaSubsidyActivityRecord2.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> activityList = Arrays.asList(hsaSubsidyActivityRecord2);
        verify(mockHsaSubsidyActivityRecordMapper).batchUpdateStatus(activityList);

        // Confirm HsaSubsidyActivityMapper.batchUpdateStatus(...).
        final HsaSubsidyActivity hsaSubsidyActivity3 = new HsaSubsidyActivity();
        hsaSubsidyActivity3.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity3.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity3.setActivityState(0);
        hsaSubsidyActivity3.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity3.setSubsidyFrequencyType(0);
        hsaSubsidyActivity3.setSubsidyMemberNum(0);
        hsaSubsidyActivity3.setIsRetreat(0);
        final List<HsaSubsidyActivity> activityList1 = Arrays.asList(hsaSubsidyActivity3);
        verify(mockHsaSubsidyActivityMapper).batchUpdateStatus(activityList1);

        // Confirm HsaMemberInfoCardMapper.batchUpdate(...).
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardValidity(0);
        hsaMemberInfoCard1.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> activityList2 = Arrays.asList(hsaMemberInfoCard1);
        verify(mockHsaMemberInfoCardMapper).batchUpdate(activityList2);

        // Confirm HsaMemberFundingDetailService.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail1 = new HsaMemberFundingDetail();
        hsaMemberFundingDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail1.setStoreName("storeName");
        hsaMemberFundingDetail1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail1.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail1.setSubsidyDetailRecordGuid("guid");
        final List<HsaMemberFundingDetail> entityList1 = Arrays.asList(hsaMemberFundingDetail1);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList1);

        // Confirm MemberBaseFeign.wechatMessageSendBatch(...).
        final MessagesSendQO messagesSendQO = new MessagesSendQO();
        messagesSendQO.setTemplateName("msgTitle");
        messagesSendQO.setPrams(new HashMap<>());
        messagesSendQO.setMpParams(new HashMap<>());
        messagesSendQO.setOperSubjectGuid("operSubjectGuid");
        messagesSendQO.setEnterpriseGuid("enterpriseGuid");
        messagesSendQO.setPhone("phone");
        messagesSendQO.setOrderNum("");
        messagesSendQO.setMemberInfoCardGuid("memberInfoCardGuid");
        messagesSendQO.setCardName("cardName");
        messagesSendQO.setCardImage("cardImage");
        final MessagesSendbatchQO messagesSendBatchQuery = new MessagesSendbatchQO(Arrays.asList(messagesSendQO));
        verify(mockMemberBaseFeign).wechatMessageSendBatch(messagesSendBatchQuery);
    }

    @Test
    public void testSendSubsidyRights_HsaMemberLabelMapperReturnsNoItems() {
        // Setup
        final SendSubsidyRightsEvent event = new SendSubsidyRightsEvent();
        final SubsidyActivityEvent subsidyActivityEvent = new SubsidyActivityEvent();
        subsidyActivityEvent.setGuid("dd1459f7-d323-4a1d-8740-71a66b6aaf2d");
        subsidyActivityEvent.setOperSubjectGuid("operSubjectGuid");
        subsidyActivityEvent.setCardGuidJson("cardGuidJson");
        subsidyActivityEvent.setSubsidyFilterType(0);
        subsidyActivityEvent.setMemberInfoGuidJson("memberInfoGuidJson");
        subsidyActivityEvent.setLabelGuidJson("labelGuidJson");
        subsidyActivityEvent.setRecordGuid(Arrays.asList("value"));
        event.setSubsidyActivityEventList(Arrays.asList(subsidyActivityEvent));
        event.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure HsaSubsidyActivityMapper.selectList(...).
        final HsaSubsidyActivity hsaSubsidyActivity = new HsaSubsidyActivity();
        hsaSubsidyActivity.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity.setActivityState(0);
        hsaSubsidyActivity.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity.setSubsidyFrequencyType(0);
        hsaSubsidyActivity.setSubsidyMemberNum(0);
        hsaSubsidyActivity.setIsRetreat(0);
        final List<HsaSubsidyActivity> hsaSubsidyActivities = Arrays.asList(hsaSubsidyActivity);
        when(mockHsaSubsidyActivityMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaSubsidyActivities);

        // Configure HsaSubsidyActivityRecordMapper.selectList(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord.setSubsidySendState(0);
        hsaSubsidyActivityRecord.setSubsidyNum(0);
        hsaSubsidyActivityRecord.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords = Arrays.asList(hsaSubsidyActivityRecord);
        when(mockHsaSubsidyActivityRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityRecords);

        // Configure HsaCardBaseInfoMapper.selectList(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo.setCardName("cardName");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardStatus(0);
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCardBaseInfos);

        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure HsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(...).
        final CardFreezeBalanceAmountQO cardFreezeBalanceAmountQO = new CardFreezeBalanceAmountQO();
        cardFreezeBalanceAmountQO.setPhysicalCardGuid("physicalCardGuid");
        cardFreezeBalanceAmountQO.setElectronicCardGuid("electronicCardGuid");
        cardFreezeBalanceAmountQO.setMemberInfoGuid("memberInfoGuid");
        cardFreezeBalanceAmountQO.setMemberInfoCardGuid("guid");
        cardFreezeBalanceAmountQO.setIsRefresh(0);
        final List<CardFreezeBalanceAmountQO> cardList = Arrays.asList(cardFreezeBalanceAmountQO);
        when(mockHsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(cardList)).thenReturn(new HashMap<>());

        // Configure HsaCardBaseInfoMapper.queryByGuids(...).
        final HsaCardBaseInfo hsaCardBaseInfo1 = new HsaCardBaseInfo();
        hsaCardBaseInfo1.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo1.setCardName("cardName");
        hsaCardBaseInfo1.setCardImage("cardImage");
        hsaCardBaseInfo1.setCardStatus(0);
        hsaCardBaseInfo1.setIsExcess(0);
        hsaCardBaseInfo1.setExcessType(0);
        hsaCardBaseInfo1.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos1 = Arrays.asList(hsaCardBaseInfo1);
        when(mockHsaCardBaseInfoMapper.queryByGuids(Arrays.asList("value"))).thenReturn(hsaCardBaseInfos1);

        // Configure HsaOperationMemberInfoMapper.listMemberInfo(...).
        final MemberInfoVO memberInfoVO = new MemberInfoVO();
        memberInfoVO.setOperSubjectGuid("operSubjectGuid");
        memberInfoVO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVO.setAccountState(0);
        memberInfoVO.setMemberGuid("memberGuid");
        memberInfoVO.setPhoneNum("phone");
        final List<MemberInfoVO> memberInfoVOS = Arrays.asList(memberInfoVO);
        final MemberListQO request = new MemberListQO();
        request.setOperSubjectGuid("operSubjectGuid");
        request.setIsDisable(0);
        request.setMemberGuids(Arrays.asList("value"));
        request.setKeywords("keywords");
        request.setSex(0);
        when(mockHsaOperationMemberInfoMapper.listMemberInfo(request)).thenReturn(memberInfoVOS);

        // Run the test
        hsaSubsidyActivityServiceImplUnderTest.sendSubsidyRights(event, 0);

        // Verify the results
        verify(mockTransactionUtil).transactional(any(Consumer.class));

        // Confirm HsaSubsidyActivityDetailRecordService.saveBatch(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord.setGuid("guid");
        hsaSubsidyActivityDetailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> entityList = Arrays.asList(hsaSubsidyActivityDetailRecord);
        verify(mockHsaSubsidyActivityDetailRecordService).saveBatch(entityList);

        // Confirm HsaSubsidyActivityRecordMapper.batchUpdateStatus(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord1 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord1.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord1.setSubsidySendState(0);
        hsaSubsidyActivityRecord1.setSubsidyNum(0);
        hsaSubsidyActivityRecord1.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> activityList = Arrays.asList(hsaSubsidyActivityRecord1);
        verify(mockHsaSubsidyActivityRecordMapper).batchUpdateStatus(activityList);

        // Confirm HsaSubsidyActivityMapper.batchUpdateStatus(...).
        final HsaSubsidyActivity hsaSubsidyActivity1 = new HsaSubsidyActivity();
        hsaSubsidyActivity1.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity1.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity1.setActivityState(0);
        hsaSubsidyActivity1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity1.setSubsidyFrequencyType(0);
        hsaSubsidyActivity1.setSubsidyMemberNum(0);
        hsaSubsidyActivity1.setIsRetreat(0);
        final List<HsaSubsidyActivity> activityList1 = Arrays.asList(hsaSubsidyActivity1);
        verify(mockHsaSubsidyActivityMapper).batchUpdateStatus(activityList1);

        // Confirm HsaMemberInfoCardMapper.batchUpdate(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardValidity(0);
        hsaMemberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> activityList2 = Arrays.asList(hsaMemberInfoCard);
        verify(mockHsaMemberInfoCardMapper).batchUpdate(activityList2);

        // Confirm HsaMemberFundingDetailService.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail.setStoreName("storeName");
        hsaMemberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail.setSubsidyDetailRecordGuid("guid");
        final List<HsaMemberFundingDetail> entityList1 = Arrays.asList(hsaMemberFundingDetail);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList1);

        // Confirm MemberBaseFeign.wechatMessageSendBatch(...).
        final MessagesSendQO messagesSendQO = new MessagesSendQO();
        messagesSendQO.setTemplateName("msgTitle");
        messagesSendQO.setPrams(new HashMap<>());
        messagesSendQO.setMpParams(new HashMap<>());
        messagesSendQO.setOperSubjectGuid("operSubjectGuid");
        messagesSendQO.setEnterpriseGuid("enterpriseGuid");
        messagesSendQO.setPhone("phone");
        messagesSendQO.setOrderNum("");
        messagesSendQO.setMemberInfoCardGuid("memberInfoCardGuid");
        messagesSendQO.setCardName("cardName");
        messagesSendQO.setCardImage("cardImage");
        final MessagesSendbatchQO messagesSendBatchQuery = new MessagesSendbatchQO(Arrays.asList(messagesSendQO));
        verify(mockMemberBaseFeign).wechatMessageSendBatch(messagesSendBatchQuery);
    }

    @Test
    public void testSendSubsidyRights_HsaOperationMemberInfoMapperSelectListReturnsNoItems() {
        // Setup
        final SendSubsidyRightsEvent event = new SendSubsidyRightsEvent();
        final SubsidyActivityEvent subsidyActivityEvent = new SubsidyActivityEvent();
        subsidyActivityEvent.setGuid("dd1459f7-d323-4a1d-8740-71a66b6aaf2d");
        subsidyActivityEvent.setOperSubjectGuid("operSubjectGuid");
        subsidyActivityEvent.setCardGuidJson("cardGuidJson");
        subsidyActivityEvent.setSubsidyFilterType(0);
        subsidyActivityEvent.setMemberInfoGuidJson("memberInfoGuidJson");
        subsidyActivityEvent.setLabelGuidJson("labelGuidJson");
        subsidyActivityEvent.setRecordGuid(Arrays.asList("value"));
        event.setSubsidyActivityEventList(Arrays.asList(subsidyActivityEvent));
        event.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure HsaSubsidyActivityMapper.selectList(...).
        final HsaSubsidyActivity hsaSubsidyActivity = new HsaSubsidyActivity();
        hsaSubsidyActivity.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity.setActivityState(0);
        hsaSubsidyActivity.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity.setSubsidyFrequencyType(0);
        hsaSubsidyActivity.setSubsidyMemberNum(0);
        hsaSubsidyActivity.setIsRetreat(0);
        final List<HsaSubsidyActivity> hsaSubsidyActivities = Arrays.asList(hsaSubsidyActivity);
        when(mockHsaSubsidyActivityMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaSubsidyActivities);

        // Configure HsaSubsidyActivityRecordMapper.selectList(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord.setSubsidySendState(0);
        hsaSubsidyActivityRecord.setSubsidyNum(0);
        hsaSubsidyActivityRecord.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords = Arrays.asList(hsaSubsidyActivityRecord);
        when(mockHsaSubsidyActivityRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityRecords);

        // Configure HsaCardBaseInfoMapper.selectList(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo.setCardName("cardName");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardStatus(0);
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCardBaseInfos);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f3a2c274-c46f-412f-b49b-d70eb56f71aa");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("operationMemberInfoGuid");
        hsaMemberLabel.setOperatorGuid("operatorGuid");
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure HsaMemberInfoCardMapper.selectList(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardValidity(0);
        hsaMemberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> hsaMemberInfoCards = Arrays.asList(hsaMemberInfoCard);
        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberInfoCards);

        // Configure SubsidyActivityAssembler.getHsaSubsidyActivityDetailRecord(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord.setGuid("guid");
        hsaSubsidyActivityDetailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord1 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord1.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord1.setSubsidySendState(0);
        hsaSubsidyActivityRecord1.setSubsidyNum(0);
        hsaSubsidyActivityRecord1.setSubsidyErrorNum(0);
        final HsaSubsidyActivity hsaSubsidyActivity1 = new HsaSubsidyActivity();
        hsaSubsidyActivity1.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity1.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity1.setActivityState(0);
        hsaSubsidyActivity1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity1.setSubsidyFrequencyType(0);
        hsaSubsidyActivity1.setSubsidyMemberNum(0);
        hsaSubsidyActivity1.setIsRetreat(0);
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("5b2bb102-6c7a-4604-b6bb-1c4f7269c56e");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setAccountState(0);
        final CreationSubsidyDetailRecordDTO recordDTO = new CreationSubsidyDetailRecordDTO();
        recordDTO.setError("【账户】已禁用");
        recordDTO.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recordDTO.setResult(0);
        recordDTO.setOutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recordDTO.setMemberInfoCardGuid("guid");
        recordDTO.setAmount(new BigDecimal("0.00"));
        when(mockSubsidyActivityAssembler.getHsaSubsidyActivityDetailRecord(hsaSubsidyActivityRecord1,
                hsaSubsidyActivity1, hsaOperationMemberInfo, recordDTO, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0))).thenReturn(hsaSubsidyActivityDetailRecord);

        when(mockSystemRoleHelper.getReplace("【会员卡】不存在", "operSubjectGuid")).thenReturn("【账户】已禁用");

        // Configure SubsidyActivityAssembler.getRechargeDetail(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail.setStoreName("storeName");
        hsaMemberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail.setSubsidyDetailRecordGuid("guid");
        final HsaMemberInfoCard memberInfoCard = new HsaMemberInfoCard();
        memberInfoCard.setGuid("guid");
        memberInfoCard.setMemberInfoGuid("memberInfoGuid");
        memberInfoCard.setCardGuid("cardGuid");
        memberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        memberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        memberInfoCard.setCardAmount(new BigDecimal("0.00"));
        memberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessTimes(0);
        memberInfoCard.setCardValidity(0);
        memberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final HsaSubsidyActivity hsaSubsidyActivity2 = new HsaSubsidyActivity();
        hsaSubsidyActivity2.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity2.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity2.setActivityState(0);
        hsaSubsidyActivity2.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity2.setSubsidyFrequencyType(0);
        hsaSubsidyActivity2.setSubsidyMemberNum(0);
        hsaSubsidyActivity2.setIsRetreat(0);
        final HsaMemberFundingDetail memberFundingDetail = new HsaMemberFundingDetail();
        memberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        memberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        memberFundingDetail.setStoreName("storeName");
        memberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        memberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberFundingDetail.setSubsidyDetailRecordGuid("guid");
        when(mockSubsidyActivityAssembler.getRechargeDetail(memberInfoCard, hsaSubsidyActivity2,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), memberFundingDetail)).thenReturn(hsaMemberFundingDetail);

        // Configure HsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(...).
        final CardFreezeBalanceAmountQO cardFreezeBalanceAmountQO = new CardFreezeBalanceAmountQO();
        cardFreezeBalanceAmountQO.setPhysicalCardGuid("physicalCardGuid");
        cardFreezeBalanceAmountQO.setElectronicCardGuid("electronicCardGuid");
        cardFreezeBalanceAmountQO.setMemberInfoGuid("memberInfoGuid");
        cardFreezeBalanceAmountQO.setMemberInfoCardGuid("guid");
        cardFreezeBalanceAmountQO.setIsRefresh(0);
        final List<CardFreezeBalanceAmountQO> cardList = Arrays.asList(cardFreezeBalanceAmountQO);
        when(mockHsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(cardList)).thenReturn(new HashMap<>());

        // Configure HsaCardBaseInfoMapper.queryByGuids(...).
        final HsaCardBaseInfo hsaCardBaseInfo1 = new HsaCardBaseInfo();
        hsaCardBaseInfo1.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo1.setCardName("cardName");
        hsaCardBaseInfo1.setCardImage("cardImage");
        hsaCardBaseInfo1.setCardStatus(0);
        hsaCardBaseInfo1.setIsExcess(0);
        hsaCardBaseInfo1.setExcessType(0);
        hsaCardBaseInfo1.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos1 = Arrays.asList(hsaCardBaseInfo1);
        when(mockHsaCardBaseInfoMapper.queryByGuids(Arrays.asList("value"))).thenReturn(hsaCardBaseInfos1);

        // Configure HsaOperationMemberInfoMapper.listMemberInfo(...).
        final MemberInfoVO memberInfoVO = new MemberInfoVO();
        memberInfoVO.setOperSubjectGuid("operSubjectGuid");
        memberInfoVO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVO.setAccountState(0);
        memberInfoVO.setMemberGuid("memberGuid");
        memberInfoVO.setPhoneNum("phone");
        final List<MemberInfoVO> memberInfoVOS = Arrays.asList(memberInfoVO);
        final MemberListQO request = new MemberListQO();
        request.setOperSubjectGuid("operSubjectGuid");
        request.setIsDisable(0);
        request.setMemberGuids(Arrays.asList("value"));
        request.setKeywords("keywords");
        request.setSex(0);
        when(mockHsaOperationMemberInfoMapper.listMemberInfo(request)).thenReturn(memberInfoVOS);

        // Run the test
        hsaSubsidyActivityServiceImplUnderTest.sendSubsidyRights(event, 0);

        // Verify the results
        verify(mockTransactionUtil).transactional(any(Consumer.class));

        // Confirm HsaSubsidyActivityDetailRecordService.saveBatch(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord1 = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord1.setGuid("guid");
        hsaSubsidyActivityDetailRecord1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord1.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord1.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord1.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord1.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord1.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> entityList = Arrays.asList(hsaSubsidyActivityDetailRecord1);
        verify(mockHsaSubsidyActivityDetailRecordService).saveBatch(entityList);

        // Confirm HsaSubsidyActivityRecordMapper.batchUpdateStatus(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord2 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord2.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord2.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord2.setSubsidySendState(0);
        hsaSubsidyActivityRecord2.setSubsidyNum(0);
        hsaSubsidyActivityRecord2.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> activityList = Arrays.asList(hsaSubsidyActivityRecord2);
        verify(mockHsaSubsidyActivityRecordMapper).batchUpdateStatus(activityList);

        // Confirm HsaSubsidyActivityMapper.batchUpdateStatus(...).
        final HsaSubsidyActivity hsaSubsidyActivity3 = new HsaSubsidyActivity();
        hsaSubsidyActivity3.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity3.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity3.setActivityState(0);
        hsaSubsidyActivity3.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity3.setSubsidyFrequencyType(0);
        hsaSubsidyActivity3.setSubsidyMemberNum(0);
        hsaSubsidyActivity3.setIsRetreat(0);
        final List<HsaSubsidyActivity> activityList1 = Arrays.asList(hsaSubsidyActivity3);
        verify(mockHsaSubsidyActivityMapper).batchUpdateStatus(activityList1);

        // Confirm HsaMemberInfoCardMapper.batchUpdate(...).
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardValidity(0);
        hsaMemberInfoCard1.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> activityList2 = Arrays.asList(hsaMemberInfoCard1);
        verify(mockHsaMemberInfoCardMapper).batchUpdate(activityList2);

        // Confirm HsaMemberFundingDetailService.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail1 = new HsaMemberFundingDetail();
        hsaMemberFundingDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail1.setStoreName("storeName");
        hsaMemberFundingDetail1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail1.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail1.setSubsidyDetailRecordGuid("guid");
        final List<HsaMemberFundingDetail> entityList1 = Arrays.asList(hsaMemberFundingDetail1);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList1);

        // Confirm MemberBaseFeign.wechatMessageSendBatch(...).
        final MessagesSendQO messagesSendQO = new MessagesSendQO();
        messagesSendQO.setTemplateName("msgTitle");
        messagesSendQO.setPrams(new HashMap<>());
        messagesSendQO.setMpParams(new HashMap<>());
        messagesSendQO.setOperSubjectGuid("operSubjectGuid");
        messagesSendQO.setEnterpriseGuid("enterpriseGuid");
        messagesSendQO.setPhone("phone");
        messagesSendQO.setOrderNum("");
        messagesSendQO.setMemberInfoCardGuid("memberInfoCardGuid");
        messagesSendQO.setCardName("cardName");
        messagesSendQO.setCardImage("cardImage");
        final MessagesSendbatchQO messagesSendBatchQuery = new MessagesSendbatchQO(Arrays.asList(messagesSendQO));
        verify(mockMemberBaseFeign).wechatMessageSendBatch(messagesSendBatchQuery);
    }

    @Test
    public void testSendSubsidyRights_HsaMemberInfoCardMapperSelectListReturnsNoItems() {
        // Setup
        final SendSubsidyRightsEvent event = new SendSubsidyRightsEvent();
        final SubsidyActivityEvent subsidyActivityEvent = new SubsidyActivityEvent();
        subsidyActivityEvent.setGuid("dd1459f7-d323-4a1d-8740-71a66b6aaf2d");
        subsidyActivityEvent.setOperSubjectGuid("operSubjectGuid");
        subsidyActivityEvent.setCardGuidJson("cardGuidJson");
        subsidyActivityEvent.setSubsidyFilterType(0);
        subsidyActivityEvent.setMemberInfoGuidJson("memberInfoGuidJson");
        subsidyActivityEvent.setLabelGuidJson("labelGuidJson");
        subsidyActivityEvent.setRecordGuid(Arrays.asList("value"));
        event.setSubsidyActivityEventList(Arrays.asList(subsidyActivityEvent));
        event.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure HsaSubsidyActivityMapper.selectList(...).
        final HsaSubsidyActivity hsaSubsidyActivity = new HsaSubsidyActivity();
        hsaSubsidyActivity.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity.setActivityState(0);
        hsaSubsidyActivity.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity.setSubsidyFrequencyType(0);
        hsaSubsidyActivity.setSubsidyMemberNum(0);
        hsaSubsidyActivity.setIsRetreat(0);
        final List<HsaSubsidyActivity> hsaSubsidyActivities = Arrays.asList(hsaSubsidyActivity);
        when(mockHsaSubsidyActivityMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaSubsidyActivities);

        // Configure HsaSubsidyActivityRecordMapper.selectList(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord.setSubsidySendState(0);
        hsaSubsidyActivityRecord.setSubsidyNum(0);
        hsaSubsidyActivityRecord.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords = Arrays.asList(hsaSubsidyActivityRecord);
        when(mockHsaSubsidyActivityRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityRecords);

        // Configure HsaCardBaseInfoMapper.selectList(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo.setCardName("cardName");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardStatus(0);
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCardBaseInfos);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f3a2c274-c46f-412f-b49b-d70eb56f71aa");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("operationMemberInfoGuid");
        hsaMemberLabel.setOperatorGuid("operatorGuid");
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        // Configure HsaOperationMemberInfoMapper.selectList(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("5b2bb102-6c7a-4604-b6bb-1c4f7269c56e");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setAccountState(0);
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo);
        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperationMemberInfos);

        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure SubsidyActivityAssembler.getHsaSubsidyActivityDetailRecord(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord.setGuid("guid");
        hsaSubsidyActivityDetailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord1 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord1.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord1.setSubsidySendState(0);
        hsaSubsidyActivityRecord1.setSubsidyNum(0);
        hsaSubsidyActivityRecord1.setSubsidyErrorNum(0);
        final HsaSubsidyActivity hsaSubsidyActivity1 = new HsaSubsidyActivity();
        hsaSubsidyActivity1.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity1.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity1.setActivityState(0);
        hsaSubsidyActivity1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity1.setSubsidyFrequencyType(0);
        hsaSubsidyActivity1.setSubsidyMemberNum(0);
        hsaSubsidyActivity1.setIsRetreat(0);
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("5b2bb102-6c7a-4604-b6bb-1c4f7269c56e");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberAccount("memberAccount");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo1.setAccountState(0);
        final CreationSubsidyDetailRecordDTO recordDTO = new CreationSubsidyDetailRecordDTO();
        recordDTO.setError("【账户】已禁用");
        recordDTO.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recordDTO.setResult(0);
        recordDTO.setOutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recordDTO.setMemberInfoCardGuid("guid");
        recordDTO.setAmount(new BigDecimal("0.00"));
        when(mockSubsidyActivityAssembler.getHsaSubsidyActivityDetailRecord(hsaSubsidyActivityRecord1,
                hsaSubsidyActivity1, hsaOperationMemberInfo1, recordDTO, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0))).thenReturn(hsaSubsidyActivityDetailRecord);

        when(mockSystemRoleHelper.getReplace("【会员卡】不存在", "operSubjectGuid")).thenReturn("【账户】已禁用");

        // Configure SubsidyActivityAssembler.getRechargeDetail(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail.setStoreName("storeName");
        hsaMemberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail.setSubsidyDetailRecordGuid("guid");
        final HsaMemberInfoCard memberInfoCard = new HsaMemberInfoCard();
        memberInfoCard.setGuid("guid");
        memberInfoCard.setMemberInfoGuid("memberInfoGuid");
        memberInfoCard.setCardGuid("cardGuid");
        memberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        memberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        memberInfoCard.setCardAmount(new BigDecimal("0.00"));
        memberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessTimes(0);
        memberInfoCard.setCardValidity(0);
        memberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final HsaSubsidyActivity hsaSubsidyActivity2 = new HsaSubsidyActivity();
        hsaSubsidyActivity2.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity2.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity2.setActivityState(0);
        hsaSubsidyActivity2.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity2.setSubsidyFrequencyType(0);
        hsaSubsidyActivity2.setSubsidyMemberNum(0);
        hsaSubsidyActivity2.setIsRetreat(0);
        final HsaMemberFundingDetail memberFundingDetail = new HsaMemberFundingDetail();
        memberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        memberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        memberFundingDetail.setStoreName("storeName");
        memberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        memberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberFundingDetail.setSubsidyDetailRecordGuid("guid");
        when(mockSubsidyActivityAssembler.getRechargeDetail(memberInfoCard, hsaSubsidyActivity2,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), memberFundingDetail)).thenReturn(hsaMemberFundingDetail);

        // Configure HsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(...).
        final CardFreezeBalanceAmountQO cardFreezeBalanceAmountQO = new CardFreezeBalanceAmountQO();
        cardFreezeBalanceAmountQO.setPhysicalCardGuid("physicalCardGuid");
        cardFreezeBalanceAmountQO.setElectronicCardGuid("electronicCardGuid");
        cardFreezeBalanceAmountQO.setMemberInfoGuid("memberInfoGuid");
        cardFreezeBalanceAmountQO.setMemberInfoCardGuid("guid");
        cardFreezeBalanceAmountQO.setIsRefresh(0);
        final List<CardFreezeBalanceAmountQO> cardList = Arrays.asList(cardFreezeBalanceAmountQO);
        when(mockHsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(cardList)).thenReturn(new HashMap<>());

        // Configure HsaCardBaseInfoMapper.queryByGuids(...).
        final HsaCardBaseInfo hsaCardBaseInfo1 = new HsaCardBaseInfo();
        hsaCardBaseInfo1.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo1.setCardName("cardName");
        hsaCardBaseInfo1.setCardImage("cardImage");
        hsaCardBaseInfo1.setCardStatus(0);
        hsaCardBaseInfo1.setIsExcess(0);
        hsaCardBaseInfo1.setExcessType(0);
        hsaCardBaseInfo1.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos1 = Arrays.asList(hsaCardBaseInfo1);
        when(mockHsaCardBaseInfoMapper.queryByGuids(Arrays.asList("value"))).thenReturn(hsaCardBaseInfos1);

        // Configure HsaOperationMemberInfoMapper.listMemberInfo(...).
        final MemberInfoVO memberInfoVO = new MemberInfoVO();
        memberInfoVO.setOperSubjectGuid("operSubjectGuid");
        memberInfoVO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVO.setAccountState(0);
        memberInfoVO.setMemberGuid("memberGuid");
        memberInfoVO.setPhoneNum("phone");
        final List<MemberInfoVO> memberInfoVOS = Arrays.asList(memberInfoVO);
        final MemberListQO request = new MemberListQO();
        request.setOperSubjectGuid("operSubjectGuid");
        request.setIsDisable(0);
        request.setMemberGuids(Arrays.asList("value"));
        request.setKeywords("keywords");
        request.setSex(0);
        when(mockHsaOperationMemberInfoMapper.listMemberInfo(request)).thenReturn(memberInfoVOS);

        // Run the test
        hsaSubsidyActivityServiceImplUnderTest.sendSubsidyRights(event, 0);

        // Verify the results
        verify(mockTransactionUtil).transactional(any(Consumer.class));

        // Confirm HsaSubsidyActivityDetailRecordService.saveBatch(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord1 = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord1.setGuid("guid");
        hsaSubsidyActivityDetailRecord1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord1.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord1.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord1.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord1.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord1.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> entityList = Arrays.asList(hsaSubsidyActivityDetailRecord1);
        verify(mockHsaSubsidyActivityDetailRecordService).saveBatch(entityList);

        // Confirm HsaSubsidyActivityRecordMapper.batchUpdateStatus(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord2 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord2.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord2.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord2.setSubsidySendState(0);
        hsaSubsidyActivityRecord2.setSubsidyNum(0);
        hsaSubsidyActivityRecord2.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> activityList = Arrays.asList(hsaSubsidyActivityRecord2);
        verify(mockHsaSubsidyActivityRecordMapper).batchUpdateStatus(activityList);

        // Confirm HsaSubsidyActivityMapper.batchUpdateStatus(...).
        final HsaSubsidyActivity hsaSubsidyActivity3 = new HsaSubsidyActivity();
        hsaSubsidyActivity3.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity3.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity3.setActivityState(0);
        hsaSubsidyActivity3.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity3.setSubsidyFrequencyType(0);
        hsaSubsidyActivity3.setSubsidyMemberNum(0);
        hsaSubsidyActivity3.setIsRetreat(0);
        final List<HsaSubsidyActivity> activityList1 = Arrays.asList(hsaSubsidyActivity3);
        verify(mockHsaSubsidyActivityMapper).batchUpdateStatus(activityList1);

        // Confirm HsaMemberInfoCardMapper.batchUpdate(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardValidity(0);
        hsaMemberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> activityList2 = Arrays.asList(hsaMemberInfoCard);
        verify(mockHsaMemberInfoCardMapper).batchUpdate(activityList2);

        // Confirm HsaMemberFundingDetailService.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail1 = new HsaMemberFundingDetail();
        hsaMemberFundingDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail1.setStoreName("storeName");
        hsaMemberFundingDetail1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail1.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail1.setSubsidyDetailRecordGuid("guid");
        final List<HsaMemberFundingDetail> entityList1 = Arrays.asList(hsaMemberFundingDetail1);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList1);

        // Confirm MemberBaseFeign.wechatMessageSendBatch(...).
        final MessagesSendQO messagesSendQO = new MessagesSendQO();
        messagesSendQO.setTemplateName("msgTitle");
        messagesSendQO.setPrams(new HashMap<>());
        messagesSendQO.setMpParams(new HashMap<>());
        messagesSendQO.setOperSubjectGuid("operSubjectGuid");
        messagesSendQO.setEnterpriseGuid("enterpriseGuid");
        messagesSendQO.setPhone("phone");
        messagesSendQO.setOrderNum("");
        messagesSendQO.setMemberInfoCardGuid("memberInfoCardGuid");
        messagesSendQO.setCardName("cardName");
        messagesSendQO.setCardImage("cardImage");
        final MessagesSendbatchQO messagesSendBatchQuery = new MessagesSendbatchQO(Arrays.asList(messagesSendQO));
        verify(mockMemberBaseFeign).wechatMessageSendBatch(messagesSendBatchQuery);
    }

    @Test
    public void testSendSubsidyRights_HsaOperationMemberInfoMapperListByOperSubjectGuidReturnsNoItems() {
        // Setup
        final SendSubsidyRightsEvent event = new SendSubsidyRightsEvent();
        final SubsidyActivityEvent subsidyActivityEvent = new SubsidyActivityEvent();
        subsidyActivityEvent.setGuid("dd1459f7-d323-4a1d-8740-71a66b6aaf2d");
        subsidyActivityEvent.setOperSubjectGuid("operSubjectGuid");
        subsidyActivityEvent.setCardGuidJson("cardGuidJson");
        subsidyActivityEvent.setSubsidyFilterType(0);
        subsidyActivityEvent.setMemberInfoGuidJson("memberInfoGuidJson");
        subsidyActivityEvent.setLabelGuidJson("labelGuidJson");
        subsidyActivityEvent.setRecordGuid(Arrays.asList("value"));
        event.setSubsidyActivityEventList(Arrays.asList(subsidyActivityEvent));
        event.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure HsaSubsidyActivityMapper.selectList(...).
        final HsaSubsidyActivity hsaSubsidyActivity = new HsaSubsidyActivity();
        hsaSubsidyActivity.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity.setActivityState(0);
        hsaSubsidyActivity.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity.setSubsidyFrequencyType(0);
        hsaSubsidyActivity.setSubsidyMemberNum(0);
        hsaSubsidyActivity.setIsRetreat(0);
        final List<HsaSubsidyActivity> hsaSubsidyActivities = Arrays.asList(hsaSubsidyActivity);
        when(mockHsaSubsidyActivityMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaSubsidyActivities);

        // Configure HsaSubsidyActivityRecordMapper.selectList(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord.setSubsidySendState(0);
        hsaSubsidyActivityRecord.setSubsidyNum(0);
        hsaSubsidyActivityRecord.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords = Arrays.asList(hsaSubsidyActivityRecord);
        when(mockHsaSubsidyActivityRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityRecords);

        // Configure HsaCardBaseInfoMapper.selectList(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo.setCardName("cardName");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardStatus(0);
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCardBaseInfos);

        when(mockHsaOperationMemberInfoMapper.listByOperSubjectGuid("operSubjectGuid"))
                .thenReturn(Collections.emptySet());

        // Configure HsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(...).
        final CardFreezeBalanceAmountQO cardFreezeBalanceAmountQO = new CardFreezeBalanceAmountQO();
        cardFreezeBalanceAmountQO.setPhysicalCardGuid("physicalCardGuid");
        cardFreezeBalanceAmountQO.setElectronicCardGuid("electronicCardGuid");
        cardFreezeBalanceAmountQO.setMemberInfoGuid("memberInfoGuid");
        cardFreezeBalanceAmountQO.setMemberInfoCardGuid("guid");
        cardFreezeBalanceAmountQO.setIsRefresh(0);
        final List<CardFreezeBalanceAmountQO> cardList = Arrays.asList(cardFreezeBalanceAmountQO);
        when(mockHsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(cardList)).thenReturn(new HashMap<>());

        // Configure HsaCardBaseInfoMapper.queryByGuids(...).
        final HsaCardBaseInfo hsaCardBaseInfo1 = new HsaCardBaseInfo();
        hsaCardBaseInfo1.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo1.setCardName("cardName");
        hsaCardBaseInfo1.setCardImage("cardImage");
        hsaCardBaseInfo1.setCardStatus(0);
        hsaCardBaseInfo1.setIsExcess(0);
        hsaCardBaseInfo1.setExcessType(0);
        hsaCardBaseInfo1.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos1 = Arrays.asList(hsaCardBaseInfo1);
        when(mockHsaCardBaseInfoMapper.queryByGuids(Arrays.asList("value"))).thenReturn(hsaCardBaseInfos1);

        // Configure HsaOperationMemberInfoMapper.listMemberInfo(...).
        final MemberInfoVO memberInfoVO = new MemberInfoVO();
        memberInfoVO.setOperSubjectGuid("operSubjectGuid");
        memberInfoVO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVO.setAccountState(0);
        memberInfoVO.setMemberGuid("memberGuid");
        memberInfoVO.setPhoneNum("phone");
        final List<MemberInfoVO> memberInfoVOS = Arrays.asList(memberInfoVO);
        final MemberListQO request = new MemberListQO();
        request.setOperSubjectGuid("operSubjectGuid");
        request.setIsDisable(0);
        request.setMemberGuids(Arrays.asList("value"));
        request.setKeywords("keywords");
        request.setSex(0);
        when(mockHsaOperationMemberInfoMapper.listMemberInfo(request)).thenReturn(memberInfoVOS);

        // Run the test
        hsaSubsidyActivityServiceImplUnderTest.sendSubsidyRights(event, 0);

        // Verify the results
        verify(mockTransactionUtil).transactional(any(Consumer.class));

        // Confirm HsaSubsidyActivityDetailRecordService.saveBatch(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord.setGuid("guid");
        hsaSubsidyActivityDetailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> entityList = Arrays.asList(hsaSubsidyActivityDetailRecord);
        verify(mockHsaSubsidyActivityDetailRecordService).saveBatch(entityList);

        // Confirm HsaSubsidyActivityRecordMapper.batchUpdateStatus(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord1 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord1.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord1.setSubsidySendState(0);
        hsaSubsidyActivityRecord1.setSubsidyNum(0);
        hsaSubsidyActivityRecord1.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> activityList = Arrays.asList(hsaSubsidyActivityRecord1);
        verify(mockHsaSubsidyActivityRecordMapper).batchUpdateStatus(activityList);

        // Confirm HsaSubsidyActivityMapper.batchUpdateStatus(...).
        final HsaSubsidyActivity hsaSubsidyActivity1 = new HsaSubsidyActivity();
        hsaSubsidyActivity1.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity1.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity1.setActivityState(0);
        hsaSubsidyActivity1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity1.setSubsidyFrequencyType(0);
        hsaSubsidyActivity1.setSubsidyMemberNum(0);
        hsaSubsidyActivity1.setIsRetreat(0);
        final List<HsaSubsidyActivity> activityList1 = Arrays.asList(hsaSubsidyActivity1);
        verify(mockHsaSubsidyActivityMapper).batchUpdateStatus(activityList1);

        // Confirm HsaMemberInfoCardMapper.batchUpdate(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardValidity(0);
        hsaMemberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> activityList2 = Arrays.asList(hsaMemberInfoCard);
        verify(mockHsaMemberInfoCardMapper).batchUpdate(activityList2);

        // Confirm HsaMemberFundingDetailService.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail.setStoreName("storeName");
        hsaMemberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail.setSubsidyDetailRecordGuid("guid");
        final List<HsaMemberFundingDetail> entityList1 = Arrays.asList(hsaMemberFundingDetail);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList1);

        // Confirm MemberBaseFeign.wechatMessageSendBatch(...).
        final MessagesSendQO messagesSendQO = new MessagesSendQO();
        messagesSendQO.setTemplateName("msgTitle");
        messagesSendQO.setPrams(new HashMap<>());
        messagesSendQO.setMpParams(new HashMap<>());
        messagesSendQO.setOperSubjectGuid("operSubjectGuid");
        messagesSendQO.setEnterpriseGuid("enterpriseGuid");
        messagesSendQO.setPhone("phone");
        messagesSendQO.setOrderNum("");
        messagesSendQO.setMemberInfoCardGuid("memberInfoCardGuid");
        messagesSendQO.setCardName("cardName");
        messagesSendQO.setCardImage("cardImage");
        final MessagesSendbatchQO messagesSendBatchQuery = new MessagesSendbatchQO(Arrays.asList(messagesSendQO));
        verify(mockMemberBaseFeign).wechatMessageSendBatch(messagesSendBatchQuery);
    }

    @Test
    public void testSendSubsidyRights_HsaCardBaseInfoMapperQueryByGuidsReturnsNoItems() {
        // Setup
        final SendSubsidyRightsEvent event = new SendSubsidyRightsEvent();
        final SubsidyActivityEvent subsidyActivityEvent = new SubsidyActivityEvent();
        subsidyActivityEvent.setGuid("dd1459f7-d323-4a1d-8740-71a66b6aaf2d");
        subsidyActivityEvent.setOperSubjectGuid("operSubjectGuid");
        subsidyActivityEvent.setCardGuidJson("cardGuidJson");
        subsidyActivityEvent.setSubsidyFilterType(0);
        subsidyActivityEvent.setMemberInfoGuidJson("memberInfoGuidJson");
        subsidyActivityEvent.setLabelGuidJson("labelGuidJson");
        subsidyActivityEvent.setRecordGuid(Arrays.asList("value"));
        event.setSubsidyActivityEventList(Arrays.asList(subsidyActivityEvent));
        event.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure HsaSubsidyActivityMapper.selectList(...).
        final HsaSubsidyActivity hsaSubsidyActivity = new HsaSubsidyActivity();
        hsaSubsidyActivity.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity.setActivityState(0);
        hsaSubsidyActivity.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity.setSubsidyFrequencyType(0);
        hsaSubsidyActivity.setSubsidyMemberNum(0);
        hsaSubsidyActivity.setIsRetreat(0);
        final List<HsaSubsidyActivity> hsaSubsidyActivities = Arrays.asList(hsaSubsidyActivity);
        when(mockHsaSubsidyActivityMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaSubsidyActivities);

        // Configure HsaSubsidyActivityRecordMapper.selectList(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord.setSubsidySendState(0);
        hsaSubsidyActivityRecord.setSubsidyNum(0);
        hsaSubsidyActivityRecord.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords = Arrays.asList(hsaSubsidyActivityRecord);
        when(mockHsaSubsidyActivityRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityRecords);

        // Configure HsaCardBaseInfoMapper.selectList(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo.setCardName("cardName");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardStatus(0);
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCardBaseInfos);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f3a2c274-c46f-412f-b49b-d70eb56f71aa");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("operationMemberInfoGuid");
        hsaMemberLabel.setOperatorGuid("operatorGuid");
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        // Configure HsaOperationMemberInfoMapper.selectList(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("5b2bb102-6c7a-4604-b6bb-1c4f7269c56e");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setAccountState(0);
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo);
        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperationMemberInfos);

        // Configure HsaMemberInfoCardMapper.selectList(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardValidity(0);
        hsaMemberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> hsaMemberInfoCards = Arrays.asList(hsaMemberInfoCard);
        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberInfoCards);

        // Configure SubsidyActivityAssembler.getHsaSubsidyActivityDetailRecord(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord.setGuid("guid");
        hsaSubsidyActivityDetailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord1 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord1.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord1.setSubsidySendState(0);
        hsaSubsidyActivityRecord1.setSubsidyNum(0);
        hsaSubsidyActivityRecord1.setSubsidyErrorNum(0);
        final HsaSubsidyActivity hsaSubsidyActivity1 = new HsaSubsidyActivity();
        hsaSubsidyActivity1.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity1.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity1.setActivityState(0);
        hsaSubsidyActivity1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity1.setSubsidyFrequencyType(0);
        hsaSubsidyActivity1.setSubsidyMemberNum(0);
        hsaSubsidyActivity1.setIsRetreat(0);
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("5b2bb102-6c7a-4604-b6bb-1c4f7269c56e");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberAccount("memberAccount");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo1.setAccountState(0);
        final CreationSubsidyDetailRecordDTO recordDTO = new CreationSubsidyDetailRecordDTO();
        recordDTO.setError("【账户】已禁用");
        recordDTO.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recordDTO.setResult(0);
        recordDTO.setOutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recordDTO.setMemberInfoCardGuid("guid");
        recordDTO.setAmount(new BigDecimal("0.00"));
        when(mockSubsidyActivityAssembler.getHsaSubsidyActivityDetailRecord(hsaSubsidyActivityRecord1,
                hsaSubsidyActivity1, hsaOperationMemberInfo1, recordDTO, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0))).thenReturn(hsaSubsidyActivityDetailRecord);

        // Configure HsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(...).
        final CardFreezeBalanceAmountQO cardFreezeBalanceAmountQO = new CardFreezeBalanceAmountQO();
        cardFreezeBalanceAmountQO.setPhysicalCardGuid("physicalCardGuid");
        cardFreezeBalanceAmountQO.setElectronicCardGuid("electronicCardGuid");
        cardFreezeBalanceAmountQO.setMemberInfoGuid("memberInfoGuid");
        cardFreezeBalanceAmountQO.setMemberInfoCardGuid("guid");
        cardFreezeBalanceAmountQO.setIsRefresh(0);
        final List<CardFreezeBalanceAmountQO> cardList = Arrays.asList(cardFreezeBalanceAmountQO);
        when(mockHsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(cardList)).thenReturn(new HashMap<>());

        when(mockHsaCardBaseInfoMapper.queryByGuids(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure HsaOperationMemberInfoMapper.listMemberInfo(...).
        final MemberInfoVO memberInfoVO = new MemberInfoVO();
        memberInfoVO.setOperSubjectGuid("operSubjectGuid");
        memberInfoVO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVO.setAccountState(0);
        memberInfoVO.setMemberGuid("memberGuid");
        memberInfoVO.setPhoneNum("phone");
        final List<MemberInfoVO> memberInfoVOS = Arrays.asList(memberInfoVO);
        final MemberListQO request = new MemberListQO();
        request.setOperSubjectGuid("operSubjectGuid");
        request.setIsDisable(0);
        request.setMemberGuids(Arrays.asList("value"));
        request.setKeywords("keywords");
        request.setSex(0);
        when(mockHsaOperationMemberInfoMapper.listMemberInfo(request)).thenReturn(memberInfoVOS);

        // Run the test
        hsaSubsidyActivityServiceImplUnderTest.sendSubsidyRights(event, 0);

        // Verify the results
        verify(mockTransactionUtil).transactional(any(Consumer.class));

        // Confirm HsaSubsidyActivityDetailRecordService.saveBatch(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord1 = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord1.setGuid("guid");
        hsaSubsidyActivityDetailRecord1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord1.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord1.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord1.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord1.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord1.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> entityList = Arrays.asList(hsaSubsidyActivityDetailRecord1);
        verify(mockHsaSubsidyActivityDetailRecordService).saveBatch(entityList);

        // Confirm HsaSubsidyActivityRecordMapper.batchUpdateStatus(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord2 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord2.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord2.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord2.setSubsidySendState(0);
        hsaSubsidyActivityRecord2.setSubsidyNum(0);
        hsaSubsidyActivityRecord2.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> activityList = Arrays.asList(hsaSubsidyActivityRecord2);
        verify(mockHsaSubsidyActivityRecordMapper).batchUpdateStatus(activityList);

        // Confirm HsaSubsidyActivityMapper.batchUpdateStatus(...).
        final HsaSubsidyActivity hsaSubsidyActivity2 = new HsaSubsidyActivity();
        hsaSubsidyActivity2.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity2.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity2.setActivityState(0);
        hsaSubsidyActivity2.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity2.setSubsidyFrequencyType(0);
        hsaSubsidyActivity2.setSubsidyMemberNum(0);
        hsaSubsidyActivity2.setIsRetreat(0);
        final List<HsaSubsidyActivity> activityList1 = Arrays.asList(hsaSubsidyActivity2);
        verify(mockHsaSubsidyActivityMapper).batchUpdateStatus(activityList1);

        // Confirm HsaMemberInfoCardMapper.batchUpdate(...).
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardValidity(0);
        hsaMemberInfoCard1.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> activityList2 = Arrays.asList(hsaMemberInfoCard1);
        verify(mockHsaMemberInfoCardMapper).batchUpdate(activityList2);

        // Confirm HsaMemberFundingDetailService.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail.setStoreName("storeName");
        hsaMemberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail.setSubsidyDetailRecordGuid("guid");
        final List<HsaMemberFundingDetail> entityList1 = Arrays.asList(hsaMemberFundingDetail);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList1);

        // Confirm MemberBaseFeign.wechatMessageSendBatch(...).
        final MessagesSendQO messagesSendQO = new MessagesSendQO();
        messagesSendQO.setTemplateName("msgTitle");
        messagesSendQO.setPrams(new HashMap<>());
        messagesSendQO.setMpParams(new HashMap<>());
        messagesSendQO.setOperSubjectGuid("operSubjectGuid");
        messagesSendQO.setEnterpriseGuid("enterpriseGuid");
        messagesSendQO.setPhone("phone");
        messagesSendQO.setOrderNum("");
        messagesSendQO.setMemberInfoCardGuid("memberInfoCardGuid");
        messagesSendQO.setCardName("cardName");
        messagesSendQO.setCardImage("cardImage");
        final MessagesSendbatchQO messagesSendBatchQuery = new MessagesSendbatchQO(Arrays.asList(messagesSendQO));
        verify(mockMemberBaseFeign).wechatMessageSendBatch(messagesSendBatchQuery);
    }

    @Test
    public void testSendSubsidyRights_HsaOperationMemberInfoMapperListMemberInfoReturnsNoItems() {
        // Setup
        final SendSubsidyRightsEvent event = new SendSubsidyRightsEvent();
        final SubsidyActivityEvent subsidyActivityEvent = new SubsidyActivityEvent();
        subsidyActivityEvent.setGuid("dd1459f7-d323-4a1d-8740-71a66b6aaf2d");
        subsidyActivityEvent.setOperSubjectGuid("operSubjectGuid");
        subsidyActivityEvent.setCardGuidJson("cardGuidJson");
        subsidyActivityEvent.setSubsidyFilterType(0);
        subsidyActivityEvent.setMemberInfoGuidJson("memberInfoGuidJson");
        subsidyActivityEvent.setLabelGuidJson("labelGuidJson");
        subsidyActivityEvent.setRecordGuid(Arrays.asList("value"));
        event.setSubsidyActivityEventList(Arrays.asList(subsidyActivityEvent));
        event.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure HsaSubsidyActivityMapper.selectList(...).
        final HsaSubsidyActivity hsaSubsidyActivity = new HsaSubsidyActivity();
        hsaSubsidyActivity.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity.setActivityState(0);
        hsaSubsidyActivity.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity.setSubsidyFrequencyType(0);
        hsaSubsidyActivity.setSubsidyMemberNum(0);
        hsaSubsidyActivity.setIsRetreat(0);
        final List<HsaSubsidyActivity> hsaSubsidyActivities = Arrays.asList(hsaSubsidyActivity);
        when(mockHsaSubsidyActivityMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaSubsidyActivities);

        // Configure HsaSubsidyActivityRecordMapper.selectList(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord.setSubsidySendState(0);
        hsaSubsidyActivityRecord.setSubsidyNum(0);
        hsaSubsidyActivityRecord.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords = Arrays.asList(hsaSubsidyActivityRecord);
        when(mockHsaSubsidyActivityRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityRecords);

        // Configure HsaCardBaseInfoMapper.selectList(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo.setCardName("cardName");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardStatus(0);
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCardBaseInfos);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("f3a2c274-c46f-412f-b49b-d70eb56f71aa");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("operationMemberInfoGuid");
        hsaMemberLabel.setOperatorGuid("operatorGuid");
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        // Configure HsaOperationMemberInfoMapper.selectList(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("5b2bb102-6c7a-4604-b6bb-1c4f7269c56e");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setAccountState(0);
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo);
        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperationMemberInfos);

        // Configure HsaMemberInfoCardMapper.selectList(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardValidity(0);
        hsaMemberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> hsaMemberInfoCards = Arrays.asList(hsaMemberInfoCard);
        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberInfoCards);

        // Configure SubsidyActivityAssembler.getHsaSubsidyActivityDetailRecord(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord.setGuid("guid");
        hsaSubsidyActivityDetailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord1 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord1.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord1.setSubsidySendState(0);
        hsaSubsidyActivityRecord1.setSubsidyNum(0);
        hsaSubsidyActivityRecord1.setSubsidyErrorNum(0);
        final HsaSubsidyActivity hsaSubsidyActivity1 = new HsaSubsidyActivity();
        hsaSubsidyActivity1.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity1.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity1.setActivityState(0);
        hsaSubsidyActivity1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity1.setSubsidyFrequencyType(0);
        hsaSubsidyActivity1.setSubsidyMemberNum(0);
        hsaSubsidyActivity1.setIsRetreat(0);
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("5b2bb102-6c7a-4604-b6bb-1c4f7269c56e");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberAccount("memberAccount");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo1.setAccountState(0);
        final CreationSubsidyDetailRecordDTO recordDTO = new CreationSubsidyDetailRecordDTO();
        recordDTO.setError("【账户】已禁用");
        recordDTO.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recordDTO.setResult(0);
        recordDTO.setOutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        recordDTO.setMemberInfoCardGuid("guid");
        recordDTO.setAmount(new BigDecimal("0.00"));
        when(mockSubsidyActivityAssembler.getHsaSubsidyActivityDetailRecord(hsaSubsidyActivityRecord1,
                hsaSubsidyActivity1, hsaOperationMemberInfo1, recordDTO, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0))).thenReturn(hsaSubsidyActivityDetailRecord);

        // Configure HsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(...).
        final CardFreezeBalanceAmountQO cardFreezeBalanceAmountQO = new CardFreezeBalanceAmountQO();
        cardFreezeBalanceAmountQO.setPhysicalCardGuid("physicalCardGuid");
        cardFreezeBalanceAmountQO.setElectronicCardGuid("electronicCardGuid");
        cardFreezeBalanceAmountQO.setMemberInfoGuid("memberInfoGuid");
        cardFreezeBalanceAmountQO.setMemberInfoCardGuid("guid");
        cardFreezeBalanceAmountQO.setIsRefresh(0);
        final List<CardFreezeBalanceAmountQO> cardList = Arrays.asList(cardFreezeBalanceAmountQO);
        when(mockHsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(cardList)).thenReturn(new HashMap<>());

        // Configure HsaCardBaseInfoMapper.queryByGuids(...).
        final HsaCardBaseInfo hsaCardBaseInfo1 = new HsaCardBaseInfo();
        hsaCardBaseInfo1.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo1.setCardName("cardName");
        hsaCardBaseInfo1.setCardImage("cardImage");
        hsaCardBaseInfo1.setCardStatus(0);
        hsaCardBaseInfo1.setIsExcess(0);
        hsaCardBaseInfo1.setExcessType(0);
        hsaCardBaseInfo1.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos1 = Arrays.asList(hsaCardBaseInfo1);
        when(mockHsaCardBaseInfoMapper.queryByGuids(Arrays.asList("value"))).thenReturn(hsaCardBaseInfos1);

        // Configure HsaOperationMemberInfoMapper.listMemberInfo(...).
        final MemberListQO request = new MemberListQO();
        request.setOperSubjectGuid("operSubjectGuid");
        request.setIsDisable(0);
        request.setMemberGuids(Arrays.asList("value"));
        request.setKeywords("keywords");
        request.setSex(0);
        when(mockHsaOperationMemberInfoMapper.listMemberInfo(request)).thenReturn(Collections.emptyList());

        // Run the test
        hsaSubsidyActivityServiceImplUnderTest.sendSubsidyRights(event, 0);

        // Verify the results
        verify(mockTransactionUtil).transactional(any(Consumer.class));

        // Confirm HsaSubsidyActivityDetailRecordService.saveBatch(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord1 = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord1.setGuid("guid");
        hsaSubsidyActivityDetailRecord1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord1.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord1.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord1.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord1.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord1.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> entityList = Arrays.asList(hsaSubsidyActivityDetailRecord1);
        verify(mockHsaSubsidyActivityDetailRecordService).saveBatch(entityList);

        // Confirm HsaSubsidyActivityRecordMapper.batchUpdateStatus(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord2 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord2.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord2.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord2.setSubsidySendState(0);
        hsaSubsidyActivityRecord2.setSubsidyNum(0);
        hsaSubsidyActivityRecord2.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> activityList = Arrays.asList(hsaSubsidyActivityRecord2);
        verify(mockHsaSubsidyActivityRecordMapper).batchUpdateStatus(activityList);

        // Confirm HsaSubsidyActivityMapper.batchUpdateStatus(...).
        final HsaSubsidyActivity hsaSubsidyActivity2 = new HsaSubsidyActivity();
        hsaSubsidyActivity2.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity2.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity2.setActivityState(0);
        hsaSubsidyActivity2.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity2.setSubsidyFrequencyType(0);
        hsaSubsidyActivity2.setSubsidyMemberNum(0);
        hsaSubsidyActivity2.setIsRetreat(0);
        final List<HsaSubsidyActivity> activityList1 = Arrays.asList(hsaSubsidyActivity2);
        verify(mockHsaSubsidyActivityMapper).batchUpdateStatus(activityList1);

        // Confirm HsaMemberInfoCardMapper.batchUpdate(...).
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardValidity(0);
        hsaMemberInfoCard1.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> activityList2 = Arrays.asList(hsaMemberInfoCard1);
        verify(mockHsaMemberInfoCardMapper).batchUpdate(activityList2);

        // Confirm HsaMemberFundingDetailService.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail.setStoreName("storeName");
        hsaMemberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail.setSubsidyDetailRecordGuid("guid");
        final List<HsaMemberFundingDetail> entityList1 = Arrays.asList(hsaMemberFundingDetail);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList1);

        // Confirm MemberBaseFeign.wechatMessageSendBatch(...).
        final MessagesSendQO messagesSendQO = new MessagesSendQO();
        messagesSendQO.setTemplateName("msgTitle");
        messagesSendQO.setPrams(new HashMap<>());
        messagesSendQO.setMpParams(new HashMap<>());
        messagesSendQO.setOperSubjectGuid("operSubjectGuid");
        messagesSendQO.setEnterpriseGuid("enterpriseGuid");
        messagesSendQO.setPhone("phone");
        messagesSendQO.setOrderNum("");
        messagesSendQO.setMemberInfoCardGuid("memberInfoCardGuid");
        messagesSendQO.setCardName("cardName");
        messagesSendQO.setCardImage("cardImage");
        final MessagesSendbatchQO messagesSendBatchQuery = new MessagesSendbatchQO(Arrays.asList(messagesSendQO));
        verify(mockMemberBaseFeign).wechatMessageSendBatch(messagesSendBatchQuery);
    }

    @Test
    public void testGetSendCheckSubsidyDTO() {
        // Setup
        final Map<String, HsaSubsidyActivity> hsaSubsidyActivityMap = new HashMap<>();
        final SubsidyActivityEvent subsidyActivityEvent = new SubsidyActivityEvent();
        subsidyActivityEvent.setGuid("dd1459f7-d323-4a1d-8740-71a66b6aaf2d");
        subsidyActivityEvent.setOperSubjectGuid("operSubjectGuid");
        subsidyActivityEvent.setCardGuidJson("cardGuidJson");
        subsidyActivityEvent.setSubsidyFilterType(0);
        subsidyActivityEvent.setMemberInfoGuidJson("memberInfoGuidJson");
        subsidyActivityEvent.setLabelGuidJson("labelGuidJson");
        subsidyActivityEvent.setRecordGuid(Arrays.asList("value"));

        final SendCheckSubsidyDTO expectedResult = new SendCheckSubsidyDTO();
        expectedResult.setHsaSubsidyActivityMap(new HashMap<>());
        expectedResult.setHsaSubsidyActivityRecordMap(new HashMap<>());
        expectedResult.setHsaCardBaseInfoMap(new HashMap<>());
        final HsaSubsidyActivity hsaSubsidyActivity = new HsaSubsidyActivity();
        hsaSubsidyActivity.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity.setActivityState(0);
        hsaSubsidyActivity.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity.setSubsidyFrequencyType(0);
        hsaSubsidyActivity.setSubsidyMemberNum(0);
        hsaSubsidyActivity.setIsRetreat(0);
        expectedResult.setHsaSubsidyActivity(hsaSubsidyActivity);
        final SubsidyActivityEvent subsidyActivityEvent1 = new SubsidyActivityEvent();
        subsidyActivityEvent1.setGuid("dd1459f7-d323-4a1d-8740-71a66b6aaf2d");
        subsidyActivityEvent1.setOperSubjectGuid("operSubjectGuid");
        subsidyActivityEvent1.setCardGuidJson("cardGuidJson");
        subsidyActivityEvent1.setSubsidyFilterType(0);
        subsidyActivityEvent1.setMemberInfoGuidJson("memberInfoGuidJson");
        subsidyActivityEvent1.setLabelGuidJson("labelGuidJson");
        subsidyActivityEvent1.setRecordGuid(Arrays.asList("value"));
        expectedResult.setSubsidyActivityEvent(subsidyActivityEvent1);
        expectedResult.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure HsaSubsidyActivityRecordMapper.selectList(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord.setSubsidySendState(0);
        hsaSubsidyActivityRecord.setSubsidyNum(0);
        hsaSubsidyActivityRecord.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords = Arrays.asList(hsaSubsidyActivityRecord);
        when(mockHsaSubsidyActivityRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityRecords);

        // Configure HsaCardBaseInfoMapper.selectList(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo.setCardName("cardName");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardStatus(0);
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCardBaseInfos);

        // Run the test
        final SendCheckSubsidyDTO result = hsaSubsidyActivityServiceImplUnderTest.getSendCheckSubsidyDTO(
                hsaSubsidyActivityMap, subsidyActivityEvent);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetSendCheckSubsidyDTO_HsaSubsidyActivityRecordMapperReturnsNoItems() {
        // Setup
        final Map<String, HsaSubsidyActivity> hsaSubsidyActivityMap = new HashMap<>();
        final SubsidyActivityEvent subsidyActivityEvent = new SubsidyActivityEvent();
        subsidyActivityEvent.setGuid("dd1459f7-d323-4a1d-8740-71a66b6aaf2d");
        subsidyActivityEvent.setOperSubjectGuid("operSubjectGuid");
        subsidyActivityEvent.setCardGuidJson("cardGuidJson");
        subsidyActivityEvent.setSubsidyFilterType(0);
        subsidyActivityEvent.setMemberInfoGuidJson("memberInfoGuidJson");
        subsidyActivityEvent.setLabelGuidJson("labelGuidJson");
        subsidyActivityEvent.setRecordGuid(Arrays.asList("value"));

        final SendCheckSubsidyDTO expectedResult = new SendCheckSubsidyDTO();
        expectedResult.setHsaSubsidyActivityMap(new HashMap<>());
        expectedResult.setHsaSubsidyActivityRecordMap(new HashMap<>());
        expectedResult.setHsaCardBaseInfoMap(new HashMap<>());
        final HsaSubsidyActivity hsaSubsidyActivity = new HsaSubsidyActivity();
        hsaSubsidyActivity.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity.setActivityState(0);
        hsaSubsidyActivity.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity.setSubsidyFrequencyType(0);
        hsaSubsidyActivity.setSubsidyMemberNum(0);
        hsaSubsidyActivity.setIsRetreat(0);
        expectedResult.setHsaSubsidyActivity(hsaSubsidyActivity);
        final SubsidyActivityEvent subsidyActivityEvent1 = new SubsidyActivityEvent();
        subsidyActivityEvent1.setGuid("dd1459f7-d323-4a1d-8740-71a66b6aaf2d");
        subsidyActivityEvent1.setOperSubjectGuid("operSubjectGuid");
        subsidyActivityEvent1.setCardGuidJson("cardGuidJson");
        subsidyActivityEvent1.setSubsidyFilterType(0);
        subsidyActivityEvent1.setMemberInfoGuidJson("memberInfoGuidJson");
        subsidyActivityEvent1.setLabelGuidJson("labelGuidJson");
        subsidyActivityEvent1.setRecordGuid(Arrays.asList("value"));
        expectedResult.setSubsidyActivityEvent(subsidyActivityEvent1);
        expectedResult.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockHsaSubsidyActivityRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure HsaCardBaseInfoMapper.selectList(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo.setCardName("cardName");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardStatus(0);
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCardBaseInfos);

        // Run the test
        final SendCheckSubsidyDTO result = hsaSubsidyActivityServiceImplUnderTest.getSendCheckSubsidyDTO(
                hsaSubsidyActivityMap, subsidyActivityEvent);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetSendCheckSubsidyDTO_HsaCardBaseInfoMapperReturnsNoItems() {
        // Setup
        final Map<String, HsaSubsidyActivity> hsaSubsidyActivityMap = new HashMap<>();
        final SubsidyActivityEvent subsidyActivityEvent = new SubsidyActivityEvent();
        subsidyActivityEvent.setGuid("dd1459f7-d323-4a1d-8740-71a66b6aaf2d");
        subsidyActivityEvent.setOperSubjectGuid("operSubjectGuid");
        subsidyActivityEvent.setCardGuidJson("cardGuidJson");
        subsidyActivityEvent.setSubsidyFilterType(0);
        subsidyActivityEvent.setMemberInfoGuidJson("memberInfoGuidJson");
        subsidyActivityEvent.setLabelGuidJson("labelGuidJson");
        subsidyActivityEvent.setRecordGuid(Arrays.asList("value"));

        final SendCheckSubsidyDTO expectedResult = new SendCheckSubsidyDTO();
        expectedResult.setHsaSubsidyActivityMap(new HashMap<>());
        expectedResult.setHsaSubsidyActivityRecordMap(new HashMap<>());
        expectedResult.setHsaCardBaseInfoMap(new HashMap<>());
        final HsaSubsidyActivity hsaSubsidyActivity = new HsaSubsidyActivity();
        hsaSubsidyActivity.setGuid("59b8c5b1-f5b2-4c28-9f0b-3b1b2905eb35");
        hsaSubsidyActivity.setOperSubjectGuid("operSubjectGuid");
        hsaSubsidyActivity.setActivityState(0);
        hsaSubsidyActivity.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivity.setSubsidyFrequencyType(0);
        hsaSubsidyActivity.setSubsidyMemberNum(0);
        hsaSubsidyActivity.setIsRetreat(0);
        expectedResult.setHsaSubsidyActivity(hsaSubsidyActivity);
        final SubsidyActivityEvent subsidyActivityEvent1 = new SubsidyActivityEvent();
        subsidyActivityEvent1.setGuid("dd1459f7-d323-4a1d-8740-71a66b6aaf2d");
        subsidyActivityEvent1.setOperSubjectGuid("operSubjectGuid");
        subsidyActivityEvent1.setCardGuidJson("cardGuidJson");
        subsidyActivityEvent1.setSubsidyFilterType(0);
        subsidyActivityEvent1.setMemberInfoGuidJson("memberInfoGuidJson");
        subsidyActivityEvent1.setLabelGuidJson("labelGuidJson");
        subsidyActivityEvent1.setRecordGuid(Arrays.asList("value"));
        expectedResult.setSubsidyActivityEvent(subsidyActivityEvent1);
        expectedResult.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure HsaSubsidyActivityRecordMapper.selectList(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord.setSubsidySendState(0);
        hsaSubsidyActivityRecord.setSubsidyNum(0);
        hsaSubsidyActivityRecord.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords = Arrays.asList(hsaSubsidyActivityRecord);
        when(mockHsaSubsidyActivityRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityRecords);

        when(mockHsaCardBaseInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final SendCheckSubsidyDTO result = hsaSubsidyActivityServiceImplUnderTest.getSendCheckSubsidyDTO(
                hsaSubsidyActivityMap, subsidyActivityEvent);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCheckMemberInfoCard() {
        // Setup
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardValidity(0);
        hsaMemberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final Set<HsaMemberInfoCard> expectedResult = new HashSet<>(Arrays.asList(hsaMemberInfoCard));

        // Configure HsaMemberInfoCardMapper.selectList(...).
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardValidity(0);
        hsaMemberInfoCard1.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> hsaMemberInfoCards = Arrays.asList(hsaMemberInfoCard1);
        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberInfoCards);

        // Run the test
        final Set<HsaMemberInfoCard> result = hsaSubsidyActivityServiceImplUnderTest.checkMemberInfoCard(
                Arrays.asList("value"), new HashSet<>(Arrays.asList("value")));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCheckMemberInfoCard_HsaMemberInfoCardMapperReturnsNoItems() {
        // Setup
        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Set<HsaMemberInfoCard> result = hsaSubsidyActivityServiceImplUnderTest.checkMemberInfoCard(
                Arrays.asList("value"), new HashSet<>(Arrays.asList("value")));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptySet());
    }

    @Test
    public void testCheckSubsidyActivityDetailRecord() {
        // Setup
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord.setGuid("guid");
        hsaSubsidyActivityDetailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final Set<HsaSubsidyActivityDetailRecord> expectedResult = new HashSet<>(
                Arrays.asList(hsaSubsidyActivityDetailRecord));

        // Configure HsaSubsidyActivityDetailRecordMapper.selectList(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord1 = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord1.setGuid("guid");
        hsaSubsidyActivityDetailRecord1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord1.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord1.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord1.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord1.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord1.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords = Arrays.asList(
                hsaSubsidyActivityDetailRecord1);
        when(mockHsaSubsidyActivityDetailRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityDetailRecords);

        // Run the test
        final Set<HsaSubsidyActivityDetailRecord> result = hsaSubsidyActivityServiceImplUnderTest.checkSubsidyActivityDetailRecord(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCheckSubsidyActivityDetailRecord_HsaSubsidyActivityDetailRecordMapperReturnsNoItems() {
        // Setup
        when(mockHsaSubsidyActivityDetailRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Set<HsaSubsidyActivityDetailRecord> result = hsaSubsidyActivityServiceImplUnderTest.checkSubsidyActivityDetailRecord(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptySet());
    }

    @Test
    public void testCheckMemberGuid() {
        // Setup
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("5b2bb102-6c7a-4604-b6bb-1c4f7269c56e");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setAccountState(0);
        final Set<HsaOperationMemberInfo> expectedResult = new HashSet<>(Arrays.asList(hsaOperationMemberInfo));

        // Configure HsaOperationMemberInfoMapper.selectList(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("5b2bb102-6c7a-4604-b6bb-1c4f7269c56e");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberAccount("memberAccount");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo1.setAccountState(0);
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo1);
        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperationMemberInfos);

        // Run the test
        final Set<HsaOperationMemberInfo> result = hsaSubsidyActivityServiceImplUnderTest.checkMemberGuid(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCheckMemberGuid_HsaOperationMemberInfoMapperReturnsNoItems() {
        // Setup
        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Set<HsaOperationMemberInfo> result = hsaSubsidyActivityServiceImplUnderTest.checkMemberGuid(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptySet());
    }

    @Test
    public void testSendSubsidyBackRights() {
        // Setup
        final SendBackSubsidyRightsEvent event = new SendBackSubsidyRightsEvent();
        event.setSubsidyActivityEventList(Arrays.asList("value"));
        event.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure HsaSubsidyActivityDetailRecordMapper.selectList(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord.setGuid("guid");
        hsaSubsidyActivityDetailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords = Arrays.asList(
                hsaSubsidyActivityDetailRecord);
        when(mockHsaSubsidyActivityDetailRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityDetailRecords);

        // Configure HsaSubsidyActivityRecordMapper.selectList(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord.setSubsidySendState(0);
        hsaSubsidyActivityRecord.setSubsidyNum(0);
        hsaSubsidyActivityRecord.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords = Arrays.asList(hsaSubsidyActivityRecord);
        when(mockHsaSubsidyActivityRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityRecords);

        // Configure HsaMemberInfoCardMapper.selectList(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardValidity(0);
        hsaMemberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> hsaMemberInfoCards = Arrays.asList(hsaMemberInfoCard);
        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberInfoCards);

        // Configure SubsidyActivityAssembler.getHsaMemberFundingDetail(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail.setStoreName("storeName");
        hsaMemberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail.setSubsidyDetailRecordGuid("guid");
        final HsaSubsidyActivityDetailRecord detailRecord = new HsaSubsidyActivityDetailRecord();
        detailRecord.setGuid("guid");
        detailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        detailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        detailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        detailRecord.setIsRetreat(0);
        detailRecord.setCardGuid("cardGuid");
        detailRecord.setMemberInfoGuid("memberInfoGuid");
        detailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        detailRecord.setIsWithdraw(0);
        detailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardValidity(0);
        hsaMemberInfoCard1.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord1 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord1.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord1.setSubsidySendState(0);
        hsaSubsidyActivityRecord1.setSubsidyNum(0);
        hsaSubsidyActivityRecord1.setSubsidyErrorNum(0);
        when(mockSubsidyActivityAssembler.getHsaMemberFundingDetail(detailRecord, hsaMemberInfoCard1,
                hsaSubsidyActivityRecord1)).thenReturn(hsaMemberFundingDetail);

        // Configure HsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(...).
        final CardFreezeBalanceAmountQO cardFreezeBalanceAmountQO = new CardFreezeBalanceAmountQO();
        cardFreezeBalanceAmountQO.setPhysicalCardGuid("physicalCardGuid");
        cardFreezeBalanceAmountQO.setElectronicCardGuid("electronicCardGuid");
        cardFreezeBalanceAmountQO.setMemberInfoGuid("memberInfoGuid");
        cardFreezeBalanceAmountQO.setMemberInfoCardGuid("guid");
        cardFreezeBalanceAmountQO.setIsRefresh(0);
        final List<CardFreezeBalanceAmountQO> cardList = Arrays.asList(cardFreezeBalanceAmountQO);
        when(mockHsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(cardList)).thenReturn(new HashMap<>());

        // Configure HsaCardBaseInfoMapper.queryByGuids(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo.setCardName("cardName");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardStatus(0);
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.queryByGuids(Arrays.asList("value"))).thenReturn(hsaCardBaseInfos);

        // Configure HsaOperationMemberInfoMapper.listMemberInfo(...).
        final MemberInfoVO memberInfoVO = new MemberInfoVO();
        memberInfoVO.setOperSubjectGuid("operSubjectGuid");
        memberInfoVO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVO.setAccountState(0);
        memberInfoVO.setMemberGuid("memberGuid");
        memberInfoVO.setPhoneNum("phone");
        final List<MemberInfoVO> memberInfoVOS = Arrays.asList(memberInfoVO);
        final MemberListQO request = new MemberListQO();
        request.setOperSubjectGuid("operSubjectGuid");
        request.setIsDisable(0);
        request.setMemberGuids(Arrays.asList("value"));
        request.setKeywords("keywords");
        request.setSex(0);
        when(mockHsaOperationMemberInfoMapper.listMemberInfo(request)).thenReturn(memberInfoVOS);

        // Run the test
        hsaSubsidyActivityServiceImplUnderTest.sendSubsidyBackRights(event, 0);

        // Verify the results
        // Confirm HsaSubsidyActivityDetailRecordMapper.batchUpdateStatus(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord1 = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord1.setGuid("guid");
        hsaSubsidyActivityDetailRecord1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord1.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord1.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord1.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord1.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord1.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> activityList = Arrays.asList(hsaSubsidyActivityDetailRecord1);
        verify(mockHsaSubsidyActivityDetailRecordMapper).batchUpdateStatus(activityList);

        // Confirm HsaMemberInfoCardMapper.batchUpdate(...).
        final HsaMemberInfoCard hsaMemberInfoCard2 = new HsaMemberInfoCard();
        hsaMemberInfoCard2.setGuid("guid");
        hsaMemberInfoCard2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard2.setCardGuid("cardGuid");
        hsaMemberInfoCard2.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessTimes(0);
        hsaMemberInfoCard2.setCardValidity(0);
        hsaMemberInfoCard2.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> activityList1 = Arrays.asList(hsaMemberInfoCard2);
        verify(mockHsaMemberInfoCardMapper).batchUpdate(activityList1);

        // Confirm HsaMemberFundingDetailService.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail1 = new HsaMemberFundingDetail();
        hsaMemberFundingDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail1.setStoreName("storeName");
        hsaMemberFundingDetail1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail1.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail1.setSubsidyDetailRecordGuid("guid");
        final List<HsaMemberFundingDetail> entityList = Arrays.asList(hsaMemberFundingDetail1);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList);

        // Confirm MemberBaseFeign.wechatMessageSendBatch(...).
        final MessagesSendQO messagesSendQO = new MessagesSendQO();
        messagesSendQO.setTemplateName("msgTitle");
        messagesSendQO.setPrams(new HashMap<>());
        messagesSendQO.setMpParams(new HashMap<>());
        messagesSendQO.setOperSubjectGuid("operSubjectGuid");
        messagesSendQO.setEnterpriseGuid("enterpriseGuid");
        messagesSendQO.setPhone("phone");
        messagesSendQO.setOrderNum("");
        messagesSendQO.setMemberInfoCardGuid("memberInfoCardGuid");
        messagesSendQO.setCardName("cardName");
        messagesSendQO.setCardImage("cardImage");
        final MessagesSendbatchQO messagesSendBatchQuery = new MessagesSendbatchQO(Arrays.asList(messagesSendQO));
        verify(mockMemberBaseFeign).wechatMessageSendBatch(messagesSendBatchQuery);
    }

    @Test
    public void testSendSubsidyBackRights_HsaSubsidyActivityDetailRecordMapperSelectListReturnsNoItems() {
        // Setup
        final SendBackSubsidyRightsEvent event = new SendBackSubsidyRightsEvent();
        event.setSubsidyActivityEventList(Arrays.asList("value"));
        event.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockHsaSubsidyActivityDetailRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure HsaSubsidyActivityRecordMapper.selectList(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord.setSubsidySendState(0);
        hsaSubsidyActivityRecord.setSubsidyNum(0);
        hsaSubsidyActivityRecord.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords = Arrays.asList(hsaSubsidyActivityRecord);
        when(mockHsaSubsidyActivityRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityRecords);

        // Configure HsaMemberInfoCardMapper.selectList(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardValidity(0);
        hsaMemberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> hsaMemberInfoCards = Arrays.asList(hsaMemberInfoCard);
        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberInfoCards);

        // Configure SubsidyActivityAssembler.getHsaMemberFundingDetail(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail.setStoreName("storeName");
        hsaMemberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail.setSubsidyDetailRecordGuid("guid");
        final HsaSubsidyActivityDetailRecord detailRecord = new HsaSubsidyActivityDetailRecord();
        detailRecord.setGuid("guid");
        detailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        detailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        detailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        detailRecord.setIsRetreat(0);
        detailRecord.setCardGuid("cardGuid");
        detailRecord.setMemberInfoGuid("memberInfoGuid");
        detailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        detailRecord.setIsWithdraw(0);
        detailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardValidity(0);
        hsaMemberInfoCard1.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord1 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord1.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord1.setSubsidySendState(0);
        hsaSubsidyActivityRecord1.setSubsidyNum(0);
        hsaSubsidyActivityRecord1.setSubsidyErrorNum(0);
        when(mockSubsidyActivityAssembler.getHsaMemberFundingDetail(detailRecord, hsaMemberInfoCard1,
                hsaSubsidyActivityRecord1)).thenReturn(hsaMemberFundingDetail);

        // Configure HsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(...).
        final CardFreezeBalanceAmountQO cardFreezeBalanceAmountQO = new CardFreezeBalanceAmountQO();
        cardFreezeBalanceAmountQO.setPhysicalCardGuid("physicalCardGuid");
        cardFreezeBalanceAmountQO.setElectronicCardGuid("electronicCardGuid");
        cardFreezeBalanceAmountQO.setMemberInfoGuid("memberInfoGuid");
        cardFreezeBalanceAmountQO.setMemberInfoCardGuid("guid");
        cardFreezeBalanceAmountQO.setIsRefresh(0);
        final List<CardFreezeBalanceAmountQO> cardList = Arrays.asList(cardFreezeBalanceAmountQO);
        when(mockHsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(cardList)).thenReturn(new HashMap<>());

        // Configure HsaCardBaseInfoMapper.queryByGuids(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo.setCardName("cardName");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardStatus(0);
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.queryByGuids(Arrays.asList("value"))).thenReturn(hsaCardBaseInfos);

        // Configure HsaOperationMemberInfoMapper.listMemberInfo(...).
        final MemberInfoVO memberInfoVO = new MemberInfoVO();
        memberInfoVO.setOperSubjectGuid("operSubjectGuid");
        memberInfoVO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVO.setAccountState(0);
        memberInfoVO.setMemberGuid("memberGuid");
        memberInfoVO.setPhoneNum("phone");
        final List<MemberInfoVO> memberInfoVOS = Arrays.asList(memberInfoVO);
        final MemberListQO request = new MemberListQO();
        request.setOperSubjectGuid("operSubjectGuid");
        request.setIsDisable(0);
        request.setMemberGuids(Arrays.asList("value"));
        request.setKeywords("keywords");
        request.setSex(0);
        when(mockHsaOperationMemberInfoMapper.listMemberInfo(request)).thenReturn(memberInfoVOS);

        // Run the test
        hsaSubsidyActivityServiceImplUnderTest.sendSubsidyBackRights(event, 0);

        // Verify the results
        // Confirm HsaSubsidyActivityDetailRecordMapper.batchUpdateStatus(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord.setGuid("guid");
        hsaSubsidyActivityDetailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> activityList = Arrays.asList(hsaSubsidyActivityDetailRecord);
        verify(mockHsaSubsidyActivityDetailRecordMapper).batchUpdateStatus(activityList);

        // Confirm HsaMemberInfoCardMapper.batchUpdate(...).
        final HsaMemberInfoCard hsaMemberInfoCard2 = new HsaMemberInfoCard();
        hsaMemberInfoCard2.setGuid("guid");
        hsaMemberInfoCard2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard2.setCardGuid("cardGuid");
        hsaMemberInfoCard2.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessTimes(0);
        hsaMemberInfoCard2.setCardValidity(0);
        hsaMemberInfoCard2.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> activityList1 = Arrays.asList(hsaMemberInfoCard2);
        verify(mockHsaMemberInfoCardMapper).batchUpdate(activityList1);

        // Confirm HsaMemberFundingDetailService.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail1 = new HsaMemberFundingDetail();
        hsaMemberFundingDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail1.setStoreName("storeName");
        hsaMemberFundingDetail1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail1.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail1.setSubsidyDetailRecordGuid("guid");
        final List<HsaMemberFundingDetail> entityList = Arrays.asList(hsaMemberFundingDetail1);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList);

        // Confirm MemberBaseFeign.wechatMessageSendBatch(...).
        final MessagesSendQO messagesSendQO = new MessagesSendQO();
        messagesSendQO.setTemplateName("msgTitle");
        messagesSendQO.setPrams(new HashMap<>());
        messagesSendQO.setMpParams(new HashMap<>());
        messagesSendQO.setOperSubjectGuid("operSubjectGuid");
        messagesSendQO.setEnterpriseGuid("enterpriseGuid");
        messagesSendQO.setPhone("phone");
        messagesSendQO.setOrderNum("");
        messagesSendQO.setMemberInfoCardGuid("memberInfoCardGuid");
        messagesSendQO.setCardName("cardName");
        messagesSendQO.setCardImage("cardImage");
        final MessagesSendbatchQO messagesSendBatchQuery = new MessagesSendbatchQO(Arrays.asList(messagesSendQO));
        verify(mockMemberBaseFeign).wechatMessageSendBatch(messagesSendBatchQuery);
    }

    @Test
    public void testSendSubsidyBackRights_HsaSubsidyActivityRecordMapperReturnsNoItems() {
        // Setup
        final SendBackSubsidyRightsEvent event = new SendBackSubsidyRightsEvent();
        event.setSubsidyActivityEventList(Arrays.asList("value"));
        event.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure HsaSubsidyActivityDetailRecordMapper.selectList(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord.setGuid("guid");
        hsaSubsidyActivityDetailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords = Arrays.asList(
                hsaSubsidyActivityDetailRecord);
        when(mockHsaSubsidyActivityDetailRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityDetailRecords);

        when(mockHsaSubsidyActivityRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure HsaMemberInfoCardMapper.selectList(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardValidity(0);
        hsaMemberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> hsaMemberInfoCards = Arrays.asList(hsaMemberInfoCard);
        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberInfoCards);

        // Configure SubsidyActivityAssembler.getHsaMemberFundingDetail(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail.setStoreName("storeName");
        hsaMemberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail.setSubsidyDetailRecordGuid("guid");
        final HsaSubsidyActivityDetailRecord detailRecord = new HsaSubsidyActivityDetailRecord();
        detailRecord.setGuid("guid");
        detailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        detailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        detailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        detailRecord.setIsRetreat(0);
        detailRecord.setCardGuid("cardGuid");
        detailRecord.setMemberInfoGuid("memberInfoGuid");
        detailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        detailRecord.setIsWithdraw(0);
        detailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardValidity(0);
        hsaMemberInfoCard1.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord.setSubsidySendState(0);
        hsaSubsidyActivityRecord.setSubsidyNum(0);
        hsaSubsidyActivityRecord.setSubsidyErrorNum(0);
        when(mockSubsidyActivityAssembler.getHsaMemberFundingDetail(detailRecord, hsaMemberInfoCard1,
                hsaSubsidyActivityRecord)).thenReturn(hsaMemberFundingDetail);

        // Configure HsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(...).
        final CardFreezeBalanceAmountQO cardFreezeBalanceAmountQO = new CardFreezeBalanceAmountQO();
        cardFreezeBalanceAmountQO.setPhysicalCardGuid("physicalCardGuid");
        cardFreezeBalanceAmountQO.setElectronicCardGuid("electronicCardGuid");
        cardFreezeBalanceAmountQO.setMemberInfoGuid("memberInfoGuid");
        cardFreezeBalanceAmountQO.setMemberInfoCardGuid("guid");
        cardFreezeBalanceAmountQO.setIsRefresh(0);
        final List<CardFreezeBalanceAmountQO> cardList = Arrays.asList(cardFreezeBalanceAmountQO);
        when(mockHsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(cardList)).thenReturn(new HashMap<>());

        // Configure HsaCardBaseInfoMapper.queryByGuids(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo.setCardName("cardName");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardStatus(0);
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.queryByGuids(Arrays.asList("value"))).thenReturn(hsaCardBaseInfos);

        // Configure HsaOperationMemberInfoMapper.listMemberInfo(...).
        final MemberInfoVO memberInfoVO = new MemberInfoVO();
        memberInfoVO.setOperSubjectGuid("operSubjectGuid");
        memberInfoVO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVO.setAccountState(0);
        memberInfoVO.setMemberGuid("memberGuid");
        memberInfoVO.setPhoneNum("phone");
        final List<MemberInfoVO> memberInfoVOS = Arrays.asList(memberInfoVO);
        final MemberListQO request = new MemberListQO();
        request.setOperSubjectGuid("operSubjectGuid");
        request.setIsDisable(0);
        request.setMemberGuids(Arrays.asList("value"));
        request.setKeywords("keywords");
        request.setSex(0);
        when(mockHsaOperationMemberInfoMapper.listMemberInfo(request)).thenReturn(memberInfoVOS);

        // Run the test
        hsaSubsidyActivityServiceImplUnderTest.sendSubsidyBackRights(event, 0);

        // Verify the results
        // Confirm HsaSubsidyActivityDetailRecordMapper.batchUpdateStatus(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord1 = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord1.setGuid("guid");
        hsaSubsidyActivityDetailRecord1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord1.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord1.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord1.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord1.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord1.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> activityList = Arrays.asList(hsaSubsidyActivityDetailRecord1);
        verify(mockHsaSubsidyActivityDetailRecordMapper).batchUpdateStatus(activityList);

        // Confirm HsaMemberInfoCardMapper.batchUpdate(...).
        final HsaMemberInfoCard hsaMemberInfoCard2 = new HsaMemberInfoCard();
        hsaMemberInfoCard2.setGuid("guid");
        hsaMemberInfoCard2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard2.setCardGuid("cardGuid");
        hsaMemberInfoCard2.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessTimes(0);
        hsaMemberInfoCard2.setCardValidity(0);
        hsaMemberInfoCard2.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> activityList1 = Arrays.asList(hsaMemberInfoCard2);
        verify(mockHsaMemberInfoCardMapper).batchUpdate(activityList1);

        // Confirm HsaMemberFundingDetailService.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail1 = new HsaMemberFundingDetail();
        hsaMemberFundingDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail1.setStoreName("storeName");
        hsaMemberFundingDetail1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail1.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail1.setSubsidyDetailRecordGuid("guid");
        final List<HsaMemberFundingDetail> entityList = Arrays.asList(hsaMemberFundingDetail1);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList);

        // Confirm MemberBaseFeign.wechatMessageSendBatch(...).
        final MessagesSendQO messagesSendQO = new MessagesSendQO();
        messagesSendQO.setTemplateName("msgTitle");
        messagesSendQO.setPrams(new HashMap<>());
        messagesSendQO.setMpParams(new HashMap<>());
        messagesSendQO.setOperSubjectGuid("operSubjectGuid");
        messagesSendQO.setEnterpriseGuid("enterpriseGuid");
        messagesSendQO.setPhone("phone");
        messagesSendQO.setOrderNum("");
        messagesSendQO.setMemberInfoCardGuid("memberInfoCardGuid");
        messagesSendQO.setCardName("cardName");
        messagesSendQO.setCardImage("cardImage");
        final MessagesSendbatchQO messagesSendBatchQuery = new MessagesSendbatchQO(Arrays.asList(messagesSendQO));
        verify(mockMemberBaseFeign).wechatMessageSendBatch(messagesSendBatchQuery);
    }

    @Test
    public void testSendSubsidyBackRights_HsaMemberInfoCardMapperSelectListReturnsNoItems() {
        // Setup
        final SendBackSubsidyRightsEvent event = new SendBackSubsidyRightsEvent();
        event.setSubsidyActivityEventList(Arrays.asList("value"));
        event.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure HsaSubsidyActivityDetailRecordMapper.selectList(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord.setGuid("guid");
        hsaSubsidyActivityDetailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords = Arrays.asList(
                hsaSubsidyActivityDetailRecord);
        when(mockHsaSubsidyActivityDetailRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityDetailRecords);

        // Configure HsaSubsidyActivityRecordMapper.selectList(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord.setSubsidySendState(0);
        hsaSubsidyActivityRecord.setSubsidyNum(0);
        hsaSubsidyActivityRecord.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords = Arrays.asList(hsaSubsidyActivityRecord);
        when(mockHsaSubsidyActivityRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityRecords);

        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure SubsidyActivityAssembler.getHsaMemberFundingDetail(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail.setStoreName("storeName");
        hsaMemberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail.setSubsidyDetailRecordGuid("guid");
        final HsaSubsidyActivityDetailRecord detailRecord = new HsaSubsidyActivityDetailRecord();
        detailRecord.setGuid("guid");
        detailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        detailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        detailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        detailRecord.setIsRetreat(0);
        detailRecord.setCardGuid("cardGuid");
        detailRecord.setMemberInfoGuid("memberInfoGuid");
        detailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        detailRecord.setIsWithdraw(0);
        detailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardValidity(0);
        hsaMemberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord1 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord1.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord1.setSubsidySendState(0);
        hsaSubsidyActivityRecord1.setSubsidyNum(0);
        hsaSubsidyActivityRecord1.setSubsidyErrorNum(0);
        when(mockSubsidyActivityAssembler.getHsaMemberFundingDetail(detailRecord, hsaMemberInfoCard,
                hsaSubsidyActivityRecord1)).thenReturn(hsaMemberFundingDetail);

        // Configure HsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(...).
        final CardFreezeBalanceAmountQO cardFreezeBalanceAmountQO = new CardFreezeBalanceAmountQO();
        cardFreezeBalanceAmountQO.setPhysicalCardGuid("physicalCardGuid");
        cardFreezeBalanceAmountQO.setElectronicCardGuid("electronicCardGuid");
        cardFreezeBalanceAmountQO.setMemberInfoGuid("memberInfoGuid");
        cardFreezeBalanceAmountQO.setMemberInfoCardGuid("guid");
        cardFreezeBalanceAmountQO.setIsRefresh(0);
        final List<CardFreezeBalanceAmountQO> cardList = Arrays.asList(cardFreezeBalanceAmountQO);
        when(mockHsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(cardList)).thenReturn(new HashMap<>());

        // Configure HsaCardBaseInfoMapper.queryByGuids(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo.setCardName("cardName");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardStatus(0);
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.queryByGuids(Arrays.asList("value"))).thenReturn(hsaCardBaseInfos);

        // Configure HsaOperationMemberInfoMapper.listMemberInfo(...).
        final MemberInfoVO memberInfoVO = new MemberInfoVO();
        memberInfoVO.setOperSubjectGuid("operSubjectGuid");
        memberInfoVO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVO.setAccountState(0);
        memberInfoVO.setMemberGuid("memberGuid");
        memberInfoVO.setPhoneNum("phone");
        final List<MemberInfoVO> memberInfoVOS = Arrays.asList(memberInfoVO);
        final MemberListQO request = new MemberListQO();
        request.setOperSubjectGuid("operSubjectGuid");
        request.setIsDisable(0);
        request.setMemberGuids(Arrays.asList("value"));
        request.setKeywords("keywords");
        request.setSex(0);
        when(mockHsaOperationMemberInfoMapper.listMemberInfo(request)).thenReturn(memberInfoVOS);

        // Run the test
        hsaSubsidyActivityServiceImplUnderTest.sendSubsidyBackRights(event, 0);

        // Verify the results
        // Confirm HsaSubsidyActivityDetailRecordMapper.batchUpdateStatus(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord1 = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord1.setGuid("guid");
        hsaSubsidyActivityDetailRecord1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord1.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord1.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord1.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord1.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord1.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> activityList = Arrays.asList(hsaSubsidyActivityDetailRecord1);
        verify(mockHsaSubsidyActivityDetailRecordMapper).batchUpdateStatus(activityList);

        // Confirm HsaMemberInfoCardMapper.batchUpdate(...).
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardValidity(0);
        hsaMemberInfoCard1.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> activityList1 = Arrays.asList(hsaMemberInfoCard1);
        verify(mockHsaMemberInfoCardMapper).batchUpdate(activityList1);

        // Confirm HsaMemberFundingDetailService.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail1 = new HsaMemberFundingDetail();
        hsaMemberFundingDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail1.setStoreName("storeName");
        hsaMemberFundingDetail1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail1.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail1.setSubsidyDetailRecordGuid("guid");
        final List<HsaMemberFundingDetail> entityList = Arrays.asList(hsaMemberFundingDetail1);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList);

        // Confirm MemberBaseFeign.wechatMessageSendBatch(...).
        final MessagesSendQO messagesSendQO = new MessagesSendQO();
        messagesSendQO.setTemplateName("msgTitle");
        messagesSendQO.setPrams(new HashMap<>());
        messagesSendQO.setMpParams(new HashMap<>());
        messagesSendQO.setOperSubjectGuid("operSubjectGuid");
        messagesSendQO.setEnterpriseGuid("enterpriseGuid");
        messagesSendQO.setPhone("phone");
        messagesSendQO.setOrderNum("");
        messagesSendQO.setMemberInfoCardGuid("memberInfoCardGuid");
        messagesSendQO.setCardName("cardName");
        messagesSendQO.setCardImage("cardImage");
        final MessagesSendbatchQO messagesSendBatchQuery = new MessagesSendbatchQO(Arrays.asList(messagesSendQO));
        verify(mockMemberBaseFeign).wechatMessageSendBatch(messagesSendBatchQuery);
    }

    @Test
    public void testSendSubsidyBackRights_HsaCardBaseInfoMapperReturnsNoItems() {
        // Setup
        final SendBackSubsidyRightsEvent event = new SendBackSubsidyRightsEvent();
        event.setSubsidyActivityEventList(Arrays.asList("value"));
        event.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure HsaSubsidyActivityDetailRecordMapper.selectList(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord.setGuid("guid");
        hsaSubsidyActivityDetailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords = Arrays.asList(
                hsaSubsidyActivityDetailRecord);
        when(mockHsaSubsidyActivityDetailRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityDetailRecords);

        // Configure HsaSubsidyActivityRecordMapper.selectList(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord.setSubsidySendState(0);
        hsaSubsidyActivityRecord.setSubsidyNum(0);
        hsaSubsidyActivityRecord.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords = Arrays.asList(hsaSubsidyActivityRecord);
        when(mockHsaSubsidyActivityRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityRecords);

        // Configure HsaMemberInfoCardMapper.selectList(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardValidity(0);
        hsaMemberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> hsaMemberInfoCards = Arrays.asList(hsaMemberInfoCard);
        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberInfoCards);

        // Configure SubsidyActivityAssembler.getHsaMemberFundingDetail(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail.setStoreName("storeName");
        hsaMemberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail.setSubsidyDetailRecordGuid("guid");
        final HsaSubsidyActivityDetailRecord detailRecord = new HsaSubsidyActivityDetailRecord();
        detailRecord.setGuid("guid");
        detailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        detailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        detailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        detailRecord.setIsRetreat(0);
        detailRecord.setCardGuid("cardGuid");
        detailRecord.setMemberInfoGuid("memberInfoGuid");
        detailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        detailRecord.setIsWithdraw(0);
        detailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardValidity(0);
        hsaMemberInfoCard1.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord1 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord1.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord1.setSubsidySendState(0);
        hsaSubsidyActivityRecord1.setSubsidyNum(0);
        hsaSubsidyActivityRecord1.setSubsidyErrorNum(0);
        when(mockSubsidyActivityAssembler.getHsaMemberFundingDetail(detailRecord, hsaMemberInfoCard1,
                hsaSubsidyActivityRecord1)).thenReturn(hsaMemberFundingDetail);

        // Configure HsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(...).
        final CardFreezeBalanceAmountQO cardFreezeBalanceAmountQO = new CardFreezeBalanceAmountQO();
        cardFreezeBalanceAmountQO.setPhysicalCardGuid("physicalCardGuid");
        cardFreezeBalanceAmountQO.setElectronicCardGuid("electronicCardGuid");
        cardFreezeBalanceAmountQO.setMemberInfoGuid("memberInfoGuid");
        cardFreezeBalanceAmountQO.setMemberInfoCardGuid("guid");
        cardFreezeBalanceAmountQO.setIsRefresh(0);
        final List<CardFreezeBalanceAmountQO> cardList = Arrays.asList(cardFreezeBalanceAmountQO);
        when(mockHsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(cardList)).thenReturn(new HashMap<>());

        when(mockHsaCardBaseInfoMapper.queryByGuids(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure HsaOperationMemberInfoMapper.listMemberInfo(...).
        final MemberInfoVO memberInfoVO = new MemberInfoVO();
        memberInfoVO.setOperSubjectGuid("operSubjectGuid");
        memberInfoVO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVO.setAccountState(0);
        memberInfoVO.setMemberGuid("memberGuid");
        memberInfoVO.setPhoneNum("phone");
        final List<MemberInfoVO> memberInfoVOS = Arrays.asList(memberInfoVO);
        final MemberListQO request = new MemberListQO();
        request.setOperSubjectGuid("operSubjectGuid");
        request.setIsDisable(0);
        request.setMemberGuids(Arrays.asList("value"));
        request.setKeywords("keywords");
        request.setSex(0);
        when(mockHsaOperationMemberInfoMapper.listMemberInfo(request)).thenReturn(memberInfoVOS);

        // Run the test
        hsaSubsidyActivityServiceImplUnderTest.sendSubsidyBackRights(event, 0);

        // Verify the results
        // Confirm HsaSubsidyActivityDetailRecordMapper.batchUpdateStatus(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord1 = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord1.setGuid("guid");
        hsaSubsidyActivityDetailRecord1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord1.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord1.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord1.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord1.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord1.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> activityList = Arrays.asList(hsaSubsidyActivityDetailRecord1);
        verify(mockHsaSubsidyActivityDetailRecordMapper).batchUpdateStatus(activityList);

        // Confirm HsaMemberInfoCardMapper.batchUpdate(...).
        final HsaMemberInfoCard hsaMemberInfoCard2 = new HsaMemberInfoCard();
        hsaMemberInfoCard2.setGuid("guid");
        hsaMemberInfoCard2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard2.setCardGuid("cardGuid");
        hsaMemberInfoCard2.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessTimes(0);
        hsaMemberInfoCard2.setCardValidity(0);
        hsaMemberInfoCard2.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> activityList1 = Arrays.asList(hsaMemberInfoCard2);
        verify(mockHsaMemberInfoCardMapper).batchUpdate(activityList1);

        // Confirm HsaMemberFundingDetailService.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail1 = new HsaMemberFundingDetail();
        hsaMemberFundingDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail1.setStoreName("storeName");
        hsaMemberFundingDetail1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail1.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail1.setSubsidyDetailRecordGuid("guid");
        final List<HsaMemberFundingDetail> entityList = Arrays.asList(hsaMemberFundingDetail1);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList);

        // Confirm MemberBaseFeign.wechatMessageSendBatch(...).
        final MessagesSendQO messagesSendQO = new MessagesSendQO();
        messagesSendQO.setTemplateName("msgTitle");
        messagesSendQO.setPrams(new HashMap<>());
        messagesSendQO.setMpParams(new HashMap<>());
        messagesSendQO.setOperSubjectGuid("operSubjectGuid");
        messagesSendQO.setEnterpriseGuid("enterpriseGuid");
        messagesSendQO.setPhone("phone");
        messagesSendQO.setOrderNum("");
        messagesSendQO.setMemberInfoCardGuid("memberInfoCardGuid");
        messagesSendQO.setCardName("cardName");
        messagesSendQO.setCardImage("cardImage");
        final MessagesSendbatchQO messagesSendBatchQuery = new MessagesSendbatchQO(Arrays.asList(messagesSendQO));
        verify(mockMemberBaseFeign).wechatMessageSendBatch(messagesSendBatchQuery);
    }

    @Test
    public void testSendSubsidyBackRights_HsaOperationMemberInfoMapperReturnsNoItems() {
        // Setup
        final SendBackSubsidyRightsEvent event = new SendBackSubsidyRightsEvent();
        event.setSubsidyActivityEventList(Arrays.asList("value"));
        event.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure HsaSubsidyActivityDetailRecordMapper.selectList(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord.setGuid("guid");
        hsaSubsidyActivityDetailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords = Arrays.asList(
                hsaSubsidyActivityDetailRecord);
        when(mockHsaSubsidyActivityDetailRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityDetailRecords);

        // Configure HsaSubsidyActivityRecordMapper.selectList(...).
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord.setSubsidySendState(0);
        hsaSubsidyActivityRecord.setSubsidyNum(0);
        hsaSubsidyActivityRecord.setSubsidyErrorNum(0);
        final List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords = Arrays.asList(hsaSubsidyActivityRecord);
        when(mockHsaSubsidyActivityRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubsidyActivityRecords);

        // Configure HsaMemberInfoCardMapper.selectList(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardValidity(0);
        hsaMemberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> hsaMemberInfoCards = Arrays.asList(hsaMemberInfoCard);
        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberInfoCards);

        // Configure SubsidyActivityAssembler.getHsaMemberFundingDetail(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail.setStoreName("storeName");
        hsaMemberFundingDetail.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail.setSubsidyDetailRecordGuid("guid");
        final HsaSubsidyActivityDetailRecord detailRecord = new HsaSubsidyActivityDetailRecord();
        detailRecord.setGuid("guid");
        detailRecord.setSubsidyMoney(new BigDecimal("0.00"));
        detailRecord.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        detailRecord.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        detailRecord.setIsRetreat(0);
        detailRecord.setCardGuid("cardGuid");
        detailRecord.setMemberInfoGuid("memberInfoGuid");
        detailRecord.setMemberInfoCardGuid("memberInfoCardGuid");
        detailRecord.setIsWithdraw(0);
        detailRecord.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardValidity(0);
        hsaMemberInfoCard1.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final HsaSubsidyActivityRecord hsaSubsidyActivityRecord1 = new HsaSubsidyActivityRecord();
        hsaSubsidyActivityRecord1.setGuid("f280f364-d9ec-4a13-a9ba-5303f14f1614");
        hsaSubsidyActivityRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityRecord1.setSubsidySendState(0);
        hsaSubsidyActivityRecord1.setSubsidyNum(0);
        hsaSubsidyActivityRecord1.setSubsidyErrorNum(0);
        when(mockSubsidyActivityAssembler.getHsaMemberFundingDetail(detailRecord, hsaMemberInfoCard1,
                hsaSubsidyActivityRecord1)).thenReturn(hsaMemberFundingDetail);

        // Configure HsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(...).
        final CardFreezeBalanceAmountQO cardFreezeBalanceAmountQO = new CardFreezeBalanceAmountQO();
        cardFreezeBalanceAmountQO.setPhysicalCardGuid("physicalCardGuid");
        cardFreezeBalanceAmountQO.setElectronicCardGuid("electronicCardGuid");
        cardFreezeBalanceAmountQO.setMemberInfoGuid("memberInfoGuid");
        cardFreezeBalanceAmountQO.setMemberInfoCardGuid("guid");
        cardFreezeBalanceAmountQO.setIsRefresh(0);
        final List<CardFreezeBalanceAmountQO> cardList = Arrays.asList(cardFreezeBalanceAmountQO);
        when(mockHsaCardRechargeGiftDetailService.getMemberCardFreezeAmountMap(cardList)).thenReturn(new HashMap<>());

        // Configure HsaCardBaseInfoMapper.queryByGuids(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("83aef417-2687-428e-8416-e36ce7d26ef9");
        hsaCardBaseInfo.setCardName("cardName");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardStatus(0);
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.queryByGuids(Arrays.asList("value"))).thenReturn(hsaCardBaseInfos);

        // Configure HsaOperationMemberInfoMapper.listMemberInfo(...).
        final MemberListQO request = new MemberListQO();
        request.setOperSubjectGuid("operSubjectGuid");
        request.setIsDisable(0);
        request.setMemberGuids(Arrays.asList("value"));
        request.setKeywords("keywords");
        request.setSex(0);
        when(mockHsaOperationMemberInfoMapper.listMemberInfo(request)).thenReturn(Collections.emptyList());

        // Run the test
        hsaSubsidyActivityServiceImplUnderTest.sendSubsidyBackRights(event, 0);

        // Verify the results
        // Confirm HsaSubsidyActivityDetailRecordMapper.batchUpdateStatus(...).
        final HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord1 = new HsaSubsidyActivityDetailRecord();
        hsaSubsidyActivityDetailRecord1.setGuid("guid");
        hsaSubsidyActivityDetailRecord1.setSubsidyMoney(new BigDecimal("0.00"));
        hsaSubsidyActivityDetailRecord1.setSubsidyActivityRecordGuid("subsidyActivityRecordGuid");
        hsaSubsidyActivityDetailRecord1.setSubsidyArriveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaSubsidyActivityDetailRecord1.setIsRetreat(0);
        hsaSubsidyActivityDetailRecord1.setCardGuid("cardGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaSubsidyActivityDetailRecord1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaSubsidyActivityDetailRecord1.setIsWithdraw(0);
        hsaSubsidyActivityDetailRecord1.setSubsidyResidueMoney(new BigDecimal("0.00"));
        final List<HsaSubsidyActivityDetailRecord> activityList = Arrays.asList(hsaSubsidyActivityDetailRecord1);
        verify(mockHsaSubsidyActivityDetailRecordMapper).batchUpdateStatus(activityList);

        // Confirm HsaMemberInfoCardMapper.batchUpdate(...).
        final HsaMemberInfoCard hsaMemberInfoCard2 = new HsaMemberInfoCard();
        hsaMemberInfoCard2.setGuid("guid");
        hsaMemberInfoCard2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard2.setCardGuid("cardGuid");
        hsaMemberInfoCard2.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setRetreatSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessTimes(0);
        hsaMemberInfoCard2.setCardValidity(0);
        hsaMemberInfoCard2.setCardValidityDate(LocalDate.of(2020, 1, 1));
        final List<HsaMemberInfoCard> activityList1 = Arrays.asList(hsaMemberInfoCard2);
        verify(mockHsaMemberInfoCardMapper).batchUpdate(activityList1);

        // Confirm HsaMemberFundingDetailService.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail1 = new HsaMemberFundingDetail();
        hsaMemberFundingDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail1.setMemberInfoCardGuid("memberInfoCardGuid");
        hsaMemberFundingDetail1.setStoreName("storeName");
        hsaMemberFundingDetail1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberFundingDetail1.setOutOfDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberFundingDetail1.setSubsidyDetailRecordGuid("guid");
        final List<HsaMemberFundingDetail> entityList = Arrays.asList(hsaMemberFundingDetail1);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList);

        // Confirm MemberBaseFeign.wechatMessageSendBatch(...).
        final MessagesSendQO messagesSendQO = new MessagesSendQO();
        messagesSendQO.setTemplateName("msgTitle");
        messagesSendQO.setPrams(new HashMap<>());
        messagesSendQO.setMpParams(new HashMap<>());
        messagesSendQO.setOperSubjectGuid("operSubjectGuid");
        messagesSendQO.setEnterpriseGuid("enterpriseGuid");
        messagesSendQO.setPhone("phone");
        messagesSendQO.setOrderNum("");
        messagesSendQO.setMemberInfoCardGuid("memberInfoCardGuid");
        messagesSendQO.setCardName("cardName");
        messagesSendQO.setCardImage("cardImage");
        final MessagesSendbatchQO messagesSendBatchQuery = new MessagesSendbatchQO(Arrays.asList(messagesSendQO));
        verify(mockMemberBaseFeign).wechatMessageSendBatch(messagesSendBatchQuery);
    }
}
