package com.holderzone.member.queue.service.member.impl;

import com.holderzone.member.queue.entity.HsaIntegralDetail;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class HsaIntegralDetailServiceImplTest {

    private HsaIntegralDetailServiceImpl hsaIntegralDetailServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        hsaIntegralDetailServiceImplUnderTest = new HsaIntegralDetailServiceImpl();
    }

    @Test
    public void testQueryByGuid() {
        // Setup
        final HsaIntegralDetail expectedResult = new HsaIntegralDetail();
        expectedResult.setId(0L);
        expectedResult.setGuid("61bcc8b9-b707-4dae-9750-bb98ecb2efc9");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setMemberInfoGuid("memberInfoGuid");
        expectedResult.setIntegralTaskGuid("integralTaskGuid");

        // Run the test
        final HsaIntegralDetail result = hsaIntegralDetailServiceImplUnderTest.queryByGuid(
                "fb8de0a0-75e5-4830-b17a-88bc68e75fff");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryByGuids() {
        // Setup
        final HsaIntegralDetail hsaIntegralDetail = new HsaIntegralDetail();
        hsaIntegralDetail.setId(0L);
        hsaIntegralDetail.setGuid("61bcc8b9-b707-4dae-9750-bb98ecb2efc9");
        hsaIntegralDetail.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail.setIntegralTaskGuid("integralTaskGuid");
        final List<HsaIntegralDetail> expectedResult = Arrays.asList(hsaIntegralDetail);

        // Run the test
        final List<HsaIntegralDetail> result = hsaIntegralDetailServiceImplUnderTest.queryByGuids(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateByGuid() {
        // Setup
        final HsaIntegralDetail t = new HsaIntegralDetail();
        t.setId(0L);
        t.setGuid("61bcc8b9-b707-4dae-9750-bb98ecb2efc9");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setIntegralTaskGuid("integralTaskGuid");

        // Run the test
        final boolean result = hsaIntegralDetailServiceImplUnderTest.updateByGuid(t);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemoveByGuid() {
        // Setup
        // Run the test
        final boolean result = hsaIntegralDetailServiceImplUnderTest.removeByGuid(
                "93fc6bcb-d916-4681-a8df-df3d7cc9067f");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemoveByGuids() {
        // Setup
        // Run the test
        final boolean result = hsaIntegralDetailServiceImplUnderTest.removeByGuids(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isFalse();
    }
}
