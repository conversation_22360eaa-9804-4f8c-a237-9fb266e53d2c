package com.holderzone.member.queue.service.coupon.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.util.concurrent.MoreExecutors;
import com.holderzone.member.common.dto.card.MemberPhoneDTO;
import com.holderzone.member.common.dto.coupon.ResponseCouponDTO;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.qo.coupon.CouponPackageMemberDetailsDTO;
import com.holderzone.member.common.qo.coupon.CouponPackageSpecificDTO;
import com.holderzone.member.common.qo.member.AddMemberLabelCorrelationQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.coupon.CouponPackageGrantVO;
import com.holderzone.member.common.vo.coupon.EditCouponPackageActivityVO;
import com.holderzone.member.common.vo.coupon.MemberCouponPackageVO;
import com.holderzone.member.queue.entity.HsaMemberLabel;
import com.holderzone.member.queue.mapper.HsaMemberLabelMapper;
import com.holderzone.member.queue.mapper.HsaOperationMemberInfoMapper;
import com.holderzone.member.queue.mapper.coupon.HsaMemberCouponPackageLinkMapper;
import com.holderzone.member.queue.service.cache.CacheService;
import com.holderzone.member.queue.service.coupon.MemberCouponLinkService;
import com.holderzone.member.queue.service.coupon.assembler.MemberCouponAssembler;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MemberCouponPackageLinkServiceImplTest {

    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private HsaMemberCouponPackageLinkMapper mockMapper;
    @Mock
    private MemberMarketingFeign mockMemberMarketingFeign;
    @Mock
    private MemberBaseFeign mockMemberBaseFeign;
    @Mock
    private HsaOperationMemberInfoMapper mockHsaOperationInfoMapper;
    @Mock
    private MemberCouponLinkService mockHsaMemberCouponLinkService;
    @Mock
    private CacheService mockCacheService;
    @Mock
    private HsaMemberLabelMapper mockHsaMemberLabelMapper;
    @Mock
    private MemberCouponAssembler mockMemberCouponAssembler;

    @InjectMocks
    private MemberCouponPackageLinkServiceImpl memberCouponPackageLinkServiceImplUnderTest;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(memberCouponPackageLinkServiceImplUnderTest, "memberQueueThreadExecutor",
                MoreExecutors.directExecutor());
    }

    @Test
    public void testXxlJobCouponPackageGrant() {
        // Setup
        when(mockCacheService.setLock("key")).thenReturn(false);

        // Configure MemberMarketingFeign.queryRunActivity(...).
        final EditCouponPackageActivityVO editCouponPackageActivityVO = new EditCouponPackageActivityVO();
        editCouponPackageActivityVO.setGuid("activityGuid");
        editCouponPackageActivityVO.setOperSubjectGuid("operSubjectGuid");
        editCouponPackageActivityVO.setActivityCode("activityCode");
        editCouponPackageActivityVO.setActivityName("activityName");
        editCouponPackageActivityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO.setTime("time");
        editCouponPackageActivityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO));
        final ResponseCouponDTO responseCouponDTO = new ResponseCouponDTO();
        responseCouponDTO.setCouponName("couponName");
        responseCouponDTO.setCouponCode("couponCode");
        responseCouponDTO.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO.setIsExist(0);
        responseCouponDTO.setNum(0);
        editCouponPackageActivityVO.setCouponGuidList(Arrays.asList(responseCouponDTO));
        editCouponPackageActivityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList = new CouponPackageMemberDetailsDTO();
        labelList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setLabelList(labelList);
        final CouponPackageMemberDetailsDTO gradeList = new CouponPackageMemberDetailsDTO();
        gradeList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setGradeList(gradeList);
        final CouponPackageMemberDetailsDTO memberList = new CouponPackageMemberDetailsDTO();
        memberList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setMemberList(memberList);
        final List<EditCouponPackageActivityVO> editCouponPackageActivityVOS = Arrays.asList(
                editCouponPackageActivityVO);
        when(mockMemberMarketingFeign.queryRunActivity(Arrays.asList("value")))
                .thenReturn(editCouponPackageActivityVOS);

        when(mockHsaOperationInfoMapper.queueSubjectGuid("operSubjectGuid"))
                .thenReturn(new HashSet<>(Arrays.asList("value")));

        // Configure HsaOperationMemberInfoMapper.listByGuid(...).
        final MemberPhoneDTO memberPhoneDTO = new MemberPhoneDTO();
        memberPhoneDTO.setPhoneNum("memberPhone");
        memberPhoneDTO.setGuid("guid");
        memberPhoneDTO.setUserName("userName");
        memberPhoneDTO.setAccountState(0);
        memberPhoneDTO.setWechatState(0);
        final List<MemberPhoneDTO> memberPhoneDTOS = Arrays.asList(memberPhoneDTO);
        when(mockHsaOperationInfoMapper.listByGuid(Arrays.asList("value"))).thenReturn(memberPhoneDTOS);

        when(mockMapper.queueMemberGuidByGuid("activityGuid")).thenReturn(Arrays.asList("value"));

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("dc306173-1358-4df9-a481-d9d5a4c1e589");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("operationMemberInfoGuid");
        hsaMemberLabel.setOperatorGuid("operatorGuid");
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        when(mockHsaOperationInfoMapper.queryMemberGrade(Arrays.asList("value"))).thenReturn(Arrays.asList("value"));
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("0baa583e-9fd1-4506-9266-2fe3b8567331");
        when(mockCacheService.getInventoryNum("key", "activityCode")).thenReturn(0);

        // Configure MemberCouponAssembler.newMemberCouponLink(...).
        final HsaMemberCouponLink hsaMemberCouponLink = new HsaMemberCouponLink();
        hsaMemberCouponLink.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink.setCouponName("couponName");
        hsaMemberCouponLink.setMemberGuid("memberGuid");
        hsaMemberCouponLink.setCouponType(0);
        hsaMemberCouponLink.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> hsaMemberCouponLinks = Arrays.asList(hsaMemberCouponLink);
        final EditCouponPackageActivityVO activityVO = new EditCouponPackageActivityVO();
        activityVO.setGuid("activityGuid");
        activityVO.setOperSubjectGuid("operSubjectGuid");
        activityVO.setActivityCode("activityCode");
        activityVO.setActivityName("activityName");
        activityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO1 = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO1.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO1.setTime("time");
        activityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO1));
        final ResponseCouponDTO responseCouponDTO1 = new ResponseCouponDTO();
        responseCouponDTO1.setCouponName("couponName");
        responseCouponDTO1.setCouponCode("couponCode");
        responseCouponDTO1.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO1.setIsExist(0);
        responseCouponDTO1.setNum(0);
        activityVO.setCouponGuidList(Arrays.asList(responseCouponDTO1));
        activityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList1 = new CouponPackageMemberDetailsDTO();
        labelList1.setGuidList(Arrays.asList("value"));
        activityVO.setLabelList(labelList1);
        final CouponPackageMemberDetailsDTO gradeList1 = new CouponPackageMemberDetailsDTO();
        gradeList1.setGuidList(Arrays.asList("value"));
        activityVO.setGradeList(gradeList1);
        final CouponPackageMemberDetailsDTO memberList1 = new CouponPackageMemberDetailsDTO();
        memberList1.setGuidList(Arrays.asList("value"));
        activityVO.setMemberList(memberList1);
        final MemberPhoneDTO memberPhoneDTO1 = new MemberPhoneDTO();
        memberPhoneDTO1.setPhoneNum("memberPhone");
        memberPhoneDTO1.setGuid("guid");
        memberPhoneDTO1.setUserName("userName");
        memberPhoneDTO1.setAccountState(0);
        memberPhoneDTO1.setWechatState(0);
        when(mockMemberCouponAssembler.newMemberCouponLink(activityVO, memberPhoneDTO1,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), new HashMap<>(),
                new HashSet<>(Arrays.asList("value")))).thenReturn(hsaMemberCouponLinks);

        // Run the test
        memberCouponPackageLinkServiceImplUnderTest.xxlJobCouponPackageGrant(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        verify(mockCacheService).delete("key");
        verify(mockCacheService).lockInventoryNum("key", "activityCode", 1, 1);
        verify(mockCacheService).updateInventoryNum("key", "activityCode");
        verify(mockCacheService).addInventoryNum("key", "activityCode");

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink1 = new HsaMemberCouponLink();
        hsaMemberCouponLink1.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink1.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink1.setCouponName("couponName");
        hsaMemberCouponLink1.setMemberGuid("memberGuid");
        hsaMemberCouponLink1.setCouponType(0);
        hsaMemberCouponLink1.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> collection = Arrays.asList(hsaMemberCouponLink1);
        verify(mockHsaMemberCouponLinkService).saveBatch(collection, 0);

        // Confirm MemberBaseFeign.addMemberInfoLabel(...).
        final AddMemberLabelCorrelationQO qo = new AddMemberLabelCorrelationQO();
        qo.setMemberInfoGuid(Arrays.asList("value"));
        qo.setLabelGuid(Arrays.asList("value"));
        qo.setBatchId("batchId");
        verify(mockMemberBaseFeign).addMemberInfoLabel(qo);

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink2 = new HsaMemberCouponLink();
        hsaMemberCouponLink2.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink2.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink2.setCouponName("couponName");
        hsaMemberCouponLink2.setMemberGuid("memberGuid");
        hsaMemberCouponLink2.setCouponType(0);
        hsaMemberCouponLink2.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> entityList = Arrays.asList(hsaMemberCouponLink2);
        verify(mockHsaMemberCouponLinkService).saveBatch(entityList);

        // Confirm MemberBaseFeign.sendMemberCouponNotice(...).
        final MemberCouponPackageVO memberCouponPackageVO = new MemberCouponPackageVO();
        memberCouponPackageVO.setOperSubjectGuid("operSubjectGuid");
        memberCouponPackageVO.setEnterpriseGuid("enterpriseGuid");
        memberCouponPackageVO.setMemberGuid("guid");
        memberCouponPackageVO.setCouponPackageName("activityName");
        memberCouponPackageVO.setCouponPackageNum(0);
        memberCouponPackageVO.setCouponName("couponName");
        memberCouponPackageVO.setCouponType(0);
        memberCouponPackageVO.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberCouponPackageVO.setCouponNum(0);
        memberCouponPackageVO.setPhoneNum("memberPhone");
        final List<MemberCouponPackageVO> memberCouponLinks = Arrays.asList(memberCouponPackageVO);
        verify(mockMemberBaseFeign).sendMemberCouponNotice(memberCouponLinks);
    }

    @Test
    public void testXxlJobCouponPackageGrant_MemberMarketingFeignReturnsNoItems() {
        // Setup
        when(mockCacheService.setLock("key")).thenReturn(false);
        when(mockMemberMarketingFeign.queryRunActivity(Arrays.asList("value"))).thenReturn(Collections.emptyList());
        when(mockHsaOperationInfoMapper.queueSubjectGuid("operSubjectGuid"))
                .thenReturn(new HashSet<>(Arrays.asList("value")));

        // Configure HsaOperationMemberInfoMapper.listByGuid(...).
        final MemberPhoneDTO memberPhoneDTO = new MemberPhoneDTO();
        memberPhoneDTO.setPhoneNum("memberPhone");
        memberPhoneDTO.setGuid("guid");
        memberPhoneDTO.setUserName("userName");
        memberPhoneDTO.setAccountState(0);
        memberPhoneDTO.setWechatState(0);
        final List<MemberPhoneDTO> memberPhoneDTOS = Arrays.asList(memberPhoneDTO);
        when(mockHsaOperationInfoMapper.listByGuid(Arrays.asList("value"))).thenReturn(memberPhoneDTOS);

        when(mockMapper.queueMemberGuidByGuid("activityGuid")).thenReturn(Arrays.asList("value"));

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("dc306173-1358-4df9-a481-d9d5a4c1e589");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("operationMemberInfoGuid");
        hsaMemberLabel.setOperatorGuid("operatorGuid");
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        when(mockHsaOperationInfoMapper.queryMemberGrade(Arrays.asList("value"))).thenReturn(Arrays.asList("value"));
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("0baa583e-9fd1-4506-9266-2fe3b8567331");
        when(mockCacheService.getInventoryNum("key", "activityCode")).thenReturn(0);

        // Configure MemberCouponAssembler.newMemberCouponLink(...).
        final HsaMemberCouponLink hsaMemberCouponLink = new HsaMemberCouponLink();
        hsaMemberCouponLink.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink.setCouponName("couponName");
        hsaMemberCouponLink.setMemberGuid("memberGuid");
        hsaMemberCouponLink.setCouponType(0);
        hsaMemberCouponLink.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> hsaMemberCouponLinks = Arrays.asList(hsaMemberCouponLink);
        final EditCouponPackageActivityVO activityVO = new EditCouponPackageActivityVO();
        activityVO.setGuid("activityGuid");
        activityVO.setOperSubjectGuid("operSubjectGuid");
        activityVO.setActivityCode("activityCode");
        activityVO.setActivityName("activityName");
        activityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO.setTime("time");
        activityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO));
        final ResponseCouponDTO responseCouponDTO = new ResponseCouponDTO();
        responseCouponDTO.setCouponName("couponName");
        responseCouponDTO.setCouponCode("couponCode");
        responseCouponDTO.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO.setIsExist(0);
        responseCouponDTO.setNum(0);
        activityVO.setCouponGuidList(Arrays.asList(responseCouponDTO));
        activityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList = new CouponPackageMemberDetailsDTO();
        labelList.setGuidList(Arrays.asList("value"));
        activityVO.setLabelList(labelList);
        final CouponPackageMemberDetailsDTO gradeList = new CouponPackageMemberDetailsDTO();
        gradeList.setGuidList(Arrays.asList("value"));
        activityVO.setGradeList(gradeList);
        final CouponPackageMemberDetailsDTO memberList = new CouponPackageMemberDetailsDTO();
        memberList.setGuidList(Arrays.asList("value"));
        activityVO.setMemberList(memberList);
        final MemberPhoneDTO memberPhoneDTO1 = new MemberPhoneDTO();
        memberPhoneDTO1.setPhoneNum("memberPhone");
        memberPhoneDTO1.setGuid("guid");
        memberPhoneDTO1.setUserName("userName");
        memberPhoneDTO1.setAccountState(0);
        memberPhoneDTO1.setWechatState(0);
        when(mockMemberCouponAssembler.newMemberCouponLink(activityVO, memberPhoneDTO1,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), new HashMap<>(),
                new HashSet<>(Arrays.asList("value")))).thenReturn(hsaMemberCouponLinks);

        // Run the test
        memberCouponPackageLinkServiceImplUnderTest.xxlJobCouponPackageGrant(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        verify(mockCacheService).delete("key");
        verify(mockCacheService).lockInventoryNum("key", "activityCode", 1, 1);
        verify(mockCacheService).updateInventoryNum("key", "activityCode");
        verify(mockCacheService).addInventoryNum("key", "activityCode");

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink1 = new HsaMemberCouponLink();
        hsaMemberCouponLink1.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink1.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink1.setCouponName("couponName");
        hsaMemberCouponLink1.setMemberGuid("memberGuid");
        hsaMemberCouponLink1.setCouponType(0);
        hsaMemberCouponLink1.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> collection = Arrays.asList(hsaMemberCouponLink1);
        verify(mockHsaMemberCouponLinkService).saveBatch(collection, 0);

        // Confirm MemberBaseFeign.addMemberInfoLabel(...).
        final AddMemberLabelCorrelationQO qo = new AddMemberLabelCorrelationQO();
        qo.setMemberInfoGuid(Arrays.asList("value"));
        qo.setLabelGuid(Arrays.asList("value"));
        qo.setBatchId("batchId");
        verify(mockMemberBaseFeign).addMemberInfoLabel(qo);

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink2 = new HsaMemberCouponLink();
        hsaMemberCouponLink2.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink2.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink2.setCouponName("couponName");
        hsaMemberCouponLink2.setMemberGuid("memberGuid");
        hsaMemberCouponLink2.setCouponType(0);
        hsaMemberCouponLink2.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> entityList = Arrays.asList(hsaMemberCouponLink2);
        verify(mockHsaMemberCouponLinkService).saveBatch(entityList);

        // Confirm MemberBaseFeign.sendMemberCouponNotice(...).
        final MemberCouponPackageVO memberCouponPackageVO = new MemberCouponPackageVO();
        memberCouponPackageVO.setOperSubjectGuid("operSubjectGuid");
        memberCouponPackageVO.setEnterpriseGuid("enterpriseGuid");
        memberCouponPackageVO.setMemberGuid("guid");
        memberCouponPackageVO.setCouponPackageName("activityName");
        memberCouponPackageVO.setCouponPackageNum(0);
        memberCouponPackageVO.setCouponName("couponName");
        memberCouponPackageVO.setCouponType(0);
        memberCouponPackageVO.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberCouponPackageVO.setCouponNum(0);
        memberCouponPackageVO.setPhoneNum("memberPhone");
        final List<MemberCouponPackageVO> memberCouponLinks = Arrays.asList(memberCouponPackageVO);
        verify(mockMemberBaseFeign).sendMemberCouponNotice(memberCouponLinks);
    }

    @Test
    public void testXxlJobCouponPackageGrant_HsaOperationMemberInfoMapperQueueSubjectGuidReturnsNoItems() {
        // Setup
        when(mockCacheService.setLock("key")).thenReturn(false);

        // Configure MemberMarketingFeign.queryRunActivity(...).
        final EditCouponPackageActivityVO editCouponPackageActivityVO = new EditCouponPackageActivityVO();
        editCouponPackageActivityVO.setGuid("activityGuid");
        editCouponPackageActivityVO.setOperSubjectGuid("operSubjectGuid");
        editCouponPackageActivityVO.setActivityCode("activityCode");
        editCouponPackageActivityVO.setActivityName("activityName");
        editCouponPackageActivityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO.setTime("time");
        editCouponPackageActivityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO));
        final ResponseCouponDTO responseCouponDTO = new ResponseCouponDTO();
        responseCouponDTO.setCouponName("couponName");
        responseCouponDTO.setCouponCode("couponCode");
        responseCouponDTO.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO.setIsExist(0);
        responseCouponDTO.setNum(0);
        editCouponPackageActivityVO.setCouponGuidList(Arrays.asList(responseCouponDTO));
        editCouponPackageActivityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList = new CouponPackageMemberDetailsDTO();
        labelList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setLabelList(labelList);
        final CouponPackageMemberDetailsDTO gradeList = new CouponPackageMemberDetailsDTO();
        gradeList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setGradeList(gradeList);
        final CouponPackageMemberDetailsDTO memberList = new CouponPackageMemberDetailsDTO();
        memberList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setMemberList(memberList);
        final List<EditCouponPackageActivityVO> editCouponPackageActivityVOS = Arrays.asList(
                editCouponPackageActivityVO);
        when(mockMemberMarketingFeign.queryRunActivity(Arrays.asList("value")))
                .thenReturn(editCouponPackageActivityVOS);

        when(mockHsaOperationInfoMapper.queueSubjectGuid("operSubjectGuid")).thenReturn(Collections.emptySet());

        // Configure HsaOperationMemberInfoMapper.listByGuid(...).
        final MemberPhoneDTO memberPhoneDTO = new MemberPhoneDTO();
        memberPhoneDTO.setPhoneNum("memberPhone");
        memberPhoneDTO.setGuid("guid");
        memberPhoneDTO.setUserName("userName");
        memberPhoneDTO.setAccountState(0);
        memberPhoneDTO.setWechatState(0);
        final List<MemberPhoneDTO> memberPhoneDTOS = Arrays.asList(memberPhoneDTO);
        when(mockHsaOperationInfoMapper.listByGuid(Arrays.asList("value"))).thenReturn(memberPhoneDTOS);

        when(mockMapper.queueMemberGuidByGuid("activityGuid")).thenReturn(Arrays.asList("value"));
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("0baa583e-9fd1-4506-9266-2fe3b8567331");
        when(mockCacheService.getInventoryNum("key", "activityCode")).thenReturn(0);

        // Configure MemberCouponAssembler.newMemberCouponLink(...).
        final HsaMemberCouponLink hsaMemberCouponLink = new HsaMemberCouponLink();
        hsaMemberCouponLink.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink.setCouponName("couponName");
        hsaMemberCouponLink.setMemberGuid("memberGuid");
        hsaMemberCouponLink.setCouponType(0);
        hsaMemberCouponLink.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> hsaMemberCouponLinks = Arrays.asList(hsaMemberCouponLink);
        final EditCouponPackageActivityVO activityVO = new EditCouponPackageActivityVO();
        activityVO.setGuid("activityGuid");
        activityVO.setOperSubjectGuid("operSubjectGuid");
        activityVO.setActivityCode("activityCode");
        activityVO.setActivityName("activityName");
        activityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO1 = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO1.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO1.setTime("time");
        activityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO1));
        final ResponseCouponDTO responseCouponDTO1 = new ResponseCouponDTO();
        responseCouponDTO1.setCouponName("couponName");
        responseCouponDTO1.setCouponCode("couponCode");
        responseCouponDTO1.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO1.setIsExist(0);
        responseCouponDTO1.setNum(0);
        activityVO.setCouponGuidList(Arrays.asList(responseCouponDTO1));
        activityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList1 = new CouponPackageMemberDetailsDTO();
        labelList1.setGuidList(Arrays.asList("value"));
        activityVO.setLabelList(labelList1);
        final CouponPackageMemberDetailsDTO gradeList1 = new CouponPackageMemberDetailsDTO();
        gradeList1.setGuidList(Arrays.asList("value"));
        activityVO.setGradeList(gradeList1);
        final CouponPackageMemberDetailsDTO memberList1 = new CouponPackageMemberDetailsDTO();
        memberList1.setGuidList(Arrays.asList("value"));
        activityVO.setMemberList(memberList1);
        final MemberPhoneDTO memberPhoneDTO1 = new MemberPhoneDTO();
        memberPhoneDTO1.setPhoneNum("memberPhone");
        memberPhoneDTO1.setGuid("guid");
        memberPhoneDTO1.setUserName("userName");
        memberPhoneDTO1.setAccountState(0);
        memberPhoneDTO1.setWechatState(0);
        when(mockMemberCouponAssembler.newMemberCouponLink(activityVO, memberPhoneDTO1,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), new HashMap<>(),
                new HashSet<>(Arrays.asList("value")))).thenReturn(hsaMemberCouponLinks);

        // Run the test
        memberCouponPackageLinkServiceImplUnderTest.xxlJobCouponPackageGrant(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        verify(mockCacheService).lockInventoryNum("key", "activityCode", 1, 1);
        verify(mockCacheService).delete("key");
        verify(mockCacheService).updateInventoryNum("key", "activityCode");
        verify(mockCacheService).addInventoryNum("key", "activityCode");

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink1 = new HsaMemberCouponLink();
        hsaMemberCouponLink1.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink1.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink1.setCouponName("couponName");
        hsaMemberCouponLink1.setMemberGuid("memberGuid");
        hsaMemberCouponLink1.setCouponType(0);
        hsaMemberCouponLink1.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> collection = Arrays.asList(hsaMemberCouponLink1);
        verify(mockHsaMemberCouponLinkService).saveBatch(collection, 0);

        // Confirm MemberBaseFeign.addMemberInfoLabel(...).
        final AddMemberLabelCorrelationQO qo = new AddMemberLabelCorrelationQO();
        qo.setMemberInfoGuid(Arrays.asList("value"));
        qo.setLabelGuid(Arrays.asList("value"));
        qo.setBatchId("batchId");
        verify(mockMemberBaseFeign).addMemberInfoLabel(qo);

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink2 = new HsaMemberCouponLink();
        hsaMemberCouponLink2.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink2.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink2.setCouponName("couponName");
        hsaMemberCouponLink2.setMemberGuid("memberGuid");
        hsaMemberCouponLink2.setCouponType(0);
        hsaMemberCouponLink2.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> entityList = Arrays.asList(hsaMemberCouponLink2);
        verify(mockHsaMemberCouponLinkService).saveBatch(entityList);

        // Confirm MemberBaseFeign.sendMemberCouponNotice(...).
        final MemberCouponPackageVO memberCouponPackageVO = new MemberCouponPackageVO();
        memberCouponPackageVO.setOperSubjectGuid("operSubjectGuid");
        memberCouponPackageVO.setEnterpriseGuid("enterpriseGuid");
        memberCouponPackageVO.setMemberGuid("guid");
        memberCouponPackageVO.setCouponPackageName("activityName");
        memberCouponPackageVO.setCouponPackageNum(0);
        memberCouponPackageVO.setCouponName("couponName");
        memberCouponPackageVO.setCouponType(0);
        memberCouponPackageVO.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberCouponPackageVO.setCouponNum(0);
        memberCouponPackageVO.setPhoneNum("memberPhone");
        final List<MemberCouponPackageVO> memberCouponLinks = Arrays.asList(memberCouponPackageVO);
        verify(mockMemberBaseFeign).sendMemberCouponNotice(memberCouponLinks);
    }

    @Test
    public void testXxlJobCouponPackageGrant_HsaOperationMemberInfoMapperListByGuidReturnsNoItems() {
        // Setup
        when(mockCacheService.setLock("key")).thenReturn(false);

        // Configure MemberMarketingFeign.queryRunActivity(...).
        final EditCouponPackageActivityVO editCouponPackageActivityVO = new EditCouponPackageActivityVO();
        editCouponPackageActivityVO.setGuid("activityGuid");
        editCouponPackageActivityVO.setOperSubjectGuid("operSubjectGuid");
        editCouponPackageActivityVO.setActivityCode("activityCode");
        editCouponPackageActivityVO.setActivityName("activityName");
        editCouponPackageActivityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO.setTime("time");
        editCouponPackageActivityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO));
        final ResponseCouponDTO responseCouponDTO = new ResponseCouponDTO();
        responseCouponDTO.setCouponName("couponName");
        responseCouponDTO.setCouponCode("couponCode");
        responseCouponDTO.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO.setIsExist(0);
        responseCouponDTO.setNum(0);
        editCouponPackageActivityVO.setCouponGuidList(Arrays.asList(responseCouponDTO));
        editCouponPackageActivityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList = new CouponPackageMemberDetailsDTO();
        labelList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setLabelList(labelList);
        final CouponPackageMemberDetailsDTO gradeList = new CouponPackageMemberDetailsDTO();
        gradeList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setGradeList(gradeList);
        final CouponPackageMemberDetailsDTO memberList = new CouponPackageMemberDetailsDTO();
        memberList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setMemberList(memberList);
        final List<EditCouponPackageActivityVO> editCouponPackageActivityVOS = Arrays.asList(
                editCouponPackageActivityVO);
        when(mockMemberMarketingFeign.queryRunActivity(Arrays.asList("value")))
                .thenReturn(editCouponPackageActivityVOS);

        when(mockHsaOperationInfoMapper.queueSubjectGuid("operSubjectGuid"))
                .thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockHsaOperationInfoMapper.listByGuid(Arrays.asList("value"))).thenReturn(Collections.emptyList());
        when(mockMapper.queueMemberGuidByGuid("activityGuid")).thenReturn(Arrays.asList("value"));
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("0baa583e-9fd1-4506-9266-2fe3b8567331");
        when(mockCacheService.getInventoryNum("key", "activityCode")).thenReturn(0);

        // Configure MemberCouponAssembler.newMemberCouponLink(...).
        final HsaMemberCouponLink hsaMemberCouponLink = new HsaMemberCouponLink();
        hsaMemberCouponLink.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink.setCouponName("couponName");
        hsaMemberCouponLink.setMemberGuid("memberGuid");
        hsaMemberCouponLink.setCouponType(0);
        hsaMemberCouponLink.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> hsaMemberCouponLinks = Arrays.asList(hsaMemberCouponLink);
        final EditCouponPackageActivityVO activityVO = new EditCouponPackageActivityVO();
        activityVO.setGuid("activityGuid");
        activityVO.setOperSubjectGuid("operSubjectGuid");
        activityVO.setActivityCode("activityCode");
        activityVO.setActivityName("activityName");
        activityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO1 = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO1.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO1.setTime("time");
        activityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO1));
        final ResponseCouponDTO responseCouponDTO1 = new ResponseCouponDTO();
        responseCouponDTO1.setCouponName("couponName");
        responseCouponDTO1.setCouponCode("couponCode");
        responseCouponDTO1.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO1.setIsExist(0);
        responseCouponDTO1.setNum(0);
        activityVO.setCouponGuidList(Arrays.asList(responseCouponDTO1));
        activityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList1 = new CouponPackageMemberDetailsDTO();
        labelList1.setGuidList(Arrays.asList("value"));
        activityVO.setLabelList(labelList1);
        final CouponPackageMemberDetailsDTO gradeList1 = new CouponPackageMemberDetailsDTO();
        gradeList1.setGuidList(Arrays.asList("value"));
        activityVO.setGradeList(gradeList1);
        final CouponPackageMemberDetailsDTO memberList1 = new CouponPackageMemberDetailsDTO();
        memberList1.setGuidList(Arrays.asList("value"));
        activityVO.setMemberList(memberList1);
        final MemberPhoneDTO memberPhoneDTO = new MemberPhoneDTO();
        memberPhoneDTO.setPhoneNum("memberPhone");
        memberPhoneDTO.setGuid("guid");
        memberPhoneDTO.setUserName("userName");
        memberPhoneDTO.setAccountState(0);
        memberPhoneDTO.setWechatState(0);
        when(mockMemberCouponAssembler.newMemberCouponLink(activityVO, memberPhoneDTO,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), new HashMap<>(),
                new HashSet<>(Arrays.asList("value")))).thenReturn(hsaMemberCouponLinks);

        // Run the test
        memberCouponPackageLinkServiceImplUnderTest.xxlJobCouponPackageGrant(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        verify(mockCacheService).lockInventoryNum("key", "activityCode", 1, 1);
        verify(mockCacheService).delete("key");
        verify(mockCacheService).updateInventoryNum("key", "activityCode");
        verify(mockCacheService).addInventoryNum("key", "activityCode");

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink1 = new HsaMemberCouponLink();
        hsaMemberCouponLink1.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink1.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink1.setCouponName("couponName");
        hsaMemberCouponLink1.setMemberGuid("memberGuid");
        hsaMemberCouponLink1.setCouponType(0);
        hsaMemberCouponLink1.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> collection = Arrays.asList(hsaMemberCouponLink1);
        verify(mockHsaMemberCouponLinkService).saveBatch(collection, 0);

        // Confirm MemberBaseFeign.addMemberInfoLabel(...).
        final AddMemberLabelCorrelationQO qo = new AddMemberLabelCorrelationQO();
        qo.setMemberInfoGuid(Arrays.asList("value"));
        qo.setLabelGuid(Arrays.asList("value"));
        qo.setBatchId("batchId");
        verify(mockMemberBaseFeign).addMemberInfoLabel(qo);

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink2 = new HsaMemberCouponLink();
        hsaMemberCouponLink2.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink2.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink2.setCouponName("couponName");
        hsaMemberCouponLink2.setMemberGuid("memberGuid");
        hsaMemberCouponLink2.setCouponType(0);
        hsaMemberCouponLink2.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> entityList = Arrays.asList(hsaMemberCouponLink2);
        verify(mockHsaMemberCouponLinkService).saveBatch(entityList);

        // Confirm MemberBaseFeign.sendMemberCouponNotice(...).
        final MemberCouponPackageVO memberCouponPackageVO = new MemberCouponPackageVO();
        memberCouponPackageVO.setOperSubjectGuid("operSubjectGuid");
        memberCouponPackageVO.setEnterpriseGuid("enterpriseGuid");
        memberCouponPackageVO.setMemberGuid("guid");
        memberCouponPackageVO.setCouponPackageName("activityName");
        memberCouponPackageVO.setCouponPackageNum(0);
        memberCouponPackageVO.setCouponName("couponName");
        memberCouponPackageVO.setCouponType(0);
        memberCouponPackageVO.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberCouponPackageVO.setCouponNum(0);
        memberCouponPackageVO.setPhoneNum("memberPhone");
        final List<MemberCouponPackageVO> memberCouponLinks = Arrays.asList(memberCouponPackageVO);
        verify(mockMemberBaseFeign).sendMemberCouponNotice(memberCouponLinks);
    }

    @Test
    public void testXxlJobCouponPackageGrant_HsaMemberCouponPackageLinkMapperReturnsNoItems() {
        // Setup
        when(mockCacheService.setLock("key")).thenReturn(false);

        // Configure MemberMarketingFeign.queryRunActivity(...).
        final EditCouponPackageActivityVO editCouponPackageActivityVO = new EditCouponPackageActivityVO();
        editCouponPackageActivityVO.setGuid("activityGuid");
        editCouponPackageActivityVO.setOperSubjectGuid("operSubjectGuid");
        editCouponPackageActivityVO.setActivityCode("activityCode");
        editCouponPackageActivityVO.setActivityName("activityName");
        editCouponPackageActivityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO.setTime("time");
        editCouponPackageActivityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO));
        final ResponseCouponDTO responseCouponDTO = new ResponseCouponDTO();
        responseCouponDTO.setCouponName("couponName");
        responseCouponDTO.setCouponCode("couponCode");
        responseCouponDTO.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO.setIsExist(0);
        responseCouponDTO.setNum(0);
        editCouponPackageActivityVO.setCouponGuidList(Arrays.asList(responseCouponDTO));
        editCouponPackageActivityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList = new CouponPackageMemberDetailsDTO();
        labelList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setLabelList(labelList);
        final CouponPackageMemberDetailsDTO gradeList = new CouponPackageMemberDetailsDTO();
        gradeList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setGradeList(gradeList);
        final CouponPackageMemberDetailsDTO memberList = new CouponPackageMemberDetailsDTO();
        memberList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setMemberList(memberList);
        final List<EditCouponPackageActivityVO> editCouponPackageActivityVOS = Arrays.asList(
                editCouponPackageActivityVO);
        when(mockMemberMarketingFeign.queryRunActivity(Arrays.asList("value")))
                .thenReturn(editCouponPackageActivityVOS);

        when(mockHsaOperationInfoMapper.queueSubjectGuid("operSubjectGuid"))
                .thenReturn(new HashSet<>(Arrays.asList("value")));

        // Configure HsaOperationMemberInfoMapper.listByGuid(...).
        final MemberPhoneDTO memberPhoneDTO = new MemberPhoneDTO();
        memberPhoneDTO.setPhoneNum("memberPhone");
        memberPhoneDTO.setGuid("guid");
        memberPhoneDTO.setUserName("userName");
        memberPhoneDTO.setAccountState(0);
        memberPhoneDTO.setWechatState(0);
        final List<MemberPhoneDTO> memberPhoneDTOS = Arrays.asList(memberPhoneDTO);
        when(mockHsaOperationInfoMapper.listByGuid(Arrays.asList("value"))).thenReturn(memberPhoneDTOS);

        when(mockMapper.queueMemberGuidByGuid("activityGuid")).thenReturn(Collections.emptyList());
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("0baa583e-9fd1-4506-9266-2fe3b8567331");
        when(mockCacheService.getInventoryNum("key", "activityCode")).thenReturn(0);

        // Configure MemberCouponAssembler.newMemberCouponLink(...).
        final HsaMemberCouponLink hsaMemberCouponLink = new HsaMemberCouponLink();
        hsaMemberCouponLink.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink.setCouponName("couponName");
        hsaMemberCouponLink.setMemberGuid("memberGuid");
        hsaMemberCouponLink.setCouponType(0);
        hsaMemberCouponLink.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> hsaMemberCouponLinks = Arrays.asList(hsaMemberCouponLink);
        final EditCouponPackageActivityVO activityVO = new EditCouponPackageActivityVO();
        activityVO.setGuid("activityGuid");
        activityVO.setOperSubjectGuid("operSubjectGuid");
        activityVO.setActivityCode("activityCode");
        activityVO.setActivityName("activityName");
        activityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO1 = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO1.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO1.setTime("time");
        activityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO1));
        final ResponseCouponDTO responseCouponDTO1 = new ResponseCouponDTO();
        responseCouponDTO1.setCouponName("couponName");
        responseCouponDTO1.setCouponCode("couponCode");
        responseCouponDTO1.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO1.setIsExist(0);
        responseCouponDTO1.setNum(0);
        activityVO.setCouponGuidList(Arrays.asList(responseCouponDTO1));
        activityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList1 = new CouponPackageMemberDetailsDTO();
        labelList1.setGuidList(Arrays.asList("value"));
        activityVO.setLabelList(labelList1);
        final CouponPackageMemberDetailsDTO gradeList1 = new CouponPackageMemberDetailsDTO();
        gradeList1.setGuidList(Arrays.asList("value"));
        activityVO.setGradeList(gradeList1);
        final CouponPackageMemberDetailsDTO memberList1 = new CouponPackageMemberDetailsDTO();
        memberList1.setGuidList(Arrays.asList("value"));
        activityVO.setMemberList(memberList1);
        final MemberPhoneDTO memberPhoneDTO1 = new MemberPhoneDTO();
        memberPhoneDTO1.setPhoneNum("memberPhone");
        memberPhoneDTO1.setGuid("guid");
        memberPhoneDTO1.setUserName("userName");
        memberPhoneDTO1.setAccountState(0);
        memberPhoneDTO1.setWechatState(0);
        when(mockMemberCouponAssembler.newMemberCouponLink(activityVO, memberPhoneDTO1,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), new HashMap<>(),
                new HashSet<>(Arrays.asList("value")))).thenReturn(hsaMemberCouponLinks);

        // Run the test
        memberCouponPackageLinkServiceImplUnderTest.xxlJobCouponPackageGrant(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        verify(mockCacheService).lockInventoryNum("key", "activityCode", 1, 1);
        verify(mockCacheService).delete("key");
        verify(mockCacheService).updateInventoryNum("key", "activityCode");
        verify(mockCacheService).addInventoryNum("key", "activityCode");

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink1 = new HsaMemberCouponLink();
        hsaMemberCouponLink1.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink1.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink1.setCouponName("couponName");
        hsaMemberCouponLink1.setMemberGuid("memberGuid");
        hsaMemberCouponLink1.setCouponType(0);
        hsaMemberCouponLink1.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> collection = Arrays.asList(hsaMemberCouponLink1);
        verify(mockHsaMemberCouponLinkService).saveBatch(collection, 0);

        // Confirm MemberBaseFeign.addMemberInfoLabel(...).
        final AddMemberLabelCorrelationQO qo = new AddMemberLabelCorrelationQO();
        qo.setMemberInfoGuid(Arrays.asList("value"));
        qo.setLabelGuid(Arrays.asList("value"));
        qo.setBatchId("batchId");
        verify(mockMemberBaseFeign).addMemberInfoLabel(qo);

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink2 = new HsaMemberCouponLink();
        hsaMemberCouponLink2.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink2.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink2.setCouponName("couponName");
        hsaMemberCouponLink2.setMemberGuid("memberGuid");
        hsaMemberCouponLink2.setCouponType(0);
        hsaMemberCouponLink2.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> entityList = Arrays.asList(hsaMemberCouponLink2);
        verify(mockHsaMemberCouponLinkService).saveBatch(entityList);

        // Confirm MemberBaseFeign.sendMemberCouponNotice(...).
        final MemberCouponPackageVO memberCouponPackageVO = new MemberCouponPackageVO();
        memberCouponPackageVO.setOperSubjectGuid("operSubjectGuid");
        memberCouponPackageVO.setEnterpriseGuid("enterpriseGuid");
        memberCouponPackageVO.setMemberGuid("guid");
        memberCouponPackageVO.setCouponPackageName("activityName");
        memberCouponPackageVO.setCouponPackageNum(0);
        memberCouponPackageVO.setCouponName("couponName");
        memberCouponPackageVO.setCouponType(0);
        memberCouponPackageVO.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberCouponPackageVO.setCouponNum(0);
        memberCouponPackageVO.setPhoneNum("memberPhone");
        final List<MemberCouponPackageVO> memberCouponLinks = Arrays.asList(memberCouponPackageVO);
        verify(mockMemberBaseFeign).sendMemberCouponNotice(memberCouponLinks);
    }

    @Test
    public void testXxlJobCouponPackageGrant_HsaMemberLabelMapperReturnsNoItems() {
        // Setup
        when(mockCacheService.setLock("key")).thenReturn(false);

        // Configure MemberMarketingFeign.queryRunActivity(...).
        final EditCouponPackageActivityVO editCouponPackageActivityVO = new EditCouponPackageActivityVO();
        editCouponPackageActivityVO.setGuid("activityGuid");
        editCouponPackageActivityVO.setOperSubjectGuid("operSubjectGuid");
        editCouponPackageActivityVO.setActivityCode("activityCode");
        editCouponPackageActivityVO.setActivityName("activityName");
        editCouponPackageActivityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO.setTime("time");
        editCouponPackageActivityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO));
        final ResponseCouponDTO responseCouponDTO = new ResponseCouponDTO();
        responseCouponDTO.setCouponName("couponName");
        responseCouponDTO.setCouponCode("couponCode");
        responseCouponDTO.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO.setIsExist(0);
        responseCouponDTO.setNum(0);
        editCouponPackageActivityVO.setCouponGuidList(Arrays.asList(responseCouponDTO));
        editCouponPackageActivityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList = new CouponPackageMemberDetailsDTO();
        labelList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setLabelList(labelList);
        final CouponPackageMemberDetailsDTO gradeList = new CouponPackageMemberDetailsDTO();
        gradeList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setGradeList(gradeList);
        final CouponPackageMemberDetailsDTO memberList = new CouponPackageMemberDetailsDTO();
        memberList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setMemberList(memberList);
        final List<EditCouponPackageActivityVO> editCouponPackageActivityVOS = Arrays.asList(
                editCouponPackageActivityVO);
        when(mockMemberMarketingFeign.queryRunActivity(Arrays.asList("value")))
                .thenReturn(editCouponPackageActivityVOS);

        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure HsaOperationMemberInfoMapper.listByGuid(...).
        final MemberPhoneDTO memberPhoneDTO = new MemberPhoneDTO();
        memberPhoneDTO.setPhoneNum("memberPhone");
        memberPhoneDTO.setGuid("guid");
        memberPhoneDTO.setUserName("userName");
        memberPhoneDTO.setAccountState(0);
        memberPhoneDTO.setWechatState(0);
        final List<MemberPhoneDTO> memberPhoneDTOS = Arrays.asList(memberPhoneDTO);
        when(mockHsaOperationInfoMapper.listByGuid(Arrays.asList("value"))).thenReturn(memberPhoneDTOS);

        when(mockMapper.queueMemberGuidByGuid("activityGuid")).thenReturn(Arrays.asList("value"));
        when(mockHsaOperationInfoMapper.queryMemberGrade(Arrays.asList("value"))).thenReturn(Arrays.asList("value"));
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("0baa583e-9fd1-4506-9266-2fe3b8567331");
        when(mockCacheService.getInventoryNum("key", "activityCode")).thenReturn(0);

        // Configure MemberCouponAssembler.newMemberCouponLink(...).
        final HsaMemberCouponLink hsaMemberCouponLink = new HsaMemberCouponLink();
        hsaMemberCouponLink.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink.setCouponName("couponName");
        hsaMemberCouponLink.setMemberGuid("memberGuid");
        hsaMemberCouponLink.setCouponType(0);
        hsaMemberCouponLink.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> hsaMemberCouponLinks = Arrays.asList(hsaMemberCouponLink);
        final EditCouponPackageActivityVO activityVO = new EditCouponPackageActivityVO();
        activityVO.setGuid("activityGuid");
        activityVO.setOperSubjectGuid("operSubjectGuid");
        activityVO.setActivityCode("activityCode");
        activityVO.setActivityName("activityName");
        activityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO1 = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO1.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO1.setTime("time");
        activityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO1));
        final ResponseCouponDTO responseCouponDTO1 = new ResponseCouponDTO();
        responseCouponDTO1.setCouponName("couponName");
        responseCouponDTO1.setCouponCode("couponCode");
        responseCouponDTO1.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO1.setIsExist(0);
        responseCouponDTO1.setNum(0);
        activityVO.setCouponGuidList(Arrays.asList(responseCouponDTO1));
        activityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList1 = new CouponPackageMemberDetailsDTO();
        labelList1.setGuidList(Arrays.asList("value"));
        activityVO.setLabelList(labelList1);
        final CouponPackageMemberDetailsDTO gradeList1 = new CouponPackageMemberDetailsDTO();
        gradeList1.setGuidList(Arrays.asList("value"));
        activityVO.setGradeList(gradeList1);
        final CouponPackageMemberDetailsDTO memberList1 = new CouponPackageMemberDetailsDTO();
        memberList1.setGuidList(Arrays.asList("value"));
        activityVO.setMemberList(memberList1);
        final MemberPhoneDTO memberPhoneDTO1 = new MemberPhoneDTO();
        memberPhoneDTO1.setPhoneNum("memberPhone");
        memberPhoneDTO1.setGuid("guid");
        memberPhoneDTO1.setUserName("userName");
        memberPhoneDTO1.setAccountState(0);
        memberPhoneDTO1.setWechatState(0);
        when(mockMemberCouponAssembler.newMemberCouponLink(activityVO, memberPhoneDTO1,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), new HashMap<>(),
                new HashSet<>(Arrays.asList("value")))).thenReturn(hsaMemberCouponLinks);

        // Run the test
        memberCouponPackageLinkServiceImplUnderTest.xxlJobCouponPackageGrant(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        verify(mockCacheService).lockInventoryNum("key", "activityCode", 1, 1);
        verify(mockCacheService).delete("key");
        verify(mockCacheService).updateInventoryNum("key", "activityCode");
        verify(mockCacheService).addInventoryNum("key", "activityCode");

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink1 = new HsaMemberCouponLink();
        hsaMemberCouponLink1.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink1.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink1.setCouponName("couponName");
        hsaMemberCouponLink1.setMemberGuid("memberGuid");
        hsaMemberCouponLink1.setCouponType(0);
        hsaMemberCouponLink1.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> collection = Arrays.asList(hsaMemberCouponLink1);
        verify(mockHsaMemberCouponLinkService).saveBatch(collection, 0);

        // Confirm MemberBaseFeign.addMemberInfoLabel(...).
        final AddMemberLabelCorrelationQO qo = new AddMemberLabelCorrelationQO();
        qo.setMemberInfoGuid(Arrays.asList("value"));
        qo.setLabelGuid(Arrays.asList("value"));
        qo.setBatchId("batchId");
        verify(mockMemberBaseFeign).addMemberInfoLabel(qo);

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink2 = new HsaMemberCouponLink();
        hsaMemberCouponLink2.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink2.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink2.setCouponName("couponName");
        hsaMemberCouponLink2.setMemberGuid("memberGuid");
        hsaMemberCouponLink2.setCouponType(0);
        hsaMemberCouponLink2.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> entityList = Arrays.asList(hsaMemberCouponLink2);
        verify(mockHsaMemberCouponLinkService).saveBatch(entityList);

        // Confirm MemberBaseFeign.sendMemberCouponNotice(...).
        final MemberCouponPackageVO memberCouponPackageVO = new MemberCouponPackageVO();
        memberCouponPackageVO.setOperSubjectGuid("operSubjectGuid");
        memberCouponPackageVO.setEnterpriseGuid("enterpriseGuid");
        memberCouponPackageVO.setMemberGuid("guid");
        memberCouponPackageVO.setCouponPackageName("activityName");
        memberCouponPackageVO.setCouponPackageNum(0);
        memberCouponPackageVO.setCouponName("couponName");
        memberCouponPackageVO.setCouponType(0);
        memberCouponPackageVO.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberCouponPackageVO.setCouponNum(0);
        memberCouponPackageVO.setPhoneNum("memberPhone");
        final List<MemberCouponPackageVO> memberCouponLinks = Arrays.asList(memberCouponPackageVO);
        verify(mockMemberBaseFeign).sendMemberCouponNotice(memberCouponLinks);
    }

    @Test
    public void testXxlJobCouponPackageGrant_HsaOperationMemberInfoMapperQueryMemberGradeReturnsNoItems() {
        // Setup
        when(mockCacheService.setLock("key")).thenReturn(false);

        // Configure MemberMarketingFeign.queryRunActivity(...).
        final EditCouponPackageActivityVO editCouponPackageActivityVO = new EditCouponPackageActivityVO();
        editCouponPackageActivityVO.setGuid("activityGuid");
        editCouponPackageActivityVO.setOperSubjectGuid("operSubjectGuid");
        editCouponPackageActivityVO.setActivityCode("activityCode");
        editCouponPackageActivityVO.setActivityName("activityName");
        editCouponPackageActivityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO.setTime("time");
        editCouponPackageActivityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO));
        final ResponseCouponDTO responseCouponDTO = new ResponseCouponDTO();
        responseCouponDTO.setCouponName("couponName");
        responseCouponDTO.setCouponCode("couponCode");
        responseCouponDTO.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO.setIsExist(0);
        responseCouponDTO.setNum(0);
        editCouponPackageActivityVO.setCouponGuidList(Arrays.asList(responseCouponDTO));
        editCouponPackageActivityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList = new CouponPackageMemberDetailsDTO();
        labelList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setLabelList(labelList);
        final CouponPackageMemberDetailsDTO gradeList = new CouponPackageMemberDetailsDTO();
        gradeList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setGradeList(gradeList);
        final CouponPackageMemberDetailsDTO memberList = new CouponPackageMemberDetailsDTO();
        memberList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setMemberList(memberList);
        final List<EditCouponPackageActivityVO> editCouponPackageActivityVOS = Arrays.asList(
                editCouponPackageActivityVO);
        when(mockMemberMarketingFeign.queryRunActivity(Arrays.asList("value")))
                .thenReturn(editCouponPackageActivityVOS);

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("dc306173-1358-4df9-a481-d9d5a4c1e589");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("operationMemberInfoGuid");
        hsaMemberLabel.setOperatorGuid("operatorGuid");
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        when(mockHsaOperationInfoMapper.queryMemberGrade(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure HsaOperationMemberInfoMapper.listByGuid(...).
        final MemberPhoneDTO memberPhoneDTO = new MemberPhoneDTO();
        memberPhoneDTO.setPhoneNum("memberPhone");
        memberPhoneDTO.setGuid("guid");
        memberPhoneDTO.setUserName("userName");
        memberPhoneDTO.setAccountState(0);
        memberPhoneDTO.setWechatState(0);
        final List<MemberPhoneDTO> memberPhoneDTOS = Arrays.asList(memberPhoneDTO);
        when(mockHsaOperationInfoMapper.listByGuid(Arrays.asList("value"))).thenReturn(memberPhoneDTOS);

        when(mockMapper.queueMemberGuidByGuid("activityGuid")).thenReturn(Arrays.asList("value"));
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("0baa583e-9fd1-4506-9266-2fe3b8567331");
        when(mockCacheService.getInventoryNum("key", "activityCode")).thenReturn(0);

        // Configure MemberCouponAssembler.newMemberCouponLink(...).
        final HsaMemberCouponLink hsaMemberCouponLink = new HsaMemberCouponLink();
        hsaMemberCouponLink.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink.setCouponName("couponName");
        hsaMemberCouponLink.setMemberGuid("memberGuid");
        hsaMemberCouponLink.setCouponType(0);
        hsaMemberCouponLink.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> hsaMemberCouponLinks = Arrays.asList(hsaMemberCouponLink);
        final EditCouponPackageActivityVO activityVO = new EditCouponPackageActivityVO();
        activityVO.setGuid("activityGuid");
        activityVO.setOperSubjectGuid("operSubjectGuid");
        activityVO.setActivityCode("activityCode");
        activityVO.setActivityName("activityName");
        activityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO1 = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO1.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO1.setTime("time");
        activityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO1));
        final ResponseCouponDTO responseCouponDTO1 = new ResponseCouponDTO();
        responseCouponDTO1.setCouponName("couponName");
        responseCouponDTO1.setCouponCode("couponCode");
        responseCouponDTO1.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO1.setIsExist(0);
        responseCouponDTO1.setNum(0);
        activityVO.setCouponGuidList(Arrays.asList(responseCouponDTO1));
        activityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList1 = new CouponPackageMemberDetailsDTO();
        labelList1.setGuidList(Arrays.asList("value"));
        activityVO.setLabelList(labelList1);
        final CouponPackageMemberDetailsDTO gradeList1 = new CouponPackageMemberDetailsDTO();
        gradeList1.setGuidList(Arrays.asList("value"));
        activityVO.setGradeList(gradeList1);
        final CouponPackageMemberDetailsDTO memberList1 = new CouponPackageMemberDetailsDTO();
        memberList1.setGuidList(Arrays.asList("value"));
        activityVO.setMemberList(memberList1);
        final MemberPhoneDTO memberPhoneDTO1 = new MemberPhoneDTO();
        memberPhoneDTO1.setPhoneNum("memberPhone");
        memberPhoneDTO1.setGuid("guid");
        memberPhoneDTO1.setUserName("userName");
        memberPhoneDTO1.setAccountState(0);
        memberPhoneDTO1.setWechatState(0);
        when(mockMemberCouponAssembler.newMemberCouponLink(activityVO, memberPhoneDTO1,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), new HashMap<>(),
                new HashSet<>(Arrays.asList("value")))).thenReturn(hsaMemberCouponLinks);

        // Run the test
        memberCouponPackageLinkServiceImplUnderTest.xxlJobCouponPackageGrant(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        verify(mockCacheService).lockInventoryNum("key", "activityCode", 1, 1);
        verify(mockCacheService).delete("key");
        verify(mockCacheService).updateInventoryNum("key", "activityCode");
        verify(mockCacheService).addInventoryNum("key", "activityCode");

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink1 = new HsaMemberCouponLink();
        hsaMemberCouponLink1.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink1.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink1.setCouponName("couponName");
        hsaMemberCouponLink1.setMemberGuid("memberGuid");
        hsaMemberCouponLink1.setCouponType(0);
        hsaMemberCouponLink1.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> collection = Arrays.asList(hsaMemberCouponLink1);
        verify(mockHsaMemberCouponLinkService).saveBatch(collection, 0);

        // Confirm MemberBaseFeign.addMemberInfoLabel(...).
        final AddMemberLabelCorrelationQO qo = new AddMemberLabelCorrelationQO();
        qo.setMemberInfoGuid(Arrays.asList("value"));
        qo.setLabelGuid(Arrays.asList("value"));
        qo.setBatchId("batchId");
        verify(mockMemberBaseFeign).addMemberInfoLabel(qo);

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink2 = new HsaMemberCouponLink();
        hsaMemberCouponLink2.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink2.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink2.setCouponName("couponName");
        hsaMemberCouponLink2.setMemberGuid("memberGuid");
        hsaMemberCouponLink2.setCouponType(0);
        hsaMemberCouponLink2.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> entityList = Arrays.asList(hsaMemberCouponLink2);
        verify(mockHsaMemberCouponLinkService).saveBatch(entityList);

        // Confirm MemberBaseFeign.sendMemberCouponNotice(...).
        final MemberCouponPackageVO memberCouponPackageVO = new MemberCouponPackageVO();
        memberCouponPackageVO.setOperSubjectGuid("operSubjectGuid");
        memberCouponPackageVO.setEnterpriseGuid("enterpriseGuid");
        memberCouponPackageVO.setMemberGuid("guid");
        memberCouponPackageVO.setCouponPackageName("activityName");
        memberCouponPackageVO.setCouponPackageNum(0);
        memberCouponPackageVO.setCouponName("couponName");
        memberCouponPackageVO.setCouponType(0);
        memberCouponPackageVO.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberCouponPackageVO.setCouponNum(0);
        memberCouponPackageVO.setPhoneNum("memberPhone");
        final List<MemberCouponPackageVO> memberCouponLinks = Arrays.asList(memberCouponPackageVO);
        verify(mockMemberBaseFeign).sendMemberCouponNotice(memberCouponLinks);
    }

    @Test
    public void testXxlJobCouponPackageGrant_MemberCouponAssemblerReturnsNoItems() {
        // Setup
        when(mockCacheService.setLock("key")).thenReturn(false);

        // Configure MemberMarketingFeign.queryRunActivity(...).
        final EditCouponPackageActivityVO editCouponPackageActivityVO = new EditCouponPackageActivityVO();
        editCouponPackageActivityVO.setGuid("activityGuid");
        editCouponPackageActivityVO.setOperSubjectGuid("operSubjectGuid");
        editCouponPackageActivityVO.setActivityCode("activityCode");
        editCouponPackageActivityVO.setActivityName("activityName");
        editCouponPackageActivityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO.setTime("time");
        editCouponPackageActivityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO));
        final ResponseCouponDTO responseCouponDTO = new ResponseCouponDTO();
        responseCouponDTO.setCouponName("couponName");
        responseCouponDTO.setCouponCode("couponCode");
        responseCouponDTO.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO.setIsExist(0);
        responseCouponDTO.setNum(0);
        editCouponPackageActivityVO.setCouponGuidList(Arrays.asList(responseCouponDTO));
        editCouponPackageActivityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList = new CouponPackageMemberDetailsDTO();
        labelList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setLabelList(labelList);
        final CouponPackageMemberDetailsDTO gradeList = new CouponPackageMemberDetailsDTO();
        gradeList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setGradeList(gradeList);
        final CouponPackageMemberDetailsDTO memberList = new CouponPackageMemberDetailsDTO();
        memberList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setMemberList(memberList);
        final List<EditCouponPackageActivityVO> editCouponPackageActivityVOS = Arrays.asList(
                editCouponPackageActivityVO);
        when(mockMemberMarketingFeign.queryRunActivity(Arrays.asList("value")))
                .thenReturn(editCouponPackageActivityVOS);

        when(mockHsaOperationInfoMapper.queueSubjectGuid("operSubjectGuid"))
                .thenReturn(new HashSet<>(Arrays.asList("value")));

        // Configure HsaOperationMemberInfoMapper.listByGuid(...).
        final MemberPhoneDTO memberPhoneDTO = new MemberPhoneDTO();
        memberPhoneDTO.setPhoneNum("memberPhone");
        memberPhoneDTO.setGuid("guid");
        memberPhoneDTO.setUserName("userName");
        memberPhoneDTO.setAccountState(0);
        memberPhoneDTO.setWechatState(0);
        final List<MemberPhoneDTO> memberPhoneDTOS = Arrays.asList(memberPhoneDTO);
        when(mockHsaOperationInfoMapper.listByGuid(Arrays.asList("value"))).thenReturn(memberPhoneDTOS);

        when(mockMapper.queueMemberGuidByGuid("activityGuid")).thenReturn(Arrays.asList("value"));
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("0baa583e-9fd1-4506-9266-2fe3b8567331");
        when(mockCacheService.getInventoryNum("key", "activityCode")).thenReturn(0);

        // Configure MemberCouponAssembler.newMemberCouponLink(...).
        final EditCouponPackageActivityVO activityVO = new EditCouponPackageActivityVO();
        activityVO.setGuid("activityGuid");
        activityVO.setOperSubjectGuid("operSubjectGuid");
        activityVO.setActivityCode("activityCode");
        activityVO.setActivityName("activityName");
        activityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO1 = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO1.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO1.setTime("time");
        activityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO1));
        final ResponseCouponDTO responseCouponDTO1 = new ResponseCouponDTO();
        responseCouponDTO1.setCouponName("couponName");
        responseCouponDTO1.setCouponCode("couponCode");
        responseCouponDTO1.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO1.setIsExist(0);
        responseCouponDTO1.setNum(0);
        activityVO.setCouponGuidList(Arrays.asList(responseCouponDTO1));
        activityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList1 = new CouponPackageMemberDetailsDTO();
        labelList1.setGuidList(Arrays.asList("value"));
        activityVO.setLabelList(labelList1);
        final CouponPackageMemberDetailsDTO gradeList1 = new CouponPackageMemberDetailsDTO();
        gradeList1.setGuidList(Arrays.asList("value"));
        activityVO.setGradeList(gradeList1);
        final CouponPackageMemberDetailsDTO memberList1 = new CouponPackageMemberDetailsDTO();
        memberList1.setGuidList(Arrays.asList("value"));
        activityVO.setMemberList(memberList1);
        final MemberPhoneDTO memberPhoneDTO1 = new MemberPhoneDTO();
        memberPhoneDTO1.setPhoneNum("memberPhone");
        memberPhoneDTO1.setGuid("guid");
        memberPhoneDTO1.setUserName("userName");
        memberPhoneDTO1.setAccountState(0);
        memberPhoneDTO1.setWechatState(0);
        when(mockMemberCouponAssembler.newMemberCouponLink(activityVO, memberPhoneDTO1,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), new HashMap<>(),
                new HashSet<>(Arrays.asList("value")))).thenReturn(Collections.emptyList());

        // Run the test
        memberCouponPackageLinkServiceImplUnderTest.xxlJobCouponPackageGrant(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        verify(mockCacheService).lockInventoryNum("key", "activityCode", 1, 1);
        verify(mockCacheService).updateInventoryNum("key", "activityCode");
        verify(mockCacheService).addInventoryNum("key", "activityCode");
        verify(mockCacheService).delete("key");

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink = new HsaMemberCouponLink();
        hsaMemberCouponLink.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink.setCouponName("couponName");
        hsaMemberCouponLink.setMemberGuid("memberGuid");
        hsaMemberCouponLink.setCouponType(0);
        hsaMemberCouponLink.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> collection = Arrays.asList(hsaMemberCouponLink);
        verify(mockHsaMemberCouponLinkService).saveBatch(collection, 0);

        // Confirm MemberBaseFeign.addMemberInfoLabel(...).
        final AddMemberLabelCorrelationQO qo = new AddMemberLabelCorrelationQO();
        qo.setMemberInfoGuid(Arrays.asList("value"));
        qo.setLabelGuid(Arrays.asList("value"));
        qo.setBatchId("batchId");
        verify(mockMemberBaseFeign).addMemberInfoLabel(qo);

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink1 = new HsaMemberCouponLink();
        hsaMemberCouponLink1.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink1.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink1.setCouponName("couponName");
        hsaMemberCouponLink1.setMemberGuid("memberGuid");
        hsaMemberCouponLink1.setCouponType(0);
        hsaMemberCouponLink1.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> entityList = Arrays.asList(hsaMemberCouponLink1);
        verify(mockHsaMemberCouponLinkService).saveBatch(entityList);

        // Confirm MemberBaseFeign.sendMemberCouponNotice(...).
        final MemberCouponPackageVO memberCouponPackageVO = new MemberCouponPackageVO();
        memberCouponPackageVO.setOperSubjectGuid("operSubjectGuid");
        memberCouponPackageVO.setEnterpriseGuid("enterpriseGuid");
        memberCouponPackageVO.setMemberGuid("guid");
        memberCouponPackageVO.setCouponPackageName("activityName");
        memberCouponPackageVO.setCouponPackageNum(0);
        memberCouponPackageVO.setCouponName("couponName");
        memberCouponPackageVO.setCouponType(0);
        memberCouponPackageVO.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberCouponPackageVO.setCouponNum(0);
        memberCouponPackageVO.setPhoneNum("memberPhone");
        final List<MemberCouponPackageVO> memberCouponLinks = Arrays.asList(memberCouponPackageVO);
        verify(mockMemberBaseFeign).sendMemberCouponNotice(memberCouponLinks);
    }

    @Test
    public void testCouponPackageGrant() {
        // Setup
        final CouponPackageGrantVO couponPackageGrantVO = new CouponPackageGrantVO();
        final EditCouponPackageActivityVO editCouponPackageActivityVO = new EditCouponPackageActivityVO();
        editCouponPackageActivityVO.setGuid("activityGuid");
        editCouponPackageActivityVO.setOperSubjectGuid("operSubjectGuid");
        editCouponPackageActivityVO.setActivityCode("activityCode");
        editCouponPackageActivityVO.setActivityName("activityName");
        editCouponPackageActivityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO.setTime("time");
        editCouponPackageActivityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO));
        final ResponseCouponDTO responseCouponDTO = new ResponseCouponDTO();
        responseCouponDTO.setCouponName("couponName");
        responseCouponDTO.setCouponCode("couponCode");
        responseCouponDTO.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO.setIsExist(0);
        responseCouponDTO.setNum(0);
        editCouponPackageActivityVO.setCouponGuidList(Arrays.asList(responseCouponDTO));
        editCouponPackageActivityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList = new CouponPackageMemberDetailsDTO();
        labelList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setLabelList(labelList);
        final CouponPackageMemberDetailsDTO gradeList = new CouponPackageMemberDetailsDTO();
        gradeList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setGradeList(gradeList);
        final CouponPackageMemberDetailsDTO memberList = new CouponPackageMemberDetailsDTO();
        memberList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setMemberList(memberList);
        couponPackageGrantVO.setCouponPackageGrantList(Arrays.asList(editCouponPackageActivityVO));
        couponPackageGrantVO.setCurrentTimeMillis("currentTimeMillis");

        when(mockHsaOperationInfoMapper.queueSubjectGuid("operSubjectGuid"))
                .thenReturn(new HashSet<>(Arrays.asList("value")));

        // Configure HsaOperationMemberInfoMapper.listByGuid(...).
        final MemberPhoneDTO memberPhoneDTO = new MemberPhoneDTO();
        memberPhoneDTO.setPhoneNum("memberPhone");
        memberPhoneDTO.setGuid("guid");
        memberPhoneDTO.setUserName("userName");
        memberPhoneDTO.setAccountState(0);
        memberPhoneDTO.setWechatState(0);
        final List<MemberPhoneDTO> memberPhoneDTOS = Arrays.asList(memberPhoneDTO);
        when(mockHsaOperationInfoMapper.listByGuid(Arrays.asList("value"))).thenReturn(memberPhoneDTOS);

        when(mockMapper.queueMemberGuidByGuid("activityGuid")).thenReturn(Arrays.asList("value"));

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("dc306173-1358-4df9-a481-d9d5a4c1e589");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("operationMemberInfoGuid");
        hsaMemberLabel.setOperatorGuid("operatorGuid");
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        when(mockHsaOperationInfoMapper.queryMemberGrade(Arrays.asList("value"))).thenReturn(Arrays.asList("value"));
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("0baa583e-9fd1-4506-9266-2fe3b8567331");
        when(mockCacheService.getInventoryNum("key", "activityCode")).thenReturn(0);

        // Configure MemberCouponAssembler.newMemberCouponLink(...).
        final HsaMemberCouponLink hsaMemberCouponLink = new HsaMemberCouponLink();
        hsaMemberCouponLink.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink.setCouponName("couponName");
        hsaMemberCouponLink.setMemberGuid("memberGuid");
        hsaMemberCouponLink.setCouponType(0);
        hsaMemberCouponLink.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> hsaMemberCouponLinks = Arrays.asList(hsaMemberCouponLink);
        final EditCouponPackageActivityVO activityVO = new EditCouponPackageActivityVO();
        activityVO.setGuid("activityGuid");
        activityVO.setOperSubjectGuid("operSubjectGuid");
        activityVO.setActivityCode("activityCode");
        activityVO.setActivityName("activityName");
        activityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO1 = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO1.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO1.setTime("time");
        activityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO1));
        final ResponseCouponDTO responseCouponDTO1 = new ResponseCouponDTO();
        responseCouponDTO1.setCouponName("couponName");
        responseCouponDTO1.setCouponCode("couponCode");
        responseCouponDTO1.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO1.setIsExist(0);
        responseCouponDTO1.setNum(0);
        activityVO.setCouponGuidList(Arrays.asList(responseCouponDTO1));
        activityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList1 = new CouponPackageMemberDetailsDTO();
        labelList1.setGuidList(Arrays.asList("value"));
        activityVO.setLabelList(labelList1);
        final CouponPackageMemberDetailsDTO gradeList1 = new CouponPackageMemberDetailsDTO();
        gradeList1.setGuidList(Arrays.asList("value"));
        activityVO.setGradeList(gradeList1);
        final CouponPackageMemberDetailsDTO memberList1 = new CouponPackageMemberDetailsDTO();
        memberList1.setGuidList(Arrays.asList("value"));
        activityVO.setMemberList(memberList1);
        final MemberPhoneDTO memberPhoneDTO1 = new MemberPhoneDTO();
        memberPhoneDTO1.setPhoneNum("memberPhone");
        memberPhoneDTO1.setGuid("guid");
        memberPhoneDTO1.setUserName("userName");
        memberPhoneDTO1.setAccountState(0);
        memberPhoneDTO1.setWechatState(0);
        when(mockMemberCouponAssembler.newMemberCouponLink(activityVO, memberPhoneDTO1,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), new HashMap<>(),
                new HashSet<>(Arrays.asList("value")))).thenReturn(hsaMemberCouponLinks);

        // Run the test
        memberCouponPackageLinkServiceImplUnderTest.couponPackageGrant(couponPackageGrantVO);

        // Verify the results
        verify(mockCacheService).lockInventoryNum("key", "activityCode", 1, 1);
        verify(mockCacheService).updateInventoryNum("key", "activityCode");
        verify(mockCacheService).addInventoryNum("key", "activityCode");

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink1 = new HsaMemberCouponLink();
        hsaMemberCouponLink1.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink1.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink1.setCouponName("couponName");
        hsaMemberCouponLink1.setMemberGuid("memberGuid");
        hsaMemberCouponLink1.setCouponType(0);
        hsaMemberCouponLink1.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> collection = Arrays.asList(hsaMemberCouponLink1);
        verify(mockHsaMemberCouponLinkService).saveBatch(collection, 0);

        // Confirm MemberBaseFeign.addMemberInfoLabel(...).
        final AddMemberLabelCorrelationQO qo = new AddMemberLabelCorrelationQO();
        qo.setMemberInfoGuid(Arrays.asList("value"));
        qo.setLabelGuid(Arrays.asList("value"));
        qo.setBatchId("batchId");
        verify(mockMemberBaseFeign).addMemberInfoLabel(qo);

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink2 = new HsaMemberCouponLink();
        hsaMemberCouponLink2.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink2.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink2.setCouponName("couponName");
        hsaMemberCouponLink2.setMemberGuid("memberGuid");
        hsaMemberCouponLink2.setCouponType(0);
        hsaMemberCouponLink2.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> entityList = Arrays.asList(hsaMemberCouponLink2);
        verify(mockHsaMemberCouponLinkService).saveBatch(entityList);

        // Confirm MemberBaseFeign.sendMemberCouponNotice(...).
        final MemberCouponPackageVO memberCouponPackageVO = new MemberCouponPackageVO();
        memberCouponPackageVO.setOperSubjectGuid("operSubjectGuid");
        memberCouponPackageVO.setEnterpriseGuid("enterpriseGuid");
        memberCouponPackageVO.setMemberGuid("guid");
        memberCouponPackageVO.setCouponPackageName("activityName");
        memberCouponPackageVO.setCouponPackageNum(0);
        memberCouponPackageVO.setCouponName("couponName");
        memberCouponPackageVO.setCouponType(0);
        memberCouponPackageVO.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberCouponPackageVO.setCouponNum(0);
        memberCouponPackageVO.setPhoneNum("memberPhone");
        final List<MemberCouponPackageVO> memberCouponLinks = Arrays.asList(memberCouponPackageVO);
        verify(mockMemberBaseFeign).sendMemberCouponNotice(memberCouponLinks);
        verify(mockCacheService).delete("key");
    }

    @Test
    public void testCouponPackageGrant_HsaOperationMemberInfoMapperQueueSubjectGuidReturnsNoItems() {
        // Setup
        final CouponPackageGrantVO couponPackageGrantVO = new CouponPackageGrantVO();
        final EditCouponPackageActivityVO editCouponPackageActivityVO = new EditCouponPackageActivityVO();
        editCouponPackageActivityVO.setGuid("activityGuid");
        editCouponPackageActivityVO.setOperSubjectGuid("operSubjectGuid");
        editCouponPackageActivityVO.setActivityCode("activityCode");
        editCouponPackageActivityVO.setActivityName("activityName");
        editCouponPackageActivityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO.setTime("time");
        editCouponPackageActivityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO));
        final ResponseCouponDTO responseCouponDTO = new ResponseCouponDTO();
        responseCouponDTO.setCouponName("couponName");
        responseCouponDTO.setCouponCode("couponCode");
        responseCouponDTO.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO.setIsExist(0);
        responseCouponDTO.setNum(0);
        editCouponPackageActivityVO.setCouponGuidList(Arrays.asList(responseCouponDTO));
        editCouponPackageActivityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList = new CouponPackageMemberDetailsDTO();
        labelList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setLabelList(labelList);
        final CouponPackageMemberDetailsDTO gradeList = new CouponPackageMemberDetailsDTO();
        gradeList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setGradeList(gradeList);
        final CouponPackageMemberDetailsDTO memberList = new CouponPackageMemberDetailsDTO();
        memberList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setMemberList(memberList);
        couponPackageGrantVO.setCouponPackageGrantList(Arrays.asList(editCouponPackageActivityVO));
        couponPackageGrantVO.setCurrentTimeMillis("currentTimeMillis");

        when(mockHsaOperationInfoMapper.queueSubjectGuid("operSubjectGuid")).thenReturn(Collections.emptySet());

        // Configure HsaOperationMemberInfoMapper.listByGuid(...).
        final MemberPhoneDTO memberPhoneDTO = new MemberPhoneDTO();
        memberPhoneDTO.setPhoneNum("memberPhone");
        memberPhoneDTO.setGuid("guid");
        memberPhoneDTO.setUserName("userName");
        memberPhoneDTO.setAccountState(0);
        memberPhoneDTO.setWechatState(0);
        final List<MemberPhoneDTO> memberPhoneDTOS = Arrays.asList(memberPhoneDTO);
        when(mockHsaOperationInfoMapper.listByGuid(Arrays.asList("value"))).thenReturn(memberPhoneDTOS);

        when(mockMapper.queueMemberGuidByGuid("activityGuid")).thenReturn(Arrays.asList("value"));
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("0baa583e-9fd1-4506-9266-2fe3b8567331");
        when(mockCacheService.getInventoryNum("key", "activityCode")).thenReturn(0);

        // Configure MemberCouponAssembler.newMemberCouponLink(...).
        final HsaMemberCouponLink hsaMemberCouponLink = new HsaMemberCouponLink();
        hsaMemberCouponLink.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink.setCouponName("couponName");
        hsaMemberCouponLink.setMemberGuid("memberGuid");
        hsaMemberCouponLink.setCouponType(0);
        hsaMemberCouponLink.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> hsaMemberCouponLinks = Arrays.asList(hsaMemberCouponLink);
        final EditCouponPackageActivityVO activityVO = new EditCouponPackageActivityVO();
        activityVO.setGuid("activityGuid");
        activityVO.setOperSubjectGuid("operSubjectGuid");
        activityVO.setActivityCode("activityCode");
        activityVO.setActivityName("activityName");
        activityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO1 = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO1.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO1.setTime("time");
        activityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO1));
        final ResponseCouponDTO responseCouponDTO1 = new ResponseCouponDTO();
        responseCouponDTO1.setCouponName("couponName");
        responseCouponDTO1.setCouponCode("couponCode");
        responseCouponDTO1.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO1.setIsExist(0);
        responseCouponDTO1.setNum(0);
        activityVO.setCouponGuidList(Arrays.asList(responseCouponDTO1));
        activityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList1 = new CouponPackageMemberDetailsDTO();
        labelList1.setGuidList(Arrays.asList("value"));
        activityVO.setLabelList(labelList1);
        final CouponPackageMemberDetailsDTO gradeList1 = new CouponPackageMemberDetailsDTO();
        gradeList1.setGuidList(Arrays.asList("value"));
        activityVO.setGradeList(gradeList1);
        final CouponPackageMemberDetailsDTO memberList1 = new CouponPackageMemberDetailsDTO();
        memberList1.setGuidList(Arrays.asList("value"));
        activityVO.setMemberList(memberList1);
        final MemberPhoneDTO memberPhoneDTO1 = new MemberPhoneDTO();
        memberPhoneDTO1.setPhoneNum("memberPhone");
        memberPhoneDTO1.setGuid("guid");
        memberPhoneDTO1.setUserName("userName");
        memberPhoneDTO1.setAccountState(0);
        memberPhoneDTO1.setWechatState(0);
        when(mockMemberCouponAssembler.newMemberCouponLink(activityVO, memberPhoneDTO1,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), new HashMap<>(),
                new HashSet<>(Arrays.asList("value")))).thenReturn(hsaMemberCouponLinks);

        // Run the test
        memberCouponPackageLinkServiceImplUnderTest.couponPackageGrant(couponPackageGrantVO);

        // Verify the results
        verify(mockCacheService).lockInventoryNum("key", "activityCode", 1, 1);
        verify(mockCacheService).updateInventoryNum("key", "activityCode");
        verify(mockCacheService).addInventoryNum("key", "activityCode");

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink1 = new HsaMemberCouponLink();
        hsaMemberCouponLink1.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink1.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink1.setCouponName("couponName");
        hsaMemberCouponLink1.setMemberGuid("memberGuid");
        hsaMemberCouponLink1.setCouponType(0);
        hsaMemberCouponLink1.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> collection = Arrays.asList(hsaMemberCouponLink1);
        verify(mockHsaMemberCouponLinkService).saveBatch(collection, 0);

        // Confirm MemberBaseFeign.addMemberInfoLabel(...).
        final AddMemberLabelCorrelationQO qo = new AddMemberLabelCorrelationQO();
        qo.setMemberInfoGuid(Arrays.asList("value"));
        qo.setLabelGuid(Arrays.asList("value"));
        qo.setBatchId("batchId");
        verify(mockMemberBaseFeign).addMemberInfoLabel(qo);

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink2 = new HsaMemberCouponLink();
        hsaMemberCouponLink2.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink2.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink2.setCouponName("couponName");
        hsaMemberCouponLink2.setMemberGuid("memberGuid");
        hsaMemberCouponLink2.setCouponType(0);
        hsaMemberCouponLink2.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> entityList = Arrays.asList(hsaMemberCouponLink2);
        verify(mockHsaMemberCouponLinkService).saveBatch(entityList);

        // Confirm MemberBaseFeign.sendMemberCouponNotice(...).
        final MemberCouponPackageVO memberCouponPackageVO = new MemberCouponPackageVO();
        memberCouponPackageVO.setOperSubjectGuid("operSubjectGuid");
        memberCouponPackageVO.setEnterpriseGuid("enterpriseGuid");
        memberCouponPackageVO.setMemberGuid("guid");
        memberCouponPackageVO.setCouponPackageName("activityName");
        memberCouponPackageVO.setCouponPackageNum(0);
        memberCouponPackageVO.setCouponName("couponName");
        memberCouponPackageVO.setCouponType(0);
        memberCouponPackageVO.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberCouponPackageVO.setCouponNum(0);
        memberCouponPackageVO.setPhoneNum("memberPhone");
        final List<MemberCouponPackageVO> memberCouponLinks = Arrays.asList(memberCouponPackageVO);
        verify(mockMemberBaseFeign).sendMemberCouponNotice(memberCouponLinks);
        verify(mockCacheService).delete("key");
    }

    @Test
    public void testCouponPackageGrant_HsaOperationMemberInfoMapperListByGuidReturnsNoItems() {
        // Setup
        final CouponPackageGrantVO couponPackageGrantVO = new CouponPackageGrantVO();
        final EditCouponPackageActivityVO editCouponPackageActivityVO = new EditCouponPackageActivityVO();
        editCouponPackageActivityVO.setGuid("activityGuid");
        editCouponPackageActivityVO.setOperSubjectGuid("operSubjectGuid");
        editCouponPackageActivityVO.setActivityCode("activityCode");
        editCouponPackageActivityVO.setActivityName("activityName");
        editCouponPackageActivityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO.setTime("time");
        editCouponPackageActivityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO));
        final ResponseCouponDTO responseCouponDTO = new ResponseCouponDTO();
        responseCouponDTO.setCouponName("couponName");
        responseCouponDTO.setCouponCode("couponCode");
        responseCouponDTO.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO.setIsExist(0);
        responseCouponDTO.setNum(0);
        editCouponPackageActivityVO.setCouponGuidList(Arrays.asList(responseCouponDTO));
        editCouponPackageActivityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList = new CouponPackageMemberDetailsDTO();
        labelList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setLabelList(labelList);
        final CouponPackageMemberDetailsDTO gradeList = new CouponPackageMemberDetailsDTO();
        gradeList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setGradeList(gradeList);
        final CouponPackageMemberDetailsDTO memberList = new CouponPackageMemberDetailsDTO();
        memberList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setMemberList(memberList);
        couponPackageGrantVO.setCouponPackageGrantList(Arrays.asList(editCouponPackageActivityVO));
        couponPackageGrantVO.setCurrentTimeMillis("currentTimeMillis");

        when(mockHsaOperationInfoMapper.queueSubjectGuid("operSubjectGuid"))
                .thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockHsaOperationInfoMapper.listByGuid(Arrays.asList("value"))).thenReturn(Collections.emptyList());
        when(mockMapper.queueMemberGuidByGuid("activityGuid")).thenReturn(Arrays.asList("value"));
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("0baa583e-9fd1-4506-9266-2fe3b8567331");
        when(mockCacheService.getInventoryNum("key", "activityCode")).thenReturn(0);

        // Configure MemberCouponAssembler.newMemberCouponLink(...).
        final HsaMemberCouponLink hsaMemberCouponLink = new HsaMemberCouponLink();
        hsaMemberCouponLink.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink.setCouponName("couponName");
        hsaMemberCouponLink.setMemberGuid("memberGuid");
        hsaMemberCouponLink.setCouponType(0);
        hsaMemberCouponLink.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> hsaMemberCouponLinks = Arrays.asList(hsaMemberCouponLink);
        final EditCouponPackageActivityVO activityVO = new EditCouponPackageActivityVO();
        activityVO.setGuid("activityGuid");
        activityVO.setOperSubjectGuid("operSubjectGuid");
        activityVO.setActivityCode("activityCode");
        activityVO.setActivityName("activityName");
        activityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO1 = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO1.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO1.setTime("time");
        activityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO1));
        final ResponseCouponDTO responseCouponDTO1 = new ResponseCouponDTO();
        responseCouponDTO1.setCouponName("couponName");
        responseCouponDTO1.setCouponCode("couponCode");
        responseCouponDTO1.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO1.setIsExist(0);
        responseCouponDTO1.setNum(0);
        activityVO.setCouponGuidList(Arrays.asList(responseCouponDTO1));
        activityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList1 = new CouponPackageMemberDetailsDTO();
        labelList1.setGuidList(Arrays.asList("value"));
        activityVO.setLabelList(labelList1);
        final CouponPackageMemberDetailsDTO gradeList1 = new CouponPackageMemberDetailsDTO();
        gradeList1.setGuidList(Arrays.asList("value"));
        activityVO.setGradeList(gradeList1);
        final CouponPackageMemberDetailsDTO memberList1 = new CouponPackageMemberDetailsDTO();
        memberList1.setGuidList(Arrays.asList("value"));
        activityVO.setMemberList(memberList1);
        final MemberPhoneDTO memberPhoneDTO = new MemberPhoneDTO();
        memberPhoneDTO.setPhoneNum("memberPhone");
        memberPhoneDTO.setGuid("guid");
        memberPhoneDTO.setUserName("userName");
        memberPhoneDTO.setAccountState(0);
        memberPhoneDTO.setWechatState(0);
        when(mockMemberCouponAssembler.newMemberCouponLink(activityVO, memberPhoneDTO,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), new HashMap<>(),
                new HashSet<>(Arrays.asList("value")))).thenReturn(hsaMemberCouponLinks);

        // Run the test
        memberCouponPackageLinkServiceImplUnderTest.couponPackageGrant(couponPackageGrantVO);

        // Verify the results
        verify(mockCacheService).lockInventoryNum("key", "activityCode", 1, 1);
        verify(mockCacheService).updateInventoryNum("key", "activityCode");
        verify(mockCacheService).addInventoryNum("key", "activityCode");

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink1 = new HsaMemberCouponLink();
        hsaMemberCouponLink1.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink1.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink1.setCouponName("couponName");
        hsaMemberCouponLink1.setMemberGuid("memberGuid");
        hsaMemberCouponLink1.setCouponType(0);
        hsaMemberCouponLink1.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> collection = Arrays.asList(hsaMemberCouponLink1);
        verify(mockHsaMemberCouponLinkService).saveBatch(collection, 0);

        // Confirm MemberBaseFeign.addMemberInfoLabel(...).
        final AddMemberLabelCorrelationQO qo = new AddMemberLabelCorrelationQO();
        qo.setMemberInfoGuid(Arrays.asList("value"));
        qo.setLabelGuid(Arrays.asList("value"));
        qo.setBatchId("batchId");
        verify(mockMemberBaseFeign).addMemberInfoLabel(qo);

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink2 = new HsaMemberCouponLink();
        hsaMemberCouponLink2.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink2.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink2.setCouponName("couponName");
        hsaMemberCouponLink2.setMemberGuid("memberGuid");
        hsaMemberCouponLink2.setCouponType(0);
        hsaMemberCouponLink2.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> entityList = Arrays.asList(hsaMemberCouponLink2);
        verify(mockHsaMemberCouponLinkService).saveBatch(entityList);

        // Confirm MemberBaseFeign.sendMemberCouponNotice(...).
        final MemberCouponPackageVO memberCouponPackageVO = new MemberCouponPackageVO();
        memberCouponPackageVO.setOperSubjectGuid("operSubjectGuid");
        memberCouponPackageVO.setEnterpriseGuid("enterpriseGuid");
        memberCouponPackageVO.setMemberGuid("guid");
        memberCouponPackageVO.setCouponPackageName("activityName");
        memberCouponPackageVO.setCouponPackageNum(0);
        memberCouponPackageVO.setCouponName("couponName");
        memberCouponPackageVO.setCouponType(0);
        memberCouponPackageVO.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberCouponPackageVO.setCouponNum(0);
        memberCouponPackageVO.setPhoneNum("memberPhone");
        final List<MemberCouponPackageVO> memberCouponLinks = Arrays.asList(memberCouponPackageVO);
        verify(mockMemberBaseFeign).sendMemberCouponNotice(memberCouponLinks);
        verify(mockCacheService).delete("key");
    }

    @Test
    public void testCouponPackageGrant_HsaMemberCouponPackageLinkMapperReturnsNoItems() {
        // Setup
        final CouponPackageGrantVO couponPackageGrantVO = new CouponPackageGrantVO();
        final EditCouponPackageActivityVO editCouponPackageActivityVO = new EditCouponPackageActivityVO();
        editCouponPackageActivityVO.setGuid("activityGuid");
        editCouponPackageActivityVO.setOperSubjectGuid("operSubjectGuid");
        editCouponPackageActivityVO.setActivityCode("activityCode");
        editCouponPackageActivityVO.setActivityName("activityName");
        editCouponPackageActivityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO.setTime("time");
        editCouponPackageActivityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO));
        final ResponseCouponDTO responseCouponDTO = new ResponseCouponDTO();
        responseCouponDTO.setCouponName("couponName");
        responseCouponDTO.setCouponCode("couponCode");
        responseCouponDTO.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO.setIsExist(0);
        responseCouponDTO.setNum(0);
        editCouponPackageActivityVO.setCouponGuidList(Arrays.asList(responseCouponDTO));
        editCouponPackageActivityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList = new CouponPackageMemberDetailsDTO();
        labelList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setLabelList(labelList);
        final CouponPackageMemberDetailsDTO gradeList = new CouponPackageMemberDetailsDTO();
        gradeList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setGradeList(gradeList);
        final CouponPackageMemberDetailsDTO memberList = new CouponPackageMemberDetailsDTO();
        memberList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setMemberList(memberList);
        couponPackageGrantVO.setCouponPackageGrantList(Arrays.asList(editCouponPackageActivityVO));
        couponPackageGrantVO.setCurrentTimeMillis("currentTimeMillis");

        when(mockHsaOperationInfoMapper.queueSubjectGuid("operSubjectGuid"))
                .thenReturn(new HashSet<>(Arrays.asList("value")));

        // Configure HsaOperationMemberInfoMapper.listByGuid(...).
        final MemberPhoneDTO memberPhoneDTO = new MemberPhoneDTO();
        memberPhoneDTO.setPhoneNum("memberPhone");
        memberPhoneDTO.setGuid("guid");
        memberPhoneDTO.setUserName("userName");
        memberPhoneDTO.setAccountState(0);
        memberPhoneDTO.setWechatState(0);
        final List<MemberPhoneDTO> memberPhoneDTOS = Arrays.asList(memberPhoneDTO);
        when(mockHsaOperationInfoMapper.listByGuid(Arrays.asList("value"))).thenReturn(memberPhoneDTOS);

        when(mockMapper.queueMemberGuidByGuid("activityGuid")).thenReturn(Collections.emptyList());
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("0baa583e-9fd1-4506-9266-2fe3b8567331");
        when(mockCacheService.getInventoryNum("key", "activityCode")).thenReturn(0);

        // Configure MemberCouponAssembler.newMemberCouponLink(...).
        final HsaMemberCouponLink hsaMemberCouponLink = new HsaMemberCouponLink();
        hsaMemberCouponLink.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink.setCouponName("couponName");
        hsaMemberCouponLink.setMemberGuid("memberGuid");
        hsaMemberCouponLink.setCouponType(0);
        hsaMemberCouponLink.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> hsaMemberCouponLinks = Arrays.asList(hsaMemberCouponLink);
        final EditCouponPackageActivityVO activityVO = new EditCouponPackageActivityVO();
        activityVO.setGuid("activityGuid");
        activityVO.setOperSubjectGuid("operSubjectGuid");
        activityVO.setActivityCode("activityCode");
        activityVO.setActivityName("activityName");
        activityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO1 = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO1.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO1.setTime("time");
        activityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO1));
        final ResponseCouponDTO responseCouponDTO1 = new ResponseCouponDTO();
        responseCouponDTO1.setCouponName("couponName");
        responseCouponDTO1.setCouponCode("couponCode");
        responseCouponDTO1.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO1.setIsExist(0);
        responseCouponDTO1.setNum(0);
        activityVO.setCouponGuidList(Arrays.asList(responseCouponDTO1));
        activityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList1 = new CouponPackageMemberDetailsDTO();
        labelList1.setGuidList(Arrays.asList("value"));
        activityVO.setLabelList(labelList1);
        final CouponPackageMemberDetailsDTO gradeList1 = new CouponPackageMemberDetailsDTO();
        gradeList1.setGuidList(Arrays.asList("value"));
        activityVO.setGradeList(gradeList1);
        final CouponPackageMemberDetailsDTO memberList1 = new CouponPackageMemberDetailsDTO();
        memberList1.setGuidList(Arrays.asList("value"));
        activityVO.setMemberList(memberList1);
        final MemberPhoneDTO memberPhoneDTO1 = new MemberPhoneDTO();
        memberPhoneDTO1.setPhoneNum("memberPhone");
        memberPhoneDTO1.setGuid("guid");
        memberPhoneDTO1.setUserName("userName");
        memberPhoneDTO1.setAccountState(0);
        memberPhoneDTO1.setWechatState(0);
        when(mockMemberCouponAssembler.newMemberCouponLink(activityVO, memberPhoneDTO1,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), new HashMap<>(),
                new HashSet<>(Arrays.asList("value")))).thenReturn(hsaMemberCouponLinks);

        // Run the test
        memberCouponPackageLinkServiceImplUnderTest.couponPackageGrant(couponPackageGrantVO);

        // Verify the results
        verify(mockCacheService).lockInventoryNum("key", "activityCode", 1, 1);
        verify(mockCacheService).updateInventoryNum("key", "activityCode");
        verify(mockCacheService).addInventoryNum("key", "activityCode");

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink1 = new HsaMemberCouponLink();
        hsaMemberCouponLink1.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink1.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink1.setCouponName("couponName");
        hsaMemberCouponLink1.setMemberGuid("memberGuid");
        hsaMemberCouponLink1.setCouponType(0);
        hsaMemberCouponLink1.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> collection = Arrays.asList(hsaMemberCouponLink1);
        verify(mockHsaMemberCouponLinkService).saveBatch(collection, 0);

        // Confirm MemberBaseFeign.addMemberInfoLabel(...).
        final AddMemberLabelCorrelationQO qo = new AddMemberLabelCorrelationQO();
        qo.setMemberInfoGuid(Arrays.asList("value"));
        qo.setLabelGuid(Arrays.asList("value"));
        qo.setBatchId("batchId");
        verify(mockMemberBaseFeign).addMemberInfoLabel(qo);

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink2 = new HsaMemberCouponLink();
        hsaMemberCouponLink2.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink2.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink2.setCouponName("couponName");
        hsaMemberCouponLink2.setMemberGuid("memberGuid");
        hsaMemberCouponLink2.setCouponType(0);
        hsaMemberCouponLink2.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> entityList = Arrays.asList(hsaMemberCouponLink2);
        verify(mockHsaMemberCouponLinkService).saveBatch(entityList);

        // Confirm MemberBaseFeign.sendMemberCouponNotice(...).
        final MemberCouponPackageVO memberCouponPackageVO = new MemberCouponPackageVO();
        memberCouponPackageVO.setOperSubjectGuid("operSubjectGuid");
        memberCouponPackageVO.setEnterpriseGuid("enterpriseGuid");
        memberCouponPackageVO.setMemberGuid("guid");
        memberCouponPackageVO.setCouponPackageName("activityName");
        memberCouponPackageVO.setCouponPackageNum(0);
        memberCouponPackageVO.setCouponName("couponName");
        memberCouponPackageVO.setCouponType(0);
        memberCouponPackageVO.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberCouponPackageVO.setCouponNum(0);
        memberCouponPackageVO.setPhoneNum("memberPhone");
        final List<MemberCouponPackageVO> memberCouponLinks = Arrays.asList(memberCouponPackageVO);
        verify(mockMemberBaseFeign).sendMemberCouponNotice(memberCouponLinks);
        verify(mockCacheService).delete("key");
    }

    @Test
    public void testCouponPackageGrant_HsaMemberLabelMapperReturnsNoItems() {
        // Setup
        final CouponPackageGrantVO couponPackageGrantVO = new CouponPackageGrantVO();
        final EditCouponPackageActivityVO editCouponPackageActivityVO = new EditCouponPackageActivityVO();
        editCouponPackageActivityVO.setGuid("activityGuid");
        editCouponPackageActivityVO.setOperSubjectGuid("operSubjectGuid");
        editCouponPackageActivityVO.setActivityCode("activityCode");
        editCouponPackageActivityVO.setActivityName("activityName");
        editCouponPackageActivityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO.setTime("time");
        editCouponPackageActivityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO));
        final ResponseCouponDTO responseCouponDTO = new ResponseCouponDTO();
        responseCouponDTO.setCouponName("couponName");
        responseCouponDTO.setCouponCode("couponCode");
        responseCouponDTO.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO.setIsExist(0);
        responseCouponDTO.setNum(0);
        editCouponPackageActivityVO.setCouponGuidList(Arrays.asList(responseCouponDTO));
        editCouponPackageActivityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList = new CouponPackageMemberDetailsDTO();
        labelList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setLabelList(labelList);
        final CouponPackageMemberDetailsDTO gradeList = new CouponPackageMemberDetailsDTO();
        gradeList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setGradeList(gradeList);
        final CouponPackageMemberDetailsDTO memberList = new CouponPackageMemberDetailsDTO();
        memberList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setMemberList(memberList);
        couponPackageGrantVO.setCouponPackageGrantList(Arrays.asList(editCouponPackageActivityVO));
        couponPackageGrantVO.setCurrentTimeMillis("currentTimeMillis");

        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure HsaOperationMemberInfoMapper.listByGuid(...).
        final MemberPhoneDTO memberPhoneDTO = new MemberPhoneDTO();
        memberPhoneDTO.setPhoneNum("memberPhone");
        memberPhoneDTO.setGuid("guid");
        memberPhoneDTO.setUserName("userName");
        memberPhoneDTO.setAccountState(0);
        memberPhoneDTO.setWechatState(0);
        final List<MemberPhoneDTO> memberPhoneDTOS = Arrays.asList(memberPhoneDTO);
        when(mockHsaOperationInfoMapper.listByGuid(Arrays.asList("value"))).thenReturn(memberPhoneDTOS);

        when(mockMapper.queueMemberGuidByGuid("activityGuid")).thenReturn(Arrays.asList("value"));
        when(mockHsaOperationInfoMapper.queryMemberGrade(Arrays.asList("value"))).thenReturn(Arrays.asList("value"));
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("0baa583e-9fd1-4506-9266-2fe3b8567331");
        when(mockCacheService.getInventoryNum("key", "activityCode")).thenReturn(0);

        // Configure MemberCouponAssembler.newMemberCouponLink(...).
        final HsaMemberCouponLink hsaMemberCouponLink = new HsaMemberCouponLink();
        hsaMemberCouponLink.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink.setCouponName("couponName");
        hsaMemberCouponLink.setMemberGuid("memberGuid");
        hsaMemberCouponLink.setCouponType(0);
        hsaMemberCouponLink.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> hsaMemberCouponLinks = Arrays.asList(hsaMemberCouponLink);
        final EditCouponPackageActivityVO activityVO = new EditCouponPackageActivityVO();
        activityVO.setGuid("activityGuid");
        activityVO.setOperSubjectGuid("operSubjectGuid");
        activityVO.setActivityCode("activityCode");
        activityVO.setActivityName("activityName");
        activityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO1 = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO1.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO1.setTime("time");
        activityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO1));
        final ResponseCouponDTO responseCouponDTO1 = new ResponseCouponDTO();
        responseCouponDTO1.setCouponName("couponName");
        responseCouponDTO1.setCouponCode("couponCode");
        responseCouponDTO1.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO1.setIsExist(0);
        responseCouponDTO1.setNum(0);
        activityVO.setCouponGuidList(Arrays.asList(responseCouponDTO1));
        activityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList1 = new CouponPackageMemberDetailsDTO();
        labelList1.setGuidList(Arrays.asList("value"));
        activityVO.setLabelList(labelList1);
        final CouponPackageMemberDetailsDTO gradeList1 = new CouponPackageMemberDetailsDTO();
        gradeList1.setGuidList(Arrays.asList("value"));
        activityVO.setGradeList(gradeList1);
        final CouponPackageMemberDetailsDTO memberList1 = new CouponPackageMemberDetailsDTO();
        memberList1.setGuidList(Arrays.asList("value"));
        activityVO.setMemberList(memberList1);
        final MemberPhoneDTO memberPhoneDTO1 = new MemberPhoneDTO();
        memberPhoneDTO1.setPhoneNum("memberPhone");
        memberPhoneDTO1.setGuid("guid");
        memberPhoneDTO1.setUserName("userName");
        memberPhoneDTO1.setAccountState(0);
        memberPhoneDTO1.setWechatState(0);
        when(mockMemberCouponAssembler.newMemberCouponLink(activityVO, memberPhoneDTO1,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), new HashMap<>(),
                new HashSet<>(Arrays.asList("value")))).thenReturn(hsaMemberCouponLinks);

        // Run the test
        memberCouponPackageLinkServiceImplUnderTest.couponPackageGrant(couponPackageGrantVO);

        // Verify the results
        verify(mockCacheService).lockInventoryNum("key", "activityCode", 1, 1);
        verify(mockCacheService).updateInventoryNum("key", "activityCode");
        verify(mockCacheService).addInventoryNum("key", "activityCode");

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink1 = new HsaMemberCouponLink();
        hsaMemberCouponLink1.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink1.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink1.setCouponName("couponName");
        hsaMemberCouponLink1.setMemberGuid("memberGuid");
        hsaMemberCouponLink1.setCouponType(0);
        hsaMemberCouponLink1.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> collection = Arrays.asList(hsaMemberCouponLink1);
        verify(mockHsaMemberCouponLinkService).saveBatch(collection, 0);

        // Confirm MemberBaseFeign.addMemberInfoLabel(...).
        final AddMemberLabelCorrelationQO qo = new AddMemberLabelCorrelationQO();
        qo.setMemberInfoGuid(Arrays.asList("value"));
        qo.setLabelGuid(Arrays.asList("value"));
        qo.setBatchId("batchId");
        verify(mockMemberBaseFeign).addMemberInfoLabel(qo);

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink2 = new HsaMemberCouponLink();
        hsaMemberCouponLink2.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink2.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink2.setCouponName("couponName");
        hsaMemberCouponLink2.setMemberGuid("memberGuid");
        hsaMemberCouponLink2.setCouponType(0);
        hsaMemberCouponLink2.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> entityList = Arrays.asList(hsaMemberCouponLink2);
        verify(mockHsaMemberCouponLinkService).saveBatch(entityList);

        // Confirm MemberBaseFeign.sendMemberCouponNotice(...).
        final MemberCouponPackageVO memberCouponPackageVO = new MemberCouponPackageVO();
        memberCouponPackageVO.setOperSubjectGuid("operSubjectGuid");
        memberCouponPackageVO.setEnterpriseGuid("enterpriseGuid");
        memberCouponPackageVO.setMemberGuid("guid");
        memberCouponPackageVO.setCouponPackageName("activityName");
        memberCouponPackageVO.setCouponPackageNum(0);
        memberCouponPackageVO.setCouponName("couponName");
        memberCouponPackageVO.setCouponType(0);
        memberCouponPackageVO.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberCouponPackageVO.setCouponNum(0);
        memberCouponPackageVO.setPhoneNum("memberPhone");
        final List<MemberCouponPackageVO> memberCouponLinks = Arrays.asList(memberCouponPackageVO);
        verify(mockMemberBaseFeign).sendMemberCouponNotice(memberCouponLinks);
        verify(mockCacheService).delete("key");
    }

    @Test
    public void testCouponPackageGrant_HsaOperationMemberInfoMapperQueryMemberGradeReturnsNoItems() {
        // Setup
        final CouponPackageGrantVO couponPackageGrantVO = new CouponPackageGrantVO();
        final EditCouponPackageActivityVO editCouponPackageActivityVO = new EditCouponPackageActivityVO();
        editCouponPackageActivityVO.setGuid("activityGuid");
        editCouponPackageActivityVO.setOperSubjectGuid("operSubjectGuid");
        editCouponPackageActivityVO.setActivityCode("activityCode");
        editCouponPackageActivityVO.setActivityName("activityName");
        editCouponPackageActivityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO.setTime("time");
        editCouponPackageActivityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO));
        final ResponseCouponDTO responseCouponDTO = new ResponseCouponDTO();
        responseCouponDTO.setCouponName("couponName");
        responseCouponDTO.setCouponCode("couponCode");
        responseCouponDTO.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO.setIsExist(0);
        responseCouponDTO.setNum(0);
        editCouponPackageActivityVO.setCouponGuidList(Arrays.asList(responseCouponDTO));
        editCouponPackageActivityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList = new CouponPackageMemberDetailsDTO();
        labelList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setLabelList(labelList);
        final CouponPackageMemberDetailsDTO gradeList = new CouponPackageMemberDetailsDTO();
        gradeList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setGradeList(gradeList);
        final CouponPackageMemberDetailsDTO memberList = new CouponPackageMemberDetailsDTO();
        memberList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setMemberList(memberList);
        couponPackageGrantVO.setCouponPackageGrantList(Arrays.asList(editCouponPackageActivityVO));
        couponPackageGrantVO.setCurrentTimeMillis("currentTimeMillis");

        // Configure HsaMemberLabelMapper.selectList(...).
        final HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid("dc306173-1358-4df9-a481-d9d5a4c1e589");
        hsaMemberLabel.setLabelSettingGuid("labelSettingGuid");
        hsaMemberLabel.setOperSubjectGuid("operSubjectGuid");
        hsaMemberLabel.setOperationMemberInfoGuid("operationMemberInfoGuid");
        hsaMemberLabel.setOperatorGuid("operatorGuid");
        final List<HsaMemberLabel> hsaMemberLabels = Arrays.asList(hsaMemberLabel);
        when(mockHsaMemberLabelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberLabels);

        when(mockHsaOperationInfoMapper.queryMemberGrade(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure HsaOperationMemberInfoMapper.listByGuid(...).
        final MemberPhoneDTO memberPhoneDTO = new MemberPhoneDTO();
        memberPhoneDTO.setPhoneNum("memberPhone");
        memberPhoneDTO.setGuid("guid");
        memberPhoneDTO.setUserName("userName");
        memberPhoneDTO.setAccountState(0);
        memberPhoneDTO.setWechatState(0);
        final List<MemberPhoneDTO> memberPhoneDTOS = Arrays.asList(memberPhoneDTO);
        when(mockHsaOperationInfoMapper.listByGuid(Arrays.asList("value"))).thenReturn(memberPhoneDTOS);

        when(mockMapper.queueMemberGuidByGuid("activityGuid")).thenReturn(Arrays.asList("value"));
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("0baa583e-9fd1-4506-9266-2fe3b8567331");
        when(mockCacheService.getInventoryNum("key", "activityCode")).thenReturn(0);

        // Configure MemberCouponAssembler.newMemberCouponLink(...).
        final HsaMemberCouponLink hsaMemberCouponLink = new HsaMemberCouponLink();
        hsaMemberCouponLink.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink.setCouponName("couponName");
        hsaMemberCouponLink.setMemberGuid("memberGuid");
        hsaMemberCouponLink.setCouponType(0);
        hsaMemberCouponLink.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> hsaMemberCouponLinks = Arrays.asList(hsaMemberCouponLink);
        final EditCouponPackageActivityVO activityVO = new EditCouponPackageActivityVO();
        activityVO.setGuid("activityGuid");
        activityVO.setOperSubjectGuid("operSubjectGuid");
        activityVO.setActivityCode("activityCode");
        activityVO.setActivityName("activityName");
        activityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO1 = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO1.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO1.setTime("time");
        activityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO1));
        final ResponseCouponDTO responseCouponDTO1 = new ResponseCouponDTO();
        responseCouponDTO1.setCouponName("couponName");
        responseCouponDTO1.setCouponCode("couponCode");
        responseCouponDTO1.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO1.setIsExist(0);
        responseCouponDTO1.setNum(0);
        activityVO.setCouponGuidList(Arrays.asList(responseCouponDTO1));
        activityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList1 = new CouponPackageMemberDetailsDTO();
        labelList1.setGuidList(Arrays.asList("value"));
        activityVO.setLabelList(labelList1);
        final CouponPackageMemberDetailsDTO gradeList1 = new CouponPackageMemberDetailsDTO();
        gradeList1.setGuidList(Arrays.asList("value"));
        activityVO.setGradeList(gradeList1);
        final CouponPackageMemberDetailsDTO memberList1 = new CouponPackageMemberDetailsDTO();
        memberList1.setGuidList(Arrays.asList("value"));
        activityVO.setMemberList(memberList1);
        final MemberPhoneDTO memberPhoneDTO1 = new MemberPhoneDTO();
        memberPhoneDTO1.setPhoneNum("memberPhone");
        memberPhoneDTO1.setGuid("guid");
        memberPhoneDTO1.setUserName("userName");
        memberPhoneDTO1.setAccountState(0);
        memberPhoneDTO1.setWechatState(0);
        when(mockMemberCouponAssembler.newMemberCouponLink(activityVO, memberPhoneDTO1,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), new HashMap<>(),
                new HashSet<>(Arrays.asList("value")))).thenReturn(hsaMemberCouponLinks);

        // Run the test
        memberCouponPackageLinkServiceImplUnderTest.couponPackageGrant(couponPackageGrantVO);

        // Verify the results
        verify(mockCacheService).lockInventoryNum("key", "activityCode", 1, 1);
        verify(mockCacheService).updateInventoryNum("key", "activityCode");
        verify(mockCacheService).addInventoryNum("key", "activityCode");

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink1 = new HsaMemberCouponLink();
        hsaMemberCouponLink1.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink1.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink1.setCouponName("couponName");
        hsaMemberCouponLink1.setMemberGuid("memberGuid");
        hsaMemberCouponLink1.setCouponType(0);
        hsaMemberCouponLink1.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> collection = Arrays.asList(hsaMemberCouponLink1);
        verify(mockHsaMemberCouponLinkService).saveBatch(collection, 0);

        // Confirm MemberBaseFeign.addMemberInfoLabel(...).
        final AddMemberLabelCorrelationQO qo = new AddMemberLabelCorrelationQO();
        qo.setMemberInfoGuid(Arrays.asList("value"));
        qo.setLabelGuid(Arrays.asList("value"));
        qo.setBatchId("batchId");
        verify(mockMemberBaseFeign).addMemberInfoLabel(qo);

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink2 = new HsaMemberCouponLink();
        hsaMemberCouponLink2.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink2.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink2.setCouponName("couponName");
        hsaMemberCouponLink2.setMemberGuid("memberGuid");
        hsaMemberCouponLink2.setCouponType(0);
        hsaMemberCouponLink2.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> entityList = Arrays.asList(hsaMemberCouponLink2);
        verify(mockHsaMemberCouponLinkService).saveBatch(entityList);

        // Confirm MemberBaseFeign.sendMemberCouponNotice(...).
        final MemberCouponPackageVO memberCouponPackageVO = new MemberCouponPackageVO();
        memberCouponPackageVO.setOperSubjectGuid("operSubjectGuid");
        memberCouponPackageVO.setEnterpriseGuid("enterpriseGuid");
        memberCouponPackageVO.setMemberGuid("guid");
        memberCouponPackageVO.setCouponPackageName("activityName");
        memberCouponPackageVO.setCouponPackageNum(0);
        memberCouponPackageVO.setCouponName("couponName");
        memberCouponPackageVO.setCouponType(0);
        memberCouponPackageVO.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberCouponPackageVO.setCouponNum(0);
        memberCouponPackageVO.setPhoneNum("memberPhone");
        final List<MemberCouponPackageVO> memberCouponLinks = Arrays.asList(memberCouponPackageVO);
        verify(mockMemberBaseFeign).sendMemberCouponNotice(memberCouponLinks);
        verify(mockCacheService).delete("key");
    }

    @Test
    public void testCouponPackageGrant_MemberCouponAssemblerReturnsNoItems() {
        // Setup
        final CouponPackageGrantVO couponPackageGrantVO = new CouponPackageGrantVO();
        final EditCouponPackageActivityVO editCouponPackageActivityVO = new EditCouponPackageActivityVO();
        editCouponPackageActivityVO.setGuid("activityGuid");
        editCouponPackageActivityVO.setOperSubjectGuid("operSubjectGuid");
        editCouponPackageActivityVO.setActivityCode("activityCode");
        editCouponPackageActivityVO.setActivityName("activityName");
        editCouponPackageActivityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO.setTime("time");
        editCouponPackageActivityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO));
        final ResponseCouponDTO responseCouponDTO = new ResponseCouponDTO();
        responseCouponDTO.setCouponName("couponName");
        responseCouponDTO.setCouponCode("couponCode");
        responseCouponDTO.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO.setIsExist(0);
        responseCouponDTO.setNum(0);
        editCouponPackageActivityVO.setCouponGuidList(Arrays.asList(responseCouponDTO));
        editCouponPackageActivityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList = new CouponPackageMemberDetailsDTO();
        labelList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setLabelList(labelList);
        final CouponPackageMemberDetailsDTO gradeList = new CouponPackageMemberDetailsDTO();
        gradeList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setGradeList(gradeList);
        final CouponPackageMemberDetailsDTO memberList = new CouponPackageMemberDetailsDTO();
        memberList.setGuidList(Arrays.asList("value"));
        editCouponPackageActivityVO.setMemberList(memberList);
        couponPackageGrantVO.setCouponPackageGrantList(Arrays.asList(editCouponPackageActivityVO));
        couponPackageGrantVO.setCurrentTimeMillis("currentTimeMillis");

        when(mockHsaOperationInfoMapper.queueSubjectGuid("operSubjectGuid"))
                .thenReturn(new HashSet<>(Arrays.asList("value")));

        // Configure HsaOperationMemberInfoMapper.listByGuid(...).
        final MemberPhoneDTO memberPhoneDTO = new MemberPhoneDTO();
        memberPhoneDTO.setPhoneNum("memberPhone");
        memberPhoneDTO.setGuid("guid");
        memberPhoneDTO.setUserName("userName");
        memberPhoneDTO.setAccountState(0);
        memberPhoneDTO.setWechatState(0);
        final List<MemberPhoneDTO> memberPhoneDTOS = Arrays.asList(memberPhoneDTO);
        when(mockHsaOperationInfoMapper.listByGuid(Arrays.asList("value"))).thenReturn(memberPhoneDTOS);

        when(mockMapper.queueMemberGuidByGuid("activityGuid")).thenReturn(Arrays.asList("value"));
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("0baa583e-9fd1-4506-9266-2fe3b8567331");
        when(mockCacheService.getInventoryNum("key", "activityCode")).thenReturn(0);

        // Configure MemberCouponAssembler.newMemberCouponLink(...).
        final EditCouponPackageActivityVO activityVO = new EditCouponPackageActivityVO();
        activityVO.setGuid("activityGuid");
        activityVO.setOperSubjectGuid("operSubjectGuid");
        activityVO.setActivityCode("activityCode");
        activityVO.setActivityName("activityName");
        activityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO1 = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO1.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO1.setTime("time");
        activityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO1));
        final ResponseCouponDTO responseCouponDTO1 = new ResponseCouponDTO();
        responseCouponDTO1.setCouponName("couponName");
        responseCouponDTO1.setCouponCode("couponCode");
        responseCouponDTO1.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO1.setIsExist(0);
        responseCouponDTO1.setNum(0);
        activityVO.setCouponGuidList(Arrays.asList(responseCouponDTO1));
        activityVO.setGiveMemberType(0);
        final CouponPackageMemberDetailsDTO labelList1 = new CouponPackageMemberDetailsDTO();
        labelList1.setGuidList(Arrays.asList("value"));
        activityVO.setLabelList(labelList1);
        final CouponPackageMemberDetailsDTO gradeList1 = new CouponPackageMemberDetailsDTO();
        gradeList1.setGuidList(Arrays.asList("value"));
        activityVO.setGradeList(gradeList1);
        final CouponPackageMemberDetailsDTO memberList1 = new CouponPackageMemberDetailsDTO();
        memberList1.setGuidList(Arrays.asList("value"));
        activityVO.setMemberList(memberList1);
        final MemberPhoneDTO memberPhoneDTO1 = new MemberPhoneDTO();
        memberPhoneDTO1.setPhoneNum("memberPhone");
        memberPhoneDTO1.setGuid("guid");
        memberPhoneDTO1.setUserName("userName");
        memberPhoneDTO1.setAccountState(0);
        memberPhoneDTO1.setWechatState(0);
        when(mockMemberCouponAssembler.newMemberCouponLink(activityVO, memberPhoneDTO1,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), new HashMap<>(),
                new HashSet<>(Arrays.asList("value")))).thenReturn(Collections.emptyList());

        // Run the test
        memberCouponPackageLinkServiceImplUnderTest.couponPackageGrant(couponPackageGrantVO);

        // Verify the results
        verify(mockCacheService).lockInventoryNum("key", "activityCode", 1, 1);
        verify(mockCacheService).updateInventoryNum("key", "activityCode");
        verify(mockCacheService).addInventoryNum("key", "activityCode");

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink = new HsaMemberCouponLink();
        hsaMemberCouponLink.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink.setCouponName("couponName");
        hsaMemberCouponLink.setMemberGuid("memberGuid");
        hsaMemberCouponLink.setCouponType(0);
        hsaMemberCouponLink.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> collection = Arrays.asList(hsaMemberCouponLink);
        verify(mockHsaMemberCouponLinkService).saveBatch(collection, 0);

        // Confirm MemberBaseFeign.addMemberInfoLabel(...).
        final AddMemberLabelCorrelationQO qo = new AddMemberLabelCorrelationQO();
        qo.setMemberInfoGuid(Arrays.asList("value"));
        qo.setLabelGuid(Arrays.asList("value"));
        qo.setBatchId("batchId");
        verify(mockMemberBaseFeign).addMemberInfoLabel(qo);

        // Confirm MemberCouponLinkService.saveBatch(...).
        final HsaMemberCouponLink hsaMemberCouponLink1 = new HsaMemberCouponLink();
        hsaMemberCouponLink1.setGuid("0baa583e-9fd1-4506-9266-2fe3b8567331");
        hsaMemberCouponLink1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink1.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink1.setCouponName("couponName");
        hsaMemberCouponLink1.setMemberGuid("memberGuid");
        hsaMemberCouponLink1.setCouponType(0);
        hsaMemberCouponLink1.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberCouponLink> entityList = Arrays.asList(hsaMemberCouponLink1);
        verify(mockHsaMemberCouponLinkService).saveBatch(entityList);

        // Confirm MemberBaseFeign.sendMemberCouponNotice(...).
        final MemberCouponPackageVO memberCouponPackageVO = new MemberCouponPackageVO();
        memberCouponPackageVO.setOperSubjectGuid("operSubjectGuid");
        memberCouponPackageVO.setEnterpriseGuid("enterpriseGuid");
        memberCouponPackageVO.setMemberGuid("guid");
        memberCouponPackageVO.setCouponPackageName("activityName");
        memberCouponPackageVO.setCouponPackageNum(0);
        memberCouponPackageVO.setCouponName("couponName");
        memberCouponPackageVO.setCouponType(0);
        memberCouponPackageVO.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberCouponPackageVO.setCouponNum(0);
        memberCouponPackageVO.setPhoneNum("memberPhone");
        final List<MemberCouponPackageVO> memberCouponLinks = Arrays.asList(memberCouponPackageVO);
        verify(mockMemberBaseFeign).sendMemberCouponNotice(memberCouponLinks);
        verify(mockCacheService).delete("key");
    }

    @Test
    public void testSubList2() {
        assertThat(
                MemberCouponPackageLinkServiceImpl.subList2(Arrays.asList("value"), Arrays.asList("value")))
                .isEqualTo(new HashSet<>(Arrays.asList("value")));
        assertThat(
                MemberCouponPackageLinkServiceImpl.subList2(Arrays.asList("value"), Arrays.asList("value")))
                .isEqualTo(Collections.emptySet());
    }

    @Test
    public void testCheckMemberGuid() {
        // Setup
        final MemberPhoneDTO memberPhoneDTO = new MemberPhoneDTO();
        memberPhoneDTO.setPhoneNum("memberPhone");
        memberPhoneDTO.setGuid("guid");
        memberPhoneDTO.setUserName("userName");
        memberPhoneDTO.setAccountState(0);
        memberPhoneDTO.setWechatState(0);
        final Set<MemberPhoneDTO> expectedResult = new HashSet<>(Arrays.asList(memberPhoneDTO));

        // Configure HsaOperationMemberInfoMapper.listByGuid(...).
        final MemberPhoneDTO memberPhoneDTO1 = new MemberPhoneDTO();
        memberPhoneDTO1.setPhoneNum("memberPhone");
        memberPhoneDTO1.setGuid("guid");
        memberPhoneDTO1.setUserName("userName");
        memberPhoneDTO1.setAccountState(0);
        memberPhoneDTO1.setWechatState(0);
        final List<MemberPhoneDTO> memberPhoneDTOS = Arrays.asList(memberPhoneDTO1);
        when(mockHsaOperationInfoMapper.listByGuid(Arrays.asList("value"))).thenReturn(memberPhoneDTOS);

        // Run the test
        final Set<MemberPhoneDTO> result = memberCouponPackageLinkServiceImplUnderTest.checkMemberGuid(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCheckMemberGuid_HsaOperationMemberInfoMapperReturnsNoItems() {
        // Setup
        when(mockHsaOperationInfoMapper.listByGuid(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final Set<MemberPhoneDTO> result = memberCouponPackageLinkServiceImplUnderTest.checkMemberGuid(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptySet());
    }
}
