package com.holderzone.member.queue.service.card.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.dto.event.SendOpenCardEvent;
import com.holderzone.member.common.dto.event.SendOpenElectronicCardEvent;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.qo.card.CardEquitiesQO;
import com.holderzone.member.common.qo.card.SendMemberEquityCard;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.queue.entity.*;
import com.holderzone.member.queue.mapper.*;
import com.holderzone.member.queue.service.card.HsaElectronicCardService;
import com.holderzone.member.queue.service.card.HsaStoreCardRuleService;
import com.holderzone.member.queue.service.card.assembler.MemberCardAssembler;
import com.holderzone.member.queue.service.member.impl.HsaMemberFundingDetailServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MemberInfoCardServiceImplTest {

    @Mock
    private HsaMemberInfoCardMapper mockHsaMemberInfoCardMapper;
    @Mock
    private HsaStoreCardRuleMapper mockHsaStoreCardRuleMapper;
    @Mock
    private HsaElectronicCardService mockHsaElectronicCardService;
    @Mock
    private HsaOperationMemberInfoMapper mockHsaOperationMemberInfoMapper;
    @Mock
    private HsaCardOpenRuleMapper mockHsaCardOpenRuleMapper;
    @Mock
    private HsaCardBaseInfoMapper mockHsaCardBaseInfoMapper;
    @Mock
    private HsaStoreCardRuleService mockHsaStoreCardRuleService;
    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private MemberBaseFeign mockMemberBaseFeign;
    @Mock
    private HsaMemberFundingDetailServiceImpl mockHsaMemberFundingDetailService;
    @Mock
    private MemberCardAssembler mockMemberCardAssembler;

    @InjectMocks
    private MemberInfoCardServiceImpl memberInfoCardServiceImplUnderTest;

    @Test
    public void testSendOpenElectronicCard() {
        // Setup
        final SendOpenElectronicCardEvent event = new SendOpenElectronicCardEvent();
        final SendOpenCardEvent sendOpenCardEvent = new SendOpenCardEvent();
        sendOpenCardEvent.setCardGuid("cardGuid");
        sendOpenCardEvent.setMemberInfoGuid(Arrays.asList("value"));
        event.setSendOpenCardEvents(Arrays.asList(sendOpenCardEvent));

        // Configure HsaCardOpenRuleMapper.selectList(...).
        final HsaCardOpenRule hsaCardOpenRule = new HsaCardOpenRule();
        hsaCardOpenRule.setGuid("9354a6e1-d954-4e90-9734-6b18dfe61454");
        hsaCardOpenRule.setCardGuid("cardGuid");
        hsaCardOpenRule.setSendStatus(0);
        hsaCardOpenRule.setSendOpenCountLimit(0);
        hsaCardOpenRule.setSurplusSendOpenCountLimit(0);
        final List<HsaCardOpenRule> hsaCardOpenRules = Arrays.asList(hsaCardOpenRule);
        when(mockHsaCardOpenRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCardOpenRules);

        // Configure HsaCardBaseInfoMapper.selectList(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("7283f9e4-076b-431b-a811-8928c88cedca");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardValueMoney(new BigDecimal("0.00"));
        hsaCardBaseInfo.setCardPayPassword("cardPayPassword");
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCardBaseInfos);

        // Configure HsaOperationMemberInfoMapper.selectList(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("9f7db170-66b8-49d4-a3e8-3be76c08434c");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setMemberGradeInfoName("memberGradeInfoName");
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo);
        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperationMemberInfos);

        // Configure HsaMemberInfoCardMapper.selectList(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardImage("cardImage");
        hsaMemberInfoCard.setCardPayPassword("cardPayPassword");
        final List<HsaMemberInfoCard> hsaMemberInfoCards = Arrays.asList(hsaMemberInfoCard);
        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberInfoCards);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("guid");

        // Configure HsaStoreCardRuleMapper.selectList(...).
        final HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        hsaStoreCardRule.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule.setCardGuid("cardGuid");
        hsaStoreCardRule.setParentGuid("parentGuid");
        hsaStoreCardRule.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRules = Arrays.asList(hsaStoreCardRule);
        when(mockHsaStoreCardRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaStoreCardRules);

        // Configure HsaMemberInfoCardMapper.queryByGuid(...).
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardImage("cardImage");
        hsaMemberInfoCard1.setCardPayPassword("cardPayPassword");
        when(mockHsaMemberInfoCardMapper.queryByGuid("guid")).thenReturn(hsaMemberInfoCard1);

        // Configure MemberCardAssembler.addOpenCardBalanceRecord(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setGuid("112f7e24-f134-4293-b96c-40cf4f29225f");
        hsaMemberFundingDetail.setOperSubjectGuid("operSubjectGuid");
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setCardNum("cardNum");
        hsaMemberFundingDetail.setCardName("cardName");
        final HsaMemberInfoCard memberInfoCard = new HsaMemberInfoCard();
        memberInfoCard.setGuid("guid");
        memberInfoCard.setMemberInfoGuid("memberInfoGuid");
        memberInfoCard.setElectronicCardGuid("electronicCardGuid");
        memberInfoCard.setCardGuid("cardGuid");
        memberInfoCard.setCardAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessTimes(0);
        memberInfoCard.setCardImage("cardImage");
        memberInfoCard.setCardPayPassword("cardPayPassword");
        when(mockMemberCardAssembler.addOpenCardBalanceRecord(memberInfoCard, new BigDecimal("0.00")))
                .thenReturn(hsaMemberFundingDetail);

        // Run the test
        memberInfoCardServiceImplUnderTest.sendOpenElectronicCard(event);

        // Verify the results
        // Confirm MemberCardAssembler.openCardProcessor(...).
        final HsaElectronicCard electronicCard = new HsaElectronicCard();
        electronicCard.setGuid("afce0919-879a-4438-a9c1-6eef4c917211");
        electronicCard.setOperSubjectGuid("operSubjectGuid");
        electronicCard.setEnterpriseGuid("enterpriseGuid");
        electronicCard.setMemberInfoGuid("memberInfoGuid");
        electronicCard.setCardGuid("cardGuid");
        final HsaMemberInfoCard hsaMemberInfoCard2 = new HsaMemberInfoCard();
        hsaMemberInfoCard2.setGuid("guid");
        hsaMemberInfoCard2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard2.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard2.setCardGuid("cardGuid");
        hsaMemberInfoCard2.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessTimes(0);
        hsaMemberInfoCard2.setCardImage("cardImage");
        hsaMemberInfoCard2.setCardPayPassword("cardPayPassword");
        final HsaCardOpenRule hsaCardOpenRule1 = new HsaCardOpenRule();
        hsaCardOpenRule1.setGuid("9354a6e1-d954-4e90-9734-6b18dfe61454");
        hsaCardOpenRule1.setCardGuid("cardGuid");
        hsaCardOpenRule1.setSendStatus(0);
        hsaCardOpenRule1.setSendOpenCountLimit(0);
        hsaCardOpenRule1.setSurplusSendOpenCountLimit(0);
        final HsaCardBaseInfo hsaCardBaseInfo1 = new HsaCardBaseInfo();
        hsaCardBaseInfo1.setGuid("7283f9e4-076b-431b-a811-8928c88cedca");
        hsaCardBaseInfo1.setCardImage("cardImage");
        hsaCardBaseInfo1.setCardValueMoney(new BigDecimal("0.00"));
        hsaCardBaseInfo1.setCardPayPassword("cardPayPassword");
        hsaCardBaseInfo1.setIsExcess(0);
        hsaCardBaseInfo1.setExcessType(0);
        hsaCardBaseInfo1.setExcessMoney(new BigDecimal("0.00"));
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("9f7db170-66b8-49d4-a3e8-3be76c08434c");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberAccount("memberAccount");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo1.setMemberGradeInfoName("memberGradeInfoName");
        final HsaMemberInfoCard hsaMemberInfoCard3 = new HsaMemberInfoCard();
        hsaMemberInfoCard3.setGuid("guid");
        hsaMemberInfoCard3.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard3.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard3.setCardGuid("cardGuid");
        hsaMemberInfoCard3.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard3.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard3.setExcessTimes(0);
        hsaMemberInfoCard3.setCardImage("cardImage");
        hsaMemberInfoCard3.setCardPayPassword("cardPayPassword");
        final List<HsaMemberInfoCard> hsaMemberInfoCardList = Arrays.asList(hsaMemberInfoCard3);
        final HsaElectronicCard hsaElectronicCard = new HsaElectronicCard();
        hsaElectronicCard.setGuid("afce0919-879a-4438-a9c1-6eef4c917211");
        hsaElectronicCard.setOperSubjectGuid("operSubjectGuid");
        hsaElectronicCard.setEnterpriseGuid("enterpriseGuid");
        hsaElectronicCard.setMemberInfoGuid("memberInfoGuid");
        hsaElectronicCard.setCardGuid("cardGuid");
        final List<HsaElectronicCard> hsaElectronicCardList = Arrays.asList(hsaElectronicCard);
        verify(mockMemberCardAssembler).openCardProcessor(electronicCard, hsaMemberInfoCard2, hsaCardOpenRule1,
                hsaCardBaseInfo1, hsaOperationMemberInfo1, hsaMemberInfoCardList, hsaElectronicCardList);

        // Confirm HsaStoreCardRuleService.mergeStoreProcessor(...).
        final HsaMemberInfoCard hsaMemberInfoCard4 = new HsaMemberInfoCard();
        hsaMemberInfoCard4.setGuid("guid");
        hsaMemberInfoCard4.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard4.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard4.setCardGuid("cardGuid");
        hsaMemberInfoCard4.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard4.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard4.setExcessTimes(0);
        hsaMemberInfoCard4.setCardImage("cardImage");
        hsaMemberInfoCard4.setCardPayPassword("cardPayPassword");
        verify(mockHsaStoreCardRuleService).mergeStoreProcessor(hsaMemberInfoCard4, "cardGuid");

        // Confirm MemberCardAssembler.addStoreCardRuleArray(...).
        final HsaMemberInfoCard hsaMemberInfoCard5 = new HsaMemberInfoCard();
        hsaMemberInfoCard5.setGuid("guid");
        hsaMemberInfoCard5.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard5.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard5.setCardGuid("cardGuid");
        hsaMemberInfoCard5.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard5.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard5.setExcessTimes(0);
        hsaMemberInfoCard5.setCardImage("cardImage");
        hsaMemberInfoCard5.setCardPayPassword("cardPayPassword");
        final HsaStoreCardRule hsaStoreCardRule1 = new HsaStoreCardRule();
        hsaStoreCardRule1.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule1.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule1.setCardGuid("cardGuid");
        hsaStoreCardRule1.setParentGuid("parentGuid");
        hsaStoreCardRule1.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRuleList = Arrays.asList(hsaStoreCardRule1);
        final HsaStoreCardRule hsaStoreCardRule2 = new HsaStoreCardRule();
        hsaStoreCardRule2.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule2.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule2.setCardGuid("cardGuid");
        hsaStoreCardRule2.setParentGuid("parentGuid");
        hsaStoreCardRule2.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRuleArrayList = Arrays.asList(hsaStoreCardRule2);
        verify(mockMemberCardAssembler).addStoreCardRuleArray(hsaMemberInfoCard5, hsaStoreCardRuleList, new HashMap<>(),
                hsaStoreCardRuleArrayList);

        // Confirm HsaStoreCardRuleService.saveBatch(...).
        final HsaStoreCardRule hsaStoreCardRule3 = new HsaStoreCardRule();
        hsaStoreCardRule3.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule3.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule3.setCardGuid("cardGuid");
        hsaStoreCardRule3.setParentGuid("parentGuid");
        hsaStoreCardRule3.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> entityList = Arrays.asList(hsaStoreCardRule3);
        verify(mockHsaStoreCardRuleService).saveBatch(entityList);

        // Confirm HsaMemberInfoCardMapper.updateByGuid(...).
        final HsaMemberInfoCard t = new HsaMemberInfoCard();
        t.setGuid("guid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setElectronicCardGuid("electronicCardGuid");
        t.setCardGuid("cardGuid");
        t.setCardAmount(new BigDecimal("0.00"));
        t.setExcessAmount(new BigDecimal("0.00"));
        t.setExcessTimes(0);
        t.setCardImage("cardImage");
        t.setCardPayPassword("cardPayPassword");
        verify(mockHsaMemberInfoCardMapper).updateByGuid(t);

        // Confirm HsaMemberInfoCardMapper.insert(...).
        final HsaMemberInfoCard t1 = new HsaMemberInfoCard();
        t1.setGuid("guid");
        t1.setMemberInfoGuid("memberInfoGuid");
        t1.setElectronicCardGuid("electronicCardGuid");
        t1.setCardGuid("cardGuid");
        t1.setCardAmount(new BigDecimal("0.00"));
        t1.setExcessAmount(new BigDecimal("0.00"));
        t1.setExcessTimes(0);
        t1.setCardImage("cardImage");
        t1.setCardPayPassword("cardPayPassword");
        verify(mockHsaMemberInfoCardMapper).insert(t1);

        // Confirm MemberBaseFeign.grantFeignCardRights(...).
        final CardEquitiesQO cardGrantEquitiesDTO = new CardEquitiesQO();
        cardGrantEquitiesDTO.setCardGuid(Arrays.asList("value"));
        final SendMemberEquityCard sendMemberEquityCard = new SendMemberEquityCard();
        sendMemberEquityCard.setOperSubjectGuid("operSubjectGuid");
        sendMemberEquityCard.setEnterpriseGuid("enterpriseGuid");
        sendMemberEquityCard.setMemberInfoGuid("memberInfoGuid");
        cardGrantEquitiesDTO.setSendMemberEquityCard(Arrays.asList(sendMemberEquityCard));
        verify(mockMemberBaseFeign).grantFeignCardRights(cardGrantEquitiesDTO);

        // Confirm HsaMemberFundingDetailServiceImpl.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail1 = new HsaMemberFundingDetail();
        hsaMemberFundingDetail1.setGuid("112f7e24-f134-4293-b96c-40cf4f29225f");
        hsaMemberFundingDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberFundingDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail1.setCardNum("cardNum");
        hsaMemberFundingDetail1.setCardName("cardName");
        final List<HsaMemberFundingDetail> entityList1 = Arrays.asList(hsaMemberFundingDetail1);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList1);

        // Confirm HsaElectronicCardService.saveBatch(...).
        final HsaElectronicCard hsaElectronicCard1 = new HsaElectronicCard();
        hsaElectronicCard1.setGuid("afce0919-879a-4438-a9c1-6eef4c917211");
        hsaElectronicCard1.setOperSubjectGuid("operSubjectGuid");
        hsaElectronicCard1.setEnterpriseGuid("enterpriseGuid");
        hsaElectronicCard1.setMemberInfoGuid("memberInfoGuid");
        hsaElectronicCard1.setCardGuid("cardGuid");
        final List<HsaElectronicCard> entityList2 = Arrays.asList(hsaElectronicCard1);
        verify(mockHsaElectronicCardService).saveBatch(entityList2);

        // Confirm HsaCardOpenRuleMapper.updateByGuid(...).
        final HsaCardOpenRule t2 = new HsaCardOpenRule();
        t2.setGuid("9354a6e1-d954-4e90-9734-6b18dfe61454");
        t2.setCardGuid("cardGuid");
        t2.setSendStatus(0);
        t2.setSendOpenCountLimit(0);
        t2.setSurplusSendOpenCountLimit(0);
        verify(mockHsaCardOpenRuleMapper).updateByGuid(t2);

        // Confirm HsaCardBaseInfoMapper.batchUpdateStatus(...).
        final HsaCardBaseInfo hsaCardBaseInfo2 = new HsaCardBaseInfo();
        hsaCardBaseInfo2.setGuid("7283f9e4-076b-431b-a811-8928c88cedca");
        hsaCardBaseInfo2.setCardImage("cardImage");
        hsaCardBaseInfo2.setCardValueMoney(new BigDecimal("0.00"));
        hsaCardBaseInfo2.setCardPayPassword("cardPayPassword");
        hsaCardBaseInfo2.setIsExcess(0);
        hsaCardBaseInfo2.setExcessType(0);
        hsaCardBaseInfo2.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> activityList = Arrays.asList(hsaCardBaseInfo2);
        verify(mockHsaCardBaseInfoMapper).batchUpdateStatus(activityList);
    }

    @Test
    public void testSendOpenElectronicCard_HsaCardOpenRuleMapperSelectListReturnsNoItems() {
        // Setup
        final SendOpenElectronicCardEvent event = new SendOpenElectronicCardEvent();
        final SendOpenCardEvent sendOpenCardEvent = new SendOpenCardEvent();
        sendOpenCardEvent.setCardGuid("cardGuid");
        sendOpenCardEvent.setMemberInfoGuid(Arrays.asList("value"));
        event.setSendOpenCardEvents(Arrays.asList(sendOpenCardEvent));

        when(mockHsaCardOpenRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure HsaCardBaseInfoMapper.selectList(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("7283f9e4-076b-431b-a811-8928c88cedca");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardValueMoney(new BigDecimal("0.00"));
        hsaCardBaseInfo.setCardPayPassword("cardPayPassword");
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCardBaseInfos);

        // Configure HsaOperationMemberInfoMapper.selectList(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("9f7db170-66b8-49d4-a3e8-3be76c08434c");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setMemberGradeInfoName("memberGradeInfoName");
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo);
        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperationMemberInfos);

        // Configure HsaMemberInfoCardMapper.selectList(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardImage("cardImage");
        hsaMemberInfoCard.setCardPayPassword("cardPayPassword");
        final List<HsaMemberInfoCard> hsaMemberInfoCards = Arrays.asList(hsaMemberInfoCard);
        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberInfoCards);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("guid");

        // Configure HsaStoreCardRuleMapper.selectList(...).
        final HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        hsaStoreCardRule.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule.setCardGuid("cardGuid");
        hsaStoreCardRule.setParentGuid("parentGuid");
        hsaStoreCardRule.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRules = Arrays.asList(hsaStoreCardRule);
        when(mockHsaStoreCardRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaStoreCardRules);

        // Configure HsaMemberInfoCardMapper.queryByGuid(...).
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardImage("cardImage");
        hsaMemberInfoCard1.setCardPayPassword("cardPayPassword");
        when(mockHsaMemberInfoCardMapper.queryByGuid("guid")).thenReturn(hsaMemberInfoCard1);

        // Configure MemberCardAssembler.addOpenCardBalanceRecord(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setGuid("112f7e24-f134-4293-b96c-40cf4f29225f");
        hsaMemberFundingDetail.setOperSubjectGuid("operSubjectGuid");
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setCardNum("cardNum");
        hsaMemberFundingDetail.setCardName("cardName");
        final HsaMemberInfoCard memberInfoCard = new HsaMemberInfoCard();
        memberInfoCard.setGuid("guid");
        memberInfoCard.setMemberInfoGuid("memberInfoGuid");
        memberInfoCard.setElectronicCardGuid("electronicCardGuid");
        memberInfoCard.setCardGuid("cardGuid");
        memberInfoCard.setCardAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessTimes(0);
        memberInfoCard.setCardImage("cardImage");
        memberInfoCard.setCardPayPassword("cardPayPassword");
        when(mockMemberCardAssembler.addOpenCardBalanceRecord(memberInfoCard, new BigDecimal("0.00")))
                .thenReturn(hsaMemberFundingDetail);

        // Run the test
        memberInfoCardServiceImplUnderTest.sendOpenElectronicCard(event);

        // Verify the results
        // Confirm MemberCardAssembler.openCardProcessor(...).
        final HsaElectronicCard electronicCard = new HsaElectronicCard();
        electronicCard.setGuid("afce0919-879a-4438-a9c1-6eef4c917211");
        electronicCard.setOperSubjectGuid("operSubjectGuid");
        electronicCard.setEnterpriseGuid("enterpriseGuid");
        electronicCard.setMemberInfoGuid("memberInfoGuid");
        electronicCard.setCardGuid("cardGuid");
        final HsaMemberInfoCard hsaMemberInfoCard2 = new HsaMemberInfoCard();
        hsaMemberInfoCard2.setGuid("guid");
        hsaMemberInfoCard2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard2.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard2.setCardGuid("cardGuid");
        hsaMemberInfoCard2.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessTimes(0);
        hsaMemberInfoCard2.setCardImage("cardImage");
        hsaMemberInfoCard2.setCardPayPassword("cardPayPassword");
        final HsaCardOpenRule hsaCardOpenRule = new HsaCardOpenRule();
        hsaCardOpenRule.setGuid("9354a6e1-d954-4e90-9734-6b18dfe61454");
        hsaCardOpenRule.setCardGuid("cardGuid");
        hsaCardOpenRule.setSendStatus(0);
        hsaCardOpenRule.setSendOpenCountLimit(0);
        hsaCardOpenRule.setSurplusSendOpenCountLimit(0);
        final HsaCardBaseInfo hsaCardBaseInfo1 = new HsaCardBaseInfo();
        hsaCardBaseInfo1.setGuid("7283f9e4-076b-431b-a811-8928c88cedca");
        hsaCardBaseInfo1.setCardImage("cardImage");
        hsaCardBaseInfo1.setCardValueMoney(new BigDecimal("0.00"));
        hsaCardBaseInfo1.setCardPayPassword("cardPayPassword");
        hsaCardBaseInfo1.setIsExcess(0);
        hsaCardBaseInfo1.setExcessType(0);
        hsaCardBaseInfo1.setExcessMoney(new BigDecimal("0.00"));
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("9f7db170-66b8-49d4-a3e8-3be76c08434c");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberAccount("memberAccount");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo1.setMemberGradeInfoName("memberGradeInfoName");
        final HsaMemberInfoCard hsaMemberInfoCard3 = new HsaMemberInfoCard();
        hsaMemberInfoCard3.setGuid("guid");
        hsaMemberInfoCard3.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard3.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard3.setCardGuid("cardGuid");
        hsaMemberInfoCard3.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard3.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard3.setExcessTimes(0);
        hsaMemberInfoCard3.setCardImage("cardImage");
        hsaMemberInfoCard3.setCardPayPassword("cardPayPassword");
        final List<HsaMemberInfoCard> hsaMemberInfoCardList = Arrays.asList(hsaMemberInfoCard3);
        final HsaElectronicCard hsaElectronicCard = new HsaElectronicCard();
        hsaElectronicCard.setGuid("afce0919-879a-4438-a9c1-6eef4c917211");
        hsaElectronicCard.setOperSubjectGuid("operSubjectGuid");
        hsaElectronicCard.setEnterpriseGuid("enterpriseGuid");
        hsaElectronicCard.setMemberInfoGuid("memberInfoGuid");
        hsaElectronicCard.setCardGuid("cardGuid");
        final List<HsaElectronicCard> hsaElectronicCardList = Arrays.asList(hsaElectronicCard);
        verify(mockMemberCardAssembler).openCardProcessor(electronicCard, hsaMemberInfoCard2, hsaCardOpenRule,
                hsaCardBaseInfo1, hsaOperationMemberInfo1, hsaMemberInfoCardList, hsaElectronicCardList);

        // Confirm HsaStoreCardRuleService.mergeStoreProcessor(...).
        final HsaMemberInfoCard hsaMemberInfoCard4 = new HsaMemberInfoCard();
        hsaMemberInfoCard4.setGuid("guid");
        hsaMemberInfoCard4.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard4.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard4.setCardGuid("cardGuid");
        hsaMemberInfoCard4.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard4.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard4.setExcessTimes(0);
        hsaMemberInfoCard4.setCardImage("cardImage");
        hsaMemberInfoCard4.setCardPayPassword("cardPayPassword");
        verify(mockHsaStoreCardRuleService).mergeStoreProcessor(hsaMemberInfoCard4, "cardGuid");

        // Confirm MemberCardAssembler.addStoreCardRuleArray(...).
        final HsaMemberInfoCard hsaMemberInfoCard5 = new HsaMemberInfoCard();
        hsaMemberInfoCard5.setGuid("guid");
        hsaMemberInfoCard5.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard5.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard5.setCardGuid("cardGuid");
        hsaMemberInfoCard5.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard5.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard5.setExcessTimes(0);
        hsaMemberInfoCard5.setCardImage("cardImage");
        hsaMemberInfoCard5.setCardPayPassword("cardPayPassword");
        final HsaStoreCardRule hsaStoreCardRule1 = new HsaStoreCardRule();
        hsaStoreCardRule1.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule1.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule1.setCardGuid("cardGuid");
        hsaStoreCardRule1.setParentGuid("parentGuid");
        hsaStoreCardRule1.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRuleList = Arrays.asList(hsaStoreCardRule1);
        final HsaStoreCardRule hsaStoreCardRule2 = new HsaStoreCardRule();
        hsaStoreCardRule2.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule2.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule2.setCardGuid("cardGuid");
        hsaStoreCardRule2.setParentGuid("parentGuid");
        hsaStoreCardRule2.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRuleArrayList = Arrays.asList(hsaStoreCardRule2);
        verify(mockMemberCardAssembler).addStoreCardRuleArray(hsaMemberInfoCard5, hsaStoreCardRuleList, new HashMap<>(),
                hsaStoreCardRuleArrayList);

        // Confirm HsaStoreCardRuleService.saveBatch(...).
        final HsaStoreCardRule hsaStoreCardRule3 = new HsaStoreCardRule();
        hsaStoreCardRule3.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule3.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule3.setCardGuid("cardGuid");
        hsaStoreCardRule3.setParentGuid("parentGuid");
        hsaStoreCardRule3.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> entityList = Arrays.asList(hsaStoreCardRule3);
        verify(mockHsaStoreCardRuleService).saveBatch(entityList);

        // Confirm HsaMemberInfoCardMapper.updateByGuid(...).
        final HsaMemberInfoCard t = new HsaMemberInfoCard();
        t.setGuid("guid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setElectronicCardGuid("electronicCardGuid");
        t.setCardGuid("cardGuid");
        t.setCardAmount(new BigDecimal("0.00"));
        t.setExcessAmount(new BigDecimal("0.00"));
        t.setExcessTimes(0);
        t.setCardImage("cardImage");
        t.setCardPayPassword("cardPayPassword");
        verify(mockHsaMemberInfoCardMapper).updateByGuid(t);

        // Confirm HsaMemberInfoCardMapper.insert(...).
        final HsaMemberInfoCard t1 = new HsaMemberInfoCard();
        t1.setGuid("guid");
        t1.setMemberInfoGuid("memberInfoGuid");
        t1.setElectronicCardGuid("electronicCardGuid");
        t1.setCardGuid("cardGuid");
        t1.setCardAmount(new BigDecimal("0.00"));
        t1.setExcessAmount(new BigDecimal("0.00"));
        t1.setExcessTimes(0);
        t1.setCardImage("cardImage");
        t1.setCardPayPassword("cardPayPassword");
        verify(mockHsaMemberInfoCardMapper).insert(t1);

        // Confirm MemberBaseFeign.grantFeignCardRights(...).
        final CardEquitiesQO cardGrantEquitiesDTO = new CardEquitiesQO();
        cardGrantEquitiesDTO.setCardGuid(Arrays.asList("value"));
        final SendMemberEquityCard sendMemberEquityCard = new SendMemberEquityCard();
        sendMemberEquityCard.setOperSubjectGuid("operSubjectGuid");
        sendMemberEquityCard.setEnterpriseGuid("enterpriseGuid");
        sendMemberEquityCard.setMemberInfoGuid("memberInfoGuid");
        cardGrantEquitiesDTO.setSendMemberEquityCard(Arrays.asList(sendMemberEquityCard));
        verify(mockMemberBaseFeign).grantFeignCardRights(cardGrantEquitiesDTO);

        // Confirm HsaMemberFundingDetailServiceImpl.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail1 = new HsaMemberFundingDetail();
        hsaMemberFundingDetail1.setGuid("112f7e24-f134-4293-b96c-40cf4f29225f");
        hsaMemberFundingDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberFundingDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail1.setCardNum("cardNum");
        hsaMemberFundingDetail1.setCardName("cardName");
        final List<HsaMemberFundingDetail> entityList1 = Arrays.asList(hsaMemberFundingDetail1);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList1);

        // Confirm HsaElectronicCardService.saveBatch(...).
        final HsaElectronicCard hsaElectronicCard1 = new HsaElectronicCard();
        hsaElectronicCard1.setGuid("afce0919-879a-4438-a9c1-6eef4c917211");
        hsaElectronicCard1.setOperSubjectGuid("operSubjectGuid");
        hsaElectronicCard1.setEnterpriseGuid("enterpriseGuid");
        hsaElectronicCard1.setMemberInfoGuid("memberInfoGuid");
        hsaElectronicCard1.setCardGuid("cardGuid");
        final List<HsaElectronicCard> entityList2 = Arrays.asList(hsaElectronicCard1);
        verify(mockHsaElectronicCardService).saveBatch(entityList2);

        // Confirm HsaCardOpenRuleMapper.updateByGuid(...).
        final HsaCardOpenRule t2 = new HsaCardOpenRule();
        t2.setGuid("9354a6e1-d954-4e90-9734-6b18dfe61454");
        t2.setCardGuid("cardGuid");
        t2.setSendStatus(0);
        t2.setSendOpenCountLimit(0);
        t2.setSurplusSendOpenCountLimit(0);
        verify(mockHsaCardOpenRuleMapper).updateByGuid(t2);

        // Confirm HsaCardBaseInfoMapper.batchUpdateStatus(...).
        final HsaCardBaseInfo hsaCardBaseInfo2 = new HsaCardBaseInfo();
        hsaCardBaseInfo2.setGuid("7283f9e4-076b-431b-a811-8928c88cedca");
        hsaCardBaseInfo2.setCardImage("cardImage");
        hsaCardBaseInfo2.setCardValueMoney(new BigDecimal("0.00"));
        hsaCardBaseInfo2.setCardPayPassword("cardPayPassword");
        hsaCardBaseInfo2.setIsExcess(0);
        hsaCardBaseInfo2.setExcessType(0);
        hsaCardBaseInfo2.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> activityList = Arrays.asList(hsaCardBaseInfo2);
        verify(mockHsaCardBaseInfoMapper).batchUpdateStatus(activityList);
    }

    @Test
    public void testSendOpenElectronicCard_HsaCardBaseInfoMapperSelectListReturnsNoItems() {
        // Setup
        final SendOpenElectronicCardEvent event = new SendOpenElectronicCardEvent();
        final SendOpenCardEvent sendOpenCardEvent = new SendOpenCardEvent();
        sendOpenCardEvent.setCardGuid("cardGuid");
        sendOpenCardEvent.setMemberInfoGuid(Arrays.asList("value"));
        event.setSendOpenCardEvents(Arrays.asList(sendOpenCardEvent));

        // Configure HsaCardOpenRuleMapper.selectList(...).
        final HsaCardOpenRule hsaCardOpenRule = new HsaCardOpenRule();
        hsaCardOpenRule.setGuid("9354a6e1-d954-4e90-9734-6b18dfe61454");
        hsaCardOpenRule.setCardGuid("cardGuid");
        hsaCardOpenRule.setSendStatus(0);
        hsaCardOpenRule.setSendOpenCountLimit(0);
        hsaCardOpenRule.setSurplusSendOpenCountLimit(0);
        final List<HsaCardOpenRule> hsaCardOpenRules = Arrays.asList(hsaCardOpenRule);
        when(mockHsaCardOpenRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCardOpenRules);

        when(mockHsaCardBaseInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure HsaOperationMemberInfoMapper.selectList(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("9f7db170-66b8-49d4-a3e8-3be76c08434c");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setMemberGradeInfoName("memberGradeInfoName");
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo);
        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperationMemberInfos);

        // Configure HsaMemberInfoCardMapper.selectList(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardImage("cardImage");
        hsaMemberInfoCard.setCardPayPassword("cardPayPassword");
        final List<HsaMemberInfoCard> hsaMemberInfoCards = Arrays.asList(hsaMemberInfoCard);
        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberInfoCards);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("guid");

        // Configure HsaStoreCardRuleMapper.selectList(...).
        final HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        hsaStoreCardRule.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule.setCardGuid("cardGuid");
        hsaStoreCardRule.setParentGuid("parentGuid");
        hsaStoreCardRule.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRules = Arrays.asList(hsaStoreCardRule);
        when(mockHsaStoreCardRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaStoreCardRules);

        // Configure HsaMemberInfoCardMapper.queryByGuid(...).
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardImage("cardImage");
        hsaMemberInfoCard1.setCardPayPassword("cardPayPassword");
        when(mockHsaMemberInfoCardMapper.queryByGuid("guid")).thenReturn(hsaMemberInfoCard1);

        // Configure MemberCardAssembler.addOpenCardBalanceRecord(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setGuid("112f7e24-f134-4293-b96c-40cf4f29225f");
        hsaMemberFundingDetail.setOperSubjectGuid("operSubjectGuid");
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setCardNum("cardNum");
        hsaMemberFundingDetail.setCardName("cardName");
        final HsaMemberInfoCard memberInfoCard = new HsaMemberInfoCard();
        memberInfoCard.setGuid("guid");
        memberInfoCard.setMemberInfoGuid("memberInfoGuid");
        memberInfoCard.setElectronicCardGuid("electronicCardGuid");
        memberInfoCard.setCardGuid("cardGuid");
        memberInfoCard.setCardAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessTimes(0);
        memberInfoCard.setCardImage("cardImage");
        memberInfoCard.setCardPayPassword("cardPayPassword");
        when(mockMemberCardAssembler.addOpenCardBalanceRecord(memberInfoCard, new BigDecimal("0.00")))
                .thenReturn(hsaMemberFundingDetail);

        // Run the test
        memberInfoCardServiceImplUnderTest.sendOpenElectronicCard(event);

        // Verify the results
        // Confirm MemberCardAssembler.openCardProcessor(...).
        final HsaElectronicCard electronicCard = new HsaElectronicCard();
        electronicCard.setGuid("afce0919-879a-4438-a9c1-6eef4c917211");
        electronicCard.setOperSubjectGuid("operSubjectGuid");
        electronicCard.setEnterpriseGuid("enterpriseGuid");
        electronicCard.setMemberInfoGuid("memberInfoGuid");
        electronicCard.setCardGuid("cardGuid");
        final HsaMemberInfoCard hsaMemberInfoCard2 = new HsaMemberInfoCard();
        hsaMemberInfoCard2.setGuid("guid");
        hsaMemberInfoCard2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard2.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard2.setCardGuid("cardGuid");
        hsaMemberInfoCard2.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessTimes(0);
        hsaMemberInfoCard2.setCardImage("cardImage");
        hsaMemberInfoCard2.setCardPayPassword("cardPayPassword");
        final HsaCardOpenRule hsaCardOpenRule1 = new HsaCardOpenRule();
        hsaCardOpenRule1.setGuid("9354a6e1-d954-4e90-9734-6b18dfe61454");
        hsaCardOpenRule1.setCardGuid("cardGuid");
        hsaCardOpenRule1.setSendStatus(0);
        hsaCardOpenRule1.setSendOpenCountLimit(0);
        hsaCardOpenRule1.setSurplusSendOpenCountLimit(0);
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("7283f9e4-076b-431b-a811-8928c88cedca");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardValueMoney(new BigDecimal("0.00"));
        hsaCardBaseInfo.setCardPayPassword("cardPayPassword");
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("9f7db170-66b8-49d4-a3e8-3be76c08434c");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberAccount("memberAccount");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo1.setMemberGradeInfoName("memberGradeInfoName");
        final HsaMemberInfoCard hsaMemberInfoCard3 = new HsaMemberInfoCard();
        hsaMemberInfoCard3.setGuid("guid");
        hsaMemberInfoCard3.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard3.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard3.setCardGuid("cardGuid");
        hsaMemberInfoCard3.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard3.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard3.setExcessTimes(0);
        hsaMemberInfoCard3.setCardImage("cardImage");
        hsaMemberInfoCard3.setCardPayPassword("cardPayPassword");
        final List<HsaMemberInfoCard> hsaMemberInfoCardList = Arrays.asList(hsaMemberInfoCard3);
        final HsaElectronicCard hsaElectronicCard = new HsaElectronicCard();
        hsaElectronicCard.setGuid("afce0919-879a-4438-a9c1-6eef4c917211");
        hsaElectronicCard.setOperSubjectGuid("operSubjectGuid");
        hsaElectronicCard.setEnterpriseGuid("enterpriseGuid");
        hsaElectronicCard.setMemberInfoGuid("memberInfoGuid");
        hsaElectronicCard.setCardGuid("cardGuid");
        final List<HsaElectronicCard> hsaElectronicCardList = Arrays.asList(hsaElectronicCard);
        verify(mockMemberCardAssembler).openCardProcessor(electronicCard, hsaMemberInfoCard2, hsaCardOpenRule1,
                hsaCardBaseInfo, hsaOperationMemberInfo1, hsaMemberInfoCardList, hsaElectronicCardList);

        // Confirm HsaStoreCardRuleService.mergeStoreProcessor(...).
        final HsaMemberInfoCard hsaMemberInfoCard4 = new HsaMemberInfoCard();
        hsaMemberInfoCard4.setGuid("guid");
        hsaMemberInfoCard4.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard4.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard4.setCardGuid("cardGuid");
        hsaMemberInfoCard4.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard4.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard4.setExcessTimes(0);
        hsaMemberInfoCard4.setCardImage("cardImage");
        hsaMemberInfoCard4.setCardPayPassword("cardPayPassword");
        verify(mockHsaStoreCardRuleService).mergeStoreProcessor(hsaMemberInfoCard4, "cardGuid");

        // Confirm MemberCardAssembler.addStoreCardRuleArray(...).
        final HsaMemberInfoCard hsaMemberInfoCard5 = new HsaMemberInfoCard();
        hsaMemberInfoCard5.setGuid("guid");
        hsaMemberInfoCard5.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard5.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard5.setCardGuid("cardGuid");
        hsaMemberInfoCard5.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard5.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard5.setExcessTimes(0);
        hsaMemberInfoCard5.setCardImage("cardImage");
        hsaMemberInfoCard5.setCardPayPassword("cardPayPassword");
        final HsaStoreCardRule hsaStoreCardRule1 = new HsaStoreCardRule();
        hsaStoreCardRule1.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule1.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule1.setCardGuid("cardGuid");
        hsaStoreCardRule1.setParentGuid("parentGuid");
        hsaStoreCardRule1.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRuleList = Arrays.asList(hsaStoreCardRule1);
        final HsaStoreCardRule hsaStoreCardRule2 = new HsaStoreCardRule();
        hsaStoreCardRule2.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule2.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule2.setCardGuid("cardGuid");
        hsaStoreCardRule2.setParentGuid("parentGuid");
        hsaStoreCardRule2.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRuleArrayList = Arrays.asList(hsaStoreCardRule2);
        verify(mockMemberCardAssembler).addStoreCardRuleArray(hsaMemberInfoCard5, hsaStoreCardRuleList, new HashMap<>(),
                hsaStoreCardRuleArrayList);

        // Confirm HsaStoreCardRuleService.saveBatch(...).
        final HsaStoreCardRule hsaStoreCardRule3 = new HsaStoreCardRule();
        hsaStoreCardRule3.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule3.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule3.setCardGuid("cardGuid");
        hsaStoreCardRule3.setParentGuid("parentGuid");
        hsaStoreCardRule3.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> entityList = Arrays.asList(hsaStoreCardRule3);
        verify(mockHsaStoreCardRuleService).saveBatch(entityList);

        // Confirm HsaMemberInfoCardMapper.updateByGuid(...).
        final HsaMemberInfoCard t = new HsaMemberInfoCard();
        t.setGuid("guid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setElectronicCardGuid("electronicCardGuid");
        t.setCardGuid("cardGuid");
        t.setCardAmount(new BigDecimal("0.00"));
        t.setExcessAmount(new BigDecimal("0.00"));
        t.setExcessTimes(0);
        t.setCardImage("cardImage");
        t.setCardPayPassword("cardPayPassword");
        verify(mockHsaMemberInfoCardMapper).updateByGuid(t);

        // Confirm HsaMemberInfoCardMapper.insert(...).
        final HsaMemberInfoCard t1 = new HsaMemberInfoCard();
        t1.setGuid("guid");
        t1.setMemberInfoGuid("memberInfoGuid");
        t1.setElectronicCardGuid("electronicCardGuid");
        t1.setCardGuid("cardGuid");
        t1.setCardAmount(new BigDecimal("0.00"));
        t1.setExcessAmount(new BigDecimal("0.00"));
        t1.setExcessTimes(0);
        t1.setCardImage("cardImage");
        t1.setCardPayPassword("cardPayPassword");
        verify(mockHsaMemberInfoCardMapper).insert(t1);

        // Confirm MemberBaseFeign.grantFeignCardRights(...).
        final CardEquitiesQO cardGrantEquitiesDTO = new CardEquitiesQO();
        cardGrantEquitiesDTO.setCardGuid(Arrays.asList("value"));
        final SendMemberEquityCard sendMemberEquityCard = new SendMemberEquityCard();
        sendMemberEquityCard.setOperSubjectGuid("operSubjectGuid");
        sendMemberEquityCard.setEnterpriseGuid("enterpriseGuid");
        sendMemberEquityCard.setMemberInfoGuid("memberInfoGuid");
        cardGrantEquitiesDTO.setSendMemberEquityCard(Arrays.asList(sendMemberEquityCard));
        verify(mockMemberBaseFeign).grantFeignCardRights(cardGrantEquitiesDTO);

        // Confirm HsaMemberFundingDetailServiceImpl.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail1 = new HsaMemberFundingDetail();
        hsaMemberFundingDetail1.setGuid("112f7e24-f134-4293-b96c-40cf4f29225f");
        hsaMemberFundingDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberFundingDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail1.setCardNum("cardNum");
        hsaMemberFundingDetail1.setCardName("cardName");
        final List<HsaMemberFundingDetail> entityList1 = Arrays.asList(hsaMemberFundingDetail1);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList1);

        // Confirm HsaElectronicCardService.saveBatch(...).
        final HsaElectronicCard hsaElectronicCard1 = new HsaElectronicCard();
        hsaElectronicCard1.setGuid("afce0919-879a-4438-a9c1-6eef4c917211");
        hsaElectronicCard1.setOperSubjectGuid("operSubjectGuid");
        hsaElectronicCard1.setEnterpriseGuid("enterpriseGuid");
        hsaElectronicCard1.setMemberInfoGuid("memberInfoGuid");
        hsaElectronicCard1.setCardGuid("cardGuid");
        final List<HsaElectronicCard> entityList2 = Arrays.asList(hsaElectronicCard1);
        verify(mockHsaElectronicCardService).saveBatch(entityList2);

        // Confirm HsaCardOpenRuleMapper.updateByGuid(...).
        final HsaCardOpenRule t2 = new HsaCardOpenRule();
        t2.setGuid("9354a6e1-d954-4e90-9734-6b18dfe61454");
        t2.setCardGuid("cardGuid");
        t2.setSendStatus(0);
        t2.setSendOpenCountLimit(0);
        t2.setSurplusSendOpenCountLimit(0);
        verify(mockHsaCardOpenRuleMapper).updateByGuid(t2);

        // Confirm HsaCardBaseInfoMapper.batchUpdateStatus(...).
        final HsaCardBaseInfo hsaCardBaseInfo1 = new HsaCardBaseInfo();
        hsaCardBaseInfo1.setGuid("7283f9e4-076b-431b-a811-8928c88cedca");
        hsaCardBaseInfo1.setCardImage("cardImage");
        hsaCardBaseInfo1.setCardValueMoney(new BigDecimal("0.00"));
        hsaCardBaseInfo1.setCardPayPassword("cardPayPassword");
        hsaCardBaseInfo1.setIsExcess(0);
        hsaCardBaseInfo1.setExcessType(0);
        hsaCardBaseInfo1.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> activityList = Arrays.asList(hsaCardBaseInfo1);
        verify(mockHsaCardBaseInfoMapper).batchUpdateStatus(activityList);
    }

    @Test
    public void testSendOpenElectronicCard_HsaOperationMemberInfoMapperReturnsNoItems() {
        // Setup
        final SendOpenElectronicCardEvent event = new SendOpenElectronicCardEvent();
        final SendOpenCardEvent sendOpenCardEvent = new SendOpenCardEvent();
        sendOpenCardEvent.setCardGuid("cardGuid");
        sendOpenCardEvent.setMemberInfoGuid(Arrays.asList("value"));
        event.setSendOpenCardEvents(Arrays.asList(sendOpenCardEvent));

        // Configure HsaCardOpenRuleMapper.selectList(...).
        final HsaCardOpenRule hsaCardOpenRule = new HsaCardOpenRule();
        hsaCardOpenRule.setGuid("9354a6e1-d954-4e90-9734-6b18dfe61454");
        hsaCardOpenRule.setCardGuid("cardGuid");
        hsaCardOpenRule.setSendStatus(0);
        hsaCardOpenRule.setSendOpenCountLimit(0);
        hsaCardOpenRule.setSurplusSendOpenCountLimit(0);
        final List<HsaCardOpenRule> hsaCardOpenRules = Arrays.asList(hsaCardOpenRule);
        when(mockHsaCardOpenRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCardOpenRules);

        // Configure HsaCardBaseInfoMapper.selectList(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("7283f9e4-076b-431b-a811-8928c88cedca");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardValueMoney(new BigDecimal("0.00"));
        hsaCardBaseInfo.setCardPayPassword("cardPayPassword");
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCardBaseInfos);

        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure HsaMemberInfoCardMapper.selectList(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardImage("cardImage");
        hsaMemberInfoCard.setCardPayPassword("cardPayPassword");
        final List<HsaMemberInfoCard> hsaMemberInfoCards = Arrays.asList(hsaMemberInfoCard);
        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberInfoCards);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("guid");

        // Configure HsaStoreCardRuleMapper.selectList(...).
        final HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        hsaStoreCardRule.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule.setCardGuid("cardGuid");
        hsaStoreCardRule.setParentGuid("parentGuid");
        hsaStoreCardRule.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRules = Arrays.asList(hsaStoreCardRule);
        when(mockHsaStoreCardRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaStoreCardRules);

        // Configure HsaMemberInfoCardMapper.queryByGuid(...).
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardImage("cardImage");
        hsaMemberInfoCard1.setCardPayPassword("cardPayPassword");
        when(mockHsaMemberInfoCardMapper.queryByGuid("guid")).thenReturn(hsaMemberInfoCard1);

        // Configure MemberCardAssembler.addOpenCardBalanceRecord(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setGuid("112f7e24-f134-4293-b96c-40cf4f29225f");
        hsaMemberFundingDetail.setOperSubjectGuid("operSubjectGuid");
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setCardNum("cardNum");
        hsaMemberFundingDetail.setCardName("cardName");
        final HsaMemberInfoCard memberInfoCard = new HsaMemberInfoCard();
        memberInfoCard.setGuid("guid");
        memberInfoCard.setMemberInfoGuid("memberInfoGuid");
        memberInfoCard.setElectronicCardGuid("electronicCardGuid");
        memberInfoCard.setCardGuid("cardGuid");
        memberInfoCard.setCardAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessTimes(0);
        memberInfoCard.setCardImage("cardImage");
        memberInfoCard.setCardPayPassword("cardPayPassword");
        when(mockMemberCardAssembler.addOpenCardBalanceRecord(memberInfoCard, new BigDecimal("0.00")))
                .thenReturn(hsaMemberFundingDetail);

        // Run the test
        memberInfoCardServiceImplUnderTest.sendOpenElectronicCard(event);

        // Verify the results
        // Confirm MemberCardAssembler.openCardProcessor(...).
        final HsaElectronicCard electronicCard = new HsaElectronicCard();
        electronicCard.setGuid("afce0919-879a-4438-a9c1-6eef4c917211");
        electronicCard.setOperSubjectGuid("operSubjectGuid");
        electronicCard.setEnterpriseGuid("enterpriseGuid");
        electronicCard.setMemberInfoGuid("memberInfoGuid");
        electronicCard.setCardGuid("cardGuid");
        final HsaMemberInfoCard hsaMemberInfoCard2 = new HsaMemberInfoCard();
        hsaMemberInfoCard2.setGuid("guid");
        hsaMemberInfoCard2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard2.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard2.setCardGuid("cardGuid");
        hsaMemberInfoCard2.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessTimes(0);
        hsaMemberInfoCard2.setCardImage("cardImage");
        hsaMemberInfoCard2.setCardPayPassword("cardPayPassword");
        final HsaCardOpenRule hsaCardOpenRule1 = new HsaCardOpenRule();
        hsaCardOpenRule1.setGuid("9354a6e1-d954-4e90-9734-6b18dfe61454");
        hsaCardOpenRule1.setCardGuid("cardGuid");
        hsaCardOpenRule1.setSendStatus(0);
        hsaCardOpenRule1.setSendOpenCountLimit(0);
        hsaCardOpenRule1.setSurplusSendOpenCountLimit(0);
        final HsaCardBaseInfo hsaCardBaseInfo1 = new HsaCardBaseInfo();
        hsaCardBaseInfo1.setGuid("7283f9e4-076b-431b-a811-8928c88cedca");
        hsaCardBaseInfo1.setCardImage("cardImage");
        hsaCardBaseInfo1.setCardValueMoney(new BigDecimal("0.00"));
        hsaCardBaseInfo1.setCardPayPassword("cardPayPassword");
        hsaCardBaseInfo1.setIsExcess(0);
        hsaCardBaseInfo1.setExcessType(0);
        hsaCardBaseInfo1.setExcessMoney(new BigDecimal("0.00"));
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("9f7db170-66b8-49d4-a3e8-3be76c08434c");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setMemberGradeInfoName("memberGradeInfoName");
        final HsaMemberInfoCard hsaMemberInfoCard3 = new HsaMemberInfoCard();
        hsaMemberInfoCard3.setGuid("guid");
        hsaMemberInfoCard3.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard3.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard3.setCardGuid("cardGuid");
        hsaMemberInfoCard3.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard3.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard3.setExcessTimes(0);
        hsaMemberInfoCard3.setCardImage("cardImage");
        hsaMemberInfoCard3.setCardPayPassword("cardPayPassword");
        final List<HsaMemberInfoCard> hsaMemberInfoCardList = Arrays.asList(hsaMemberInfoCard3);
        final HsaElectronicCard hsaElectronicCard = new HsaElectronicCard();
        hsaElectronicCard.setGuid("afce0919-879a-4438-a9c1-6eef4c917211");
        hsaElectronicCard.setOperSubjectGuid("operSubjectGuid");
        hsaElectronicCard.setEnterpriseGuid("enterpriseGuid");
        hsaElectronicCard.setMemberInfoGuid("memberInfoGuid");
        hsaElectronicCard.setCardGuid("cardGuid");
        final List<HsaElectronicCard> hsaElectronicCardList = Arrays.asList(hsaElectronicCard);
        verify(mockMemberCardAssembler).openCardProcessor(electronicCard, hsaMemberInfoCard2, hsaCardOpenRule1,
                hsaCardBaseInfo1, hsaOperationMemberInfo, hsaMemberInfoCardList, hsaElectronicCardList);

        // Confirm HsaStoreCardRuleService.mergeStoreProcessor(...).
        final HsaMemberInfoCard hsaMemberInfoCard4 = new HsaMemberInfoCard();
        hsaMemberInfoCard4.setGuid("guid");
        hsaMemberInfoCard4.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard4.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard4.setCardGuid("cardGuid");
        hsaMemberInfoCard4.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard4.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard4.setExcessTimes(0);
        hsaMemberInfoCard4.setCardImage("cardImage");
        hsaMemberInfoCard4.setCardPayPassword("cardPayPassword");
        verify(mockHsaStoreCardRuleService).mergeStoreProcessor(hsaMemberInfoCard4, "cardGuid");

        // Confirm MemberCardAssembler.addStoreCardRuleArray(...).
        final HsaMemberInfoCard hsaMemberInfoCard5 = new HsaMemberInfoCard();
        hsaMemberInfoCard5.setGuid("guid");
        hsaMemberInfoCard5.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard5.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard5.setCardGuid("cardGuid");
        hsaMemberInfoCard5.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard5.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard5.setExcessTimes(0);
        hsaMemberInfoCard5.setCardImage("cardImage");
        hsaMemberInfoCard5.setCardPayPassword("cardPayPassword");
        final HsaStoreCardRule hsaStoreCardRule1 = new HsaStoreCardRule();
        hsaStoreCardRule1.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule1.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule1.setCardGuid("cardGuid");
        hsaStoreCardRule1.setParentGuid("parentGuid");
        hsaStoreCardRule1.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRuleList = Arrays.asList(hsaStoreCardRule1);
        final HsaStoreCardRule hsaStoreCardRule2 = new HsaStoreCardRule();
        hsaStoreCardRule2.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule2.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule2.setCardGuid("cardGuid");
        hsaStoreCardRule2.setParentGuid("parentGuid");
        hsaStoreCardRule2.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRuleArrayList = Arrays.asList(hsaStoreCardRule2);
        verify(mockMemberCardAssembler).addStoreCardRuleArray(hsaMemberInfoCard5, hsaStoreCardRuleList, new HashMap<>(),
                hsaStoreCardRuleArrayList);

        // Confirm HsaStoreCardRuleService.saveBatch(...).
        final HsaStoreCardRule hsaStoreCardRule3 = new HsaStoreCardRule();
        hsaStoreCardRule3.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule3.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule3.setCardGuid("cardGuid");
        hsaStoreCardRule3.setParentGuid("parentGuid");
        hsaStoreCardRule3.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> entityList = Arrays.asList(hsaStoreCardRule3);
        verify(mockHsaStoreCardRuleService).saveBatch(entityList);

        // Confirm HsaMemberInfoCardMapper.updateByGuid(...).
        final HsaMemberInfoCard t = new HsaMemberInfoCard();
        t.setGuid("guid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setElectronicCardGuid("electronicCardGuid");
        t.setCardGuid("cardGuid");
        t.setCardAmount(new BigDecimal("0.00"));
        t.setExcessAmount(new BigDecimal("0.00"));
        t.setExcessTimes(0);
        t.setCardImage("cardImage");
        t.setCardPayPassword("cardPayPassword");
        verify(mockHsaMemberInfoCardMapper).updateByGuid(t);

        // Confirm HsaMemberInfoCardMapper.insert(...).
        final HsaMemberInfoCard t1 = new HsaMemberInfoCard();
        t1.setGuid("guid");
        t1.setMemberInfoGuid("memberInfoGuid");
        t1.setElectronicCardGuid("electronicCardGuid");
        t1.setCardGuid("cardGuid");
        t1.setCardAmount(new BigDecimal("0.00"));
        t1.setExcessAmount(new BigDecimal("0.00"));
        t1.setExcessTimes(0);
        t1.setCardImage("cardImage");
        t1.setCardPayPassword("cardPayPassword");
        verify(mockHsaMemberInfoCardMapper).insert(t1);

        // Confirm MemberBaseFeign.grantFeignCardRights(...).
        final CardEquitiesQO cardGrantEquitiesDTO = new CardEquitiesQO();
        cardGrantEquitiesDTO.setCardGuid(Arrays.asList("value"));
        final SendMemberEquityCard sendMemberEquityCard = new SendMemberEquityCard();
        sendMemberEquityCard.setOperSubjectGuid("operSubjectGuid");
        sendMemberEquityCard.setEnterpriseGuid("enterpriseGuid");
        sendMemberEquityCard.setMemberInfoGuid("memberInfoGuid");
        cardGrantEquitiesDTO.setSendMemberEquityCard(Arrays.asList(sendMemberEquityCard));
        verify(mockMemberBaseFeign).grantFeignCardRights(cardGrantEquitiesDTO);

        // Confirm HsaMemberFundingDetailServiceImpl.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail1 = new HsaMemberFundingDetail();
        hsaMemberFundingDetail1.setGuid("112f7e24-f134-4293-b96c-40cf4f29225f");
        hsaMemberFundingDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberFundingDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail1.setCardNum("cardNum");
        hsaMemberFundingDetail1.setCardName("cardName");
        final List<HsaMemberFundingDetail> entityList1 = Arrays.asList(hsaMemberFundingDetail1);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList1);

        // Confirm HsaElectronicCardService.saveBatch(...).
        final HsaElectronicCard hsaElectronicCard1 = new HsaElectronicCard();
        hsaElectronicCard1.setGuid("afce0919-879a-4438-a9c1-6eef4c917211");
        hsaElectronicCard1.setOperSubjectGuid("operSubjectGuid");
        hsaElectronicCard1.setEnterpriseGuid("enterpriseGuid");
        hsaElectronicCard1.setMemberInfoGuid("memberInfoGuid");
        hsaElectronicCard1.setCardGuid("cardGuid");
        final List<HsaElectronicCard> entityList2 = Arrays.asList(hsaElectronicCard1);
        verify(mockHsaElectronicCardService).saveBatch(entityList2);

        // Confirm HsaCardOpenRuleMapper.updateByGuid(...).
        final HsaCardOpenRule t2 = new HsaCardOpenRule();
        t2.setGuid("9354a6e1-d954-4e90-9734-6b18dfe61454");
        t2.setCardGuid("cardGuid");
        t2.setSendStatus(0);
        t2.setSendOpenCountLimit(0);
        t2.setSurplusSendOpenCountLimit(0);
        verify(mockHsaCardOpenRuleMapper).updateByGuid(t2);

        // Confirm HsaCardBaseInfoMapper.batchUpdateStatus(...).
        final HsaCardBaseInfo hsaCardBaseInfo2 = new HsaCardBaseInfo();
        hsaCardBaseInfo2.setGuid("7283f9e4-076b-431b-a811-8928c88cedca");
        hsaCardBaseInfo2.setCardImage("cardImage");
        hsaCardBaseInfo2.setCardValueMoney(new BigDecimal("0.00"));
        hsaCardBaseInfo2.setCardPayPassword("cardPayPassword");
        hsaCardBaseInfo2.setIsExcess(0);
        hsaCardBaseInfo2.setExcessType(0);
        hsaCardBaseInfo2.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> activityList = Arrays.asList(hsaCardBaseInfo2);
        verify(mockHsaCardBaseInfoMapper).batchUpdateStatus(activityList);
    }

    @Test
    public void testSendOpenElectronicCard_HsaMemberInfoCardMapperSelectListReturnsNoItems() {
        // Setup
        final SendOpenElectronicCardEvent event = new SendOpenElectronicCardEvent();
        final SendOpenCardEvent sendOpenCardEvent = new SendOpenCardEvent();
        sendOpenCardEvent.setCardGuid("cardGuid");
        sendOpenCardEvent.setMemberInfoGuid(Arrays.asList("value"));
        event.setSendOpenCardEvents(Arrays.asList(sendOpenCardEvent));

        // Configure HsaCardOpenRuleMapper.selectList(...).
        final HsaCardOpenRule hsaCardOpenRule = new HsaCardOpenRule();
        hsaCardOpenRule.setGuid("9354a6e1-d954-4e90-9734-6b18dfe61454");
        hsaCardOpenRule.setCardGuid("cardGuid");
        hsaCardOpenRule.setSendStatus(0);
        hsaCardOpenRule.setSendOpenCountLimit(0);
        hsaCardOpenRule.setSurplusSendOpenCountLimit(0);
        final List<HsaCardOpenRule> hsaCardOpenRules = Arrays.asList(hsaCardOpenRule);
        when(mockHsaCardOpenRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCardOpenRules);

        // Configure HsaCardBaseInfoMapper.selectList(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("7283f9e4-076b-431b-a811-8928c88cedca");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardValueMoney(new BigDecimal("0.00"));
        hsaCardBaseInfo.setCardPayPassword("cardPayPassword");
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCardBaseInfos);

        // Configure HsaOperationMemberInfoMapper.selectList(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("9f7db170-66b8-49d4-a3e8-3be76c08434c");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setMemberGradeInfoName("memberGradeInfoName");
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo);
        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperationMemberInfos);

        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("guid");

        // Configure HsaStoreCardRuleMapper.selectList(...).
        final HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        hsaStoreCardRule.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule.setCardGuid("cardGuid");
        hsaStoreCardRule.setParentGuid("parentGuid");
        hsaStoreCardRule.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRules = Arrays.asList(hsaStoreCardRule);
        when(mockHsaStoreCardRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaStoreCardRules);

        // Configure HsaMemberInfoCardMapper.queryByGuid(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardImage("cardImage");
        hsaMemberInfoCard.setCardPayPassword("cardPayPassword");
        when(mockHsaMemberInfoCardMapper.queryByGuid("guid")).thenReturn(hsaMemberInfoCard);

        // Configure MemberCardAssembler.addOpenCardBalanceRecord(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setGuid("112f7e24-f134-4293-b96c-40cf4f29225f");
        hsaMemberFundingDetail.setOperSubjectGuid("operSubjectGuid");
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setCardNum("cardNum");
        hsaMemberFundingDetail.setCardName("cardName");
        final HsaMemberInfoCard memberInfoCard = new HsaMemberInfoCard();
        memberInfoCard.setGuid("guid");
        memberInfoCard.setMemberInfoGuid("memberInfoGuid");
        memberInfoCard.setElectronicCardGuid("electronicCardGuid");
        memberInfoCard.setCardGuid("cardGuid");
        memberInfoCard.setCardAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessTimes(0);
        memberInfoCard.setCardImage("cardImage");
        memberInfoCard.setCardPayPassword("cardPayPassword");
        when(mockMemberCardAssembler.addOpenCardBalanceRecord(memberInfoCard, new BigDecimal("0.00")))
                .thenReturn(hsaMemberFundingDetail);

        // Run the test
        memberInfoCardServiceImplUnderTest.sendOpenElectronicCard(event);

        // Verify the results
        // Confirm MemberCardAssembler.openCardProcessor(...).
        final HsaElectronicCard electronicCard = new HsaElectronicCard();
        electronicCard.setGuid("afce0919-879a-4438-a9c1-6eef4c917211");
        electronicCard.setOperSubjectGuid("operSubjectGuid");
        electronicCard.setEnterpriseGuid("enterpriseGuid");
        electronicCard.setMemberInfoGuid("memberInfoGuid");
        electronicCard.setCardGuid("cardGuid");
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardImage("cardImage");
        hsaMemberInfoCard1.setCardPayPassword("cardPayPassword");
        final HsaCardOpenRule hsaCardOpenRule1 = new HsaCardOpenRule();
        hsaCardOpenRule1.setGuid("9354a6e1-d954-4e90-9734-6b18dfe61454");
        hsaCardOpenRule1.setCardGuid("cardGuid");
        hsaCardOpenRule1.setSendStatus(0);
        hsaCardOpenRule1.setSendOpenCountLimit(0);
        hsaCardOpenRule1.setSurplusSendOpenCountLimit(0);
        final HsaCardBaseInfo hsaCardBaseInfo1 = new HsaCardBaseInfo();
        hsaCardBaseInfo1.setGuid("7283f9e4-076b-431b-a811-8928c88cedca");
        hsaCardBaseInfo1.setCardImage("cardImage");
        hsaCardBaseInfo1.setCardValueMoney(new BigDecimal("0.00"));
        hsaCardBaseInfo1.setCardPayPassword("cardPayPassword");
        hsaCardBaseInfo1.setIsExcess(0);
        hsaCardBaseInfo1.setExcessType(0);
        hsaCardBaseInfo1.setExcessMoney(new BigDecimal("0.00"));
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("9f7db170-66b8-49d4-a3e8-3be76c08434c");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberAccount("memberAccount");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo1.setMemberGradeInfoName("memberGradeInfoName");
        final HsaMemberInfoCard hsaMemberInfoCard2 = new HsaMemberInfoCard();
        hsaMemberInfoCard2.setGuid("guid");
        hsaMemberInfoCard2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard2.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard2.setCardGuid("cardGuid");
        hsaMemberInfoCard2.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessTimes(0);
        hsaMemberInfoCard2.setCardImage("cardImage");
        hsaMemberInfoCard2.setCardPayPassword("cardPayPassword");
        final List<HsaMemberInfoCard> hsaMemberInfoCardList = Arrays.asList(hsaMemberInfoCard2);
        final HsaElectronicCard hsaElectronicCard = new HsaElectronicCard();
        hsaElectronicCard.setGuid("afce0919-879a-4438-a9c1-6eef4c917211");
        hsaElectronicCard.setOperSubjectGuid("operSubjectGuid");
        hsaElectronicCard.setEnterpriseGuid("enterpriseGuid");
        hsaElectronicCard.setMemberInfoGuid("memberInfoGuid");
        hsaElectronicCard.setCardGuid("cardGuid");
        final List<HsaElectronicCard> hsaElectronicCardList = Arrays.asList(hsaElectronicCard);
        verify(mockMemberCardAssembler).openCardProcessor(electronicCard, hsaMemberInfoCard1, hsaCardOpenRule1,
                hsaCardBaseInfo1, hsaOperationMemberInfo1, hsaMemberInfoCardList, hsaElectronicCardList);

        // Confirm HsaStoreCardRuleService.mergeStoreProcessor(...).
        final HsaMemberInfoCard hsaMemberInfoCard3 = new HsaMemberInfoCard();
        hsaMemberInfoCard3.setGuid("guid");
        hsaMemberInfoCard3.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard3.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard3.setCardGuid("cardGuid");
        hsaMemberInfoCard3.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard3.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard3.setExcessTimes(0);
        hsaMemberInfoCard3.setCardImage("cardImage");
        hsaMemberInfoCard3.setCardPayPassword("cardPayPassword");
        verify(mockHsaStoreCardRuleService).mergeStoreProcessor(hsaMemberInfoCard3, "cardGuid");

        // Confirm MemberCardAssembler.addStoreCardRuleArray(...).
        final HsaMemberInfoCard hsaMemberInfoCard4 = new HsaMemberInfoCard();
        hsaMemberInfoCard4.setGuid("guid");
        hsaMemberInfoCard4.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard4.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard4.setCardGuid("cardGuid");
        hsaMemberInfoCard4.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard4.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard4.setExcessTimes(0);
        hsaMemberInfoCard4.setCardImage("cardImage");
        hsaMemberInfoCard4.setCardPayPassword("cardPayPassword");
        final HsaStoreCardRule hsaStoreCardRule1 = new HsaStoreCardRule();
        hsaStoreCardRule1.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule1.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule1.setCardGuid("cardGuid");
        hsaStoreCardRule1.setParentGuid("parentGuid");
        hsaStoreCardRule1.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRuleList = Arrays.asList(hsaStoreCardRule1);
        final HsaStoreCardRule hsaStoreCardRule2 = new HsaStoreCardRule();
        hsaStoreCardRule2.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule2.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule2.setCardGuid("cardGuid");
        hsaStoreCardRule2.setParentGuid("parentGuid");
        hsaStoreCardRule2.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRuleArrayList = Arrays.asList(hsaStoreCardRule2);
        verify(mockMemberCardAssembler).addStoreCardRuleArray(hsaMemberInfoCard4, hsaStoreCardRuleList, new HashMap<>(),
                hsaStoreCardRuleArrayList);

        // Confirm HsaStoreCardRuleService.saveBatch(...).
        final HsaStoreCardRule hsaStoreCardRule3 = new HsaStoreCardRule();
        hsaStoreCardRule3.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule3.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule3.setCardGuid("cardGuid");
        hsaStoreCardRule3.setParentGuid("parentGuid");
        hsaStoreCardRule3.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> entityList = Arrays.asList(hsaStoreCardRule3);
        verify(mockHsaStoreCardRuleService).saveBatch(entityList);

        // Confirm HsaMemberInfoCardMapper.updateByGuid(...).
        final HsaMemberInfoCard t = new HsaMemberInfoCard();
        t.setGuid("guid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setElectronicCardGuid("electronicCardGuid");
        t.setCardGuid("cardGuid");
        t.setCardAmount(new BigDecimal("0.00"));
        t.setExcessAmount(new BigDecimal("0.00"));
        t.setExcessTimes(0);
        t.setCardImage("cardImage");
        t.setCardPayPassword("cardPayPassword");
        verify(mockHsaMemberInfoCardMapper).updateByGuid(t);

        // Confirm HsaMemberInfoCardMapper.insert(...).
        final HsaMemberInfoCard t1 = new HsaMemberInfoCard();
        t1.setGuid("guid");
        t1.setMemberInfoGuid("memberInfoGuid");
        t1.setElectronicCardGuid("electronicCardGuid");
        t1.setCardGuid("cardGuid");
        t1.setCardAmount(new BigDecimal("0.00"));
        t1.setExcessAmount(new BigDecimal("0.00"));
        t1.setExcessTimes(0);
        t1.setCardImage("cardImage");
        t1.setCardPayPassword("cardPayPassword");
        verify(mockHsaMemberInfoCardMapper).insert(t1);

        // Confirm MemberBaseFeign.grantFeignCardRights(...).
        final CardEquitiesQO cardGrantEquitiesDTO = new CardEquitiesQO();
        cardGrantEquitiesDTO.setCardGuid(Arrays.asList("value"));
        final SendMemberEquityCard sendMemberEquityCard = new SendMemberEquityCard();
        sendMemberEquityCard.setOperSubjectGuid("operSubjectGuid");
        sendMemberEquityCard.setEnterpriseGuid("enterpriseGuid");
        sendMemberEquityCard.setMemberInfoGuid("memberInfoGuid");
        cardGrantEquitiesDTO.setSendMemberEquityCard(Arrays.asList(sendMemberEquityCard));
        verify(mockMemberBaseFeign).grantFeignCardRights(cardGrantEquitiesDTO);

        // Confirm HsaMemberFundingDetailServiceImpl.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail1 = new HsaMemberFundingDetail();
        hsaMemberFundingDetail1.setGuid("112f7e24-f134-4293-b96c-40cf4f29225f");
        hsaMemberFundingDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberFundingDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail1.setCardNum("cardNum");
        hsaMemberFundingDetail1.setCardName("cardName");
        final List<HsaMemberFundingDetail> entityList1 = Arrays.asList(hsaMemberFundingDetail1);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList1);

        // Confirm HsaElectronicCardService.saveBatch(...).
        final HsaElectronicCard hsaElectronicCard1 = new HsaElectronicCard();
        hsaElectronicCard1.setGuid("afce0919-879a-4438-a9c1-6eef4c917211");
        hsaElectronicCard1.setOperSubjectGuid("operSubjectGuid");
        hsaElectronicCard1.setEnterpriseGuid("enterpriseGuid");
        hsaElectronicCard1.setMemberInfoGuid("memberInfoGuid");
        hsaElectronicCard1.setCardGuid("cardGuid");
        final List<HsaElectronicCard> entityList2 = Arrays.asList(hsaElectronicCard1);
        verify(mockHsaElectronicCardService).saveBatch(entityList2);

        // Confirm HsaCardOpenRuleMapper.updateByGuid(...).
        final HsaCardOpenRule t2 = new HsaCardOpenRule();
        t2.setGuid("9354a6e1-d954-4e90-9734-6b18dfe61454");
        t2.setCardGuid("cardGuid");
        t2.setSendStatus(0);
        t2.setSendOpenCountLimit(0);
        t2.setSurplusSendOpenCountLimit(0);
        verify(mockHsaCardOpenRuleMapper).updateByGuid(t2);

        // Confirm HsaCardBaseInfoMapper.batchUpdateStatus(...).
        final HsaCardBaseInfo hsaCardBaseInfo2 = new HsaCardBaseInfo();
        hsaCardBaseInfo2.setGuid("7283f9e4-076b-431b-a811-8928c88cedca");
        hsaCardBaseInfo2.setCardImage("cardImage");
        hsaCardBaseInfo2.setCardValueMoney(new BigDecimal("0.00"));
        hsaCardBaseInfo2.setCardPayPassword("cardPayPassword");
        hsaCardBaseInfo2.setIsExcess(0);
        hsaCardBaseInfo2.setExcessType(0);
        hsaCardBaseInfo2.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> activityList = Arrays.asList(hsaCardBaseInfo2);
        verify(mockHsaCardBaseInfoMapper).batchUpdateStatus(activityList);
    }

    @Test
    public void testSendOpenElectronicCard_HsaStoreCardRuleMapperReturnsNoItems() {
        // Setup
        final SendOpenElectronicCardEvent event = new SendOpenElectronicCardEvent();
        final SendOpenCardEvent sendOpenCardEvent = new SendOpenCardEvent();
        sendOpenCardEvent.setCardGuid("cardGuid");
        sendOpenCardEvent.setMemberInfoGuid(Arrays.asList("value"));
        event.setSendOpenCardEvents(Arrays.asList(sendOpenCardEvent));

        // Configure HsaCardOpenRuleMapper.selectList(...).
        final HsaCardOpenRule hsaCardOpenRule = new HsaCardOpenRule();
        hsaCardOpenRule.setGuid("9354a6e1-d954-4e90-9734-6b18dfe61454");
        hsaCardOpenRule.setCardGuid("cardGuid");
        hsaCardOpenRule.setSendStatus(0);
        hsaCardOpenRule.setSendOpenCountLimit(0);
        hsaCardOpenRule.setSurplusSendOpenCountLimit(0);
        final List<HsaCardOpenRule> hsaCardOpenRules = Arrays.asList(hsaCardOpenRule);
        when(mockHsaCardOpenRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCardOpenRules);

        // Configure HsaCardBaseInfoMapper.selectList(...).
        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setGuid("7283f9e4-076b-431b-a811-8928c88cedca");
        hsaCardBaseInfo.setCardImage("cardImage");
        hsaCardBaseInfo.setCardValueMoney(new BigDecimal("0.00"));
        hsaCardBaseInfo.setCardPayPassword("cardPayPassword");
        hsaCardBaseInfo.setIsExcess(0);
        hsaCardBaseInfo.setExcessType(0);
        hsaCardBaseInfo.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> hsaCardBaseInfos = Arrays.asList(hsaCardBaseInfo);
        when(mockHsaCardBaseInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCardBaseInfos);

        // Configure HsaOperationMemberInfoMapper.selectList(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("9f7db170-66b8-49d4-a3e8-3be76c08434c");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setMemberGradeInfoName("memberGradeInfoName");
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo);
        when(mockHsaOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperationMemberInfos);

        // Configure HsaMemberInfoCardMapper.selectList(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardImage("cardImage");
        hsaMemberInfoCard.setCardPayPassword("cardPayPassword");
        final List<HsaMemberInfoCard> hsaMemberInfoCards = Arrays.asList(hsaMemberInfoCard);
        when(mockHsaMemberInfoCardMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberInfoCards);

        when(mockHsaStoreCardRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure HsaMemberInfoCardMapper.queryByGuid(...).
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardImage("cardImage");
        hsaMemberInfoCard1.setCardPayPassword("cardPayPassword");
        when(mockHsaMemberInfoCardMapper.queryByGuid("guid")).thenReturn(hsaMemberInfoCard1);

        // Configure MemberCardAssembler.addOpenCardBalanceRecord(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setGuid("112f7e24-f134-4293-b96c-40cf4f29225f");
        hsaMemberFundingDetail.setOperSubjectGuid("operSubjectGuid");
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setCardNum("cardNum");
        hsaMemberFundingDetail.setCardName("cardName");
        final HsaMemberInfoCard memberInfoCard = new HsaMemberInfoCard();
        memberInfoCard.setGuid("guid");
        memberInfoCard.setMemberInfoGuid("memberInfoGuid");
        memberInfoCard.setElectronicCardGuid("electronicCardGuid");
        memberInfoCard.setCardGuid("cardGuid");
        memberInfoCard.setCardAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        memberInfoCard.setExcessTimes(0);
        memberInfoCard.setCardImage("cardImage");
        memberInfoCard.setCardPayPassword("cardPayPassword");
        when(mockMemberCardAssembler.addOpenCardBalanceRecord(memberInfoCard, new BigDecimal("0.00")))
                .thenReturn(hsaMemberFundingDetail);

        // Run the test
        memberInfoCardServiceImplUnderTest.sendOpenElectronicCard(event);

        // Verify the results
        // Confirm MemberCardAssembler.openCardProcessor(...).
        final HsaElectronicCard electronicCard = new HsaElectronicCard();
        electronicCard.setGuid("afce0919-879a-4438-a9c1-6eef4c917211");
        electronicCard.setOperSubjectGuid("operSubjectGuid");
        electronicCard.setEnterpriseGuid("enterpriseGuid");
        electronicCard.setMemberInfoGuid("memberInfoGuid");
        electronicCard.setCardGuid("cardGuid");
        final HsaMemberInfoCard hsaMemberInfoCard2 = new HsaMemberInfoCard();
        hsaMemberInfoCard2.setGuid("guid");
        hsaMemberInfoCard2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard2.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard2.setCardGuid("cardGuid");
        hsaMemberInfoCard2.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard2.setExcessTimes(0);
        hsaMemberInfoCard2.setCardImage("cardImage");
        hsaMemberInfoCard2.setCardPayPassword("cardPayPassword");
        final HsaCardOpenRule hsaCardOpenRule1 = new HsaCardOpenRule();
        hsaCardOpenRule1.setGuid("9354a6e1-d954-4e90-9734-6b18dfe61454");
        hsaCardOpenRule1.setCardGuid("cardGuid");
        hsaCardOpenRule1.setSendStatus(0);
        hsaCardOpenRule1.setSendOpenCountLimit(0);
        hsaCardOpenRule1.setSurplusSendOpenCountLimit(0);
        final HsaCardBaseInfo hsaCardBaseInfo1 = new HsaCardBaseInfo();
        hsaCardBaseInfo1.setGuid("7283f9e4-076b-431b-a811-8928c88cedca");
        hsaCardBaseInfo1.setCardImage("cardImage");
        hsaCardBaseInfo1.setCardValueMoney(new BigDecimal("0.00"));
        hsaCardBaseInfo1.setCardPayPassword("cardPayPassword");
        hsaCardBaseInfo1.setIsExcess(0);
        hsaCardBaseInfo1.setExcessType(0);
        hsaCardBaseInfo1.setExcessMoney(new BigDecimal("0.00"));
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("9f7db170-66b8-49d4-a3e8-3be76c08434c");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberAccount("memberAccount");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo1.setMemberGradeInfoName("memberGradeInfoName");
        final HsaMemberInfoCard hsaMemberInfoCard3 = new HsaMemberInfoCard();
        hsaMemberInfoCard3.setGuid("guid");
        hsaMemberInfoCard3.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard3.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard3.setCardGuid("cardGuid");
        hsaMemberInfoCard3.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard3.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard3.setExcessTimes(0);
        hsaMemberInfoCard3.setCardImage("cardImage");
        hsaMemberInfoCard3.setCardPayPassword("cardPayPassword");
        final List<HsaMemberInfoCard> hsaMemberInfoCardList = Arrays.asList(hsaMemberInfoCard3);
        final HsaElectronicCard hsaElectronicCard = new HsaElectronicCard();
        hsaElectronicCard.setGuid("afce0919-879a-4438-a9c1-6eef4c917211");
        hsaElectronicCard.setOperSubjectGuid("operSubjectGuid");
        hsaElectronicCard.setEnterpriseGuid("enterpriseGuid");
        hsaElectronicCard.setMemberInfoGuid("memberInfoGuid");
        hsaElectronicCard.setCardGuid("cardGuid");
        final List<HsaElectronicCard> hsaElectronicCardList = Arrays.asList(hsaElectronicCard);
        verify(mockMemberCardAssembler).openCardProcessor(electronicCard, hsaMemberInfoCard2, hsaCardOpenRule1,
                hsaCardBaseInfo1, hsaOperationMemberInfo1, hsaMemberInfoCardList, hsaElectronicCardList);

        // Confirm MemberCardAssembler.addStoreCardRuleArray(...).
        final HsaMemberInfoCard hsaMemberInfoCard4 = new HsaMemberInfoCard();
        hsaMemberInfoCard4.setGuid("guid");
        hsaMemberInfoCard4.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard4.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard4.setCardGuid("cardGuid");
        hsaMemberInfoCard4.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard4.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard4.setExcessTimes(0);
        hsaMemberInfoCard4.setCardImage("cardImage");
        hsaMemberInfoCard4.setCardPayPassword("cardPayPassword");
        final HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        hsaStoreCardRule.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule.setCardGuid("cardGuid");
        hsaStoreCardRule.setParentGuid("parentGuid");
        hsaStoreCardRule.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRuleList = Arrays.asList(hsaStoreCardRule);
        final HsaStoreCardRule hsaStoreCardRule1 = new HsaStoreCardRule();
        hsaStoreCardRule1.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule1.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule1.setCardGuid("cardGuid");
        hsaStoreCardRule1.setParentGuid("parentGuid");
        hsaStoreCardRule1.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRuleArrayList = Arrays.asList(hsaStoreCardRule1);
        verify(mockMemberCardAssembler).addStoreCardRuleArray(hsaMemberInfoCard4, hsaStoreCardRuleList, new HashMap<>(),
                hsaStoreCardRuleArrayList);

        // Confirm HsaStoreCardRuleService.saveBatch(...).
        final HsaStoreCardRule hsaStoreCardRule2 = new HsaStoreCardRule();
        hsaStoreCardRule2.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule2.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule2.setCardGuid("cardGuid");
        hsaStoreCardRule2.setParentGuid("parentGuid");
        hsaStoreCardRule2.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> entityList = Arrays.asList(hsaStoreCardRule2);
        verify(mockHsaStoreCardRuleService).saveBatch(entityList);

        // Confirm HsaMemberInfoCardMapper.updateByGuid(...).
        final HsaMemberInfoCard t = new HsaMemberInfoCard();
        t.setGuid("guid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setElectronicCardGuid("electronicCardGuid");
        t.setCardGuid("cardGuid");
        t.setCardAmount(new BigDecimal("0.00"));
        t.setExcessAmount(new BigDecimal("0.00"));
        t.setExcessTimes(0);
        t.setCardImage("cardImage");
        t.setCardPayPassword("cardPayPassword");
        verify(mockHsaMemberInfoCardMapper).updateByGuid(t);

        // Confirm HsaMemberInfoCardMapper.insert(...).
        final HsaMemberInfoCard t1 = new HsaMemberInfoCard();
        t1.setGuid("guid");
        t1.setMemberInfoGuid("memberInfoGuid");
        t1.setElectronicCardGuid("electronicCardGuid");
        t1.setCardGuid("cardGuid");
        t1.setCardAmount(new BigDecimal("0.00"));
        t1.setExcessAmount(new BigDecimal("0.00"));
        t1.setExcessTimes(0);
        t1.setCardImage("cardImage");
        t1.setCardPayPassword("cardPayPassword");
        verify(mockHsaMemberInfoCardMapper).insert(t1);

        // Confirm MemberBaseFeign.grantFeignCardRights(...).
        final CardEquitiesQO cardGrantEquitiesDTO = new CardEquitiesQO();
        cardGrantEquitiesDTO.setCardGuid(Arrays.asList("value"));
        final SendMemberEquityCard sendMemberEquityCard = new SendMemberEquityCard();
        sendMemberEquityCard.setOperSubjectGuid("operSubjectGuid");
        sendMemberEquityCard.setEnterpriseGuid("enterpriseGuid");
        sendMemberEquityCard.setMemberInfoGuid("memberInfoGuid");
        cardGrantEquitiesDTO.setSendMemberEquityCard(Arrays.asList(sendMemberEquityCard));
        verify(mockMemberBaseFeign).grantFeignCardRights(cardGrantEquitiesDTO);

        // Confirm HsaMemberFundingDetailServiceImpl.saveBatch(...).
        final HsaMemberFundingDetail hsaMemberFundingDetail1 = new HsaMemberFundingDetail();
        hsaMemberFundingDetail1.setGuid("112f7e24-f134-4293-b96c-40cf4f29225f");
        hsaMemberFundingDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberFundingDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail1.setCardNum("cardNum");
        hsaMemberFundingDetail1.setCardName("cardName");
        final List<HsaMemberFundingDetail> entityList1 = Arrays.asList(hsaMemberFundingDetail1);
        verify(mockHsaMemberFundingDetailService).saveBatch(entityList1);

        // Confirm HsaElectronicCardService.saveBatch(...).
        final HsaElectronicCard hsaElectronicCard1 = new HsaElectronicCard();
        hsaElectronicCard1.setGuid("afce0919-879a-4438-a9c1-6eef4c917211");
        hsaElectronicCard1.setOperSubjectGuid("operSubjectGuid");
        hsaElectronicCard1.setEnterpriseGuid("enterpriseGuid");
        hsaElectronicCard1.setMemberInfoGuid("memberInfoGuid");
        hsaElectronicCard1.setCardGuid("cardGuid");
        final List<HsaElectronicCard> entityList2 = Arrays.asList(hsaElectronicCard1);
        verify(mockHsaElectronicCardService).saveBatch(entityList2);

        // Confirm HsaCardOpenRuleMapper.updateByGuid(...).
        final HsaCardOpenRule t2 = new HsaCardOpenRule();
        t2.setGuid("9354a6e1-d954-4e90-9734-6b18dfe61454");
        t2.setCardGuid("cardGuid");
        t2.setSendStatus(0);
        t2.setSendOpenCountLimit(0);
        t2.setSurplusSendOpenCountLimit(0);
        verify(mockHsaCardOpenRuleMapper).updateByGuid(t2);

        // Confirm HsaCardBaseInfoMapper.batchUpdateStatus(...).
        final HsaCardBaseInfo hsaCardBaseInfo2 = new HsaCardBaseInfo();
        hsaCardBaseInfo2.setGuid("7283f9e4-076b-431b-a811-8928c88cedca");
        hsaCardBaseInfo2.setCardImage("cardImage");
        hsaCardBaseInfo2.setCardValueMoney(new BigDecimal("0.00"));
        hsaCardBaseInfo2.setCardPayPassword("cardPayPassword");
        hsaCardBaseInfo2.setIsExcess(0);
        hsaCardBaseInfo2.setExcessType(0);
        hsaCardBaseInfo2.setExcessMoney(new BigDecimal("0.00"));
        final List<HsaCardBaseInfo> activityList = Arrays.asList(hsaCardBaseInfo2);
        verify(mockHsaCardBaseInfoMapper).batchUpdateStatus(activityList);
    }

    @Test
    public void testSaveStoreCardRule() {
        // Setup
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardImage("cardImage");
        hsaMemberInfoCard.setCardPayPassword("cardPayPassword");

        // Configure HsaStoreCardRuleMapper.selectList(...).
        final HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        hsaStoreCardRule.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule.setCardGuid("cardGuid");
        hsaStoreCardRule.setParentGuid("parentGuid");
        hsaStoreCardRule.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRules = Arrays.asList(hsaStoreCardRule);
        when(mockHsaStoreCardRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaStoreCardRules);

        // Run the test
        memberInfoCardServiceImplUnderTest.saveStoreCardRule(hsaMemberInfoCard);

        // Verify the results
        // Confirm MemberCardAssembler.addStoreCardRuleArray(...).
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardImage("cardImage");
        hsaMemberInfoCard1.setCardPayPassword("cardPayPassword");
        final HsaStoreCardRule hsaStoreCardRule1 = new HsaStoreCardRule();
        hsaStoreCardRule1.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule1.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule1.setCardGuid("cardGuid");
        hsaStoreCardRule1.setParentGuid("parentGuid");
        hsaStoreCardRule1.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRuleList = Arrays.asList(hsaStoreCardRule1);
        final HsaStoreCardRule hsaStoreCardRule2 = new HsaStoreCardRule();
        hsaStoreCardRule2.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule2.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule2.setCardGuid("cardGuid");
        hsaStoreCardRule2.setParentGuid("parentGuid");
        hsaStoreCardRule2.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRuleArrayList = Arrays.asList(hsaStoreCardRule2);
        verify(mockMemberCardAssembler).addStoreCardRuleArray(hsaMemberInfoCard1, hsaStoreCardRuleList, new HashMap<>(),
                hsaStoreCardRuleArrayList);

        // Confirm HsaStoreCardRuleService.saveBatch(...).
        final HsaStoreCardRule hsaStoreCardRule3 = new HsaStoreCardRule();
        hsaStoreCardRule3.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule3.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule3.setCardGuid("cardGuid");
        hsaStoreCardRule3.setParentGuid("parentGuid");
        hsaStoreCardRule3.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> entityList = Arrays.asList(hsaStoreCardRule3);
        verify(mockHsaStoreCardRuleService).saveBatch(entityList);
    }

    @Test
    public void testSaveStoreCardRule_HsaStoreCardRuleMapperReturnsNoItems() {
        // Setup
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("guid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setExcessTimes(0);
        hsaMemberInfoCard.setCardImage("cardImage");
        hsaMemberInfoCard.setCardPayPassword("cardPayPassword");

        when(mockHsaStoreCardRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        memberInfoCardServiceImplUnderTest.saveStoreCardRule(hsaMemberInfoCard);

        // Verify the results
        // Confirm MemberCardAssembler.addStoreCardRuleArray(...).
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("guid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard1.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setExcessTimes(0);
        hsaMemberInfoCard1.setCardImage("cardImage");
        hsaMemberInfoCard1.setCardPayPassword("cardPayPassword");
        final HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        hsaStoreCardRule.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule.setCardGuid("cardGuid");
        hsaStoreCardRule.setParentGuid("parentGuid");
        hsaStoreCardRule.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRuleList = Arrays.asList(hsaStoreCardRule);
        final HsaStoreCardRule hsaStoreCardRule1 = new HsaStoreCardRule();
        hsaStoreCardRule1.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule1.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule1.setCardGuid("cardGuid");
        hsaStoreCardRule1.setParentGuid("parentGuid");
        hsaStoreCardRule1.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRuleArrayList = Arrays.asList(hsaStoreCardRule1);
        verify(mockMemberCardAssembler).addStoreCardRuleArray(hsaMemberInfoCard1, hsaStoreCardRuleList, new HashMap<>(),
                hsaStoreCardRuleArrayList);

        // Confirm HsaStoreCardRuleService.saveBatch(...).
        final HsaStoreCardRule hsaStoreCardRule2 = new HsaStoreCardRule();
        hsaStoreCardRule2.setGuid("9a04466f-24c5-48ec-a7e4-ecdc47447bad");
        hsaStoreCardRule2.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule2.setCardGuid("cardGuid");
        hsaStoreCardRule2.setParentGuid("parentGuid");
        hsaStoreCardRule2.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> entityList = Arrays.asList(hsaStoreCardRule2);
        verify(mockHsaStoreCardRuleService).saveBatch(entityList);
    }
}
