package com.holderzone.member.queue.service.grade.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.dto.grade.MemberGradeRelationDTO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.queue.entity.HsaMemberGradeInfo;
import com.holderzone.member.queue.entity.HsaMemberGradeRelation;
import com.holderzone.member.queue.mapper.HsaMemberGradeInfoMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaMemberGradeRelationServiceImplTest {

    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private HsaMemberGradeInfoMapper mockMemberGradeInfoMapper;

    private HsaMemberGradeRelationServiceImpl hsaMemberGradeRelationServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        hsaMemberGradeRelationServiceImplUnderTest = new HsaMemberGradeRelationServiceImpl(mockGuidGeneratorUtil,
                mockMemberGradeInfoMapper);
    }

    @Test
    public void testListByMemberGuidList() {
        // Setup
        final HsaMemberGradeRelation hsaMemberGradeRelation = new HsaMemberGradeRelation();
        hsaMemberGradeRelation.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRelation.setGuid("f3d4bc8f-ea0d-436b-9023-3a76c71f308b");
        hsaMemberGradeRelation.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRelation.setIsDelete(false);
        hsaMemberGradeRelation.setMemberInfoGradeGuid("memberInfoGradeGuid");
        hsaMemberGradeRelation.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeRelation.setPayTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeRelation.setRoleType("roleType");
        hsaMemberGradeRelation.setUnit(0);
        hsaMemberGradeRelation.setNum(0);
        hsaMemberGradeRelation.setIsEnable(false);
        hsaMemberGradeRelation.setTemporaryVipGrade(0);
        hsaMemberGradeRelation.setSourceType(0);
        final List<HsaMemberGradeRelation> expectedResult = Arrays.asList(hsaMemberGradeRelation);

        // Run the test
        final List<HsaMemberGradeRelation> result = hsaMemberGradeRelationServiceImplUnderTest.listByMemberGuidList(
                Arrays.asList("value"), "roleType");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testBatchSaveAndUpdateRelation() {
        // Setup
        final MemberGradeRelationDTO relationDTO = new MemberGradeRelationDTO();
        relationDTO.setMemberGuidList(Arrays.asList("value"));
        relationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        relationDTO.setRoleType("roleType");
        relationDTO.setNum(0);
        relationDTO.setUnit(0);
        relationDTO.setSourceType(0);
        relationDTO.setOperSubjectGuid("operSubjectGuid");

        // Configure HsaMemberGradeInfoMapper.selectOne(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setId(0L);
        hsaMemberGradeInfo.setGuid("6f96ca59-3361-4dd5-861f-c3283add6a1f");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setIsDelete(0);
        hsaMemberGradeInfo.setTemporaryVipGrade(0);
        when(mockMemberGradeInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfo);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("f3d4bc8f-ea0d-436b-9023-3a76c71f308b");

        // Run the test
        hsaMemberGradeRelationServiceImplUnderTest.batchSaveAndUpdateRelation(relationDTO);

        // Verify the results
    }

    @Test
    public void testBatchDeleteMemberGrade() {
        // Setup
        // Run the test
        hsaMemberGradeRelationServiceImplUnderTest.batchDeleteMemberGrade(Arrays.asList("value"), "roleType");

        // Verify the results
    }

    @Test
    public void testQueryMemberCurrentGrade() {
        // Setup
        final MemberGradeRelationDTO relationDTO = new MemberGradeRelationDTO();
        relationDTO.setMemberGuidList(Arrays.asList("value"));
        relationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        relationDTO.setRoleType("roleType");
        relationDTO.setNum(0);
        relationDTO.setUnit(0);
        relationDTO.setSourceType(0);
        relationDTO.setOperSubjectGuid("operSubjectGuid");

        final HsaMemberGradeInfo expectedResult = new HsaMemberGradeInfo();
        expectedResult.setId(0L);
        expectedResult.setGuid("6f96ca59-3361-4dd5-861f-c3283add6a1f");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setIsDelete(0);
        expectedResult.setTemporaryVipGrade(0);

        // Configure HsaMemberGradeInfoMapper.selectOne(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setId(0L);
        hsaMemberGradeInfo.setGuid("6f96ca59-3361-4dd5-861f-c3283add6a1f");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setIsDelete(0);
        hsaMemberGradeInfo.setTemporaryVipGrade(0);
        when(mockMemberGradeInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfo);

        // Run the test
        final HsaMemberGradeInfo result = hsaMemberGradeRelationServiceImplUnderTest.queryMemberCurrentGrade(
                relationDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testBatchUpdateExpireTime() {
        // Setup
        final Map<String, LocalDateTime> expireTimeMap = new HashMap<>();

        // Run the test
        hsaMemberGradeRelationServiceImplUnderTest.batchUpdateExpireTime(expireTimeMap);

        // Verify the results
        verify(mockMemberGradeInfoMapper).batchUpdateExpireTime(new HashMap<>());
    }
}
