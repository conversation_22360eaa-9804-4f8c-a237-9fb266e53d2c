package com.holderzone.member.queue.service.grade.impl;

import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.member.RoleTypeEnum;
import com.holderzone.member.queue.dto.grade.MemberGradeChangeDTO;
import com.holderzone.member.queue.entity.HsaOperationMemberInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 性能对比测试
 * 
 * 对比优化前后的性能差异，特别是循环查询优化的效果
 * 
 * <AUTHOR>
 * @since 2025-01-18
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class PerformanceComparisonTest {

    @Resource(name = "hsaMemberGradeChangeDetailServiceOptimized")
    private HsaMemberGradeChangeDetailServiceOptimizedImpl optimizedService;

    @Resource
    private HsaMemberGradeChangeDetailServiceImpl originalService;

    /**
     * 小批量数据性能对比测试（1000个会员）
     */
    @Test
    public void testSmallBatchPerformance() {
        int memberCount = 1000;
        log.info("开始小批量数据性能对比测试，会员数量：{}", memberCount);

        // 创建测试数据
        MemberGradeChangeDTO gradeChangeDTO = createTestData(memberCount);

        // 测试优化版本
        long optimizedStartTime = System.currentTimeMillis();
        try {
            optimizedService.memberGradeChange(gradeChangeDTO);
        } catch (Exception e) {
            log.warn("优化版本执行异常（测试环境正常）：{}", e.getMessage());
        }
        long optimizedEndTime = System.currentTimeMillis();
        long optimizedDuration = optimizedEndTime - optimizedStartTime;

        // 测试原版本
        long originalStartTime = System.currentTimeMillis();
        try {
            originalService.memberGradeChange(gradeChangeDTO);
        } catch (Exception e) {
            log.warn("原版本执行异常（测试环境正常）：{}", e.getMessage());
        }
        long originalEndTime = System.currentTimeMillis();
        long originalDuration = originalEndTime - originalStartTime;

        // 输出对比结果
        log.info("小批量数据性能对比结果：");
        log.info("会员数量：{}", memberCount);
        log.info("优化版本耗时：{}ms", optimizedDuration);
        log.info("原版本耗时：{}ms", originalDuration);
        
        if (originalDuration > 0) {
            double improvement = ((double) (originalDuration - optimizedDuration) / originalDuration) * 100;
            log.info("性能提升：{:.2f}%", improvement);
        }
    }

    /**
     * 中等批量数据性能对比测试（5000个会员）
     */
    @Test
    public void testMediumBatchPerformance() {
        int memberCount = 5000;
        log.info("开始中等批量数据性能对比测试，会员数量：{}", memberCount);

        // 创建测试数据
        MemberGradeChangeDTO gradeChangeDTO = createTestData(memberCount);

        // 测试优化版本
        long optimizedStartTime = System.currentTimeMillis();
        try {
            optimizedService.memberGradeChange(gradeChangeDTO);
        } catch (Exception e) {
            log.warn("优化版本执行异常（测试环境正常）：{}", e.getMessage());
        }
        long optimizedEndTime = System.currentTimeMillis();
        long optimizedDuration = optimizedEndTime - optimizedStartTime;

        // 测试原版本
        long originalStartTime = System.currentTimeMillis();
        try {
            originalService.memberGradeChange(gradeChangeDTO);
        } catch (Exception e) {
            log.warn("原版本执行异常（测试环境正常）：{}", e.getMessage());
        }
        long originalEndTime = System.currentTimeMillis();
        long originalDuration = originalEndTime - originalStartTime;

        // 输出对比结果
        log.info("中等批量数据性能对比结果：");
        log.info("会员数量：{}", memberCount);
        log.info("优化版本耗时：{}ms", optimizedDuration);
        log.info("原版本耗时：{}ms", originalDuration);
        
        if (originalDuration > 0) {
            double improvement = ((double) (originalDuration - optimizedDuration) / originalDuration) * 100;
            log.info("性能提升：{:.2f}%", improvement);
        }
    }

    /**
     * 大批量数据性能对比测试（10000个会员）
     */
    @Test
    public void testLargeBatchPerformance() {
        int memberCount = 10000;
        log.info("开始大批量数据性能对比测试，会员数量：{}", memberCount);

        // 创建测试数据
        MemberGradeChangeDTO gradeChangeDTO = createTestData(memberCount);

        // 测试优化版本
        long optimizedStartTime = System.currentTimeMillis();
        try {
            optimizedService.memberGradeChange(gradeChangeDTO);
        } catch (Exception e) {
            log.warn("优化版本执行异常（测试环境正常）：{}", e.getMessage());
        }
        long optimizedEndTime = System.currentTimeMillis();
        long optimizedDuration = optimizedEndTime - optimizedStartTime;

        // 测试原版本
        long originalStartTime = System.currentTimeMillis();
        try {
            originalService.memberGradeChange(gradeChangeDTO);
        } catch (Exception e) {
            log.warn("原版本执行异常（测试环境正常）：{}", e.getMessage());
        }
        long originalEndTime = System.currentTimeMillis();
        long originalDuration = originalEndTime - originalStartTime;

        // 输出对比结果
        log.info("大批量数据性能对比结果：");
        log.info("会员数量：{}", memberCount);
        log.info("优化版本耗时：{}ms", optimizedDuration);
        log.info("原版本耗时：{}ms", originalDuration);
        
        if (originalDuration > 0) {
            double improvement = ((double) (originalDuration - optimizedDuration) / originalDuration) * 100;
            log.info("性能提升：{:.2f}%", improvement);
        }
    }

    /**
     * 数据库查询次数对比测试
     */
    @Test
    public void testDatabaseQueryCountComparison() {
        log.info("开始数据库查询次数对比测试");

        int memberCount = 2000; // 会分成4个批次处理
        MemberGradeChangeDTO gradeChangeDTO = createTestData(memberCount);

        log.info("测试场景：{}个会员，预期分成{}个批次处理", memberCount, (memberCount + 499) / 500);

        // 在实际环境中，可以通过数据库监控或AOP来统计查询次数
        // 这里主要是展示测试思路
        
        log.info("优化前：每个批次都会重复查询基础数据");
        log.info("优化后：所有基础数据只在开始时查询一次");
        
        // 模拟查询次数统计
        int batchCount = (memberCount + 499) / 500;
        int originalQueryCount = batchCount * 5; // 假设每个批次需要5次基础查询
        int optimizedQueryCount = 5; // 优化后只需要5次基础查询
        
        log.info("预期查询次数对比：");
        log.info("优化前查询次数：{}", originalQueryCount);
        log.info("优化后查询次数：{}", optimizedQueryCount);
        log.info("查询次数减少：{:.2f}%", ((double)(originalQueryCount - optimizedQueryCount) / originalQueryCount) * 100);
    }

    /**
     * 内存使用对比测试
     */
    @Test
    public void testMemoryUsageComparison() {
        log.info("开始内存使用对比测试");

        // 获取初始内存使用情况
        Runtime runtime = Runtime.getRuntime();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        int memberCount = 5000;
        MemberGradeChangeDTO gradeChangeDTO = createTestData(memberCount);

        // 测试优化版本内存使用
        long beforeOptimized = runtime.totalMemory() - runtime.freeMemory();
        try {
            optimizedService.memberGradeChange(gradeChangeDTO);
        } catch (Exception e) {
            log.warn("优化版本执行异常（测试环境正常）：{}", e.getMessage());
        }
        System.gc(); // 建议垃圾回收
        Thread.sleep(100); // 等待GC完成
        long afterOptimized = runtime.totalMemory() - runtime.freeMemory();

        // 测试原版本内存使用
        long beforeOriginal = runtime.totalMemory() - runtime.freeMemory();
        try {
            originalService.memberGradeChange(gradeChangeDTO);
        } catch (Exception e) {
            log.warn("原版本执行异常（测试环境正常）：{}", e.getMessage());
        }
        System.gc(); // 建议垃圾回收
        Thread.sleep(100); // 等待GC完成
        long afterOriginal = runtime.totalMemory() - runtime.freeMemory();

        // 输出内存使用对比
        long optimizedMemoryUsage = afterOptimized - beforeOptimized;
        long originalMemoryUsage = afterOriginal - beforeOriginal;

        log.info("内存使用对比结果：");
        log.info("优化版本内存使用：{}KB", optimizedMemoryUsage / 1024);
        log.info("原版本内存使用：{}KB", originalMemoryUsage / 1024);
        
        if (originalMemoryUsage > 0) {
            double memoryImprovement = ((double)(originalMemoryUsage - optimizedMemoryUsage) / originalMemoryUsage) * 100;
            log.info("内存使用优化：{:.2f}%", memoryImprovement);
        }
    }

    /**
     * 创建测试数据
     */
    private MemberGradeChangeDTO createTestData(int memberCount) {
        MemberGradeChangeDTO gradeChangeDTO = new MemberGradeChangeDTO();
        gradeChangeDTO.setOperSubjectGuid("test-subject-performance");
        gradeChangeDTO.setRoleType(RoleTypeEnum.MEMBER.name());
        gradeChangeDTO.setSourceType(1);
        gradeChangeDTO.setIsRefresh(BooleanEnum.FALSE.getCode());
        gradeChangeDTO.setRemark("性能测试数据");

        List<HsaOperationMemberInfo> memberInfos = new ArrayList<>();
        for (int i = 0; i < memberCount; i++) {
            HsaOperationMemberInfo memberInfo = new HsaOperationMemberInfo();
            memberInfo.setGuid("perf-test-member-" + i);
            memberInfo.setOperSubjectGuid("test-subject-performance");
            memberInfo.setMemberGradeInfoGuid("old-grade-" + (i % 3)); // 模拟不同的旧等级
            memberInfo.setMemberGradeInfoName("Old Grade " + (i % 3));
            memberInfo.setMemberGrowthValue(100 + (i % 500)); // 模拟不同的成长值
            memberInfo.setMemberIntegral(50 + (i % 200)); // 模拟不同的积分
            memberInfo.setUpgradeTime(LocalDateTime.now());
            memberInfos.add(memberInfo);
        }

        gradeChangeDTO.setHsaOperationMemberInfos(memberInfos);
        return gradeChangeDTO;
    }
}
