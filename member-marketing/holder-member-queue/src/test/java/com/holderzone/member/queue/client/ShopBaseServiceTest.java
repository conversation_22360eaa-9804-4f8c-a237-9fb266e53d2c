package com.holderzone.member.queue.client;

import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.queue.entity.HsaGrowthValueCommodityRule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ShopBaseServiceTest {

    @Mock
    private ExternalSupport mockExternalSupport;

    @InjectMocks
    private ShopBaseService shopBaseServiceUnderTest;

    @Test
    public void testQueryCommodityCategoryIdListNew() {
        // Setup
        final HsaGrowthValueCommodityRule hsaGrowthValueCommodityRule = new HsaGrowthValueCommodityRule();
        hsaGrowthValueCommodityRule.setStrategyId("strategyId");
        hsaGrowthValueCommodityRule.setStrategyName("strategyName");
        hsaGrowthValueCommodityRule.setCommodityCode("commodityCode");
        hsaGrowthValueCommodityRule.setComboType(0);
        hsaGrowthValueCommodityRule.setStoreState(0);
        hsaGrowthValueCommodityRule.setCommodityId("commodityId");
        hsaGrowthValueCommodityRule.setCommodityName("commodityName");
        hsaGrowthValueCommodityRule.setCommodityPrice("basePrice");
        hsaGrowthValueCommodityRule.setCategoryId("categoryId");
        hsaGrowthValueCommodityRule.setCategoryName("categoryName");
        final List<HsaGrowthValueCommodityRule> expectedResult = Arrays.asList(hsaGrowthValueCommodityRule);
        when(mockExternalSupport.itemServer(0)).thenReturn(null);

        // Run the test
        final List<HsaGrowthValueCommodityRule> result = shopBaseServiceUnderTest.queryCommodityCategoryIdListNew(0,
                Arrays.asList(0));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
