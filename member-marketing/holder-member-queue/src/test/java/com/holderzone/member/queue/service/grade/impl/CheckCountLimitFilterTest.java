package com.holderzone.member.queue.service.grade.impl;

import com.holderzone.member.queue.dto.grade.DoubleValueRequest;
import com.holderzone.member.queue.entity.HsaBusinessEquities;
import com.holderzone.member.queue.entity.HsaMemberEquitiesReceiveRecord;
import org.junit.Before;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class CheckCountLimitFilterTest {

    private CheckCountLimitFilter checkCountLimitFilterUnderTest;

    @Before
    public void setUp() {
        checkCountLimitFilterUnderTest = new CheckCountLimitFilter();
    }

    @Test
    public void testDoFilter() {
        // Setup
        final DoubleValueRequest request = DoubleValueRequest.builder()
                .doubleValue(0L)
                .build();
        final HsaBusinessEquities hsaBusinessEquities = HsaBusinessEquities.builder()
                .doubleCountLimited(0)
                .doubleCountUpperLimit(0)
                .build();
        final HsaMemberEquitiesReceiveRecord equitiesReceiveRecord = new HsaMemberEquitiesReceiveRecord();
        equitiesReceiveRecord.setGuid("d835a3ed-273e-4e78-807a-6132bfc2e067");
        equitiesReceiveRecord.setOperSubjectGuid("operSubjectGuid");
        equitiesReceiveRecord.setMemberGradeGuid("memberGradeGuid");
        equitiesReceiveRecord.setMemberInfoGuid("memberInfoGuid");
        equitiesReceiveRecord.setDoubleValueCount(0);

        // Run the test
        final boolean result = checkCountLimitFilterUnderTest.doFilter(request, hsaBusinessEquities,
                equitiesReceiveRecord);

        // Verify the results
        assertThat(result).isFalse();
    }
}
