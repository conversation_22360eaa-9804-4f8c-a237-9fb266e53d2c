package com.holderzone.member.queue.service.member.impl;

import com.holderzone.member.queue.entity.HsaGrowthCommodityTouchDetail;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class HsaGrowthCommodityTouchDetailServiceImplTest {

    private HsaGrowthCommodityTouchDetailServiceImpl hsaGrowthCommodityTouchDetailServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        hsaGrowthCommodityTouchDetailServiceImplUnderTest = new HsaGrowthCommodityTouchDetailServiceImpl();
    }

    @Test
    public void testQueryByGuid() {
        // Setup
        final HsaGrowthCommodityTouchDetail expectedResult = new HsaGrowthCommodityTouchDetail();
        expectedResult.setGuid("b3298e20-52d5-435a-8cc4-36587290e835");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setGrowthValueTaskGuid("growthValueTaskGuid");
        expectedResult.setMemberInfoGuid("memberInfoGuid");
        expectedResult.setBuyType(0);

        // Run the test
        final HsaGrowthCommodityTouchDetail result = hsaGrowthCommodityTouchDetailServiceImplUnderTest.queryByGuid(
                "417b6421-ae52-4713-8afd-76a960169fd0");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryByGuids() {
        // Setup
        final HsaGrowthCommodityTouchDetail hsaGrowthCommodityTouchDetail = new HsaGrowthCommodityTouchDetail();
        hsaGrowthCommodityTouchDetail.setGuid("b3298e20-52d5-435a-8cc4-36587290e835");
        hsaGrowthCommodityTouchDetail.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthCommodityTouchDetail.setGrowthValueTaskGuid("growthValueTaskGuid");
        hsaGrowthCommodityTouchDetail.setMemberInfoGuid("memberInfoGuid");
        hsaGrowthCommodityTouchDetail.setBuyType(0);
        final List<HsaGrowthCommodityTouchDetail> expectedResult = Arrays.asList(hsaGrowthCommodityTouchDetail);

        // Run the test
        final List<HsaGrowthCommodityTouchDetail> result = hsaGrowthCommodityTouchDetailServiceImplUnderTest.queryByGuids(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateByGuid() {
        // Setup
        final HsaGrowthCommodityTouchDetail t = new HsaGrowthCommodityTouchDetail();
        t.setGuid("b3298e20-52d5-435a-8cc4-36587290e835");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setGrowthValueTaskGuid("growthValueTaskGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setBuyType(0);

        // Run the test
        final boolean result = hsaGrowthCommodityTouchDetailServiceImplUnderTest.updateByGuid(t);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemoveByGuid() {
        // Setup
        // Run the test
        final boolean result = hsaGrowthCommodityTouchDetailServiceImplUnderTest.removeByGuid(
                "9b45f014-abfe-4df0-ba2d-738f8bdabb1b");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemoveByGuids() {
        // Setup
        // Run the test
        final boolean result = hsaGrowthCommodityTouchDetailServiceImplUnderTest.removeByGuids(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isFalse();
    }
}
