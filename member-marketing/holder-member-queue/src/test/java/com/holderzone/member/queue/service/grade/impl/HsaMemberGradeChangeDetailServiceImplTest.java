package com.holderzone.member.queue.service.grade.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.util.concurrent.MoreExecutors;
import com.holderzone.member.common.dto.grade.MemberGradeRelationDTO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.grade.MemberGradePayRecordReqVO;
import com.holderzone.member.common.vo.growth.MemberGrowthValueRelationVO;
import com.holderzone.member.queue.dto.grade.GradeRightsProcessorDTO;
import com.holderzone.member.queue.dto.grade.MemberGradeChangeDTO;
import com.holderzone.member.queue.dto.member.UpdateIntegral;
import com.holderzone.member.queue.entity.*;
import com.holderzone.member.queue.mapper.*;
import com.holderzone.member.queue.service.assembler.GradeChangeAssembler;
import com.holderzone.member.queue.service.cache.CacheService;
import com.holderzone.member.queue.service.grade.*;
import com.holderzone.member.queue.service.member.HsaGrowthValueDetailService;
import com.holderzone.member.queue.service.member.HsaIntegralDetailService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaMemberGradeChangeDetailServiceImplTest {

    @Mock
    private HsaGradeGiftBagMapper mockHsaGradeGiftBagMapper;
    @Mock
    private HsaMemberGradeChangeDetailMapper mockHsaMemberGradeChangeDetailMapper;
    @Mock
    private HsaMemberGradeInfoMapper mockHsaMemberGradeInfoMapper;
    @Mock
    private HsaOperationMemberInfoMapper mockHsaOperationMemberInfoMapper;
    @Mock
    private HsaMemberGradeRightsRecordMapper mockHsaMemberGradeRightsRecordMapper;
    @Mock
    private HsaGiftBagBaseInfoMapper mockHsaGiftBagBaseInfoMapper;
    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private HsaMemberGradeChangeDetailService mockHsaMemberGradeChangeDetailService;
    @Mock
    private HsaMemberGradeRightsRecordService mockHsaMemberGradeRightsRecordService;
    @Mock
    private HsaGrowthValueDetailService mockHsaGrowthValueDetailService;
    @Mock
    private HsaIntegralDetailService mockHsaIntegralDetailService;
    @Mock
    private HsaControlledGradeStateMapper mockHsaControlledGradeStateMapper;
    @Mock
    private CacheService mockCacheService;
    @Mock
    private HsaGradeEquitiesService mockHsaGradeEquitiesService;
    @Mock
    private HsaGradeEquitiesMapper mockHsaGradeEquitiesMapper;
    @Mock
    private HsaMemberEquitiesReceiveRecordMapper mockHsaMemberEquitiesReceiveRecordMapper;
    @Mock
    private HsaMemberEquitiesReceiveRecordServiceImpl mockHsaMemberEquitiesReceiveRecordService;
    @Mock
    private IHsaMemberGradeRelationService mockMemberGradeRelationService;
    @Mock
    private IHsaMemberGradePayRecordService mockMemberGradePayRecordService;
    @Mock
    private GradeChangeAssembler mockGradeChangeAssembler;

    @InjectMocks
    private HsaMemberGradeChangeDetailServiceImpl hsaMemberGradeChangeDetailServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(hsaMemberGradeChangeDetailServiceImplUnderTest, "memberQueueThreadExecutor",
                MoreExecutors.directExecutor());
    }

    @Test
    public void testMemberGradeChange() {
        // Setup
        final MemberGradeChangeDTO gradeChangeDTO = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("guid");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo.setMemberGradeInfoName("");
        hsaOperationMemberInfo.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo.setMemberIntegral(0);
        hsaOperationMemberInfo.setMemberGrowthValue(0);
        hsaOperationMemberInfo.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo));
        gradeChangeDTO.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO.setSourceType(0);
        gradeChangeDTO.setIsRefresh(0);
        gradeChangeDTO.setRoleType("roleType");
        gradeChangeDTO.setSystem(0);
        gradeChangeDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");

        // Configure HsaControlledGradeStateMapper.selectOne(...).
        final HsaControlledGradeState hsaControlledGradeState = new HsaControlledGradeState();
        hsaControlledGradeState.setId(0L);
        hsaControlledGradeState.setOperSubjectGuid("operSubjectGuid");
        hsaControlledGradeState.setState(0);
        hsaControlledGradeState.setIsDelete(false);
        hsaControlledGradeState.setRoleType("roleType");
        when(mockHsaControlledGradeStateMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaControlledGradeState);

        // Configure HsaMemberGradeInfoMapper.selectList(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("");
        hsaMemberGradeInfo.setGrowthValue(0);
        hsaMemberGradeInfo.setIsDelete(0);
        hsaMemberGradeInfo.setEffective(0);
        hsaMemberGradeInfo.setRoleType("roleType");
        hsaMemberGradeInfo.setUpgradeType(0);
        final List<HsaMemberGradeInfo> hsaMemberGradeInfos = Arrays.asList(hsaMemberGradeInfo);
        when(mockHsaMemberGradeInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfos);

        // Configure IHsaMemberGradeRelationService.listByMemberGuidList(...).
        final HsaMemberGradeRelation hsaMemberGradeRelation = new HsaMemberGradeRelation();
        hsaMemberGradeRelation.setId(0);
        hsaMemberGradeRelation.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRelation.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRelation.setMemberInfoGradeGuid("memberInfoGradeGuid");
        hsaMemberGradeRelation.setIsEnable(false);
        final List<HsaMemberGradeRelation> hsaMemberGradeRelations = Arrays.asList(hsaMemberGradeRelation);
        when(mockMemberGradeRelationService.listByMemberGuidList(Arrays.asList("value"), "roleType"))
                .thenReturn(hsaMemberGradeRelations);

        // Configure HsaGradeEquitiesService.list(...).
        final List<HsaBusinessEquities> hsaBusinessEquities = Arrays.asList(HsaBusinessEquities.builder()
                .guid("guid")
                .memberGradeInfoGuid("memberGradeInfoGuid")
                .equitiesRuleType(0)
                .totalGiveNumber(0)
                .setPeriod(0)
                .isDelete(0)
                .effective(0)
                .operSubjectGuid("operSubjectGuid")
                .build());
        when(mockHsaGradeEquitiesService.list(any(LambdaQueryWrapper.class))).thenReturn(hsaBusinessEquities);

        // Configure HsaMemberGradeRightsRecordMapper.selectList(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord.setRightsType(0);
        final List<HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecords = Arrays.asList(hsaMemberGradeRightsRecord);
        when(mockHsaMemberGradeRightsRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberGradeRightsRecords);

        // Configure HsaGradeEquitiesMapper.selectList(...).
        final List<HsaBusinessEquities> hsaBusinessEquities1 = Arrays.asList(HsaBusinessEquities.builder()
                .guid("guid")
                .memberGradeInfoGuid("memberGradeInfoGuid")
                .equitiesRuleType(0)
                .totalGiveNumber(0)
                .setPeriod(0)
                .isDelete(0)
                .effective(0)
                .operSubjectGuid("operSubjectGuid")
                .build());
        when(mockHsaGradeEquitiesMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaBusinessEquities1);

        // Configure HsaGiftBagBaseInfoMapper.selectOne(...).
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo.setId(0L);
        hsaGiftBagBaseInfo.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo.setEquitiesType(0);
        hsaGiftBagBaseInfo.setEquitiesRule(0);
        when(mockHsaGiftBagBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaGiftBagBaseInfo);

        // Configure HsaMemberGradeInfoMapper.selectOne(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo1 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo1.setGuid("");
        hsaMemberGradeInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo1.setVipGrade(0);
        hsaMemberGradeInfo1.setName("");
        hsaMemberGradeInfo1.setGrowthValue(0);
        hsaMemberGradeInfo1.setIsDelete(0);
        hsaMemberGradeInfo1.setEffective(0);
        hsaMemberGradeInfo1.setRoleType("roleType");
        hsaMemberGradeInfo1.setUpgradeType(0);
        when(mockHsaMemberGradeInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfo1);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");

        // Configure HsaGradeGiftBagMapper.selectList(...).
        final HsaGradeGiftBag hsaGradeGiftBag = new HsaGradeGiftBag();
        hsaGradeGiftBag.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag.setType(0);
        hsaGradeGiftBag.setValue(0);
        hsaGradeGiftBag.setIsDelete(0);
        hsaGradeGiftBag.setEffective(0);
        final List<HsaGradeGiftBag> hsaGradeGiftBags = Arrays.asList(hsaGradeGiftBag);
        when(mockHsaGradeGiftBagMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaGradeGiftBags);

        // Configure GradeChangeAssembler.getHsaMemberGradeRightsRecord(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord1 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord1.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord1.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord1.setRightsType(0);
        final GradeRightsProcessorDTO gradeRightsProcessorDTO = new GradeRightsProcessorDTO();
        gradeRightsProcessorDTO.setUpdateMemberIntegralMap(new HashMap<>());
        gradeRightsProcessorDTO.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo1 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo1.setEquitiesRule(0);
        gradeRightsProcessorDTO.setHsaGiftBagBaseInfo(hsaGiftBagBaseInfo1);
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord2 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord2.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord2.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord2.setRightsType(0);
        gradeRightsProcessorDTO.setHsaMemberGradeRightsRecords(Arrays.asList(hsaMemberGradeRightsRecord2));
        final HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        gradeRightsProcessorDTO.setHsaGrowthValueDetails(Arrays.asList(hsaGrowthValueDetail));
        final HsaIntegralDetail hsaIntegralDetail = new HsaIntegralDetail();
        gradeRightsProcessorDTO.setHsaIntegralDetails(Arrays.asList(hsaIntegralDetail));
        gradeRightsProcessorDTO.setSourceType(0);
        gradeRightsProcessorDTO.setOperSubjectGuid("operSubjectGuid");
        final HsaOperationMemberInfo operationMemberInfo = new HsaOperationMemberInfo();
        operationMemberInfo.setGuid("guid");
        operationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo.setMemberGradeInfoGuid("");
        operationMemberInfo.setMemberGradeInfoName("");
        operationMemberInfo.setOperatorGuid("operatorGuid");
        operationMemberInfo.setMemberIntegral(0);
        operationMemberInfo.setMemberGrowthValue(0);
        operationMemberInfo.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag1 = new HsaGradeGiftBag();
        hsaGradeGiftBag1.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag1.setType(0);
        hsaGradeGiftBag1.setValue(0);
        hsaGradeGiftBag1.setIsDelete(0);
        hsaGradeGiftBag1.setEffective(0);
        when(mockGradeChangeAssembler.getHsaMemberGradeRightsRecord(gradeRightsProcessorDTO, operationMemberInfo,
                hsaGradeGiftBag1)).thenReturn(hsaMemberGradeRightsRecord1);

        // Configure GradeChangeAssembler.getHsaGrowthValueDetail(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail1 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail1.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail1.setId(0L);
        hsaGrowthValueDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail1.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail1.setMemberInfoGuid("memberInfoGuid");
        final HsaOperationMemberInfo operationMemberInfo1 = new HsaOperationMemberInfo();
        operationMemberInfo1.setGuid("guid");
        operationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo1.setMemberGradeInfoGuid("");
        operationMemberInfo1.setMemberGradeInfoName("");
        operationMemberInfo1.setOperatorGuid("operatorGuid");
        operationMemberInfo1.setMemberIntegral(0);
        operationMemberInfo1.setMemberGrowthValue(0);
        operationMemberInfo1.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag2 = new HsaGradeGiftBag();
        hsaGradeGiftBag2.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag2.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag2.setType(0);
        hsaGradeGiftBag2.setValue(0);
        hsaGradeGiftBag2.setIsDelete(0);
        hsaGradeGiftBag2.setEffective(0);
        final HsaMemberGradeInfo hsaMemberGradeInfo2 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo2.setGuid("");
        hsaMemberGradeInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo2.setVipGrade(0);
        hsaMemberGradeInfo2.setName("");
        hsaMemberGradeInfo2.setGrowthValue(0);
        hsaMemberGradeInfo2.setIsDelete(0);
        hsaMemberGradeInfo2.setEffective(0);
        hsaMemberGradeInfo2.setRoleType("roleType");
        hsaMemberGradeInfo2.setUpgradeType(0);
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo2 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo2.setId(0L);
        hsaGiftBagBaseInfo2.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo2.setEquitiesType(0);
        hsaGiftBagBaseInfo2.setEquitiesRule(0);
        final HsaMemberGradeInfo afterMemberGradeInfo = new HsaMemberGradeInfo();
        afterMemberGradeInfo.setGuid("");
        afterMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo.setVipGrade(0);
        afterMemberGradeInfo.setName("");
        afterMemberGradeInfo.setGrowthValue(0);
        afterMemberGradeInfo.setIsDelete(0);
        afterMemberGradeInfo.setEffective(0);
        afterMemberGradeInfo.setRoleType("roleType");
        afterMemberGradeInfo.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaGrowthValueDetail(operationMemberInfo1, 0, "operSubjectGuid",
                hsaGradeGiftBag2, hsaMemberGradeInfo2, hsaGiftBagBaseInfo2, afterMemberGradeInfo))
                .thenReturn(hsaGrowthValueDetail1);

        // Configure GradeChangeAssembler.getHsaIntegralValueDetail(...).
        final HsaIntegralDetail hsaIntegralDetail1 = new HsaIntegralDetail();
        hsaIntegralDetail1.setId(0L);
        hsaIntegralDetail1.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail1.setIntegralTaskGuid("integralTaskGuid");
        final HsaOperationMemberInfo operationMemberInfo2 = new HsaOperationMemberInfo();
        operationMemberInfo2.setGuid("guid");
        operationMemberInfo2.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo2.setMemberGradeInfoGuid("");
        operationMemberInfo2.setMemberGradeInfoName("");
        operationMemberInfo2.setOperatorGuid("operatorGuid");
        operationMemberInfo2.setMemberIntegral(0);
        operationMemberInfo2.setMemberGrowthValue(0);
        operationMemberInfo2.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag3 = new HsaGradeGiftBag();
        hsaGradeGiftBag3.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag3.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag3.setType(0);
        hsaGradeGiftBag3.setValue(0);
        hsaGradeGiftBag3.setIsDelete(0);
        hsaGradeGiftBag3.setEffective(0);
        final HsaMemberGradeInfo hsaMemberGradeInfo3 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo3.setGuid("");
        hsaMemberGradeInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo3.setVipGrade(0);
        hsaMemberGradeInfo3.setName("");
        hsaMemberGradeInfo3.setGrowthValue(0);
        hsaMemberGradeInfo3.setIsDelete(0);
        hsaMemberGradeInfo3.setEffective(0);
        hsaMemberGradeInfo3.setRoleType("roleType");
        hsaMemberGradeInfo3.setUpgradeType(0);
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo3 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo3.setId(0L);
        hsaGiftBagBaseInfo3.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo3.setEquitiesType(0);
        hsaGiftBagBaseInfo3.setEquitiesRule(0);
        final HsaMemberGradeInfo afterMemberGradeInfo1 = new HsaMemberGradeInfo();
        afterMemberGradeInfo1.setGuid("");
        afterMemberGradeInfo1.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo1.setVipGrade(0);
        afterMemberGradeInfo1.setName("");
        afterMemberGradeInfo1.setGrowthValue(0);
        afterMemberGradeInfo1.setIsDelete(0);
        afterMemberGradeInfo1.setEffective(0);
        afterMemberGradeInfo1.setRoleType("roleType");
        afterMemberGradeInfo1.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaIntegralValueDetail(operationMemberInfo2, 0, "operSubjectGuid",
                hsaGradeGiftBag3, hsaMemberGradeInfo3, hsaGiftBagBaseInfo3, afterMemberGradeInfo1))
                .thenReturn(hsaIntegralDetail1);

        // Configure GradeChangeAssembler.getMemberGradeRightsRecord(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord3 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord3.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord3.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord3.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord3.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord3.setRightsType(0);
        final HsaOperationMemberInfo operationMemberInfo3 = new HsaOperationMemberInfo();
        operationMemberInfo3.setGuid("guid");
        operationMemberInfo3.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo3.setMemberGradeInfoGuid("");
        operationMemberInfo3.setMemberGradeInfoName("");
        operationMemberInfo3.setOperatorGuid("operatorGuid");
        operationMemberInfo3.setMemberIntegral(0);
        operationMemberInfo3.setMemberGrowthValue(0);
        operationMemberInfo3.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockGradeChangeAssembler.getMemberGradeRightsRecord(operationMemberInfo3, "operSubjectGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build())).thenReturn(hsaMemberGradeRightsRecord3);

        // Configure HsaMemberEquitiesReceiveRecordMapper.selectAllGrowthValue(...).
        final MemberGrowthValueRelationVO memberGrowthValueRelationVO = new MemberGrowthValueRelationVO();
        memberGrowthValueRelationVO.setMemberGuid("memberGuid");
        memberGrowthValueRelationVO.setGrowthValue(0);
        final List<MemberGrowthValueRelationVO> memberGrowthValueRelationVOS = Arrays.asList(
                memberGrowthValueRelationVO);
        when(mockHsaMemberEquitiesReceiveRecordMapper.selectAllGrowthValue("guid", 0))
                .thenReturn(memberGrowthValueRelationVOS);

        // Configure GradeChangeAssembler.getHsaIntegralValueDetail(...).
        final HsaIntegralDetail hsaIntegralDetail2 = new HsaIntegralDetail();
        hsaIntegralDetail2.setId(0L);
        hsaIntegralDetail2.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail2.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail2.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail2.setIntegralTaskGuid("integralTaskGuid");
        final HsaMemberGradeInfo hsaMemberGradeInfo4 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo4.setGuid("");
        hsaMemberGradeInfo4.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo4.setVipGrade(0);
        hsaMemberGradeInfo4.setName("");
        hsaMemberGradeInfo4.setGrowthValue(0);
        hsaMemberGradeInfo4.setIsDelete(0);
        hsaMemberGradeInfo4.setEffective(0);
        hsaMemberGradeInfo4.setRoleType("roleType");
        hsaMemberGradeInfo4.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaIntegralValueDetail("guid", 0, "operSubjectGuid", hsaMemberGradeInfo4,
                HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build(), 0, 0)).thenReturn(hsaIntegralDetail2);

        // Configure GradeChangeAssembler.getHsaGrowthValueDetail(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail2 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail2.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail2.setId(0L);
        hsaGrowthValueDetail2.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail2.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail2.setMemberInfoGuid("memberInfoGuid");
        final HsaMemberGradeInfo hsaMemberGradeInfo5 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo5.setGuid("");
        hsaMemberGradeInfo5.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo5.setVipGrade(0);
        hsaMemberGradeInfo5.setName("");
        hsaMemberGradeInfo5.setGrowthValue(0);
        hsaMemberGradeInfo5.setIsDelete(0);
        hsaMemberGradeInfo5.setEffective(0);
        hsaMemberGradeInfo5.setRoleType("roleType");
        hsaMemberGradeInfo5.setUpgradeType(0);
        final MemberGradeChangeDTO gradeChangeDTO1 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("guid");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo1.setMemberGradeInfoName("");
        hsaOperationMemberInfo1.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo1.setMemberIntegral(0);
        hsaOperationMemberInfo1.setMemberGrowthValue(0);
        hsaOperationMemberInfo1.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO1.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo1));
        gradeChangeDTO1.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO1.setSourceType(0);
        gradeChangeDTO1.setIsRefresh(0);
        gradeChangeDTO1.setRoleType("roleType");
        gradeChangeDTO1.setSystem(0);
        gradeChangeDTO1.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getHsaGrowthValueDetail("guid", 0, hsaMemberGradeInfo5,
                HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build(), 0, 0, gradeChangeDTO1)).thenReturn(hsaGrowthValueDetail2);

        // Configure GradeChangeAssembler.getHsaMemberEquitiesReceiveRecord(...).
        final HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord = new HsaMemberEquitiesReceiveRecord();
        hsaMemberEquitiesReceiveRecord.setGuid("a782bb73-1961-4997-8c42-d00d348d6623");
        hsaMemberEquitiesReceiveRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberEquitiesReceiveRecord.setMemberGradeGuid("memberGradeGuid");
        hsaMemberEquitiesReceiveRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberEquitiesReceiveRecord.setGradeEquitiesGuid("gradeEquitiesGuid");
        when(mockGradeChangeAssembler.getHsaMemberEquitiesReceiveRecord("operatorGuid", "guid", "", "guid", 0,
                0)).thenReturn(hsaMemberEquitiesReceiveRecord);

        // Configure GradeChangeAssembler.getMemberGradeRelationDTO(...).
        final MemberGradeRelationDTO memberGradeRelationDTO = new MemberGradeRelationDTO();
        memberGradeRelationDTO.setMemberGuidList(Arrays.asList("value"));
        memberGradeRelationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberGradeRelationDTO.setRoleType("roleType");
        memberGradeRelationDTO.setNum(0);
        memberGradeRelationDTO.setUnit(0);
        final MemberGradeChangeDTO gradeChangeDTO2 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo2 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo2.setGuid("guid");
        hsaOperationMemberInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo2.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo2.setMemberGradeInfoName("");
        hsaOperationMemberInfo2.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo2.setMemberIntegral(0);
        hsaOperationMemberInfo2.setMemberGrowthValue(0);
        hsaOperationMemberInfo2.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO2.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo2));
        gradeChangeDTO2.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO2.setSourceType(0);
        gradeChangeDTO2.setIsRefresh(0);
        gradeChangeDTO2.setRoleType("roleType");
        gradeChangeDTO2.setSystem(0);
        gradeChangeDTO2.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getMemberGradeRelationDTO(gradeChangeDTO2, Arrays.asList("value")))
                .thenReturn(memberGradeRelationDTO);

        // Configure GradeChangeAssembler.getMemberGradePayRecordReqVO(...).
        final MemberGradePayRecordReqVO memberGradePayRecordReqVO = new MemberGradePayRecordReqVO();
        memberGradePayRecordReqVO.setMemberGuidList(Arrays.asList("value"));
        memberGradePayRecordReqVO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberGradePayRecordReqVO.setRoleType("roleType");
        memberGradePayRecordReqVO.setNum(0);
        memberGradePayRecordReqVO.setUnit(0);
        final MemberGradeChangeDTO gradeChangeDTO3 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo3 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo3.setGuid("guid");
        hsaOperationMemberInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo3.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo3.setMemberGradeInfoName("");
        hsaOperationMemberInfo3.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo3.setMemberIntegral(0);
        hsaOperationMemberInfo3.setMemberGrowthValue(0);
        hsaOperationMemberInfo3.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO3.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo3));
        gradeChangeDTO3.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO3.setSourceType(0);
        gradeChangeDTO3.setIsRefresh(0);
        gradeChangeDTO3.setRoleType("roleType");
        gradeChangeDTO3.setSystem(0);
        gradeChangeDTO3.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getMemberGradePayRecordReqVO(gradeChangeDTO3, Arrays.asList("value")))
                .thenReturn(memberGradePayRecordReqVO);

        when(mockCacheService.cleanToken("cacheKey")).thenReturn(false);

        // Run the test
        hsaMemberGradeChangeDetailServiceImplUnderTest.memberGradeChange(gradeChangeDTO);

        // Verify the results
        // Confirm GradeChangeAssembler.getMemberGradeChangeDetail(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo4 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo4.setGuid("guid");
        hsaOperationMemberInfo4.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo4.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo4.setMemberGradeInfoName("");
        hsaOperationMemberInfo4.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo4.setMemberIntegral(0);
        hsaOperationMemberInfo4.setMemberGrowthValue(0);
        hsaOperationMemberInfo4.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaMemberGradeInfo afterMemberGradeInfo2 = new HsaMemberGradeInfo();
        afterMemberGradeInfo2.setGuid("");
        afterMemberGradeInfo2.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo2.setVipGrade(0);
        afterMemberGradeInfo2.setName("");
        afterMemberGradeInfo2.setGrowthValue(0);
        afterMemberGradeInfo2.setIsDelete(0);
        afterMemberGradeInfo2.setEffective(0);
        afterMemberGradeInfo2.setRoleType("roleType");
        afterMemberGradeInfo2.setUpgradeType(0);
        final HsaMemberGradeInfo beforeMemberGradeInfo = new HsaMemberGradeInfo();
        beforeMemberGradeInfo.setGuid("");
        beforeMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        beforeMemberGradeInfo.setVipGrade(0);
        beforeMemberGradeInfo.setName("");
        beforeMemberGradeInfo.setGrowthValue(0);
        beforeMemberGradeInfo.setIsDelete(0);
        beforeMemberGradeInfo.setEffective(0);
        beforeMemberGradeInfo.setRoleType("roleType");
        beforeMemberGradeInfo.setUpgradeType(0);
        final HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail = new HsaMemberGradeChangeDetail();
        hsaMemberGradeChangeDetail.setGuid("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");
        hsaMemberGradeChangeDetail.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeChangeDetail.setMemberInfoGuid("guid");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeName("");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeGuid("");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail.setAfterChangeGradeName("");
        hsaMemberGradeChangeDetail.setAfterChangeGradeGuid("");
        hsaMemberGradeChangeDetail.setAfterChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail.setGradeChangeType(0);
        hsaMemberGradeChangeDetail.setCurrentGrowthValue(0);
        hsaMemberGradeChangeDetail.setIsShowApplet(0);
        hsaMemberGradeChangeDetail.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail.setAfterChangeGradeIcon("");
        hsaMemberGradeChangeDetail.setAfterChangeBackgroundColor("");
        hsaMemberGradeChangeDetail.setAfterChangeGrowthValue(0);
        verify(mockGradeChangeAssembler).getMemberGradeChangeDetail(hsaOperationMemberInfo4, afterMemberGradeInfo2,
                beforeMemberGradeInfo, hsaMemberGradeChangeDetail, LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Confirm HsaMemberGradeChangeDetailService.saveBatch(...).
        final HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail1 = new HsaMemberGradeChangeDetail();
        hsaMemberGradeChangeDetail1.setGuid("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");
        hsaMemberGradeChangeDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeChangeDetail1.setMemberInfoGuid("guid");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeName("");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeGuid("");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail1.setAfterChangeGradeName("");
        hsaMemberGradeChangeDetail1.setAfterChangeGradeGuid("");
        hsaMemberGradeChangeDetail1.setAfterChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail1.setGradeChangeType(0);
        hsaMemberGradeChangeDetail1.setCurrentGrowthValue(0);
        hsaMemberGradeChangeDetail1.setIsShowApplet(0);
        hsaMemberGradeChangeDetail1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail1.setAfterChangeGradeIcon("");
        hsaMemberGradeChangeDetail1.setAfterChangeBackgroundColor("");
        hsaMemberGradeChangeDetail1.setAfterChangeGrowthValue(0);
        final List<HsaMemberGradeChangeDetail> collection = Arrays.asList(hsaMemberGradeChangeDetail1);
        verify(mockHsaMemberGradeChangeDetailService).saveBatch(collection, 1000);

        // Confirm HsaMemberGradeRightsRecordService.saveBatch(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord4 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord4.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord4.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord4.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord4.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord4.setRightsType(0);
        final List<HsaMemberGradeRightsRecord> collection1 = Arrays.asList(hsaMemberGradeRightsRecord4);
        verify(mockHsaMemberGradeRightsRecordService).saveBatch(collection1, 1000);

        // Confirm HsaGrowthValueDetailService.saveBatch(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail3 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail3.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail3.setId(0L);
        hsaGrowthValueDetail3.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail3.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail3.setMemberInfoGuid("memberInfoGuid");
        final List<HsaGrowthValueDetail> collection2 = Arrays.asList(hsaGrowthValueDetail3);
        verify(mockHsaGrowthValueDetailService).saveBatch(collection2, 1000);

        // Confirm HsaIntegralDetailService.saveBatch(...).
        final HsaIntegralDetail hsaIntegralDetail3 = new HsaIntegralDetail();
        hsaIntegralDetail3.setId(0L);
        hsaIntegralDetail3.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail3.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail3.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail3.setIntegralTaskGuid("integralTaskGuid");
        final List<HsaIntegralDetail> collection3 = Arrays.asList(hsaIntegralDetail3);
        verify(mockHsaIntegralDetailService).saveBatch(collection3, 1000);

        // Confirm HsaMemberEquitiesReceiveRecordServiceImpl.saveBatch(...).
        final HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord1 = new HsaMemberEquitiesReceiveRecord();
        hsaMemberEquitiesReceiveRecord1.setGuid("a782bb73-1961-4997-8c42-d00d348d6623");
        hsaMemberEquitiesReceiveRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberEquitiesReceiveRecord1.setMemberGradeGuid("memberGradeGuid");
        hsaMemberEquitiesReceiveRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberEquitiesReceiveRecord1.setGradeEquitiesGuid("gradeEquitiesGuid");
        final List<HsaMemberEquitiesReceiveRecord> entityList = Arrays.asList(hsaMemberEquitiesReceiveRecord1);
        verify(mockHsaMemberEquitiesReceiveRecordService).saveBatch(entityList, 1000);

        // Confirm HsaOperationMemberInfoMapper.batchUpdateGrowth(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo5 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo5.setGuid("guid");
        hsaOperationMemberInfo5.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo5.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo5.setMemberGradeInfoName("");
        hsaOperationMemberInfo5.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo5.setMemberIntegral(0);
        hsaOperationMemberInfo5.setMemberGrowthValue(0);
        hsaOperationMemberInfo5.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo5);
        verify(mockHsaOperationMemberInfoMapper).batchUpdateGrowth(hsaOperationMemberInfos);
        verify(mockMemberGradeRelationService).batchDeleteMemberGrade(Arrays.asList("value"), "roleType");

        // Confirm IHsaMemberGradeRelationService.batchSaveAndUpdateRelation(...).
        final MemberGradeRelationDTO relationDTO = new MemberGradeRelationDTO();
        relationDTO.setMemberGuidList(Arrays.asList("value"));
        relationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        relationDTO.setRoleType("roleType");
        relationDTO.setNum(0);
        relationDTO.setUnit(0);
        verify(mockMemberGradeRelationService).batchSaveAndUpdateRelation(relationDTO);

        // Confirm IHsaMemberGradePayRecordService.batchAdd(...).
        final MemberGradePayRecordReqVO gradePayRecordReqVO = new MemberGradePayRecordReqVO();
        gradePayRecordReqVO.setMemberGuidList(Arrays.asList("value"));
        gradePayRecordReqVO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        gradePayRecordReqVO.setRoleType("roleType");
        gradePayRecordReqVO.setNum(0);
        gradePayRecordReqVO.setUnit(0);
        verify(mockMemberGradePayRecordService).batchAdd(gradePayRecordReqVO);

        // Confirm HsaOperationMemberInfoMapper.batchUpdateIntegral(...).
        final UpdateIntegral updateIntegral = new UpdateIntegral();
        updateIntegral.setGuid("32f2621a-4a96-4713-abb6-d3f2e4b4e56f");
        updateIntegral.setIntegralValue(0);
        final List<UpdateIntegral> updateMemberIntegralList = Arrays.asList(updateIntegral);
        verify(mockHsaOperationMemberInfoMapper).batchUpdateIntegral(updateMemberIntegralList);
    }

    @Test
    public void testMemberGradeChange_HsaMemberGradeInfoMapperSelectListReturnsNoItems() {
        // Setup
        final MemberGradeChangeDTO gradeChangeDTO = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("guid");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo.setMemberGradeInfoName("");
        hsaOperationMemberInfo.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo.setMemberIntegral(0);
        hsaOperationMemberInfo.setMemberGrowthValue(0);
        hsaOperationMemberInfo.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo));
        gradeChangeDTO.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO.setSourceType(0);
        gradeChangeDTO.setIsRefresh(0);
        gradeChangeDTO.setRoleType("roleType");
        gradeChangeDTO.setSystem(0);
        gradeChangeDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");

        // Configure HsaControlledGradeStateMapper.selectOne(...).
        final HsaControlledGradeState hsaControlledGradeState = new HsaControlledGradeState();
        hsaControlledGradeState.setId(0L);
        hsaControlledGradeState.setOperSubjectGuid("operSubjectGuid");
        hsaControlledGradeState.setState(0);
        hsaControlledGradeState.setIsDelete(false);
        hsaControlledGradeState.setRoleType("roleType");
        when(mockHsaControlledGradeStateMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaControlledGradeState);

        when(mockHsaMemberGradeInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure IHsaMemberGradeRelationService.listByMemberGuidList(...).
        final HsaMemberGradeRelation hsaMemberGradeRelation = new HsaMemberGradeRelation();
        hsaMemberGradeRelation.setId(0);
        hsaMemberGradeRelation.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRelation.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRelation.setMemberInfoGradeGuid("memberInfoGradeGuid");
        hsaMemberGradeRelation.setIsEnable(false);
        final List<HsaMemberGradeRelation> hsaMemberGradeRelations = Arrays.asList(hsaMemberGradeRelation);
        when(mockMemberGradeRelationService.listByMemberGuidList(Arrays.asList("value"), "roleType"))
                .thenReturn(hsaMemberGradeRelations);

        // Configure HsaGradeEquitiesService.list(...).
        final List<HsaBusinessEquities> hsaBusinessEquities = Arrays.asList(HsaBusinessEquities.builder()
                .guid("guid")
                .memberGradeInfoGuid("memberGradeInfoGuid")
                .equitiesRuleType(0)
                .totalGiveNumber(0)
                .setPeriod(0)
                .isDelete(0)
                .effective(0)
                .operSubjectGuid("operSubjectGuid")
                .build());
        when(mockHsaGradeEquitiesService.list(any(LambdaQueryWrapper.class))).thenReturn(hsaBusinessEquities);

        // Configure HsaMemberGradeRightsRecordMapper.selectList(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord.setRightsType(0);
        final List<HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecords = Arrays.asList(hsaMemberGradeRightsRecord);
        when(mockHsaMemberGradeRightsRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberGradeRightsRecords);

        // Configure HsaGradeEquitiesMapper.selectList(...).
        final List<HsaBusinessEquities> hsaBusinessEquities1 = Arrays.asList(HsaBusinessEquities.builder()
                .guid("guid")
                .memberGradeInfoGuid("memberGradeInfoGuid")
                .equitiesRuleType(0)
                .totalGiveNumber(0)
                .setPeriod(0)
                .isDelete(0)
                .effective(0)
                .operSubjectGuid("operSubjectGuid")
                .build());
        when(mockHsaGradeEquitiesMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaBusinessEquities1);

        // Configure HsaGiftBagBaseInfoMapper.selectOne(...).
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo.setId(0L);
        hsaGiftBagBaseInfo.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo.setEquitiesType(0);
        hsaGiftBagBaseInfo.setEquitiesRule(0);
        when(mockHsaGiftBagBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaGiftBagBaseInfo);

        // Configure HsaMemberGradeInfoMapper.selectOne(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("");
        hsaMemberGradeInfo.setGrowthValue(0);
        hsaMemberGradeInfo.setIsDelete(0);
        hsaMemberGradeInfo.setEffective(0);
        hsaMemberGradeInfo.setRoleType("roleType");
        hsaMemberGradeInfo.setUpgradeType(0);
        when(mockHsaMemberGradeInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfo);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");

        // Configure HsaGradeGiftBagMapper.selectList(...).
        final HsaGradeGiftBag hsaGradeGiftBag = new HsaGradeGiftBag();
        hsaGradeGiftBag.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag.setType(0);
        hsaGradeGiftBag.setValue(0);
        hsaGradeGiftBag.setIsDelete(0);
        hsaGradeGiftBag.setEffective(0);
        final List<HsaGradeGiftBag> hsaGradeGiftBags = Arrays.asList(hsaGradeGiftBag);
        when(mockHsaGradeGiftBagMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaGradeGiftBags);

        // Configure GradeChangeAssembler.getHsaMemberGradeRightsRecord(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord1 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord1.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord1.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord1.setRightsType(0);
        final GradeRightsProcessorDTO gradeRightsProcessorDTO = new GradeRightsProcessorDTO();
        gradeRightsProcessorDTO.setUpdateMemberIntegralMap(new HashMap<>());
        gradeRightsProcessorDTO.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo1 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo1.setEquitiesRule(0);
        gradeRightsProcessorDTO.setHsaGiftBagBaseInfo(hsaGiftBagBaseInfo1);
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord2 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord2.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord2.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord2.setRightsType(0);
        gradeRightsProcessorDTO.setHsaMemberGradeRightsRecords(Arrays.asList(hsaMemberGradeRightsRecord2));
        final HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        gradeRightsProcessorDTO.setHsaGrowthValueDetails(Arrays.asList(hsaGrowthValueDetail));
        final HsaIntegralDetail hsaIntegralDetail = new HsaIntegralDetail();
        gradeRightsProcessorDTO.setHsaIntegralDetails(Arrays.asList(hsaIntegralDetail));
        gradeRightsProcessorDTO.setSourceType(0);
        gradeRightsProcessorDTO.setOperSubjectGuid("operSubjectGuid");
        final HsaOperationMemberInfo operationMemberInfo = new HsaOperationMemberInfo();
        operationMemberInfo.setGuid("guid");
        operationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo.setMemberGradeInfoGuid("");
        operationMemberInfo.setMemberGradeInfoName("");
        operationMemberInfo.setOperatorGuid("operatorGuid");
        operationMemberInfo.setMemberIntegral(0);
        operationMemberInfo.setMemberGrowthValue(0);
        operationMemberInfo.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag1 = new HsaGradeGiftBag();
        hsaGradeGiftBag1.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag1.setType(0);
        hsaGradeGiftBag1.setValue(0);
        hsaGradeGiftBag1.setIsDelete(0);
        hsaGradeGiftBag1.setEffective(0);
        when(mockGradeChangeAssembler.getHsaMemberGradeRightsRecord(gradeRightsProcessorDTO, operationMemberInfo,
                hsaGradeGiftBag1)).thenReturn(hsaMemberGradeRightsRecord1);

        // Configure GradeChangeAssembler.getHsaGrowthValueDetail(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail1 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail1.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail1.setId(0L);
        hsaGrowthValueDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail1.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail1.setMemberInfoGuid("memberInfoGuid");
        final HsaOperationMemberInfo operationMemberInfo1 = new HsaOperationMemberInfo();
        operationMemberInfo1.setGuid("guid");
        operationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo1.setMemberGradeInfoGuid("");
        operationMemberInfo1.setMemberGradeInfoName("");
        operationMemberInfo1.setOperatorGuid("operatorGuid");
        operationMemberInfo1.setMemberIntegral(0);
        operationMemberInfo1.setMemberGrowthValue(0);
        operationMemberInfo1.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag2 = new HsaGradeGiftBag();
        hsaGradeGiftBag2.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag2.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag2.setType(0);
        hsaGradeGiftBag2.setValue(0);
        hsaGradeGiftBag2.setIsDelete(0);
        hsaGradeGiftBag2.setEffective(0);
        final HsaMemberGradeInfo hsaMemberGradeInfo1 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo1.setGuid("");
        hsaMemberGradeInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo1.setVipGrade(0);
        hsaMemberGradeInfo1.setName("");
        hsaMemberGradeInfo1.setGrowthValue(0);
        hsaMemberGradeInfo1.setIsDelete(0);
        hsaMemberGradeInfo1.setEffective(0);
        hsaMemberGradeInfo1.setRoleType("roleType");
        hsaMemberGradeInfo1.setUpgradeType(0);
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo2 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo2.setId(0L);
        hsaGiftBagBaseInfo2.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo2.setEquitiesType(0);
        hsaGiftBagBaseInfo2.setEquitiesRule(0);
        final HsaMemberGradeInfo afterMemberGradeInfo = new HsaMemberGradeInfo();
        afterMemberGradeInfo.setGuid("");
        afterMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo.setVipGrade(0);
        afterMemberGradeInfo.setName("");
        afterMemberGradeInfo.setGrowthValue(0);
        afterMemberGradeInfo.setIsDelete(0);
        afterMemberGradeInfo.setEffective(0);
        afterMemberGradeInfo.setRoleType("roleType");
        afterMemberGradeInfo.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaGrowthValueDetail(operationMemberInfo1, 0, "operSubjectGuid",
                hsaGradeGiftBag2, hsaMemberGradeInfo1, hsaGiftBagBaseInfo2, afterMemberGradeInfo))
                .thenReturn(hsaGrowthValueDetail1);

        // Configure GradeChangeAssembler.getHsaIntegralValueDetail(...).
        final HsaIntegralDetail hsaIntegralDetail1 = new HsaIntegralDetail();
        hsaIntegralDetail1.setId(0L);
        hsaIntegralDetail1.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail1.setIntegralTaskGuid("integralTaskGuid");
        final HsaOperationMemberInfo operationMemberInfo2 = new HsaOperationMemberInfo();
        operationMemberInfo2.setGuid("guid");
        operationMemberInfo2.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo2.setMemberGradeInfoGuid("");
        operationMemberInfo2.setMemberGradeInfoName("");
        operationMemberInfo2.setOperatorGuid("operatorGuid");
        operationMemberInfo2.setMemberIntegral(0);
        operationMemberInfo2.setMemberGrowthValue(0);
        operationMemberInfo2.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag3 = new HsaGradeGiftBag();
        hsaGradeGiftBag3.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag3.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag3.setType(0);
        hsaGradeGiftBag3.setValue(0);
        hsaGradeGiftBag3.setIsDelete(0);
        hsaGradeGiftBag3.setEffective(0);
        final HsaMemberGradeInfo hsaMemberGradeInfo2 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo2.setGuid("");
        hsaMemberGradeInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo2.setVipGrade(0);
        hsaMemberGradeInfo2.setName("");
        hsaMemberGradeInfo2.setGrowthValue(0);
        hsaMemberGradeInfo2.setIsDelete(0);
        hsaMemberGradeInfo2.setEffective(0);
        hsaMemberGradeInfo2.setRoleType("roleType");
        hsaMemberGradeInfo2.setUpgradeType(0);
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo3 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo3.setId(0L);
        hsaGiftBagBaseInfo3.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo3.setEquitiesType(0);
        hsaGiftBagBaseInfo3.setEquitiesRule(0);
        final HsaMemberGradeInfo afterMemberGradeInfo1 = new HsaMemberGradeInfo();
        afterMemberGradeInfo1.setGuid("");
        afterMemberGradeInfo1.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo1.setVipGrade(0);
        afterMemberGradeInfo1.setName("");
        afterMemberGradeInfo1.setGrowthValue(0);
        afterMemberGradeInfo1.setIsDelete(0);
        afterMemberGradeInfo1.setEffective(0);
        afterMemberGradeInfo1.setRoleType("roleType");
        afterMemberGradeInfo1.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaIntegralValueDetail(operationMemberInfo2, 0, "operSubjectGuid",
                hsaGradeGiftBag3, hsaMemberGradeInfo2, hsaGiftBagBaseInfo3, afterMemberGradeInfo1))
                .thenReturn(hsaIntegralDetail1);

        // Configure GradeChangeAssembler.getMemberGradeRightsRecord(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord3 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord3.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord3.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord3.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord3.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord3.setRightsType(0);
        final HsaOperationMemberInfo operationMemberInfo3 = new HsaOperationMemberInfo();
        operationMemberInfo3.setGuid("guid");
        operationMemberInfo3.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo3.setMemberGradeInfoGuid("");
        operationMemberInfo3.setMemberGradeInfoName("");
        operationMemberInfo3.setOperatorGuid("operatorGuid");
        operationMemberInfo3.setMemberIntegral(0);
        operationMemberInfo3.setMemberGrowthValue(0);
        operationMemberInfo3.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockGradeChangeAssembler.getMemberGradeRightsRecord(operationMemberInfo3, "operSubjectGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build())).thenReturn(hsaMemberGradeRightsRecord3);

        // Configure HsaMemberEquitiesReceiveRecordMapper.selectAllGrowthValue(...).
        final MemberGrowthValueRelationVO memberGrowthValueRelationVO = new MemberGrowthValueRelationVO();
        memberGrowthValueRelationVO.setMemberGuid("memberGuid");
        memberGrowthValueRelationVO.setGrowthValue(0);
        final List<MemberGrowthValueRelationVO> memberGrowthValueRelationVOS = Arrays.asList(
                memberGrowthValueRelationVO);
        when(mockHsaMemberEquitiesReceiveRecordMapper.selectAllGrowthValue("guid", 0))
                .thenReturn(memberGrowthValueRelationVOS);

        // Configure GradeChangeAssembler.getHsaIntegralValueDetail(...).
        final HsaIntegralDetail hsaIntegralDetail2 = new HsaIntegralDetail();
        hsaIntegralDetail2.setId(0L);
        hsaIntegralDetail2.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail2.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail2.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail2.setIntegralTaskGuid("integralTaskGuid");
        final HsaMemberGradeInfo hsaMemberGradeInfo3 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo3.setGuid("");
        hsaMemberGradeInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo3.setVipGrade(0);
        hsaMemberGradeInfo3.setName("");
        hsaMemberGradeInfo3.setGrowthValue(0);
        hsaMemberGradeInfo3.setIsDelete(0);
        hsaMemberGradeInfo3.setEffective(0);
        hsaMemberGradeInfo3.setRoleType("roleType");
        hsaMemberGradeInfo3.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaIntegralValueDetail("guid", 0, "operSubjectGuid", hsaMemberGradeInfo3,
                HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build(), 0, 0)).thenReturn(hsaIntegralDetail2);

        // Configure GradeChangeAssembler.getHsaGrowthValueDetail(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail2 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail2.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail2.setId(0L);
        hsaGrowthValueDetail2.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail2.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail2.setMemberInfoGuid("memberInfoGuid");
        final HsaMemberGradeInfo hsaMemberGradeInfo4 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo4.setGuid("");
        hsaMemberGradeInfo4.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo4.setVipGrade(0);
        hsaMemberGradeInfo4.setName("");
        hsaMemberGradeInfo4.setGrowthValue(0);
        hsaMemberGradeInfo4.setIsDelete(0);
        hsaMemberGradeInfo4.setEffective(0);
        hsaMemberGradeInfo4.setRoleType("roleType");
        hsaMemberGradeInfo4.setUpgradeType(0);
        final MemberGradeChangeDTO gradeChangeDTO1 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("guid");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo1.setMemberGradeInfoName("");
        hsaOperationMemberInfo1.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo1.setMemberIntegral(0);
        hsaOperationMemberInfo1.setMemberGrowthValue(0);
        hsaOperationMemberInfo1.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO1.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo1));
        gradeChangeDTO1.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO1.setSourceType(0);
        gradeChangeDTO1.setIsRefresh(0);
        gradeChangeDTO1.setRoleType("roleType");
        gradeChangeDTO1.setSystem(0);
        gradeChangeDTO1.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getHsaGrowthValueDetail("guid", 0, hsaMemberGradeInfo4,
                HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build(), 0, 0, gradeChangeDTO1)).thenReturn(hsaGrowthValueDetail2);

        // Configure GradeChangeAssembler.getHsaMemberEquitiesReceiveRecord(...).
        final HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord = new HsaMemberEquitiesReceiveRecord();
        hsaMemberEquitiesReceiveRecord.setGuid("a782bb73-1961-4997-8c42-d00d348d6623");
        hsaMemberEquitiesReceiveRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberEquitiesReceiveRecord.setMemberGradeGuid("memberGradeGuid");
        hsaMemberEquitiesReceiveRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberEquitiesReceiveRecord.setGradeEquitiesGuid("gradeEquitiesGuid");
        when(mockGradeChangeAssembler.getHsaMemberEquitiesReceiveRecord("operatorGuid", "guid", "", "guid", 0,
                0)).thenReturn(hsaMemberEquitiesReceiveRecord);

        // Configure GradeChangeAssembler.getMemberGradeRelationDTO(...).
        final MemberGradeRelationDTO memberGradeRelationDTO = new MemberGradeRelationDTO();
        memberGradeRelationDTO.setMemberGuidList(Arrays.asList("value"));
        memberGradeRelationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberGradeRelationDTO.setRoleType("roleType");
        memberGradeRelationDTO.setNum(0);
        memberGradeRelationDTO.setUnit(0);
        final MemberGradeChangeDTO gradeChangeDTO2 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo2 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo2.setGuid("guid");
        hsaOperationMemberInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo2.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo2.setMemberGradeInfoName("");
        hsaOperationMemberInfo2.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo2.setMemberIntegral(0);
        hsaOperationMemberInfo2.setMemberGrowthValue(0);
        hsaOperationMemberInfo2.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO2.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo2));
        gradeChangeDTO2.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO2.setSourceType(0);
        gradeChangeDTO2.setIsRefresh(0);
        gradeChangeDTO2.setRoleType("roleType");
        gradeChangeDTO2.setSystem(0);
        gradeChangeDTO2.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getMemberGradeRelationDTO(gradeChangeDTO2, Arrays.asList("value")))
                .thenReturn(memberGradeRelationDTO);

        // Configure GradeChangeAssembler.getMemberGradePayRecordReqVO(...).
        final MemberGradePayRecordReqVO memberGradePayRecordReqVO = new MemberGradePayRecordReqVO();
        memberGradePayRecordReqVO.setMemberGuidList(Arrays.asList("value"));
        memberGradePayRecordReqVO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberGradePayRecordReqVO.setRoleType("roleType");
        memberGradePayRecordReqVO.setNum(0);
        memberGradePayRecordReqVO.setUnit(0);
        final MemberGradeChangeDTO gradeChangeDTO3 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo3 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo3.setGuid("guid");
        hsaOperationMemberInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo3.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo3.setMemberGradeInfoName("");
        hsaOperationMemberInfo3.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo3.setMemberIntegral(0);
        hsaOperationMemberInfo3.setMemberGrowthValue(0);
        hsaOperationMemberInfo3.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO3.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo3));
        gradeChangeDTO3.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO3.setSourceType(0);
        gradeChangeDTO3.setIsRefresh(0);
        gradeChangeDTO3.setRoleType("roleType");
        gradeChangeDTO3.setSystem(0);
        gradeChangeDTO3.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getMemberGradePayRecordReqVO(gradeChangeDTO3, Arrays.asList("value")))
                .thenReturn(memberGradePayRecordReqVO);

        when(mockCacheService.cleanToken("cacheKey")).thenReturn(false);

        // Run the test
        hsaMemberGradeChangeDetailServiceImplUnderTest.memberGradeChange(gradeChangeDTO);

        // Verify the results
        // Confirm GradeChangeAssembler.getMemberGradeChangeDetail(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo4 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo4.setGuid("guid");
        hsaOperationMemberInfo4.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo4.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo4.setMemberGradeInfoName("");
        hsaOperationMemberInfo4.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo4.setMemberIntegral(0);
        hsaOperationMemberInfo4.setMemberGrowthValue(0);
        hsaOperationMemberInfo4.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaMemberGradeInfo afterMemberGradeInfo2 = new HsaMemberGradeInfo();
        afterMemberGradeInfo2.setGuid("");
        afterMemberGradeInfo2.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo2.setVipGrade(0);
        afterMemberGradeInfo2.setName("");
        afterMemberGradeInfo2.setGrowthValue(0);
        afterMemberGradeInfo2.setIsDelete(0);
        afterMemberGradeInfo2.setEffective(0);
        afterMemberGradeInfo2.setRoleType("roleType");
        afterMemberGradeInfo2.setUpgradeType(0);
        final HsaMemberGradeInfo beforeMemberGradeInfo = new HsaMemberGradeInfo();
        beforeMemberGradeInfo.setGuid("");
        beforeMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        beforeMemberGradeInfo.setVipGrade(0);
        beforeMemberGradeInfo.setName("");
        beforeMemberGradeInfo.setGrowthValue(0);
        beforeMemberGradeInfo.setIsDelete(0);
        beforeMemberGradeInfo.setEffective(0);
        beforeMemberGradeInfo.setRoleType("roleType");
        beforeMemberGradeInfo.setUpgradeType(0);
        final HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail = new HsaMemberGradeChangeDetail();
        hsaMemberGradeChangeDetail.setGuid("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");
        hsaMemberGradeChangeDetail.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeChangeDetail.setMemberInfoGuid("guid");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeName("");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeGuid("");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail.setAfterChangeGradeName("");
        hsaMemberGradeChangeDetail.setAfterChangeGradeGuid("");
        hsaMemberGradeChangeDetail.setAfterChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail.setGradeChangeType(0);
        hsaMemberGradeChangeDetail.setCurrentGrowthValue(0);
        hsaMemberGradeChangeDetail.setIsShowApplet(0);
        hsaMemberGradeChangeDetail.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail.setAfterChangeGradeIcon("");
        hsaMemberGradeChangeDetail.setAfterChangeBackgroundColor("");
        hsaMemberGradeChangeDetail.setAfterChangeGrowthValue(0);
        verify(mockGradeChangeAssembler).getMemberGradeChangeDetail(hsaOperationMemberInfo4, afterMemberGradeInfo2,
                beforeMemberGradeInfo, hsaMemberGradeChangeDetail, LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Confirm HsaMemberGradeChangeDetailService.saveBatch(...).
        final HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail1 = new HsaMemberGradeChangeDetail();
        hsaMemberGradeChangeDetail1.setGuid("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");
        hsaMemberGradeChangeDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeChangeDetail1.setMemberInfoGuid("guid");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeName("");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeGuid("");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail1.setAfterChangeGradeName("");
        hsaMemberGradeChangeDetail1.setAfterChangeGradeGuid("");
        hsaMemberGradeChangeDetail1.setAfterChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail1.setGradeChangeType(0);
        hsaMemberGradeChangeDetail1.setCurrentGrowthValue(0);
        hsaMemberGradeChangeDetail1.setIsShowApplet(0);
        hsaMemberGradeChangeDetail1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail1.setAfterChangeGradeIcon("");
        hsaMemberGradeChangeDetail1.setAfterChangeBackgroundColor("");
        hsaMemberGradeChangeDetail1.setAfterChangeGrowthValue(0);
        final List<HsaMemberGradeChangeDetail> collection = Arrays.asList(hsaMemberGradeChangeDetail1);
        verify(mockHsaMemberGradeChangeDetailService).saveBatch(collection, 1000);

        // Confirm HsaMemberGradeRightsRecordService.saveBatch(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord4 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord4.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord4.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord4.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord4.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord4.setRightsType(0);
        final List<HsaMemberGradeRightsRecord> collection1 = Arrays.asList(hsaMemberGradeRightsRecord4);
        verify(mockHsaMemberGradeRightsRecordService).saveBatch(collection1, 1000);

        // Confirm HsaGrowthValueDetailService.saveBatch(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail3 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail3.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail3.setId(0L);
        hsaGrowthValueDetail3.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail3.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail3.setMemberInfoGuid("memberInfoGuid");
        final List<HsaGrowthValueDetail> collection2 = Arrays.asList(hsaGrowthValueDetail3);
        verify(mockHsaGrowthValueDetailService).saveBatch(collection2, 1000);

        // Confirm HsaIntegralDetailService.saveBatch(...).
        final HsaIntegralDetail hsaIntegralDetail3 = new HsaIntegralDetail();
        hsaIntegralDetail3.setId(0L);
        hsaIntegralDetail3.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail3.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail3.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail3.setIntegralTaskGuid("integralTaskGuid");
        final List<HsaIntegralDetail> collection3 = Arrays.asList(hsaIntegralDetail3);
        verify(mockHsaIntegralDetailService).saveBatch(collection3, 1000);

        // Confirm HsaMemberEquitiesReceiveRecordServiceImpl.saveBatch(...).
        final HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord1 = new HsaMemberEquitiesReceiveRecord();
        hsaMemberEquitiesReceiveRecord1.setGuid("a782bb73-1961-4997-8c42-d00d348d6623");
        hsaMemberEquitiesReceiveRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberEquitiesReceiveRecord1.setMemberGradeGuid("memberGradeGuid");
        hsaMemberEquitiesReceiveRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberEquitiesReceiveRecord1.setGradeEquitiesGuid("gradeEquitiesGuid");
        final List<HsaMemberEquitiesReceiveRecord> entityList = Arrays.asList(hsaMemberEquitiesReceiveRecord1);
        verify(mockHsaMemberEquitiesReceiveRecordService).saveBatch(entityList, 1000);

        // Confirm HsaOperationMemberInfoMapper.batchUpdateGrowth(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo5 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo5.setGuid("guid");
        hsaOperationMemberInfo5.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo5.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo5.setMemberGradeInfoName("");
        hsaOperationMemberInfo5.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo5.setMemberIntegral(0);
        hsaOperationMemberInfo5.setMemberGrowthValue(0);
        hsaOperationMemberInfo5.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo5);
        verify(mockHsaOperationMemberInfoMapper).batchUpdateGrowth(hsaOperationMemberInfos);
        verify(mockMemberGradeRelationService).batchDeleteMemberGrade(Arrays.asList("value"), "roleType");

        // Confirm IHsaMemberGradeRelationService.batchSaveAndUpdateRelation(...).
        final MemberGradeRelationDTO relationDTO = new MemberGradeRelationDTO();
        relationDTO.setMemberGuidList(Arrays.asList("value"));
        relationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        relationDTO.setRoleType("roleType");
        relationDTO.setNum(0);
        relationDTO.setUnit(0);
        verify(mockMemberGradeRelationService).batchSaveAndUpdateRelation(relationDTO);

        // Confirm IHsaMemberGradePayRecordService.batchAdd(...).
        final MemberGradePayRecordReqVO gradePayRecordReqVO = new MemberGradePayRecordReqVO();
        gradePayRecordReqVO.setMemberGuidList(Arrays.asList("value"));
        gradePayRecordReqVO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        gradePayRecordReqVO.setRoleType("roleType");
        gradePayRecordReqVO.setNum(0);
        gradePayRecordReqVO.setUnit(0);
        verify(mockMemberGradePayRecordService).batchAdd(gradePayRecordReqVO);

        // Confirm HsaOperationMemberInfoMapper.batchUpdateIntegral(...).
        final UpdateIntegral updateIntegral = new UpdateIntegral();
        updateIntegral.setGuid("32f2621a-4a96-4713-abb6-d3f2e4b4e56f");
        updateIntegral.setIntegralValue(0);
        final List<UpdateIntegral> updateMemberIntegralList = Arrays.asList(updateIntegral);
        verify(mockHsaOperationMemberInfoMapper).batchUpdateIntegral(updateMemberIntegralList);
    }

    @Test
    public void testMemberGradeChange_IHsaMemberGradeRelationServiceListByMemberGuidListReturnsNoItems() {
        // Setup
        final MemberGradeChangeDTO gradeChangeDTO = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("guid");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo.setMemberGradeInfoName("");
        hsaOperationMemberInfo.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo.setMemberIntegral(0);
        hsaOperationMemberInfo.setMemberGrowthValue(0);
        hsaOperationMemberInfo.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo));
        gradeChangeDTO.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO.setSourceType(0);
        gradeChangeDTO.setIsRefresh(0);
        gradeChangeDTO.setRoleType("roleType");
        gradeChangeDTO.setSystem(0);
        gradeChangeDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");

        // Configure HsaControlledGradeStateMapper.selectOne(...).
        final HsaControlledGradeState hsaControlledGradeState = new HsaControlledGradeState();
        hsaControlledGradeState.setId(0L);
        hsaControlledGradeState.setOperSubjectGuid("operSubjectGuid");
        hsaControlledGradeState.setState(0);
        hsaControlledGradeState.setIsDelete(false);
        hsaControlledGradeState.setRoleType("roleType");
        when(mockHsaControlledGradeStateMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaControlledGradeState);

        // Configure HsaMemberGradeInfoMapper.selectList(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("");
        hsaMemberGradeInfo.setGrowthValue(0);
        hsaMemberGradeInfo.setIsDelete(0);
        hsaMemberGradeInfo.setEffective(0);
        hsaMemberGradeInfo.setRoleType("roleType");
        hsaMemberGradeInfo.setUpgradeType(0);
        final List<HsaMemberGradeInfo> hsaMemberGradeInfos = Arrays.asList(hsaMemberGradeInfo);
        when(mockHsaMemberGradeInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfos);

        when(mockMemberGradeRelationService.listByMemberGuidList(Arrays.asList("value"), "roleType"))
                .thenReturn(Collections.emptyList());

        // Configure HsaGradeEquitiesService.list(...).
        final List<HsaBusinessEquities> hsaBusinessEquities = Arrays.asList(HsaBusinessEquities.builder()
                .guid("guid")
                .memberGradeInfoGuid("memberGradeInfoGuid")
                .equitiesRuleType(0)
                .totalGiveNumber(0)
                .setPeriod(0)
                .isDelete(0)
                .effective(0)
                .operSubjectGuid("operSubjectGuid")
                .build());
        when(mockHsaGradeEquitiesService.list(any(LambdaQueryWrapper.class))).thenReturn(hsaBusinessEquities);

        // Configure HsaMemberGradeRightsRecordMapper.selectList(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord.setRightsType(0);
        final List<HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecords = Arrays.asList(hsaMemberGradeRightsRecord);
        when(mockHsaMemberGradeRightsRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberGradeRightsRecords);

        // Configure HsaGradeEquitiesMapper.selectList(...).
        final List<HsaBusinessEquities> hsaBusinessEquities1 = Arrays.asList(HsaBusinessEquities.builder()
                .guid("guid")
                .memberGradeInfoGuid("memberGradeInfoGuid")
                .equitiesRuleType(0)
                .totalGiveNumber(0)
                .setPeriod(0)
                .isDelete(0)
                .effective(0)
                .operSubjectGuid("operSubjectGuid")
                .build());
        when(mockHsaGradeEquitiesMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaBusinessEquities1);

        // Configure HsaGiftBagBaseInfoMapper.selectOne(...).
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo.setId(0L);
        hsaGiftBagBaseInfo.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo.setEquitiesType(0);
        hsaGiftBagBaseInfo.setEquitiesRule(0);
        when(mockHsaGiftBagBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaGiftBagBaseInfo);

        // Configure HsaMemberGradeInfoMapper.selectOne(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo1 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo1.setGuid("");
        hsaMemberGradeInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo1.setVipGrade(0);
        hsaMemberGradeInfo1.setName("");
        hsaMemberGradeInfo1.setGrowthValue(0);
        hsaMemberGradeInfo1.setIsDelete(0);
        hsaMemberGradeInfo1.setEffective(0);
        hsaMemberGradeInfo1.setRoleType("roleType");
        hsaMemberGradeInfo1.setUpgradeType(0);
        when(mockHsaMemberGradeInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfo1);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");

        // Configure HsaGradeGiftBagMapper.selectList(...).
        final HsaGradeGiftBag hsaGradeGiftBag = new HsaGradeGiftBag();
        hsaGradeGiftBag.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag.setType(0);
        hsaGradeGiftBag.setValue(0);
        hsaGradeGiftBag.setIsDelete(0);
        hsaGradeGiftBag.setEffective(0);
        final List<HsaGradeGiftBag> hsaGradeGiftBags = Arrays.asList(hsaGradeGiftBag);
        when(mockHsaGradeGiftBagMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaGradeGiftBags);

        // Configure GradeChangeAssembler.getHsaMemberGradeRightsRecord(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord1 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord1.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord1.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord1.setRightsType(0);
        final GradeRightsProcessorDTO gradeRightsProcessorDTO = new GradeRightsProcessorDTO();
        gradeRightsProcessorDTO.setUpdateMemberIntegralMap(new HashMap<>());
        gradeRightsProcessorDTO.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo1 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo1.setEquitiesRule(0);
        gradeRightsProcessorDTO.setHsaGiftBagBaseInfo(hsaGiftBagBaseInfo1);
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord2 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord2.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord2.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord2.setRightsType(0);
        gradeRightsProcessorDTO.setHsaMemberGradeRightsRecords(Arrays.asList(hsaMemberGradeRightsRecord2));
        final HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        gradeRightsProcessorDTO.setHsaGrowthValueDetails(Arrays.asList(hsaGrowthValueDetail));
        final HsaIntegralDetail hsaIntegralDetail = new HsaIntegralDetail();
        gradeRightsProcessorDTO.setHsaIntegralDetails(Arrays.asList(hsaIntegralDetail));
        gradeRightsProcessorDTO.setSourceType(0);
        gradeRightsProcessorDTO.setOperSubjectGuid("operSubjectGuid");
        final HsaOperationMemberInfo operationMemberInfo = new HsaOperationMemberInfo();
        operationMemberInfo.setGuid("guid");
        operationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo.setMemberGradeInfoGuid("");
        operationMemberInfo.setMemberGradeInfoName("");
        operationMemberInfo.setOperatorGuid("operatorGuid");
        operationMemberInfo.setMemberIntegral(0);
        operationMemberInfo.setMemberGrowthValue(0);
        operationMemberInfo.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag1 = new HsaGradeGiftBag();
        hsaGradeGiftBag1.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag1.setType(0);
        hsaGradeGiftBag1.setValue(0);
        hsaGradeGiftBag1.setIsDelete(0);
        hsaGradeGiftBag1.setEffective(0);
        when(mockGradeChangeAssembler.getHsaMemberGradeRightsRecord(gradeRightsProcessorDTO, operationMemberInfo,
                hsaGradeGiftBag1)).thenReturn(hsaMemberGradeRightsRecord1);

        // Configure GradeChangeAssembler.getHsaGrowthValueDetail(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail1 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail1.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail1.setId(0L);
        hsaGrowthValueDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail1.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail1.setMemberInfoGuid("memberInfoGuid");
        final HsaOperationMemberInfo operationMemberInfo1 = new HsaOperationMemberInfo();
        operationMemberInfo1.setGuid("guid");
        operationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo1.setMemberGradeInfoGuid("");
        operationMemberInfo1.setMemberGradeInfoName("");
        operationMemberInfo1.setOperatorGuid("operatorGuid");
        operationMemberInfo1.setMemberIntegral(0);
        operationMemberInfo1.setMemberGrowthValue(0);
        operationMemberInfo1.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag2 = new HsaGradeGiftBag();
        hsaGradeGiftBag2.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag2.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag2.setType(0);
        hsaGradeGiftBag2.setValue(0);
        hsaGradeGiftBag2.setIsDelete(0);
        hsaGradeGiftBag2.setEffective(0);
        final HsaMemberGradeInfo hsaMemberGradeInfo2 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo2.setGuid("");
        hsaMemberGradeInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo2.setVipGrade(0);
        hsaMemberGradeInfo2.setName("");
        hsaMemberGradeInfo2.setGrowthValue(0);
        hsaMemberGradeInfo2.setIsDelete(0);
        hsaMemberGradeInfo2.setEffective(0);
        hsaMemberGradeInfo2.setRoleType("roleType");
        hsaMemberGradeInfo2.setUpgradeType(0);
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo2 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo2.setId(0L);
        hsaGiftBagBaseInfo2.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo2.setEquitiesType(0);
        hsaGiftBagBaseInfo2.setEquitiesRule(0);
        final HsaMemberGradeInfo afterMemberGradeInfo = new HsaMemberGradeInfo();
        afterMemberGradeInfo.setGuid("");
        afterMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo.setVipGrade(0);
        afterMemberGradeInfo.setName("");
        afterMemberGradeInfo.setGrowthValue(0);
        afterMemberGradeInfo.setIsDelete(0);
        afterMemberGradeInfo.setEffective(0);
        afterMemberGradeInfo.setRoleType("roleType");
        afterMemberGradeInfo.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaGrowthValueDetail(operationMemberInfo1, 0, "operSubjectGuid",
                hsaGradeGiftBag2, hsaMemberGradeInfo2, hsaGiftBagBaseInfo2, afterMemberGradeInfo))
                .thenReturn(hsaGrowthValueDetail1);

        // Configure GradeChangeAssembler.getHsaIntegralValueDetail(...).
        final HsaIntegralDetail hsaIntegralDetail1 = new HsaIntegralDetail();
        hsaIntegralDetail1.setId(0L);
        hsaIntegralDetail1.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail1.setIntegralTaskGuid("integralTaskGuid");
        final HsaOperationMemberInfo operationMemberInfo2 = new HsaOperationMemberInfo();
        operationMemberInfo2.setGuid("guid");
        operationMemberInfo2.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo2.setMemberGradeInfoGuid("");
        operationMemberInfo2.setMemberGradeInfoName("");
        operationMemberInfo2.setOperatorGuid("operatorGuid");
        operationMemberInfo2.setMemberIntegral(0);
        operationMemberInfo2.setMemberGrowthValue(0);
        operationMemberInfo2.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag3 = new HsaGradeGiftBag();
        hsaGradeGiftBag3.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag3.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag3.setType(0);
        hsaGradeGiftBag3.setValue(0);
        hsaGradeGiftBag3.setIsDelete(0);
        hsaGradeGiftBag3.setEffective(0);
        final HsaMemberGradeInfo hsaMemberGradeInfo3 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo3.setGuid("");
        hsaMemberGradeInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo3.setVipGrade(0);
        hsaMemberGradeInfo3.setName("");
        hsaMemberGradeInfo3.setGrowthValue(0);
        hsaMemberGradeInfo3.setIsDelete(0);
        hsaMemberGradeInfo3.setEffective(0);
        hsaMemberGradeInfo3.setRoleType("roleType");
        hsaMemberGradeInfo3.setUpgradeType(0);
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo3 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo3.setId(0L);
        hsaGiftBagBaseInfo3.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo3.setEquitiesType(0);
        hsaGiftBagBaseInfo3.setEquitiesRule(0);
        final HsaMemberGradeInfo afterMemberGradeInfo1 = new HsaMemberGradeInfo();
        afterMemberGradeInfo1.setGuid("");
        afterMemberGradeInfo1.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo1.setVipGrade(0);
        afterMemberGradeInfo1.setName("");
        afterMemberGradeInfo1.setGrowthValue(0);
        afterMemberGradeInfo1.setIsDelete(0);
        afterMemberGradeInfo1.setEffective(0);
        afterMemberGradeInfo1.setRoleType("roleType");
        afterMemberGradeInfo1.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaIntegralValueDetail(operationMemberInfo2, 0, "operSubjectGuid",
                hsaGradeGiftBag3, hsaMemberGradeInfo3, hsaGiftBagBaseInfo3, afterMemberGradeInfo1))
                .thenReturn(hsaIntegralDetail1);

        // Configure GradeChangeAssembler.getMemberGradeRightsRecord(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord3 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord3.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord3.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord3.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord3.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord3.setRightsType(0);
        final HsaOperationMemberInfo operationMemberInfo3 = new HsaOperationMemberInfo();
        operationMemberInfo3.setGuid("guid");
        operationMemberInfo3.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo3.setMemberGradeInfoGuid("");
        operationMemberInfo3.setMemberGradeInfoName("");
        operationMemberInfo3.setOperatorGuid("operatorGuid");
        operationMemberInfo3.setMemberIntegral(0);
        operationMemberInfo3.setMemberGrowthValue(0);
        operationMemberInfo3.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockGradeChangeAssembler.getMemberGradeRightsRecord(operationMemberInfo3, "operSubjectGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build())).thenReturn(hsaMemberGradeRightsRecord3);

        // Configure HsaMemberEquitiesReceiveRecordMapper.selectAllGrowthValue(...).
        final MemberGrowthValueRelationVO memberGrowthValueRelationVO = new MemberGrowthValueRelationVO();
        memberGrowthValueRelationVO.setMemberGuid("memberGuid");
        memberGrowthValueRelationVO.setGrowthValue(0);
        final List<MemberGrowthValueRelationVO> memberGrowthValueRelationVOS = Arrays.asList(
                memberGrowthValueRelationVO);
        when(mockHsaMemberEquitiesReceiveRecordMapper.selectAllGrowthValue("guid", 0))
                .thenReturn(memberGrowthValueRelationVOS);

        // Configure GradeChangeAssembler.getHsaIntegralValueDetail(...).
        final HsaIntegralDetail hsaIntegralDetail2 = new HsaIntegralDetail();
        hsaIntegralDetail2.setId(0L);
        hsaIntegralDetail2.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail2.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail2.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail2.setIntegralTaskGuid("integralTaskGuid");
        final HsaMemberGradeInfo hsaMemberGradeInfo4 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo4.setGuid("");
        hsaMemberGradeInfo4.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo4.setVipGrade(0);
        hsaMemberGradeInfo4.setName("");
        hsaMemberGradeInfo4.setGrowthValue(0);
        hsaMemberGradeInfo4.setIsDelete(0);
        hsaMemberGradeInfo4.setEffective(0);
        hsaMemberGradeInfo4.setRoleType("roleType");
        hsaMemberGradeInfo4.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaIntegralValueDetail("guid", 0, "operSubjectGuid", hsaMemberGradeInfo4,
                HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build(), 0, 0)).thenReturn(hsaIntegralDetail2);

        // Configure GradeChangeAssembler.getHsaGrowthValueDetail(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail2 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail2.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail2.setId(0L);
        hsaGrowthValueDetail2.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail2.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail2.setMemberInfoGuid("memberInfoGuid");
        final HsaMemberGradeInfo hsaMemberGradeInfo5 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo5.setGuid("");
        hsaMemberGradeInfo5.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo5.setVipGrade(0);
        hsaMemberGradeInfo5.setName("");
        hsaMemberGradeInfo5.setGrowthValue(0);
        hsaMemberGradeInfo5.setIsDelete(0);
        hsaMemberGradeInfo5.setEffective(0);
        hsaMemberGradeInfo5.setRoleType("roleType");
        hsaMemberGradeInfo5.setUpgradeType(0);
        final MemberGradeChangeDTO gradeChangeDTO1 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("guid");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo1.setMemberGradeInfoName("");
        hsaOperationMemberInfo1.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo1.setMemberIntegral(0);
        hsaOperationMemberInfo1.setMemberGrowthValue(0);
        hsaOperationMemberInfo1.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO1.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo1));
        gradeChangeDTO1.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO1.setSourceType(0);
        gradeChangeDTO1.setIsRefresh(0);
        gradeChangeDTO1.setRoleType("roleType");
        gradeChangeDTO1.setSystem(0);
        gradeChangeDTO1.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getHsaGrowthValueDetail("guid", 0, hsaMemberGradeInfo5,
                HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build(), 0, 0, gradeChangeDTO1)).thenReturn(hsaGrowthValueDetail2);

        // Configure GradeChangeAssembler.getHsaMemberEquitiesReceiveRecord(...).
        final HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord = new HsaMemberEquitiesReceiveRecord();
        hsaMemberEquitiesReceiveRecord.setGuid("a782bb73-1961-4997-8c42-d00d348d6623");
        hsaMemberEquitiesReceiveRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberEquitiesReceiveRecord.setMemberGradeGuid("memberGradeGuid");
        hsaMemberEquitiesReceiveRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberEquitiesReceiveRecord.setGradeEquitiesGuid("gradeEquitiesGuid");
        when(mockGradeChangeAssembler.getHsaMemberEquitiesReceiveRecord("operatorGuid", "guid", "", "guid", 0,
                0)).thenReturn(hsaMemberEquitiesReceiveRecord);

        // Configure GradeChangeAssembler.getMemberGradeRelationDTO(...).
        final MemberGradeRelationDTO memberGradeRelationDTO = new MemberGradeRelationDTO();
        memberGradeRelationDTO.setMemberGuidList(Arrays.asList("value"));
        memberGradeRelationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberGradeRelationDTO.setRoleType("roleType");
        memberGradeRelationDTO.setNum(0);
        memberGradeRelationDTO.setUnit(0);
        final MemberGradeChangeDTO gradeChangeDTO2 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo2 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo2.setGuid("guid");
        hsaOperationMemberInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo2.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo2.setMemberGradeInfoName("");
        hsaOperationMemberInfo2.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo2.setMemberIntegral(0);
        hsaOperationMemberInfo2.setMemberGrowthValue(0);
        hsaOperationMemberInfo2.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO2.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo2));
        gradeChangeDTO2.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO2.setSourceType(0);
        gradeChangeDTO2.setIsRefresh(0);
        gradeChangeDTO2.setRoleType("roleType");
        gradeChangeDTO2.setSystem(0);
        gradeChangeDTO2.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getMemberGradeRelationDTO(gradeChangeDTO2, Arrays.asList("value")))
                .thenReturn(memberGradeRelationDTO);

        // Configure GradeChangeAssembler.getMemberGradePayRecordReqVO(...).
        final MemberGradePayRecordReqVO memberGradePayRecordReqVO = new MemberGradePayRecordReqVO();
        memberGradePayRecordReqVO.setMemberGuidList(Arrays.asList("value"));
        memberGradePayRecordReqVO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberGradePayRecordReqVO.setRoleType("roleType");
        memberGradePayRecordReqVO.setNum(0);
        memberGradePayRecordReqVO.setUnit(0);
        final MemberGradeChangeDTO gradeChangeDTO3 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo3 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo3.setGuid("guid");
        hsaOperationMemberInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo3.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo3.setMemberGradeInfoName("");
        hsaOperationMemberInfo3.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo3.setMemberIntegral(0);
        hsaOperationMemberInfo3.setMemberGrowthValue(0);
        hsaOperationMemberInfo3.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO3.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo3));
        gradeChangeDTO3.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO3.setSourceType(0);
        gradeChangeDTO3.setIsRefresh(0);
        gradeChangeDTO3.setRoleType("roleType");
        gradeChangeDTO3.setSystem(0);
        gradeChangeDTO3.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getMemberGradePayRecordReqVO(gradeChangeDTO3, Arrays.asList("value")))
                .thenReturn(memberGradePayRecordReqVO);

        when(mockCacheService.cleanToken("cacheKey")).thenReturn(false);

        // Run the test
        hsaMemberGradeChangeDetailServiceImplUnderTest.memberGradeChange(gradeChangeDTO);

        // Verify the results
        // Confirm GradeChangeAssembler.getMemberGradeChangeDetail(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo4 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo4.setGuid("guid");
        hsaOperationMemberInfo4.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo4.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo4.setMemberGradeInfoName("");
        hsaOperationMemberInfo4.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo4.setMemberIntegral(0);
        hsaOperationMemberInfo4.setMemberGrowthValue(0);
        hsaOperationMemberInfo4.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaMemberGradeInfo afterMemberGradeInfo2 = new HsaMemberGradeInfo();
        afterMemberGradeInfo2.setGuid("");
        afterMemberGradeInfo2.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo2.setVipGrade(0);
        afterMemberGradeInfo2.setName("");
        afterMemberGradeInfo2.setGrowthValue(0);
        afterMemberGradeInfo2.setIsDelete(0);
        afterMemberGradeInfo2.setEffective(0);
        afterMemberGradeInfo2.setRoleType("roleType");
        afterMemberGradeInfo2.setUpgradeType(0);
        final HsaMemberGradeInfo beforeMemberGradeInfo = new HsaMemberGradeInfo();
        beforeMemberGradeInfo.setGuid("");
        beforeMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        beforeMemberGradeInfo.setVipGrade(0);
        beforeMemberGradeInfo.setName("");
        beforeMemberGradeInfo.setGrowthValue(0);
        beforeMemberGradeInfo.setIsDelete(0);
        beforeMemberGradeInfo.setEffective(0);
        beforeMemberGradeInfo.setRoleType("roleType");
        beforeMemberGradeInfo.setUpgradeType(0);
        final HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail = new HsaMemberGradeChangeDetail();
        hsaMemberGradeChangeDetail.setGuid("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");
        hsaMemberGradeChangeDetail.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeChangeDetail.setMemberInfoGuid("guid");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeName("");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeGuid("");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail.setAfterChangeGradeName("");
        hsaMemberGradeChangeDetail.setAfterChangeGradeGuid("");
        hsaMemberGradeChangeDetail.setAfterChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail.setGradeChangeType(0);
        hsaMemberGradeChangeDetail.setCurrentGrowthValue(0);
        hsaMemberGradeChangeDetail.setIsShowApplet(0);
        hsaMemberGradeChangeDetail.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail.setAfterChangeGradeIcon("");
        hsaMemberGradeChangeDetail.setAfterChangeBackgroundColor("");
        hsaMemberGradeChangeDetail.setAfterChangeGrowthValue(0);
        verify(mockGradeChangeAssembler).getMemberGradeChangeDetail(hsaOperationMemberInfo4, afterMemberGradeInfo2,
                beforeMemberGradeInfo, hsaMemberGradeChangeDetail, LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Confirm HsaMemberGradeChangeDetailService.saveBatch(...).
        final HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail1 = new HsaMemberGradeChangeDetail();
        hsaMemberGradeChangeDetail1.setGuid("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");
        hsaMemberGradeChangeDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeChangeDetail1.setMemberInfoGuid("guid");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeName("");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeGuid("");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail1.setAfterChangeGradeName("");
        hsaMemberGradeChangeDetail1.setAfterChangeGradeGuid("");
        hsaMemberGradeChangeDetail1.setAfterChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail1.setGradeChangeType(0);
        hsaMemberGradeChangeDetail1.setCurrentGrowthValue(0);
        hsaMemberGradeChangeDetail1.setIsShowApplet(0);
        hsaMemberGradeChangeDetail1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail1.setAfterChangeGradeIcon("");
        hsaMemberGradeChangeDetail1.setAfterChangeBackgroundColor("");
        hsaMemberGradeChangeDetail1.setAfterChangeGrowthValue(0);
        final List<HsaMemberGradeChangeDetail> collection = Arrays.asList(hsaMemberGradeChangeDetail1);
        verify(mockHsaMemberGradeChangeDetailService).saveBatch(collection, 1000);

        // Confirm HsaMemberGradeRightsRecordService.saveBatch(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord4 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord4.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord4.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord4.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord4.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord4.setRightsType(0);
        final List<HsaMemberGradeRightsRecord> collection1 = Arrays.asList(hsaMemberGradeRightsRecord4);
        verify(mockHsaMemberGradeRightsRecordService).saveBatch(collection1, 1000);

        // Confirm HsaGrowthValueDetailService.saveBatch(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail3 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail3.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail3.setId(0L);
        hsaGrowthValueDetail3.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail3.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail3.setMemberInfoGuid("memberInfoGuid");
        final List<HsaGrowthValueDetail> collection2 = Arrays.asList(hsaGrowthValueDetail3);
        verify(mockHsaGrowthValueDetailService).saveBatch(collection2, 1000);

        // Confirm HsaIntegralDetailService.saveBatch(...).
        final HsaIntegralDetail hsaIntegralDetail3 = new HsaIntegralDetail();
        hsaIntegralDetail3.setId(0L);
        hsaIntegralDetail3.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail3.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail3.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail3.setIntegralTaskGuid("integralTaskGuid");
        final List<HsaIntegralDetail> collection3 = Arrays.asList(hsaIntegralDetail3);
        verify(mockHsaIntegralDetailService).saveBatch(collection3, 1000);

        // Confirm HsaMemberEquitiesReceiveRecordServiceImpl.saveBatch(...).
        final HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord1 = new HsaMemberEquitiesReceiveRecord();
        hsaMemberEquitiesReceiveRecord1.setGuid("a782bb73-1961-4997-8c42-d00d348d6623");
        hsaMemberEquitiesReceiveRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberEquitiesReceiveRecord1.setMemberGradeGuid("memberGradeGuid");
        hsaMemberEquitiesReceiveRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberEquitiesReceiveRecord1.setGradeEquitiesGuid("gradeEquitiesGuid");
        final List<HsaMemberEquitiesReceiveRecord> entityList = Arrays.asList(hsaMemberEquitiesReceiveRecord1);
        verify(mockHsaMemberEquitiesReceiveRecordService).saveBatch(entityList, 1000);

        // Confirm HsaOperationMemberInfoMapper.batchUpdateGrowth(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo5 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo5.setGuid("guid");
        hsaOperationMemberInfo5.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo5.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo5.setMemberGradeInfoName("");
        hsaOperationMemberInfo5.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo5.setMemberIntegral(0);
        hsaOperationMemberInfo5.setMemberGrowthValue(0);
        hsaOperationMemberInfo5.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo5);
        verify(mockHsaOperationMemberInfoMapper).batchUpdateGrowth(hsaOperationMemberInfos);
        verify(mockMemberGradeRelationService).batchDeleteMemberGrade(Arrays.asList("value"), "roleType");

        // Confirm IHsaMemberGradeRelationService.batchSaveAndUpdateRelation(...).
        final MemberGradeRelationDTO relationDTO = new MemberGradeRelationDTO();
        relationDTO.setMemberGuidList(Arrays.asList("value"));
        relationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        relationDTO.setRoleType("roleType");
        relationDTO.setNum(0);
        relationDTO.setUnit(0);
        verify(mockMemberGradeRelationService).batchSaveAndUpdateRelation(relationDTO);

        // Confirm IHsaMemberGradePayRecordService.batchAdd(...).
        final MemberGradePayRecordReqVO gradePayRecordReqVO = new MemberGradePayRecordReqVO();
        gradePayRecordReqVO.setMemberGuidList(Arrays.asList("value"));
        gradePayRecordReqVO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        gradePayRecordReqVO.setRoleType("roleType");
        gradePayRecordReqVO.setNum(0);
        gradePayRecordReqVO.setUnit(0);
        verify(mockMemberGradePayRecordService).batchAdd(gradePayRecordReqVO);

        // Confirm HsaOperationMemberInfoMapper.batchUpdateIntegral(...).
        final UpdateIntegral updateIntegral = new UpdateIntegral();
        updateIntegral.setGuid("32f2621a-4a96-4713-abb6-d3f2e4b4e56f");
        updateIntegral.setIntegralValue(0);
        final List<UpdateIntegral> updateMemberIntegralList = Arrays.asList(updateIntegral);
        verify(mockHsaOperationMemberInfoMapper).batchUpdateIntegral(updateMemberIntegralList);
    }

    @Test
    public void testMemberGradeChange_HsaGradeEquitiesServiceReturnsNoItems() {
        // Setup
        final MemberGradeChangeDTO gradeChangeDTO = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("guid");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo.setMemberGradeInfoName("");
        hsaOperationMemberInfo.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo.setMemberIntegral(0);
        hsaOperationMemberInfo.setMemberGrowthValue(0);
        hsaOperationMemberInfo.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo));
        gradeChangeDTO.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO.setSourceType(0);
        gradeChangeDTO.setIsRefresh(0);
        gradeChangeDTO.setRoleType("roleType");
        gradeChangeDTO.setSystem(0);
        gradeChangeDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");

        // Configure HsaControlledGradeStateMapper.selectOne(...).
        final HsaControlledGradeState hsaControlledGradeState = new HsaControlledGradeState();
        hsaControlledGradeState.setId(0L);
        hsaControlledGradeState.setOperSubjectGuid("operSubjectGuid");
        hsaControlledGradeState.setState(0);
        hsaControlledGradeState.setIsDelete(false);
        hsaControlledGradeState.setRoleType("roleType");
        when(mockHsaControlledGradeStateMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaControlledGradeState);

        // Configure HsaMemberGradeInfoMapper.selectList(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("");
        hsaMemberGradeInfo.setGrowthValue(0);
        hsaMemberGradeInfo.setIsDelete(0);
        hsaMemberGradeInfo.setEffective(0);
        hsaMemberGradeInfo.setRoleType("roleType");
        hsaMemberGradeInfo.setUpgradeType(0);
        final List<HsaMemberGradeInfo> hsaMemberGradeInfos = Arrays.asList(hsaMemberGradeInfo);
        when(mockHsaMemberGradeInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfos);

        // Configure IHsaMemberGradeRelationService.listByMemberGuidList(...).
        final HsaMemberGradeRelation hsaMemberGradeRelation = new HsaMemberGradeRelation();
        hsaMemberGradeRelation.setId(0);
        hsaMemberGradeRelation.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRelation.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRelation.setMemberInfoGradeGuid("memberInfoGradeGuid");
        hsaMemberGradeRelation.setIsEnable(false);
        final List<HsaMemberGradeRelation> hsaMemberGradeRelations = Arrays.asList(hsaMemberGradeRelation);
        when(mockMemberGradeRelationService.listByMemberGuidList(Arrays.asList("value"), "roleType"))
                .thenReturn(hsaMemberGradeRelations);

        when(mockHsaGradeEquitiesService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure HsaMemberGradeRightsRecordMapper.selectList(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord.setRightsType(0);
        final List<HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecords = Arrays.asList(hsaMemberGradeRightsRecord);
        when(mockHsaMemberGradeRightsRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberGradeRightsRecords);

        // Configure HsaGradeEquitiesMapper.selectList(...).
        final List<HsaBusinessEquities> hsaBusinessEquities = Arrays.asList(HsaBusinessEquities.builder()
                .guid("guid")
                .memberGradeInfoGuid("memberGradeInfoGuid")
                .equitiesRuleType(0)
                .totalGiveNumber(0)
                .setPeriod(0)
                .isDelete(0)
                .effective(0)
                .operSubjectGuid("operSubjectGuid")
                .build());
        when(mockHsaGradeEquitiesMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaBusinessEquities);

        // Configure HsaGiftBagBaseInfoMapper.selectOne(...).
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo.setId(0L);
        hsaGiftBagBaseInfo.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo.setEquitiesType(0);
        hsaGiftBagBaseInfo.setEquitiesRule(0);
        when(mockHsaGiftBagBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaGiftBagBaseInfo);

        // Configure HsaMemberGradeInfoMapper.selectOne(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo1 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo1.setGuid("");
        hsaMemberGradeInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo1.setVipGrade(0);
        hsaMemberGradeInfo1.setName("");
        hsaMemberGradeInfo1.setGrowthValue(0);
        hsaMemberGradeInfo1.setIsDelete(0);
        hsaMemberGradeInfo1.setEffective(0);
        hsaMemberGradeInfo1.setRoleType("roleType");
        hsaMemberGradeInfo1.setUpgradeType(0);
        when(mockHsaMemberGradeInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfo1);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");

        // Configure HsaGradeGiftBagMapper.selectList(...).
        final HsaGradeGiftBag hsaGradeGiftBag = new HsaGradeGiftBag();
        hsaGradeGiftBag.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag.setType(0);
        hsaGradeGiftBag.setValue(0);
        hsaGradeGiftBag.setIsDelete(0);
        hsaGradeGiftBag.setEffective(0);
        final List<HsaGradeGiftBag> hsaGradeGiftBags = Arrays.asList(hsaGradeGiftBag);
        when(mockHsaGradeGiftBagMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaGradeGiftBags);

        // Configure GradeChangeAssembler.getHsaMemberGradeRightsRecord(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord1 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord1.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord1.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord1.setRightsType(0);
        final GradeRightsProcessorDTO gradeRightsProcessorDTO = new GradeRightsProcessorDTO();
        gradeRightsProcessorDTO.setUpdateMemberIntegralMap(new HashMap<>());
        gradeRightsProcessorDTO.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo1 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo1.setEquitiesRule(0);
        gradeRightsProcessorDTO.setHsaGiftBagBaseInfo(hsaGiftBagBaseInfo1);
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord2 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord2.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord2.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord2.setRightsType(0);
        gradeRightsProcessorDTO.setHsaMemberGradeRightsRecords(Arrays.asList(hsaMemberGradeRightsRecord2));
        final HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        gradeRightsProcessorDTO.setHsaGrowthValueDetails(Arrays.asList(hsaGrowthValueDetail));
        final HsaIntegralDetail hsaIntegralDetail = new HsaIntegralDetail();
        gradeRightsProcessorDTO.setHsaIntegralDetails(Arrays.asList(hsaIntegralDetail));
        gradeRightsProcessorDTO.setSourceType(0);
        gradeRightsProcessorDTO.setOperSubjectGuid("operSubjectGuid");
        final HsaOperationMemberInfo operationMemberInfo = new HsaOperationMemberInfo();
        operationMemberInfo.setGuid("guid");
        operationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo.setMemberGradeInfoGuid("");
        operationMemberInfo.setMemberGradeInfoName("");
        operationMemberInfo.setOperatorGuid("operatorGuid");
        operationMemberInfo.setMemberIntegral(0);
        operationMemberInfo.setMemberGrowthValue(0);
        operationMemberInfo.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag1 = new HsaGradeGiftBag();
        hsaGradeGiftBag1.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag1.setType(0);
        hsaGradeGiftBag1.setValue(0);
        hsaGradeGiftBag1.setIsDelete(0);
        hsaGradeGiftBag1.setEffective(0);
        when(mockGradeChangeAssembler.getHsaMemberGradeRightsRecord(gradeRightsProcessorDTO, operationMemberInfo,
                hsaGradeGiftBag1)).thenReturn(hsaMemberGradeRightsRecord1);

        // Configure GradeChangeAssembler.getHsaGrowthValueDetail(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail1 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail1.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail1.setId(0L);
        hsaGrowthValueDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail1.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail1.setMemberInfoGuid("memberInfoGuid");
        final HsaOperationMemberInfo operationMemberInfo1 = new HsaOperationMemberInfo();
        operationMemberInfo1.setGuid("guid");
        operationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo1.setMemberGradeInfoGuid("");
        operationMemberInfo1.setMemberGradeInfoName("");
        operationMemberInfo1.setOperatorGuid("operatorGuid");
        operationMemberInfo1.setMemberIntegral(0);
        operationMemberInfo1.setMemberGrowthValue(0);
        operationMemberInfo1.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag2 = new HsaGradeGiftBag();
        hsaGradeGiftBag2.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag2.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag2.setType(0);
        hsaGradeGiftBag2.setValue(0);
        hsaGradeGiftBag2.setIsDelete(0);
        hsaGradeGiftBag2.setEffective(0);
        final HsaMemberGradeInfo hsaMemberGradeInfo2 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo2.setGuid("");
        hsaMemberGradeInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo2.setVipGrade(0);
        hsaMemberGradeInfo2.setName("");
        hsaMemberGradeInfo2.setGrowthValue(0);
        hsaMemberGradeInfo2.setIsDelete(0);
        hsaMemberGradeInfo2.setEffective(0);
        hsaMemberGradeInfo2.setRoleType("roleType");
        hsaMemberGradeInfo2.setUpgradeType(0);
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo2 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo2.setId(0L);
        hsaGiftBagBaseInfo2.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo2.setEquitiesType(0);
        hsaGiftBagBaseInfo2.setEquitiesRule(0);
        final HsaMemberGradeInfo afterMemberGradeInfo = new HsaMemberGradeInfo();
        afterMemberGradeInfo.setGuid("");
        afterMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo.setVipGrade(0);
        afterMemberGradeInfo.setName("");
        afterMemberGradeInfo.setGrowthValue(0);
        afterMemberGradeInfo.setIsDelete(0);
        afterMemberGradeInfo.setEffective(0);
        afterMemberGradeInfo.setRoleType("roleType");
        afterMemberGradeInfo.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaGrowthValueDetail(operationMemberInfo1, 0, "operSubjectGuid",
                hsaGradeGiftBag2, hsaMemberGradeInfo2, hsaGiftBagBaseInfo2, afterMemberGradeInfo))
                .thenReturn(hsaGrowthValueDetail1);

        // Configure GradeChangeAssembler.getHsaIntegralValueDetail(...).
        final HsaIntegralDetail hsaIntegralDetail1 = new HsaIntegralDetail();
        hsaIntegralDetail1.setId(0L);
        hsaIntegralDetail1.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail1.setIntegralTaskGuid("integralTaskGuid");
        final HsaOperationMemberInfo operationMemberInfo2 = new HsaOperationMemberInfo();
        operationMemberInfo2.setGuid("guid");
        operationMemberInfo2.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo2.setMemberGradeInfoGuid("");
        operationMemberInfo2.setMemberGradeInfoName("");
        operationMemberInfo2.setOperatorGuid("operatorGuid");
        operationMemberInfo2.setMemberIntegral(0);
        operationMemberInfo2.setMemberGrowthValue(0);
        operationMemberInfo2.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag3 = new HsaGradeGiftBag();
        hsaGradeGiftBag3.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag3.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag3.setType(0);
        hsaGradeGiftBag3.setValue(0);
        hsaGradeGiftBag3.setIsDelete(0);
        hsaGradeGiftBag3.setEffective(0);
        final HsaMemberGradeInfo hsaMemberGradeInfo3 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo3.setGuid("");
        hsaMemberGradeInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo3.setVipGrade(0);
        hsaMemberGradeInfo3.setName("");
        hsaMemberGradeInfo3.setGrowthValue(0);
        hsaMemberGradeInfo3.setIsDelete(0);
        hsaMemberGradeInfo3.setEffective(0);
        hsaMemberGradeInfo3.setRoleType("roleType");
        hsaMemberGradeInfo3.setUpgradeType(0);
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo3 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo3.setId(0L);
        hsaGiftBagBaseInfo3.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo3.setEquitiesType(0);
        hsaGiftBagBaseInfo3.setEquitiesRule(0);
        final HsaMemberGradeInfo afterMemberGradeInfo1 = new HsaMemberGradeInfo();
        afterMemberGradeInfo1.setGuid("");
        afterMemberGradeInfo1.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo1.setVipGrade(0);
        afterMemberGradeInfo1.setName("");
        afterMemberGradeInfo1.setGrowthValue(0);
        afterMemberGradeInfo1.setIsDelete(0);
        afterMemberGradeInfo1.setEffective(0);
        afterMemberGradeInfo1.setRoleType("roleType");
        afterMemberGradeInfo1.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaIntegralValueDetail(operationMemberInfo2, 0, "operSubjectGuid",
                hsaGradeGiftBag3, hsaMemberGradeInfo3, hsaGiftBagBaseInfo3, afterMemberGradeInfo1))
                .thenReturn(hsaIntegralDetail1);

        // Configure GradeChangeAssembler.getMemberGradeRightsRecord(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord3 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord3.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord3.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord3.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord3.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord3.setRightsType(0);
        final HsaOperationMemberInfo operationMemberInfo3 = new HsaOperationMemberInfo();
        operationMemberInfo3.setGuid("guid");
        operationMemberInfo3.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo3.setMemberGradeInfoGuid("");
        operationMemberInfo3.setMemberGradeInfoName("");
        operationMemberInfo3.setOperatorGuid("operatorGuid");
        operationMemberInfo3.setMemberIntegral(0);
        operationMemberInfo3.setMemberGrowthValue(0);
        operationMemberInfo3.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockGradeChangeAssembler.getMemberGradeRightsRecord(operationMemberInfo3, "operSubjectGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build())).thenReturn(hsaMemberGradeRightsRecord3);

        // Configure HsaMemberEquitiesReceiveRecordMapper.selectAllGrowthValue(...).
        final MemberGrowthValueRelationVO memberGrowthValueRelationVO = new MemberGrowthValueRelationVO();
        memberGrowthValueRelationVO.setMemberGuid("memberGuid");
        memberGrowthValueRelationVO.setGrowthValue(0);
        final List<MemberGrowthValueRelationVO> memberGrowthValueRelationVOS = Arrays.asList(
                memberGrowthValueRelationVO);
        when(mockHsaMemberEquitiesReceiveRecordMapper.selectAllGrowthValue("guid", 0))
                .thenReturn(memberGrowthValueRelationVOS);

        // Configure GradeChangeAssembler.getHsaIntegralValueDetail(...).
        final HsaIntegralDetail hsaIntegralDetail2 = new HsaIntegralDetail();
        hsaIntegralDetail2.setId(0L);
        hsaIntegralDetail2.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail2.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail2.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail2.setIntegralTaskGuid("integralTaskGuid");
        final HsaMemberGradeInfo hsaMemberGradeInfo4 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo4.setGuid("");
        hsaMemberGradeInfo4.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo4.setVipGrade(0);
        hsaMemberGradeInfo4.setName("");
        hsaMemberGradeInfo4.setGrowthValue(0);
        hsaMemberGradeInfo4.setIsDelete(0);
        hsaMemberGradeInfo4.setEffective(0);
        hsaMemberGradeInfo4.setRoleType("roleType");
        hsaMemberGradeInfo4.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaIntegralValueDetail("guid", 0, "operSubjectGuid", hsaMemberGradeInfo4,
                HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build(), 0, 0)).thenReturn(hsaIntegralDetail2);

        // Configure GradeChangeAssembler.getHsaGrowthValueDetail(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail2 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail2.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail2.setId(0L);
        hsaGrowthValueDetail2.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail2.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail2.setMemberInfoGuid("memberInfoGuid");
        final HsaMemberGradeInfo hsaMemberGradeInfo5 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo5.setGuid("");
        hsaMemberGradeInfo5.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo5.setVipGrade(0);
        hsaMemberGradeInfo5.setName("");
        hsaMemberGradeInfo5.setGrowthValue(0);
        hsaMemberGradeInfo5.setIsDelete(0);
        hsaMemberGradeInfo5.setEffective(0);
        hsaMemberGradeInfo5.setRoleType("roleType");
        hsaMemberGradeInfo5.setUpgradeType(0);
        final MemberGradeChangeDTO gradeChangeDTO1 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("guid");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo1.setMemberGradeInfoName("");
        hsaOperationMemberInfo1.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo1.setMemberIntegral(0);
        hsaOperationMemberInfo1.setMemberGrowthValue(0);
        hsaOperationMemberInfo1.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO1.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo1));
        gradeChangeDTO1.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO1.setSourceType(0);
        gradeChangeDTO1.setIsRefresh(0);
        gradeChangeDTO1.setRoleType("roleType");
        gradeChangeDTO1.setSystem(0);
        gradeChangeDTO1.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getHsaGrowthValueDetail("guid", 0, hsaMemberGradeInfo5,
                HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build(), 0, 0, gradeChangeDTO1)).thenReturn(hsaGrowthValueDetail2);

        // Configure GradeChangeAssembler.getHsaMemberEquitiesReceiveRecord(...).
        final HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord = new HsaMemberEquitiesReceiveRecord();
        hsaMemberEquitiesReceiveRecord.setGuid("a782bb73-1961-4997-8c42-d00d348d6623");
        hsaMemberEquitiesReceiveRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberEquitiesReceiveRecord.setMemberGradeGuid("memberGradeGuid");
        hsaMemberEquitiesReceiveRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberEquitiesReceiveRecord.setGradeEquitiesGuid("gradeEquitiesGuid");
        when(mockGradeChangeAssembler.getHsaMemberEquitiesReceiveRecord("operatorGuid", "guid", "", "guid", 0,
                0)).thenReturn(hsaMemberEquitiesReceiveRecord);

        // Configure GradeChangeAssembler.getMemberGradeRelationDTO(...).
        final MemberGradeRelationDTO memberGradeRelationDTO = new MemberGradeRelationDTO();
        memberGradeRelationDTO.setMemberGuidList(Arrays.asList("value"));
        memberGradeRelationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberGradeRelationDTO.setRoleType("roleType");
        memberGradeRelationDTO.setNum(0);
        memberGradeRelationDTO.setUnit(0);
        final MemberGradeChangeDTO gradeChangeDTO2 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo2 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo2.setGuid("guid");
        hsaOperationMemberInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo2.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo2.setMemberGradeInfoName("");
        hsaOperationMemberInfo2.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo2.setMemberIntegral(0);
        hsaOperationMemberInfo2.setMemberGrowthValue(0);
        hsaOperationMemberInfo2.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO2.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo2));
        gradeChangeDTO2.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO2.setSourceType(0);
        gradeChangeDTO2.setIsRefresh(0);
        gradeChangeDTO2.setRoleType("roleType");
        gradeChangeDTO2.setSystem(0);
        gradeChangeDTO2.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getMemberGradeRelationDTO(gradeChangeDTO2, Arrays.asList("value")))
                .thenReturn(memberGradeRelationDTO);

        // Configure GradeChangeAssembler.getMemberGradePayRecordReqVO(...).
        final MemberGradePayRecordReqVO memberGradePayRecordReqVO = new MemberGradePayRecordReqVO();
        memberGradePayRecordReqVO.setMemberGuidList(Arrays.asList("value"));
        memberGradePayRecordReqVO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberGradePayRecordReqVO.setRoleType("roleType");
        memberGradePayRecordReqVO.setNum(0);
        memberGradePayRecordReqVO.setUnit(0);
        final MemberGradeChangeDTO gradeChangeDTO3 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo3 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo3.setGuid("guid");
        hsaOperationMemberInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo3.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo3.setMemberGradeInfoName("");
        hsaOperationMemberInfo3.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo3.setMemberIntegral(0);
        hsaOperationMemberInfo3.setMemberGrowthValue(0);
        hsaOperationMemberInfo3.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO3.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo3));
        gradeChangeDTO3.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO3.setSourceType(0);
        gradeChangeDTO3.setIsRefresh(0);
        gradeChangeDTO3.setRoleType("roleType");
        gradeChangeDTO3.setSystem(0);
        gradeChangeDTO3.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getMemberGradePayRecordReqVO(gradeChangeDTO3, Arrays.asList("value")))
                .thenReturn(memberGradePayRecordReqVO);

        when(mockCacheService.cleanToken("cacheKey")).thenReturn(false);

        // Run the test
        hsaMemberGradeChangeDetailServiceImplUnderTest.memberGradeChange(gradeChangeDTO);

        // Verify the results
        // Confirm GradeChangeAssembler.getMemberGradeChangeDetail(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo4 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo4.setGuid("guid");
        hsaOperationMemberInfo4.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo4.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo4.setMemberGradeInfoName("");
        hsaOperationMemberInfo4.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo4.setMemberIntegral(0);
        hsaOperationMemberInfo4.setMemberGrowthValue(0);
        hsaOperationMemberInfo4.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaMemberGradeInfo afterMemberGradeInfo2 = new HsaMemberGradeInfo();
        afterMemberGradeInfo2.setGuid("");
        afterMemberGradeInfo2.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo2.setVipGrade(0);
        afterMemberGradeInfo2.setName("");
        afterMemberGradeInfo2.setGrowthValue(0);
        afterMemberGradeInfo2.setIsDelete(0);
        afterMemberGradeInfo2.setEffective(0);
        afterMemberGradeInfo2.setRoleType("roleType");
        afterMemberGradeInfo2.setUpgradeType(0);
        final HsaMemberGradeInfo beforeMemberGradeInfo = new HsaMemberGradeInfo();
        beforeMemberGradeInfo.setGuid("");
        beforeMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        beforeMemberGradeInfo.setVipGrade(0);
        beforeMemberGradeInfo.setName("");
        beforeMemberGradeInfo.setGrowthValue(0);
        beforeMemberGradeInfo.setIsDelete(0);
        beforeMemberGradeInfo.setEffective(0);
        beforeMemberGradeInfo.setRoleType("roleType");
        beforeMemberGradeInfo.setUpgradeType(0);
        final HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail = new HsaMemberGradeChangeDetail();
        hsaMemberGradeChangeDetail.setGuid("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");
        hsaMemberGradeChangeDetail.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeChangeDetail.setMemberInfoGuid("guid");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeName("");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeGuid("");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail.setAfterChangeGradeName("");
        hsaMemberGradeChangeDetail.setAfterChangeGradeGuid("");
        hsaMemberGradeChangeDetail.setAfterChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail.setGradeChangeType(0);
        hsaMemberGradeChangeDetail.setCurrentGrowthValue(0);
        hsaMemberGradeChangeDetail.setIsShowApplet(0);
        hsaMemberGradeChangeDetail.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail.setAfterChangeGradeIcon("");
        hsaMemberGradeChangeDetail.setAfterChangeBackgroundColor("");
        hsaMemberGradeChangeDetail.setAfterChangeGrowthValue(0);
        verify(mockGradeChangeAssembler).getMemberGradeChangeDetail(hsaOperationMemberInfo4, afterMemberGradeInfo2,
                beforeMemberGradeInfo, hsaMemberGradeChangeDetail, LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Confirm HsaMemberGradeChangeDetailService.saveBatch(...).
        final HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail1 = new HsaMemberGradeChangeDetail();
        hsaMemberGradeChangeDetail1.setGuid("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");
        hsaMemberGradeChangeDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeChangeDetail1.setMemberInfoGuid("guid");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeName("");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeGuid("");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail1.setAfterChangeGradeName("");
        hsaMemberGradeChangeDetail1.setAfterChangeGradeGuid("");
        hsaMemberGradeChangeDetail1.setAfterChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail1.setGradeChangeType(0);
        hsaMemberGradeChangeDetail1.setCurrentGrowthValue(0);
        hsaMemberGradeChangeDetail1.setIsShowApplet(0);
        hsaMemberGradeChangeDetail1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail1.setAfterChangeGradeIcon("");
        hsaMemberGradeChangeDetail1.setAfterChangeBackgroundColor("");
        hsaMemberGradeChangeDetail1.setAfterChangeGrowthValue(0);
        final List<HsaMemberGradeChangeDetail> collection = Arrays.asList(hsaMemberGradeChangeDetail1);
        verify(mockHsaMemberGradeChangeDetailService).saveBatch(collection, 1000);

        // Confirm HsaMemberGradeRightsRecordService.saveBatch(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord4 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord4.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord4.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord4.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord4.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord4.setRightsType(0);
        final List<HsaMemberGradeRightsRecord> collection1 = Arrays.asList(hsaMemberGradeRightsRecord4);
        verify(mockHsaMemberGradeRightsRecordService).saveBatch(collection1, 1000);

        // Confirm HsaGrowthValueDetailService.saveBatch(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail3 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail3.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail3.setId(0L);
        hsaGrowthValueDetail3.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail3.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail3.setMemberInfoGuid("memberInfoGuid");
        final List<HsaGrowthValueDetail> collection2 = Arrays.asList(hsaGrowthValueDetail3);
        verify(mockHsaGrowthValueDetailService).saveBatch(collection2, 1000);

        // Confirm HsaIntegralDetailService.saveBatch(...).
        final HsaIntegralDetail hsaIntegralDetail3 = new HsaIntegralDetail();
        hsaIntegralDetail3.setId(0L);
        hsaIntegralDetail3.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail3.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail3.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail3.setIntegralTaskGuid("integralTaskGuid");
        final List<HsaIntegralDetail> collection3 = Arrays.asList(hsaIntegralDetail3);
        verify(mockHsaIntegralDetailService).saveBatch(collection3, 1000);

        // Confirm HsaMemberEquitiesReceiveRecordServiceImpl.saveBatch(...).
        final HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord1 = new HsaMemberEquitiesReceiveRecord();
        hsaMemberEquitiesReceiveRecord1.setGuid("a782bb73-1961-4997-8c42-d00d348d6623");
        hsaMemberEquitiesReceiveRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberEquitiesReceiveRecord1.setMemberGradeGuid("memberGradeGuid");
        hsaMemberEquitiesReceiveRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberEquitiesReceiveRecord1.setGradeEquitiesGuid("gradeEquitiesGuid");
        final List<HsaMemberEquitiesReceiveRecord> entityList = Arrays.asList(hsaMemberEquitiesReceiveRecord1);
        verify(mockHsaMemberEquitiesReceiveRecordService).saveBatch(entityList, 1000);

        // Confirm HsaOperationMemberInfoMapper.batchUpdateGrowth(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo5 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo5.setGuid("guid");
        hsaOperationMemberInfo5.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo5.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo5.setMemberGradeInfoName("");
        hsaOperationMemberInfo5.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo5.setMemberIntegral(0);
        hsaOperationMemberInfo5.setMemberGrowthValue(0);
        hsaOperationMemberInfo5.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo5);
        verify(mockHsaOperationMemberInfoMapper).batchUpdateGrowth(hsaOperationMemberInfos);
        verify(mockMemberGradeRelationService).batchDeleteMemberGrade(Arrays.asList("value"), "roleType");

        // Confirm IHsaMemberGradeRelationService.batchSaveAndUpdateRelation(...).
        final MemberGradeRelationDTO relationDTO = new MemberGradeRelationDTO();
        relationDTO.setMemberGuidList(Arrays.asList("value"));
        relationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        relationDTO.setRoleType("roleType");
        relationDTO.setNum(0);
        relationDTO.setUnit(0);
        verify(mockMemberGradeRelationService).batchSaveAndUpdateRelation(relationDTO);

        // Confirm IHsaMemberGradePayRecordService.batchAdd(...).
        final MemberGradePayRecordReqVO gradePayRecordReqVO = new MemberGradePayRecordReqVO();
        gradePayRecordReqVO.setMemberGuidList(Arrays.asList("value"));
        gradePayRecordReqVO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        gradePayRecordReqVO.setRoleType("roleType");
        gradePayRecordReqVO.setNum(0);
        gradePayRecordReqVO.setUnit(0);
        verify(mockMemberGradePayRecordService).batchAdd(gradePayRecordReqVO);

        // Confirm HsaOperationMemberInfoMapper.batchUpdateIntegral(...).
        final UpdateIntegral updateIntegral = new UpdateIntegral();
        updateIntegral.setGuid("32f2621a-4a96-4713-abb6-d3f2e4b4e56f");
        updateIntegral.setIntegralValue(0);
        final List<UpdateIntegral> updateMemberIntegralList = Arrays.asList(updateIntegral);
        verify(mockHsaOperationMemberInfoMapper).batchUpdateIntegral(updateMemberIntegralList);
    }

    @Test
    public void testMemberGradeChange_HsaMemberGradeRightsRecordMapperReturnsNoItems() {
        // Setup
        final MemberGradeChangeDTO gradeChangeDTO = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("guid");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo.setMemberGradeInfoName("");
        hsaOperationMemberInfo.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo.setMemberIntegral(0);
        hsaOperationMemberInfo.setMemberGrowthValue(0);
        hsaOperationMemberInfo.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo));
        gradeChangeDTO.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO.setSourceType(0);
        gradeChangeDTO.setIsRefresh(0);
        gradeChangeDTO.setRoleType("roleType");
        gradeChangeDTO.setSystem(0);
        gradeChangeDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");

        // Configure HsaControlledGradeStateMapper.selectOne(...).
        final HsaControlledGradeState hsaControlledGradeState = new HsaControlledGradeState();
        hsaControlledGradeState.setId(0L);
        hsaControlledGradeState.setOperSubjectGuid("operSubjectGuid");
        hsaControlledGradeState.setState(0);
        hsaControlledGradeState.setIsDelete(false);
        hsaControlledGradeState.setRoleType("roleType");
        when(mockHsaControlledGradeStateMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaControlledGradeState);

        // Configure HsaMemberGradeInfoMapper.selectList(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("");
        hsaMemberGradeInfo.setGrowthValue(0);
        hsaMemberGradeInfo.setIsDelete(0);
        hsaMemberGradeInfo.setEffective(0);
        hsaMemberGradeInfo.setRoleType("roleType");
        hsaMemberGradeInfo.setUpgradeType(0);
        final List<HsaMemberGradeInfo> hsaMemberGradeInfos = Arrays.asList(hsaMemberGradeInfo);
        when(mockHsaMemberGradeInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfos);

        // Configure IHsaMemberGradeRelationService.listByMemberGuidList(...).
        final HsaMemberGradeRelation hsaMemberGradeRelation = new HsaMemberGradeRelation();
        hsaMemberGradeRelation.setId(0);
        hsaMemberGradeRelation.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRelation.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRelation.setMemberInfoGradeGuid("memberInfoGradeGuid");
        hsaMemberGradeRelation.setIsEnable(false);
        final List<HsaMemberGradeRelation> hsaMemberGradeRelations = Arrays.asList(hsaMemberGradeRelation);
        when(mockMemberGradeRelationService.listByMemberGuidList(Arrays.asList("value"), "roleType"))
                .thenReturn(hsaMemberGradeRelations);

        // Configure HsaGradeEquitiesService.list(...).
        final List<HsaBusinessEquities> hsaBusinessEquities = Arrays.asList(HsaBusinessEquities.builder()
                .guid("guid")
                .memberGradeInfoGuid("memberGradeInfoGuid")
                .equitiesRuleType(0)
                .totalGiveNumber(0)
                .setPeriod(0)
                .isDelete(0)
                .effective(0)
                .operSubjectGuid("operSubjectGuid")
                .build());
        when(mockHsaGradeEquitiesService.list(any(LambdaQueryWrapper.class))).thenReturn(hsaBusinessEquities);

        when(mockHsaMemberGradeRightsRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure HsaGradeEquitiesMapper.selectList(...).
        final List<HsaBusinessEquities> hsaBusinessEquities1 = Arrays.asList(HsaBusinessEquities.builder()
                .guid("guid")
                .memberGradeInfoGuid("memberGradeInfoGuid")
                .equitiesRuleType(0)
                .totalGiveNumber(0)
                .setPeriod(0)
                .isDelete(0)
                .effective(0)
                .operSubjectGuid("operSubjectGuid")
                .build());
        when(mockHsaGradeEquitiesMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaBusinessEquities1);

        // Configure HsaGiftBagBaseInfoMapper.selectOne(...).
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo.setId(0L);
        hsaGiftBagBaseInfo.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo.setEquitiesType(0);
        hsaGiftBagBaseInfo.setEquitiesRule(0);
        when(mockHsaGiftBagBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaGiftBagBaseInfo);

        // Configure HsaMemberGradeInfoMapper.selectOne(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo1 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo1.setGuid("");
        hsaMemberGradeInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo1.setVipGrade(0);
        hsaMemberGradeInfo1.setName("");
        hsaMemberGradeInfo1.setGrowthValue(0);
        hsaMemberGradeInfo1.setIsDelete(0);
        hsaMemberGradeInfo1.setEffective(0);
        hsaMemberGradeInfo1.setRoleType("roleType");
        hsaMemberGradeInfo1.setUpgradeType(0);
        when(mockHsaMemberGradeInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfo1);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");

        // Configure HsaGradeGiftBagMapper.selectList(...).
        final HsaGradeGiftBag hsaGradeGiftBag = new HsaGradeGiftBag();
        hsaGradeGiftBag.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag.setType(0);
        hsaGradeGiftBag.setValue(0);
        hsaGradeGiftBag.setIsDelete(0);
        hsaGradeGiftBag.setEffective(0);
        final List<HsaGradeGiftBag> hsaGradeGiftBags = Arrays.asList(hsaGradeGiftBag);
        when(mockHsaGradeGiftBagMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaGradeGiftBags);

        // Configure GradeChangeAssembler.getHsaMemberGradeRightsRecord(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord.setRightsType(0);
        final GradeRightsProcessorDTO gradeRightsProcessorDTO = new GradeRightsProcessorDTO();
        gradeRightsProcessorDTO.setUpdateMemberIntegralMap(new HashMap<>());
        gradeRightsProcessorDTO.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo1 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo1.setEquitiesRule(0);
        gradeRightsProcessorDTO.setHsaGiftBagBaseInfo(hsaGiftBagBaseInfo1);
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord1 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord1.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord1.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord1.setRightsType(0);
        gradeRightsProcessorDTO.setHsaMemberGradeRightsRecords(Arrays.asList(hsaMemberGradeRightsRecord1));
        final HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        gradeRightsProcessorDTO.setHsaGrowthValueDetails(Arrays.asList(hsaGrowthValueDetail));
        final HsaIntegralDetail hsaIntegralDetail = new HsaIntegralDetail();
        gradeRightsProcessorDTO.setHsaIntegralDetails(Arrays.asList(hsaIntegralDetail));
        gradeRightsProcessorDTO.setSourceType(0);
        gradeRightsProcessorDTO.setOperSubjectGuid("operSubjectGuid");
        final HsaOperationMemberInfo operationMemberInfo = new HsaOperationMemberInfo();
        operationMemberInfo.setGuid("guid");
        operationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo.setMemberGradeInfoGuid("");
        operationMemberInfo.setMemberGradeInfoName("");
        operationMemberInfo.setOperatorGuid("operatorGuid");
        operationMemberInfo.setMemberIntegral(0);
        operationMemberInfo.setMemberGrowthValue(0);
        operationMemberInfo.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag1 = new HsaGradeGiftBag();
        hsaGradeGiftBag1.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag1.setType(0);
        hsaGradeGiftBag1.setValue(0);
        hsaGradeGiftBag1.setIsDelete(0);
        hsaGradeGiftBag1.setEffective(0);
        when(mockGradeChangeAssembler.getHsaMemberGradeRightsRecord(gradeRightsProcessorDTO, operationMemberInfo,
                hsaGradeGiftBag1)).thenReturn(hsaMemberGradeRightsRecord);

        // Configure GradeChangeAssembler.getHsaGrowthValueDetail(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail1 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail1.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail1.setId(0L);
        hsaGrowthValueDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail1.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail1.setMemberInfoGuid("memberInfoGuid");
        final HsaOperationMemberInfo operationMemberInfo1 = new HsaOperationMemberInfo();
        operationMemberInfo1.setGuid("guid");
        operationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo1.setMemberGradeInfoGuid("");
        operationMemberInfo1.setMemberGradeInfoName("");
        operationMemberInfo1.setOperatorGuid("operatorGuid");
        operationMemberInfo1.setMemberIntegral(0);
        operationMemberInfo1.setMemberGrowthValue(0);
        operationMemberInfo1.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag2 = new HsaGradeGiftBag();
        hsaGradeGiftBag2.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag2.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag2.setType(0);
        hsaGradeGiftBag2.setValue(0);
        hsaGradeGiftBag2.setIsDelete(0);
        hsaGradeGiftBag2.setEffective(0);
        final HsaMemberGradeInfo hsaMemberGradeInfo2 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo2.setGuid("");
        hsaMemberGradeInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo2.setVipGrade(0);
        hsaMemberGradeInfo2.setName("");
        hsaMemberGradeInfo2.setGrowthValue(0);
        hsaMemberGradeInfo2.setIsDelete(0);
        hsaMemberGradeInfo2.setEffective(0);
        hsaMemberGradeInfo2.setRoleType("roleType");
        hsaMemberGradeInfo2.setUpgradeType(0);
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo2 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo2.setId(0L);
        hsaGiftBagBaseInfo2.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo2.setEquitiesType(0);
        hsaGiftBagBaseInfo2.setEquitiesRule(0);
        final HsaMemberGradeInfo afterMemberGradeInfo = new HsaMemberGradeInfo();
        afterMemberGradeInfo.setGuid("");
        afterMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo.setVipGrade(0);
        afterMemberGradeInfo.setName("");
        afterMemberGradeInfo.setGrowthValue(0);
        afterMemberGradeInfo.setIsDelete(0);
        afterMemberGradeInfo.setEffective(0);
        afterMemberGradeInfo.setRoleType("roleType");
        afterMemberGradeInfo.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaGrowthValueDetail(operationMemberInfo1, 0, "operSubjectGuid",
                hsaGradeGiftBag2, hsaMemberGradeInfo2, hsaGiftBagBaseInfo2, afterMemberGradeInfo))
                .thenReturn(hsaGrowthValueDetail1);

        // Configure GradeChangeAssembler.getHsaIntegralValueDetail(...).
        final HsaIntegralDetail hsaIntegralDetail1 = new HsaIntegralDetail();
        hsaIntegralDetail1.setId(0L);
        hsaIntegralDetail1.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail1.setIntegralTaskGuid("integralTaskGuid");
        final HsaOperationMemberInfo operationMemberInfo2 = new HsaOperationMemberInfo();
        operationMemberInfo2.setGuid("guid");
        operationMemberInfo2.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo2.setMemberGradeInfoGuid("");
        operationMemberInfo2.setMemberGradeInfoName("");
        operationMemberInfo2.setOperatorGuid("operatorGuid");
        operationMemberInfo2.setMemberIntegral(0);
        operationMemberInfo2.setMemberGrowthValue(0);
        operationMemberInfo2.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag3 = new HsaGradeGiftBag();
        hsaGradeGiftBag3.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag3.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag3.setType(0);
        hsaGradeGiftBag3.setValue(0);
        hsaGradeGiftBag3.setIsDelete(0);
        hsaGradeGiftBag3.setEffective(0);
        final HsaMemberGradeInfo hsaMemberGradeInfo3 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo3.setGuid("");
        hsaMemberGradeInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo3.setVipGrade(0);
        hsaMemberGradeInfo3.setName("");
        hsaMemberGradeInfo3.setGrowthValue(0);
        hsaMemberGradeInfo3.setIsDelete(0);
        hsaMemberGradeInfo3.setEffective(0);
        hsaMemberGradeInfo3.setRoleType("roleType");
        hsaMemberGradeInfo3.setUpgradeType(0);
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo3 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo3.setId(0L);
        hsaGiftBagBaseInfo3.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo3.setEquitiesType(0);
        hsaGiftBagBaseInfo3.setEquitiesRule(0);
        final HsaMemberGradeInfo afterMemberGradeInfo1 = new HsaMemberGradeInfo();
        afterMemberGradeInfo1.setGuid("");
        afterMemberGradeInfo1.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo1.setVipGrade(0);
        afterMemberGradeInfo1.setName("");
        afterMemberGradeInfo1.setGrowthValue(0);
        afterMemberGradeInfo1.setIsDelete(0);
        afterMemberGradeInfo1.setEffective(0);
        afterMemberGradeInfo1.setRoleType("roleType");
        afterMemberGradeInfo1.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaIntegralValueDetail(operationMemberInfo2, 0, "operSubjectGuid",
                hsaGradeGiftBag3, hsaMemberGradeInfo3, hsaGiftBagBaseInfo3, afterMemberGradeInfo1))
                .thenReturn(hsaIntegralDetail1);

        // Configure GradeChangeAssembler.getMemberGradeRightsRecord(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord2 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord2.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord2.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord2.setRightsType(0);
        final HsaOperationMemberInfo operationMemberInfo3 = new HsaOperationMemberInfo();
        operationMemberInfo3.setGuid("guid");
        operationMemberInfo3.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo3.setMemberGradeInfoGuid("");
        operationMemberInfo3.setMemberGradeInfoName("");
        operationMemberInfo3.setOperatorGuid("operatorGuid");
        operationMemberInfo3.setMemberIntegral(0);
        operationMemberInfo3.setMemberGrowthValue(0);
        operationMemberInfo3.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockGradeChangeAssembler.getMemberGradeRightsRecord(operationMemberInfo3, "operSubjectGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build())).thenReturn(hsaMemberGradeRightsRecord2);

        // Configure HsaMemberEquitiesReceiveRecordMapper.selectAllGrowthValue(...).
        final MemberGrowthValueRelationVO memberGrowthValueRelationVO = new MemberGrowthValueRelationVO();
        memberGrowthValueRelationVO.setMemberGuid("memberGuid");
        memberGrowthValueRelationVO.setGrowthValue(0);
        final List<MemberGrowthValueRelationVO> memberGrowthValueRelationVOS = Arrays.asList(
                memberGrowthValueRelationVO);
        when(mockHsaMemberEquitiesReceiveRecordMapper.selectAllGrowthValue("guid", 0))
                .thenReturn(memberGrowthValueRelationVOS);

        // Configure GradeChangeAssembler.getHsaIntegralValueDetail(...).
        final HsaIntegralDetail hsaIntegralDetail2 = new HsaIntegralDetail();
        hsaIntegralDetail2.setId(0L);
        hsaIntegralDetail2.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail2.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail2.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail2.setIntegralTaskGuid("integralTaskGuid");
        final HsaMemberGradeInfo hsaMemberGradeInfo4 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo4.setGuid("");
        hsaMemberGradeInfo4.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo4.setVipGrade(0);
        hsaMemberGradeInfo4.setName("");
        hsaMemberGradeInfo4.setGrowthValue(0);
        hsaMemberGradeInfo4.setIsDelete(0);
        hsaMemberGradeInfo4.setEffective(0);
        hsaMemberGradeInfo4.setRoleType("roleType");
        hsaMemberGradeInfo4.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaIntegralValueDetail("guid", 0, "operSubjectGuid", hsaMemberGradeInfo4,
                HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build(), 0, 0)).thenReturn(hsaIntegralDetail2);

        // Configure GradeChangeAssembler.getHsaGrowthValueDetail(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail2 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail2.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail2.setId(0L);
        hsaGrowthValueDetail2.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail2.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail2.setMemberInfoGuid("memberInfoGuid");
        final HsaMemberGradeInfo hsaMemberGradeInfo5 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo5.setGuid("");
        hsaMemberGradeInfo5.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo5.setVipGrade(0);
        hsaMemberGradeInfo5.setName("");
        hsaMemberGradeInfo5.setGrowthValue(0);
        hsaMemberGradeInfo5.setIsDelete(0);
        hsaMemberGradeInfo5.setEffective(0);
        hsaMemberGradeInfo5.setRoleType("roleType");
        hsaMemberGradeInfo5.setUpgradeType(0);
        final MemberGradeChangeDTO gradeChangeDTO1 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("guid");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo1.setMemberGradeInfoName("");
        hsaOperationMemberInfo1.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo1.setMemberIntegral(0);
        hsaOperationMemberInfo1.setMemberGrowthValue(0);
        hsaOperationMemberInfo1.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO1.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo1));
        gradeChangeDTO1.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO1.setSourceType(0);
        gradeChangeDTO1.setIsRefresh(0);
        gradeChangeDTO1.setRoleType("roleType");
        gradeChangeDTO1.setSystem(0);
        gradeChangeDTO1.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getHsaGrowthValueDetail("guid", 0, hsaMemberGradeInfo5,
                HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build(), 0, 0, gradeChangeDTO1)).thenReturn(hsaGrowthValueDetail2);

        // Configure GradeChangeAssembler.getHsaMemberEquitiesReceiveRecord(...).
        final HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord = new HsaMemberEquitiesReceiveRecord();
        hsaMemberEquitiesReceiveRecord.setGuid("a782bb73-1961-4997-8c42-d00d348d6623");
        hsaMemberEquitiesReceiveRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberEquitiesReceiveRecord.setMemberGradeGuid("memberGradeGuid");
        hsaMemberEquitiesReceiveRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberEquitiesReceiveRecord.setGradeEquitiesGuid("gradeEquitiesGuid");
        when(mockGradeChangeAssembler.getHsaMemberEquitiesReceiveRecord("operatorGuid", "guid", "", "guid", 0,
                0)).thenReturn(hsaMemberEquitiesReceiveRecord);

        // Configure GradeChangeAssembler.getMemberGradeRelationDTO(...).
        final MemberGradeRelationDTO memberGradeRelationDTO = new MemberGradeRelationDTO();
        memberGradeRelationDTO.setMemberGuidList(Arrays.asList("value"));
        memberGradeRelationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberGradeRelationDTO.setRoleType("roleType");
        memberGradeRelationDTO.setNum(0);
        memberGradeRelationDTO.setUnit(0);
        final MemberGradeChangeDTO gradeChangeDTO2 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo2 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo2.setGuid("guid");
        hsaOperationMemberInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo2.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo2.setMemberGradeInfoName("");
        hsaOperationMemberInfo2.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo2.setMemberIntegral(0);
        hsaOperationMemberInfo2.setMemberGrowthValue(0);
        hsaOperationMemberInfo2.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO2.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo2));
        gradeChangeDTO2.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO2.setSourceType(0);
        gradeChangeDTO2.setIsRefresh(0);
        gradeChangeDTO2.setRoleType("roleType");
        gradeChangeDTO2.setSystem(0);
        gradeChangeDTO2.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getMemberGradeRelationDTO(gradeChangeDTO2, Arrays.asList("value")))
                .thenReturn(memberGradeRelationDTO);

        // Configure GradeChangeAssembler.getMemberGradePayRecordReqVO(...).
        final MemberGradePayRecordReqVO memberGradePayRecordReqVO = new MemberGradePayRecordReqVO();
        memberGradePayRecordReqVO.setMemberGuidList(Arrays.asList("value"));
        memberGradePayRecordReqVO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberGradePayRecordReqVO.setRoleType("roleType");
        memberGradePayRecordReqVO.setNum(0);
        memberGradePayRecordReqVO.setUnit(0);
        final MemberGradeChangeDTO gradeChangeDTO3 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo3 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo3.setGuid("guid");
        hsaOperationMemberInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo3.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo3.setMemberGradeInfoName("");
        hsaOperationMemberInfo3.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo3.setMemberIntegral(0);
        hsaOperationMemberInfo3.setMemberGrowthValue(0);
        hsaOperationMemberInfo3.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO3.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo3));
        gradeChangeDTO3.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO3.setSourceType(0);
        gradeChangeDTO3.setIsRefresh(0);
        gradeChangeDTO3.setRoleType("roleType");
        gradeChangeDTO3.setSystem(0);
        gradeChangeDTO3.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getMemberGradePayRecordReqVO(gradeChangeDTO3, Arrays.asList("value")))
                .thenReturn(memberGradePayRecordReqVO);

        when(mockCacheService.cleanToken("cacheKey")).thenReturn(false);

        // Run the test
        hsaMemberGradeChangeDetailServiceImplUnderTest.memberGradeChange(gradeChangeDTO);

        // Verify the results
        // Confirm GradeChangeAssembler.getMemberGradeChangeDetail(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo4 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo4.setGuid("guid");
        hsaOperationMemberInfo4.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo4.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo4.setMemberGradeInfoName("");
        hsaOperationMemberInfo4.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo4.setMemberIntegral(0);
        hsaOperationMemberInfo4.setMemberGrowthValue(0);
        hsaOperationMemberInfo4.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaMemberGradeInfo afterMemberGradeInfo2 = new HsaMemberGradeInfo();
        afterMemberGradeInfo2.setGuid("");
        afterMemberGradeInfo2.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo2.setVipGrade(0);
        afterMemberGradeInfo2.setName("");
        afterMemberGradeInfo2.setGrowthValue(0);
        afterMemberGradeInfo2.setIsDelete(0);
        afterMemberGradeInfo2.setEffective(0);
        afterMemberGradeInfo2.setRoleType("roleType");
        afterMemberGradeInfo2.setUpgradeType(0);
        final HsaMemberGradeInfo beforeMemberGradeInfo = new HsaMemberGradeInfo();
        beforeMemberGradeInfo.setGuid("");
        beforeMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        beforeMemberGradeInfo.setVipGrade(0);
        beforeMemberGradeInfo.setName("");
        beforeMemberGradeInfo.setGrowthValue(0);
        beforeMemberGradeInfo.setIsDelete(0);
        beforeMemberGradeInfo.setEffective(0);
        beforeMemberGradeInfo.setRoleType("roleType");
        beforeMemberGradeInfo.setUpgradeType(0);
        final HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail = new HsaMemberGradeChangeDetail();
        hsaMemberGradeChangeDetail.setGuid("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");
        hsaMemberGradeChangeDetail.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeChangeDetail.setMemberInfoGuid("guid");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeName("");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeGuid("");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail.setAfterChangeGradeName("");
        hsaMemberGradeChangeDetail.setAfterChangeGradeGuid("");
        hsaMemberGradeChangeDetail.setAfterChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail.setGradeChangeType(0);
        hsaMemberGradeChangeDetail.setCurrentGrowthValue(0);
        hsaMemberGradeChangeDetail.setIsShowApplet(0);
        hsaMemberGradeChangeDetail.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail.setAfterChangeGradeIcon("");
        hsaMemberGradeChangeDetail.setAfterChangeBackgroundColor("");
        hsaMemberGradeChangeDetail.setAfterChangeGrowthValue(0);
        verify(mockGradeChangeAssembler).getMemberGradeChangeDetail(hsaOperationMemberInfo4, afterMemberGradeInfo2,
                beforeMemberGradeInfo, hsaMemberGradeChangeDetail, LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Confirm HsaMemberGradeChangeDetailService.saveBatch(...).
        final HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail1 = new HsaMemberGradeChangeDetail();
        hsaMemberGradeChangeDetail1.setGuid("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");
        hsaMemberGradeChangeDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeChangeDetail1.setMemberInfoGuid("guid");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeName("");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeGuid("");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail1.setAfterChangeGradeName("");
        hsaMemberGradeChangeDetail1.setAfterChangeGradeGuid("");
        hsaMemberGradeChangeDetail1.setAfterChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail1.setGradeChangeType(0);
        hsaMemberGradeChangeDetail1.setCurrentGrowthValue(0);
        hsaMemberGradeChangeDetail1.setIsShowApplet(0);
        hsaMemberGradeChangeDetail1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail1.setAfterChangeGradeIcon("");
        hsaMemberGradeChangeDetail1.setAfterChangeBackgroundColor("");
        hsaMemberGradeChangeDetail1.setAfterChangeGrowthValue(0);
        final List<HsaMemberGradeChangeDetail> collection = Arrays.asList(hsaMemberGradeChangeDetail1);
        verify(mockHsaMemberGradeChangeDetailService).saveBatch(collection, 1000);

        // Confirm HsaMemberGradeRightsRecordService.saveBatch(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord3 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord3.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord3.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord3.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord3.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord3.setRightsType(0);
        final List<HsaMemberGradeRightsRecord> collection1 = Arrays.asList(hsaMemberGradeRightsRecord3);
        verify(mockHsaMemberGradeRightsRecordService).saveBatch(collection1, 1000);

        // Confirm HsaGrowthValueDetailService.saveBatch(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail3 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail3.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail3.setId(0L);
        hsaGrowthValueDetail3.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail3.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail3.setMemberInfoGuid("memberInfoGuid");
        final List<HsaGrowthValueDetail> collection2 = Arrays.asList(hsaGrowthValueDetail3);
        verify(mockHsaGrowthValueDetailService).saveBatch(collection2, 1000);

        // Confirm HsaIntegralDetailService.saveBatch(...).
        final HsaIntegralDetail hsaIntegralDetail3 = new HsaIntegralDetail();
        hsaIntegralDetail3.setId(0L);
        hsaIntegralDetail3.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail3.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail3.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail3.setIntegralTaskGuid("integralTaskGuid");
        final List<HsaIntegralDetail> collection3 = Arrays.asList(hsaIntegralDetail3);
        verify(mockHsaIntegralDetailService).saveBatch(collection3, 1000);

        // Confirm HsaMemberEquitiesReceiveRecordServiceImpl.saveBatch(...).
        final HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord1 = new HsaMemberEquitiesReceiveRecord();
        hsaMemberEquitiesReceiveRecord1.setGuid("a782bb73-1961-4997-8c42-d00d348d6623");
        hsaMemberEquitiesReceiveRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberEquitiesReceiveRecord1.setMemberGradeGuid("memberGradeGuid");
        hsaMemberEquitiesReceiveRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberEquitiesReceiveRecord1.setGradeEquitiesGuid("gradeEquitiesGuid");
        final List<HsaMemberEquitiesReceiveRecord> entityList = Arrays.asList(hsaMemberEquitiesReceiveRecord1);
        verify(mockHsaMemberEquitiesReceiveRecordService).saveBatch(entityList, 1000);

        // Confirm HsaOperationMemberInfoMapper.batchUpdateGrowth(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo5 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo5.setGuid("guid");
        hsaOperationMemberInfo5.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo5.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo5.setMemberGradeInfoName("");
        hsaOperationMemberInfo5.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo5.setMemberIntegral(0);
        hsaOperationMemberInfo5.setMemberGrowthValue(0);
        hsaOperationMemberInfo5.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo5);
        verify(mockHsaOperationMemberInfoMapper).batchUpdateGrowth(hsaOperationMemberInfos);
        verify(mockMemberGradeRelationService).batchDeleteMemberGrade(Arrays.asList("value"), "roleType");

        // Confirm IHsaMemberGradeRelationService.batchSaveAndUpdateRelation(...).
        final MemberGradeRelationDTO relationDTO = new MemberGradeRelationDTO();
        relationDTO.setMemberGuidList(Arrays.asList("value"));
        relationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        relationDTO.setRoleType("roleType");
        relationDTO.setNum(0);
        relationDTO.setUnit(0);
        verify(mockMemberGradeRelationService).batchSaveAndUpdateRelation(relationDTO);

        // Confirm IHsaMemberGradePayRecordService.batchAdd(...).
        final MemberGradePayRecordReqVO gradePayRecordReqVO = new MemberGradePayRecordReqVO();
        gradePayRecordReqVO.setMemberGuidList(Arrays.asList("value"));
        gradePayRecordReqVO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        gradePayRecordReqVO.setRoleType("roleType");
        gradePayRecordReqVO.setNum(0);
        gradePayRecordReqVO.setUnit(0);
        verify(mockMemberGradePayRecordService).batchAdd(gradePayRecordReqVO);

        // Confirm HsaOperationMemberInfoMapper.batchUpdateIntegral(...).
        final UpdateIntegral updateIntegral = new UpdateIntegral();
        updateIntegral.setGuid("32f2621a-4a96-4713-abb6-d3f2e4b4e56f");
        updateIntegral.setIntegralValue(0);
        final List<UpdateIntegral> updateMemberIntegralList = Arrays.asList(updateIntegral);
        verify(mockHsaOperationMemberInfoMapper).batchUpdateIntegral(updateMemberIntegralList);
    }

    @Test
    public void testMemberGradeChange_HsaGradeEquitiesMapperReturnsNoItems() {
        // Setup
        final MemberGradeChangeDTO gradeChangeDTO = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("guid");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo.setMemberGradeInfoName("");
        hsaOperationMemberInfo.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo.setMemberIntegral(0);
        hsaOperationMemberInfo.setMemberGrowthValue(0);
        hsaOperationMemberInfo.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo));
        gradeChangeDTO.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO.setSourceType(0);
        gradeChangeDTO.setIsRefresh(0);
        gradeChangeDTO.setRoleType("roleType");
        gradeChangeDTO.setSystem(0);
        gradeChangeDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");

        // Configure HsaControlledGradeStateMapper.selectOne(...).
        final HsaControlledGradeState hsaControlledGradeState = new HsaControlledGradeState();
        hsaControlledGradeState.setId(0L);
        hsaControlledGradeState.setOperSubjectGuid("operSubjectGuid");
        hsaControlledGradeState.setState(0);
        hsaControlledGradeState.setIsDelete(false);
        hsaControlledGradeState.setRoleType("roleType");
        when(mockHsaControlledGradeStateMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaControlledGradeState);

        // Configure HsaMemberGradeInfoMapper.selectList(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("");
        hsaMemberGradeInfo.setGrowthValue(0);
        hsaMemberGradeInfo.setIsDelete(0);
        hsaMemberGradeInfo.setEffective(0);
        hsaMemberGradeInfo.setRoleType("roleType");
        hsaMemberGradeInfo.setUpgradeType(0);
        final List<HsaMemberGradeInfo> hsaMemberGradeInfos = Arrays.asList(hsaMemberGradeInfo);
        when(mockHsaMemberGradeInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfos);

        // Configure IHsaMemberGradeRelationService.listByMemberGuidList(...).
        final HsaMemberGradeRelation hsaMemberGradeRelation = new HsaMemberGradeRelation();
        hsaMemberGradeRelation.setId(0);
        hsaMemberGradeRelation.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRelation.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRelation.setMemberInfoGradeGuid("memberInfoGradeGuid");
        hsaMemberGradeRelation.setIsEnable(false);
        final List<HsaMemberGradeRelation> hsaMemberGradeRelations = Arrays.asList(hsaMemberGradeRelation);
        when(mockMemberGradeRelationService.listByMemberGuidList(Arrays.asList("value"), "roleType"))
                .thenReturn(hsaMemberGradeRelations);

        // Configure HsaGradeEquitiesService.list(...).
        final List<HsaBusinessEquities> hsaBusinessEquities = Arrays.asList(HsaBusinessEquities.builder()
                .guid("guid")
                .memberGradeInfoGuid("memberGradeInfoGuid")
                .equitiesRuleType(0)
                .totalGiveNumber(0)
                .setPeriod(0)
                .isDelete(0)
                .effective(0)
                .operSubjectGuid("operSubjectGuid")
                .build());
        when(mockHsaGradeEquitiesService.list(any(LambdaQueryWrapper.class))).thenReturn(hsaBusinessEquities);

        // Configure HsaMemberGradeRightsRecordMapper.selectList(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord.setRightsType(0);
        final List<HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecords = Arrays.asList(hsaMemberGradeRightsRecord);
        when(mockHsaMemberGradeRightsRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberGradeRightsRecords);

        when(mockHsaGradeEquitiesMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure HsaGiftBagBaseInfoMapper.selectOne(...).
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo.setId(0L);
        hsaGiftBagBaseInfo.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo.setEquitiesType(0);
        hsaGiftBagBaseInfo.setEquitiesRule(0);
        when(mockHsaGiftBagBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaGiftBagBaseInfo);

        // Configure HsaMemberGradeInfoMapper.selectOne(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo1 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo1.setGuid("");
        hsaMemberGradeInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo1.setVipGrade(0);
        hsaMemberGradeInfo1.setName("");
        hsaMemberGradeInfo1.setGrowthValue(0);
        hsaMemberGradeInfo1.setIsDelete(0);
        hsaMemberGradeInfo1.setEffective(0);
        hsaMemberGradeInfo1.setRoleType("roleType");
        hsaMemberGradeInfo1.setUpgradeType(0);
        when(mockHsaMemberGradeInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfo1);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");

        // Configure HsaGradeGiftBagMapper.selectList(...).
        final HsaGradeGiftBag hsaGradeGiftBag = new HsaGradeGiftBag();
        hsaGradeGiftBag.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag.setType(0);
        hsaGradeGiftBag.setValue(0);
        hsaGradeGiftBag.setIsDelete(0);
        hsaGradeGiftBag.setEffective(0);
        final List<HsaGradeGiftBag> hsaGradeGiftBags = Arrays.asList(hsaGradeGiftBag);
        when(mockHsaGradeGiftBagMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaGradeGiftBags);

        // Configure GradeChangeAssembler.getHsaMemberGradeRightsRecord(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord1 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord1.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord1.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord1.setRightsType(0);
        final GradeRightsProcessorDTO gradeRightsProcessorDTO = new GradeRightsProcessorDTO();
        gradeRightsProcessorDTO.setUpdateMemberIntegralMap(new HashMap<>());
        gradeRightsProcessorDTO.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo1 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo1.setEquitiesRule(0);
        gradeRightsProcessorDTO.setHsaGiftBagBaseInfo(hsaGiftBagBaseInfo1);
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord2 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord2.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord2.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord2.setRightsType(0);
        gradeRightsProcessorDTO.setHsaMemberGradeRightsRecords(Arrays.asList(hsaMemberGradeRightsRecord2));
        final HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        gradeRightsProcessorDTO.setHsaGrowthValueDetails(Arrays.asList(hsaGrowthValueDetail));
        final HsaIntegralDetail hsaIntegralDetail = new HsaIntegralDetail();
        gradeRightsProcessorDTO.setHsaIntegralDetails(Arrays.asList(hsaIntegralDetail));
        gradeRightsProcessorDTO.setSourceType(0);
        gradeRightsProcessorDTO.setOperSubjectGuid("operSubjectGuid");
        final HsaOperationMemberInfo operationMemberInfo = new HsaOperationMemberInfo();
        operationMemberInfo.setGuid("guid");
        operationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo.setMemberGradeInfoGuid("");
        operationMemberInfo.setMemberGradeInfoName("");
        operationMemberInfo.setOperatorGuid("operatorGuid");
        operationMemberInfo.setMemberIntegral(0);
        operationMemberInfo.setMemberGrowthValue(0);
        operationMemberInfo.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag1 = new HsaGradeGiftBag();
        hsaGradeGiftBag1.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag1.setType(0);
        hsaGradeGiftBag1.setValue(0);
        hsaGradeGiftBag1.setIsDelete(0);
        hsaGradeGiftBag1.setEffective(0);
        when(mockGradeChangeAssembler.getHsaMemberGradeRightsRecord(gradeRightsProcessorDTO, operationMemberInfo,
                hsaGradeGiftBag1)).thenReturn(hsaMemberGradeRightsRecord1);

        // Configure GradeChangeAssembler.getHsaGrowthValueDetail(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail1 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail1.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail1.setId(0L);
        hsaGrowthValueDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail1.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail1.setMemberInfoGuid("memberInfoGuid");
        final HsaOperationMemberInfo operationMemberInfo1 = new HsaOperationMemberInfo();
        operationMemberInfo1.setGuid("guid");
        operationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo1.setMemberGradeInfoGuid("");
        operationMemberInfo1.setMemberGradeInfoName("");
        operationMemberInfo1.setOperatorGuid("operatorGuid");
        operationMemberInfo1.setMemberIntegral(0);
        operationMemberInfo1.setMemberGrowthValue(0);
        operationMemberInfo1.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag2 = new HsaGradeGiftBag();
        hsaGradeGiftBag2.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag2.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag2.setType(0);
        hsaGradeGiftBag2.setValue(0);
        hsaGradeGiftBag2.setIsDelete(0);
        hsaGradeGiftBag2.setEffective(0);
        final HsaMemberGradeInfo hsaMemberGradeInfo2 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo2.setGuid("");
        hsaMemberGradeInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo2.setVipGrade(0);
        hsaMemberGradeInfo2.setName("");
        hsaMemberGradeInfo2.setGrowthValue(0);
        hsaMemberGradeInfo2.setIsDelete(0);
        hsaMemberGradeInfo2.setEffective(0);
        hsaMemberGradeInfo2.setRoleType("roleType");
        hsaMemberGradeInfo2.setUpgradeType(0);
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo2 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo2.setId(0L);
        hsaGiftBagBaseInfo2.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo2.setEquitiesType(0);
        hsaGiftBagBaseInfo2.setEquitiesRule(0);
        final HsaMemberGradeInfo afterMemberGradeInfo = new HsaMemberGradeInfo();
        afterMemberGradeInfo.setGuid("");
        afterMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo.setVipGrade(0);
        afterMemberGradeInfo.setName("");
        afterMemberGradeInfo.setGrowthValue(0);
        afterMemberGradeInfo.setIsDelete(0);
        afterMemberGradeInfo.setEffective(0);
        afterMemberGradeInfo.setRoleType("roleType");
        afterMemberGradeInfo.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaGrowthValueDetail(operationMemberInfo1, 0, "operSubjectGuid",
                hsaGradeGiftBag2, hsaMemberGradeInfo2, hsaGiftBagBaseInfo2, afterMemberGradeInfo))
                .thenReturn(hsaGrowthValueDetail1);

        // Configure GradeChangeAssembler.getHsaIntegralValueDetail(...).
        final HsaIntegralDetail hsaIntegralDetail1 = new HsaIntegralDetail();
        hsaIntegralDetail1.setId(0L);
        hsaIntegralDetail1.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail1.setIntegralTaskGuid("integralTaskGuid");
        final HsaOperationMemberInfo operationMemberInfo2 = new HsaOperationMemberInfo();
        operationMemberInfo2.setGuid("guid");
        operationMemberInfo2.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo2.setMemberGradeInfoGuid("");
        operationMemberInfo2.setMemberGradeInfoName("");
        operationMemberInfo2.setOperatorGuid("operatorGuid");
        operationMemberInfo2.setMemberIntegral(0);
        operationMemberInfo2.setMemberGrowthValue(0);
        operationMemberInfo2.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag3 = new HsaGradeGiftBag();
        hsaGradeGiftBag3.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag3.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag3.setType(0);
        hsaGradeGiftBag3.setValue(0);
        hsaGradeGiftBag3.setIsDelete(0);
        hsaGradeGiftBag3.setEffective(0);
        final HsaMemberGradeInfo hsaMemberGradeInfo3 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo3.setGuid("");
        hsaMemberGradeInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo3.setVipGrade(0);
        hsaMemberGradeInfo3.setName("");
        hsaMemberGradeInfo3.setGrowthValue(0);
        hsaMemberGradeInfo3.setIsDelete(0);
        hsaMemberGradeInfo3.setEffective(0);
        hsaMemberGradeInfo3.setRoleType("roleType");
        hsaMemberGradeInfo3.setUpgradeType(0);
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo3 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo3.setId(0L);
        hsaGiftBagBaseInfo3.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo3.setEquitiesType(0);
        hsaGiftBagBaseInfo3.setEquitiesRule(0);
        final HsaMemberGradeInfo afterMemberGradeInfo1 = new HsaMemberGradeInfo();
        afterMemberGradeInfo1.setGuid("");
        afterMemberGradeInfo1.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo1.setVipGrade(0);
        afterMemberGradeInfo1.setName("");
        afterMemberGradeInfo1.setGrowthValue(0);
        afterMemberGradeInfo1.setIsDelete(0);
        afterMemberGradeInfo1.setEffective(0);
        afterMemberGradeInfo1.setRoleType("roleType");
        afterMemberGradeInfo1.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaIntegralValueDetail(operationMemberInfo2, 0, "operSubjectGuid",
                hsaGradeGiftBag3, hsaMemberGradeInfo3, hsaGiftBagBaseInfo3, afterMemberGradeInfo1))
                .thenReturn(hsaIntegralDetail1);

        // Configure GradeChangeAssembler.getMemberGradeRightsRecord(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord3 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord3.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord3.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord3.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord3.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord3.setRightsType(0);
        final HsaOperationMemberInfo operationMemberInfo3 = new HsaOperationMemberInfo();
        operationMemberInfo3.setGuid("guid");
        operationMemberInfo3.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo3.setMemberGradeInfoGuid("");
        operationMemberInfo3.setMemberGradeInfoName("");
        operationMemberInfo3.setOperatorGuid("operatorGuid");
        operationMemberInfo3.setMemberIntegral(0);
        operationMemberInfo3.setMemberGrowthValue(0);
        operationMemberInfo3.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockGradeChangeAssembler.getMemberGradeRightsRecord(operationMemberInfo3, "operSubjectGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build())).thenReturn(hsaMemberGradeRightsRecord3);

        // Configure HsaMemberEquitiesReceiveRecordMapper.selectAllGrowthValue(...).
        final MemberGrowthValueRelationVO memberGrowthValueRelationVO = new MemberGrowthValueRelationVO();
        memberGrowthValueRelationVO.setMemberGuid("memberGuid");
        memberGrowthValueRelationVO.setGrowthValue(0);
        final List<MemberGrowthValueRelationVO> memberGrowthValueRelationVOS = Arrays.asList(
                memberGrowthValueRelationVO);
        when(mockHsaMemberEquitiesReceiveRecordMapper.selectAllGrowthValue("guid", 0))
                .thenReturn(memberGrowthValueRelationVOS);

        // Configure GradeChangeAssembler.getHsaIntegralValueDetail(...).
        final HsaIntegralDetail hsaIntegralDetail2 = new HsaIntegralDetail();
        hsaIntegralDetail2.setId(0L);
        hsaIntegralDetail2.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail2.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail2.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail2.setIntegralTaskGuid("integralTaskGuid");
        final HsaMemberGradeInfo hsaMemberGradeInfo4 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo4.setGuid("");
        hsaMemberGradeInfo4.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo4.setVipGrade(0);
        hsaMemberGradeInfo4.setName("");
        hsaMemberGradeInfo4.setGrowthValue(0);
        hsaMemberGradeInfo4.setIsDelete(0);
        hsaMemberGradeInfo4.setEffective(0);
        hsaMemberGradeInfo4.setRoleType("roleType");
        hsaMemberGradeInfo4.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaIntegralValueDetail("guid", 0, "operSubjectGuid", hsaMemberGradeInfo4,
                HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build(), 0, 0)).thenReturn(hsaIntegralDetail2);

        // Configure GradeChangeAssembler.getHsaGrowthValueDetail(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail2 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail2.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail2.setId(0L);
        hsaGrowthValueDetail2.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail2.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail2.setMemberInfoGuid("memberInfoGuid");
        final HsaMemberGradeInfo hsaMemberGradeInfo5 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo5.setGuid("");
        hsaMemberGradeInfo5.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo5.setVipGrade(0);
        hsaMemberGradeInfo5.setName("");
        hsaMemberGradeInfo5.setGrowthValue(0);
        hsaMemberGradeInfo5.setIsDelete(0);
        hsaMemberGradeInfo5.setEffective(0);
        hsaMemberGradeInfo5.setRoleType("roleType");
        hsaMemberGradeInfo5.setUpgradeType(0);
        final MemberGradeChangeDTO gradeChangeDTO1 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("guid");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo1.setMemberGradeInfoName("");
        hsaOperationMemberInfo1.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo1.setMemberIntegral(0);
        hsaOperationMemberInfo1.setMemberGrowthValue(0);
        hsaOperationMemberInfo1.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO1.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo1));
        gradeChangeDTO1.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO1.setSourceType(0);
        gradeChangeDTO1.setIsRefresh(0);
        gradeChangeDTO1.setRoleType("roleType");
        gradeChangeDTO1.setSystem(0);
        gradeChangeDTO1.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getHsaGrowthValueDetail("guid", 0, hsaMemberGradeInfo5,
                HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build(), 0, 0, gradeChangeDTO1)).thenReturn(hsaGrowthValueDetail2);

        // Configure GradeChangeAssembler.getHsaMemberEquitiesReceiveRecord(...).
        final HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord = new HsaMemberEquitiesReceiveRecord();
        hsaMemberEquitiesReceiveRecord.setGuid("a782bb73-1961-4997-8c42-d00d348d6623");
        hsaMemberEquitiesReceiveRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberEquitiesReceiveRecord.setMemberGradeGuid("memberGradeGuid");
        hsaMemberEquitiesReceiveRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberEquitiesReceiveRecord.setGradeEquitiesGuid("gradeEquitiesGuid");
        when(mockGradeChangeAssembler.getHsaMemberEquitiesReceiveRecord("operatorGuid", "guid", "", "guid", 0,
                0)).thenReturn(hsaMemberEquitiesReceiveRecord);

        // Configure GradeChangeAssembler.getMemberGradeRelationDTO(...).
        final MemberGradeRelationDTO memberGradeRelationDTO = new MemberGradeRelationDTO();
        memberGradeRelationDTO.setMemberGuidList(Arrays.asList("value"));
        memberGradeRelationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberGradeRelationDTO.setRoleType("roleType");
        memberGradeRelationDTO.setNum(0);
        memberGradeRelationDTO.setUnit(0);
        final MemberGradeChangeDTO gradeChangeDTO2 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo2 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo2.setGuid("guid");
        hsaOperationMemberInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo2.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo2.setMemberGradeInfoName("");
        hsaOperationMemberInfo2.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo2.setMemberIntegral(0);
        hsaOperationMemberInfo2.setMemberGrowthValue(0);
        hsaOperationMemberInfo2.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO2.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo2));
        gradeChangeDTO2.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO2.setSourceType(0);
        gradeChangeDTO2.setIsRefresh(0);
        gradeChangeDTO2.setRoleType("roleType");
        gradeChangeDTO2.setSystem(0);
        gradeChangeDTO2.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getMemberGradeRelationDTO(gradeChangeDTO2, Arrays.asList("value")))
                .thenReturn(memberGradeRelationDTO);

        // Configure GradeChangeAssembler.getMemberGradePayRecordReqVO(...).
        final MemberGradePayRecordReqVO memberGradePayRecordReqVO = new MemberGradePayRecordReqVO();
        memberGradePayRecordReqVO.setMemberGuidList(Arrays.asList("value"));
        memberGradePayRecordReqVO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberGradePayRecordReqVO.setRoleType("roleType");
        memberGradePayRecordReqVO.setNum(0);
        memberGradePayRecordReqVO.setUnit(0);
        final MemberGradeChangeDTO gradeChangeDTO3 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo3 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo3.setGuid("guid");
        hsaOperationMemberInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo3.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo3.setMemberGradeInfoName("");
        hsaOperationMemberInfo3.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo3.setMemberIntegral(0);
        hsaOperationMemberInfo3.setMemberGrowthValue(0);
        hsaOperationMemberInfo3.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO3.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo3));
        gradeChangeDTO3.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO3.setSourceType(0);
        gradeChangeDTO3.setIsRefresh(0);
        gradeChangeDTO3.setRoleType("roleType");
        gradeChangeDTO3.setSystem(0);
        gradeChangeDTO3.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getMemberGradePayRecordReqVO(gradeChangeDTO3, Arrays.asList("value")))
                .thenReturn(memberGradePayRecordReqVO);

        when(mockCacheService.cleanToken("cacheKey")).thenReturn(false);

        // Run the test
        hsaMemberGradeChangeDetailServiceImplUnderTest.memberGradeChange(gradeChangeDTO);

        // Verify the results
        // Confirm GradeChangeAssembler.getMemberGradeChangeDetail(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo4 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo4.setGuid("guid");
        hsaOperationMemberInfo4.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo4.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo4.setMemberGradeInfoName("");
        hsaOperationMemberInfo4.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo4.setMemberIntegral(0);
        hsaOperationMemberInfo4.setMemberGrowthValue(0);
        hsaOperationMemberInfo4.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaMemberGradeInfo afterMemberGradeInfo2 = new HsaMemberGradeInfo();
        afterMemberGradeInfo2.setGuid("");
        afterMemberGradeInfo2.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo2.setVipGrade(0);
        afterMemberGradeInfo2.setName("");
        afterMemberGradeInfo2.setGrowthValue(0);
        afterMemberGradeInfo2.setIsDelete(0);
        afterMemberGradeInfo2.setEffective(0);
        afterMemberGradeInfo2.setRoleType("roleType");
        afterMemberGradeInfo2.setUpgradeType(0);
        final HsaMemberGradeInfo beforeMemberGradeInfo = new HsaMemberGradeInfo();
        beforeMemberGradeInfo.setGuid("");
        beforeMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        beforeMemberGradeInfo.setVipGrade(0);
        beforeMemberGradeInfo.setName("");
        beforeMemberGradeInfo.setGrowthValue(0);
        beforeMemberGradeInfo.setIsDelete(0);
        beforeMemberGradeInfo.setEffective(0);
        beforeMemberGradeInfo.setRoleType("roleType");
        beforeMemberGradeInfo.setUpgradeType(0);
        final HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail = new HsaMemberGradeChangeDetail();
        hsaMemberGradeChangeDetail.setGuid("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");
        hsaMemberGradeChangeDetail.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeChangeDetail.setMemberInfoGuid("guid");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeName("");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeGuid("");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail.setAfterChangeGradeName("");
        hsaMemberGradeChangeDetail.setAfterChangeGradeGuid("");
        hsaMemberGradeChangeDetail.setAfterChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail.setGradeChangeType(0);
        hsaMemberGradeChangeDetail.setCurrentGrowthValue(0);
        hsaMemberGradeChangeDetail.setIsShowApplet(0);
        hsaMemberGradeChangeDetail.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail.setAfterChangeGradeIcon("");
        hsaMemberGradeChangeDetail.setAfterChangeBackgroundColor("");
        hsaMemberGradeChangeDetail.setAfterChangeGrowthValue(0);
        verify(mockGradeChangeAssembler).getMemberGradeChangeDetail(hsaOperationMemberInfo4, afterMemberGradeInfo2,
                beforeMemberGradeInfo, hsaMemberGradeChangeDetail, LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Confirm HsaMemberGradeChangeDetailService.saveBatch(...).
        final HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail1 = new HsaMemberGradeChangeDetail();
        hsaMemberGradeChangeDetail1.setGuid("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");
        hsaMemberGradeChangeDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeChangeDetail1.setMemberInfoGuid("guid");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeName("");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeGuid("");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail1.setAfterChangeGradeName("");
        hsaMemberGradeChangeDetail1.setAfterChangeGradeGuid("");
        hsaMemberGradeChangeDetail1.setAfterChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail1.setGradeChangeType(0);
        hsaMemberGradeChangeDetail1.setCurrentGrowthValue(0);
        hsaMemberGradeChangeDetail1.setIsShowApplet(0);
        hsaMemberGradeChangeDetail1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail1.setAfterChangeGradeIcon("");
        hsaMemberGradeChangeDetail1.setAfterChangeBackgroundColor("");
        hsaMemberGradeChangeDetail1.setAfterChangeGrowthValue(0);
        final List<HsaMemberGradeChangeDetail> collection = Arrays.asList(hsaMemberGradeChangeDetail1);
        verify(mockHsaMemberGradeChangeDetailService).saveBatch(collection, 1000);

        // Confirm HsaMemberGradeRightsRecordService.saveBatch(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord4 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord4.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord4.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord4.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord4.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord4.setRightsType(0);
        final List<HsaMemberGradeRightsRecord> collection1 = Arrays.asList(hsaMemberGradeRightsRecord4);
        verify(mockHsaMemberGradeRightsRecordService).saveBatch(collection1, 1000);

        // Confirm HsaGrowthValueDetailService.saveBatch(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail3 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail3.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail3.setId(0L);
        hsaGrowthValueDetail3.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail3.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail3.setMemberInfoGuid("memberInfoGuid");
        final List<HsaGrowthValueDetail> collection2 = Arrays.asList(hsaGrowthValueDetail3);
        verify(mockHsaGrowthValueDetailService).saveBatch(collection2, 1000);

        // Confirm HsaIntegralDetailService.saveBatch(...).
        final HsaIntegralDetail hsaIntegralDetail3 = new HsaIntegralDetail();
        hsaIntegralDetail3.setId(0L);
        hsaIntegralDetail3.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail3.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail3.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail3.setIntegralTaskGuid("integralTaskGuid");
        final List<HsaIntegralDetail> collection3 = Arrays.asList(hsaIntegralDetail3);
        verify(mockHsaIntegralDetailService).saveBatch(collection3, 1000);

        // Confirm HsaMemberEquitiesReceiveRecordServiceImpl.saveBatch(...).
        final HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord1 = new HsaMemberEquitiesReceiveRecord();
        hsaMemberEquitiesReceiveRecord1.setGuid("a782bb73-1961-4997-8c42-d00d348d6623");
        hsaMemberEquitiesReceiveRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberEquitiesReceiveRecord1.setMemberGradeGuid("memberGradeGuid");
        hsaMemberEquitiesReceiveRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberEquitiesReceiveRecord1.setGradeEquitiesGuid("gradeEquitiesGuid");
        final List<HsaMemberEquitiesReceiveRecord> entityList = Arrays.asList(hsaMemberEquitiesReceiveRecord1);
        verify(mockHsaMemberEquitiesReceiveRecordService).saveBatch(entityList, 1000);

        // Confirm HsaOperationMemberInfoMapper.batchUpdateGrowth(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo5 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo5.setGuid("guid");
        hsaOperationMemberInfo5.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo5.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo5.setMemberGradeInfoName("");
        hsaOperationMemberInfo5.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo5.setMemberIntegral(0);
        hsaOperationMemberInfo5.setMemberGrowthValue(0);
        hsaOperationMemberInfo5.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo5);
        verify(mockHsaOperationMemberInfoMapper).batchUpdateGrowth(hsaOperationMemberInfos);
        verify(mockMemberGradeRelationService).batchDeleteMemberGrade(Arrays.asList("value"), "roleType");

        // Confirm IHsaMemberGradeRelationService.batchSaveAndUpdateRelation(...).
        final MemberGradeRelationDTO relationDTO = new MemberGradeRelationDTO();
        relationDTO.setMemberGuidList(Arrays.asList("value"));
        relationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        relationDTO.setRoleType("roleType");
        relationDTO.setNum(0);
        relationDTO.setUnit(0);
        verify(mockMemberGradeRelationService).batchSaveAndUpdateRelation(relationDTO);

        // Confirm IHsaMemberGradePayRecordService.batchAdd(...).
        final MemberGradePayRecordReqVO gradePayRecordReqVO = new MemberGradePayRecordReqVO();
        gradePayRecordReqVO.setMemberGuidList(Arrays.asList("value"));
        gradePayRecordReqVO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        gradePayRecordReqVO.setRoleType("roleType");
        gradePayRecordReqVO.setNum(0);
        gradePayRecordReqVO.setUnit(0);
        verify(mockMemberGradePayRecordService).batchAdd(gradePayRecordReqVO);

        // Confirm HsaOperationMemberInfoMapper.batchUpdateIntegral(...).
        final UpdateIntegral updateIntegral = new UpdateIntegral();
        updateIntegral.setGuid("32f2621a-4a96-4713-abb6-d3f2e4b4e56f");
        updateIntegral.setIntegralValue(0);
        final List<UpdateIntegral> updateMemberIntegralList = Arrays.asList(updateIntegral);
        verify(mockHsaOperationMemberInfoMapper).batchUpdateIntegral(updateMemberIntegralList);
    }

    @Test
    public void testMemberGradeChange_HsaGradeGiftBagMapperReturnsNoItems() {
        // Setup
        final MemberGradeChangeDTO gradeChangeDTO = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("guid");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo.setMemberGradeInfoName("");
        hsaOperationMemberInfo.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo.setMemberIntegral(0);
        hsaOperationMemberInfo.setMemberGrowthValue(0);
        hsaOperationMemberInfo.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo));
        gradeChangeDTO.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO.setSourceType(0);
        gradeChangeDTO.setIsRefresh(0);
        gradeChangeDTO.setRoleType("roleType");
        gradeChangeDTO.setSystem(0);
        gradeChangeDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");

        // Configure HsaControlledGradeStateMapper.selectOne(...).
        final HsaControlledGradeState hsaControlledGradeState = new HsaControlledGradeState();
        hsaControlledGradeState.setId(0L);
        hsaControlledGradeState.setOperSubjectGuid("operSubjectGuid");
        hsaControlledGradeState.setState(0);
        hsaControlledGradeState.setIsDelete(false);
        hsaControlledGradeState.setRoleType("roleType");
        when(mockHsaControlledGradeStateMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaControlledGradeState);

        // Configure HsaMemberGradeInfoMapper.selectList(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("");
        hsaMemberGradeInfo.setGrowthValue(0);
        hsaMemberGradeInfo.setIsDelete(0);
        hsaMemberGradeInfo.setEffective(0);
        hsaMemberGradeInfo.setRoleType("roleType");
        hsaMemberGradeInfo.setUpgradeType(0);
        final List<HsaMemberGradeInfo> hsaMemberGradeInfos = Arrays.asList(hsaMemberGradeInfo);
        when(mockHsaMemberGradeInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfos);

        // Configure IHsaMemberGradeRelationService.listByMemberGuidList(...).
        final HsaMemberGradeRelation hsaMemberGradeRelation = new HsaMemberGradeRelation();
        hsaMemberGradeRelation.setId(0);
        hsaMemberGradeRelation.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRelation.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRelation.setMemberInfoGradeGuid("memberInfoGradeGuid");
        hsaMemberGradeRelation.setIsEnable(false);
        final List<HsaMemberGradeRelation> hsaMemberGradeRelations = Arrays.asList(hsaMemberGradeRelation);
        when(mockMemberGradeRelationService.listByMemberGuidList(Arrays.asList("value"), "roleType"))
                .thenReturn(hsaMemberGradeRelations);

        // Configure HsaGradeEquitiesService.list(...).
        final List<HsaBusinessEquities> hsaBusinessEquities = Arrays.asList(HsaBusinessEquities.builder()
                .guid("guid")
                .memberGradeInfoGuid("memberGradeInfoGuid")
                .equitiesRuleType(0)
                .totalGiveNumber(0)
                .setPeriod(0)
                .isDelete(0)
                .effective(0)
                .operSubjectGuid("operSubjectGuid")
                .build());
        when(mockHsaGradeEquitiesService.list(any(LambdaQueryWrapper.class))).thenReturn(hsaBusinessEquities);

        // Configure HsaMemberGradeRightsRecordMapper.selectList(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord.setRightsType(0);
        final List<HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecords = Arrays.asList(hsaMemberGradeRightsRecord);
        when(mockHsaMemberGradeRightsRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberGradeRightsRecords);

        // Configure HsaGradeEquitiesMapper.selectList(...).
        final List<HsaBusinessEquities> hsaBusinessEquities1 = Arrays.asList(HsaBusinessEquities.builder()
                .guid("guid")
                .memberGradeInfoGuid("memberGradeInfoGuid")
                .equitiesRuleType(0)
                .totalGiveNumber(0)
                .setPeriod(0)
                .isDelete(0)
                .effective(0)
                .operSubjectGuid("operSubjectGuid")
                .build());
        when(mockHsaGradeEquitiesMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaBusinessEquities1);

        // Configure HsaGiftBagBaseInfoMapper.selectOne(...).
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo.setId(0L);
        hsaGiftBagBaseInfo.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo.setEquitiesType(0);
        hsaGiftBagBaseInfo.setEquitiesRule(0);
        when(mockHsaGiftBagBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaGiftBagBaseInfo);

        // Configure HsaMemberGradeInfoMapper.selectOne(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo1 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo1.setGuid("");
        hsaMemberGradeInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo1.setVipGrade(0);
        hsaMemberGradeInfo1.setName("");
        hsaMemberGradeInfo1.setGrowthValue(0);
        hsaMemberGradeInfo1.setIsDelete(0);
        hsaMemberGradeInfo1.setEffective(0);
        hsaMemberGradeInfo1.setRoleType("roleType");
        hsaMemberGradeInfo1.setUpgradeType(0);
        when(mockHsaMemberGradeInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfo1);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");
        when(mockHsaGradeGiftBagMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure GradeChangeAssembler.getMemberGradeRightsRecord(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord1 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord1.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord1.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord1.setRightsType(0);
        final HsaOperationMemberInfo operationMemberInfo = new HsaOperationMemberInfo();
        operationMemberInfo.setGuid("guid");
        operationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo.setMemberGradeInfoGuid("");
        operationMemberInfo.setMemberGradeInfoName("");
        operationMemberInfo.setOperatorGuid("operatorGuid");
        operationMemberInfo.setMemberIntegral(0);
        operationMemberInfo.setMemberGrowthValue(0);
        operationMemberInfo.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockGradeChangeAssembler.getMemberGradeRightsRecord(operationMemberInfo, "operSubjectGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build())).thenReturn(hsaMemberGradeRightsRecord1);

        // Configure HsaMemberEquitiesReceiveRecordMapper.selectAllGrowthValue(...).
        final MemberGrowthValueRelationVO memberGrowthValueRelationVO = new MemberGrowthValueRelationVO();
        memberGrowthValueRelationVO.setMemberGuid("memberGuid");
        memberGrowthValueRelationVO.setGrowthValue(0);
        final List<MemberGrowthValueRelationVO> memberGrowthValueRelationVOS = Arrays.asList(
                memberGrowthValueRelationVO);
        when(mockHsaMemberEquitiesReceiveRecordMapper.selectAllGrowthValue("guid", 0))
                .thenReturn(memberGrowthValueRelationVOS);

        // Configure GradeChangeAssembler.getHsaIntegralValueDetail(...).
        final HsaIntegralDetail hsaIntegralDetail = new HsaIntegralDetail();
        hsaIntegralDetail.setId(0L);
        hsaIntegralDetail.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail.setIntegralTaskGuid("integralTaskGuid");
        final HsaMemberGradeInfo hsaMemberGradeInfo2 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo2.setGuid("");
        hsaMemberGradeInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo2.setVipGrade(0);
        hsaMemberGradeInfo2.setName("");
        hsaMemberGradeInfo2.setGrowthValue(0);
        hsaMemberGradeInfo2.setIsDelete(0);
        hsaMemberGradeInfo2.setEffective(0);
        hsaMemberGradeInfo2.setRoleType("roleType");
        hsaMemberGradeInfo2.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaIntegralValueDetail("guid", 0, "operSubjectGuid", hsaMemberGradeInfo2,
                HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build(), 0, 0)).thenReturn(hsaIntegralDetail);

        // Configure GradeChangeAssembler.getHsaGrowthValueDetail(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        hsaGrowthValueDetail.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail.setId(0L);
        hsaGrowthValueDetail.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail.setMemberInfoGuid("memberInfoGuid");
        final HsaMemberGradeInfo hsaMemberGradeInfo3 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo3.setGuid("");
        hsaMemberGradeInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo3.setVipGrade(0);
        hsaMemberGradeInfo3.setName("");
        hsaMemberGradeInfo3.setGrowthValue(0);
        hsaMemberGradeInfo3.setIsDelete(0);
        hsaMemberGradeInfo3.setEffective(0);
        hsaMemberGradeInfo3.setRoleType("roleType");
        hsaMemberGradeInfo3.setUpgradeType(0);
        final MemberGradeChangeDTO gradeChangeDTO1 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("guid");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo1.setMemberGradeInfoName("");
        hsaOperationMemberInfo1.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo1.setMemberIntegral(0);
        hsaOperationMemberInfo1.setMemberGrowthValue(0);
        hsaOperationMemberInfo1.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO1.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo1));
        gradeChangeDTO1.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO1.setSourceType(0);
        gradeChangeDTO1.setIsRefresh(0);
        gradeChangeDTO1.setRoleType("roleType");
        gradeChangeDTO1.setSystem(0);
        gradeChangeDTO1.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getHsaGrowthValueDetail("guid", 0, hsaMemberGradeInfo3,
                HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build(), 0, 0, gradeChangeDTO1)).thenReturn(hsaGrowthValueDetail);

        // Configure GradeChangeAssembler.getHsaMemberEquitiesReceiveRecord(...).
        final HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord = new HsaMemberEquitiesReceiveRecord();
        hsaMemberEquitiesReceiveRecord.setGuid("a782bb73-1961-4997-8c42-d00d348d6623");
        hsaMemberEquitiesReceiveRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberEquitiesReceiveRecord.setMemberGradeGuid("memberGradeGuid");
        hsaMemberEquitiesReceiveRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberEquitiesReceiveRecord.setGradeEquitiesGuid("gradeEquitiesGuid");
        when(mockGradeChangeAssembler.getHsaMemberEquitiesReceiveRecord("operatorGuid", "guid", "", "guid", 0,
                0)).thenReturn(hsaMemberEquitiesReceiveRecord);

        // Configure GradeChangeAssembler.getMemberGradeRelationDTO(...).
        final MemberGradeRelationDTO memberGradeRelationDTO = new MemberGradeRelationDTO();
        memberGradeRelationDTO.setMemberGuidList(Arrays.asList("value"));
        memberGradeRelationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberGradeRelationDTO.setRoleType("roleType");
        memberGradeRelationDTO.setNum(0);
        memberGradeRelationDTO.setUnit(0);
        final MemberGradeChangeDTO gradeChangeDTO2 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo2 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo2.setGuid("guid");
        hsaOperationMemberInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo2.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo2.setMemberGradeInfoName("");
        hsaOperationMemberInfo2.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo2.setMemberIntegral(0);
        hsaOperationMemberInfo2.setMemberGrowthValue(0);
        hsaOperationMemberInfo2.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO2.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo2));
        gradeChangeDTO2.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO2.setSourceType(0);
        gradeChangeDTO2.setIsRefresh(0);
        gradeChangeDTO2.setRoleType("roleType");
        gradeChangeDTO2.setSystem(0);
        gradeChangeDTO2.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getMemberGradeRelationDTO(gradeChangeDTO2, Arrays.asList("value")))
                .thenReturn(memberGradeRelationDTO);

        // Configure GradeChangeAssembler.getMemberGradePayRecordReqVO(...).
        final MemberGradePayRecordReqVO memberGradePayRecordReqVO = new MemberGradePayRecordReqVO();
        memberGradePayRecordReqVO.setMemberGuidList(Arrays.asList("value"));
        memberGradePayRecordReqVO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberGradePayRecordReqVO.setRoleType("roleType");
        memberGradePayRecordReqVO.setNum(0);
        memberGradePayRecordReqVO.setUnit(0);
        final MemberGradeChangeDTO gradeChangeDTO3 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo3 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo3.setGuid("guid");
        hsaOperationMemberInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo3.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo3.setMemberGradeInfoName("");
        hsaOperationMemberInfo3.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo3.setMemberIntegral(0);
        hsaOperationMemberInfo3.setMemberGrowthValue(0);
        hsaOperationMemberInfo3.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO3.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo3));
        gradeChangeDTO3.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO3.setSourceType(0);
        gradeChangeDTO3.setIsRefresh(0);
        gradeChangeDTO3.setRoleType("roleType");
        gradeChangeDTO3.setSystem(0);
        gradeChangeDTO3.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getMemberGradePayRecordReqVO(gradeChangeDTO3, Arrays.asList("value")))
                .thenReturn(memberGradePayRecordReqVO);

        when(mockCacheService.cleanToken("cacheKey")).thenReturn(false);

        // Run the test
        hsaMemberGradeChangeDetailServiceImplUnderTest.memberGradeChange(gradeChangeDTO);

        // Verify the results
        // Confirm GradeChangeAssembler.getMemberGradeChangeDetail(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo4 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo4.setGuid("guid");
        hsaOperationMemberInfo4.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo4.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo4.setMemberGradeInfoName("");
        hsaOperationMemberInfo4.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo4.setMemberIntegral(0);
        hsaOperationMemberInfo4.setMemberGrowthValue(0);
        hsaOperationMemberInfo4.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaMemberGradeInfo afterMemberGradeInfo = new HsaMemberGradeInfo();
        afterMemberGradeInfo.setGuid("");
        afterMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo.setVipGrade(0);
        afterMemberGradeInfo.setName("");
        afterMemberGradeInfo.setGrowthValue(0);
        afterMemberGradeInfo.setIsDelete(0);
        afterMemberGradeInfo.setEffective(0);
        afterMemberGradeInfo.setRoleType("roleType");
        afterMemberGradeInfo.setUpgradeType(0);
        final HsaMemberGradeInfo beforeMemberGradeInfo = new HsaMemberGradeInfo();
        beforeMemberGradeInfo.setGuid("");
        beforeMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        beforeMemberGradeInfo.setVipGrade(0);
        beforeMemberGradeInfo.setName("");
        beforeMemberGradeInfo.setGrowthValue(0);
        beforeMemberGradeInfo.setIsDelete(0);
        beforeMemberGradeInfo.setEffective(0);
        beforeMemberGradeInfo.setRoleType("roleType");
        beforeMemberGradeInfo.setUpgradeType(0);
        final HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail = new HsaMemberGradeChangeDetail();
        hsaMemberGradeChangeDetail.setGuid("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");
        hsaMemberGradeChangeDetail.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeChangeDetail.setMemberInfoGuid("guid");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeName("");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeGuid("");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail.setAfterChangeGradeName("");
        hsaMemberGradeChangeDetail.setAfterChangeGradeGuid("");
        hsaMemberGradeChangeDetail.setAfterChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail.setGradeChangeType(0);
        hsaMemberGradeChangeDetail.setCurrentGrowthValue(0);
        hsaMemberGradeChangeDetail.setIsShowApplet(0);
        hsaMemberGradeChangeDetail.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail.setAfterChangeGradeIcon("");
        hsaMemberGradeChangeDetail.setAfterChangeBackgroundColor("");
        hsaMemberGradeChangeDetail.setAfterChangeGrowthValue(0);
        verify(mockGradeChangeAssembler).getMemberGradeChangeDetail(hsaOperationMemberInfo4, afterMemberGradeInfo,
                beforeMemberGradeInfo, hsaMemberGradeChangeDetail, LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Confirm HsaMemberGradeChangeDetailService.saveBatch(...).
        final HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail1 = new HsaMemberGradeChangeDetail();
        hsaMemberGradeChangeDetail1.setGuid("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");
        hsaMemberGradeChangeDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeChangeDetail1.setMemberInfoGuid("guid");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeName("");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeGuid("");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail1.setAfterChangeGradeName("");
        hsaMemberGradeChangeDetail1.setAfterChangeGradeGuid("");
        hsaMemberGradeChangeDetail1.setAfterChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail1.setGradeChangeType(0);
        hsaMemberGradeChangeDetail1.setCurrentGrowthValue(0);
        hsaMemberGradeChangeDetail1.setIsShowApplet(0);
        hsaMemberGradeChangeDetail1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail1.setAfterChangeGradeIcon("");
        hsaMemberGradeChangeDetail1.setAfterChangeBackgroundColor("");
        hsaMemberGradeChangeDetail1.setAfterChangeGrowthValue(0);
        final List<HsaMemberGradeChangeDetail> collection = Arrays.asList(hsaMemberGradeChangeDetail1);
        verify(mockHsaMemberGradeChangeDetailService).saveBatch(collection, 1000);

        // Confirm HsaMemberGradeRightsRecordService.saveBatch(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord2 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord2.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord2.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord2.setRightsType(0);
        final List<HsaMemberGradeRightsRecord> collection1 = Arrays.asList(hsaMemberGradeRightsRecord2);
        verify(mockHsaMemberGradeRightsRecordService).saveBatch(collection1, 1000);

        // Confirm HsaGrowthValueDetailService.saveBatch(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail1 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail1.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail1.setId(0L);
        hsaGrowthValueDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail1.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail1.setMemberInfoGuid("memberInfoGuid");
        final List<HsaGrowthValueDetail> collection2 = Arrays.asList(hsaGrowthValueDetail1);
        verify(mockHsaGrowthValueDetailService).saveBatch(collection2, 1000);

        // Confirm HsaIntegralDetailService.saveBatch(...).
        final HsaIntegralDetail hsaIntegralDetail1 = new HsaIntegralDetail();
        hsaIntegralDetail1.setId(0L);
        hsaIntegralDetail1.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail1.setIntegralTaskGuid("integralTaskGuid");
        final List<HsaIntegralDetail> collection3 = Arrays.asList(hsaIntegralDetail1);
        verify(mockHsaIntegralDetailService).saveBatch(collection3, 1000);

        // Confirm HsaMemberEquitiesReceiveRecordServiceImpl.saveBatch(...).
        final HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord1 = new HsaMemberEquitiesReceiveRecord();
        hsaMemberEquitiesReceiveRecord1.setGuid("a782bb73-1961-4997-8c42-d00d348d6623");
        hsaMemberEquitiesReceiveRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberEquitiesReceiveRecord1.setMemberGradeGuid("memberGradeGuid");
        hsaMemberEquitiesReceiveRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberEquitiesReceiveRecord1.setGradeEquitiesGuid("gradeEquitiesGuid");
        final List<HsaMemberEquitiesReceiveRecord> entityList = Arrays.asList(hsaMemberEquitiesReceiveRecord1);
        verify(mockHsaMemberEquitiesReceiveRecordService).saveBatch(entityList, 1000);

        // Confirm HsaOperationMemberInfoMapper.batchUpdateGrowth(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo5 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo5.setGuid("guid");
        hsaOperationMemberInfo5.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo5.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo5.setMemberGradeInfoName("");
        hsaOperationMemberInfo5.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo5.setMemberIntegral(0);
        hsaOperationMemberInfo5.setMemberGrowthValue(0);
        hsaOperationMemberInfo5.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo5);
        verify(mockHsaOperationMemberInfoMapper).batchUpdateGrowth(hsaOperationMemberInfos);
        verify(mockMemberGradeRelationService).batchDeleteMemberGrade(Arrays.asList("value"), "roleType");

        // Confirm IHsaMemberGradeRelationService.batchSaveAndUpdateRelation(...).
        final MemberGradeRelationDTO relationDTO = new MemberGradeRelationDTO();
        relationDTO.setMemberGuidList(Arrays.asList("value"));
        relationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        relationDTO.setRoleType("roleType");
        relationDTO.setNum(0);
        relationDTO.setUnit(0);
        verify(mockMemberGradeRelationService).batchSaveAndUpdateRelation(relationDTO);

        // Confirm IHsaMemberGradePayRecordService.batchAdd(...).
        final MemberGradePayRecordReqVO gradePayRecordReqVO = new MemberGradePayRecordReqVO();
        gradePayRecordReqVO.setMemberGuidList(Arrays.asList("value"));
        gradePayRecordReqVO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        gradePayRecordReqVO.setRoleType("roleType");
        gradePayRecordReqVO.setNum(0);
        gradePayRecordReqVO.setUnit(0);
        verify(mockMemberGradePayRecordService).batchAdd(gradePayRecordReqVO);

        // Confirm HsaOperationMemberInfoMapper.batchUpdateIntegral(...).
        final UpdateIntegral updateIntegral = new UpdateIntegral();
        updateIntegral.setGuid("32f2621a-4a96-4713-abb6-d3f2e4b4e56f");
        updateIntegral.setIntegralValue(0);
        final List<UpdateIntegral> updateMemberIntegralList = Arrays.asList(updateIntegral);
        verify(mockHsaOperationMemberInfoMapper).batchUpdateIntegral(updateMemberIntegralList);
    }

    @Test
    public void testMemberGradeChange_HsaMemberEquitiesReceiveRecordMapperReturnsNoItems() {
        // Setup
        final MemberGradeChangeDTO gradeChangeDTO = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("guid");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo.setMemberGradeInfoName("");
        hsaOperationMemberInfo.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo.setMemberIntegral(0);
        hsaOperationMemberInfo.setMemberGrowthValue(0);
        hsaOperationMemberInfo.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo));
        gradeChangeDTO.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO.setSourceType(0);
        gradeChangeDTO.setIsRefresh(0);
        gradeChangeDTO.setRoleType("roleType");
        gradeChangeDTO.setSystem(0);
        gradeChangeDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");

        // Configure HsaControlledGradeStateMapper.selectOne(...).
        final HsaControlledGradeState hsaControlledGradeState = new HsaControlledGradeState();
        hsaControlledGradeState.setId(0L);
        hsaControlledGradeState.setOperSubjectGuid("operSubjectGuid");
        hsaControlledGradeState.setState(0);
        hsaControlledGradeState.setIsDelete(false);
        hsaControlledGradeState.setRoleType("roleType");
        when(mockHsaControlledGradeStateMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaControlledGradeState);

        // Configure HsaMemberGradeInfoMapper.selectList(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("");
        hsaMemberGradeInfo.setGrowthValue(0);
        hsaMemberGradeInfo.setIsDelete(0);
        hsaMemberGradeInfo.setEffective(0);
        hsaMemberGradeInfo.setRoleType("roleType");
        hsaMemberGradeInfo.setUpgradeType(0);
        final List<HsaMemberGradeInfo> hsaMemberGradeInfos = Arrays.asList(hsaMemberGradeInfo);
        when(mockHsaMemberGradeInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfos);

        // Configure IHsaMemberGradeRelationService.listByMemberGuidList(...).
        final HsaMemberGradeRelation hsaMemberGradeRelation = new HsaMemberGradeRelation();
        hsaMemberGradeRelation.setId(0);
        hsaMemberGradeRelation.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRelation.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRelation.setMemberInfoGradeGuid("memberInfoGradeGuid");
        hsaMemberGradeRelation.setIsEnable(false);
        final List<HsaMemberGradeRelation> hsaMemberGradeRelations = Arrays.asList(hsaMemberGradeRelation);
        when(mockMemberGradeRelationService.listByMemberGuidList(Arrays.asList("value"), "roleType"))
                .thenReturn(hsaMemberGradeRelations);

        // Configure HsaGradeEquitiesService.list(...).
        final List<HsaBusinessEquities> hsaBusinessEquities = Arrays.asList(HsaBusinessEquities.builder()
                .guid("guid")
                .memberGradeInfoGuid("memberGradeInfoGuid")
                .equitiesRuleType(0)
                .totalGiveNumber(0)
                .setPeriod(0)
                .isDelete(0)
                .effective(0)
                .operSubjectGuid("operSubjectGuid")
                .build());
        when(mockHsaGradeEquitiesService.list(any(LambdaQueryWrapper.class))).thenReturn(hsaBusinessEquities);

        // Configure HsaMemberGradeRightsRecordMapper.selectList(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord.setRightsType(0);
        final List<HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecords = Arrays.asList(hsaMemberGradeRightsRecord);
        when(mockHsaMemberGradeRightsRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberGradeRightsRecords);

        // Configure HsaGradeEquitiesMapper.selectList(...).
        final List<HsaBusinessEquities> hsaBusinessEquities1 = Arrays.asList(HsaBusinessEquities.builder()
                .guid("guid")
                .memberGradeInfoGuid("memberGradeInfoGuid")
                .equitiesRuleType(0)
                .totalGiveNumber(0)
                .setPeriod(0)
                .isDelete(0)
                .effective(0)
                .operSubjectGuid("operSubjectGuid")
                .build());
        when(mockHsaGradeEquitiesMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaBusinessEquities1);

        // Configure HsaGiftBagBaseInfoMapper.selectOne(...).
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo.setId(0L);
        hsaGiftBagBaseInfo.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo.setEquitiesType(0);
        hsaGiftBagBaseInfo.setEquitiesRule(0);
        when(mockHsaGiftBagBaseInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaGiftBagBaseInfo);

        // Configure HsaMemberGradeInfoMapper.selectOne(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo1 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo1.setGuid("");
        hsaMemberGradeInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo1.setVipGrade(0);
        hsaMemberGradeInfo1.setName("");
        hsaMemberGradeInfo1.setGrowthValue(0);
        hsaMemberGradeInfo1.setIsDelete(0);
        hsaMemberGradeInfo1.setEffective(0);
        hsaMemberGradeInfo1.setRoleType("roleType");
        hsaMemberGradeInfo1.setUpgradeType(0);
        when(mockHsaMemberGradeInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfo1);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");

        // Configure HsaGradeGiftBagMapper.selectList(...).
        final HsaGradeGiftBag hsaGradeGiftBag = new HsaGradeGiftBag();
        hsaGradeGiftBag.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag.setType(0);
        hsaGradeGiftBag.setValue(0);
        hsaGradeGiftBag.setIsDelete(0);
        hsaGradeGiftBag.setEffective(0);
        final List<HsaGradeGiftBag> hsaGradeGiftBags = Arrays.asList(hsaGradeGiftBag);
        when(mockHsaGradeGiftBagMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaGradeGiftBags);

        // Configure GradeChangeAssembler.getHsaMemberGradeRightsRecord(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord1 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord1.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord1.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord1.setRightsType(0);
        final GradeRightsProcessorDTO gradeRightsProcessorDTO = new GradeRightsProcessorDTO();
        gradeRightsProcessorDTO.setUpdateMemberIntegralMap(new HashMap<>());
        gradeRightsProcessorDTO.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo1 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo1.setEquitiesRule(0);
        gradeRightsProcessorDTO.setHsaGiftBagBaseInfo(hsaGiftBagBaseInfo1);
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord2 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord2.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord2.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord2.setRightsType(0);
        gradeRightsProcessorDTO.setHsaMemberGradeRightsRecords(Arrays.asList(hsaMemberGradeRightsRecord2));
        final HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        gradeRightsProcessorDTO.setHsaGrowthValueDetails(Arrays.asList(hsaGrowthValueDetail));
        final HsaIntegralDetail hsaIntegralDetail = new HsaIntegralDetail();
        gradeRightsProcessorDTO.setHsaIntegralDetails(Arrays.asList(hsaIntegralDetail));
        gradeRightsProcessorDTO.setSourceType(0);
        gradeRightsProcessorDTO.setOperSubjectGuid("operSubjectGuid");
        final HsaOperationMemberInfo operationMemberInfo = new HsaOperationMemberInfo();
        operationMemberInfo.setGuid("guid");
        operationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo.setMemberGradeInfoGuid("");
        operationMemberInfo.setMemberGradeInfoName("");
        operationMemberInfo.setOperatorGuid("operatorGuid");
        operationMemberInfo.setMemberIntegral(0);
        operationMemberInfo.setMemberGrowthValue(0);
        operationMemberInfo.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag1 = new HsaGradeGiftBag();
        hsaGradeGiftBag1.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag1.setType(0);
        hsaGradeGiftBag1.setValue(0);
        hsaGradeGiftBag1.setIsDelete(0);
        hsaGradeGiftBag1.setEffective(0);
        when(mockGradeChangeAssembler.getHsaMemberGradeRightsRecord(gradeRightsProcessorDTO, operationMemberInfo,
                hsaGradeGiftBag1)).thenReturn(hsaMemberGradeRightsRecord1);

        // Configure GradeChangeAssembler.getHsaGrowthValueDetail(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail1 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail1.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail1.setId(0L);
        hsaGrowthValueDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail1.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail1.setMemberInfoGuid("memberInfoGuid");
        final HsaOperationMemberInfo operationMemberInfo1 = new HsaOperationMemberInfo();
        operationMemberInfo1.setGuid("guid");
        operationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo1.setMemberGradeInfoGuid("");
        operationMemberInfo1.setMemberGradeInfoName("");
        operationMemberInfo1.setOperatorGuid("operatorGuid");
        operationMemberInfo1.setMemberIntegral(0);
        operationMemberInfo1.setMemberGrowthValue(0);
        operationMemberInfo1.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag2 = new HsaGradeGiftBag();
        hsaGradeGiftBag2.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag2.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag2.setType(0);
        hsaGradeGiftBag2.setValue(0);
        hsaGradeGiftBag2.setIsDelete(0);
        hsaGradeGiftBag2.setEffective(0);
        final HsaMemberGradeInfo hsaMemberGradeInfo2 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo2.setGuid("");
        hsaMemberGradeInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo2.setVipGrade(0);
        hsaMemberGradeInfo2.setName("");
        hsaMemberGradeInfo2.setGrowthValue(0);
        hsaMemberGradeInfo2.setIsDelete(0);
        hsaMemberGradeInfo2.setEffective(0);
        hsaMemberGradeInfo2.setRoleType("roleType");
        hsaMemberGradeInfo2.setUpgradeType(0);
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo2 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo2.setId(0L);
        hsaGiftBagBaseInfo2.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo2.setEquitiesType(0);
        hsaGiftBagBaseInfo2.setEquitiesRule(0);
        final HsaMemberGradeInfo afterMemberGradeInfo = new HsaMemberGradeInfo();
        afterMemberGradeInfo.setGuid("");
        afterMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo.setVipGrade(0);
        afterMemberGradeInfo.setName("");
        afterMemberGradeInfo.setGrowthValue(0);
        afterMemberGradeInfo.setIsDelete(0);
        afterMemberGradeInfo.setEffective(0);
        afterMemberGradeInfo.setRoleType("roleType");
        afterMemberGradeInfo.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaGrowthValueDetail(operationMemberInfo1, 0, "operSubjectGuid",
                hsaGradeGiftBag2, hsaMemberGradeInfo2, hsaGiftBagBaseInfo2, afterMemberGradeInfo))
                .thenReturn(hsaGrowthValueDetail1);

        // Configure GradeChangeAssembler.getMemberGradeRightsRecord(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord3 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord3.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord3.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord3.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord3.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord3.setRightsType(0);
        final HsaOperationMemberInfo operationMemberInfo2 = new HsaOperationMemberInfo();
        operationMemberInfo2.setGuid("guid");
        operationMemberInfo2.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo2.setMemberGradeInfoGuid("");
        operationMemberInfo2.setMemberGradeInfoName("");
        operationMemberInfo2.setOperatorGuid("operatorGuid");
        operationMemberInfo2.setMemberIntegral(0);
        operationMemberInfo2.setMemberGrowthValue(0);
        operationMemberInfo2.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockGradeChangeAssembler.getMemberGradeRightsRecord(operationMemberInfo2, "operSubjectGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build())).thenReturn(hsaMemberGradeRightsRecord3);

        when(mockHsaMemberEquitiesReceiveRecordMapper.selectAllGrowthValue("guid", 0))
                .thenReturn(Collections.emptyList());

        // Configure GradeChangeAssembler.getHsaIntegralValueDetail(...).
        final HsaIntegralDetail hsaIntegralDetail1 = new HsaIntegralDetail();
        hsaIntegralDetail1.setId(0L);
        hsaIntegralDetail1.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail1.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail1.setIntegralTaskGuid("integralTaskGuid");
        final HsaMemberGradeInfo hsaMemberGradeInfo3 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo3.setGuid("");
        hsaMemberGradeInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo3.setVipGrade(0);
        hsaMemberGradeInfo3.setName("");
        hsaMemberGradeInfo3.setGrowthValue(0);
        hsaMemberGradeInfo3.setIsDelete(0);
        hsaMemberGradeInfo3.setEffective(0);
        hsaMemberGradeInfo3.setRoleType("roleType");
        hsaMemberGradeInfo3.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaIntegralValueDetail("guid", 0, "operSubjectGuid", hsaMemberGradeInfo3,
                HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build(), 0, 0)).thenReturn(hsaIntegralDetail1);

        // Configure GradeChangeAssembler.getHsaGrowthValueDetail(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail2 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail2.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail2.setId(0L);
        hsaGrowthValueDetail2.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail2.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail2.setMemberInfoGuid("memberInfoGuid");
        final HsaMemberGradeInfo hsaMemberGradeInfo4 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo4.setGuid("");
        hsaMemberGradeInfo4.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo4.setVipGrade(0);
        hsaMemberGradeInfo4.setName("");
        hsaMemberGradeInfo4.setGrowthValue(0);
        hsaMemberGradeInfo4.setIsDelete(0);
        hsaMemberGradeInfo4.setEffective(0);
        hsaMemberGradeInfo4.setRoleType("roleType");
        hsaMemberGradeInfo4.setUpgradeType(0);
        final MemberGradeChangeDTO gradeChangeDTO1 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo1 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo1.setGuid("guid");
        hsaOperationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo1.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo1.setMemberGradeInfoName("");
        hsaOperationMemberInfo1.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo1.setMemberIntegral(0);
        hsaOperationMemberInfo1.setMemberGrowthValue(0);
        hsaOperationMemberInfo1.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO1.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo1));
        gradeChangeDTO1.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO1.setSourceType(0);
        gradeChangeDTO1.setIsRefresh(0);
        gradeChangeDTO1.setRoleType("roleType");
        gradeChangeDTO1.setSystem(0);
        gradeChangeDTO1.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getHsaGrowthValueDetail("guid", 0, hsaMemberGradeInfo4,
                HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build(), 0, 0, gradeChangeDTO1)).thenReturn(hsaGrowthValueDetail2);

        // Configure GradeChangeAssembler.getHsaMemberEquitiesReceiveRecord(...).
        final HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord = new HsaMemberEquitiesReceiveRecord();
        hsaMemberEquitiesReceiveRecord.setGuid("a782bb73-1961-4997-8c42-d00d348d6623");
        hsaMemberEquitiesReceiveRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberEquitiesReceiveRecord.setMemberGradeGuid("memberGradeGuid");
        hsaMemberEquitiesReceiveRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberEquitiesReceiveRecord.setGradeEquitiesGuid("gradeEquitiesGuid");
        when(mockGradeChangeAssembler.getHsaMemberEquitiesReceiveRecord("operatorGuid", "guid", "", "guid", 0,
                0)).thenReturn(hsaMemberEquitiesReceiveRecord);

        // Configure GradeChangeAssembler.getMemberGradeRelationDTO(...).
        final MemberGradeRelationDTO memberGradeRelationDTO = new MemberGradeRelationDTO();
        memberGradeRelationDTO.setMemberGuidList(Arrays.asList("value"));
        memberGradeRelationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberGradeRelationDTO.setRoleType("roleType");
        memberGradeRelationDTO.setNum(0);
        memberGradeRelationDTO.setUnit(0);
        final MemberGradeChangeDTO gradeChangeDTO2 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo2 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo2.setGuid("guid");
        hsaOperationMemberInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo2.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo2.setMemberGradeInfoName("");
        hsaOperationMemberInfo2.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo2.setMemberIntegral(0);
        hsaOperationMemberInfo2.setMemberGrowthValue(0);
        hsaOperationMemberInfo2.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO2.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo2));
        gradeChangeDTO2.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO2.setSourceType(0);
        gradeChangeDTO2.setIsRefresh(0);
        gradeChangeDTO2.setRoleType("roleType");
        gradeChangeDTO2.setSystem(0);
        gradeChangeDTO2.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getMemberGradeRelationDTO(gradeChangeDTO2, Arrays.asList("value")))
                .thenReturn(memberGradeRelationDTO);

        // Configure GradeChangeAssembler.getMemberGradePayRecordReqVO(...).
        final MemberGradePayRecordReqVO memberGradePayRecordReqVO = new MemberGradePayRecordReqVO();
        memberGradePayRecordReqVO.setMemberGuidList(Arrays.asList("value"));
        memberGradePayRecordReqVO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberGradePayRecordReqVO.setRoleType("roleType");
        memberGradePayRecordReqVO.setNum(0);
        memberGradePayRecordReqVO.setUnit(0);
        final MemberGradeChangeDTO gradeChangeDTO3 = new MemberGradeChangeDTO();
        final HsaOperationMemberInfo hsaOperationMemberInfo3 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo3.setGuid("guid");
        hsaOperationMemberInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo3.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo3.setMemberGradeInfoName("");
        hsaOperationMemberInfo3.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo3.setMemberIntegral(0);
        hsaOperationMemberInfo3.setMemberGrowthValue(0);
        hsaOperationMemberInfo3.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        gradeChangeDTO3.setHsaOperationMemberInfos(Arrays.asList(hsaOperationMemberInfo3));
        gradeChangeDTO3.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO3.setSourceType(0);
        gradeChangeDTO3.setIsRefresh(0);
        gradeChangeDTO3.setRoleType("roleType");
        gradeChangeDTO3.setSystem(0);
        gradeChangeDTO3.setMemberInfoGradeGuid("memberInfoGradeGuid");
        when(mockGradeChangeAssembler.getMemberGradePayRecordReqVO(gradeChangeDTO3, Arrays.asList("value")))
                .thenReturn(memberGradePayRecordReqVO);

        when(mockCacheService.cleanToken("cacheKey")).thenReturn(false);

        // Run the test
        hsaMemberGradeChangeDetailServiceImplUnderTest.memberGradeChange(gradeChangeDTO);

        // Verify the results
        // Confirm GradeChangeAssembler.getMemberGradeChangeDetail(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo4 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo4.setGuid("guid");
        hsaOperationMemberInfo4.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo4.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo4.setMemberGradeInfoName("");
        hsaOperationMemberInfo4.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo4.setMemberIntegral(0);
        hsaOperationMemberInfo4.setMemberGrowthValue(0);
        hsaOperationMemberInfo4.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaMemberGradeInfo afterMemberGradeInfo1 = new HsaMemberGradeInfo();
        afterMemberGradeInfo1.setGuid("");
        afterMemberGradeInfo1.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo1.setVipGrade(0);
        afterMemberGradeInfo1.setName("");
        afterMemberGradeInfo1.setGrowthValue(0);
        afterMemberGradeInfo1.setIsDelete(0);
        afterMemberGradeInfo1.setEffective(0);
        afterMemberGradeInfo1.setRoleType("roleType");
        afterMemberGradeInfo1.setUpgradeType(0);
        final HsaMemberGradeInfo beforeMemberGradeInfo = new HsaMemberGradeInfo();
        beforeMemberGradeInfo.setGuid("");
        beforeMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        beforeMemberGradeInfo.setVipGrade(0);
        beforeMemberGradeInfo.setName("");
        beforeMemberGradeInfo.setGrowthValue(0);
        beforeMemberGradeInfo.setIsDelete(0);
        beforeMemberGradeInfo.setEffective(0);
        beforeMemberGradeInfo.setRoleType("roleType");
        beforeMemberGradeInfo.setUpgradeType(0);
        final HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail = new HsaMemberGradeChangeDetail();
        hsaMemberGradeChangeDetail.setGuid("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");
        hsaMemberGradeChangeDetail.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeChangeDetail.setMemberInfoGuid("guid");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeName("");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeGuid("");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail.setAfterChangeGradeName("");
        hsaMemberGradeChangeDetail.setAfterChangeGradeGuid("");
        hsaMemberGradeChangeDetail.setAfterChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail.setGradeChangeType(0);
        hsaMemberGradeChangeDetail.setCurrentGrowthValue(0);
        hsaMemberGradeChangeDetail.setIsShowApplet(0);
        hsaMemberGradeChangeDetail.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail.setAfterChangeGradeIcon("");
        hsaMemberGradeChangeDetail.setAfterChangeBackgroundColor("");
        hsaMemberGradeChangeDetail.setAfterChangeGrowthValue(0);
        verify(mockGradeChangeAssembler).getMemberGradeChangeDetail(hsaOperationMemberInfo4, afterMemberGradeInfo1,
                beforeMemberGradeInfo, hsaMemberGradeChangeDetail, LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Confirm HsaMemberGradeChangeDetailService.saveBatch(...).
        final HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail1 = new HsaMemberGradeChangeDetail();
        hsaMemberGradeChangeDetail1.setGuid("ac2005cc-a9f7-4139-acb2-0e7434e2bb96");
        hsaMemberGradeChangeDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeChangeDetail1.setMemberInfoGuid("guid");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeName("");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeGuid("");
        hsaMemberGradeChangeDetail1.setBeforeChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail1.setAfterChangeGradeName("");
        hsaMemberGradeChangeDetail1.setAfterChangeGradeGuid("");
        hsaMemberGradeChangeDetail1.setAfterChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail1.setGradeChangeType(0);
        hsaMemberGradeChangeDetail1.setCurrentGrowthValue(0);
        hsaMemberGradeChangeDetail1.setIsShowApplet(0);
        hsaMemberGradeChangeDetail1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail1.setAfterChangeGradeIcon("");
        hsaMemberGradeChangeDetail1.setAfterChangeBackgroundColor("");
        hsaMemberGradeChangeDetail1.setAfterChangeGrowthValue(0);
        final List<HsaMemberGradeChangeDetail> collection = Arrays.asList(hsaMemberGradeChangeDetail1);
        verify(mockHsaMemberGradeChangeDetailService).saveBatch(collection, 1000);

        // Confirm HsaMemberGradeRightsRecordService.saveBatch(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord4 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord4.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord4.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord4.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord4.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord4.setRightsType(0);
        final List<HsaMemberGradeRightsRecord> collection1 = Arrays.asList(hsaMemberGradeRightsRecord4);
        verify(mockHsaMemberGradeRightsRecordService).saveBatch(collection1, 1000);

        // Confirm HsaGrowthValueDetailService.saveBatch(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail3 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail3.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail3.setId(0L);
        hsaGrowthValueDetail3.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail3.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail3.setMemberInfoGuid("memberInfoGuid");
        final List<HsaGrowthValueDetail> collection2 = Arrays.asList(hsaGrowthValueDetail3);
        verify(mockHsaGrowthValueDetailService).saveBatch(collection2, 1000);

        // Confirm HsaIntegralDetailService.saveBatch(...).
        final HsaIntegralDetail hsaIntegralDetail2 = new HsaIntegralDetail();
        hsaIntegralDetail2.setId(0L);
        hsaIntegralDetail2.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail2.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail2.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail2.setIntegralTaskGuid("integralTaskGuid");
        final List<HsaIntegralDetail> collection3 = Arrays.asList(hsaIntegralDetail2);
        verify(mockHsaIntegralDetailService).saveBatch(collection3, 1000);

        // Confirm HsaMemberEquitiesReceiveRecordServiceImpl.saveBatch(...).
        final HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord1 = new HsaMemberEquitiesReceiveRecord();
        hsaMemberEquitiesReceiveRecord1.setGuid("a782bb73-1961-4997-8c42-d00d348d6623");
        hsaMemberEquitiesReceiveRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberEquitiesReceiveRecord1.setMemberGradeGuid("memberGradeGuid");
        hsaMemberEquitiesReceiveRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberEquitiesReceiveRecord1.setGradeEquitiesGuid("gradeEquitiesGuid");
        final List<HsaMemberEquitiesReceiveRecord> entityList = Arrays.asList(hsaMemberEquitiesReceiveRecord1);
        verify(mockHsaMemberEquitiesReceiveRecordService).saveBatch(entityList, 1000);

        // Confirm HsaOperationMemberInfoMapper.batchUpdateGrowth(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo5 = new HsaOperationMemberInfo();
        hsaOperationMemberInfo5.setGuid("guid");
        hsaOperationMemberInfo5.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo5.setMemberGradeInfoGuid("");
        hsaOperationMemberInfo5.setMemberGradeInfoName("");
        hsaOperationMemberInfo5.setOperatorGuid("operatorGuid");
        hsaOperationMemberInfo5.setMemberIntegral(0);
        hsaOperationMemberInfo5.setMemberGrowthValue(0);
        hsaOperationMemberInfo5.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo5);
        verify(mockHsaOperationMemberInfoMapper).batchUpdateGrowth(hsaOperationMemberInfos);
        verify(mockMemberGradeRelationService).batchDeleteMemberGrade(Arrays.asList("value"), "roleType");

        // Confirm IHsaMemberGradeRelationService.batchSaveAndUpdateRelation(...).
        final MemberGradeRelationDTO relationDTO = new MemberGradeRelationDTO();
        relationDTO.setMemberGuidList(Arrays.asList("value"));
        relationDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        relationDTO.setRoleType("roleType");
        relationDTO.setNum(0);
        relationDTO.setUnit(0);
        verify(mockMemberGradeRelationService).batchSaveAndUpdateRelation(relationDTO);

        // Confirm IHsaMemberGradePayRecordService.batchAdd(...).
        final MemberGradePayRecordReqVO gradePayRecordReqVO = new MemberGradePayRecordReqVO();
        gradePayRecordReqVO.setMemberGuidList(Arrays.asList("value"));
        gradePayRecordReqVO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        gradePayRecordReqVO.setRoleType("roleType");
        gradePayRecordReqVO.setNum(0);
        gradePayRecordReqVO.setUnit(0);
        verify(mockMemberGradePayRecordService).batchAdd(gradePayRecordReqVO);

        // Confirm HsaOperationMemberInfoMapper.batchUpdateIntegral(...).
        final UpdateIntegral updateIntegral = new UpdateIntegral();
        updateIntegral.setGuid("32f2621a-4a96-4713-abb6-d3f2e4b4e56f");
        updateIntegral.setIntegralValue(0);
        final List<UpdateIntegral> updateMemberIntegralList = Arrays.asList(updateIntegral);
        verify(mockHsaOperationMemberInfoMapper).batchUpdateIntegral(updateMemberIntegralList);
    }

    @Test
    public void testMemberGradeRightsProcessor1() {
        // Setup
        final GradeRightsProcessorDTO gradeRightsProcessorDTO = new GradeRightsProcessorDTO();
        gradeRightsProcessorDTO.setUpdateMemberIntegralMap(new HashMap<>());
        gradeRightsProcessorDTO.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo.setEquitiesRule(0);
        gradeRightsProcessorDTO.setHsaGiftBagBaseInfo(hsaGiftBagBaseInfo);
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord.setRightsType(0);
        gradeRightsProcessorDTO.setHsaMemberGradeRightsRecords(Arrays.asList(hsaMemberGradeRightsRecord));
        final HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        gradeRightsProcessorDTO.setHsaGrowthValueDetails(Arrays.asList(hsaGrowthValueDetail));
        final HsaIntegralDetail hsaIntegralDetail = new HsaIntegralDetail();
        gradeRightsProcessorDTO.setHsaIntegralDetails(Arrays.asList(hsaIntegralDetail));
        gradeRightsProcessorDTO.setSourceType(0);
        gradeRightsProcessorDTO.setOperSubjectGuid("operSubjectGuid");

        final HsaOperationMemberInfo operationMemberInfo = new HsaOperationMemberInfo();
        operationMemberInfo.setGuid("guid");
        operationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo.setMemberGradeInfoGuid("");
        operationMemberInfo.setMemberGradeInfoName("");
        operationMemberInfo.setOperatorGuid("operatorGuid");
        operationMemberInfo.setMemberIntegral(0);
        operationMemberInfo.setMemberGrowthValue(0);
        operationMemberInfo.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final HsaMemberGradeInfo beforeChangeGradeVipGrade = new HsaMemberGradeInfo();
        beforeChangeGradeVipGrade.setGuid("");
        beforeChangeGradeVipGrade.setOperSubjectGuid("operSubjectGuid");
        beforeChangeGradeVipGrade.setVipGrade(0);
        beforeChangeGradeVipGrade.setName("");
        beforeChangeGradeVipGrade.setGrowthValue(0);
        beforeChangeGradeVipGrade.setIsDelete(0);
        beforeChangeGradeVipGrade.setEffective(0);
        beforeChangeGradeVipGrade.setRoleType("roleType");
        beforeChangeGradeVipGrade.setUpgradeType(0);

        final HsaMemberGradeInfo afterChangeGradeVipGrade = new HsaMemberGradeInfo();
        afterChangeGradeVipGrade.setGuid("");
        afterChangeGradeVipGrade.setOperSubjectGuid("operSubjectGuid");
        afterChangeGradeVipGrade.setVipGrade(0);
        afterChangeGradeVipGrade.setName("");
        afterChangeGradeVipGrade.setGrowthValue(0);
        afterChangeGradeVipGrade.setIsDelete(0);
        afterChangeGradeVipGrade.setEffective(0);
        afterChangeGradeVipGrade.setRoleType("roleType");
        afterChangeGradeVipGrade.setUpgradeType(0);

        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("");
        hsaMemberGradeInfo.setGrowthValue(0);
        hsaMemberGradeInfo.setIsDelete(0);
        hsaMemberGradeInfo.setEffective(0);
        hsaMemberGradeInfo.setRoleType("roleType");
        hsaMemberGradeInfo.setUpgradeType(0);
        final List<HsaMemberGradeInfo> hsaMemberGradeInfoList = Arrays.asList(hsaMemberGradeInfo);

        // Configure HsaGradeGiftBagMapper.selectList(...).
        final HsaGradeGiftBag hsaGradeGiftBag = new HsaGradeGiftBag();
        hsaGradeGiftBag.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag.setType(0);
        hsaGradeGiftBag.setValue(0);
        hsaGradeGiftBag.setIsDelete(0);
        hsaGradeGiftBag.setEffective(0);
        final List<HsaGradeGiftBag> hsaGradeGiftBags = Arrays.asList(hsaGradeGiftBag);
        when(mockHsaGradeGiftBagMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaGradeGiftBags);

        // Configure HsaMemberGradeRightsRecordMapper.selectList(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord1 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord1.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord1.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord1.setRightsType(0);
        final List<HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecords = Arrays.asList(hsaMemberGradeRightsRecord1);
        when(mockHsaMemberGradeRightsRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberGradeRightsRecords);

        // Configure GradeChangeAssembler.getHsaMemberGradeRightsRecord(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord2 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord2.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord2.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord2.setRightsType(0);
        final GradeRightsProcessorDTO gradeRightsProcessorDTO1 = new GradeRightsProcessorDTO();
        gradeRightsProcessorDTO1.setUpdateMemberIntegralMap(new HashMap<>());
        gradeRightsProcessorDTO1.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo1 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo1.setEquitiesRule(0);
        gradeRightsProcessorDTO1.setHsaGiftBagBaseInfo(hsaGiftBagBaseInfo1);
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord3 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord3.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord3.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord3.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord3.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord3.setRightsType(0);
        gradeRightsProcessorDTO1.setHsaMemberGradeRightsRecords(Arrays.asList(hsaMemberGradeRightsRecord3));
        final HsaGrowthValueDetail hsaGrowthValueDetail1 = new HsaGrowthValueDetail();
        gradeRightsProcessorDTO1.setHsaGrowthValueDetails(Arrays.asList(hsaGrowthValueDetail1));
        final HsaIntegralDetail hsaIntegralDetail1 = new HsaIntegralDetail();
        gradeRightsProcessorDTO1.setHsaIntegralDetails(Arrays.asList(hsaIntegralDetail1));
        gradeRightsProcessorDTO1.setSourceType(0);
        gradeRightsProcessorDTO1.setOperSubjectGuid("operSubjectGuid");
        final HsaOperationMemberInfo operationMemberInfo1 = new HsaOperationMemberInfo();
        operationMemberInfo1.setGuid("guid");
        operationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo1.setMemberGradeInfoGuid("");
        operationMemberInfo1.setMemberGradeInfoName("");
        operationMemberInfo1.setOperatorGuid("operatorGuid");
        operationMemberInfo1.setMemberIntegral(0);
        operationMemberInfo1.setMemberGrowthValue(0);
        operationMemberInfo1.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag1 = new HsaGradeGiftBag();
        hsaGradeGiftBag1.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag1.setType(0);
        hsaGradeGiftBag1.setValue(0);
        hsaGradeGiftBag1.setIsDelete(0);
        hsaGradeGiftBag1.setEffective(0);
        when(mockGradeChangeAssembler.getHsaMemberGradeRightsRecord(gradeRightsProcessorDTO1, operationMemberInfo1,
                hsaGradeGiftBag1)).thenReturn(hsaMemberGradeRightsRecord2);

        // Configure GradeChangeAssembler.getHsaGrowthValueDetail(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail2 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail2.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail2.setId(0L);
        hsaGrowthValueDetail2.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail2.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail2.setMemberInfoGuid("memberInfoGuid");
        final HsaOperationMemberInfo operationMemberInfo2 = new HsaOperationMemberInfo();
        operationMemberInfo2.setGuid("guid");
        operationMemberInfo2.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo2.setMemberGradeInfoGuid("");
        operationMemberInfo2.setMemberGradeInfoName("");
        operationMemberInfo2.setOperatorGuid("operatorGuid");
        operationMemberInfo2.setMemberIntegral(0);
        operationMemberInfo2.setMemberGrowthValue(0);
        operationMemberInfo2.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag2 = new HsaGradeGiftBag();
        hsaGradeGiftBag2.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag2.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag2.setType(0);
        hsaGradeGiftBag2.setValue(0);
        hsaGradeGiftBag2.setIsDelete(0);
        hsaGradeGiftBag2.setEffective(0);
        final HsaMemberGradeInfo hsaMemberGradeInfo1 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo1.setGuid("");
        hsaMemberGradeInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo1.setVipGrade(0);
        hsaMemberGradeInfo1.setName("");
        hsaMemberGradeInfo1.setGrowthValue(0);
        hsaMemberGradeInfo1.setIsDelete(0);
        hsaMemberGradeInfo1.setEffective(0);
        hsaMemberGradeInfo1.setRoleType("roleType");
        hsaMemberGradeInfo1.setUpgradeType(0);
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo2 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo2.setId(0L);
        hsaGiftBagBaseInfo2.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo2.setEquitiesType(0);
        hsaGiftBagBaseInfo2.setEquitiesRule(0);
        final HsaMemberGradeInfo afterMemberGradeInfo = new HsaMemberGradeInfo();
        afterMemberGradeInfo.setGuid("");
        afterMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo.setVipGrade(0);
        afterMemberGradeInfo.setName("");
        afterMemberGradeInfo.setGrowthValue(0);
        afterMemberGradeInfo.setIsDelete(0);
        afterMemberGradeInfo.setEffective(0);
        afterMemberGradeInfo.setRoleType("roleType");
        afterMemberGradeInfo.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaGrowthValueDetail(operationMemberInfo2, 0, "operSubjectGuid",
                hsaGradeGiftBag2, hsaMemberGradeInfo1, hsaGiftBagBaseInfo2, afterMemberGradeInfo))
                .thenReturn(hsaGrowthValueDetail2);

        // Configure GradeChangeAssembler.getHsaIntegralValueDetail(...).
        final HsaIntegralDetail hsaIntegralDetail2 = new HsaIntegralDetail();
        hsaIntegralDetail2.setId(0L);
        hsaIntegralDetail2.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail2.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail2.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail2.setIntegralTaskGuid("integralTaskGuid");
        final HsaOperationMemberInfo operationMemberInfo3 = new HsaOperationMemberInfo();
        operationMemberInfo3.setGuid("guid");
        operationMemberInfo3.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo3.setMemberGradeInfoGuid("");
        operationMemberInfo3.setMemberGradeInfoName("");
        operationMemberInfo3.setOperatorGuid("operatorGuid");
        operationMemberInfo3.setMemberIntegral(0);
        operationMemberInfo3.setMemberGrowthValue(0);
        operationMemberInfo3.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag3 = new HsaGradeGiftBag();
        hsaGradeGiftBag3.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag3.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag3.setType(0);
        hsaGradeGiftBag3.setValue(0);
        hsaGradeGiftBag3.setIsDelete(0);
        hsaGradeGiftBag3.setEffective(0);
        final HsaMemberGradeInfo hsaMemberGradeInfo2 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo2.setGuid("");
        hsaMemberGradeInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo2.setVipGrade(0);
        hsaMemberGradeInfo2.setName("");
        hsaMemberGradeInfo2.setGrowthValue(0);
        hsaMemberGradeInfo2.setIsDelete(0);
        hsaMemberGradeInfo2.setEffective(0);
        hsaMemberGradeInfo2.setRoleType("roleType");
        hsaMemberGradeInfo2.setUpgradeType(0);
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo3 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo3.setId(0L);
        hsaGiftBagBaseInfo3.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo3.setEquitiesType(0);
        hsaGiftBagBaseInfo3.setEquitiesRule(0);
        final HsaMemberGradeInfo afterMemberGradeInfo1 = new HsaMemberGradeInfo();
        afterMemberGradeInfo1.setGuid("");
        afterMemberGradeInfo1.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo1.setVipGrade(0);
        afterMemberGradeInfo1.setName("");
        afterMemberGradeInfo1.setGrowthValue(0);
        afterMemberGradeInfo1.setIsDelete(0);
        afterMemberGradeInfo1.setEffective(0);
        afterMemberGradeInfo1.setRoleType("roleType");
        afterMemberGradeInfo1.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaIntegralValueDetail(operationMemberInfo3, 0, "operSubjectGuid",
                hsaGradeGiftBag3, hsaMemberGradeInfo2, hsaGiftBagBaseInfo3, afterMemberGradeInfo1))
                .thenReturn(hsaIntegralDetail2);

        // Run the test
        hsaMemberGradeChangeDetailServiceImplUnderTest.memberGradeRightsProcessor(gradeRightsProcessorDTO,
                operationMemberInfo, beforeChangeGradeVipGrade, afterChangeGradeVipGrade, hsaMemberGradeInfoList);

        // Verify the results
    }

    @Test
    public void testMemberGradeRightsProcessor1_HsaGradeGiftBagMapperReturnsNoItems() {
        // Setup
        final GradeRightsProcessorDTO gradeRightsProcessorDTO = new GradeRightsProcessorDTO();
        gradeRightsProcessorDTO.setUpdateMemberIntegralMap(new HashMap<>());
        gradeRightsProcessorDTO.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo.setEquitiesRule(0);
        gradeRightsProcessorDTO.setHsaGiftBagBaseInfo(hsaGiftBagBaseInfo);
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord.setRightsType(0);
        gradeRightsProcessorDTO.setHsaMemberGradeRightsRecords(Arrays.asList(hsaMemberGradeRightsRecord));
        final HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        gradeRightsProcessorDTO.setHsaGrowthValueDetails(Arrays.asList(hsaGrowthValueDetail));
        final HsaIntegralDetail hsaIntegralDetail = new HsaIntegralDetail();
        gradeRightsProcessorDTO.setHsaIntegralDetails(Arrays.asList(hsaIntegralDetail));
        gradeRightsProcessorDTO.setSourceType(0);
        gradeRightsProcessorDTO.setOperSubjectGuid("operSubjectGuid");

        final HsaOperationMemberInfo operationMemberInfo = new HsaOperationMemberInfo();
        operationMemberInfo.setGuid("guid");
        operationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo.setMemberGradeInfoGuid("");
        operationMemberInfo.setMemberGradeInfoName("");
        operationMemberInfo.setOperatorGuid("operatorGuid");
        operationMemberInfo.setMemberIntegral(0);
        operationMemberInfo.setMemberGrowthValue(0);
        operationMemberInfo.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final HsaMemberGradeInfo beforeChangeGradeVipGrade = new HsaMemberGradeInfo();
        beforeChangeGradeVipGrade.setGuid("");
        beforeChangeGradeVipGrade.setOperSubjectGuid("operSubjectGuid");
        beforeChangeGradeVipGrade.setVipGrade(0);
        beforeChangeGradeVipGrade.setName("");
        beforeChangeGradeVipGrade.setGrowthValue(0);
        beforeChangeGradeVipGrade.setIsDelete(0);
        beforeChangeGradeVipGrade.setEffective(0);
        beforeChangeGradeVipGrade.setRoleType("roleType");
        beforeChangeGradeVipGrade.setUpgradeType(0);

        final HsaMemberGradeInfo afterChangeGradeVipGrade = new HsaMemberGradeInfo();
        afterChangeGradeVipGrade.setGuid("");
        afterChangeGradeVipGrade.setOperSubjectGuid("operSubjectGuid");
        afterChangeGradeVipGrade.setVipGrade(0);
        afterChangeGradeVipGrade.setName("");
        afterChangeGradeVipGrade.setGrowthValue(0);
        afterChangeGradeVipGrade.setIsDelete(0);
        afterChangeGradeVipGrade.setEffective(0);
        afterChangeGradeVipGrade.setRoleType("roleType");
        afterChangeGradeVipGrade.setUpgradeType(0);

        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("");
        hsaMemberGradeInfo.setGrowthValue(0);
        hsaMemberGradeInfo.setIsDelete(0);
        hsaMemberGradeInfo.setEffective(0);
        hsaMemberGradeInfo.setRoleType("roleType");
        hsaMemberGradeInfo.setUpgradeType(0);
        final List<HsaMemberGradeInfo> hsaMemberGradeInfoList = Arrays.asList(hsaMemberGradeInfo);
        when(mockHsaGradeGiftBagMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure HsaMemberGradeRightsRecordMapper.selectList(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord1 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord1.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord1.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord1.setRightsType(0);
        final List<HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecords = Arrays.asList(hsaMemberGradeRightsRecord1);
        when(mockHsaMemberGradeRightsRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaMemberGradeRightsRecords);

        // Run the test
        hsaMemberGradeChangeDetailServiceImplUnderTest.memberGradeRightsProcessor(gradeRightsProcessorDTO,
                operationMemberInfo, beforeChangeGradeVipGrade, afterChangeGradeVipGrade, hsaMemberGradeInfoList);

        // Verify the results
    }

    @Test
    public void testMemberGradeRightsProcessor1_HsaMemberGradeRightsRecordMapperReturnsNoItems() {
        // Setup
        final GradeRightsProcessorDTO gradeRightsProcessorDTO = new GradeRightsProcessorDTO();
        gradeRightsProcessorDTO.setUpdateMemberIntegralMap(new HashMap<>());
        gradeRightsProcessorDTO.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo.setEquitiesRule(0);
        gradeRightsProcessorDTO.setHsaGiftBagBaseInfo(hsaGiftBagBaseInfo);
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord.setRightsType(0);
        gradeRightsProcessorDTO.setHsaMemberGradeRightsRecords(Arrays.asList(hsaMemberGradeRightsRecord));
        final HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        gradeRightsProcessorDTO.setHsaGrowthValueDetails(Arrays.asList(hsaGrowthValueDetail));
        final HsaIntegralDetail hsaIntegralDetail = new HsaIntegralDetail();
        gradeRightsProcessorDTO.setHsaIntegralDetails(Arrays.asList(hsaIntegralDetail));
        gradeRightsProcessorDTO.setSourceType(0);
        gradeRightsProcessorDTO.setOperSubjectGuid("operSubjectGuid");

        final HsaOperationMemberInfo operationMemberInfo = new HsaOperationMemberInfo();
        operationMemberInfo.setGuid("guid");
        operationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo.setMemberGradeInfoGuid("");
        operationMemberInfo.setMemberGradeInfoName("");
        operationMemberInfo.setOperatorGuid("operatorGuid");
        operationMemberInfo.setMemberIntegral(0);
        operationMemberInfo.setMemberGrowthValue(0);
        operationMemberInfo.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final HsaMemberGradeInfo beforeChangeGradeVipGrade = new HsaMemberGradeInfo();
        beforeChangeGradeVipGrade.setGuid("");
        beforeChangeGradeVipGrade.setOperSubjectGuid("operSubjectGuid");
        beforeChangeGradeVipGrade.setVipGrade(0);
        beforeChangeGradeVipGrade.setName("");
        beforeChangeGradeVipGrade.setGrowthValue(0);
        beforeChangeGradeVipGrade.setIsDelete(0);
        beforeChangeGradeVipGrade.setEffective(0);
        beforeChangeGradeVipGrade.setRoleType("roleType");
        beforeChangeGradeVipGrade.setUpgradeType(0);

        final HsaMemberGradeInfo afterChangeGradeVipGrade = new HsaMemberGradeInfo();
        afterChangeGradeVipGrade.setGuid("");
        afterChangeGradeVipGrade.setOperSubjectGuid("operSubjectGuid");
        afterChangeGradeVipGrade.setVipGrade(0);
        afterChangeGradeVipGrade.setName("");
        afterChangeGradeVipGrade.setGrowthValue(0);
        afterChangeGradeVipGrade.setIsDelete(0);
        afterChangeGradeVipGrade.setEffective(0);
        afterChangeGradeVipGrade.setRoleType("roleType");
        afterChangeGradeVipGrade.setUpgradeType(0);

        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("");
        hsaMemberGradeInfo.setGrowthValue(0);
        hsaMemberGradeInfo.setIsDelete(0);
        hsaMemberGradeInfo.setEffective(0);
        hsaMemberGradeInfo.setRoleType("roleType");
        hsaMemberGradeInfo.setUpgradeType(0);
        final List<HsaMemberGradeInfo> hsaMemberGradeInfoList = Arrays.asList(hsaMemberGradeInfo);

        // Configure HsaGradeGiftBagMapper.selectList(...).
        final HsaGradeGiftBag hsaGradeGiftBag = new HsaGradeGiftBag();
        hsaGradeGiftBag.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag.setType(0);
        hsaGradeGiftBag.setValue(0);
        hsaGradeGiftBag.setIsDelete(0);
        hsaGradeGiftBag.setEffective(0);
        final List<HsaGradeGiftBag> hsaGradeGiftBags = Arrays.asList(hsaGradeGiftBag);
        when(mockHsaGradeGiftBagMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaGradeGiftBags);

        when(mockHsaMemberGradeRightsRecordMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure GradeChangeAssembler.getHsaMemberGradeRightsRecord(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord1 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord1.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord1.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord1.setRightsType(0);
        final GradeRightsProcessorDTO gradeRightsProcessorDTO1 = new GradeRightsProcessorDTO();
        gradeRightsProcessorDTO1.setUpdateMemberIntegralMap(new HashMap<>());
        gradeRightsProcessorDTO1.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo1 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo1.setEquitiesRule(0);
        gradeRightsProcessorDTO1.setHsaGiftBagBaseInfo(hsaGiftBagBaseInfo1);
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord2 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord2.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord2.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord2.setRightsType(0);
        gradeRightsProcessorDTO1.setHsaMemberGradeRightsRecords(Arrays.asList(hsaMemberGradeRightsRecord2));
        final HsaGrowthValueDetail hsaGrowthValueDetail1 = new HsaGrowthValueDetail();
        gradeRightsProcessorDTO1.setHsaGrowthValueDetails(Arrays.asList(hsaGrowthValueDetail1));
        final HsaIntegralDetail hsaIntegralDetail1 = new HsaIntegralDetail();
        gradeRightsProcessorDTO1.setHsaIntegralDetails(Arrays.asList(hsaIntegralDetail1));
        gradeRightsProcessorDTO1.setSourceType(0);
        gradeRightsProcessorDTO1.setOperSubjectGuid("operSubjectGuid");
        final HsaOperationMemberInfo operationMemberInfo1 = new HsaOperationMemberInfo();
        operationMemberInfo1.setGuid("guid");
        operationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo1.setMemberGradeInfoGuid("");
        operationMemberInfo1.setMemberGradeInfoName("");
        operationMemberInfo1.setOperatorGuid("operatorGuid");
        operationMemberInfo1.setMemberIntegral(0);
        operationMemberInfo1.setMemberGrowthValue(0);
        operationMemberInfo1.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag1 = new HsaGradeGiftBag();
        hsaGradeGiftBag1.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag1.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag1.setType(0);
        hsaGradeGiftBag1.setValue(0);
        hsaGradeGiftBag1.setIsDelete(0);
        hsaGradeGiftBag1.setEffective(0);
        when(mockGradeChangeAssembler.getHsaMemberGradeRightsRecord(gradeRightsProcessorDTO1, operationMemberInfo1,
                hsaGradeGiftBag1)).thenReturn(hsaMemberGradeRightsRecord1);

        // Configure GradeChangeAssembler.getHsaGrowthValueDetail(...).
        final HsaGrowthValueDetail hsaGrowthValueDetail2 = new HsaGrowthValueDetail();
        hsaGrowthValueDetail2.setGuid("cf706cf4-bc0c-44dd-a2ae-61ef046f87aa");
        hsaGrowthValueDetail2.setId(0L);
        hsaGrowthValueDetail2.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail2.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail2.setMemberInfoGuid("memberInfoGuid");
        final HsaOperationMemberInfo operationMemberInfo2 = new HsaOperationMemberInfo();
        operationMemberInfo2.setGuid("guid");
        operationMemberInfo2.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo2.setMemberGradeInfoGuid("");
        operationMemberInfo2.setMemberGradeInfoName("");
        operationMemberInfo2.setOperatorGuid("operatorGuid");
        operationMemberInfo2.setMemberIntegral(0);
        operationMemberInfo2.setMemberGrowthValue(0);
        operationMemberInfo2.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag2 = new HsaGradeGiftBag();
        hsaGradeGiftBag2.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag2.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag2.setType(0);
        hsaGradeGiftBag2.setValue(0);
        hsaGradeGiftBag2.setIsDelete(0);
        hsaGradeGiftBag2.setEffective(0);
        final HsaMemberGradeInfo hsaMemberGradeInfo1 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo1.setGuid("");
        hsaMemberGradeInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo1.setVipGrade(0);
        hsaMemberGradeInfo1.setName("");
        hsaMemberGradeInfo1.setGrowthValue(0);
        hsaMemberGradeInfo1.setIsDelete(0);
        hsaMemberGradeInfo1.setEffective(0);
        hsaMemberGradeInfo1.setRoleType("roleType");
        hsaMemberGradeInfo1.setUpgradeType(0);
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo2 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo2.setId(0L);
        hsaGiftBagBaseInfo2.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo2.setEquitiesType(0);
        hsaGiftBagBaseInfo2.setEquitiesRule(0);
        final HsaMemberGradeInfo afterMemberGradeInfo = new HsaMemberGradeInfo();
        afterMemberGradeInfo.setGuid("");
        afterMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo.setVipGrade(0);
        afterMemberGradeInfo.setName("");
        afterMemberGradeInfo.setGrowthValue(0);
        afterMemberGradeInfo.setIsDelete(0);
        afterMemberGradeInfo.setEffective(0);
        afterMemberGradeInfo.setRoleType("roleType");
        afterMemberGradeInfo.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaGrowthValueDetail(operationMemberInfo2, 0, "operSubjectGuid",
                hsaGradeGiftBag2, hsaMemberGradeInfo1, hsaGiftBagBaseInfo2, afterMemberGradeInfo))
                .thenReturn(hsaGrowthValueDetail2);

        // Configure GradeChangeAssembler.getHsaIntegralValueDetail(...).
        final HsaIntegralDetail hsaIntegralDetail2 = new HsaIntegralDetail();
        hsaIntegralDetail2.setId(0L);
        hsaIntegralDetail2.setGuid("42bb8fe6-d606-49f1-9398-137ba4887cfe");
        hsaIntegralDetail2.setOperSubjectGuid("operSubjectGuid");
        hsaIntegralDetail2.setMemberInfoGuid("memberInfoGuid");
        hsaIntegralDetail2.setIntegralTaskGuid("integralTaskGuid");
        final HsaOperationMemberInfo operationMemberInfo3 = new HsaOperationMemberInfo();
        operationMemberInfo3.setGuid("guid");
        operationMemberInfo3.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo3.setMemberGradeInfoGuid("");
        operationMemberInfo3.setMemberGradeInfoName("");
        operationMemberInfo3.setOperatorGuid("operatorGuid");
        operationMemberInfo3.setMemberIntegral(0);
        operationMemberInfo3.setMemberGrowthValue(0);
        operationMemberInfo3.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGradeGiftBag hsaGradeGiftBag3 = new HsaGradeGiftBag();
        hsaGradeGiftBag3.setGuid("df7d7d36-bd5a-4295-ae59-1132b342f15a");
        hsaGradeGiftBag3.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaGradeGiftBag3.setType(0);
        hsaGradeGiftBag3.setValue(0);
        hsaGradeGiftBag3.setIsDelete(0);
        hsaGradeGiftBag3.setEffective(0);
        final HsaMemberGradeInfo hsaMemberGradeInfo2 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo2.setGuid("");
        hsaMemberGradeInfo2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo2.setVipGrade(0);
        hsaMemberGradeInfo2.setName("");
        hsaMemberGradeInfo2.setGrowthValue(0);
        hsaMemberGradeInfo2.setIsDelete(0);
        hsaMemberGradeInfo2.setEffective(0);
        hsaMemberGradeInfo2.setRoleType("roleType");
        hsaMemberGradeInfo2.setUpgradeType(0);
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo3 = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo3.setId(0L);
        hsaGiftBagBaseInfo3.setGuid("f7fff942-5b01-4053-9d7a-1ab89c750351");
        hsaGiftBagBaseInfo3.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo3.setEquitiesType(0);
        hsaGiftBagBaseInfo3.setEquitiesRule(0);
        final HsaMemberGradeInfo afterMemberGradeInfo1 = new HsaMemberGradeInfo();
        afterMemberGradeInfo1.setGuid("");
        afterMemberGradeInfo1.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo1.setVipGrade(0);
        afterMemberGradeInfo1.setName("");
        afterMemberGradeInfo1.setGrowthValue(0);
        afterMemberGradeInfo1.setIsDelete(0);
        afterMemberGradeInfo1.setEffective(0);
        afterMemberGradeInfo1.setRoleType("roleType");
        afterMemberGradeInfo1.setUpgradeType(0);
        when(mockGradeChangeAssembler.getHsaIntegralValueDetail(operationMemberInfo3, 0, "operSubjectGuid",
                hsaGradeGiftBag3, hsaMemberGradeInfo2, hsaGiftBagBaseInfo3, afterMemberGradeInfo1))
                .thenReturn(hsaIntegralDetail2);

        // Run the test
        hsaMemberGradeChangeDetailServiceImplUnderTest.memberGradeRightsProcessor(gradeRightsProcessorDTO,
                operationMemberInfo, beforeChangeGradeVipGrade, afterChangeGradeVipGrade, hsaMemberGradeInfoList);

        // Verify the results
    }

    @Test
    public void testMemberGradeRightsProcessor2() {
        // Setup
        final HsaOperationMemberInfo operationMemberInfo = new HsaOperationMemberInfo();
        operationMemberInfo.setGuid("guid");
        operationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo.setMemberGradeInfoGuid("");
        operationMemberInfo.setMemberGradeInfoName("");
        operationMemberInfo.setOperatorGuid("operatorGuid");
        operationMemberInfo.setMemberIntegral(0);
        operationMemberInfo.setMemberGrowthValue(0);
        operationMemberInfo.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final List<HsaBusinessEquities> hsaBusinessEquities = Arrays.asList(HsaBusinessEquities.builder()
                .guid("guid")
                .memberGradeInfoGuid("memberGradeInfoGuid")
                .equitiesRuleType(0)
                .totalGiveNumber(0)
                .setPeriod(0)
                .isDelete(0)
                .effective(0)
                .operSubjectGuid("operSubjectGuid")
                .build());
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord.setRightsType(0);
        final List<HsaMemberGradeRightsRecord> equitiesRightsRecords = Arrays.asList(hsaMemberGradeRightsRecord);
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord1 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord1.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord1.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord1.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord1.setRightsType(0);
        final List<HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecords = Arrays.asList(hsaMemberGradeRightsRecord1);

        // Configure GradeChangeAssembler.getMemberGradeRightsRecord(...).
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord2 = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord2.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord2.setMemberGradeGuid("memberGradeGuid");
        hsaMemberGradeRightsRecord2.setRightsGuid("rightsGuid");
        hsaMemberGradeRightsRecord2.setRightsType(0);
        final HsaOperationMemberInfo operationMemberInfo1 = new HsaOperationMemberInfo();
        operationMemberInfo1.setGuid("guid");
        operationMemberInfo1.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo1.setMemberGradeInfoGuid("");
        operationMemberInfo1.setMemberGradeInfoName("");
        operationMemberInfo1.setOperatorGuid("operatorGuid");
        operationMemberInfo1.setMemberIntegral(0);
        operationMemberInfo1.setMemberGrowthValue(0);
        operationMemberInfo1.setUpgradeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockGradeChangeAssembler.getMemberGradeRightsRecord(operationMemberInfo1, "operSubjectGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), HsaBusinessEquities.builder()
                        .guid("guid")
                        .memberGradeInfoGuid("memberGradeInfoGuid")
                        .equitiesRuleType(0)
                        .totalGiveNumber(0)
                        .setPeriod(0)
                        .isDelete(0)
                        .effective(0)
                        .operSubjectGuid("operSubjectGuid")
                        .build())).thenReturn(hsaMemberGradeRightsRecord2);

        // Run the test
        hsaMemberGradeChangeDetailServiceImplUnderTest.memberGradeRightsProcessor(operationMemberInfo,
                hsaBusinessEquities, equitiesRightsRecords, "operSubjectGuid", hsaMemberGradeRightsRecords,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
    }

    @Test
    public void testCalculateExtraAmount2() {
        // Setup
        final HsaMemberGradeInfo expectedResult = new HsaMemberGradeInfo();
        expectedResult.setGuid("");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setVipGrade(0);
        expectedResult.setName("");
        expectedResult.setGrowthValue(0);
        expectedResult.setIsDelete(0);
        expectedResult.setEffective(0);
        expectedResult.setRoleType("roleType");
        expectedResult.setUpgradeType(0);

        // Configure HsaMemberGradeInfoMapper.selectList(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("");
        hsaMemberGradeInfo.setGrowthValue(0);
        hsaMemberGradeInfo.setIsDelete(0);
        hsaMemberGradeInfo.setEffective(0);
        hsaMemberGradeInfo.setRoleType("roleType");
        hsaMemberGradeInfo.setUpgradeType(0);
        final List<HsaMemberGradeInfo> hsaMemberGradeInfos = Arrays.asList(hsaMemberGradeInfo);
        when(mockHsaMemberGradeInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfos);

        // Run the test
        final HsaMemberGradeInfo result = hsaMemberGradeChangeDetailServiceImplUnderTest.calculateExtraAmount(0,
                "operSubjectGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCalculateExtraAmount2_HsaMemberGradeInfoMapperReturnsNoItems() {
        // Setup
        final HsaMemberGradeInfo expectedResult = new HsaMemberGradeInfo();
        expectedResult.setGuid("");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setVipGrade(0);
        expectedResult.setName("");
        expectedResult.setGrowthValue(0);
        expectedResult.setIsDelete(0);
        expectedResult.setEffective(0);
        expectedResult.setRoleType("roleType");
        expectedResult.setUpgradeType(0);

        when(mockHsaMemberGradeInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final HsaMemberGradeInfo result = hsaMemberGradeChangeDetailServiceImplUnderTest.calculateExtraAmount(0,
                "operSubjectGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
