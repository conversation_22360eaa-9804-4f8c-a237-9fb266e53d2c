package com.holderzone.member.queue.utils;

import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class NumberUtilTest {

    @Test
    public void testBuildMessageCode() {
        assertThat(NumberUtil.buildMessageCode()).isEqualTo("result");
    }

    @Test
    public void testBuildNumToStr() {
        assertThat(NumberUtil.buildNumToStr(0)).isEqualTo("");
    }

    @Test
    public void testIsPhoneNum() {
        assertThat(NumberUtil.isPhoneNum("phone")).isFalse();
    }

    @Test
    public void testIsPhoneNum11() {
        assertThat(NumberUtil.isPhoneNum11("phone")).isFalse();
    }
}
