package com.holderzone.member.queue.service.cache.impl;

import com.holderzone.member.common.constant.StringConstant;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CacheServiceImplTest {

    @Mock
    private RedisTemplate mockRedisTemplate;
    @Mock
    private RedisTemplate<String, Object> mockStringObjectRedisTemplate;
    @Mock
    private RedissonClient mockRedissonClient;
    @Mock
    private RedisTemplate<String, String> mockStringRedisTemplate;

    @InjectMocks
    private CacheServiceImpl cacheServiceImplUnderTest;

    @Test
    public void testCreateGradeRefresh() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        cacheServiceImplUnderTest.createGradeRefresh("cacheKey", "operSubjectGuid");

        // Verify the results
    }

    @Test
    public void testGetGradeRefresh() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final boolean result = cacheServiceImplUnderTest.getGradeRefresh("cacheKey");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testCleanToken() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockRedisTemplate.delete("cacheKey")).thenReturn(false);

        // Run the test
        final Boolean result = cacheServiceImplUnderTest.cleanToken("cacheKey");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testCleanToken_RedisTemplateDeleteReturnsNull() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockRedisTemplate.delete("cacheKey")).thenReturn(null);

        // Run the test
        final Boolean result = cacheServiceImplUnderTest.cleanToken("cacheKey");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testCleanToken_RedisTemplateDeleteReturnsTrue() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockRedisTemplate.delete("cacheKey")).thenReturn(true);

        // Run the test
        final Boolean result = cacheServiceImplUnderTest.cleanToken("cacheKey");

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testDelete() {
        // Setup
        // Run the test
        cacheServiceImplUnderTest.delete("key");

        // Verify the results
        verify(mockStringRedisTemplate).delete("key");
    }

    @Test
    public void testUpdateInventoryNum() {
        // Setup
        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        cacheServiceImplUnderTest.updateInventoryNum("key", "code");

        // Verify the results
    }

    @Test
    public void testGetInventoryNum() {
        // Setup
        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockStringObjectRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final int result = cacheServiceImplUnderTest.getInventoryNum("key", "code");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testLockInventoryNum() {
        // Setup
        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockStringObjectRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        cacheServiceImplUnderTest.lockInventoryNum("key", "code", 0, 0);

        // Verify the results
        verify(mockStringObjectRedisTemplate).delete("key");
    }

    @Test
    public void testSetLock1() {
        // Setup
        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);
        when(mockStringObjectRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final Boolean result = cacheServiceImplUnderTest.setLock("key", "code");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testSetLock2() {
        // Setup
        when(mockRedissonClient.getLock(StringConstant.LOCK_XXL_JOB_COUPON_PACKAGE)).thenReturn(null);
        when(mockStringObjectRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final Boolean result = cacheServiceImplUnderTest.setLock("key");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testAddInventoryNum() {
        // Setup
        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockStringObjectRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        cacheServiceImplUnderTest.addInventoryNum("key", "code");

        // Verify the results
    }
}
