package com.holderzone.member.queue.service.card.impl;

import com.holderzone.member.queue.entity.HsaElectronicCard;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class HsaElectronicCardServiceImplTest {

    private HsaElectronicCardServiceImpl hsaElectronicCardServiceImplUnderTest;

    @Before
    public void setUp() {
        hsaElectronicCardServiceImplUnderTest = new HsaElectronicCardServiceImpl();
    }

    @Test
    public void testQueryByGuid() {
        // Setup
        final HsaElectronicCard expectedResult = new HsaElectronicCard();
        expectedResult.setGuid("9590a854-0df4-45ae-b4ba-91a8374d8081");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setMemberInfoGuid("memberInfoGuid");
        expectedResult.setCardGuid("cardGuid");

        // Run the test
        final HsaElectronicCard result = hsaElectronicCardServiceImplUnderTest.queryByGuid(
                "3377d920-e824-4305-b08a-d495b80d224e");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryByGuids() {
        // Setup
        final HsaElectronicCard hsaElectronicCard = new HsaElectronicCard();
        hsaElectronicCard.setGuid("9590a854-0df4-45ae-b4ba-91a8374d8081");
        hsaElectronicCard.setOperSubjectGuid("operSubjectGuid");
        hsaElectronicCard.setEnterpriseGuid("enterpriseGuid");
        hsaElectronicCard.setMemberInfoGuid("memberInfoGuid");
        hsaElectronicCard.setCardGuid("cardGuid");
        final List<HsaElectronicCard> expectedResult = Arrays.asList(hsaElectronicCard);

        // Run the test
        final List<HsaElectronicCard> result = hsaElectronicCardServiceImplUnderTest.queryByGuids(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateByGuid() {
        // Setup
        final HsaElectronicCard t = new HsaElectronicCard();
        t.setGuid("9590a854-0df4-45ae-b4ba-91a8374d8081");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setCardGuid("cardGuid");

        // Run the test
        final boolean result = hsaElectronicCardServiceImplUnderTest.updateByGuid(t);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemoveByGuid() {
        // Setup
        // Run the test
        final boolean result = hsaElectronicCardServiceImplUnderTest.removeByGuid(
                "124e2fb5-be2c-4d20-98e1-5758312d8bbf");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemoveByGuids() {
        // Setup
        // Run the test
        final boolean result = hsaElectronicCardServiceImplUnderTest.removeByGuids(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isFalse();
    }
}
