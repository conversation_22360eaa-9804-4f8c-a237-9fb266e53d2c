package com.holderzone.member.queue.service.card.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.queue.entity.HsaMemberInfoCard;
import com.holderzone.member.queue.entity.HsaStoreCardRule;
import com.holderzone.member.queue.mapper.HsaStoreCardRuleMapper;
import com.holderzone.member.queue.service.card.HsaStoreCardRuleService;
import com.holderzone.member.queue.service.card.assembler.MemberCardAssembler;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaStoreCardRuleServiceImplTest {

    @Mock
    private HsaStoreCardRuleMapper mockHsaStoreCardRuleMapper;
    @Mock
    private HsaStoreCardRuleService mockHsaStoreCardRuleService;
    @Mock
    private MemberCardAssembler mockMemberCardAssembler;

    @InjectMocks
    private HsaStoreCardRuleServiceImpl hsaStoreCardRuleServiceImplUnderTest;

    @Test
    public void testDeleteByCardGuid() {
        // Setup
        when(mockHsaStoreCardRuleMapper.delete(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final Integer result = hsaStoreCardRuleServiceImplUnderTest.deleteByCardGuid("cardGuid");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testSaveStoreCardRule() {
        // Setup
        // Configure HsaStoreCardRuleMapper.selectList(...).
        final HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        hsaStoreCardRule.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule.setStoreGuid("storeGuid");
        hsaStoreCardRule.setCardGuid("cardGuid");
        hsaStoreCardRule.setParentGuid("parentGuid");
        hsaStoreCardRule.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRules = Arrays.asList(hsaStoreCardRule);
        when(mockHsaStoreCardRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaStoreCardRules);

        // Run the test
        hsaStoreCardRuleServiceImplUnderTest.saveStoreCardRule("cardGuid", "newMemberCardGuid", "memberCardGuid");

        // Verify the results
        // Confirm MemberCardAssembler.addStoreCardRuleArrayList(...).
        final HsaStoreCardRule hsaStoreCardRule1 = new HsaStoreCardRule();
        hsaStoreCardRule1.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule1.setStoreGuid("storeGuid");
        hsaStoreCardRule1.setCardGuid("cardGuid");
        hsaStoreCardRule1.setParentGuid("parentGuid");
        hsaStoreCardRule1.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRuleList = Arrays.asList(hsaStoreCardRule1);
        final HsaStoreCardRule hsaStoreCardRule2 = new HsaStoreCardRule();
        hsaStoreCardRule2.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule2.setStoreGuid("storeGuid");
        hsaStoreCardRule2.setCardGuid("cardGuid");
        hsaStoreCardRule2.setParentGuid("parentGuid");
        hsaStoreCardRule2.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRuleArrayList = Arrays.asList(hsaStoreCardRule2);
        verify(mockMemberCardAssembler).addStoreCardRuleArrayList("cardGuid", "newMemberCardGuid", hsaStoreCardRuleList,
                new HashMap<>(), hsaStoreCardRuleArrayList);

        // Confirm HsaStoreCardRuleService.saveBatch(...).
        final HsaStoreCardRule hsaStoreCardRule3 = new HsaStoreCardRule();
        hsaStoreCardRule3.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule3.setStoreGuid("storeGuid");
        hsaStoreCardRule3.setCardGuid("cardGuid");
        hsaStoreCardRule3.setParentGuid("parentGuid");
        hsaStoreCardRule3.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> entityList = Arrays.asList(hsaStoreCardRule3);
        verify(mockHsaStoreCardRuleService).saveBatch(entityList);
    }

    @Test
    public void testSaveStoreCardRule_HsaStoreCardRuleMapperReturnsNoItems() {
        // Setup
        when(mockHsaStoreCardRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        hsaStoreCardRuleServiceImplUnderTest.saveStoreCardRule("cardGuid", "newMemberCardGuid", "memberCardGuid");

        // Verify the results
        // Confirm MemberCardAssembler.addStoreCardRuleArrayList(...).
        final HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        hsaStoreCardRule.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule.setStoreGuid("storeGuid");
        hsaStoreCardRule.setCardGuid("cardGuid");
        hsaStoreCardRule.setParentGuid("parentGuid");
        hsaStoreCardRule.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRuleList = Arrays.asList(hsaStoreCardRule);
        final HsaStoreCardRule hsaStoreCardRule1 = new HsaStoreCardRule();
        hsaStoreCardRule1.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule1.setStoreGuid("storeGuid");
        hsaStoreCardRule1.setCardGuid("cardGuid");
        hsaStoreCardRule1.setParentGuid("parentGuid");
        hsaStoreCardRule1.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRuleArrayList = Arrays.asList(hsaStoreCardRule1);
        verify(mockMemberCardAssembler).addStoreCardRuleArrayList("cardGuid", "newMemberCardGuid", hsaStoreCardRuleList,
                new HashMap<>(), hsaStoreCardRuleArrayList);

        // Confirm HsaStoreCardRuleService.saveBatch(...).
        final HsaStoreCardRule hsaStoreCardRule2 = new HsaStoreCardRule();
        hsaStoreCardRule2.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule2.setStoreGuid("storeGuid");
        hsaStoreCardRule2.setCardGuid("cardGuid");
        hsaStoreCardRule2.setParentGuid("parentGuid");
        hsaStoreCardRule2.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> entityList = Arrays.asList(hsaStoreCardRule2);
        verify(mockHsaStoreCardRuleService).saveBatch(entityList);
    }

    @Test
    public void testMergeStoreProcessor1() {
        // Setup
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setId(0L);
        hsaMemberInfoCard.setGuid("08a8391b-e9c3-4b81-8dc6-ce7fb844e379");
        hsaMemberInfoCard.setOperSubjectGuid("operSubjectGuid");
        hsaMemberInfoCard.setEnterpriseGuid("enterpriseGuid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");

        final HsaMemberInfoCard memberPhysicalCard = new HsaMemberInfoCard();
        memberPhysicalCard.setId(0L);
        memberPhysicalCard.setGuid("08a8391b-e9c3-4b81-8dc6-ce7fb844e379");
        memberPhysicalCard.setOperSubjectGuid("operSubjectGuid");
        memberPhysicalCard.setEnterpriseGuid("enterpriseGuid");
        memberPhysicalCard.setMemberInfoGuid("memberInfoGuid");

        // Configure HsaStoreCardRuleMapper.selectList(...).
        final HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        hsaStoreCardRule.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule.setStoreGuid("storeGuid");
        hsaStoreCardRule.setCardGuid("cardGuid");
        hsaStoreCardRule.setParentGuid("parentGuid");
        hsaStoreCardRule.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRules = Arrays.asList(hsaStoreCardRule);
        when(mockHsaStoreCardRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaStoreCardRules);

        // Run the test
        hsaStoreCardRuleServiceImplUnderTest.mergeStoreProcessor(hsaMemberInfoCard, memberPhysicalCard);

        // Verify the results
        verify(mockHsaStoreCardRuleMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm MemberCardAssembler.seStore(...).
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setId(0L);
        hsaMemberInfoCard1.setGuid("08a8391b-e9c3-4b81-8dc6-ce7fb844e379");
        hsaMemberInfoCard1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberInfoCard1.setEnterpriseGuid("enterpriseGuid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        final HsaStoreCardRule hsaStoreCardRule1 = new HsaStoreCardRule();
        hsaStoreCardRule1.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule1.setStoreGuid("storeGuid");
        hsaStoreCardRule1.setCardGuid("cardGuid");
        hsaStoreCardRule1.setParentGuid("parentGuid");
        hsaStoreCardRule1.setMemberInfoCardGuid("memberInfoCardGuid");
        final Set<HsaStoreCardRule> hsaStoreCardRuleSet = new HashSet<>(Arrays.asList(hsaStoreCardRule1));
        final HsaStoreCardRule rule = new HsaStoreCardRule();
        rule.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        rule.setStoreGuid("storeGuid");
        rule.setCardGuid("cardGuid");
        rule.setParentGuid("parentGuid");
        rule.setMemberInfoCardGuid("memberInfoCardGuid");
        verify(mockMemberCardAssembler).seStore(hsaMemberInfoCard1, hsaStoreCardRuleSet, new HashMap<>(), rule);

        // Confirm MemberCardAssembler.setStall(...).
        final HsaMemberInfoCard hsaMemberInfoCard2 = new HsaMemberInfoCard();
        hsaMemberInfoCard2.setId(0L);
        hsaMemberInfoCard2.setGuid("08a8391b-e9c3-4b81-8dc6-ce7fb844e379");
        hsaMemberInfoCard2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberInfoCard2.setEnterpriseGuid("enterpriseGuid");
        hsaMemberInfoCard2.setMemberInfoGuid("memberInfoGuid");
        final HsaStoreCardRule hsaStoreCardRule2 = new HsaStoreCardRule();
        hsaStoreCardRule2.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule2.setStoreGuid("storeGuid");
        hsaStoreCardRule2.setCardGuid("cardGuid");
        hsaStoreCardRule2.setParentGuid("parentGuid");
        hsaStoreCardRule2.setMemberInfoCardGuid("memberInfoCardGuid");
        final Set<HsaStoreCardRule> hsaStoreCardRuleSet1 = new HashSet<>(Arrays.asList(hsaStoreCardRule2));
        final HsaStoreCardRule rule1 = new HsaStoreCardRule();
        rule1.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        rule1.setStoreGuid("storeGuid");
        rule1.setCardGuid("cardGuid");
        rule1.setParentGuid("parentGuid");
        rule1.setMemberInfoCardGuid("memberInfoCardGuid");
        final HsaStoreCardRule hsaStoreCardRule3 = new HsaStoreCardRule();
        hsaStoreCardRule3.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule3.setStoreGuid("storeGuid");
        hsaStoreCardRule3.setCardGuid("cardGuid");
        hsaStoreCardRule3.setParentGuid("parentGuid");
        hsaStoreCardRule3.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> storeCardRule = Arrays.asList(hsaStoreCardRule3);
        final HsaStoreCardRule in = new HsaStoreCardRule();
        in.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        in.setStoreGuid("storeGuid");
        in.setCardGuid("cardGuid");
        in.setParentGuid("parentGuid");
        in.setMemberInfoCardGuid("memberInfoCardGuid");
        verify(mockMemberCardAssembler).setStall(hsaMemberInfoCard2, hsaStoreCardRuleSet1, new HashMap<>(), rule1,
                storeCardRule, in);

        // Confirm HsaStoreCardRuleService.saveBatch(...).
        final HsaStoreCardRule hsaStoreCardRule4 = new HsaStoreCardRule();
        hsaStoreCardRule4.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule4.setStoreGuid("storeGuid");
        hsaStoreCardRule4.setCardGuid("cardGuid");
        hsaStoreCardRule4.setParentGuid("parentGuid");
        hsaStoreCardRule4.setMemberInfoCardGuid("memberInfoCardGuid");
        final Set<HsaStoreCardRule> entityList = new HashSet<>(Arrays.asList(hsaStoreCardRule4));
        verify(mockHsaStoreCardRuleService).saveBatch(entityList);
    }

    @Test
    public void testMergeStoreProcessor1_HsaStoreCardRuleMapperSelectListReturnsNoItems() {
        // Setup
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setId(0L);
        hsaMemberInfoCard.setGuid("08a8391b-e9c3-4b81-8dc6-ce7fb844e379");
        hsaMemberInfoCard.setOperSubjectGuid("operSubjectGuid");
        hsaMemberInfoCard.setEnterpriseGuid("enterpriseGuid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");

        final HsaMemberInfoCard memberPhysicalCard = new HsaMemberInfoCard();
        memberPhysicalCard.setId(0L);
        memberPhysicalCard.setGuid("08a8391b-e9c3-4b81-8dc6-ce7fb844e379");
        memberPhysicalCard.setOperSubjectGuid("operSubjectGuid");
        memberPhysicalCard.setEnterpriseGuid("enterpriseGuid");
        memberPhysicalCard.setMemberInfoGuid("memberInfoGuid");

        when(mockHsaStoreCardRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        hsaStoreCardRuleServiceImplUnderTest.mergeStoreProcessor(hsaMemberInfoCard, memberPhysicalCard);

        // Verify the results
        verify(mockHsaStoreCardRuleMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm MemberCardAssembler.seStore(...).
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setId(0L);
        hsaMemberInfoCard1.setGuid("08a8391b-e9c3-4b81-8dc6-ce7fb844e379");
        hsaMemberInfoCard1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberInfoCard1.setEnterpriseGuid("enterpriseGuid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        final HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        hsaStoreCardRule.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule.setStoreGuid("storeGuid");
        hsaStoreCardRule.setCardGuid("cardGuid");
        hsaStoreCardRule.setParentGuid("parentGuid");
        hsaStoreCardRule.setMemberInfoCardGuid("memberInfoCardGuid");
        final Set<HsaStoreCardRule> hsaStoreCardRuleSet = new HashSet<>(Arrays.asList(hsaStoreCardRule));
        final HsaStoreCardRule rule = new HsaStoreCardRule();
        rule.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        rule.setStoreGuid("storeGuid");
        rule.setCardGuid("cardGuid");
        rule.setParentGuid("parentGuid");
        rule.setMemberInfoCardGuid("memberInfoCardGuid");
        verify(mockMemberCardAssembler).seStore(hsaMemberInfoCard1, hsaStoreCardRuleSet, new HashMap<>(), rule);

        // Confirm MemberCardAssembler.setStall(...).
        final HsaMemberInfoCard hsaMemberInfoCard2 = new HsaMemberInfoCard();
        hsaMemberInfoCard2.setId(0L);
        hsaMemberInfoCard2.setGuid("08a8391b-e9c3-4b81-8dc6-ce7fb844e379");
        hsaMemberInfoCard2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberInfoCard2.setEnterpriseGuid("enterpriseGuid");
        hsaMemberInfoCard2.setMemberInfoGuid("memberInfoGuid");
        final HsaStoreCardRule hsaStoreCardRule1 = new HsaStoreCardRule();
        hsaStoreCardRule1.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule1.setStoreGuid("storeGuid");
        hsaStoreCardRule1.setCardGuid("cardGuid");
        hsaStoreCardRule1.setParentGuid("parentGuid");
        hsaStoreCardRule1.setMemberInfoCardGuid("memberInfoCardGuid");
        final Set<HsaStoreCardRule> hsaStoreCardRuleSet1 = new HashSet<>(Arrays.asList(hsaStoreCardRule1));
        final HsaStoreCardRule rule1 = new HsaStoreCardRule();
        rule1.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        rule1.setStoreGuid("storeGuid");
        rule1.setCardGuid("cardGuid");
        rule1.setParentGuid("parentGuid");
        rule1.setMemberInfoCardGuid("memberInfoCardGuid");
        final HsaStoreCardRule hsaStoreCardRule2 = new HsaStoreCardRule();
        hsaStoreCardRule2.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule2.setStoreGuid("storeGuid");
        hsaStoreCardRule2.setCardGuid("cardGuid");
        hsaStoreCardRule2.setParentGuid("parentGuid");
        hsaStoreCardRule2.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> storeCardRule = Arrays.asList(hsaStoreCardRule2);
        final HsaStoreCardRule in = new HsaStoreCardRule();
        in.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        in.setStoreGuid("storeGuid");
        in.setCardGuid("cardGuid");
        in.setParentGuid("parentGuid");
        in.setMemberInfoCardGuid("memberInfoCardGuid");
        verify(mockMemberCardAssembler).setStall(hsaMemberInfoCard2, hsaStoreCardRuleSet1, new HashMap<>(), rule1,
                storeCardRule, in);

        // Confirm HsaStoreCardRuleService.saveBatch(...).
        final HsaStoreCardRule hsaStoreCardRule3 = new HsaStoreCardRule();
        hsaStoreCardRule3.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule3.setStoreGuid("storeGuid");
        hsaStoreCardRule3.setCardGuid("cardGuid");
        hsaStoreCardRule3.setParentGuid("parentGuid");
        hsaStoreCardRule3.setMemberInfoCardGuid("memberInfoCardGuid");
        final Set<HsaStoreCardRule> entityList = new HashSet<>(Arrays.asList(hsaStoreCardRule3));
        verify(mockHsaStoreCardRuleService).saveBatch(entityList);
    }

    @Test
    public void testMergeStoreProcessor2() {
        // Setup
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setId(0L);
        hsaMemberInfoCard.setGuid("08a8391b-e9c3-4b81-8dc6-ce7fb844e379");
        hsaMemberInfoCard.setOperSubjectGuid("operSubjectGuid");
        hsaMemberInfoCard.setEnterpriseGuid("enterpriseGuid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");

        // Configure HsaStoreCardRuleMapper.selectList(...).
        final HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        hsaStoreCardRule.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule.setStoreGuid("storeGuid");
        hsaStoreCardRule.setCardGuid("cardGuid");
        hsaStoreCardRule.setParentGuid("parentGuid");
        hsaStoreCardRule.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> hsaStoreCardRules = Arrays.asList(hsaStoreCardRule);
        when(mockHsaStoreCardRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaStoreCardRules);

        // Run the test
        hsaStoreCardRuleServiceImplUnderTest.mergeStoreProcessor(hsaMemberInfoCard, "cardGuid");

        // Verify the results
        verify(mockHsaStoreCardRuleMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm MemberCardAssembler.seStore(...).
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setId(0L);
        hsaMemberInfoCard1.setGuid("08a8391b-e9c3-4b81-8dc6-ce7fb844e379");
        hsaMemberInfoCard1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberInfoCard1.setEnterpriseGuid("enterpriseGuid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        final HsaStoreCardRule hsaStoreCardRule1 = new HsaStoreCardRule();
        hsaStoreCardRule1.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule1.setStoreGuid("storeGuid");
        hsaStoreCardRule1.setCardGuid("cardGuid");
        hsaStoreCardRule1.setParentGuid("parentGuid");
        hsaStoreCardRule1.setMemberInfoCardGuid("memberInfoCardGuid");
        final Set<HsaStoreCardRule> hsaStoreCardRuleSet = new HashSet<>(Arrays.asList(hsaStoreCardRule1));
        final HsaStoreCardRule rule = new HsaStoreCardRule();
        rule.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        rule.setStoreGuid("storeGuid");
        rule.setCardGuid("cardGuid");
        rule.setParentGuid("parentGuid");
        rule.setMemberInfoCardGuid("memberInfoCardGuid");
        verify(mockMemberCardAssembler).seStore(hsaMemberInfoCard1, hsaStoreCardRuleSet, new HashMap<>(), rule);

        // Confirm MemberCardAssembler.setStall(...).
        final HsaMemberInfoCard hsaMemberInfoCard2 = new HsaMemberInfoCard();
        hsaMemberInfoCard2.setId(0L);
        hsaMemberInfoCard2.setGuid("08a8391b-e9c3-4b81-8dc6-ce7fb844e379");
        hsaMemberInfoCard2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberInfoCard2.setEnterpriseGuid("enterpriseGuid");
        hsaMemberInfoCard2.setMemberInfoGuid("memberInfoGuid");
        final HsaStoreCardRule hsaStoreCardRule2 = new HsaStoreCardRule();
        hsaStoreCardRule2.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule2.setStoreGuid("storeGuid");
        hsaStoreCardRule2.setCardGuid("cardGuid");
        hsaStoreCardRule2.setParentGuid("parentGuid");
        hsaStoreCardRule2.setMemberInfoCardGuid("memberInfoCardGuid");
        final Set<HsaStoreCardRule> hsaStoreCardRuleSet1 = new HashSet<>(Arrays.asList(hsaStoreCardRule2));
        final HsaStoreCardRule rule1 = new HsaStoreCardRule();
        rule1.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        rule1.setStoreGuid("storeGuid");
        rule1.setCardGuid("cardGuid");
        rule1.setParentGuid("parentGuid");
        rule1.setMemberInfoCardGuid("memberInfoCardGuid");
        final HsaStoreCardRule hsaStoreCardRule3 = new HsaStoreCardRule();
        hsaStoreCardRule3.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule3.setStoreGuid("storeGuid");
        hsaStoreCardRule3.setCardGuid("cardGuid");
        hsaStoreCardRule3.setParentGuid("parentGuid");
        hsaStoreCardRule3.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> storeCardRule = Arrays.asList(hsaStoreCardRule3);
        final HsaStoreCardRule in = new HsaStoreCardRule();
        in.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        in.setStoreGuid("storeGuid");
        in.setCardGuid("cardGuid");
        in.setParentGuid("parentGuid");
        in.setMemberInfoCardGuid("memberInfoCardGuid");
        verify(mockMemberCardAssembler).setStall(hsaMemberInfoCard2, hsaStoreCardRuleSet1, new HashMap<>(), rule1,
                storeCardRule, in);

        // Confirm HsaStoreCardRuleService.saveBatch(...).
        final HsaStoreCardRule hsaStoreCardRule4 = new HsaStoreCardRule();
        hsaStoreCardRule4.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule4.setStoreGuid("storeGuid");
        hsaStoreCardRule4.setCardGuid("cardGuid");
        hsaStoreCardRule4.setParentGuid("parentGuid");
        hsaStoreCardRule4.setMemberInfoCardGuid("memberInfoCardGuid");
        final Set<HsaStoreCardRule> entityList = new HashSet<>(Arrays.asList(hsaStoreCardRule4));
        verify(mockHsaStoreCardRuleService).saveBatch(entityList);
    }

    @Test
    public void testMergeStoreProcessor2_HsaStoreCardRuleMapperSelectListReturnsNoItems() {
        // Setup
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setId(0L);
        hsaMemberInfoCard.setGuid("08a8391b-e9c3-4b81-8dc6-ce7fb844e379");
        hsaMemberInfoCard.setOperSubjectGuid("operSubjectGuid");
        hsaMemberInfoCard.setEnterpriseGuid("enterpriseGuid");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");

        when(mockHsaStoreCardRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        hsaStoreCardRuleServiceImplUnderTest.mergeStoreProcessor(hsaMemberInfoCard, "cardGuid");

        // Verify the results
        verify(mockHsaStoreCardRuleMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm MemberCardAssembler.seStore(...).
        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setId(0L);
        hsaMemberInfoCard1.setGuid("08a8391b-e9c3-4b81-8dc6-ce7fb844e379");
        hsaMemberInfoCard1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberInfoCard1.setEnterpriseGuid("enterpriseGuid");
        hsaMemberInfoCard1.setMemberInfoGuid("memberInfoGuid");
        final HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        hsaStoreCardRule.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule.setStoreGuid("storeGuid");
        hsaStoreCardRule.setCardGuid("cardGuid");
        hsaStoreCardRule.setParentGuid("parentGuid");
        hsaStoreCardRule.setMemberInfoCardGuid("memberInfoCardGuid");
        final Set<HsaStoreCardRule> hsaStoreCardRuleSet = new HashSet<>(Arrays.asList(hsaStoreCardRule));
        final HsaStoreCardRule rule = new HsaStoreCardRule();
        rule.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        rule.setStoreGuid("storeGuid");
        rule.setCardGuid("cardGuid");
        rule.setParentGuid("parentGuid");
        rule.setMemberInfoCardGuid("memberInfoCardGuid");
        verify(mockMemberCardAssembler).seStore(hsaMemberInfoCard1, hsaStoreCardRuleSet, new HashMap<>(), rule);

        // Confirm MemberCardAssembler.setStall(...).
        final HsaMemberInfoCard hsaMemberInfoCard2 = new HsaMemberInfoCard();
        hsaMemberInfoCard2.setId(0L);
        hsaMemberInfoCard2.setGuid("08a8391b-e9c3-4b81-8dc6-ce7fb844e379");
        hsaMemberInfoCard2.setOperSubjectGuid("operSubjectGuid");
        hsaMemberInfoCard2.setEnterpriseGuid("enterpriseGuid");
        hsaMemberInfoCard2.setMemberInfoGuid("memberInfoGuid");
        final HsaStoreCardRule hsaStoreCardRule1 = new HsaStoreCardRule();
        hsaStoreCardRule1.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule1.setStoreGuid("storeGuid");
        hsaStoreCardRule1.setCardGuid("cardGuid");
        hsaStoreCardRule1.setParentGuid("parentGuid");
        hsaStoreCardRule1.setMemberInfoCardGuid("memberInfoCardGuid");
        final Set<HsaStoreCardRule> hsaStoreCardRuleSet1 = new HashSet<>(Arrays.asList(hsaStoreCardRule1));
        final HsaStoreCardRule rule1 = new HsaStoreCardRule();
        rule1.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        rule1.setStoreGuid("storeGuid");
        rule1.setCardGuid("cardGuid");
        rule1.setParentGuid("parentGuid");
        rule1.setMemberInfoCardGuid("memberInfoCardGuid");
        final HsaStoreCardRule hsaStoreCardRule2 = new HsaStoreCardRule();
        hsaStoreCardRule2.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule2.setStoreGuid("storeGuid");
        hsaStoreCardRule2.setCardGuid("cardGuid");
        hsaStoreCardRule2.setParentGuid("parentGuid");
        hsaStoreCardRule2.setMemberInfoCardGuid("memberInfoCardGuid");
        final List<HsaStoreCardRule> storeCardRule = Arrays.asList(hsaStoreCardRule2);
        final HsaStoreCardRule in = new HsaStoreCardRule();
        in.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        in.setStoreGuid("storeGuid");
        in.setCardGuid("cardGuid");
        in.setParentGuid("parentGuid");
        in.setMemberInfoCardGuid("memberInfoCardGuid");
        verify(mockMemberCardAssembler).setStall(hsaMemberInfoCard2, hsaStoreCardRuleSet1, new HashMap<>(), rule1,
                storeCardRule, in);

        // Confirm HsaStoreCardRuleService.saveBatch(...).
        final HsaStoreCardRule hsaStoreCardRule3 = new HsaStoreCardRule();
        hsaStoreCardRule3.setGuid("81211ca2-9911-4761-9b69-97ff755561ac");
        hsaStoreCardRule3.setStoreGuid("storeGuid");
        hsaStoreCardRule3.setCardGuid("cardGuid");
        hsaStoreCardRule3.setParentGuid("parentGuid");
        hsaStoreCardRule3.setMemberInfoCardGuid("memberInfoCardGuid");
        final Set<HsaStoreCardRule> entityList = new HashSet<>(Arrays.asList(hsaStoreCardRule3));
        verify(mockHsaStoreCardRuleService).saveBatch(entityList);
    }
}
