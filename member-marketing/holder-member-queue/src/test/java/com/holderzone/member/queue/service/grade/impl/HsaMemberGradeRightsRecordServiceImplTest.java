package com.holderzone.member.queue.service.grade.impl;

import com.holderzone.member.queue.entity.HsaMemberGradeRightsRecord;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class HsaMemberGradeRightsRecordServiceImplTest {

    private HsaMemberGradeRightsRecordServiceImpl hsaMemberGradeRightsRecordServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        hsaMemberGradeRightsRecordServiceImplUnderTest = new HsaMemberGradeRightsRecordServiceImpl();
    }

    @Test
    public void testQueryByGuid() {
        // Setup
        final HsaMemberGradeRightsRecord expectedResult = new HsaMemberGradeRightsRecord();
        expectedResult.setId(0L);
        expectedResult.setGuid("bad3cc36-29e4-4eef-b20e-6cb216c1066b");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setMemberInfoGuid("memberInfoGuid");
        expectedResult.setMemberGradeGuid("memberGradeGuid");

        // Run the test
        final HsaMemberGradeRightsRecord result = hsaMemberGradeRightsRecordServiceImplUnderTest.queryByGuid(
                "ad42b080-84a0-4bb8-b4d2-986e8332eae6");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryByGuids() {
        // Setup
        final HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord = new HsaMemberGradeRightsRecord();
        hsaMemberGradeRightsRecord.setId(0L);
        hsaMemberGradeRightsRecord.setGuid("bad3cc36-29e4-4eef-b20e-6cb216c1066b");
        hsaMemberGradeRightsRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeRightsRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberGradeRightsRecord.setMemberGradeGuid("memberGradeGuid");
        final List<HsaMemberGradeRightsRecord> expectedResult = Arrays.asList(hsaMemberGradeRightsRecord);

        // Run the test
        final List<HsaMemberGradeRightsRecord> result = hsaMemberGradeRightsRecordServiceImplUnderTest.queryByGuids(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateByGuid() {
        // Setup
        final HsaMemberGradeRightsRecord t = new HsaMemberGradeRightsRecord();
        t.setId(0L);
        t.setGuid("bad3cc36-29e4-4eef-b20e-6cb216c1066b");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setMemberGradeGuid("memberGradeGuid");

        // Run the test
        final boolean result = hsaMemberGradeRightsRecordServiceImplUnderTest.updateByGuid(t);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemoveByGuid() {
        // Setup
        // Run the test
        final boolean result = hsaMemberGradeRightsRecordServiceImplUnderTest.removeByGuid(
                "df3fec21-0f27-4e8d-bf46-3b2b5935d4cb");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemoveByGuids() {
        // Setup
        // Run the test
        final boolean result = hsaMemberGradeRightsRecordServiceImplUnderTest.removeByGuids(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isFalse();
    }
}
