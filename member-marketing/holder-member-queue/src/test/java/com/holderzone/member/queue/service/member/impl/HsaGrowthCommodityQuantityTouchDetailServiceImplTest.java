package com.holderzone.member.queue.service.member.impl;

import com.holderzone.member.queue.entity.HsaGrowthCommodityQuantityTouchDetail;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class HsaGrowthCommodityQuantityTouchDetailServiceImplTest {

    private HsaGrowthCommodityQuantityTouchDetailServiceImpl hsaGrowthCommodityQuantityTouchDetailServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        hsaGrowthCommodityQuantityTouchDetailServiceImplUnderTest = new HsaGrowthCommodityQuantityTouchDetailServiceImpl();
    }

    @Test
    public void testQueryByGuid() {
        // Setup
        final HsaGrowthCommodityQuantityTouchDetail expectedResult = new HsaGrowthCommodityQuantityTouchDetail();
        expectedResult.setGuid("2f1ffa79-400a-42ee-a45c-93e4a239ae93");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setGrowthValueTaskGuid("growthValueTaskGuid");
        expectedResult.setMemberInfoGuid("memberInfoGuid");
        expectedResult.setMemberConsumptionGuid("memberConsumptionGuid");

        // Run the test
        final HsaGrowthCommodityQuantityTouchDetail result = hsaGrowthCommodityQuantityTouchDetailServiceImplUnderTest.queryByGuid(
                "03722dbe-0674-4aa3-a54a-93e740e4ff97");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryByGuids() {
        // Setup
        final HsaGrowthCommodityQuantityTouchDetail hsaGrowthCommodityQuantityTouchDetail = new HsaGrowthCommodityQuantityTouchDetail();
        hsaGrowthCommodityQuantityTouchDetail.setGuid("2f1ffa79-400a-42ee-a45c-93e4a239ae93");
        hsaGrowthCommodityQuantityTouchDetail.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthCommodityQuantityTouchDetail.setGrowthValueTaskGuid("growthValueTaskGuid");
        hsaGrowthCommodityQuantityTouchDetail.setMemberInfoGuid("memberInfoGuid");
        hsaGrowthCommodityQuantityTouchDetail.setMemberConsumptionGuid("memberConsumptionGuid");
        final List<HsaGrowthCommodityQuantityTouchDetail> expectedResult = Arrays.asList(
                hsaGrowthCommodityQuantityTouchDetail);

        // Run the test
        final List<HsaGrowthCommodityQuantityTouchDetail> result = hsaGrowthCommodityQuantityTouchDetailServiceImplUnderTest.queryByGuids(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateByGuid() {
        // Setup
        final HsaGrowthCommodityQuantityTouchDetail t = new HsaGrowthCommodityQuantityTouchDetail();
        t.setGuid("2f1ffa79-400a-42ee-a45c-93e4a239ae93");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setGrowthValueTaskGuid("growthValueTaskGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setMemberConsumptionGuid("memberConsumptionGuid");

        // Run the test
        final boolean result = hsaGrowthCommodityQuantityTouchDetailServiceImplUnderTest.updateByGuid(t);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemoveByGuid() {
        // Setup
        // Run the test
        final boolean result = hsaGrowthCommodityQuantityTouchDetailServiceImplUnderTest.removeByGuid(
                "1bcd1c78-cb30-428f-a7bb-e76520ef1d85");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemoveByGuids() {
        // Setup
        // Run the test
        final boolean result = hsaGrowthCommodityQuantityTouchDetailServiceImplUnderTest.removeByGuids(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isFalse();
    }
}
