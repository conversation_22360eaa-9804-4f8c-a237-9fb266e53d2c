package com.holderzone.member.queue.service.grade.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.grade.MemberGradePayRecordReqVO;
import com.holderzone.member.queue.entity.HsaMemberGradeInfo;
import com.holderzone.member.queue.entity.HsaOperationMemberInfo;
import com.holderzone.member.queue.mapper.HsaMemberGradeInfoMapper;
import com.holderzone.member.queue.mapper.HsaMemberGradePayRecordMapper;
import com.holderzone.member.queue.mapper.HsaOperationMemberInfoMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaMemberGradePayRecordServiceImplTest {

    @Mock
    private HsaOperationMemberInfoMapper mockOperationMemberInfoMapper;
    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private HsaMemberGradePayRecordMapper mockMemberGradePayRecordMapper;
    @Mock
    private HsaMemberGradeInfoMapper mockMemberGradeInfoMapper;

    private HsaMemberGradePayRecordServiceImpl hsaMemberGradePayRecordServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        hsaMemberGradePayRecordServiceImplUnderTest = new HsaMemberGradePayRecordServiceImpl(
                mockOperationMemberInfoMapper, mockGuidGeneratorUtil, mockMemberGradePayRecordMapper,
                mockMemberGradeInfoMapper);
    }

    @Test
    public void testBatchAdd() {
        // Setup
        final MemberGradePayRecordReqVO gradePayRecordReqVO = new MemberGradePayRecordReqVO();
        gradePayRecordReqVO.setMemberGuidList(Arrays.asList("value"));
        gradePayRecordReqVO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        gradePayRecordReqVO.setRoleType("roleType");
        gradePayRecordReqVO.setNum(0);
        gradePayRecordReqVO.setUnit(0);
        gradePayRecordReqVO.setChangeType(0);
        gradePayRecordReqVO.setRemark("remark");
        gradePayRecordReqVO.setOperSubjectGuid("operSubjectGuid");
        gradePayRecordReqVO.setPayAmount(new BigDecimal("0.00"));

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("83975bd3-be7a-445f-9fce-0bc532568025");

        // Configure HsaMemberGradeInfoMapper.selectOne(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setId(0L);
        hsaMemberGradeInfo.setGuid("fe276dcc-6880-4bff-a68b-45093d3b67a8");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setName("memberInfoGradeName");
        hsaMemberGradeInfo.setIsDelete(0);
        when(mockMemberGradeInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfo);

        // Configure HsaOperationMemberInfoMapper.selectList(...).
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("guid");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setPhoneNum("phoneNum");
        hsaOperationMemberInfo.setUserName("memberInfoName");
        final List<HsaOperationMemberInfo> hsaOperationMemberInfos = Arrays.asList(hsaOperationMemberInfo);
        when(mockOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperationMemberInfos);

        // Run the test
        hsaMemberGradePayRecordServiceImplUnderTest.batchAdd(gradePayRecordReqVO);

        // Verify the results
    }

    @Test
    public void testBatchAdd_HsaOperationMemberInfoMapperReturnsNoItems() {
        // Setup
        final MemberGradePayRecordReqVO gradePayRecordReqVO = new MemberGradePayRecordReqVO();
        gradePayRecordReqVO.setMemberGuidList(Arrays.asList("value"));
        gradePayRecordReqVO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        gradePayRecordReqVO.setRoleType("roleType");
        gradePayRecordReqVO.setNum(0);
        gradePayRecordReqVO.setUnit(0);
        gradePayRecordReqVO.setChangeType(0);
        gradePayRecordReqVO.setRemark("remark");
        gradePayRecordReqVO.setOperSubjectGuid("operSubjectGuid");
        gradePayRecordReqVO.setPayAmount(new BigDecimal("0.00"));

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("83975bd3-be7a-445f-9fce-0bc532568025");

        // Configure HsaMemberGradeInfoMapper.selectOne(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setId(0L);
        hsaMemberGradeInfo.setGuid("fe276dcc-6880-4bff-a68b-45093d3b67a8");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setName("memberInfoGradeName");
        hsaMemberGradeInfo.setIsDelete(0);
        when(mockMemberGradeInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMemberGradeInfo);

        when(mockOperationMemberInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(
                () -> hsaMemberGradePayRecordServiceImplUnderTest.batchAdd(gradePayRecordReqVO))
                .isInstanceOf(BusinessException.class);
    }
}
