package com.holderzone.member.queue.utils;

import org.junit.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.GregorianCalendar;

import static org.assertj.core.api.Assertions.assertThat;

public class DateUtilTest {

    @Test
    public void testFormatDate() {
        assertThat(DateUtil.formatDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isEqualTo("result");
    }

    @Test
    public void testFormatDateTime() {
        assertThat(DateUtil.formatDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
                .isEqualTo("result");
    }

    @Test
    public void testFormat() {
        assertThat(DateUtil.format(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "patten"))
                .isEqualTo("result");
    }

    @Test
    public void testParseDate() {
        assertThat(DateUtil.parseDate("dateString"))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testParseDateTime() {
        assertThat(DateUtil.parseDateTime("dateString"))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testParse() {
        assertThat(DateUtil.parse("dateString", "patten"))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testAddDays() {
        assertThat(DateUtil.addDays(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testAddYears() {
        assertThat(DateUtil.addYears(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testAddMonths() {
        assertThat(DateUtil.addMonths(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testAddHours() {
        assertThat(DateUtil.addHours(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testAddMinutes() {
        assertThat(DateUtil.addMinutes(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testGetFirstDayOfMonth() {
        assertThat(DateUtil.getFirstDayOfMonth(2020, 1)).isEqualTo("result");
    }

    @Test
    public void testGetMinToday() {
        assertThat(DateUtil.getMinToday()).isEqualTo("result");
    }

    @Test
    public void testGetLastDayOfMonth() {
        assertThat(DateUtil.getLastDayOfMonth(2020, 1)).isEqualTo("result");
    }

    @Test
    public void testGetPreYearDay() {
        assertThat(DateUtil.getPreYearDay("day")).isEqualTo("result");
    }

    @Test
    public void testGetPreYearMonth() {
        assertThat(DateUtil.getPreYearMonth("month")).isEqualTo("result");
    }

    @Test
    public void testLocalDate2Date() {
        assertThat(DateUtil.localDate2Date(LocalDate.of(2020, 1, 1)))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testDate2LocalDate() {
        assertThat(DateUtil.date2LocalDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
                .isEqualTo(LocalDate.of(2020, 1, 1));
    }

    @Test
    public void testMain() {
        // Setup
        // Run the test
        DateUtil.main(new String[]{"args"});

        // Verify the results
    }

    @Test
    public void testLoseDateTimeProcessor() {
        assertThat(DateUtil.loseDateTimeProcessor(0, "fixedValueValidityDate", 0, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0))).isEqualTo(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }

    @Test
    public void testGetDayStartTime() {
        assertThat(DateUtil.getDayStartTime()).isEqualTo(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }

    @Test
    public void testGetDayEndTime() {
        assertThat(DateUtil.getDayEndTime()).isEqualTo(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }

    @Test
    public void testGetWeekStartTime() {
        assertThat(DateUtil.getWeekStartTime()).isEqualTo(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }

    @Test
    public void testGetWeekEndTime() {
        assertThat(DateUtil.getWeekEndTime()).isEqualTo(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }

    @Test
    public void testGetMonthStartTime() {
        assertThat(DateUtil.getMonthStartTime()).isEqualTo(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }

    @Test
    public void testGetMonthEndTime() {
        assertThat(DateUtil.getMonthEndTime()).isEqualTo(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }

    @Test
    public void testGetStartOrEndDayOfWeek() {
        assertThat(DateUtil.getStartOrEndDayOfWeek(LocalDate.of(2020, 1, 1), false))
                .isEqualTo(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }
}
