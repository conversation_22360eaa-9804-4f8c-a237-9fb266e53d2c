package com.holderzone.member.queue.service.grade.impl;

import com.holderzone.member.queue.dto.grade.DoubleValueRequest;
import com.holderzone.member.queue.entity.HsaBusinessEquities;
import com.holderzone.member.queue.entity.HsaMemberEquitiesReceiveRecord;
import org.junit.Before;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class CheckTotalFilterTest {

    private CheckTotalFilter checkTotalFilterUnderTest;

    @Before
    public void setUp() throws Exception {
        checkTotalFilterUnderTest = new CheckTotalFilter();
    }

    @Test
    public void testDoFilter() {
        // Setup
        final DoubleValueRequest request = DoubleValueRequest.builder()
                .memberInfoGuid("memberInfoGuid")
                .doubleValue(0L)
                .remainingValue(0)
                .build();
        final HsaBusinessEquities hsaBusinessEquities = HsaBusinessEquities.builder()
                .totalDoubleCountLimited(0)
                .totalDoubleCountUpperLimit(0)
                .build();
        final HsaMemberEquitiesReceiveRecord equitiesReceiveRecord = new HsaMemberEquitiesReceiveRecord();
        equitiesReceiveRecord.setGuid("aa2cb850-d848-402e-8229-29a6f33c398b");
        equitiesReceiveRecord.setOperSubjectGuid("operSubjectGuid");
        equitiesReceiveRecord.setMemberGradeGuid("memberGradeGuid");
        equitiesReceiveRecord.setMemberInfoGuid("memberInfoGuid");
        equitiesReceiveRecord.setTotalDoubleUpperValue(0);

        // Run the test
        final boolean result = checkTotalFilterUnderTest.doFilter(request, hsaBusinessEquities, equitiesReceiveRecord);

        // Verify the results
        assertThat(result).isFalse();
    }
}
