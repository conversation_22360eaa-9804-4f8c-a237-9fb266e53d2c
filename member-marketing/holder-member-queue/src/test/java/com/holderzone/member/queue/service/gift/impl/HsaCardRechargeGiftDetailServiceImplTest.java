package com.holderzone.member.queue.service.gift.impl;

import com.holderzone.member.common.qo.card.CardFreezeBalanceAmountQO;
import com.holderzone.member.queue.entity.HsaMemberInfoCard;
import com.holderzone.member.queue.mapper.HsaMemberInfoCardMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaCardRechargeGiftDetailServiceImplTest {

    @Mock
    private HsaMemberInfoCardMapper mockHsaMemberInfoCardMapper;

    private HsaCardRechargeGiftDetailServiceImpl hsaCardRechargeGiftDetailServiceImplUnderTest;

    @Before
    public void setUp() {
        hsaCardRechargeGiftDetailServiceImplUnderTest = new HsaCardRechargeGiftDetailServiceImpl(
                mockHsaMemberInfoCardMapper);
    }

    @Test
    public void testGetMemberCardFreezeAmountMap() {
        // Setup
        final CardFreezeBalanceAmountQO cardFreezeBalanceAmountQO = new CardFreezeBalanceAmountQO();
        cardFreezeBalanceAmountQO.setPhysicalCardGuid("physicalCardGuid");
        cardFreezeBalanceAmountQO.setElectronicCardGuid("electronicCardGuid");
        cardFreezeBalanceAmountQO.setMemberInfoGuid("memberInfoGuid");
        cardFreezeBalanceAmountQO.setMemberInfoCardGuid("memberInfoCardGuid");
        cardFreezeBalanceAmountQO.setExcludeMemberFundingDetail("ExcludeMemberFundingDetail");
        final List<CardFreezeBalanceAmountQO> cardList = Arrays.asList(cardFreezeBalanceAmountQO);
        final Map<String, BigDecimal> expectedResult = new HashMap<>();

        // Configure HsaMemberInfoCardMapper.queryByGuids(...).
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setId(0L);
        hsaMemberInfoCard.setGuid("a5c0096c-555e-41e1-b0c4-6f15fea0c9b3");
        hsaMemberInfoCard.setMemberInfoGuid("memberInfoGuid");
        hsaMemberInfoCard.setPhysicalCardGuid("physicalCardGuid");
        hsaMemberInfoCard.setElectronicCardGuid("electronicCardGuid");
        final List<HsaMemberInfoCard> hsaMemberInfoCards = Arrays.asList(hsaMemberInfoCard);
        when(mockHsaMemberInfoCardMapper.queryByGuids(Arrays.asList("value"))).thenReturn(hsaMemberInfoCards);

        // Run the test
        final Map<String, BigDecimal> result = hsaCardRechargeGiftDetailServiceImplUnderTest.getMemberCardFreezeAmountMap(
                cardList);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetMemberCardFreezeAmountMap_HsaMemberInfoCardMapperReturnsNoItems() {
        // Setup
        final CardFreezeBalanceAmountQO cardFreezeBalanceAmountQO = new CardFreezeBalanceAmountQO();
        cardFreezeBalanceAmountQO.setPhysicalCardGuid("physicalCardGuid");
        cardFreezeBalanceAmountQO.setElectronicCardGuid("electronicCardGuid");
        cardFreezeBalanceAmountQO.setMemberInfoGuid("memberInfoGuid");
        cardFreezeBalanceAmountQO.setMemberInfoCardGuid("memberInfoCardGuid");
        cardFreezeBalanceAmountQO.setExcludeMemberFundingDetail("ExcludeMemberFundingDetail");
        final List<CardFreezeBalanceAmountQO> cardList = Arrays.asList(cardFreezeBalanceAmountQO);
        final Map<String, BigDecimal> expectedResult = new HashMap<>();
        when(mockHsaMemberInfoCardMapper.queryByGuids(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final Map<String, BigDecimal> result = hsaCardRechargeGiftDetailServiceImplUnderTest.getMemberCardFreezeAmountMap(
                cardList);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
