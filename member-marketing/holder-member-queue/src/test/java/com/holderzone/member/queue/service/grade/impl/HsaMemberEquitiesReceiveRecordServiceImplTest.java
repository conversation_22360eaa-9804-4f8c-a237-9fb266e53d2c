package com.holderzone.member.queue.service.grade.impl;

import com.holderzone.member.queue.entity.HsaMemberEquitiesReceiveRecord;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class HsaMemberEquitiesReceiveRecordServiceImplTest {

    private HsaMemberEquitiesReceiveRecordServiceImpl hsaMemberEquitiesReceiveRecordServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        hsaMemberEquitiesReceiveRecordServiceImplUnderTest = new HsaMemberEquitiesReceiveRecordServiceImpl();
    }

    @Test
    public void testQueryByGuid() {
        // Setup
        final HsaMemberEquitiesReceiveRecord expectedResult = new HsaMemberEquitiesReceiveRecord();
        expectedResult.setGuid("7cf65029-89d7-4da7-b427-1463fa0c8220");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setMemberGradeGuid("memberGradeGuid");
        expectedResult.setMemberInfoGuid("memberInfoGuid");
        expectedResult.setGradeEquitiesGuid("gradeEquitiesGuid");

        // Run the test
        final HsaMemberEquitiesReceiveRecord result = hsaMemberEquitiesReceiveRecordServiceImplUnderTest.queryByGuid(
                "0c358478-ecc9-4304-8922-9c53e4e425a7");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryByGuids() {
        // Setup
        final HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord = new HsaMemberEquitiesReceiveRecord();
        hsaMemberEquitiesReceiveRecord.setGuid("7cf65029-89d7-4da7-b427-1463fa0c8220");
        hsaMemberEquitiesReceiveRecord.setOperSubjectGuid("operSubjectGuid");
        hsaMemberEquitiesReceiveRecord.setMemberGradeGuid("memberGradeGuid");
        hsaMemberEquitiesReceiveRecord.setMemberInfoGuid("memberInfoGuid");
        hsaMemberEquitiesReceiveRecord.setGradeEquitiesGuid("gradeEquitiesGuid");
        final List<HsaMemberEquitiesReceiveRecord> expectedResult = Arrays.asList(hsaMemberEquitiesReceiveRecord);

        // Run the test
        final List<HsaMemberEquitiesReceiveRecord> result = hsaMemberEquitiesReceiveRecordServiceImplUnderTest.queryByGuids(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateByGuid() {
        // Setup
        final HsaMemberEquitiesReceiveRecord t = new HsaMemberEquitiesReceiveRecord();
        t.setGuid("7cf65029-89d7-4da7-b427-1463fa0c8220");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberGradeGuid("memberGradeGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setGradeEquitiesGuid("gradeEquitiesGuid");

        // Run the test
        final boolean result = hsaMemberEquitiesReceiveRecordServiceImplUnderTest.updateByGuid(t);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemoveByGuid() {
        // Setup
        // Run the test
        final boolean result = hsaMemberEquitiesReceiveRecordServiceImplUnderTest.removeByGuid(
                "31fad6b1-3197-4dee-982c-00a41840cbe7");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemoveByGuids() {
        // Setup
        // Run the test
        final boolean result = hsaMemberEquitiesReceiveRecordServiceImplUnderTest.removeByGuids(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isFalse();
    }
}
