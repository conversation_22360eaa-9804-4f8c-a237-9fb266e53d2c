package com.holderzone.member.queue.service.card.assembler;

import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.number.SerialNumberBuilder;
import com.holderzone.member.queue.entity.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MemberCardAssemblerTest {

    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private SerialNumberBuilder mockSerialNumberBuilderUtil;

    @InjectMocks
    private MemberCardAssembler memberCardAssemblerUnderTest;

    @Test
    public void testOpenCardProcessor() {
        // Setup
        final HsaElectronicCard electronicCard = new HsaElectronicCard();
        electronicCard.setGuid("electronicCardGuid");
        electronicCard.setOperSubjectGuid("operSubjectGuid");
        electronicCard.setEnterpriseGuid("enterpriseGuid");
        electronicCard.setMemberInfoGuid("guid");
        electronicCard.setCardGuid("cardGuid");
        electronicCard.setCardState(0);
        electronicCard.setCardNum("cardNum");
        electronicCard.setSource(0);
        electronicCard.setOpenWay(0);
        electronicCard.setOperationName("指定用户发卡");
        electronicCard.setOperationPhone("指定用户发卡");
        electronicCard.setOpenCardWay(0);
        electronicCard.setIsDelete(0);
        electronicCard.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        electronicCard.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("newMemberCardGuid");
        hsaMemberInfoCard.setOperSubjectGuid("operSubjectGuid");
        hsaMemberInfoCard.setEnterpriseGuid("enterpriseGuid");
        hsaMemberInfoCard.setMemberInfoGuid("guid");
        hsaMemberInfoCard.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setCardName("cardName");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberInfoCard.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberInfoCard.setCardValidity(0);
        hsaMemberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        hsaMemberInfoCard.setApplicableAllStore(0);
        hsaMemberInfoCard.setElectronicCardNum("cardNum");
        hsaMemberInfoCard.setPhysicalCardNum("physicalCardNum");
        hsaMemberInfoCard.setMemberPhoneNum("memberPhoneNum");
        hsaMemberInfoCard.setElectronicCardState(0);
        hsaMemberInfoCard.setElectronicOpenTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final HsaCardOpenRule hsaCardOpenRule = new HsaCardOpenRule();
        hsaCardOpenRule.setGuid("6431fa75-d53f-472f-8069-7e07a00ada01");
        hsaCardOpenRule.setOperSubjectGuid("operSubjectGuid");
        hsaCardOpenRule.setEnterpriseGuid("enterpriseGuid");
        hsaCardOpenRule.setCardGuid("cardGuid");
        hsaCardOpenRule.setIsSupportElectronicCard(0);

        final HsaCardBaseInfo hsaCardBaseInfo = new HsaCardBaseInfo();
        hsaCardBaseInfo.setCardName("cardName");
        hsaCardBaseInfo.setApplicableAllStore(0);
        hsaCardBaseInfo.setCardValidity(0);
        hsaCardBaseInfo.setValidityUnit(0);
        hsaCardBaseInfo.setCardValidityTime(0);
        hsaCardBaseInfo.setCardValidityDate(LocalDate.of(2020, 1, 1));

        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("guid");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberAccount("memberAccount");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setPhoneNum("memberPhoneNum");

        final HsaMemberInfoCard hsaMemberInfoCard1 = new HsaMemberInfoCard();
        hsaMemberInfoCard1.setGuid("newMemberCardGuid");
        hsaMemberInfoCard1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberInfoCard1.setEnterpriseGuid("enterpriseGuid");
        hsaMemberInfoCard1.setMemberInfoGuid("guid");
        hsaMemberInfoCard1.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard1.setCardGuid("cardGuid");
        hsaMemberInfoCard1.setCardName("cardName");
        hsaMemberInfoCard1.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberInfoCard1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberInfoCard1.setCardValidity(0);
        hsaMemberInfoCard1.setCardValidityDate(LocalDate.of(2020, 1, 1));
        hsaMemberInfoCard1.setApplicableAllStore(0);
        hsaMemberInfoCard1.setElectronicCardNum("cardNum");
        hsaMemberInfoCard1.setPhysicalCardNum("physicalCardNum");
        hsaMemberInfoCard1.setMemberPhoneNum("memberPhoneNum");
        hsaMemberInfoCard1.setElectronicCardState(0);
        hsaMemberInfoCard1.setElectronicOpenTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMemberInfoCard> hsaMemberInfoCardList = Arrays.asList(hsaMemberInfoCard1);
        final HsaElectronicCard hsaElectronicCard = new HsaElectronicCard();
        hsaElectronicCard.setGuid("electronicCardGuid");
        hsaElectronicCard.setOperSubjectGuid("operSubjectGuid");
        hsaElectronicCard.setEnterpriseGuid("enterpriseGuid");
        hsaElectronicCard.setMemberInfoGuid("guid");
        hsaElectronicCard.setCardGuid("cardGuid");
        hsaElectronicCard.setCardState(0);
        hsaElectronicCard.setCardNum("cardNum");
        hsaElectronicCard.setSource(0);
        hsaElectronicCard.setOpenWay(0);
        hsaElectronicCard.setOperationName("指定用户发卡");
        hsaElectronicCard.setOperationPhone("指定用户发卡");
        hsaElectronicCard.setOpenCardWay(0);
        hsaElectronicCard.setIsDelete(0);
        hsaElectronicCard.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaElectronicCard.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaElectronicCard> hsaElectronicCardList = Arrays.asList(hsaElectronicCard);
        when(mockSerialNumberBuilderUtil.getSequence("prefix")).thenReturn("cardNum");
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("electronicCardGuid");

        // Run the test
        memberCardAssemblerUnderTest.openCardProcessor(electronicCard, hsaMemberInfoCard, hsaCardOpenRule,
                hsaCardBaseInfo, hsaOperationMemberInfo, hsaMemberInfoCardList, hsaElectronicCardList);

        // Verify the results
    }

    @Test
    public void testAddStoreCardRuleArray() {
        // Setup
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("newMemberCardGuid");
        hsaMemberInfoCard.setOperSubjectGuid("operSubjectGuid");
        hsaMemberInfoCard.setEnterpriseGuid("enterpriseGuid");
        hsaMemberInfoCard.setMemberInfoGuid("guid");
        hsaMemberInfoCard.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setCardName("cardName");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberInfoCard.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberInfoCard.setCardValidity(0);
        hsaMemberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        hsaMemberInfoCard.setApplicableAllStore(0);
        hsaMemberInfoCard.setElectronicCardNum("cardNum");
        hsaMemberInfoCard.setPhysicalCardNum("physicalCardNum");
        hsaMemberInfoCard.setMemberPhoneNum("memberPhoneNum");
        hsaMemberInfoCard.setElectronicCardState(0);
        hsaMemberInfoCard.setElectronicOpenTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        hsaStoreCardRule.setGuid("guid");
        hsaStoreCardRule.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule.setStoreGuid("storeGuid");
        hsaStoreCardRule.setStoreName("storeName");
        hsaStoreCardRule.setStoreNumber("storeNumber");
        hsaStoreCardRule.setTime("time");
        hsaStoreCardRule.setAddress("address");
        hsaStoreCardRule.setCardGuid("cardGuid");
        hsaStoreCardRule.setParentGuid("guid");
        hsaStoreCardRule.setMemberInfoCardGuid("newMemberCardGuid");
        hsaStoreCardRule.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaStoreCardRule.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaStoreCardRule.setAddressPoint("addressPoint");
        final List<HsaStoreCardRule> hsaStoreCardRuleList = Arrays.asList(hsaStoreCardRule);
        final Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap = new HashMap<>();
        final HsaStoreCardRule hsaStoreCardRule1 = new HsaStoreCardRule();
        hsaStoreCardRule1.setGuid("guid");
        hsaStoreCardRule1.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule1.setStoreGuid("storeGuid");
        hsaStoreCardRule1.setStoreName("storeName");
        hsaStoreCardRule1.setStoreNumber("storeNumber");
        hsaStoreCardRule1.setTime("time");
        hsaStoreCardRule1.setAddress("address");
        hsaStoreCardRule1.setCardGuid("cardGuid");
        hsaStoreCardRule1.setParentGuid("guid");
        hsaStoreCardRule1.setMemberInfoCardGuid("newMemberCardGuid");
        hsaStoreCardRule1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaStoreCardRule1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaStoreCardRule1.setAddressPoint("addressPoint");
        final List<HsaStoreCardRule> hsaStoreCardRuleArrayList = Arrays.asList(hsaStoreCardRule1);
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("guid");

        // Run the test
        memberCardAssemblerUnderTest.addStoreCardRuleArray(hsaMemberInfoCard, hsaStoreCardRuleList, hsaStoreCardRuleMap,
                hsaStoreCardRuleArrayList);

        // Verify the results
    }

    @Test
    public void testAddOpenCardBalanceRecord() {
        // Setup
        final HsaMemberInfoCard memberInfoCard = new HsaMemberInfoCard();
        memberInfoCard.setGuid("newMemberCardGuid");
        memberInfoCard.setOperSubjectGuid("operSubjectGuid");
        memberInfoCard.setEnterpriseGuid("enterpriseGuid");
        memberInfoCard.setMemberInfoGuid("guid");
        memberInfoCard.setElectronicCardGuid("electronicCardGuid");
        memberInfoCard.setCardGuid("cardGuid");
        memberInfoCard.setCardName("cardName");
        memberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        memberInfoCard.setCardAmount(new BigDecimal("0.00"));
        memberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        memberInfoCard.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberInfoCard.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberInfoCard.setCardValidity(0);
        memberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        memberInfoCard.setApplicableAllStore(0);
        memberInfoCard.setElectronicCardNum("cardNum");
        memberInfoCard.setPhysicalCardNum("physicalCardNum");
        memberInfoCard.setMemberPhoneNum("memberPhoneNum");
        memberInfoCard.setElectronicCardState(0);
        memberInfoCard.setElectronicOpenTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final HsaMemberFundingDetail expectedResult = new HsaMemberFundingDetail();
        expectedResult.setGuid("001f4c52-a7ad-40e0-9936-95653e9dc383");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setMemberInfoGuid("guid");
        expectedResult.setCardNum("cardNum");
        expectedResult.setCardName("cardName");
        expectedResult.setCardGuid("cardGuid");
        expectedResult.setCardType(0);
        expectedResult.setMemberInfoCardGuid("newMemberCardGuid");
        expectedResult.setAmountSourceType(0);
        expectedResult.setAmountRechargeFundingType(0);
        expectedResult.setRechargeAmount(new BigDecimal("0.00"));
        expectedResult.setCardRechargeResidualBalance(new BigDecimal("0.00"));
        expectedResult.setCardGiftResidualBalance(new BigDecimal("0.00"));
        expectedResult.setCardSubsidyResidualBalance(new BigDecimal("0.00"));
        expectedResult.setRemark("des");
        expectedResult.setOperatorTelName("指定发卡");
        expectedResult.setIsValid(0);
        expectedResult.setChangeSource(0);
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("001f4c52-a7ad-40e0-9936-95653e9dc383");

        // Run the test
        final HsaMemberFundingDetail result = memberCardAssemblerUnderTest.addOpenCardBalanceRecord(memberInfoCard,
                new BigDecimal("0.00"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testAddStoreCardRuleArrayList() {
        // Setup
        final HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        hsaStoreCardRule.setGuid("guid");
        hsaStoreCardRule.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule.setStoreGuid("storeGuid");
        hsaStoreCardRule.setStoreName("storeName");
        hsaStoreCardRule.setStoreNumber("storeNumber");
        hsaStoreCardRule.setTime("time");
        hsaStoreCardRule.setAddress("address");
        hsaStoreCardRule.setCardGuid("cardGuid");
        hsaStoreCardRule.setParentGuid("guid");
        hsaStoreCardRule.setMemberInfoCardGuid("newMemberCardGuid");
        hsaStoreCardRule.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaStoreCardRule.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaStoreCardRule.setAddressPoint("addressPoint");
        final List<HsaStoreCardRule> hsaStoreCardRuleList = Arrays.asList(hsaStoreCardRule);
        final Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap = new HashMap<>();
        final HsaStoreCardRule hsaStoreCardRule1 = new HsaStoreCardRule();
        hsaStoreCardRule1.setGuid("guid");
        hsaStoreCardRule1.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule1.setStoreGuid("storeGuid");
        hsaStoreCardRule1.setStoreName("storeName");
        hsaStoreCardRule1.setStoreNumber("storeNumber");
        hsaStoreCardRule1.setTime("time");
        hsaStoreCardRule1.setAddress("address");
        hsaStoreCardRule1.setCardGuid("cardGuid");
        hsaStoreCardRule1.setParentGuid("guid");
        hsaStoreCardRule1.setMemberInfoCardGuid("newMemberCardGuid");
        hsaStoreCardRule1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaStoreCardRule1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaStoreCardRule1.setAddressPoint("addressPoint");
        final List<HsaStoreCardRule> hsaStoreCardRuleArrayList = Arrays.asList(hsaStoreCardRule1);
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("guid");

        // Run the test
        memberCardAssemblerUnderTest.addStoreCardRuleArrayList("cardGuid", "newMemberCardGuid", hsaStoreCardRuleList,
                hsaStoreCardRuleMap, hsaStoreCardRuleArrayList);

        // Verify the results
    }

    @Test
    public void testSetStall() {
        // Setup
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("newMemberCardGuid");
        hsaMemberInfoCard.setOperSubjectGuid("operSubjectGuid");
        hsaMemberInfoCard.setEnterpriseGuid("enterpriseGuid");
        hsaMemberInfoCard.setMemberInfoGuid("guid");
        hsaMemberInfoCard.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setCardName("cardName");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberInfoCard.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberInfoCard.setCardValidity(0);
        hsaMemberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        hsaMemberInfoCard.setApplicableAllStore(0);
        hsaMemberInfoCard.setElectronicCardNum("cardNum");
        hsaMemberInfoCard.setPhysicalCardNum("physicalCardNum");
        hsaMemberInfoCard.setMemberPhoneNum("memberPhoneNum");
        hsaMemberInfoCard.setElectronicCardState(0);
        hsaMemberInfoCard.setElectronicOpenTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        hsaStoreCardRule.setGuid("guid");
        hsaStoreCardRule.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule.setStoreGuid("storeGuid");
        hsaStoreCardRule.setStoreName("storeName");
        hsaStoreCardRule.setStoreNumber("storeNumber");
        hsaStoreCardRule.setTime("time");
        hsaStoreCardRule.setAddress("address");
        hsaStoreCardRule.setCardGuid("cardGuid");
        hsaStoreCardRule.setParentGuid("guid");
        hsaStoreCardRule.setMemberInfoCardGuid("newMemberCardGuid");
        hsaStoreCardRule.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaStoreCardRule.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaStoreCardRule.setAddressPoint("addressPoint");
        final Set<HsaStoreCardRule> hsaStoreCardRuleSet = new HashSet<>(Arrays.asList(hsaStoreCardRule));
        final Map<String, String> map = new HashMap<>();
        final HsaStoreCardRule rule = new HsaStoreCardRule();
        rule.setGuid("guid");
        rule.setOperSubjectGuid("operSubjectGuid");
        rule.setStoreGuid("storeGuid");
        rule.setStoreName("storeName");
        rule.setStoreNumber("storeNumber");
        rule.setTime("time");
        rule.setAddress("address");
        rule.setCardGuid("cardGuid");
        rule.setParentGuid("guid");
        rule.setMemberInfoCardGuid("newMemberCardGuid");
        rule.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rule.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rule.setAddressPoint("addressPoint");

        final HsaStoreCardRule hsaStoreCardRule1 = new HsaStoreCardRule();
        hsaStoreCardRule1.setGuid("guid");
        hsaStoreCardRule1.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule1.setStoreGuid("storeGuid");
        hsaStoreCardRule1.setStoreName("storeName");
        hsaStoreCardRule1.setStoreNumber("storeNumber");
        hsaStoreCardRule1.setTime("time");
        hsaStoreCardRule1.setAddress("address");
        hsaStoreCardRule1.setCardGuid("cardGuid");
        hsaStoreCardRule1.setParentGuid("guid");
        hsaStoreCardRule1.setMemberInfoCardGuid("newMemberCardGuid");
        hsaStoreCardRule1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaStoreCardRule1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaStoreCardRule1.setAddressPoint("addressPoint");
        final List<HsaStoreCardRule> storeCardRule = Arrays.asList(hsaStoreCardRule1);
        final HsaStoreCardRule in = new HsaStoreCardRule();
        in.setGuid("guid");
        in.setOperSubjectGuid("operSubjectGuid");
        in.setStoreGuid("storeGuid");
        in.setStoreName("storeName");
        in.setStoreNumber("storeNumber");
        in.setTime("time");
        in.setAddress("address");
        in.setCardGuid("cardGuid");
        in.setParentGuid("guid");
        in.setMemberInfoCardGuid("newMemberCardGuid");
        in.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        in.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        in.setAddressPoint("addressPoint");

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("guid");

        // Run the test
        memberCardAssemblerUnderTest.setStall(hsaMemberInfoCard, hsaStoreCardRuleSet, map, rule, storeCardRule, in);

        // Verify the results
    }

    @Test
    public void testSeStore() {
        // Setup
        final HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setGuid("newMemberCardGuid");
        hsaMemberInfoCard.setOperSubjectGuid("operSubjectGuid");
        hsaMemberInfoCard.setEnterpriseGuid("enterpriseGuid");
        hsaMemberInfoCard.setMemberInfoGuid("guid");
        hsaMemberInfoCard.setElectronicCardGuid("electronicCardGuid");
        hsaMemberInfoCard.setCardGuid("cardGuid");
        hsaMemberInfoCard.setCardName("cardName");
        hsaMemberInfoCard.setSubsidyAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setCardAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGiftAmount(new BigDecimal("0.00"));
        hsaMemberInfoCard.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberInfoCard.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberInfoCard.setCardValidity(0);
        hsaMemberInfoCard.setCardValidityDate(LocalDate.of(2020, 1, 1));
        hsaMemberInfoCard.setApplicableAllStore(0);
        hsaMemberInfoCard.setElectronicCardNum("cardNum");
        hsaMemberInfoCard.setPhysicalCardNum("physicalCardNum");
        hsaMemberInfoCard.setMemberPhoneNum("memberPhoneNum");
        hsaMemberInfoCard.setElectronicCardState(0);
        hsaMemberInfoCard.setElectronicOpenTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        hsaStoreCardRule.setGuid("guid");
        hsaStoreCardRule.setOperSubjectGuid("operSubjectGuid");
        hsaStoreCardRule.setStoreGuid("storeGuid");
        hsaStoreCardRule.setStoreName("storeName");
        hsaStoreCardRule.setStoreNumber("storeNumber");
        hsaStoreCardRule.setTime("time");
        hsaStoreCardRule.setAddress("address");
        hsaStoreCardRule.setCardGuid("cardGuid");
        hsaStoreCardRule.setParentGuid("guid");
        hsaStoreCardRule.setMemberInfoCardGuid("newMemberCardGuid");
        hsaStoreCardRule.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaStoreCardRule.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaStoreCardRule.setAddressPoint("addressPoint");
        final Set<HsaStoreCardRule> hsaStoreCardRuleSet = new HashSet<>(Arrays.asList(hsaStoreCardRule));
        final Map<String, String> map = new HashMap<>();
        final HsaStoreCardRule rule = new HsaStoreCardRule();
        rule.setGuid("guid");
        rule.setOperSubjectGuid("operSubjectGuid");
        rule.setStoreGuid("storeGuid");
        rule.setStoreName("storeName");
        rule.setStoreNumber("storeNumber");
        rule.setTime("time");
        rule.setAddress("address");
        rule.setCardGuid("cardGuid");
        rule.setParentGuid("guid");
        rule.setMemberInfoCardGuid("newMemberCardGuid");
        rule.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rule.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rule.setAddressPoint("addressPoint");

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("guid");

        // Run the test
        memberCardAssemblerUnderTest.seStore(hsaMemberInfoCard, hsaStoreCardRuleSet, map, rule);

        // Verify the results
    }
}
