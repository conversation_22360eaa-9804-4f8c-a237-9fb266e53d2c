package com.holderzone.member.queue.service.assembler;

import com.holderzone.member.common.dto.base.ResOrderCommodity;
import com.holderzone.member.common.dto.grade.MemberGradeRelationDTO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.grade.MemberGradePayRecordReqVO;
import com.holderzone.member.queue.dto.grade.GradeRightsProcessorDTO;
import com.holderzone.member.queue.dto.grade.MemberGradeChangeDTO;
import com.holderzone.member.queue.dto.member.MemberGrowthValueEvent;
import com.holderzone.member.queue.entity.*;
import com.holderzone.member.queue.service.grade.HsaMemberGradeChangeDetailService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GradeChangeAssemblerTest {

    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private HsaMemberGradeChangeDetailService mockHsaMemberGradeChangeDetailService;

    @InjectMocks
    private GradeChangeAssembler gradeChangeAssemblerUnderTest;

    @Test
    public void testGetHsaGrowthValueDetail1() {
        // Setup
        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("afterChangeGradeGuid");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("afterChangeGradeName");
        hsaMemberGradeInfo.setGradeIcon("afterChangeGradeIcon");
        hsaMemberGradeInfo.setBackgroundColor("afterChangeBackgroundColor");
        hsaMemberGradeInfo.setGrowthValue(0);
        hsaMemberGradeInfo.setOperatorName("operatorAccountName");
        hsaMemberGradeInfo.setUpgradeType(0);

        final HsaBusinessEquities hsaBusinessEquities = HsaBusinessEquities.builder()
                .id(0L)
                .guid("4c8911ae-ae7b-470d-86be-f3585c460480")
                .memberGradeInfoGuid("memberGradeInfoGuid")
                .equitiesName("equitiesName")
                .equitiesNumber("equitiesNumber")
                .build();
        final MemberGradeChangeDTO gradeChangeDTO = new MemberGradeChangeDTO();
        gradeChangeDTO.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO.setSourceType(0);
        gradeChangeDTO.setRoleType("roleType");
        gradeChangeDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        gradeChangeDTO.setUnit(0);
        gradeChangeDTO.setNum(0);
        gradeChangeDTO.setRemark("remark");

        final HsaGrowthValueDetail expectedResult = new HsaGrowthValueDetail();
        expectedResult.setGuid("6180078b-0086-499a-b814-4769b5d436e4");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setGrowthValueTaskId("growthValueTaskId");
        expectedResult.setMemberInfoGuid("memberGuid");
        expectedResult.setTaskName("taskName");
        expectedResult.setRecordDeclaration("成长值失效");
        expectedResult.setTaskNumber("taskNumber");
        expectedResult.setTaskFinishTime(0);
        expectedResult.setGrowthValue(0);
        expectedResult.setGrowthValueType(0);
        expectedResult.setGrowthValidity(0);
        expectedResult.setTaskFinishDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGrowthValidityDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRemainGrowthValue(0);
        expectedResult.setGrowthValueSourceType(0);
        expectedResult.setRecordRemainGrowthValue(0);
        expectedResult.setMemberConsumptionGuid("memberConsumptionGuid");
        expectedResult.setOrderNumber("orderNumber");
        expectedResult.setOperatorAccountName("operatorAccountName");
        expectedResult.setStoreName("storeName");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setChangeType(0);
        expectedResult.setCurrentMemberLevel("currentMemberLevel");
        expectedResult.setTaskAction(0);
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRoleType("roleType");
        expectedResult.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setMemberInfoGradeGuid("afterChangeGradeGuid");
        expectedResult.setUnit(0);
        expectedResult.setUpgradeType(0);
        expectedResult.setNum(0);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("6180078b-0086-499a-b814-4769b5d436e4");

        // Run the test
        final HsaGrowthValueDetail result = gradeChangeAssemblerUnderTest.getHsaGrowthValueDetail("memberGuid", 0,
                hsaMemberGradeInfo, hsaBusinessEquities, 0, 0, gradeChangeDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetHsaIntegralValueDetail1() {
        // Setup
        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("afterChangeGradeGuid");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("afterChangeGradeName");
        hsaMemberGradeInfo.setGradeIcon("afterChangeGradeIcon");
        hsaMemberGradeInfo.setBackgroundColor("afterChangeBackgroundColor");
        hsaMemberGradeInfo.setGrowthValue(0);
        hsaMemberGradeInfo.setOperatorName("operatorAccountName");
        hsaMemberGradeInfo.setUpgradeType(0);

        final HsaBusinessEquities hsaBusinessEquities = HsaBusinessEquities.builder()
                .id(0L)
                .guid("4c8911ae-ae7b-470d-86be-f3585c460480")
                .memberGradeInfoGuid("memberGradeInfoGuid")
                .equitiesName("equitiesName")
                .equitiesNumber("equitiesNumber")
                .build();
        final HsaIntegralDetail expectedResult = new HsaIntegralDetail();
        expectedResult.setGuid("1c04b02f-c830-400b-a552-f6309dd1a83f");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setMemberInfoGuid("memberGuid");
        expectedResult.setRecordDeclaration("recordDeclaration");
        expectedResult.setDeclaration("declaration");
        expectedResult.setIntegralType(0);
        expectedResult.setIntegral(0);
        expectedResult.setIntegralValidity(0);
        expectedResult.setCurrentIntegral(0);
        expectedResult.setOperatorAccountName("operatorAccountName");
        expectedResult.setCurrentMemberLevel("currentMemberLevel");
        expectedResult.setRecordRemainIntegral(0);
        expectedResult.setChangeType(0);
        expectedResult.setIntegralSourceType(0);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("1c04b02f-c830-400b-a552-f6309dd1a83f");

        // Run the test
        final HsaIntegralDetail result = gradeChangeAssemblerUnderTest.getHsaIntegralValueDetail("memberGuid", 0,
                "operSubjectGuid", hsaMemberGradeInfo, hsaBusinessEquities, 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetHsaMemberEquitiesReceiveRecord() {
        // Setup
        final HsaMemberEquitiesReceiveRecord expectedResult = new HsaMemberEquitiesReceiveRecord();
        expectedResult.setGuid("4a051f38-dc07-46a9-81a9-083e04aceb19");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setMemberGradeGuid("memberGradeGuid");
        expectedResult.setMemberInfoGuid("memberGuid");
        expectedResult.setGradeEquitiesGuid("gradeEquitiesGuid");
        expectedResult.setGiveGrowthValueNumber(0);
        expectedResult.setType(0);
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("4a051f38-dc07-46a9-81a9-083e04aceb19");

        // Run the test
        final HsaMemberEquitiesReceiveRecord result = gradeChangeAssemblerUnderTest.getHsaMemberEquitiesReceiveRecord(
                "operSubjectGuid", "memberGuid", "memberGradeGuid", "gradeEquitiesGuid", 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetMemberGradeChangeDetail() {
        // Setup
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("memberGuid");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setMemberIntegral(0);
        hsaOperationMemberInfo.setMemberGrowthValue(0);

        final HsaMemberGradeInfo afterMemberGradeInfo = new HsaMemberGradeInfo();
        afterMemberGradeInfo.setGuid("afterChangeGradeGuid");
        afterMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo.setVipGrade(0);
        afterMemberGradeInfo.setName("afterChangeGradeName");
        afterMemberGradeInfo.setGradeIcon("afterChangeGradeIcon");
        afterMemberGradeInfo.setBackgroundColor("afterChangeBackgroundColor");
        afterMemberGradeInfo.setGrowthValue(0);
        afterMemberGradeInfo.setOperatorName("operatorAccountName");
        afterMemberGradeInfo.setUpgradeType(0);

        final HsaMemberGradeInfo beforeMemberGradeInfo = new HsaMemberGradeInfo();
        beforeMemberGradeInfo.setGuid("afterChangeGradeGuid");
        beforeMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        beforeMemberGradeInfo.setVipGrade(0);
        beforeMemberGradeInfo.setName("afterChangeGradeName");
        beforeMemberGradeInfo.setGradeIcon("afterChangeGradeIcon");
        beforeMemberGradeInfo.setBackgroundColor("afterChangeBackgroundColor");
        beforeMemberGradeInfo.setGrowthValue(0);
        beforeMemberGradeInfo.setOperatorName("operatorAccountName");
        beforeMemberGradeInfo.setUpgradeType(0);

        final HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail = new HsaMemberGradeChangeDetail();
        hsaMemberGradeChangeDetail.setGuid("31b03fec-bf4a-48aa-a7c8-d217261269c3");
        hsaMemberGradeChangeDetail.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeChangeDetail.setMemberInfoGuid("memberGuid");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeName("afterChangeGradeName");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeGuid("afterChangeGradeGuid");
        hsaMemberGradeChangeDetail.setBeforeChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail.setAfterChangeGradeName("afterChangeGradeName");
        hsaMemberGradeChangeDetail.setAfterChangeGradeGuid("afterChangeGradeGuid");
        hsaMemberGradeChangeDetail.setAfterChangeGradeVipGrade(0);
        hsaMemberGradeChangeDetail.setGradeChangeType(0);
        hsaMemberGradeChangeDetail.setIsShowApplet(0);
        hsaMemberGradeChangeDetail.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberGradeChangeDetail.setAfterChangeGradeIcon("afterChangeGradeIcon");
        hsaMemberGradeChangeDetail.setAfterChangeBackgroundColor("afterChangeBackgroundColor");
        hsaMemberGradeChangeDetail.setAfterChangeGrowthValue(0);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("31b03fec-bf4a-48aa-a7c8-d217261269c3");

        // Run the test
        gradeChangeAssemblerUnderTest.getMemberGradeChangeDetail(hsaOperationMemberInfo, afterMemberGradeInfo,
                beforeMemberGradeInfo, hsaMemberGradeChangeDetail, LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
    }

    @Test
    public void testGetHsaMemberGradeRightsRecord() {
        // Setup
        final GradeRightsProcessorDTO gradeRightsProcessorDTO = new GradeRightsProcessorDTO();
        gradeRightsProcessorDTO.setUpdateMemberIntegralMap(new HashMap<>());
        gradeRightsProcessorDTO.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo.setId(0L);
        gradeRightsProcessorDTO.setHsaGiftBagBaseInfo(hsaGiftBagBaseInfo);
        gradeRightsProcessorDTO.setOperSubjectGuid("operSubjectGuid");

        final HsaOperationMemberInfo operationMemberInfo = new HsaOperationMemberInfo();
        operationMemberInfo.setGuid("memberGuid");
        operationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        operationMemberInfo.setMemberIntegral(0);
        operationMemberInfo.setMemberGrowthValue(0);

        final HsaGradeGiftBag hsaGradeGiftBag = new HsaGradeGiftBag();
        hsaGradeGiftBag.setId(0L);
        hsaGradeGiftBag.setGuid("guid");
        hsaGradeGiftBag.setOperSubjectGuid("operSubjectGuid");
        hsaGradeGiftBag.setMemberGradeInfoGuid("memberGradeGuid");
        hsaGradeGiftBag.setValue(0);

        final HsaMemberGradeRightsRecord expectedResult = new HsaMemberGradeRightsRecord();
        expectedResult.setGuid("32a2781b-4fde-47ee-99e2-43ce48d0708f");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setMemberInfoGuid("memberGuid");
        expectedResult.setMemberGradeGuid("memberGradeGuid");
        expectedResult.setRightsGuid("guid");
        expectedResult.setRightsType(0);
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEquitiesId(0L);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("32a2781b-4fde-47ee-99e2-43ce48d0708f");

        // Run the test
        final HsaMemberGradeRightsRecord result = gradeChangeAssemblerUnderTest.getHsaMemberGradeRightsRecord(
                gradeRightsProcessorDTO, operationMemberInfo, hsaGradeGiftBag);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetMemberGradeRightsRecord() {
        // Setup
        final HsaOperationMemberInfo operationMemberInfo = new HsaOperationMemberInfo();
        operationMemberInfo.setGuid("memberGuid");
        operationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        operationMemberInfo.setMemberIntegral(0);
        operationMemberInfo.setMemberGrowthValue(0);

        final HsaBusinessEquities hsaGradeEquity = HsaBusinessEquities.builder()
                .id(0L)
                .guid("4c8911ae-ae7b-470d-86be-f3585c460480")
                .memberGradeInfoGuid("memberGradeInfoGuid")
                .equitiesName("equitiesName")
                .equitiesNumber("equitiesNumber")
                .build();
        final HsaMemberGradeRightsRecord expectedResult = new HsaMemberGradeRightsRecord();
        expectedResult.setGuid("32a2781b-4fde-47ee-99e2-43ce48d0708f");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setMemberInfoGuid("memberGuid");
        expectedResult.setMemberGradeGuid("memberGradeGuid");
        expectedResult.setRightsGuid("guid");
        expectedResult.setRightsType(0);
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEquitiesId(0L);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("32a2781b-4fde-47ee-99e2-43ce48d0708f");

        // Run the test
        final HsaMemberGradeRightsRecord result = gradeChangeAssemblerUnderTest.getMemberGradeRightsRecord(
                operationMemberInfo, "operSubjectGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0), hsaGradeEquity);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetHsaGrowthValueDetail2() {
        // Setup
        final HsaOperationMemberInfo operationMemberInfo = new HsaOperationMemberInfo();
        operationMemberInfo.setGuid("memberGuid");
        operationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        operationMemberInfo.setMemberIntegral(0);
        operationMemberInfo.setMemberGrowthValue(0);

        final HsaGradeGiftBag hsaGradeGiftBag = new HsaGradeGiftBag();
        hsaGradeGiftBag.setId(0L);
        hsaGradeGiftBag.setGuid("guid");
        hsaGradeGiftBag.setOperSubjectGuid("operSubjectGuid");
        hsaGradeGiftBag.setMemberGradeInfoGuid("memberGradeGuid");
        hsaGradeGiftBag.setValue(0);

        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("afterChangeGradeGuid");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("afterChangeGradeName");
        hsaMemberGradeInfo.setGradeIcon("afterChangeGradeIcon");
        hsaMemberGradeInfo.setBackgroundColor("afterChangeBackgroundColor");
        hsaMemberGradeInfo.setGrowthValue(0);
        hsaMemberGradeInfo.setOperatorName("operatorAccountName");
        hsaMemberGradeInfo.setUpgradeType(0);

        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo.setId(0L);
        hsaGiftBagBaseInfo.setGuid("76e9f795-f532-4e4d-a01f-84a33041b0c2");
        hsaGiftBagBaseInfo.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo.setEquitiesType(0);
        hsaGiftBagBaseInfo.setEquitiesName("equitiesName");

        final HsaMemberGradeInfo afterMemberGradeInfo = new HsaMemberGradeInfo();
        afterMemberGradeInfo.setGuid("afterChangeGradeGuid");
        afterMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo.setVipGrade(0);
        afterMemberGradeInfo.setName("afterChangeGradeName");
        afterMemberGradeInfo.setGradeIcon("afterChangeGradeIcon");
        afterMemberGradeInfo.setBackgroundColor("afterChangeBackgroundColor");
        afterMemberGradeInfo.setGrowthValue(0);
        afterMemberGradeInfo.setOperatorName("operatorAccountName");
        afterMemberGradeInfo.setUpgradeType(0);

        final HsaGrowthValueDetail expectedResult = new HsaGrowthValueDetail();
        expectedResult.setGuid("6180078b-0086-499a-b814-4769b5d436e4");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setGrowthValueTaskId("growthValueTaskId");
        expectedResult.setMemberInfoGuid("memberGuid");
        expectedResult.setTaskName("taskName");
        expectedResult.setRecordDeclaration("成长值失效");
        expectedResult.setTaskNumber("taskNumber");
        expectedResult.setTaskFinishTime(0);
        expectedResult.setGrowthValue(0);
        expectedResult.setGrowthValueType(0);
        expectedResult.setGrowthValidity(0);
        expectedResult.setTaskFinishDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGrowthValidityDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRemainGrowthValue(0);
        expectedResult.setGrowthValueSourceType(0);
        expectedResult.setRecordRemainGrowthValue(0);
        expectedResult.setMemberConsumptionGuid("memberConsumptionGuid");
        expectedResult.setOrderNumber("orderNumber");
        expectedResult.setOperatorAccountName("operatorAccountName");
        expectedResult.setStoreName("storeName");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setChangeType(0);
        expectedResult.setCurrentMemberLevel("currentMemberLevel");
        expectedResult.setTaskAction(0);
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRoleType("roleType");
        expectedResult.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setMemberInfoGradeGuid("afterChangeGradeGuid");
        expectedResult.setUnit(0);
        expectedResult.setUpgradeType(0);
        expectedResult.setNum(0);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("6180078b-0086-499a-b814-4769b5d436e4");

        // Run the test
        final HsaGrowthValueDetail result = gradeChangeAssemblerUnderTest.getHsaGrowthValueDetail(operationMemberInfo,
                0, "operSubjectGuid", hsaGradeGiftBag, hsaMemberGradeInfo, hsaGiftBagBaseInfo, afterMemberGradeInfo);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetHsaIntegralValueDetail2() {
        // Setup
        final HsaOperationMemberInfo operationMemberInfo = new HsaOperationMemberInfo();
        operationMemberInfo.setGuid("memberGuid");
        operationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        operationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        operationMemberInfo.setMemberIntegral(0);
        operationMemberInfo.setMemberGrowthValue(0);

        final HsaGradeGiftBag hsaGradeGiftBag = new HsaGradeGiftBag();
        hsaGradeGiftBag.setId(0L);
        hsaGradeGiftBag.setGuid("guid");
        hsaGradeGiftBag.setOperSubjectGuid("operSubjectGuid");
        hsaGradeGiftBag.setMemberGradeInfoGuid("memberGradeGuid");
        hsaGradeGiftBag.setValue(0);

        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("afterChangeGradeGuid");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("afterChangeGradeName");
        hsaMemberGradeInfo.setGradeIcon("afterChangeGradeIcon");
        hsaMemberGradeInfo.setBackgroundColor("afterChangeBackgroundColor");
        hsaMemberGradeInfo.setGrowthValue(0);
        hsaMemberGradeInfo.setOperatorName("operatorAccountName");
        hsaMemberGradeInfo.setUpgradeType(0);

        final HsaGiftBagBaseInfo hsaGiftBagBaseInfo = new HsaGiftBagBaseInfo();
        hsaGiftBagBaseInfo.setId(0L);
        hsaGiftBagBaseInfo.setGuid("76e9f795-f532-4e4d-a01f-84a33041b0c2");
        hsaGiftBagBaseInfo.setOperSubjectGuid("operSubjectGuid");
        hsaGiftBagBaseInfo.setEquitiesType(0);
        hsaGiftBagBaseInfo.setEquitiesName("equitiesName");

        final HsaMemberGradeInfo afterMemberGradeInfo = new HsaMemberGradeInfo();
        afterMemberGradeInfo.setGuid("afterChangeGradeGuid");
        afterMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        afterMemberGradeInfo.setVipGrade(0);
        afterMemberGradeInfo.setName("afterChangeGradeName");
        afterMemberGradeInfo.setGradeIcon("afterChangeGradeIcon");
        afterMemberGradeInfo.setBackgroundColor("afterChangeBackgroundColor");
        afterMemberGradeInfo.setGrowthValue(0);
        afterMemberGradeInfo.setOperatorName("operatorAccountName");
        afterMemberGradeInfo.setUpgradeType(0);

        final HsaIntegralDetail expectedResult = new HsaIntegralDetail();
        expectedResult.setGuid("1c04b02f-c830-400b-a552-f6309dd1a83f");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setMemberInfoGuid("memberGuid");
        expectedResult.setRecordDeclaration("recordDeclaration");
        expectedResult.setDeclaration("declaration");
        expectedResult.setIntegralType(0);
        expectedResult.setIntegral(0);
        expectedResult.setIntegralValidity(0);
        expectedResult.setCurrentIntegral(0);
        expectedResult.setOperatorAccountName("operatorAccountName");
        expectedResult.setCurrentMemberLevel("currentMemberLevel");
        expectedResult.setRecordRemainIntegral(0);
        expectedResult.setChangeType(0);
        expectedResult.setIntegralSourceType(0);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("1c04b02f-c830-400b-a552-f6309dd1a83f");

        // Run the test
        final HsaIntegralDetail result = gradeChangeAssemblerUnderTest.getHsaIntegralValueDetail(operationMemberInfo, 0,
                "operSubjectGuid", hsaGradeGiftBag, hsaMemberGradeInfo, hsaGiftBagBaseInfo, afterMemberGradeInfo);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetMemberGradePayRecordReqVO() {
        // Setup
        final MemberGradeChangeDTO gradeChangeDTO = new MemberGradeChangeDTO();
        gradeChangeDTO.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO.setSourceType(0);
        gradeChangeDTO.setRoleType("roleType");
        gradeChangeDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        gradeChangeDTO.setUnit(0);
        gradeChangeDTO.setNum(0);
        gradeChangeDTO.setRemark("remark");

        final MemberGradePayRecordReqVO expectedResult = new MemberGradePayRecordReqVO();
        expectedResult.setMemberGuidList(Arrays.asList("value"));
        expectedResult.setMemberInfoGradeGuid("memberInfoGradeGuid");
        expectedResult.setRoleType("roleType");
        expectedResult.setNum(0);
        expectedResult.setUnit(0);
        expectedResult.setChangeType(0);
        expectedResult.setRemark("remark");
        expectedResult.setOperSubjectGuid("operSubjectGuid");

        // Run the test
        final MemberGradePayRecordReqVO result = gradeChangeAssemblerUnderTest.getMemberGradePayRecordReqVO(
                gradeChangeDTO, Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetMemberGradeRelationDTO() {
        // Setup
        final MemberGradeChangeDTO gradeChangeDTO = new MemberGradeChangeDTO();
        gradeChangeDTO.setOperSubjectGuid("operSubjectGuid");
        gradeChangeDTO.setSourceType(0);
        gradeChangeDTO.setRoleType("roleType");
        gradeChangeDTO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        gradeChangeDTO.setUnit(0);
        gradeChangeDTO.setNum(0);
        gradeChangeDTO.setRemark("remark");

        final MemberGradeRelationDTO expectedResult = new MemberGradeRelationDTO();
        expectedResult.setMemberGuidList(Arrays.asList("value"));
        expectedResult.setMemberInfoGradeGuid("memberInfoGradeGuid");
        expectedResult.setRoleType("roleType");
        expectedResult.setNum(0);
        expectedResult.setUnit(0);
        expectedResult.setSourceType(0);
        expectedResult.setOperSubjectGuid("operSubjectGuid");

        // Run the test
        final MemberGradeRelationDTO result = gradeChangeAssemblerUnderTest.getMemberGradeRelationDTO(gradeChangeDTO,
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetHsaGrowthValueDetail3() {
        // Setup
        final HsaGrowthValueTask hsaGrowthValueTask = new HsaGrowthValueTask();
        hsaGrowthValueTask.setId(0L);
        hsaGrowthValueTask.setGuid("growthValueTaskGuid");
        hsaGrowthValueTask.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueTask.setTaskName("taskName");
        hsaGrowthValueTask.setTaskNumber("taskNumber");
        hsaGrowthValueTask.setTaskAction(0);
        hsaGrowthValueTask.setGrowthValueValidityType(0);
        hsaGrowthValueTask.setBuyType(0);
        hsaGrowthValueTask.setOperatorTelName("operatorAccountName");

        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("memberGuid");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setMemberIntegral(0);
        hsaOperationMemberInfo.setMemberGrowthValue(0);

        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("afterChangeGradeGuid");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("afterChangeGradeName");
        hsaMemberGradeInfo.setGradeIcon("afterChangeGradeIcon");
        hsaMemberGradeInfo.setBackgroundColor("afterChangeBackgroundColor");
        hsaMemberGradeInfo.setGrowthValue(0);
        hsaMemberGradeInfo.setOperatorName("operatorAccountName");
        hsaMemberGradeInfo.setUpgradeType(0);

        final HsaGrowthValueDetail expectedResult = new HsaGrowthValueDetail();
        expectedResult.setGuid("6180078b-0086-499a-b814-4769b5d436e4");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setGrowthValueTaskId("growthValueTaskId");
        expectedResult.setMemberInfoGuid("memberGuid");
        expectedResult.setTaskName("taskName");
        expectedResult.setRecordDeclaration("成长值失效");
        expectedResult.setTaskNumber("taskNumber");
        expectedResult.setTaskFinishTime(0);
        expectedResult.setGrowthValue(0);
        expectedResult.setGrowthValueType(0);
        expectedResult.setGrowthValidity(0);
        expectedResult.setTaskFinishDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGrowthValidityDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRemainGrowthValue(0);
        expectedResult.setGrowthValueSourceType(0);
        expectedResult.setRecordRemainGrowthValue(0);
        expectedResult.setMemberConsumptionGuid("memberConsumptionGuid");
        expectedResult.setOrderNumber("orderNumber");
        expectedResult.setOperatorAccountName("operatorAccountName");
        expectedResult.setStoreName("storeName");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setChangeType(0);
        expectedResult.setCurrentMemberLevel("currentMemberLevel");
        expectedResult.setTaskAction(0);
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRoleType("roleType");
        expectedResult.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setMemberInfoGradeGuid("afterChangeGradeGuid");
        expectedResult.setUnit(0);
        expectedResult.setUpgradeType(0);
        expectedResult.setNum(0);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("6180078b-0086-499a-b814-4769b5d436e4");

        // Configure HsaMemberGradeChangeDetailService.calculateExtraAmount(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo1 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo1.setGuid("afterChangeGradeGuid");
        hsaMemberGradeInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo1.setVipGrade(0);
        hsaMemberGradeInfo1.setName("afterChangeGradeName");
        hsaMemberGradeInfo1.setGradeIcon("afterChangeGradeIcon");
        hsaMemberGradeInfo1.setBackgroundColor("afterChangeBackgroundColor");
        hsaMemberGradeInfo1.setGrowthValue(0);
        hsaMemberGradeInfo1.setOperatorName("operatorAccountName");
        hsaMemberGradeInfo1.setUpgradeType(0);
        when(mockHsaMemberGradeChangeDetailService.calculateExtraAmount(0, "operSubjectGuid"))
                .thenReturn(hsaMemberGradeInfo1);

        // Run the test
        final HsaGrowthValueDetail result = gradeChangeAssemblerUnderTest.getHsaGrowthValueDetail(hsaGrowthValueTask, 0,
                hsaOperationMemberInfo, hsaMemberGradeInfo);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSetCurrentMemberLevel() {
        // Setup
        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("memberGuid");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setMemberIntegral(0);
        hsaOperationMemberInfo.setMemberGrowthValue(0);

        final HsaMemberGradeInfo hsaMemberGradeInfo = new HsaMemberGradeInfo();
        hsaMemberGradeInfo.setGuid("afterChangeGradeGuid");
        hsaMemberGradeInfo.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo.setVipGrade(0);
        hsaMemberGradeInfo.setName("afterChangeGradeName");
        hsaMemberGradeInfo.setGradeIcon("afterChangeGradeIcon");
        hsaMemberGradeInfo.setBackgroundColor("afterChangeBackgroundColor");
        hsaMemberGradeInfo.setGrowthValue(0);
        hsaMemberGradeInfo.setOperatorName("operatorAccountName");
        hsaMemberGradeInfo.setUpgradeType(0);

        final HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        hsaGrowthValueDetail.setGuid("6180078b-0086-499a-b814-4769b5d436e4");
        hsaGrowthValueDetail.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueDetail.setGrowthValueTaskId("growthValueTaskId");
        hsaGrowthValueDetail.setMemberInfoGuid("memberGuid");
        hsaGrowthValueDetail.setTaskName("taskName");
        hsaGrowthValueDetail.setRecordDeclaration("成长值失效");
        hsaGrowthValueDetail.setTaskNumber("taskNumber");
        hsaGrowthValueDetail.setTaskFinishTime(0);
        hsaGrowthValueDetail.setGrowthValue(0);
        hsaGrowthValueDetail.setGrowthValueType(0);
        hsaGrowthValueDetail.setGrowthValidity(0);
        hsaGrowthValueDetail.setTaskFinishDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail.setGrowthValidityDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail.setRemainGrowthValue(0);
        hsaGrowthValueDetail.setGrowthValueSourceType(0);
        hsaGrowthValueDetail.setRecordRemainGrowthValue(0);
        hsaGrowthValueDetail.setMemberConsumptionGuid("memberConsumptionGuid");
        hsaGrowthValueDetail.setOrderNumber("orderNumber");
        hsaGrowthValueDetail.setOperatorAccountName("operatorAccountName");
        hsaGrowthValueDetail.setStoreName("storeName");
        hsaGrowthValueDetail.setStoreGuid("storeGuid");
        hsaGrowthValueDetail.setChangeType(0);
        hsaGrowthValueDetail.setCurrentMemberLevel("currentMemberLevel");
        hsaGrowthValueDetail.setTaskAction(0);
        hsaGrowthValueDetail.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail.setRoleType("roleType");
        hsaGrowthValueDetail.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthValueDetail.setMemberInfoGradeGuid("afterChangeGradeGuid");
        hsaGrowthValueDetail.setUnit(0);
        hsaGrowthValueDetail.setUpgradeType(0);
        hsaGrowthValueDetail.setNum(0);

        // Configure HsaMemberGradeChangeDetailService.calculateExtraAmount(...).
        final HsaMemberGradeInfo hsaMemberGradeInfo1 = new HsaMemberGradeInfo();
        hsaMemberGradeInfo1.setGuid("afterChangeGradeGuid");
        hsaMemberGradeInfo1.setOperSubjectGuid("operSubjectGuid");
        hsaMemberGradeInfo1.setVipGrade(0);
        hsaMemberGradeInfo1.setName("afterChangeGradeName");
        hsaMemberGradeInfo1.setGradeIcon("afterChangeGradeIcon");
        hsaMemberGradeInfo1.setBackgroundColor("afterChangeBackgroundColor");
        hsaMemberGradeInfo1.setGrowthValue(0);
        hsaMemberGradeInfo1.setOperatorName("operatorAccountName");
        hsaMemberGradeInfo1.setUpgradeType(0);
        when(mockHsaMemberGradeChangeDetailService.calculateExtraAmount(0, "operSubjectGuid"))
                .thenReturn(hsaMemberGradeInfo1);

        // Run the test
        gradeChangeAssemblerUnderTest.setCurrentMemberLevel(hsaOperationMemberInfo, hsaMemberGradeInfo,
                hsaGrowthValueDetail);

        // Verify the results
    }

    @Test
    public void testSaveSatisfyTouchDetail() {
        // Setup
        final MemberGrowthValueEvent event = new MemberGrowthValueEvent();
        event.setMemberInfoGuidList(Arrays.asList("value"));
        event.setOperSubjectGuid("operSubjectGuid");
        event.setSource(0);
        event.setMemberConsumptionGuid("memberConsumptionGuid");
        event.setOrderNumber("orderNumber");
        event.setStoreGuid("storeGuid");
        event.setStoreName("storeName");
        event.setOperatorAccountName("operatorAccountName");

        final HsaGrowthValueTask hsaGrowthValueTask = new HsaGrowthValueTask();
        hsaGrowthValueTask.setId(0L);
        hsaGrowthValueTask.setGuid("growthValueTaskGuid");
        hsaGrowthValueTask.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueTask.setTaskName("taskName");
        hsaGrowthValueTask.setTaskNumber("taskNumber");
        hsaGrowthValueTask.setTaskAction(0);
        hsaGrowthValueTask.setGrowthValueValidityType(0);
        hsaGrowthValueTask.setBuyType(0);
        hsaGrowthValueTask.setOperatorTelName("operatorAccountName");

        final HsaGrowthCommodityTouchDetail hsaGrowthCommodityTouchDetail = new HsaGrowthCommodityTouchDetail();
        hsaGrowthCommodityTouchDetail.setGuid("32f3b0cb-1c47-4281-b458-aedbc6980d36");
        hsaGrowthCommodityTouchDetail.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthCommodityTouchDetail.setGrowthValueTaskGuid("growthValueTaskGuid");
        hsaGrowthCommodityTouchDetail.setMemberInfoGuid("memberInfoGuid");
        hsaGrowthCommodityTouchDetail.setBuyType(0);
        hsaGrowthCommodityTouchDetail.setMemberConsumptionGuid("memberConsumptionGuid");
        hsaGrowthCommodityTouchDetail.setOrderNumber("orderNumber");
        hsaGrowthCommodityTouchDetail.setOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaGrowthCommodityTouchDetail.setIsDelete(0);
        final List<HsaGrowthCommodityTouchDetail> saveGrowthCommodityTouchDetails = Arrays.asList(
                hsaGrowthCommodityTouchDetail);
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("32f3b0cb-1c47-4281-b458-aedbc6980d36");

        // Run the test
        gradeChangeAssemblerUnderTest.saveSatisfyTouchDetail(event, hsaGrowthValueTask,
                saveGrowthCommodityTouchDetails);

        // Verify the results
    }

    @Test
    public void testGetHsaGrowthCommodityQuantityTouchDetailList() {
        // Setup
        final HsaGrowthValueTask hsaGrowthValueTask = new HsaGrowthValueTask();
        hsaGrowthValueTask.setId(0L);
        hsaGrowthValueTask.setGuid("growthValueTaskGuid");
        hsaGrowthValueTask.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueTask.setTaskName("taskName");
        hsaGrowthValueTask.setTaskNumber("taskNumber");
        hsaGrowthValueTask.setTaskAction(0);
        hsaGrowthValueTask.setGrowthValueValidityType(0);
        hsaGrowthValueTask.setBuyType(0);
        hsaGrowthValueTask.setOperatorTelName("operatorAccountName");

        final MemberGrowthValueEvent event = new MemberGrowthValueEvent();
        event.setMemberInfoGuidList(Arrays.asList("value"));
        event.setOperSubjectGuid("operSubjectGuid");
        event.setSource(0);
        event.setMemberConsumptionGuid("memberConsumptionGuid");
        event.setOrderNumber("orderNumber");
        event.setStoreGuid("storeGuid");
        event.setStoreName("storeName");
        event.setOperatorAccountName("operatorAccountName");

        final HsaGrowthCommodityQuantityTouchDetail hsaGrowthCommodityQuantityTouchDetail = new HsaGrowthCommodityQuantityTouchDetail();
        hsaGrowthCommodityQuantityTouchDetail.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthCommodityQuantityTouchDetail.setGrowthValueTaskGuid("growthValueTaskGuid");
        hsaGrowthCommodityQuantityTouchDetail.setMemberInfoGuid("memberInfoGuid");
        hsaGrowthCommodityQuantityTouchDetail.setMemberConsumptionGuid("memberConsumptionGuid");
        hsaGrowthCommodityQuantityTouchDetail.setCommodityId("commodityId");
        hsaGrowthCommodityQuantityTouchDetail.setCommodityName("commodityName");
        hsaGrowthCommodityQuantityTouchDetail.setCommodityNum(0);
        hsaGrowthCommodityQuantityTouchDetail.setCommodityCode("commodityCode");
        hsaGrowthCommodityQuantityTouchDetail.setStrategyId("strategyId");
        final List<HsaGrowthCommodityQuantityTouchDetail> touchDetails = Arrays.asList(
                hsaGrowthCommodityQuantityTouchDetail);
        final ResOrderCommodity in = new ResOrderCommodity();
        in.setProductId(0);
        in.setProductCode("commodityCode");
        in.setProduct_name("commodityName");
        in.setQuantity(0);
        in.setStrategyId(0);

        // Run the test
        gradeChangeAssemblerUnderTest.getHsaGrowthCommodityQuantityTouchDetailList(hsaGrowthValueTask, event,
                touchDetails, in);

        // Verify the results
    }

    @Test
    public void testGetHsaGrowthValueDetail4() {
        // Setup
        final MemberGrowthValueEvent event = new MemberGrowthValueEvent();
        event.setMemberInfoGuidList(Arrays.asList("value"));
        event.setOperSubjectGuid("operSubjectGuid");
        event.setSource(0);
        event.setMemberConsumptionGuid("memberConsumptionGuid");
        event.setOrderNumber("orderNumber");
        event.setStoreGuid("storeGuid");
        event.setStoreName("storeName");
        event.setOperatorAccountName("operatorAccountName");

        final HsaGrowthValueTask hsaGrowthValueTask = new HsaGrowthValueTask();
        hsaGrowthValueTask.setId(0L);
        hsaGrowthValueTask.setGuid("growthValueTaskGuid");
        hsaGrowthValueTask.setOperSubjectGuid("operSubjectGuid");
        hsaGrowthValueTask.setTaskName("taskName");
        hsaGrowthValueTask.setTaskNumber("taskNumber");
        hsaGrowthValueTask.setTaskAction(0);
        hsaGrowthValueTask.setGrowthValueValidityType(0);
        hsaGrowthValueTask.setBuyType(0);
        hsaGrowthValueTask.setOperatorTelName("operatorAccountName");

        final HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        hsaOperationMemberInfo.setGuid("memberGuid");
        hsaOperationMemberInfo.setOperSubjectGuid("operSubjectGuid");
        hsaOperationMemberInfo.setMemberGradeInfoGuid("memberGradeInfoGuid");
        hsaOperationMemberInfo.setMemberIntegral(0);
        hsaOperationMemberInfo.setMemberGrowthValue(0);

        final HsaGrowthValueDetail expectedResult = new HsaGrowthValueDetail();
        expectedResult.setGuid("6180078b-0086-499a-b814-4769b5d436e4");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setGrowthValueTaskId("growthValueTaskId");
        expectedResult.setMemberInfoGuid("memberGuid");
        expectedResult.setTaskName("taskName");
        expectedResult.setRecordDeclaration("成长值失效");
        expectedResult.setTaskNumber("taskNumber");
        expectedResult.setTaskFinishTime(0);
        expectedResult.setGrowthValue(0);
        expectedResult.setGrowthValueType(0);
        expectedResult.setGrowthValidity(0);
        expectedResult.setTaskFinishDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGrowthValidityDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRemainGrowthValue(0);
        expectedResult.setGrowthValueSourceType(0);
        expectedResult.setRecordRemainGrowthValue(0);
        expectedResult.setMemberConsumptionGuid("memberConsumptionGuid");
        expectedResult.setOrderNumber("orderNumber");
        expectedResult.setOperatorAccountName("operatorAccountName");
        expectedResult.setStoreName("storeName");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setChangeType(0);
        expectedResult.setCurrentMemberLevel("currentMemberLevel");
        expectedResult.setTaskAction(0);
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRoleType("roleType");
        expectedResult.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setMemberInfoGradeGuid("afterChangeGradeGuid");
        expectedResult.setUnit(0);
        expectedResult.setUpgradeType(0);
        expectedResult.setNum(0);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("6180078b-0086-499a-b814-4769b5d436e4");

        // Run the test
        final HsaGrowthValueDetail result = gradeChangeAssemblerUnderTest.getHsaGrowthValueDetail(event,
                hsaGrowthValueTask, hsaOperationMemberInfo, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
