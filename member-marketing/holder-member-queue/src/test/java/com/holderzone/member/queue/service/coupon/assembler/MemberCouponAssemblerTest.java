package com.holderzone.member.queue.service.coupon.assembler;

import com.holderzone.member.common.dto.card.MemberPhoneDTO;
import com.holderzone.member.common.dto.coupon.ResponseCouponDTO;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponPackageLink;
import com.holderzone.member.common.qo.coupon.CouponPackageSpecificDTO;
import com.holderzone.member.common.vo.coupon.EditCouponPackageActivityVO;
import com.holderzone.member.common.vo.coupon.ResponseCouponCommodityVO;
import com.holderzone.member.common.vo.coupon.ResponseCouponStoreVO;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;

public class MemberCouponAssemblerTest {

    private MemberCouponAssembler memberCouponAssemblerUnderTest;

    @Before
    public void setUp() {
        memberCouponAssemblerUnderTest = null;
    }

    @Test
    public void testNewMemberCouponPackageLink() {
        // Setup
        final HsaMemberCouponPackageLink hsaMemberCouponPackageLink = new HsaMemberCouponPackageLink();
        hsaMemberCouponPackageLink.setGuid("6aa87d46-c48f-45ca-a1b7-dcbf68f4dbf6");
        hsaMemberCouponPackageLink.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponPackageLink.setActivityName("activityName");
        hsaMemberCouponPackageLink.setActivityGuid("activityGuid");
        hsaMemberCouponPackageLink.setActivityCode("activityCode");
        hsaMemberCouponPackageLink.setCouponPackageJson("couponPackageJson");
        hsaMemberCouponPackageLink.setUserName("userName");
        hsaMemberCouponPackageLink.setMemberGuid("guid");
        hsaMemberCouponPackageLink.setMemberPhone("memberPhone");
        hsaMemberCouponPackageLink.setSource(0);
        hsaMemberCouponPackageLink.setReachTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberCouponPackageLink.setState(0);
        hsaMemberCouponPackageLink.setReason("reason");

        final EditCouponPackageActivityVO activityVO = new EditCouponPackageActivityVO();
        activityVO.setGuid("activityGuid");
        activityVO.setOperSubjectGuid("operSubjectGuid");
        activityVO.setActivityCode("activityCode");
        activityVO.setActivityName("activityName");
        activityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO.setTime("time");
        activityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO));
        final ResponseCouponDTO responseCouponDTO = new ResponseCouponDTO();
        responseCouponDTO.setCouponName("couponName");
        responseCouponDTO.setCouponCode("couponCode");
        responseCouponDTO.setCouponType(0);
        responseCouponDTO.setThresholdType(0);
        responseCouponDTO.setThresholdAmount(new BigDecimal("0.00"));
        responseCouponDTO.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO.setRemark("remark");
        responseCouponDTO.setApplyTimeLimitedJson("applyTimeLimitedJson");
        responseCouponDTO.setApplyBusiness(0);
        responseCouponDTO.setApplyBusinessList(Arrays.asList("value"));
        responseCouponDTO.setEffectiveType(0);
        responseCouponDTO.setAfterValue(0);
        responseCouponDTO.setAfterUnit(0);
        responseCouponDTO.setEffectiveValue(0);
        responseCouponDTO.setEffectiveUnit(0);
        responseCouponDTO.setCouponEffectiveStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        responseCouponDTO.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        responseCouponDTO.setApplyDateLimited(0);
        responseCouponDTO.setApplyTimeLimitedType(0);
        responseCouponDTO.setApplyTerminalList(Arrays.asList("value"));
        responseCouponDTO.setApplyLabelGuidList(Arrays.asList("value"));
        responseCouponDTO.setApplicableAllStore(0);
        responseCouponDTO.setApplyCommodity(0);
        final ResponseCouponStoreVO responseCouponStoreVO = new ResponseCouponStoreVO();
        responseCouponDTO.setRequestCouponStoreQOList(Arrays.asList(responseCouponStoreVO));
        final ResponseCouponCommodityVO responseCouponCommodityVO = new ResponseCouponCommodityVO();
        responseCouponDTO.setRequestCouponCommodityQOList(Arrays.asList(responseCouponCommodityVO));
        responseCouponDTO.setNum(0);
        activityVO.setCouponGuidList(Arrays.asList(responseCouponDTO));

        final MemberPhoneDTO memberPhoneDTO = new MemberPhoneDTO();
        memberPhoneDTO.setPhoneCountryCode("phoneCountryCode");
        memberPhoneDTO.setEnterpriseGuid("enterpriseGuid");
        memberPhoneDTO.setPhoneNum("memberPhone");
        memberPhoneDTO.setGuid("guid");
        memberPhoneDTO.setUserName("userName");

        final HsaMemberCouponPackageLink expectedResult = new HsaMemberCouponPackageLink();
        expectedResult.setGuid("6aa87d46-c48f-45ca-a1b7-dcbf68f4dbf6");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setActivityName("activityName");
        expectedResult.setActivityGuid("activityGuid");
        expectedResult.setActivityCode("activityCode");
        expectedResult.setCouponPackageJson("couponPackageJson");
        expectedResult.setUserName("userName");
        expectedResult.setMemberGuid("guid");
        expectedResult.setMemberPhone("memberPhone");
        expectedResult.setSource(0);
        expectedResult.setReachTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setState(0);
        expectedResult.setReason("reason");

        // Run the test
        final HsaMemberCouponPackageLink result = MemberCouponAssembler.newMemberCouponPackageLink(
                hsaMemberCouponPackageLink, activityVO, memberPhoneDTO, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                "reason");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testNewMemberCouponLink() {
        // Setup
        final EditCouponPackageActivityVO activityVO = new EditCouponPackageActivityVO();
        activityVO.setGuid("activityGuid");
        activityVO.setOperSubjectGuid("operSubjectGuid");
        activityVO.setActivityCode("activityCode");
        activityVO.setActivityName("activityName");
        activityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO.setTime("time");
        activityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO));
        final ResponseCouponDTO responseCouponDTO = new ResponseCouponDTO();
        responseCouponDTO.setCouponName("couponName");
        responseCouponDTO.setCouponCode("couponCode");
        responseCouponDTO.setCouponType(0);
        responseCouponDTO.setThresholdType(0);
        responseCouponDTO.setThresholdAmount(new BigDecimal("0.00"));
        responseCouponDTO.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO.setRemark("remark");
        responseCouponDTO.setApplyTimeLimitedJson("applyTimeLimitedJson");
        responseCouponDTO.setApplyBusiness(0);
        responseCouponDTO.setApplyBusinessList(Arrays.asList("value"));
        responseCouponDTO.setEffectiveType(0);
        responseCouponDTO.setAfterValue(0);
        responseCouponDTO.setAfterUnit(0);
        responseCouponDTO.setEffectiveValue(0);
        responseCouponDTO.setEffectiveUnit(0);
        responseCouponDTO.setCouponEffectiveStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        responseCouponDTO.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        responseCouponDTO.setApplyDateLimited(0);
        responseCouponDTO.setApplyTimeLimitedType(0);
        responseCouponDTO.setApplyTerminalList(Arrays.asList("value"));
        responseCouponDTO.setApplyLabelGuidList(Arrays.asList("value"));
        responseCouponDTO.setApplicableAllStore(0);
        responseCouponDTO.setApplyCommodity(0);
        final ResponseCouponStoreVO responseCouponStoreVO = new ResponseCouponStoreVO();
        responseCouponDTO.setRequestCouponStoreQOList(Arrays.asList(responseCouponStoreVO));
        final ResponseCouponCommodityVO responseCouponCommodityVO = new ResponseCouponCommodityVO();
        responseCouponDTO.setRequestCouponCommodityQOList(Arrays.asList(responseCouponCommodityVO));
        responseCouponDTO.setNum(0);
        activityVO.setCouponGuidList(Arrays.asList(responseCouponDTO));

        final MemberPhoneDTO memberPhoneDTO = new MemberPhoneDTO();
        memberPhoneDTO.setPhoneCountryCode("phoneCountryCode");
        memberPhoneDTO.setEnterpriseGuid("enterpriseGuid");
        memberPhoneDTO.setPhoneNum("memberPhone");
        memberPhoneDTO.setGuid("guid");
        memberPhoneDTO.setUserName("userName");

        final Map<String, String> codeMap = new HashMap<>();
        final HsaMemberCouponLink hsaMemberCouponLink = new HsaMemberCouponLink();
        hsaMemberCouponLink.setGuid("6aa87d46-c48f-45ca-a1b7-dcbf68f4dbf6");
        hsaMemberCouponLink.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink.setCouponName("couponName");
        hsaMemberCouponLink.setUserName("userName");
        hsaMemberCouponLink.setMemberGuid("guid");
        hsaMemberCouponLink.setMemberPhone("memberPhone");
        hsaMemberCouponLink.setSource(0);
        hsaMemberCouponLink.setCouponPackageType(0);
        hsaMemberCouponLink.setCouponPackageCode("activityCode");
        hsaMemberCouponLink.setCouponPackageName("activityName");
        hsaMemberCouponLink.setState(0);
        hsaMemberCouponLink.setReachTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberCouponLink.setCode("code");
        hsaMemberCouponLink.setCouponCode("couponCode");
        hsaMemberCouponLink.setCouponType(0);
        hsaMemberCouponLink.setThresholdType(0);
        hsaMemberCouponLink.setThresholdAmount(new BigDecimal("0.00"));
        hsaMemberCouponLink.setDiscountAmount(new BigDecimal("0.00"));
        hsaMemberCouponLink.setRemark("remark");
        hsaMemberCouponLink.setCouponEffectiveStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberCouponLink.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMemberCouponLink.setApplyDateLimited(0);
        hsaMemberCouponLink.setApplyTimeLimitedType(0);
        hsaMemberCouponLink.setApplyTimeLimitedJson("applyTimeLimitedJson");
        hsaMemberCouponLink.setApplyBusiness(0);
        hsaMemberCouponLink.setApplyBusinessJson("applyBusinessJson");
        hsaMemberCouponLink.setApplyTerminalJson("applyTerminalJson");
        hsaMemberCouponLink.setApplyLabelGuidJson("applyLabelGuidJson");
        hsaMemberCouponLink.setApplicableAllStore(0);
        hsaMemberCouponLink.setApplicableAllStoreJson("applicableAllStoreJson");
        hsaMemberCouponLink.setApplyCommodity(0);
        hsaMemberCouponLink.setApplyCommodityJson("applyCommodityJson");
        final List<HsaMemberCouponLink> expectedResult = Arrays.asList(hsaMemberCouponLink);

        // Run the test
        final List<HsaMemberCouponLink> result = memberCouponAssemblerUnderTest.newMemberCouponLink(activityVO,
                memberPhoneDTO, LocalDateTime.of(2020, 1, 1, 0, 0, 0), codeMap, new HashSet<>(Arrays.asList("value")));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCreateCouponCode() {
        // Setup
        final Map<String, String> codeMap = new HashMap<>();

        // Run the test
        final String result = memberCouponAssemblerUnderTest.createCouponCode(codeMap,
                "21d3a4dd-cb5e-4af9-88d8-668d23b0495e");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testForGetCouponPackageGrantList() {
        // Setup
        final EditCouponPackageActivityVO editCouponPackageActivityVO = new EditCouponPackageActivityVO();
        editCouponPackageActivityVO.setGuid("activityGuid");
        editCouponPackageActivityVO.setOperSubjectGuid("operSubjectGuid");
        editCouponPackageActivityVO.setActivityCode("activityCode");
        editCouponPackageActivityVO.setActivityName("activityName");
        editCouponPackageActivityVO.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO.setTime("time");
        editCouponPackageActivityVO.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO));
        final ResponseCouponDTO responseCouponDTO = new ResponseCouponDTO();
        responseCouponDTO.setCouponName("couponName");
        responseCouponDTO.setCouponCode("couponCode");
        responseCouponDTO.setCouponType(0);
        responseCouponDTO.setThresholdType(0);
        responseCouponDTO.setThresholdAmount(new BigDecimal("0.00"));
        responseCouponDTO.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO.setRemark("remark");
        responseCouponDTO.setApplyTimeLimitedJson("applyTimeLimitedJson");
        responseCouponDTO.setApplyBusiness(0);
        responseCouponDTO.setApplyBusinessList(Arrays.asList("value"));
        responseCouponDTO.setEffectiveType(0);
        responseCouponDTO.setAfterValue(0);
        responseCouponDTO.setAfterUnit(0);
        responseCouponDTO.setEffectiveValue(0);
        responseCouponDTO.setEffectiveUnit(0);
        responseCouponDTO.setCouponEffectiveStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        responseCouponDTO.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        responseCouponDTO.setApplyDateLimited(0);
        responseCouponDTO.setApplyTimeLimitedType(0);
        responseCouponDTO.setApplyTerminalList(Arrays.asList("value"));
        responseCouponDTO.setApplyLabelGuidList(Arrays.asList("value"));
        responseCouponDTO.setApplicableAllStore(0);
        responseCouponDTO.setApplyCommodity(0);
        final ResponseCouponStoreVO responseCouponStoreVO = new ResponseCouponStoreVO();
        responseCouponDTO.setRequestCouponStoreQOList(Arrays.asList(responseCouponStoreVO));
        final ResponseCouponCommodityVO responseCouponCommodityVO = new ResponseCouponCommodityVO();
        responseCouponDTO.setRequestCouponCommodityQOList(Arrays.asList(responseCouponCommodityVO));
        responseCouponDTO.setNum(0);
        editCouponPackageActivityVO.setCouponGuidList(Arrays.asList(responseCouponDTO));
        final List<EditCouponPackageActivityVO> couponPackageList = Arrays.asList(editCouponPackageActivityVO);
        final EditCouponPackageActivityVO editCouponPackageActivityVO1 = new EditCouponPackageActivityVO();
        editCouponPackageActivityVO1.setGuid("activityGuid");
        editCouponPackageActivityVO1.setOperSubjectGuid("operSubjectGuid");
        editCouponPackageActivityVO1.setActivityCode("activityCode");
        editCouponPackageActivityVO1.setActivityName("activityName");
        editCouponPackageActivityVO1.setGiveFrequencyType(0);
        final CouponPackageSpecificDTO couponPackageSpecificDTO1 = new CouponPackageSpecificDTO();
        couponPackageSpecificDTO1.setTimeList(Arrays.asList("value"));
        couponPackageSpecificDTO1.setTime("time");
        editCouponPackageActivityVO1.setCouponPackageSpecificDTO(Arrays.asList(couponPackageSpecificDTO1));
        final ResponseCouponDTO responseCouponDTO1 = new ResponseCouponDTO();
        responseCouponDTO1.setCouponName("couponName");
        responseCouponDTO1.setCouponCode("couponCode");
        responseCouponDTO1.setCouponType(0);
        responseCouponDTO1.setThresholdType(0);
        responseCouponDTO1.setThresholdAmount(new BigDecimal("0.00"));
        responseCouponDTO1.setDiscountAmount(new BigDecimal("0.00"));
        responseCouponDTO1.setRemark("remark");
        responseCouponDTO1.setApplyTimeLimitedJson("applyTimeLimitedJson");
        responseCouponDTO1.setApplyBusiness(0);
        responseCouponDTO1.setApplyBusinessList(Arrays.asList("value"));
        responseCouponDTO1.setEffectiveType(0);
        responseCouponDTO1.setAfterValue(0);
        responseCouponDTO1.setAfterUnit(0);
        responseCouponDTO1.setEffectiveValue(0);
        responseCouponDTO1.setEffectiveUnit(0);
        responseCouponDTO1.setCouponEffectiveStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        responseCouponDTO1.setCouponEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        responseCouponDTO1.setApplyDateLimited(0);
        responseCouponDTO1.setApplyTimeLimitedType(0);
        responseCouponDTO1.setApplyTerminalList(Arrays.asList("value"));
        responseCouponDTO1.setApplyLabelGuidList(Arrays.asList("value"));
        responseCouponDTO1.setApplicableAllStore(0);
        responseCouponDTO1.setApplyCommodity(0);
        final ResponseCouponStoreVO responseCouponStoreVO1 = new ResponseCouponStoreVO();
        responseCouponDTO1.setRequestCouponStoreQOList(Arrays.asList(responseCouponStoreVO1));
        final ResponseCouponCommodityVO responseCouponCommodityVO1 = new ResponseCouponCommodityVO();
        responseCouponDTO1.setRequestCouponCommodityQOList(Arrays.asList(responseCouponCommodityVO1));
        responseCouponDTO1.setNum(0);
        editCouponPackageActivityVO1.setCouponGuidList(Arrays.asList(responseCouponDTO1));
        final Set<EditCouponPackageActivityVO> couponPackageGrantList = new HashSet<>(
                Arrays.asList(editCouponPackageActivityVO1));

        // Run the test
        MemberCouponAssembler.forGetCouponPackageGrantList(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                "presentDate", couponPackageList, couponPackageGrantList);

        // Verify the results
    }
}
