package com.holderzone.member.queue.service.order.impl;

import com.holderzone.member.common.dto.mall.OrderReceiverAddressDTO;
import com.holderzone.member.common.dto.order.MallBaseOrderDTO;
import com.holderzone.member.common.dto.order.MallBaseOrderDetailDTO;
import com.holderzone.member.common.external.ExternalSupport;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CrmOrderServiceImplTest {

    @Mock
    private ExternalSupport mockExternalSupport;

    private CrmOrderServiceImpl crmOrderServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        crmOrderServiceImplUnderTest = new CrmOrderServiceImpl(mockExternalSupport);
    }

    @Test
    public void testPushCrmOrder() {
        // Setup
        final MallBaseOrderDTO order = new MallBaseOrderDTO();
        order.setStoreGuid("store_id");
        order.setMemberInfoGuid("member_number");
        order.setMemberPhone("connection");
        order.setMemberName("member_name");
        order.setOrderNumber("name");
        order.setOrderCondition(0);
        order.setPayCondition(0);
        order.setFreightAmount(new BigDecimal("0.00"));
        order.setOrderPaymentAmount(new BigDecimal("0.00"));
        order.setOrderDiscountAmount(new BigDecimal("0.00"));
        order.setOrderPaidAmount(new BigDecimal("0.00"));
        order.setActualRefundAmount(new BigDecimal("0.00"));
        order.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final MallBaseOrderDetailDTO mallBaseOrderDetailDTO = new MallBaseOrderDetailDTO();
        mallBaseOrderDetailDTO.setGuid("31232633-12be-4093-a24e-3cc30cf9fbfe");
        mallBaseOrderDetailDTO.setOperSubjectGuid("operSubjectGuid");
        mallBaseOrderDetailDTO.setProductId(0L);
        mallBaseOrderDetailDTO.setProductUnitPrice("productUnitPrice");
        mallBaseOrderDetailDTO.setProductNum(0);
        final List<MallBaseOrderDetailDTO> details = Arrays.asList(mallBaseOrderDetailDTO);
        final OrderReceiverAddressDTO orderReceiverAd = new OrderReceiverAddressDTO();
        orderReceiverAd.setReceiverName("consignee_name");
        orderReceiverAd.setReceiverPhone("consignee_phone");
        orderReceiverAd.setReceiverAddress("receiverAddress");
        orderReceiverAd.setProvince("province");
        orderReceiverAd.setCity("city");
        orderReceiverAd.setArea("area");
        orderReceiverAd.setHouseNumber("houseNumber");

        when(mockExternalSupport.storeServer(0)).thenReturn(null);

        // Run the test
        crmOrderServiceImplUnderTest.pushCrmOrder(order, details, orderReceiverAd, false);

        // Verify the results
    }

    @Test
    public void testCreateCrmOrder() {
        // Setup
        final MallBaseOrderDTO order = new MallBaseOrderDTO();
        order.setStoreGuid("store_id");
        order.setMemberInfoGuid("member_number");
        order.setMemberPhone("connection");
        order.setMemberName("member_name");
        order.setOrderNumber("name");
        order.setOrderCondition(0);
        order.setPayCondition(0);
        order.setFreightAmount(new BigDecimal("0.00"));
        order.setOrderPaymentAmount(new BigDecimal("0.00"));
        order.setOrderDiscountAmount(new BigDecimal("0.00"));
        order.setOrderPaidAmount(new BigDecimal("0.00"));
        order.setActualRefundAmount(new BigDecimal("0.00"));
        order.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final MallBaseOrderDetailDTO mallBaseOrderDetailDTO = new MallBaseOrderDetailDTO();
        mallBaseOrderDetailDTO.setGuid("31232633-12be-4093-a24e-3cc30cf9fbfe");
        mallBaseOrderDetailDTO.setOperSubjectGuid("operSubjectGuid");
        mallBaseOrderDetailDTO.setProductId(0L);
        mallBaseOrderDetailDTO.setProductUnitPrice("productUnitPrice");
        mallBaseOrderDetailDTO.setProductNum(0);
        final List<MallBaseOrderDetailDTO> details = Arrays.asList(mallBaseOrderDetailDTO);
        final OrderReceiverAddressDTO orderReceiverAd = new OrderReceiverAddressDTO();
        orderReceiverAd.setReceiverName("consignee_name");
        orderReceiverAd.setReceiverPhone("consignee_phone");
        orderReceiverAd.setReceiverAddress("receiverAddress");
        orderReceiverAd.setProvince("province");
        orderReceiverAd.setCity("city");
        orderReceiverAd.setArea("area");
        orderReceiverAd.setHouseNumber("houseNumber");

        when(mockExternalSupport.storeServer(0)).thenReturn(null);

        // Run the test
        crmOrderServiceImplUnderTest.createCrmOrder(order, details, orderReceiverAd);

        // Verify the results
    }

    @Test
    public void testPushCrmOrderStock() {
        // Setup
        final MallBaseOrderDTO order = new MallBaseOrderDTO();
        order.setStoreGuid("store_id");
        order.setMemberInfoGuid("member_number");
        order.setMemberPhone("connection");
        order.setMemberName("member_name");
        order.setOrderNumber("name");
        order.setOrderCondition(0);
        order.setPayCondition(0);
        order.setFreightAmount(new BigDecimal("0.00"));
        order.setOrderPaymentAmount(new BigDecimal("0.00"));
        order.setOrderDiscountAmount(new BigDecimal("0.00"));
        order.setOrderPaidAmount(new BigDecimal("0.00"));
        order.setActualRefundAmount(new BigDecimal("0.00"));
        order.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final MallBaseOrderDetailDTO mallBaseOrderDetailDTO = new MallBaseOrderDetailDTO();
        mallBaseOrderDetailDTO.setGuid("31232633-12be-4093-a24e-3cc30cf9fbfe");
        mallBaseOrderDetailDTO.setOperSubjectGuid("operSubjectGuid");
        mallBaseOrderDetailDTO.setProductId(0L);
        mallBaseOrderDetailDTO.setProductUnitPrice("productUnitPrice");
        mallBaseOrderDetailDTO.setProductNum(0);
        final List<MallBaseOrderDetailDTO> details = Arrays.asList(mallBaseOrderDetailDTO);
        when(mockExternalSupport.storeServer(0)).thenReturn(null);

        // Run the test
        crmOrderServiceImplUnderTest.pushCrmOrderStock(order, details);

        // Verify the results
    }

    @Test
    public void testPushCrmOrderStatus() {
        // Setup
        final MallBaseOrderDTO order = new MallBaseOrderDTO();
        order.setStoreGuid("store_id");
        order.setMemberInfoGuid("member_number");
        order.setMemberPhone("connection");
        order.setMemberName("member_name");
        order.setOrderNumber("name");
        order.setOrderCondition(0);
        order.setPayCondition(0);
        order.setFreightAmount(new BigDecimal("0.00"));
        order.setOrderPaymentAmount(new BigDecimal("0.00"));
        order.setOrderDiscountAmount(new BigDecimal("0.00"));
        order.setOrderPaidAmount(new BigDecimal("0.00"));
        order.setActualRefundAmount(new BigDecimal("0.00"));
        order.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockExternalSupport.storeServer(0)).thenReturn(null);

        // Run the test
        crmOrderServiceImplUnderTest.pushCrmOrderStatus(order);

        // Verify the results
    }
}
