package com.holderzone.member.queue.service.coupon.impl;

import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class HsaMemberCouponLinkServiceImplTest {

    private HsaMemberCouponLinkServiceImpl hsaMemberCouponLinkServiceImplUnderTest;

    @Before
    public void setUp() {
        hsaMemberCouponLinkServiceImplUnderTest = new HsaMemberCouponLinkServiceImpl();
    }

    @Test
    public void testQueryByGuid() {
        // Setup
        final HsaMemberCouponLink expectedResult = new HsaMemberCouponLink();
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setCouponName("couponName");
        expectedResult.setUserName("userName");
        expectedResult.setMemberGuid("memberGuid");

        // Run the test
        final HsaMemberCouponLink result = hsaMemberCouponLinkServiceImplUnderTest.queryByGuid(
                "cd1a438c-04f2-4377-8bc9-7c3ad29d3ec3");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryByGuids() {
        // Setup
        final HsaMemberCouponLink hsaMemberCouponLink = new HsaMemberCouponLink();
        hsaMemberCouponLink.setOperSubjectGuid("operSubjectGuid");
        hsaMemberCouponLink.setEnterpriseGuid("enterpriseGuid");
        hsaMemberCouponLink.setCouponName("couponName");
        hsaMemberCouponLink.setUserName("userName");
        hsaMemberCouponLink.setMemberGuid("memberGuid");
        final List<HsaMemberCouponLink> expectedResult = Arrays.asList(hsaMemberCouponLink);

        // Run the test
        final List<HsaMemberCouponLink> result = hsaMemberCouponLinkServiceImplUnderTest.queryByGuids(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateByGuid() {
        // Setup
        final HsaMemberCouponLink t = new HsaMemberCouponLink();
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setCouponName("couponName");
        t.setUserName("userName");
        t.setMemberGuid("memberGuid");

        // Run the test
        final boolean result = hsaMemberCouponLinkServiceImplUnderTest.updateByGuid(t);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemoveByGuid() {
        // Setup
        // Run the test
        final boolean result = hsaMemberCouponLinkServiceImplUnderTest.removeByGuid(
                "ec28ac7e-85c5-419d-bceb-07f78bc25c3b");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemoveByGuids() {
        // Setup
        // Run the test
        final boolean result = hsaMemberCouponLinkServiceImplUnderTest.removeByGuids(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isFalse();
    }
}
