package com.holderzone.member.queue.service.member.impl;

import com.holderzone.member.queue.entity.HsaMemberFundingDetail;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class HsaMemberFundingDetailServiceImplTest {

    private HsaMemberFundingDetailServiceImpl hsaMemberFundingDetailServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        hsaMemberFundingDetailServiceImplUnderTest = new HsaMemberFundingDetailServiceImpl();
    }

    @Test
    public void testQueryByGuid() {
        // Setup
        final HsaMemberFundingDetail expectedResult = new HsaMemberFundingDetail();
        expectedResult.setGuid("20121f05-b775-425b-bd61-a11a826be96c");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setMemberInfoGuid("memberInfoGuid");
        expectedResult.setCardNum("cardNum");
        expectedResult.setCardName("cardName");

        // Run the test
        final HsaMemberFundingDetail result = hsaMemberFundingDetailServiceImplUnderTest.queryByGuid(
                "6a5cf6e7-1aa7-40f9-b757-e99f89143a38");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryByGuids() {
        // Setup
        final HsaMemberFundingDetail hsaMemberFundingDetail = new HsaMemberFundingDetail();
        hsaMemberFundingDetail.setGuid("20121f05-b775-425b-bd61-a11a826be96c");
        hsaMemberFundingDetail.setOperSubjectGuid("operSubjectGuid");
        hsaMemberFundingDetail.setMemberInfoGuid("memberInfoGuid");
        hsaMemberFundingDetail.setCardNum("cardNum");
        hsaMemberFundingDetail.setCardName("cardName");
        final List<HsaMemberFundingDetail> expectedResult = Arrays.asList(hsaMemberFundingDetail);

        // Run the test
        final List<HsaMemberFundingDetail> result = hsaMemberFundingDetailServiceImplUnderTest.queryByGuids(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateByGuid() {
        // Setup
        final HsaMemberFundingDetail t = new HsaMemberFundingDetail();
        t.setGuid("20121f05-b775-425b-bd61-a11a826be96c");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setCardNum("cardNum");
        t.setCardName("cardName");

        // Run the test
        final boolean result = hsaMemberFundingDetailServiceImplUnderTest.updateByGuid(t);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemoveByGuid() {
        // Setup
        // Run the test
        final boolean result = hsaMemberFundingDetailServiceImplUnderTest.removeByGuid(
                "389dfc45-7a08-4746-8347-5d3ef634c598");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemoveByGuids() {
        // Setup
        // Run the test
        final boolean result = hsaMemberFundingDetailServiceImplUnderTest.removeByGuids(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isFalse();
    }
}
