package com.holderzone.member.base.aop;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 获取请求的入参和出参
 *
 * <AUTHOR>
 */

@Component
@Aspect
@Slf4j
public class RequestAspect {

    @Pointcut("@within(org.springframework.stereotype.Controller) || @within(org.springframework.web.bind.annotation.RestController)")
    public void pointcut() {
        //default implementation ignored
    }

    @Around("pointcut()")
    public Object handle(ProceedingJoinPoint joinPoint) throws Throwable {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        final String[] parameterNames = ((MethodSignature) joinPoint.getSignature()).getParameterNames();
        if(parameterNames == null){
            log.info("接口请求:{},入参无",request.getRequestURI());
            return joinPoint.proceed();
        }
        Object[] args = joinPoint.getArgs();
        int size = parameterNames.length;
        Map<String,Object> params = new HashMap<>(size);
        for (int i = 0; i < size; i++) {
            params.put(parameterNames[i],args[i] );
        }
        log.info("接口请求:{},入参 => {}",request.getRequestURI(), JSONUtil.toJsonStr(params));
        return joinPoint.proceed();
    }
}