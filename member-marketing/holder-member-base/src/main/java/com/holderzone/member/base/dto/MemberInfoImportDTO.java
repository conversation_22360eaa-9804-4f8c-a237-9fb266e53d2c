package com.holderzone.member.base.dto;

import com.holderzone.member.base.entity.growth.HsaGrowthValueDetail;
import com.holderzone.member.base.entity.member.*;
import com.holderzone.member.common.dto.card.ECardCompareExcelDTO;
import com.holderzone.member.common.dto.card.MemberCardOpenDTO;
import com.holderzone.member.common.dto.excel.HsmMemberUploadExcel;
import com.holderzone.member.common.dto.member.CertificateInfo;
import com.holderzone.member.common.qo.grade.AdjustGradeQO;
import com.holderzone.member.common.vo.base.CountryVO;
import com.holderzone.member.common.vo.base.JobVO;
import com.holderzone.member.common.vo.grade.HsaMemberGradeInfoVO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class MemberInfoImportDTO implements Serializable {

    private static final long serialVersionUID = 6557223919458621173L;

    private HsmMemberUploadExcel hsmMemberUploadExcel;
    private Map<String, HsaMemberCertificateInfo> operationMemberCertificateNumMap;
    private List<HsaOperationMemberInfo> hsaOperationMemberInfoList;
    private List<HsaMemberImportRecordError> hsaMemberImportRecordErrorList;
    private HsaMemberImportRecord hsaMemberImportRecord;
    private List<HsaMemberCertificateInfo> hsaMemberCertificateInfos;
    private Map<String, String> checkIphoneNumMap;
    private Map<String, CertificateInfo> certificateInfoMap;
    private List<MemberCardOpenDTO> cardOpenList;
    private Map<String, ECardCompareExcelDTO> cardMap;
    private Map<String, Integer> cardCount;
    private Map<String, String> memberPhoneMap;
    private Map<String, List<HsaLabelSetting>> manualRelationLabelMap;
    private List<String> relationLabelMembers;
    private Map<String, HsaLabelSetting> labelMap;
    private List<HsaGrowthValueDetail> hsaGrowthValueDetails;
    private Map<String, HsaMemberGradeInfoVO> memberGradeEffectiveMap;
    private List<AdjustGradeQO> adjustGrades;
    private List<JobVO> jobList;
    private List<CountryVO> countryList;

    private Map<String, String> checkAccountNumMap;
}
