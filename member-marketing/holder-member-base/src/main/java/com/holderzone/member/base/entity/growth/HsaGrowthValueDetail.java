package com.holderzone.member.base.entity.growth;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.annotation.FieldLabel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 成长值明细表
 * @date 2021/11/23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "hsa_member_growth_value_detail")
public class HsaGrowthValueDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 
     * 成长值明细id
     */
    @FieldLabel(mark = "成长值明细实体")
    private Long id;

    /**
     * 账户成长值明细GUID
     */
    @FieldLabel(mark = "成长值明细实体")
    private String guid;

    /**
     * 运营主体GUID
     */
    @FieldLabel(mark = "成长值明细实体")
    private String operSubjectGuid;

    /**
     * 会员GUID
     */
    @FieldLabel(mark = "成长值明细实体")
    private String memberInfoGuid;

    /**
     * 成长值任务id
     */
    @FieldLabel(mark = "成长值明细实体")
    private String growthValueTaskId;

    /**
     * 任务名称
     */
    @FieldLabel(mark = "成长值明细实体")
    private String taskName;

    /**
     * 任务编号
     */
    @FieldLabel(mark = "成长值明细实体")
    private String taskNumber;

    /**
     * 记录说明
     */
    @FieldLabel(mark = "成长值明细实体")
    private String recordDeclaration;

    /**
     * 任务完成第次数
     */
    @FieldLabel(mark = "成长值明细实体")
    private Integer taskFinishTime;

    /**
     * 成长值变化类型,0增加,1减少
     */
    @FieldLabel(mark = "成长值明细实体")
    private Integer growthValueType;

    /**
     * 成长值
     */
    @FieldLabel(mark = "成长值明细实体")
    private Integer growthValue;

    /**
     * 翻倍成长值权益（翻了多少倍）
     */
    @FieldLabel(mark = "成长值明细实体")
    private BigDecimal multipleGrowth;

    /**
     * 翻倍权益guid
     */
    @FieldLabel(mark = "成长值明细实体")
    private String multipleEquitiesGuid;

    /**
     * 翻倍增加的成长值
     */
    @FieldLabel(mark = "成长值明细实体")
    private Integer multipleGrowthValue;

    /**
     * 会员卡翻倍成长值权益（翻了多少倍）
     */
    private BigDecimal multipleCardGrowth;

    /**
     * 会员卡翻倍增加的成长值
     */
    private Integer multipleCardGrowthValue;

    /**
     * 会员卡等级翻倍权益guid
     */
    private String multipleCardEquitiesGuid;

    /**
     * 0永久有效1固定时间
     */
    @FieldLabel(mark = "成长值明细实体")
    private Integer growthValidity;

    /**
     * 失效时间
     */
    @FieldLabel(mark = "成长值明细实体")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime growthValidityDate;

    /**
     * 任务完成时间
     */
    @FieldLabel(mark = "成长值明细实体")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime taskFinishDate;

    /**
     * 当前用户剩余成长值
     */
    @FieldLabel(mark = "成长值明细实体")
    private Integer remainGrowthValue;

    /**
     * 当前记录剩余成长值
     */
    @FieldLabel(mark = "成长值明细实体")
    private Integer recordRemainGrowthValue;

    /**
     * 成长值来源
     */
    @FieldLabel(mark = "成长值明细实体")
    private Integer growthValueSourceType;

    /**
     * 会员消费记录GUID
     */
    @FieldLabel(mark = "成长值明细实体")
    private String memberConsumptionGuid;

    /**
     * 订单编号
     */
    @FieldLabel(mark = "成长值明细实体")
    private String orderNumber;

    /**
     * 操作人员账号
     */
    @FieldLabel(mark = "成长值明细实体")
    private String operatorAccount;

    /**
     * 操作人员账号名字
     */
    @FieldLabel(mark = "成长值明细实体")
    private String operatorAccountName;

    /**
     * 门店来源guid
     */
    @FieldLabel(mark = "成长值明细实体")
    private String storeGuid;

    /**
     * 门店来源名称
     */
    @FieldLabel(mark = "成长值明细实体")
    private String storeName;

    /**
     * 成长值变动类型（1初始成长值，2系统调整，3成长值失效，4消费退款，5成长值任务
     *
     *
     * @see com.holderzone.member.common.enums.growth.SumValueChangeEnum
     */
    @FieldLabel(mark = "成长值明细实体")
    private Integer changeType;

    /**
     * 任务动作 0:注册 1:完善个人信息 2:单笔消费金额 3:消费指定商品 4:累计消费金额 5:累计消费笔数 6:单笔充值金额 7:累计充值金额
     *
     * @see com.holderzone.member.common.enums.growth.TaskActionEnum
     */
    @FieldLabel(mark = "成长值明细实体")
    private Integer taskAction;

    /**
     * 当前会员等级
     */
    @FieldLabel(mark = "成长值明细实体")
    private String currentMemberLevel;

    /**
     * 创建时间
     */
    @FieldLabel(mark = "成长值明细实体")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @FieldLabel(mark = "成长值明细实体")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

    /**
     * 角色类型
     *
     * @see com.holderzone.member.common.enums.member.RoleTypeEnum
     */
    @FieldLabel(mark = "成长值明细实体")
    private String roleType;

    /**
     * 会员等级GUID
     */
    @FieldLabel(mark = "成长值明细实体")
    private String memberInfoGradeGuid;

    /**
     * 到期时间
     */
    @FieldLabel(mark = "成长值明细实体")
    private LocalDateTime expireTime;

    /**
     * 有效期数量
     */
    @FieldLabel(mark = "成长值明细实体")
    private Integer num;

    /**
     * 有效期单位：3月 4年
     */
    @FieldLabel(mark = "成长值明细实体")
    private Integer unit;

    /**
     * 升级机制：1成长值升级 2付费升级
     *
     * @see com.holderzone.member.common.enums.grade.UpgradeTypeEnum
     */
    @FieldLabel(mark = "成长值明细实体")
    private Integer upgradeType;
}
