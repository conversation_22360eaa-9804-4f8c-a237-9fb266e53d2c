package com.holderzone.member.base.util;

import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.util.StyleUtil;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.holderzone.member.common.enums.card.MiniProgramOpenCardReturnEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;

import java.util.List;

/**
 * 给内容匹配的单元格配置红色字体
 */
@Slf4j
public class ContentHandler implements CellWriteHandler {

    /**操作列*/
    private Integer columnIndex;


    public ContentHandler(Integer columnIndex) {
        this.columnIndex = columnIndex;
    }

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
        log.info("row:{}",row.getRowNum());
    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        log.info("cell:{}",cell.getCellComment());
    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<CellData> list, Cell cell, Head head, Integer integer, Boolean aBoolean) {
        if(Boolean.FALSE.equals(aBoolean) && columnIndex == cell.getColumnIndex()) {
            log.info("第{}行，第{}列写入完成。", cell.getRowIndex(), cell.getColumnIndex());
            log.info("单元格内容={}",cell.getStringCellValue());
            Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
            WriteCellStyle writeCellStyle = new WriteCellStyle();
            // 设置字体颜色
            WriteFont headWriteFont = new WriteFont();
            String cellValue = cell.getStringCellValue();
            if (MiniProgramOpenCardReturnEnum.OPEN_ERROR.getDes().equals(cellValue)) {
                // 设置字体颜色
                headWriteFont.setColor(IndexedColors.RED.index);
            }
            writeCellStyle.setWriteFont(headWriteFont);
            CellStyle cellStyle = StyleUtil.buildContentCellStyle(workbook, writeCellStyle);
            cell.setCellStyle(cellStyle);
        }
    }
}
