package com.holderzone.member.base.dto;


import com.holderzone.member.base.entity.integral.HsaIntegralTask;
import com.holderzone.member.common.dto.event.BaseEvent;
import com.holderzone.member.common.dto.integral.IntegralChangeDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 成长值变更参数
 *
 * <AUTHOR>
 * @version 1.0
 * @description 积分变更参数
 */
@Data
public class IntegralChangeDetailDTO{

    private IntegralChangeDTO event;

    private HsaIntegralTask hsaIntegralTask;

    private int integralValue;

    private String awardGuid;
}
