package com.holderzone.member.base.service.card.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.assembler.*;
import com.holderzone.member.base.client.RequestGoalgoService;
import com.holderzone.member.base.client.StoreBaseService;
import com.holderzone.member.base.dto.MemberCardRechargeNewDTO;
import com.holderzone.member.base.entity.activity.HsaSubsidyActivityDetailRecord;
import com.holderzone.member.base.entity.card.*;
import com.holderzone.member.base.entity.credit.*;
import com.holderzone.member.base.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.base.entity.growth.HsaGrowthValueDetail;
import com.holderzone.member.base.entity.integral.HsaIntegralDetail;
import com.holderzone.member.base.entity.member.*;
import com.holderzone.member.base.entity.recharge.HsaCardRechargeGiftDetail;
import com.holderzone.member.base.event.ConsumptionOrderPublisher;
import com.holderzone.member.base.event.GrowthValueEvent;
import com.holderzone.member.base.event.MemberConsumptionDistributeEvent;
import com.holderzone.member.base.event.MemberGradeChangeEvent;
import com.holderzone.member.base.event.domain.ConsumptionOrderEventEnum;
import com.holderzone.member.base.factory.CardTimeFactory;
import com.holderzone.member.base.helper.RechargeGiftActivityHelper;
import com.holderzone.member.base.manage.MemberGrowthProcessorManage;
import com.holderzone.member.base.manage.MemberIntegralProcessorManage;
import com.holderzone.member.base.mapper.activity.HsaSubsidyActivityDetailRecordMapper;
import com.holderzone.member.base.mapper.card.*;
import com.holderzone.member.base.mapper.coupon.HsaMemberCouponLinkMapper;
import com.holderzone.member.base.mapper.coupon.HsaMemberCouponUseMapper;
import com.holderzone.member.base.mapper.credit.*;
import com.holderzone.member.base.mapper.gift.HsaCardRechargeGiftDetailMapper;
import com.holderzone.member.base.mapper.grade.HsaMemberEquitiesReceiveRecordMapper;
import com.holderzone.member.base.mapper.grade.HsaMemberGradeInfoMapper;
import com.holderzone.member.base.mapper.growth.HsaGrowthValueDetailMapper;
import com.holderzone.member.base.mapper.growth.HsaGrowthValueTaskMapper;
import com.holderzone.member.base.mapper.growth.HsaIntegralCommodityTouchDetailMapper;
import com.holderzone.member.base.mapper.integral.HsaIntegralDetailMapper;
import com.holderzone.member.base.mapper.integral.HsaIntegralTaskMapper;
import com.holderzone.member.base.mapper.member.*;
import com.holderzone.member.base.mapper.system.HsaStoreRuleInfoMapper;
import com.holderzone.member.base.service.activity.HsaSubsidyActivityDetailRecordService;
import com.holderzone.member.base.service.assembler.MerchantPayAssembler;
import com.holderzone.member.base.service.assembler.MemberCouponApplyAssembler;
import com.holderzone.member.base.service.cache.CacheService;
import com.holderzone.member.base.service.card.*;
import com.holderzone.member.base.service.card.business.TerCardCheckBusinessService;
import com.holderzone.member.base.service.card.business.TerMemberCardBusinessService;
import com.holderzone.member.base.service.coupon.IHsaMemberCouponLinkService;
import com.holderzone.member.base.service.credit.HsaCreditInfoService;
import com.holderzone.member.base.service.gift.HsaCardRechargeGiftDetailService;
import com.holderzone.member.base.service.gift.HsaRechargeGiftAmountRecordService;
import com.holderzone.member.base.service.grade.HsaControlledGradeStateService;
import com.holderzone.member.base.service.grade.HsaMemberGradeChangeDetailService;
import com.holderzone.member.base.service.grade.HsaMemberGradePriceDetailService;
import com.holderzone.member.base.service.growth.HsaGrowthValueDetailService;
import com.holderzone.member.base.service.integral.HsaIntegralDetailService;
import com.holderzone.member.base.service.integral.IntegralTaskProcessorService;
import com.holderzone.member.base.service.member.*;
import com.holderzone.member.base.service.send.ShortMessageSendService;
import com.holderzone.member.base.service.send.WechatSendService;
import com.holderzone.member.base.service.settlement.SettlementCmdService;
import com.holderzone.member.base.support.MemberRightsSupport;
import com.holderzone.member.base.transform.card.HsaPhysicalCardStrategyRecordTransform;
import com.holderzone.member.common.annotation.RedissonLock;
import com.holderzone.member.common.constant.*;
import com.holderzone.member.common.dto.base.*;
import com.holderzone.member.common.dto.card.*;
import com.holderzone.member.common.dto.crm.CrmRefundPayDTO;
import com.holderzone.member.common.dto.event.GrowthValueChangeEvent;
import com.holderzone.member.common.dto.event.SendMemberConsumptionDistributeEvent;
import com.holderzone.member.common.dto.integral.EventGrowthChangeDTO;
import com.holderzone.member.common.dto.integral.IntegralChangeDTO;
import com.holderzone.member.common.dto.member.EndConsumptionInfoDTO;
import com.holderzone.member.common.dto.message.SendShortMessageDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.pay.*;
import com.holderzone.member.common.dto.terminal.SettlementBalanceDTO;
import com.holderzone.member.common.dto.terminal.SubsidyAbatementBalanceDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponUse;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.card.*;
import com.holderzone.member.common.enums.coupon.CouponMemberStateEnum;
import com.holderzone.member.common.enums.coupon.CouponPackageTypeEnum;
import com.holderzone.member.common.enums.credit.OperateType;
import com.holderzone.member.common.enums.credit.PayStatusEnum;
import com.holderzone.member.common.enums.credit.ReconciliationStatusEnum;
import com.holderzone.member.common.enums.exception.CardOperationExceptionEnum;
import com.holderzone.member.common.enums.exception.MemberInfoCardExceptionEnum;
import com.holderzone.member.common.enums.gift.GiftDetailRefreshEnum;
import com.holderzone.member.common.enums.growth.*;
import com.holderzone.member.common.enums.mall.order.OrderRefundConditionEnum;
import com.holderzone.member.common.enums.member.*;
import com.holderzone.member.common.enums.order.OrderRefundTypeEnum;
import com.holderzone.member.common.enums.wechat.WechatMsgSendType;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.MemberMallToolFeign;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.feign.MemberSettlementFeign;
import com.holderzone.member.common.module.base.purchase.dto.PurchaseOrderStateDto;
import com.holderzone.member.common.module.base.purchase.enums.ConsumptionOrderStateEnum;
import com.holderzone.member.common.module.settlement.apply.dto.OrderPayDiscountDTO;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.qo.card.MemberFaceQO;
import com.holderzone.member.common.qo.card.*;
import com.holderzone.member.common.qo.gift.TerCardRechargeDataVO;
import com.holderzone.member.common.qo.gift.TerRechargeActivityQO;
import com.holderzone.member.common.qo.member.EndConsumptionInfoQO;
import com.holderzone.member.common.qo.member.MemberSendCouponQO;
import com.holderzone.member.common.qo.member.UpdateLabelStatusQO;
import com.holderzone.member.common.qo.redeem.RequestCheckRedeemApplyQO;
import com.holderzone.member.common.qo.redeem.RequestUpdateDtlQO;
import com.holderzone.member.common.qo.redeem.RespondEditActiveVO;
import com.holderzone.member.common.qo.tool.MessagesSendQO;
import com.holderzone.member.common.qo.tool.ShortMessagesConfigDTO;
import com.holderzone.member.common.util.date.ValidTimeUtils;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.number.NumberUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import com.holderzone.member.common.util.transaction.TransactionUtil;
import com.holderzone.member.common.vo.card.*;
import com.holderzone.member.common.vo.coupon.MemberCouponPackageVO;
import com.holderzone.member.common.vo.gift.RechargeGiftActivityVO;
import com.holderzone.member.common.vo.gift.RechargeOrderGiftSummaryVO;
import com.holderzone.member.common.vo.member.HsaDepositStrategyVO;
import com.holderzone.member.common.vo.tool.SendMessagesConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.member.common.constant.NumberConstant.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class HsaCardInfoServiceImpl implements HsaCardInfoService {

    private static final String MEMBER_CARD_NOT = "会员卡不存在";

    private static final String MEMBER_CARD_EXPIRED = "会员卡已过期";

    private final HsaCardInfoMapper hsaCardInfoMapper;

    private final HsaCardBaseInfoMapper hsaCardBaseInfoMapper;

    private final HsaCardBalanceRuleMapper hsaCardBalanceRuleMapper;

    private final HsaMemberConsumptionPayWayMapper hsaMemberConsumptionPayWayMapper;

    private final HsaMemberFundingDetailMapper hsaMemberFundingDetailMapper;

    private final HsaRechargeGiftAmountRecordService hsaRechargeGiftAmountRecordService;

    private final HsaMemberFundingDetailService hsaMemberFundingDetailService;

    private final HsaMemberConsumptionMapper hsaMemberConsumptionMapper;

    @Resource
    private TerCardCheckBusinessService checkBusinessService;

    @Resource
    private HsaMemberCouponUseMapper hsaMemberCouponUseMapper;

    @Resource
    private IHsaMemberCouponLinkService hsaMemberCouponLinkService;

    @Resource
    private HsaMemberCouponLinkMapper hsaMemberCouponLinkMapper;

    @Resource
    private HsaMemberConsumptionService hsaMemberConsumptionService;

    private final MemberMarketingFeign memberMarketingFeign;

    @Resource
    private MemberMallToolFeign memberMallToolFeign;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private TransactionUtil transactionUtil;

    @Resource
    private ExternalSupport externalSupport;

    private final HsaSubsidyActivityDetailRecordMapper hsaSubsidyActivityDetailRecordMapper;

    private final HsaMemberInfoCardMapper hsaMemberInfoCardMapper;

    @Resource
    private HsaMemberInfoCardErrorMapper hsaMemberInfoCardErrorMapper;

    @Resource
    private MemberIntegralProcessorManage memberIntegralProcessorManage;

    @Resource
    private MemberGrowthProcessorManage memberGrowthProcessorManage;

    @Resource
    private TerMemberCardBusinessService terMemberCardBusinessService;

    private final HsaCardRechargeGiftDetailService hsaCardRechargeGiftDetailService;

    @Resource
    private HsaCardRechargeGiftDetailMapper hsaCardRechargeGiftDetailMapper;

    @Autowired
    @Lazy
    private HsaCreditInfoService hsaCreditInfoService;

    public final Executor memberBaseThreadExecutor;

    @Resource
    private ShortMessageSendService sendService;

    @Resource
    private MemberCouponApplyAssembler assembler;

    @Resource
    private WechatSendService wechatSendService;

    @Resource
    private ShortMessageSendService shortMessageSendService;

    private final HsaStoreCardRuleMapper hsaStoreCardRuleMapper;

    private final HsaStoreRuleInfoMapper hsaStoreRuleInfoMapper;

    private final HsaGrowthValueTaskMapper hsaGrowthValueTaskMapper;

    private final HsaGrowthValueDetailMapper hsaGrowthValueDetailMapper;

    private final HsaMemberGradeChangeDetailService hsaMemberGradeChangeDetailService;

    private final RedissonClient redissonClient;

    @Autowired
    @Lazy
    private HsaGrowthValueDetailService hsaGrowthValueDetailService;

    @Autowired
    @Lazy
    private HsaIntegralDetailService hsaIntegralDetailService;

    @Autowired
    @Lazy
    private IntegralTaskProcessorService integralTaskProcessorService;

    private final HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Resource
    private MemberFundingDetailAssembler memberFundingDetailAssembler;

    @Resource
    private HsaMemberGradePriceDetailMapper hsaMemberGradePriceDetailMapper;

    @Resource
    private HsaIntegralDetailMapper hsaIntegralDetailMapper;

    @Resource
    private HsaMemberOrderDiscountMapper hsaMemberOrderDiscountMapper;

    @Resource
    private IHsaMemberOrderDiscountService hsaMemberOrderDiscountService;

    @Resource
    private HsaIntegralTaskMapper hsaIntegralTaskMapper;

    @Resource
    private HsaCreditInfoMapper hsaCreditInfoMapper;

    @Resource
    private SystemRoleHelper systemRoleHelper;

    @Resource
    private HsaCreditUserMapper hsaCreditUserMapper;

    @Resource
    private HsaCreditOrderRecordMapper hsaCreditOrderRecordMapper;

    @Resource
    private HsaCreditWalletLogMapper hsaCreditWalletLogMapper;

    @Resource
    private HsaClearingStatementOperateRecordMapper hsaClearingStatementOperateRecordMapper;

    @Resource
    private HsaCreditFundingDetailMapper hsaCreditFundingDetailMapper;

    @Resource
    private HsaClearingStatementMapper hsaClearingStatementMapper;

    @Resource
    private HsaIntegralCommodityTouchDetailMapper integralCommodityTouchDetailMapper;

    @Resource
    private CacheService cacheService;

    @Resource
    private HsaControlledGradeStateService gradeStateService;

    @Autowired
    private HsaMemberCardExcelService hsaMemberCardExcelService;

    @Autowired
    private HsaMemberGradePriceDetailService hsaMemberGradePriceDetailService;

    @Lazy
    @Resource
    private HsaMemberLabelService hsaMemberLabelService;

    @Resource
    private ConsumptionOrderPublisher consumptionOrderPublisher;

    @Resource
    private HsaKinsfolkManagementService hsaKinsfolkManagementService;

    @Resource
    private RechargeGiftActivityHelper activityHelper;

    /**
     * 优惠结算操作
     */
    @Resource
    private SettlementCmdService settlementCmdService;

    /**
     * 权益处理
     */
    @Resource
    private MemberRightsSupport memberRightsSupport;

    private final HsaMemberCardExcelMapper hsaMemberCardExcelMapper;

    private final HsaPhysicalCardMapper hsaPhysicalCardMapper;

    private final HsaSubsidyActivityDetailRecordService hsaSubsidyActivityDetailRecordService;

    private final HsaElectronicCardMapper hsaElectronicCardMapper;

    private final GuidGeneratorUtil guidGeneratorUtil;

    private final HsaMemberEquitiesReceiveRecordMapper hsaMemberEquitiesReceiveRecordMapper;

    private final MemberGradeChangeEvent memberGradeChangeEvent;

    private final HsaDepositStrategyService hsaDepositStrategyService;

    private final HsaPhysicalCardStrategyRecordService hsaPhysicalCardStrategyRecordService;

    public HsaCardInfoServiceImpl(HsaCardInfoMapper hsaCardInfoMapper, HsaCardBaseInfoMapper hsaCardBaseInfoMapper,
                                  HsaMemberConsumptionPayWayMapper hsaMemberConsumptionPayWayMapper,
                                  HsaMemberFundingDetailMapper hsaMemberFundingDetailMapper,
                                  HsaRechargeGiftAmountRecordService hsaRechargeGiftAmountRecordService, HsaMemberFundingDetailService hsaMemberFundingDetailService,
                                  HsaMemberConsumptionMapper hsaMemberConsumptionMapper,
                                  MemberMarketingFeign memberMarketingFeign, TransactionTemplate transactionTemplate, HsaMemberInfoCardMapper hsaMemberInfoCardMapper,
                                  @Lazy HsaCardRechargeGiftDetailService hsaCardRechargeGiftDetailService, HsaStoreCardRuleMapper hsaStoreCardRuleMapper,
                                  HsaStoreRuleInfoMapper hsaStoreRuleInfoMapper, HsaGrowthValueTaskMapper hsaGrowthValueTaskMapper,
                                  HsaGrowthValueDetailMapper hsaGrowthValueDetailMapper,
                                  HsaMemberGradeChangeDetailService hsaMemberGradeChangeDetailService,
                                  HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper,
                                  HsaPhysicalCardMapper hsaPhysicalCardMapper,
                                  HsaElectronicCardMapper hsaElectronicCardMapper,
                                  GuidGeneratorUtil guidGeneratorUtil,
                                  HsaPhysicalCardCreateRecordMapper recordMapper,
                                  HsaCardBalanceRuleMapper hsaCardBalanceRuleMapper,
                                  HsaSubsidyActivityDetailRecordMapper hsaSubsidyActivityDetailRecordMapper,
                                  Executor memberBaseThreadExecutor,
                                  HsaSubsidyActivityDetailRecordService hsaSubsidyActivityDetailRecordService,
                                  HsaMemberGradeInfoMapper hsaMemberGradeInfoMapper,
                                  HsaMemberEquitiesReceiveRecordMapper hsaMemberEquitiesReceiveRecordMapper,
                                  MemberGradeChangeEvent memberGradeChangeEvent,
                                  RedissonClient redissonClient, HsaMemberCardExcelMapper hsaMemberCardExcelMapper, HsaDepositStrategyService hsaDepositStrategyService, HsaPhysicalCardStrategyRecordService hsaPhysicalCardStrategyRecordService) {
        this.hsaCardInfoMapper = hsaCardInfoMapper;
        this.hsaCardBaseInfoMapper = hsaCardBaseInfoMapper;
        this.hsaMemberConsumptionPayWayMapper = hsaMemberConsumptionPayWayMapper;
        this.hsaMemberFundingDetailMapper = hsaMemberFundingDetailMapper;
        this.hsaRechargeGiftAmountRecordService = hsaRechargeGiftAmountRecordService;
        this.hsaMemberFundingDetailService = hsaMemberFundingDetailService;
        this.hsaMemberConsumptionMapper = hsaMemberConsumptionMapper;
        this.transactionTemplate = transactionTemplate;
        this.memberMarketingFeign = memberMarketingFeign;
        this.transactionTemplate = transactionTemplate;
        this.hsaMemberInfoCardMapper = hsaMemberInfoCardMapper;
        this.hsaCardRechargeGiftDetailService = hsaCardRechargeGiftDetailService;
        this.hsaStoreCardRuleMapper = hsaStoreCardRuleMapper;
        this.hsaStoreRuleInfoMapper = hsaStoreRuleInfoMapper;
        this.hsaGrowthValueTaskMapper = hsaGrowthValueTaskMapper;
        this.hsaGrowthValueDetailMapper = hsaGrowthValueDetailMapper;
        this.hsaMemberGradeChangeDetailService = hsaMemberGradeChangeDetailService;
        this.hsaOperationMemberInfoMapper = hsaOperationMemberInfoMapper;
        this.hsaPhysicalCardMapper = hsaPhysicalCardMapper;
        this.hsaElectronicCardMapper = hsaElectronicCardMapper;
        this.guidGeneratorUtil = guidGeneratorUtil;
        this.recordMapper = recordMapper;
        this.hsaCardBalanceRuleMapper = hsaCardBalanceRuleMapper;
        this.hsaSubsidyActivityDetailRecordMapper = hsaSubsidyActivityDetailRecordMapper;
        this.memberBaseThreadExecutor = memberBaseThreadExecutor;
        this.hsaSubsidyActivityDetailRecordService = hsaSubsidyActivityDetailRecordService;
        this.hsaMemberGradeInfoMapper = hsaMemberGradeInfoMapper;
        this.hsaMemberEquitiesReceiveRecordMapper = hsaMemberEquitiesReceiveRecordMapper;
        this.memberGradeChangeEvent = memberGradeChangeEvent;
        this.redissonClient = redissonClient;
        this.hsaMemberCardExcelMapper = hsaMemberCardExcelMapper;
        this.hsaDepositStrategyService = hsaDepositStrategyService;
        this.hsaPhysicalCardStrategyRecordService = hsaPhysicalCardStrategyRecordService;
    }

    private final HsaPhysicalCardCreateRecordMapper recordMapper;

    private final HsaMemberGradeInfoMapper hsaMemberGradeInfoMapper;

    @Resource
    private MemberCardOperationService memberCardOperationService;

    @Resource
    @Lazy
    private HsaMemberInfoCardService hsaMemberInfoCardService;

    @Resource
    private StoreBaseService storeBaseService;

    @Resource
    private HsaIntegralCommodityTouchDetailMapper hsaIntegralCommodityTouchDetailMapper;

    @Resource
    private GrowthValueEvent growthValueEvent;

    @Resource
    private MemberConsumptionDistributeEvent distributeEvent;

    @Lazy
    @Resource
    private HsaLabelSettingService hsaLabelSettingService;

    @Lazy
    @Resource
    private RequestGoalgoService hsaRequestGoalgoService;

    @Resource
    private RedisTemplate redisTemplate;

    private static final String USE_STORE_MESSAGE = "%s家门店可用";

    private static final String ALL_STORE_MESSAGE = "全部门店可适用";

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    @Lazy
    private PayServiceImpl payService;

    @Resource
    private HsaCardOpenRuleMapper hsaCardOpenRuleMapper;

    @Resource
    private MemberSettlementFeign memberSettlementFeign;

    @Resource
    private HsaMemberInfoWeChatMapper memberInfoWeChatMapper;

    @Resource
    private HsaCardBalanceRuleService cardBalanceRuleService;

    @Value("${feign.base}")
    private String RequestBaseHost;

    @Override
    public Boolean isCardExist(String cardNum) {
        HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getPhysicalCardNum, cardNum)
                .eq(HsaMemberInfoCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
        return Objects.nonNull(hsaMemberInfoCard);
    }

    @Override
    public BindingMemberCardVO isBindingMemberCard(String cardNum) {
        BindingMemberCardVO bindingMemberCardVO = new BindingMemberCardVO();
        HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getPhysicalCardNum, cardNum)
                .eq(HsaMemberInfoCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
        if (Objects.nonNull(hsaMemberInfoCard)) {
            hsaMemberInfoCardService.setBindingMemberCard(bindingMemberCardVO, hsaMemberInfoCard);
            if (StringUtils.isNotBlank(hsaMemberInfoCard.getMemberInfoGuid())) {
                HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(hsaMemberInfoCard.getMemberInfoGuid());
                bindingMemberCardVO
                        .setMemberAccount(hsaOperationMemberInfo.getMemberAccount());
            }
        }
        return bindingMemberCardVO;
    }


    /**
     * 实体卡激活
     *
     * @return CardActivateVO
     */
    @Override
    public CardActivateVO cardActivate(CardInfoDTO cardInfoDTO) {
        log.debug("实体卡激活参数：{}", JSON.toJSONString(cardInfoDTO));
        HsaPhysicalCard hsaPhysicalCard = getHsaPhysicalCard(cardInfoDTO);
        HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getPhysicalCardGuid, hsaPhysicalCard.getGuid()));

        HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(hsaPhysicalCard.getCardGuid());
        checkBusinessService.checkCard(hsaCardBaseInfo, hsaMemberInfoCard);
        LocalDateTime now = LocalDateTime.now();
        hsaPhysicalCard.setCardState(PhysicalCardStateEnum.NORMAL.getCode())
                .setActivationTime(now);

        hsaMemberInfoCardService.setHsaMemberInfoCardValidityDate(hsaMemberInfoCard, hsaCardBaseInfo, now);
        hsaPhysicalCardMapper.updateByGuid(hsaPhysicalCard);
        hsaMemberInfoCardMapper.updateByGuid(hsaMemberInfoCard);
        return new CardActivateVO().setResult(BooleanEnum.TRUE.getCode())
                .setMemberInfoCardGuid(hsaMemberInfoCard.getGuid())
                .setCardName(hsaMemberInfoCard.getCardName())
                .setCardNum(hsaPhysicalCard.getCardNum())
                .setMemberPhone(hsaMemberInfoCard.getMemberPhoneNum());
    }

    @Override
    public CardActivateVO queryPhysicalCardInfo(CardInfoDTO cardInfoDTO) {
        HsaPhysicalCard hsaPhysicalCard = getHsaPhysicalCard(cardInfoDTO);
        HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getPhysicalCardGuid, hsaPhysicalCard.getGuid()));
        return new CardActivateVO()
                .setMemberInfoCardGuid(hsaMemberInfoCard.getGuid())
                .setCardName(hsaMemberInfoCard.getCardName())
                .setCardNum(hsaPhysicalCard.getCardNum())
                .setMemberPhone(hsaMemberInfoCard.getMemberPhoneNum());
    }

    private HsaPhysicalCard getHsaPhysicalCard(CardInfoDTO cardInfoDTO) {
        HsaPhysicalCard hsaPhysicalCard;
        //卡号+绑定码
        if (!StringUtils.isBlank(cardInfoDTO.getCardBindingNum())) {
            hsaPhysicalCard = hsaPhysicalCardMapper.selectOne(new LambdaQueryWrapper<HsaPhysicalCard>()
                    .eq(HsaPhysicalCard::getCardNum, cardInfoDTO.getCardNum())
                    .eq(HsaPhysicalCard::getCardBindingNum, cardInfoDTO.getCardBindingNum()));
        } else {
            //uid
            hsaPhysicalCard = hsaPhysicalCardMapper.selectOne(new LambdaQueryWrapper<HsaPhysicalCard>()
                    .eq(HsaPhysicalCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                    .eq(HsaPhysicalCard::getUid, cardInfoDTO.getUid()));
        }

        if (Objects.isNull(hsaPhysicalCard)) {
            throw new MemberBaseException(MemberInfoCardExceptionEnum.CARD_NOT_EXISTS);
        }
        return hsaPhysicalCard;
    }

    /**
     * @param type           type=1 表示更新实体卡状态为：未激活状态，否则去激活实体卡
     * @param cardNum        实体卡卡号
     * @param cardBindingNum 实体卡绑定码
     */
    @Override
    public CardActivateVO updateCardStatus(Integer type, String cardNum, String cardBindingNum) {
        if (BooleanEnum.TRUE.getCode() == type) {
            HsaPhysicalCard hsaPhysicalCard = hsaPhysicalCardMapper.selectOne(new LambdaQueryWrapper<HsaPhysicalCard>()
                    .eq(HsaPhysicalCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                    .eq(HsaPhysicalCard::getCardNum, cardNum)
                    .eq(HsaPhysicalCard::getCardBindingNum, cardBindingNum));
            if (Objects.isNull(hsaPhysicalCard)) {
                throw new MemberBaseException(MemberInfoCardExceptionEnum.CARD_NOT_EXISTS);
            }

            HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                    .eq(HsaMemberInfoCard::getPhysicalCardGuid, hsaPhysicalCard.getGuid()));
            hsaMemberInfoCard.setPhysicalCardState(PhysicalCardStateEnum.NOT_ACTIVATE.getCode());
            hsaPhysicalCard.setCardState(PhysicalCardStateEnum.NOT_ACTIVATE.getCode());
            hsaPhysicalCardMapper.updateByGuid(hsaPhysicalCard);
            hsaMemberInfoCardMapper.updateByGuid(hsaMemberInfoCard);
            return null;
        } else {
            CardInfoDTO cardInfoDTO = new CardInfoDTO();
            cardInfoDTO.setCardNum(cardNum);
            cardInfoDTO.setCardBindingNum(cardBindingNum);
            return this.cardActivate(cardInfoDTO);
        }
    }

    @Override
    public boolean updateWriteCardStatus(WriteCardQO writeCardQO) {
        HsaPhysicalCard hsaPhysicalCard = hsaPhysicalCardMapper.selectOne(
                new LambdaQueryWrapper<HsaPhysicalCard>()
                        .eq(HsaPhysicalCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                        .eq(HsaPhysicalCard::getCardNum, writeCardQO.getCardNum())
                        .eq(HsaPhysicalCard::getCardBindingNum, writeCardQO.getCardBindingNum()));
        String physicalCardRecordGuid = Optional.ofNullable(hsaPhysicalCard)
                .map(HsaPhysicalCard::getMakePhysicalCardRecordGuid).orElse(null);
        HsaPhysicalCardCreateRecord hsaPhysicalCardCreateRecord = recordMapper.selectOne(
                new LambdaQueryWrapper<HsaPhysicalCardCreateRecord>()
                        .eq(HsaPhysicalCardCreateRecord::getGuid, physicalCardRecordGuid));
        if (Objects.isNull(hsaPhysicalCardCreateRecord)) {
            throw new MemberBaseException("未查询到该卡信息");
        }
        hsaPhysicalCardCreateRecord.setCreateResult(writeCardQO.getStatus());
        return recordMapper.updateByGuid(hsaPhysicalCardCreateRecord);
    }

    @Override
    public TerCardRechargeDataVO getTerRechargeList(TerRechargeActivityQO terRechargeActivityQO) {
        TerCardRechargeDataVO terCardRechargeDataVO = new TerCardRechargeDataVO();
        List<String> rechargeMoneyList = Lists.newArrayList();
        if (StringUtils.isNotBlank(terRechargeActivityQO.getCardGuid())) {
            HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(terRechargeActivityQO.getCardGuid());
            rechargeMoneyList = JSON.parseArray(hsaCardBaseInfo.getRechargeMoneys(), String.class);
        }


        List<RechargeGiftActivityVO> rechargeGiftActivityVOS = Lists.newArrayList();
        List<RechargeThresholdVO> thresholdVOS = activityHelper.terBuildRechargeThreshold(
                terRechargeActivityQO,
                rechargeMoneyList,
                rechargeGiftActivityVOS);

        terCardRechargeDataVO.setRechargeThresholdList(thresholdVOS);

        //充值提示
        terCardRechargeDataVO.setRechargeTips(activityHelper.handleRechargeGiftActivity(rechargeGiftActivityVOS));

        List<RechargeActivityDescVO> rechargeActivityDesc = activityHelper.buildActivityDesc(rechargeGiftActivityVOS);
        terCardRechargeDataVO.setRechargeActivityDescList(rechargeActivityDesc);
        return terCardRechargeDataVO;
    }

    /**
     * 手机号/卡号登录获取账户下会员卡信息
     *
     * @param terLoginMemberCardQO terLoginMemberCardQO
     * @return List<TerLoginMemberCardVO>
     */
    @Override
    public TerBaseLoginMemberCardVO loginMemberCard(TerLoginMemberCardQO terLoginMemberCardQO) {
        // 1. 初始化基础数据
        TerBaseLoginMemberCardVO terBaseLoginMemberCardVO = new TerBaseLoginMemberCardVO();
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();

        // 2. 前置校验和准备
        prepareLoginRequest(terLoginMemberCardQO, terBaseLoginMemberCardVO);

        // 3. 验证登录参数
        String loginNum = validateLoginNum(terLoginMemberCardQO);

        // 4. 获取余额规则
        HsaCardBalanceRule hsaCardBalanceRule = getCardBalanceRule();

        // 5. 初始化登录结果数据
        List<TerLoginMemberCardVO> terLoginMemberCardListVOList = Lists.newArrayList();
        List<HsaMemberInfoCard> hsaMemberInfoCardList;
        HsaOperationMemberInfo hsaOperationMemberInfo;

        // 6. 处理登录逻辑（手机号登录 vs 卡号登录）
        if (isaCheckLoginNum(loginNum)) {
            // 手机号登录处理
            hsaOperationMemberInfo = processPhoneNumLogin(terLoginMemberCardQO, terBaseLoginMemberCardVO, loginNum, headerUserInfo);
            hsaMemberInfoCardList = hsaMemberInfoCardMapper.selectList(
                    new LambdaQueryWrapper<HsaMemberInfoCard>()
                            .in(HsaMemberInfoCard::getMemberInfoGuid, hsaOperationMemberInfo.getGuid()));
        } else {
            // 卡号登录处理
            hsaOperationMemberInfo = processCardNumLogin(terLoginMemberCardQO, loginNum, headerUserInfo,
                                                       hsaCardBalanceRule, terLoginMemberCardListVOList);
            hsaMemberInfoCardList = getHsaMemberInfoCards(new TerBaseLoginMemberCardVO(),
                                                        getMemberInfoCard(terLoginMemberCardQO, loginNum, headerUserInfo),
                                                        null, hsaOperationMemberInfo);
        }

        // 7. 设置终端检查状态和等级头像
        setTerminalStatusAndGradeIcon(terLoginMemberCardQO, terBaseLoginMemberCardVO, hsaOperationMemberInfo);

        // 8. 校验并设置会员卡信息
        setMemberCardInfo(terLoginMemberCardQO, terBaseLoginMemberCardVO, terLoginMemberCardListVOList,
                         hsaOperationMemberInfo, hsaMemberInfoCardList, hsaCardBalanceRule);

        // 9. 处理冻结和积分信息
        handleFreezeAndIntegralInfo(terLoginMemberCardQO, terBaseLoginMemberCardVO, hsaOperationMemberInfo);

        // 订单不记名优惠券绑定会员
        bindOrderMemberCouponLink(terLoginMemberCardQO, hsaOperationMemberInfo);

        return terBaseLoginMemberCardVO;
    }

    /**
     * 准备登录请求
     *
     * @param terLoginMemberCardQO 登录请求参数
     * @param terBaseLoginMemberCardVO 登录响应对象
     */
    private void prepareLoginRequest(TerLoginMemberCardQO terLoginMemberCardQO, TerBaseLoginMemberCardVO terBaseLoginMemberCardVO) {
        // 前置查询，获取实际登录参数
        prepositionCheck(terLoginMemberCardQO);

        // 校验手机国家代码
        terLoginMemberCardQO.checkPhoneCountryCode();

        // 亲属人脸登录校验
        checkUserId(terLoginMemberCardQO, terBaseLoginMemberCardVO);
    }

    /**
     * 验证登录号码
     *
     * @param terLoginMemberCardQO 登录请求参数
     * @return 登录号码
     */
    private String validateLoginNum(TerLoginMemberCardQO terLoginMemberCardQO) {
        String loginNum = terLoginMemberCardQO.getLoginNum();
        if (StringUtils.isEmpty(loginNum)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.LOGIN_INFORMATION_CANNOT_BE_EMPTY);
        }
        return loginNum;
    }

    /**
     * 获取卡余额规则
     *
     * @return 卡余额规则
     */
    private HsaCardBalanceRule getCardBalanceRule() {
        return hsaCardBalanceRuleMapper.selectOne(new LambdaQueryWrapper<HsaCardBalanceRule>()
                .eq(HsaCardBalanceRule::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
    }

    /**
     * 处理手机号登录
     *
     * @param terLoginMemberCardQO 登录请求参数
     * @param terBaseLoginMemberCardVO 登录响应对象
     * @param loginNum 登录号码
     * @param headerUserInfo 请求头用户信息
     * @return 会员操作信息
     */
    private HsaOperationMemberInfo processPhoneNumLogin(TerLoginMemberCardQO terLoginMemberCardQO,
                                                       TerBaseLoginMemberCardVO terBaseLoginMemberCardVO,
                                                       String loginNum,
                                                       HeaderUserInfo headerUserInfo) {
        // 内置挂账二维码登录校验
        String phoneNum = checkBusinessService.getCheckPhoneNum(terLoginMemberCardQO, terBaseLoginMemberCardVO, loginNum);

        // 获取会员信息
        HsaOperationMemberInfo hsaOperationMemberInfo = getHsaOperationMemberInfo(terLoginMemberCardQO, phoneNum, headerUserInfo);
        checkBusinessService.checkMemberInfo(hsaOperationMemberInfo);

        // 设置基础信息
        BeanUtils.copyProperties(hsaOperationMemberInfo, terBaseLoginMemberCardVO);
        terBaseLoginMemberCardVO.setAccountState(hsaOperationMemberInfo.getAccountState());
        terBaseLoginMemberCardVO.setMemberInfoGuid(hsaOperationMemberInfo.getGuid());

        // 设置终端检查状态
        setTerminalCheckStatus(terLoginMemberCardQO, TerminalCheckStatusEnum.PHONE_NUM);

        return hsaOperationMemberInfo;
    }

    /**
     * 处理卡号登录
     *
     * @param terLoginMemberCardQO 登录请求参数
     * @param loginNum 登录号码
     * @param headerUserInfo 请求头用户信息
     * @param hsaCardBalanceRule 卡余额规则
     * @param terLoginMemberCardListVOList 会员卡列表
     * @return 会员操作信息
     */
    private HsaOperationMemberInfo processCardNumLogin(TerLoginMemberCardQO terLoginMemberCardQO,
                                                      String loginNum,
                                                      HeaderUserInfo headerUserInfo,
                                                      HsaCardBalanceRule hsaCardBalanceRule,
                                                      List<TerLoginMemberCardVO> terLoginMemberCardListVOList) {
        // 获取会员持卡信息
        HsaMemberInfoCard hsaMemberInfoCard = getMemberInfoCard(terLoginMemberCardQO, loginNum, headerUserInfo);

        HsaOperationMemberInfo hsaOperationMemberInfo = null;
        if (StringUtils.isNotEmpty(hsaMemberInfoCard.getMemberInfoGuid())) {
            hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(hsaMemberInfoCard.getMemberInfoGuid());
            checkBusinessService.checkMemberInfo(hsaOperationMemberInfo);
        }

        // 单卡校验
        setLoginMemberCardInfo(terLoginMemberCardListVOList, hsaMemberInfoCard, terLoginMemberCardQO, hsaOperationMemberInfo, hsaCardBalanceRule);

        return hsaOperationMemberInfo;
    }

    /**
     * 设置终端状态和等级头像
     *
     * @param terLoginMemberCardQO 登录请求参数
     * @param terBaseLoginMemberCardVO 登录响应对象
     * @param hsaOperationMemberInfo 会员操作信息
     */
    private void setTerminalStatusAndGradeIcon(TerLoginMemberCardQO terLoginMemberCardQO,
                                             TerBaseLoginMemberCardVO terBaseLoginMemberCardVO,
                                             HsaOperationMemberInfo hsaOperationMemberInfo) {
        // 设置终端检查状态
        terBaseLoginMemberCardVO.setTerminalCheckStatus(terLoginMemberCardQO.getTerminalCheckStatus());

        // 获取等级头像
        setGradeIcon(hsaOperationMemberInfo, terBaseLoginMemberCardVO);
    }

    /**
     * 设置会员卡信息
     *
     * @param terLoginMemberCardQO 登录请求参数
     * @param terBaseLoginMemberCardVO 登录响应对象
     * @param terLoginMemberCardListVOList 会员卡列表
     * @param hsaOperationMemberInfo 会员操作信息
     * @param hsaMemberInfoCardList 会员信息卡列表
     * @param hsaCardBalanceRule 卡余额规则
     */
    private void setMemberCardInfo(TerLoginMemberCardQO terLoginMemberCardQO,
                                 TerBaseLoginMemberCardVO terBaseLoginMemberCardVO,
                                 List<TerLoginMemberCardVO> terLoginMemberCardListVOList,
                                 HsaOperationMemberInfo hsaOperationMemberInfo,
                                 List<HsaMemberInfoCard> hsaMemberInfoCardList,
                                 HsaCardBalanceRule hsaCardBalanceRule) {
        // 校验会员所有持卡信息
        getTerBaseLoginMemberCardVO(
                terLoginMemberCardQO,
                terBaseLoginMemberCardVO,
                terLoginMemberCardListVOList,
                hsaOperationMemberInfo,
                hsaMemberInfoCardList,
                hsaCardBalanceRule);
    }

    /**
     * 处理冻结和积分信息
     *
     * @param terLoginMemberCardQO 登录请求参数
     * @param terBaseLoginMemberCardVO 登录响应对象
     * @param hsaOperationMemberInfo 会员操作信息
     */
    private void handleFreezeAndIntegralInfo(TerLoginMemberCardQO terLoginMemberCardQO,
                                           TerBaseLoginMemberCardVO terBaseLoginMemberCardVO,
                                           HsaOperationMemberInfo hsaOperationMemberInfo) {
        // 处理冻结金额
        terBaseLoginMemberCardVO.setTerLoginMemberCardListVOS(
                dealFreezeAmount(terLoginMemberCardQO, terBaseLoginMemberCardVO.getTerLoginMemberCardListVOS()));

        // 处理冻结业务
        terMemberCardBusinessService.dealFreeze(terBaseLoginMemberCardVO, terLoginMemberCardQO, hsaOperationMemberInfo);

        // 设置会员积分
        setMemberIntegral(hsaOperationMemberInfo, terBaseLoginMemberCardVO);
    }

    /**
     * 订单绑定会员优惠券
     */
    private void bindOrderMemberCouponLink(TerLoginMemberCardQO terLoginMemberCardQO, HsaOperationMemberInfo hsaOperationMemberInfo) {
        if (StringUtils.isEmpty(terLoginMemberCardQO.getOrderGuid()) || Objects.isNull(hsaOperationMemberInfo)) {
            return;
        }
        // 查询
        List<HsaMemberCouponLink> memberCouponLinks = hsaMemberCouponLinkService.list(
                new LambdaQueryWrapper<HsaMemberCouponLink>()
                        .eq(HsaMemberCouponLink::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                        .eq(HsaMemberCouponLink::getOrderGuid, terLoginMemberCardQO.getOrderGuid())
                        .isNull(HsaMemberCouponLink::getMemberGuid));
        if (CollectionUtils.isEmpty(memberCouponLinks)) {
            return;
        }
        log.info("当前订单上不记名优惠券:{}", JacksonUtils.writeValueAsString(memberCouponLinks));
        memberCouponLinks.forEach(e -> e.setMemberGuid(hsaOperationMemberInfo.getGuid())
                .setUserName(hsaOperationMemberInfo.getUserName())
                .setMemberPhone(hsaOperationMemberInfo.getPhoneNum()));
        hsaMemberCouponLinkService.updateBatchById(memberCouponLinks);
        // 兑换码的优惠券
        List<HsaMemberCouponLink> redeemCouponLinks = memberCouponLinks.stream()
                .filter(e -> Objects.equals(CouponPackageTypeEnum.COUPON_REDEEM_ACTIVITY.getCode(), e.getCouponPackageType()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(redeemCouponLinks)) {
            // 处理关联关系
            List<String> redeemDtlGuids = redeemCouponLinks.stream()
                    .map(HsaMemberCouponLink::getDtlGuid)
                    .distinct()
                    .collect(Collectors.toList());
            RequestUpdateDtlQO requestUpdateDtlQO = new RequestUpdateDtlQO();
            requestUpdateDtlQO.setGuids(redeemDtlGuids);
            requestUpdateDtlQO.setMemberGuid(hsaOperationMemberInfo.getGuid());
            requestUpdateDtlQO.setMemberName(hsaOperationMemberInfo.getUserName());
            requestUpdateDtlQO.setMemberPhone(hsaOperationMemberInfo.getPhoneNum());
            memberMarketingFeign.batchRedeemCodeMemberInfo(requestUpdateDtlQO);
        }

    }

    private void setMemberIntegral(HsaOperationMemberInfo hsaOperationMemberInfo, TerBaseLoginMemberCardVO terBaseLoginMemberCardVO) {
        if (Objects.nonNull(hsaOperationMemberInfo)) {
            int usableIntegral = getUsableIntegral(hsaOperationMemberInfo.getGuid());

            terBaseLoginMemberCardVO.setMemberIntegral(usableIntegral);

            //冻结积分
            Integer freezeIntegral = hsaIntegralDetailMapper.sumIntegralProtectDateIntegral(
                    hsaOperationMemberInfo.getGuid(),
                    hsaOperationMemberInfo.getOperSubjectGuid(),
                    ConsumptionOrderStateEnum.REFUND.getDes());
            terBaseLoginMemberCardVO.setFreezeIntegral(freezeIntegral);
        }
    }


    private void prepositionCheck(TerLoginMemberCardQO terLoginMemberCardQO) {
        if (StringUtils.isNotEmpty(terLoginMemberCardQO.getLoginNum())) {
            String cardNum = getCardNumByUid(terLoginMemberCardQO.getLoginNum());
            if (StringUtils.isNotEmpty(cardNum)) {
                terLoginMemberCardQO.setLoginNum(cardNum);
            }
        } else {
            dealMemberInfoGuid(terLoginMemberCardQO);
        }
    }

    private HsaOperationMemberInfo getHsaOperationMemberInfo(TerLoginMemberCardQO terLoginMemberCardQO, String phoneNum, HeaderUserInfo headerUserInfo) {
        return hsaOperationMemberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .eq(HsaOperationMemberInfo::getPhoneNum, phoneNum)
                .eq(HsaOperationMemberInfo::getPhoneCountryCode, terLoginMemberCardQO.getPhoneCountryCode())
                .eq(HsaOperationMemberInfo::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid()));
    }

    private HsaMemberInfoCard getMemberInfoCard(TerLoginMemberCardQO terLoginMemberCardQO, String loginNum, HeaderUserInfo headerUserInfo) {
        HsaMemberInfoCard hsaMemberInfoCard;
        //会员卡二维码查询
        if (loginNum.contains(QrcodeTypeEnum.MEMBER.getDes())) {
            hsaMemberInfoCard = getHsaMemberInfoCard(terLoginMemberCardQO, loginNum, headerUserInfo);

            setTerminalCheckStatus(terLoginMemberCardQO, TerminalCheckStatusEnum.SCAN_QR_CODES_PAY);
        } else {
            //会员卡号查询
            HsaPhysicalCard hsaPhysicalCard = hsaPhysicalCardMapper.selectOne(new LambdaQueryWrapper<HsaPhysicalCard>()
                    .eq(HsaPhysicalCard::getCardNum, loginNum)
                    .eq(HsaPhysicalCard::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid()));

            hsaMemberInfoCard = getHsaMemberInfoCard(terLoginMemberCardQO, headerUserInfo, loginNum, hsaPhysicalCard);

            setTerminalCheckStatus(terLoginMemberCardQO, TerminalCheckStatusEnum.CARD_NUM_PAY);
        }
        return hsaMemberInfoCard;
    }

    private static void setTerminalCheckStatus(TerLoginMemberCardQO terLoginMemberCardQO, TerminalCheckStatusEnum cardNumPay) {
        if (Objects.isNull(terLoginMemberCardQO.getTerminalCheckStatus())) {
            terLoginMemberCardQO.setTerminalCheckStatus(cardNumPay.getCode());
        }
    }

    private HsaMemberInfoCard getHsaMemberInfoCard(TerLoginMemberCardQO terLoginMemberCardQO, String loginNum, HeaderUserInfo headerUserInfo) {
        HsaElectronicCard hsaElectronicCard;
        HsaMemberInfoCard hsaMemberInfoCard;
        log.info("会员二维码扫码，二维码key=======>{}", loginNum);
        Object content = redisTemplate.opsForValue().get(loginNum);
        checkContent(content);
        loginNum = content.toString();
        log.info("会员二维码扫码，二维码卡号======>{}", loginNum);
        hsaElectronicCard = getHsaElectronicCard(headerUserInfo, loginNum);
        hsaMemberInfoCard = hsaMemberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getElectronicCardNum, hsaElectronicCard.getCardNum())
                .eq(HsaMemberInfoCard::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid()));
        checkBusinessService.checkMemberCardLogin(hsaMemberInfoCard, hsaElectronicCard.getCardState(), terLoginMemberCardQO, NumberConstant.NUMBER_1);
        return hsaMemberInfoCard;
    }

    private void dealMemberInfoGuid(TerLoginMemberCardQO terLoginMemberCardQO) {
        if (StringUtils.isNotEmpty(terLoginMemberCardQO.getMemberInfoGuid())) {
            HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(terLoginMemberCardQO.getMemberInfoGuid());
            checkBusinessService.checkMemberInfo(hsaOperationMemberInfo);
            terLoginMemberCardQO.setLoginNum(hsaOperationMemberInfo.getPhoneNum());

            terLoginMemberCardQO.setPhoneCountryCode(hsaOperationMemberInfo.getPhoneCountryCode());
        }
    }

    private void setGradeIcon(HsaOperationMemberInfo hsaOperationMemberInfo, TerBaseLoginMemberCardVO terBaseLoginMemberCardVO) {
        if (Objects.nonNull(hsaOperationMemberInfo) && StringUtils.isNotBlank(hsaOperationMemberInfo.getMemberGradeInfoGuid())) {
            HsaMemberGradeInfo hsaMemberGradeInfo = hsaMemberGradeInfoMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                    .eq(HsaMemberGradeInfo::getGuid, hsaOperationMemberInfo.getMemberGradeInfoGuid())
                    .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                    .eq(HsaMemberGradeInfo::getEffective, BooleanEnum.TRUE.getCode()));
            terBaseLoginMemberCardVO.setGradeIcon(hsaMemberGradeInfo.getGradeIcon());
        }
    }

    @Override
    public List<MemberFaceVO> queryMemberByFace(MemberFaceQO memberFaceQO) {
        if (Objects.isNull(memberFaceQO.getUserId())) {
            return Collections.emptyList();
        }
        final String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .eq(HsaOperationMemberInfo::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaOperationMemberInfo::getUserId, memberFaceQO.getUserId()));
        if (Objects.nonNull(hsaOperationMemberInfo)) {
            List<MemberFaceVO> faceVOList = new ArrayList<>();
            //会员人脸
            MemberFaceVO faceVO = MemberAssembler.toMemberFaceVO(hsaOperationMemberInfo);
            faceVOList.add(faceVO);
            //相同手机号的亲属
            final List<MemberFaceVO> kinsfolkMemberList = getKinsfolkMemberList(hsaOperationMemberInfo.getPhoneNum());
            Optional.ofNullable(kinsfolkMemberList).ifPresent(list -> {
                //重复
                list.removeIf(l -> l.getMemberInfoGuid().equals(faceVO.getMemberInfoGuid()));
            });
            faceVOList.addAll(kinsfolkMemberList);
            return faceVOList;
        }
        return getMemberFaceByKinsfolk(memberFaceQO);
    }

    private List<MemberFaceVO> getMemberFaceByKinsfolk(MemberFaceQO memberFaceQO) {
        List<MemberFaceVO> faceVOList = new ArrayList<>();
        //亲属人脸
        final KinsfolkManagementVO kinsfolkManagementVO = hsaKinsfolkManagementService.getByFaceUserId(memberFaceQO.getUserId());
        if (Objects.nonNull(kinsfolkManagementVO)) {

            HsaOperationMemberInfo kinsfolkMember = hsaOperationMemberInfoMapper.queryByGuid(kinsfolkManagementVO.getMemberInfoGuid());
            final MemberFaceVO memberFaceVO = MemberAssembler.toMemberFaceVO(kinsfolkMember);
            memberFaceVO.setKinsfolkManagement(kinsfolkManagementVO);
            faceVOList.add(memberFaceVO);

            //相同手机号的亲属
            if (StringUtils.isBlank(kinsfolkManagementVO.getPhoneNum())) {
                return faceVOList;
            }
            //会员重复
            final List<MemberFaceVO> kinsfolkMemberList = getKinsfolkMemberList(kinsfolkManagementVO.getPhoneNum());
            Optional.ofNullable(kinsfolkMemberList).ifPresent(list -> {
                //重复
                list.removeIf(l -> l.getMemberInfoGuid().equals(memberFaceVO.getMemberInfoGuid()));
            });
            faceVOList.addAll(kinsfolkMemberList);

            final List<String> exculdGuids = faceVOList.stream().map(MemberFaceVO::getMemberInfoGuid).collect(Collectors.toList());

            //相同手机号的会员
            HsaOperationMemberInfo phoneMember = hsaOperationMemberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                    .eq(HsaOperationMemberInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                    .eq(HsaOperationMemberInfo::getPhoneCountryCode, StringConstant.STR_DEFAULT_PHONE_COUNTRY_CODE)
                    .eq(HsaOperationMemberInfo::getPhoneNum, kinsfolkManagementVO.getPhoneNum())
                    .notIn(HsaOperationMemberInfo::getGuid, exculdGuids)
            );
            Optional.ofNullable(phoneMember).ifPresent(m -> {
                //会员人脸
                MemberFaceVO faceVO = MemberAssembler.toMemberFaceVO(m);
                faceVOList.add(faceVO);
            });
        }
        return faceVOList;
    }

    /**
     * 手机号查询亲属
     *
     * @param phone
     * @return
     */
    private List<MemberFaceVO> getKinsfolkMemberList(String phone) {
        if (StringUtils.isBlank(phone)) {
            return Collections.emptyList();
        }
        final List<KinsfolkManagementVO> managementVOList = hsaKinsfolkManagementService.listByPhone(phone);
        if (CollectionUtils.isEmpty(managementVOList)) {
            return Collections.emptyList();
        }
        final List<String> memberInGuids = managementVOList.stream().map(KinsfolkManagementVO::getMemberInfoGuid).collect(Collectors.toList());
        final List<HsaOperationMemberInfo> memberInfoList = hsaOperationMemberInfoMapper.queryByGuids(memberInGuids);
        if (CollectionUtils.isEmpty(memberInfoList)) {
            return Collections.emptyList();
        }
        //会员人脸
        final List<MemberFaceVO> memberFaceVOs = MemberAssembler.toMemberFaceVOs(memberInfoList);
        for (MemberFaceVO memberFaceVO : memberFaceVOs) {
            final String memberInfoGuid = memberFaceVO.getMemberInfoGuid();
            //todo 多个
            final List<KinsfolkManagementVO> managementVOS = managementVOList.stream().filter(m -> m.getMemberInfoGuid().equals(memberInfoGuid)).collect(Collectors.toList());
            memberFaceVO.setKinsfolkManagement(managementVOS.get(0));
        }
        return memberFaceVOs;
    }


    private List<TerLoginMemberCardVO> dealFreezeAmount(TerLoginMemberCardQO terLoginMemberCardQO, List<TerLoginMemberCardVO> terLoginMemberCardListVOS) {
        if (terLoginMemberCardQO.getType() == 0
                && CollUtil.isNotEmpty(terLoginMemberCardListVOS)) {
            for (TerLoginMemberCardVO terLoginMemberCardListVO : terLoginMemberCardListVOS) {
                CardFreezeBalanceAmountQO amountQO = new CardFreezeBalanceAmountQO();
                amountQO.setIsRefresh(BooleanEnum.FALSE.getCode());
                amountQO.setMemberInfoCardGuid(terLoginMemberCardListVO.getMemberInfoCardGuid());
                BigDecimal freezeAmount = hsaCardRechargeGiftDetailService.getMemberCardFreezeAmount(amountQO);
                terLoginMemberCardListVO.setFreezeAmount(freezeAmount);
            }
        }
        return terLoginMemberCardListVOS;
    }

    private HsaMemberInfoCard getHsaMemberInfoCard(TerLoginMemberCardQO terLoginMemberCardQO,
                                                   HeaderUserInfo headerUserInfo,
                                                   String loginNum,
                                                   HsaPhysicalCard hsaPhysicalCard) {
        HsaMemberInfoCard hsaMemberInfoCard;
        if (Objects.isNull(hsaPhysicalCard)) {
            HsaElectronicCard electronicCard = getHsaElectronicCard(headerUserInfo, loginNum);
            hsaMemberInfoCard = hsaMemberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                    .eq(HsaMemberInfoCard::getElectronicCardNum, electronicCard.getCardNum())
                    .eq(HsaMemberInfoCard::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid()));
            checkBusinessService.checkMemberCardLogin(hsaMemberInfoCard, electronicCard.getCardState(), terLoginMemberCardQO, NumberConstant.NUMBER_1);
        } else {
            hsaMemberInfoCard = hsaMemberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                    .eq(HsaMemberInfoCard::getPhysicalCardNum, hsaPhysicalCard.getCardNum())
                    .eq(HsaMemberInfoCard::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid()));
            checkBusinessService.checkMemberCardLogin(hsaMemberInfoCard, hsaPhysicalCard.getCardState(), terLoginMemberCardQO, NumberConstant.NUMBER_0);
        }
        return hsaMemberInfoCard;
    }

    private void checkContent(Object content) {
        if (null == content) {
            throw new MemberBaseException(CardOperationExceptionEnum.MEMBER_CARD_INVALID);
        }
    }

    private List<HsaMemberInfoCard> getHsaMemberInfoCards(TerBaseLoginMemberCardVO terBaseLoginMemberCardVO,
                                                          HsaMemberInfoCard hsaMemberInfoCard,
                                                          List<HsaMemberInfoCard> hsaMemberInfoCardList,
                                                          HsaOperationMemberInfo memberInfo) {
        if (Objects.nonNull(memberInfo)) {
            hsaMemberInfoCardList = hsaMemberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                    .in(HsaMemberInfoCard::getMemberInfoGuid, memberInfo.getGuid())
                    .notIn(HsaMemberInfoCard::getGuid, hsaMemberInfoCard.getGuid())
                    .and(wq -> wq.eq(HsaMemberInfoCard::getPhysicalCardState, 1).or().eq(HsaMemberInfoCard::getElectronicCardState, 1))
            );
            BeanUtils.copyProperties(memberInfo, terBaseLoginMemberCardVO);
            terBaseLoginMemberCardVO.setAccountState(memberInfo.getAccountState());
            terBaseLoginMemberCardVO.setMemberInfoGuid(memberInfo.getGuid());
        }
        return hsaMemberInfoCardList;
    }

    private static boolean isaCheckLoginNum(String loginNum) {
        return NumberUtil.isPhoneNum11(loginNum) || loginNum.contains(QrcodeTypeEnum.CREDIT.getDes());
    }

    private void getTerBaseLoginMemberCardVO(TerLoginMemberCardQO terLoginMemberCardQO,
                                             TerBaseLoginMemberCardVO terBaseLoginMemberCardVO,
                                             List<TerLoginMemberCardVO> terLoginMemberCardListVOList,
                                             HsaOperationMemberInfo hsaOperationMemberInfo,
                                             List<HsaMemberInfoCard> hsaMemberInfoCardList,
                                             HsaCardBalanceRule hsaCardBalanceRule) {
        if (CollUtil.isEmpty(hsaMemberInfoCardList) || Objects.isNull(hsaOperationMemberInfo)) {
            terBaseLoginMemberCardVO.setTerLoginMemberCardListVOS(terLoginMemberCardListVOList);
            return;
        }

        //校验会员持卡信息
        setMemberInfoCardData(terLoginMemberCardListVOList,
                hsaMemberInfoCardList,
                hsaOperationMemberInfo,
                terBaseLoginMemberCardVO,
                terLoginMemberCardQO,
                hsaCardBalanceRule);
    }

    private void checkUserId(TerLoginMemberCardQO terLoginMemberCardQO, TerBaseLoginMemberCardVO terBaseLoginMemberCardVO) {
        if (StringUtils.isNotBlank(terLoginMemberCardQO.getUserId())) {
            //这里查询不加主体，百度是随机返回的（拿到手机号再去按主体+手机号）
            HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                    .eq(HsaOperationMemberInfo::getUserId, terLoginMemberCardQO.getUserId())
            );
            if (terLoginMemberCardQO.getUserType() == BooleanEnum.TRUE.getCode()) {
                final KinsfolkManagementVO kinsfolkManagementVO = hsaKinsfolkManagementService.getByFaceUserId(terLoginMemberCardQO.getUserId());
                if (Objects.nonNull(kinsfolkManagementVO)) {
                    hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(kinsfolkManagementVO.getMemberInfoGuid());
                    terBaseLoginMemberCardVO.setKinsfolkManagement(kinsfolkManagementVO);
                }
            }
            if (Objects.isNull(hsaOperationMemberInfo)) {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_MEMBER);
            }
            terLoginMemberCardQO.setLoginNum(hsaOperationMemberInfo.getPhoneNum());
        }
    }

    private HsaElectronicCard getHsaElectronicCard(HeaderUserInfo headerUserInfo, String loginNum) {
        HsaElectronicCard hsaElectronicCard = hsaElectronicCardMapper.selectOne(new LambdaQueryWrapper<HsaElectronicCard>()
                .eq(HsaElectronicCard::getCardNum, loginNum)
                .eq(HsaElectronicCard::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid()));


        if (Objects.isNull(hsaElectronicCard)) {
            log.info("电子卡无效");
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_MEMBER);
        }

        return hsaElectronicCard;
    }

    private void setMemberInfoCardData(List<TerLoginMemberCardVO> terLoginMemberCardListVOList,
                                       List<HsaMemberInfoCard> hsaMemberInfoCardList,
                                       HsaOperationMemberInfo hsaOperationMemberInfo,
                                       TerBaseLoginMemberCardVO terBaseLoginMemberCardVO,
                                       TerLoginMemberCardQO terLoginMemberCardQO,
                                       HsaCardBalanceRule hsaCardBalanceRule) {
        //用户下的卡数据
        if (CollUtil.isNotEmpty(hsaMemberInfoCardList)) {
            TerCheckMemberCardQO terCheckMemberCardQO = new TerCheckMemberCardQO();
            BeanUtils.copyProperties(terLoginMemberCardQO, terCheckMemberCardQO);
            log.info("批量校验会员卡={}", JSON.toJSONString(terCheckMemberCardQO));
            //批量校验会员卡
            List<TerLoginMemberCardVO> cardVOS = terMemberCardBusinessService.doCheckMemberInfoCardList(
                    hsaMemberInfoCardList,
                    hsaOperationMemberInfo,
                    hsaCardBalanceRule,
                    terCheckMemberCardQO);

            terLoginMemberCardListVOList.addAll(cardVOS);

            terBaseLoginMemberCardVO.setTerLoginMemberCardListVOS(terLoginMemberCardListVOList);
        } else {
            terBaseLoginMemberCardVO.setTerLoginMemberCardListVOS(terLoginMemberCardListVOList);
        }
    }


    private void setLoginMemberCardInfo(List<TerLoginMemberCardVO> terLoginMemberCardListVOList,
                                        HsaMemberInfoCard hsaMemberInfoCard,
                                        TerLoginMemberCardQO terLoginMemberCardQO,
                                        HsaOperationMemberInfo hsaOperationMemberInfo,
                                        HsaCardBalanceRule hsaCardBalanceRule) {
        HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(hsaMemberInfoCard.getCardGuid());

        //设置单卡参数
        TerLoginMemberCardVO vo = MemberCardAssembler.setTerLoginMemberCardVO(
                hsaMemberInfoCard,
                hsaOperationMemberInfo,
                hsaCardBaseInfo);

        //支付密码
        Integer isCheckPassword = terMemberCardBusinessService.getCheckPassword(hsaCardBalanceRule,
                hsaCardBaseInfo,
                terLoginMemberCardQO.getTerminalCheckStatus());
        vo.setIsCheckPassword(isCheckPassword);
        List<StoreCardRuleVO> saveStoreCardRuleQOList = terMemberCardBusinessService.getStoreCardRuleVOS(hsaMemberInfoCard);
        vo.setStoreCardRuleVO(saveStoreCardRuleQOList);
        terLoginMemberCardListVOList.add(vo);
    }

    /**
     * 会员卡充值
     *
     * @param terMemberCardRechargeQO terMemberCardRechargeQO
     * @return RechargeRespVO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MemberCardRechargeNewDTO memberCardRecharge(TerMemberCardRechargeQO terMemberCardRechargeQO) {
        log.info("memberCardRecharge====>{}", JSON.toJSON(terMemberCardRechargeQO));
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        HsaMemberInfoCard hsaMemberInfoCard;
        RLock lock = redissonClient.getLock("CARD_RECHARGE:" + terMemberCardRechargeQO.getMemberInfoCardGuid());
        try {
            if (!lock.tryLock(NumberConstant.NUMBER_30, NumberConstant.NUMBER_30, TimeUnit.SECONDS)) {
                throw new MemberBaseException("会员卡现金充值锁超时：" + terMemberCardRechargeQO.getMemberInfoCardGuid());
            }
            hsaMemberInfoCard = terMemberCardBusinessService.getHsaMemberInfoCard(terMemberCardRechargeQO, headerUserInfo);
            HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(hsaMemberInfoCard.getCardGuid());
            HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(hsaMemberInfoCard.getMemberInfoGuid());

            String guid = guidGeneratorUtil.getStringGuid(HsaMemberFundingDetail.class.getSimpleName());
            terMemberCardRechargeQO.setMemberFundingDetailGuid(guid);
            String orderNum = guidGeneratorUtil.getStringGuid(HsaMemberConsumption.class.getSimpleName());
            String consumptionGuid = guidGeneratorUtil.getStringGuid(HsaMemberConsumption.class.getSimpleName());
            headerUserInfo.setEnterpriseGuid(hsaCardBaseInfo.getEnterpriseGuid());
            ThreadLocalCache.put(JSON.toJSONString(headerUserInfo));
            terMemberCardRechargeQO.setConsumptionGuid(consumptionGuid);
            //充值赠送
            RechargeOrderGiftSummaryVO rechargeOrderGiftSummaryVO = hsaCardRechargeGiftDetailService
                    .dealRechargeGift(MerchantPayAssembler.rechargeAfterHandle(hsaOperationMemberInfo, terMemberCardRechargeQO, hsaMemberInfoCard, orderNum));

            log.info("处理充值赠送返参：{}", JSON.toJSONString(rechargeOrderGiftSummaryVO));
            //订单赠送总金额 只作记录
            BigDecimal giftAmount = addGiftAmount(rechargeOrderGiftSummaryVO);
            BigDecimal giftEffectuateTotalAmount = rechargeOrderGiftSummaryVO.getGiftEffectuateTotalAmount();

            log.info("充值最终实充金额：{}", JSON.toJSONString(terMemberCardRechargeQO.getRechargeMoney()));


            //恢复超额以及卡金额更新
            terMemberCardRechargeQO.setGiftAmount(giftEffectuateTotalAmount);
            excessProcessor(terMemberCardRechargeQO, hsaMemberInfoCard, hsaCardBaseInfo);
            boolean rb = hsaMemberInfoCardMapper.updateByGuid(hsaMemberInfoCard);
            log.info("会员卡充值结果：{}, {}", JSON.toJSONString(hsaMemberInfoCard), rb);

            //消费记录
            HsaMemberConsumption hsaMemberConsumption = getMemberRechargeConsumption(headerUserInfo, terMemberCardRechargeQO, hsaMemberInfoCard);
            hsaMemberConsumption.setGuid(consumptionGuid);
            hsaMemberConsumption.setOrderNumber(orderNum);
            hsaMemberConsumption.setDeviceId(terMemberCardRechargeQO.getDevice_id());
            if (Objects.nonNull(hsaOperationMemberInfo)) {
                hsaMemberConsumption.setWorkName(hsaOperationMemberInfo.getWorkName());
                hsaMemberConsumption.setDepartmentName(hsaOperationMemberInfo.getDepartmentName());
            }
            //资金来往明细
            HsaMemberFundingDetail hsaMemberFundingDetail = MerchantPayAssembler.fromRechargeDetail(hsaMemberInfoCard, terMemberCardRechargeQO, hsaMemberConsumption);
            hsaMemberFundingDetail.setGuid(guid);

            //赠送余额需算上以往冻结金额
            setCardGiftResidualBalance(hsaMemberInfoCard, hsaMemberFundingDetail);
            hsaMemberFundingDetail.setGiftAmount(giftAmount);
            //充值支付方式
            HsaMemberConsumptionPayWay payWay = getRechargePayWay(terMemberCardRechargeQO, hsaMemberConsumption.getGuid());

            terMemberCardRechargeQO.setCardGuid(hsaMemberInfoCard.getCardGuid());
            terMemberCardRechargeQO.setCardName(hsaMemberInfoCard.getCardName());
            hsaMemberConsumptionMapper.insert(hsaMemberConsumption);
            hsaMemberFundingDetailMapper.insert(hsaMemberFundingDetail);
            hsaMemberConsumptionPayWayMapper.insert(payWay);

            //活动统计记录
            rechargeOrderGiftSummaryVO.setStoreId(terMemberCardRechargeQO.getStoreId());

            final String useStr = ThreadLocalCache.get();
            //写入活动统计记录
            memberBaseThreadExecutor.execute(() -> {
                ThreadLocalCache.put(useStr);

                terMemberCardBusinessService.saveGiftAmountRecord(hsaMemberConsumption,
                        hsaMemberInfoCard,
                        hsaOperationMemberInfo,
                        rechargeOrderGiftSummaryVO,
                        hsaMemberFundingDetail,
                        payWay);
            });

            terMemberCardRechargeQO.setRechargeOrderGiftSummaryVO(rechargeOrderGiftSummaryVO);
            return new MemberCardRechargeNewDTO(
                    terMemberCardRechargeQO,
                    headerUserInfo,
                    hsaMemberConsumption,
                    hsaMemberInfoCard,
                    payWay,
                    hsaMemberFundingDetail,
                    hsaOperationMemberInfo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new MemberBaseException(e.getMessage());
        } finally {
            lock.unlock();
        }
    }

    public void sendCoupon(RechargeOrderGiftSummaryVO rechargeOrderGiftSummaryVO,
                           HsaOperationMemberInfo hsaOperationMemberInfo,
                           HsaMemberConsumption hsaMemberConsumption) {
        if (CollUtil.isNotEmpty(rechargeOrderGiftSummaryVO.getCouponGuidList())) {

            MemberSendCouponQO memberSendCouponQO = getMemberSendCouponQO(
                    rechargeOrderGiftSummaryVO,
                    hsaOperationMemberInfo,
                    hsaMemberConsumption);
            log.info("充值赠送券: {}", JSON.toJSONString(memberSendCouponQO));
            List<HsaMemberCouponLink> hsaMemberCouponLinks = assembler.formMemberCouponLink(memberSendCouponQO);
            if (CollUtil.isNotEmpty(hsaMemberCouponLinks)) {
                memberBaseThreadExecutor.execute(() -> hsaMemberCouponLinkService.saveBatch(hsaMemberCouponLinks));

                List<MemberCouponPackageVO> memberCouponPackageVOS = Lists.newArrayList();
                for (HsaMemberCouponLink hsaMemberCouponLink : hsaMemberCouponLinks) {
                    MemberCouponPackageVO memberCouponPackageVO = assembler.getMemberCouponPackageVO(hsaMemberCouponLink, memberSendCouponQO);
                    memberCouponPackageVOS.add(memberCouponPackageVO);
                }
                sendService.sendMemberCouponNotice(memberCouponPackageVOS);
            }
        }
    }

    private static MemberSendCouponQO getMemberSendCouponQO(RechargeOrderGiftSummaryVO rechargeOrderGiftSummaryVO,
                                                            HsaOperationMemberInfo hsaOperationMemberInfo,
                                                            HsaMemberConsumption hsaMemberConsumption) {
        MemberSendCouponQO memberSendCouponQO = new MemberSendCouponQO();
        memberSendCouponQO.setCouponPackageType(CouponPackageTypeEnum.RECHARGE_GIFT.getCode());
        memberSendCouponQO.setCouponPackageCode(rechargeOrderGiftSummaryVO.getActivityCode());
        memberSendCouponQO.setActivityName(rechargeOrderGiftSummaryVO.getActivityName());
        memberSendCouponQO.setCouponGuidList(rechargeOrderGiftSummaryVO.getCouponGuidList());
        memberSendCouponQO.setStoreName(hsaMemberConsumption.getStoreName());
        memberSendCouponQO.setConsumptionGuid(hsaMemberConsumption.getGuid());
        if (Objects.nonNull(hsaOperationMemberInfo)) {
            memberSendCouponQO.setMemberPhone(hsaOperationMemberInfo.getPhoneNum());
            memberSendCouponQO.setMemberGuid(hsaOperationMemberInfo.getGuid());
            memberSendCouponQO.setMemberName(hsaOperationMemberInfo.getUserName());
        }
        return memberSendCouponQO;
    }


    private void sendRechargeIntegralEvent(HeaderUserInfo header, HsaMemberInfoCard hsaMemberInfoCard, HsaMemberConsumption hsaMemberConsumption) {
        if (StringUtils.isEmpty(hsaMemberInfoCard.getMemberInfoGuid())) {
            return;
        }
        IntegralChangeDTO event = MemberCardAssembler.getIntegralChangeDTO(header, hsaMemberInfoCard, hsaMemberConsumption);
        integralTaskProcessorService.rechargeTaskBusinessProcessor(event);
        log.info("积分充值event参数" + JSON.toJSONString(event));
    }

    private void receiptPrinting(TerMemberCardRechargeQO terMemberCardRechargeQO, HsaOperationMemberInfo hsaOperationMemberInfo, HsaMemberConsumption hsaMemberConsumption, RechargeRespVO rechargeRespVO) {
        ReceiptPrintingBaseDTO baseDTO = new ReceiptPrintingBaseDTO();
        baseDTO.setStore_id(Integer.parseInt(terMemberCardRechargeQO.getStoreGuid()));
        baseDTO.setDevice_id(terMemberCardRechargeQO.getDevice_id());
        RechargeOrderGiftSummaryVO rechargeOrderGiftSummaryVO = terMemberCardRechargeQO.getRechargeOrderGiftSummaryVO();
        BigDecimal res = rechargeOrderGiftSummaryVO.getGiftEffectuateTotalAmount().add(rechargeOrderGiftSummaryVO.getGiftFixedTotalAmount());
        PrintingDTO printingDTO = MemberCardAssembler.getPrintingDTO(terMemberCardRechargeQO, hsaOperationMemberInfo, hsaMemberConsumption, rechargeRespVO, res);
        baseDTO.setPrint_data(printingDTO);
        log.info("现金支付小票打印请求参数:{}", JSON.toJSON(baseDTO));
        externalSupport.storeServer(ThreadLocalCache.getSystem()).getReceiptPrinting(baseDTO);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public RechargeRespVO rechargePreOrder(TerMemberCardRechargeQO terMemberCardRechargeQO) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        log.info("headerUserInfo----->{}", JSON.toJSONString(headerUserInfo));
        log.info("会员卡充值预下单请求参数====>{}", JSON.toJSON(terMemberCardRechargeQO));
        RLock lock = redissonClient.getLock(RedisKeyConstant.CARD_RECHARGE_NEW + terMemberCardRechargeQO.getMemberInfoCardGuid());
        try {
            if (!lock.tryLock(NumberConstant.NUMBER_30, NumberConstant.NUMBER_30, TimeUnit.SECONDS)) {
                throw new MemberBaseException(systemRoleHelper.getReplace("会员卡充值预下单请求参数", ThreadLocalCache.getOperSubjectGuid()) + terMemberCardRechargeQO.getMemberInfoCardGuid());
            }
            HsaMemberInfoCard hsaMemberInfoCard = terMemberCardBusinessService.getHsaMemberInfoCard(terMemberCardRechargeQO, headerUserInfo);
            //消费记录
            HsaMemberConsumption hsaMemberConsumption = getMemberRechargeConsumption(headerUserInfo, terMemberCardRechargeQO, hsaMemberInfoCard);
            hsaMemberConsumption.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberConsumption.class.getSimpleName()));
            hsaMemberConsumption.setOrderNumber(guidGeneratorUtil.getStringGuid(HsaMemberConsumption.class.getSimpleName()));
            hsaMemberConsumption.setIsComplete(NUMBER_0);
            if (headerUserInfo.getSource() == SourceTypeEnum.ADD_ONE_MACHINE.getCode()) {
                hsaMemberConsumption.setDeviceId(terMemberCardRechargeQO.getDevice_id());
            }
            //充值支付方式
            HsaMemberConsumptionPayWay payWay = getRechargePayWay(terMemberCardRechargeQO, hsaMemberConsumption.getGuid());
            hsaMemberConsumptionPayWayMapper.insert(payWay);
            hsaMemberConsumptionMapper.insert(hsaMemberConsumption);
            log.info("生成订单号====>{}", JSON.toJSON(hsaMemberConsumption.getOrderNumber()));
            return new RechargeRespVO().setOrderNumber(hsaMemberConsumption.getOrderNumber());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new MemberBaseException(((MemberBaseException) e).getCode(), ((MemberBaseException) e).getDes());
        } finally {
            lock.unlock();
        }
    }

    /**
     * 会员卡充值
     *
     * @param terMemberCardRechargeQO terMemberCardRechargeQO
     * @return RechargeRespVO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MemberCardRechargeNewDTO memberCardRechargeNew(TerMemberCardRechargeQO terMemberCardRechargeQO) {
        if (StringUtils.isEmpty(terMemberCardRechargeQO.getOrderNumber())) {
            throw new MemberBaseException("会员订单不存在");
        }
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        log.info("headerUserInfo----->{}", JSON.toJSONString(headerUserInfo));
        log.info("会员卡充值回调请求参数====>{}", JSON.toJSON(terMemberCardRechargeQO));
        // 会员充值回调请求参数 补偿
        fillMemberCardRechargeCallbackRequest(terMemberCardRechargeQO);
        RLock lock = redissonClient.getLock(RedisKeyConstant.CARD_RECHARGE_NEW + terMemberCardRechargeQO.getMemberInfoCardGuid());
        try {
            if (!lock.tryLock(NumberConstant.NUMBER_30, NumberConstant.NUMBER_30, TimeUnit.SECONDS)) {
                throw new MemberBaseException("会员卡充值预下单请求参数：" + terMemberCardRechargeQO.getMemberInfoCardGuid());
            }
            HsaMemberConsumption hsaMemberConsumption = hsaMemberConsumptionMapper.selectOne(new LambdaQueryWrapper<HsaMemberConsumption>()
                    .eq(HsaMemberConsumption::getOrderNumber, terMemberCardRechargeQO.getOrderNumber())
                    .eq(HsaMemberConsumption::getConsumptionType, NumberConstant.NUMBER_0));
            if (Objects.isNull(hsaMemberConsumption)) {
                log.info("会员订单不存在====>{}", terMemberCardRechargeQO.getOrderNumber());
                throw new MemberBaseException("会员订单不存在");
            }
            if (hsaMemberConsumption.getIsComplete() == NumberConstant.NUMBER_1) {
                log.error("会员订单充值已完成====>{}", JSON.toJSON(terMemberCardRechargeQO.getOrderNumber()));
                return new MemberCardRechargeNewDTO();
            }
            terMemberCardRechargeQO.setStoreGuid(hsaMemberConsumption.getStoreGuid());
            terMemberCardRechargeQO.setStoreName(hsaMemberConsumption.getStoreName());
            terMemberCardRechargeQO.setOrderSource(hsaMemberConsumption.getOrderSource());
            //查询加锁
            HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMapper.queryByGuidForUpdate(hsaMemberConsumption.getMemberInfoCardGuid());
            HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(hsaMemberInfoCard.getMemberInfoGuid());

            //解决小程序充值，没有运营主体问题
            if (StringUtils.isEmpty(headerUserInfo.getOperSubjectGuid())) {
                headerUserInfo.setOperSubjectGuid(hsaMemberInfoCard.getOperSubjectGuid());
                HeaderUserInfo headerUserInfoVo = Optional.of(headerUserInfo).orElse(new HeaderUserInfo());
                headerUserInfoVo.setOperSubjectGuid(hsaMemberInfoCard.getOperSubjectGuid());
                final String jsonString = JSON.toJSONString(headerUserInfoVo);
                ThreadLocalCache.put(jsonString);
                log.info("ThreadLocalCache.getHeaderUserInfo:{}", jsonString);
            }

            String guid = guidGeneratorUtil.getStringGuid(HsaMemberFundingDetail.class.getSimpleName());
            terMemberCardRechargeQO.setMemberFundingDetailGuid(guid);
            headerUserInfo.setEnterpriseGuid(hsaMemberInfoCard.getEnterpriseGuid());
            headerUserInfo.setSource(hsaMemberConsumption.getOrderSource());
            ThreadLocalCache.put(JSON.toJSONString(headerUserInfo));
            terMemberCardRechargeQO.setConsumptionGuid(hsaMemberConsumption.getGuid());
            //充值赠送
            RechargeOrderGiftSummaryVO rechargeOrderGiftSummaryVO = hsaCardRechargeGiftDetailService
                    .dealRechargeGift(MerchantPayAssembler.rechargeAfterHandle(hsaOperationMemberInfo,
                            terMemberCardRechargeQO,
                            hsaMemberInfoCard,
                            terMemberCardRechargeQO.getOrderNumber()));
            log.debug("处理充值赠送返参：{}", JSON.toJSONString(rechargeOrderGiftSummaryVO));
            //订单赠送总金额 只作记录
            BigDecimal giftAmount = addGiftAmount(rechargeOrderGiftSummaryVO);
            BigDecimal giftEffectuateTotalAmount = rechargeOrderGiftSummaryVO.getGiftEffectuateTotalAmount();

            HsaMemberConsumptionPayWay hsaMemberConsumptionPayWay = hsaMemberConsumptionPayWayMapper.selectOne(new LambdaQueryWrapper<HsaMemberConsumptionPayWay>()
                    .eq(HsaMemberConsumptionPayWay::getConsumptionGuid, hsaMemberConsumption.getGuid()));
            HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(hsaMemberInfoCard.getCardGuid());
            //恢复超额及次数
            terMemberCardRechargeQO.setGiftAmount(giftEffectuateTotalAmount);
            excessProcessor(terMemberCardRechargeQO, hsaMemberInfoCard, hsaCardBaseInfo);


            hsaMemberConsumption.setIsComplete(NumberConstant.NUMBER_1);
            hsaMemberConsumption.setOrderGuid(terMemberCardRechargeQO.getPay_number());

            if (Objects.nonNull(hsaOperationMemberInfo)) {
                hsaMemberConsumption.setWorkName(hsaOperationMemberInfo.getWorkName())
                        .setDepartmentName(hsaOperationMemberInfo.getDepartmentName());
            }

            //资金来往明细
            HsaMemberFundingDetail hsaMemberFundingDetail = MerchantPayAssembler.fromRechargeDetail(hsaMemberInfoCard, terMemberCardRechargeQO, hsaMemberConsumption);
            hsaMemberFundingDetail.setGuid(guid);
            hsaMemberFundingDetail.setGiftAmount(giftAmount);

            //赠送余额需算上以往冻结金额
            setCardGiftResidualBalance(hsaMemberInfoCard, hsaMemberFundingDetail);
            hsaMemberInfoCard.setGmtModified(LocalDateTime.now());
            if (StringUtils.isNotBlank(terMemberCardRechargeQO.getStoreId())) {
                rechargeOrderGiftSummaryVO.setStoreId(terMemberCardRechargeQO.getStoreId());
            } else {
                rechargeOrderGiftSummaryVO.setStoreId(terMemberCardRechargeQO.getStoreGuid());
            }
            //整体事务
            transactionUtil.transactional((s) -> {
                hsaMemberConsumptionMapper.updateByGuid(hsaMemberConsumption);
                final boolean rb = hsaMemberInfoCardMapper.updateByGuid(hsaMemberInfoCard);
                log.info("会员卡充值写入结果：{}, {}", JSONObject.toJSONString(hsaMemberInfoCard), rb);
                hsaMemberFundingDetailMapper.insert(hsaMemberFundingDetail);
                //活动统计记录
                terMemberCardBusinessService.saveGiftAmountRecord(hsaMemberConsumption,
                        hsaMemberInfoCard,
                        hsaOperationMemberInfo,
                        rechargeOrderGiftSummaryVO,
                        hsaMemberFundingDetail,
                        hsaMemberConsumptionPayWay);
            });
            terMemberCardRechargeQO.setRechargeOrderGiftSummaryVO(rechargeOrderGiftSummaryVO);
            return new MemberCardRechargeNewDTO(
                    terMemberCardRechargeQO,
                    headerUserInfo,
                    hsaMemberConsumption,
                    hsaMemberInfoCard,
                    hsaMemberConsumptionPayWay,
                    hsaMemberFundingDetail,
                    null);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new MemberBaseException(((MemberBaseException) e).getCode(), ((MemberBaseException) e).getDes());
        } finally {
            lock.unlock();
        }
    }

    /**
     * 会员充值回调请求参数 补偿
     */
    private void fillMemberCardRechargeCallbackRequest(TerMemberCardRechargeQO terMemberCardRechargeQO) {
        String memberInfoCardGuid = terMemberCardRechargeQO.getMemberInfoCardGuid();
        if (StringUtils.isNotEmpty(memberInfoCardGuid)) {
            return;
        }
        HsaMemberConsumption hsaMemberConsumption = hsaMemberConsumptionMapper.selectOne(new LambdaQueryWrapper<HsaMemberConsumption>()
                .eq(HsaMemberConsumption::getOrderNumber, terMemberCardRechargeQO.getOrderNumber())
                .eq(HsaMemberConsumption::getConsumptionType, NumberConstant.NUMBER_0));
        if (Objects.isNull(hsaMemberConsumption)) {
            log.info("会员订单不存在====>{}", terMemberCardRechargeQO.getOrderNumber());
            throw new MemberBaseException("会员订单不存在");
        }
        terMemberCardRechargeQO.setMemberInfoGuid(hsaMemberConsumption.getMemberInfoGuid());
        terMemberCardRechargeQO.setMemberInfoCardGuid(hsaMemberConsumption.getMemberInfoCardGuid());
        terMemberCardRechargeQO.setRechargeMoney(hsaMemberConsumption.getOrderPaidAmount());
        terMemberCardRechargeQO.setStoreGuid(hsaMemberConsumption.getStoreGuid());
        terMemberCardRechargeQO.setStoreName(hsaMemberConsumption.getStoreName());
        terMemberCardRechargeQO.setPayWay(PayWayEnum.AGGREGATION_PAY.getCode());
    }

    @Override
    public AggPayRespDTO rechargePay(TerMemberCardRechargeQO terMemberCardRechargeQO, HttpServletRequest request) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        log.info("headerUserInfo:{}", JacksonUtils.writeValueAsString(headerUserInfo));
        log.info("会员卡充值预下单请求参数:{}", JacksonUtils.writeValueAsString(terMemberCardRechargeQO));
        RLock lock = redissonClient.getLock(RedisKeyConstant.CARD_RECHARGE_NEW + terMemberCardRechargeQO.getMemberInfoCardGuid());
        try {
            if (!lock.tryLock(NumberConstant.NUMBER_30, NumberConstant.NUMBER_30, TimeUnit.SECONDS)) {
                throw new MemberBaseException(systemRoleHelper.getReplace("会员卡充值预下单请求参数",
                        ThreadLocalCache.getOperSubjectGuid()) + terMemberCardRechargeQO.getMemberInfoCardGuid());
            }
            HsaMemberInfoCard hsaMemberInfoCard = terMemberCardBusinessService.getHsaMemberInfoCard(terMemberCardRechargeQO, headerUserInfo);
            //消费记录
            HsaMemberConsumption hsaMemberConsumption = getMemberRechargeConsumption(headerUserInfo, terMemberCardRechargeQO, hsaMemberInfoCard);
            hsaMemberConsumption.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberConsumption.class.getSimpleName()));
            hsaMemberConsumption.setOrderNumber(guidGeneratorUtil.getStringGuid(HsaMemberConsumption.class.getSimpleName()));
            if (headerUserInfo.getSource() == SourceTypeEnum.ADD_ONE_MACHINE.getCode()) {
                hsaMemberConsumption.setDeviceId(terMemberCardRechargeQO.getDevice_id());
            }
            hsaMemberConsumption.setIsComplete(NUMBER_0);
            //充值支付方式
            HsaMemberConsumptionPayWay payWay = getRechargePayWay(terMemberCardRechargeQO, hsaMemberConsumption.getGuid());
            hsaMemberConsumptionPayWayMapper.insert(payWay);
            hsaMemberConsumptionMapper.insert(hsaMemberConsumption);
            log.info("生成订单号:{}", hsaMemberConsumption.getOrderNumber());
            // 查询支付配置信息
            PaySettingBaseRes paySetting = getPaySettingBaseResByOperSubjectGuid(headerUserInfo.getOperSubjectGuid());
            // 查询openId
            String openId = memberInfoWeChatMapper.getMemberInfoOpenId(ThreadLocalCache.getOperSubjectGuid(), terMemberCardRechargeQO.getMemberInfoGuid());
            // 回调地址
            String callBackUrl = String.format(SettlementAggPayAssembler.RECHARGE_PAY_WECHAT_CALLBACK, RequestBaseHost).intern();
            // 聚合支付预下单
            SaasAggPayDTO saasAggPayDTO = SettlementAggPayAssembler.toSaasAggPayDTO(request, hsaMemberConsumption, paySetting,
                    callBackUrl, openId);
            return memberSettlementFeign.pay(saasAggPayDTO);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new MemberBaseException(((MemberBaseException) e).getCode(), ((MemberBaseException) e).getDes());
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void rechargeCallback(SaasNotifyDTO saasNotifyDTO) {
        log.info("saasNotifyDTO:{}", JacksonUtils.writeValueAsString(saasNotifyDTO));
    }

    private PaySettingBaseRes getPaySettingBaseResByOperSubjectGuid(String operSubjectGuid) {
        PaySettingDTO paySettingQueryDTO = new PaySettingDTO();
        paySettingQueryDTO.setOperSubjectGuid(operSubjectGuid);
        return externalSupport.storeServer(ThreadLocalCache.getSystem()).getPaySetting(paySettingQueryDTO);
    }

    private void setCardGiftResidualBalance(HsaMemberInfoCard hsaMemberInfoCard, HsaMemberFundingDetail hsaMemberFundingDetail) {
        BigDecimal cardFreezeAmount = getFreezeAmount(hsaMemberInfoCard);
        hsaMemberFundingDetail.setCardGiftResidualBalance(hsaMemberInfoCard.getGiftAmount().add(cardFreezeAmount));
    }

    private BigDecimal getFreezeAmount(HsaMemberInfoCard hsaMemberInfoCard) {
        CardFreezeBalanceAmountQO cardFreeze = new CardFreezeBalanceAmountQO();
        cardFreeze.setPhysicalCardGuid(hsaMemberInfoCard.getPhysicalCardGuid());
        cardFreeze.setElectronicCardGuid(hsaMemberInfoCard.getElectronicCardGuid());
        cardFreeze.setMemberInfoGuid(hsaMemberInfoCard.getMemberInfoGuid());
        cardFreeze.setIsRefresh(BooleanEnum.FALSE.getCode());
        return hsaCardRechargeGiftDetailService.getMemberCardFreezeAmount(cardFreeze);
    }


    private static BigDecimal addGiftAmount(RechargeOrderGiftSummaryVO rechargeOrderGiftSummaryVO) {
        BigDecimal giftAmount = BigDecimal.ZERO;
        giftAmount = giftAmount.add(rechargeOrderGiftSummaryVO.getGiftEffectuateTotalAmount());
        giftAmount = giftAmount.add(rechargeOrderGiftSummaryVO.getGiftFixedTotalAmount());
        log.info("充值赠送金额：{}", giftAmount);
        return giftAmount;
    }

    @Override
    public RechargeRespVO getRechargeRespVO(MemberCardRechargeNewDTO newDTO) {
        TerMemberCardRechargeQO terMemberCardRechargeQO = newDTO.getTerMemberCardRechargeQO();
        HeaderUserInfo headerUserInfo = newDTO.getHeaderUserInfo();
        HsaMemberConsumption hsaMemberConsumption = newDTO.getHsaMemberConsumption();
        HsaMemberInfoCard hsaMemberInfoCard = newDTO.getHsaMemberInfoCard();
        HsaMemberConsumptionPayWay hsaMemberConsumptionPayWay = newDTO.getHsaMemberConsumptionPayWay();
        HsaMemberFundingDetail hsaMemberFundingDetail = newDTO.getHsaMemberFundingDetail();

        RechargeOrderGiftSummaryVO rechargeOrderGiftSummaryVO = terMemberCardRechargeQO.getRechargeOrderGiftSummaryVO();
        log.info("充值后成功，开始生成权益：orderNumber {}", hsaMemberConsumption.getOrderNumber());
        //成长值任务
        memberBaseThreadExecutor.execute(() -> rechargeTaskBusiness(headerUserInfo, hsaMemberInfoCard, hsaMemberConsumption));
        //积分任务
        memberBaseThreadExecutor.execute(() -> sendRechargeIntegralEvent(headerUserInfo, hsaMemberInfoCard, hsaMemberConsumption));
        log.info("ThreadLocalCache.getHeaderUserInfo:{}", ThreadLocalCache.getHeaderUserInfo());
        hsaLabelSettingService.refreshLabel(Lists.newArrayList(hsaMemberInfoCard.getMemberInfoGuid()), null,
                BooleanEnum.FALSE.getCode(),
                null,
                LabelTriggerTypeEnum.RECHARGE_INFO.getCode());

        HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(hsaMemberInfoCard.getMemberInfoGuid());
        sendChangeCardAmountMsg(hsaMemberInfoCard, hsaMemberConsumption, hsaOperationMemberInfo, hsaMemberFundingDetail);
        sendChangeCardAmountShortMessage(hsaMemberInfoCard, hsaMemberConsumption, hsaOperationMemberInfo, hsaMemberFundingDetail);
        //修复异常充值记录
        fixMemberInfoCardAmount(hsaMemberInfoCard, hsaMemberFundingDetail);
        RechargeRespVO rechargeRespVO = getRechargeRespVO(rechargeOrderGiftSummaryVO, terMemberCardRechargeQO, hsaMemberInfoCard, hsaMemberConsumption, hsaMemberConsumptionPayWay);
        log.info("充值赠送金额：{}", hsaMemberFundingDetail.getGiftAmount());
        rechargeRespVO.setGiftAmount(Objects.nonNull(hsaMemberFundingDetail.getGiftAmount()) ? hsaMemberFundingDetail.getGiftAmount() : BigDecimal.ZERO);
        log.info("聚合充值成功，返回充值结果：{}", JSON.toJSONString(rechargeRespVO));
        return rechargeRespVO;
    }


    private void fixMemberInfoCardAmount(HsaMemberInfoCard hsaMemberInfoCard, HsaMemberFundingDetail hsaMemberFundingDetail) {
        memberBaseThreadExecutor.execute(() -> {
            final HsaMemberFundingDetail memberFundingDetail = hsaMemberFundingDetailMapper.queryByGuid(hsaMemberFundingDetail.getGuid());
            if (Objects.isNull(memberFundingDetail)) {
                //事务回退，整体未写入
                log.error("充值事务回退，明细未写入:{}", JacksonUtils.writeValueAsString(hsaMemberFundingDetail));
                return;
            }
            final HsaMemberInfoCard memberInfoCard = hsaMemberInfoCardMapper.queryByGuid(hsaMemberInfoCard.getGuid());
            //金额没更新
            if (memberInfoCard.getCardAmount().compareTo(hsaMemberInfoCard.getCardAmount()) < 0) {
                //金额没更新
                log.error("修复会员卡充值异常记录，异常记录：{},修复为：{}", JacksonUtils.writeValueAsString(memberInfoCard), JacksonUtils.writeValueAsString(hsaMemberInfoCard));
                transactionUtil.transactional((s) -> {
                    hsaMemberInfoCardMapper.updateByGuid(hsaMemberInfoCard);
                    //异常记录
                    HsaMemberInfoCardError memberInfoCardError = new HsaMemberInfoCardError();
                    BeanUtils.copyProperties(hsaMemberInfoCard, memberInfoCardError);
                    hsaMemberInfoCardErrorMapper.insert(memberInfoCardError);
                });
            }
        });
    }

    @Override
    public RechargeRespVO getTerRechargeRespVO(MemberCardRechargeNewDTO memberCardRechargeNewDTO) {
        HeaderUserInfo headerUserInfo = memberCardRechargeNewDTO.getHeaderUserInfo();
        HsaMemberInfoCard hsaMemberInfoCard = memberCardRechargeNewDTO.getHsaMemberInfoCard();
        HsaMemberConsumption hsaMemberConsumption = memberCardRechargeNewDTO.getHsaMemberConsumption();
        HsaMemberConsumptionPayWay payWay = memberCardRechargeNewDTO.getHsaMemberConsumptionPayWay();
        HsaOperationMemberInfo hsaOperationMemberInfo = memberCardRechargeNewDTO.getHsaOperationMemberInfo();
        TerMemberCardRechargeQO terMemberCardRechargeQO = memberCardRechargeNewDTO.getTerMemberCardRechargeQO();
        HsaMemberFundingDetail hsaMemberFundingDetail = memberCardRechargeNewDTO.getHsaMemberFundingDetail();
        RechargeOrderGiftSummaryVO rechargeOrderGiftSummaryVO = terMemberCardRechargeQO.getRechargeOrderGiftSummaryVO();
        terMemberCardRechargeQO.setOperator(hsaMemberConsumption.getOperatorTelName());
        if (hsaMemberConsumption.getOrderSource() == SourceTypeEnum.ADD_ONE_MACHINE.getCode()) {
            terMemberCardRechargeQO.setOperator(ThreadLocalCache.getOperatorName());
        }

        log.info("现金充值成功，开始生成权益：orderNumber {}", hsaMemberConsumption.getOrderNumber());
        //成长值任务
        memberBaseThreadExecutor.execute(() -> rechargeTaskBusiness(headerUserInfo, hsaMemberInfoCard, hsaMemberConsumption));
        //积分任务
        memberBaseThreadExecutor.execute(() -> sendRechargeIntegralEvent(headerUserInfo, hsaMemberInfoCard, hsaMemberConsumption));

        //刷新标签
        hsaLabelSettingService.refreshLabel(Lists.newArrayList(hsaMemberInfoCard.getMemberInfoGuid()), null,
                BooleanEnum.FALSE.getCode(),
                null,
                LabelTriggerTypeEnum.RECHARGE_INFO.getCode());

        // 返回充值结果
        RechargeRespVO rechargeRespVO = getRechargeRespVO(rechargeOrderGiftSummaryVO, terMemberCardRechargeQO, hsaMemberInfoCard, hsaMemberConsumption, payWay);

        //充值赠送
        log.info("充值赠送金额={}", hsaMemberFundingDetail.getGiftAmount());
        rechargeRespVO.setGiftAmount(Objects.nonNull(hsaMemberFundingDetail.getGiftAmount()) ? hsaMemberFundingDetail.getGiftAmount() : BigDecimal.ZERO);
        log.info("现金充值成功，rechargeRespVO {}", JSON.toJSONString(rechargeRespVO));
        memberBaseThreadExecutor.execute(() -> {
            ThreadLocalCache.put(JSON.toJSONString(headerUserInfo));
            //打印小票
            receiptPrinting(terMemberCardRechargeQO, hsaOperationMemberInfo, hsaMemberConsumption, rechargeRespVO);
        });

        memberBaseThreadExecutor.execute(() -> {
            ThreadLocalCache.put(JSON.toJSONString(headerUserInfo));
            //资金变动消息发送
            sendChangeCardAmountMsg(hsaMemberInfoCard, hsaMemberConsumption, hsaOperationMemberInfo, hsaMemberFundingDetail);
            //短信发送
            sendChangeCardAmountShortMessage(hsaMemberInfoCard, hsaMemberConsumption, hsaOperationMemberInfo, hsaMemberFundingDetail);

        });
        log.info("现金充值成功，返回充值结果：{}", JSON.toJSONString(rechargeRespVO));
        return rechargeRespVO;
    }


    /**
     * 处理超额数据
     *
     * @param terMemberCardRechargeQO
     * @param hsaMemberInfoCard
     * @param hsaCardBaseInfo
     */
    @Override
    public void excessProcessor(TerMemberCardRechargeQO terMemberCardRechargeQO, HsaMemberInfoCard hsaMemberInfoCard, HsaCardBaseInfo hsaCardBaseInfo) {
        if (hsaMemberInfoCard.getCardAmount().compareTo(BigDecimal.ZERO) < 0 && hsaCardBaseInfo.getIsExcess() == BooleanEnum.TRUE.getCode()) {
            // 超额处理
            checkCardRecharge(terMemberCardRechargeQO, hsaMemberInfoCard, hsaCardBaseInfo);
        } else {
            hsaMemberInfoCard.setCardAmount(hsaMemberInfoCard.getCardAmount().add(terMemberCardRechargeQO.getRechargeMoney()));
        }
        if (Objects.nonNull(terMemberCardRechargeQO.getGiftAmount()) && terMemberCardRechargeQO.getGiftAmount().compareTo(BigDecimal.ZERO) > 0) {
            hsaMemberInfoCard.setGiftAmount(hsaMemberInfoCard.getGiftAmount().add(terMemberCardRechargeQO.getGiftAmount()));
        }
    }


    private static void checkCardRecharge(TerMemberCardRechargeQO terMemberCardRechargeQO, HsaMemberInfoCard hsaMemberInfoCard, HsaCardBaseInfo hsaCardBaseInfo) {
        hsaMemberInfoCard.setCardAmount(hsaMemberInfoCard.getCardAmount().add(terMemberCardRechargeQO.getRechargeMoney()));

        //处理赠送冲正
        dealGiftAmount(terMemberCardRechargeQO, hsaMemberInfoCard);
        if (hsaCardBaseInfo.getExcessType() == 0 && hsaMemberInfoCard.getCardAmount().compareTo(BigDecimal.ZERO) >= 0) {
            hsaMemberInfoCard.setExcessTimes(hsaCardBaseInfo.getExcessMoney().intValue());
        } else {
            checkExcessAmount(terMemberCardRechargeQO, hsaMemberInfoCard, hsaCardBaseInfo);
        }
    }

    private static void dealGiftAmount(TerMemberCardRechargeQO terMemberCardRechargeQO, HsaMemberInfoCard hsaMemberInfoCard) {
        if (hsaMemberInfoCard.getCardAmount().compareTo(BigDecimal.ZERO) < 0
                && Objects.nonNull(terMemberCardRechargeQO.getGiftAmount())
                && terMemberCardRechargeQO.getGiftAmount().compareTo(BigDecimal.ZERO) > 0) {
            //到此条件下实充金额仍未冲正  则将赠送金额对实充进行冲正  如果能够冲正则返回剩余赠送金额  若扔无法冲正 则本次赠送金额为0

            BigDecimal cardAmountAbs = hsaMemberInfoCard.getCardAmount().abs();
            BigDecimal giftAmount = terMemberCardRechargeQO.getGiftAmount();
            //冲正剩余的赠送金额
            BigDecimal surplusGiftAmount = BigDecimal.ZERO;
            if (cardAmountAbs.compareTo(giftAmount) >= 0) {
                hsaMemberInfoCard.setCardAmount(hsaMemberInfoCard.getCardAmount().add(giftAmount));
            } else {
                surplusGiftAmount = giftAmount.subtract(cardAmountAbs);
                hsaMemberInfoCard.setCardAmount(hsaMemberInfoCard.getCardAmount().add(cardAmountAbs));
            }
            terMemberCardRechargeQO.setGiftAmount(surplusGiftAmount);
        }
    }

    private static void checkExcessAmount(TerMemberCardRechargeQO terMemberCardRechargeQO, HsaMemberInfoCard hsaMemberInfoCard, HsaCardBaseInfo hsaCardBaseInfo) {
        if (hsaMemberInfoCard.getCardAmount().compareTo(BigDecimal.ZERO) >= 0) {
            hsaMemberInfoCard.setExcessAmount(hsaCardBaseInfo.getExcessMoney());
        } else {
            checkCardAmount(terMemberCardRechargeQO, hsaMemberInfoCard, hsaCardBaseInfo);
        }
    }

    private static void checkCardAmount(TerMemberCardRechargeQO terMemberCardRechargeQO, HsaMemberInfoCard hsaMemberInfoCard, HsaCardBaseInfo hsaCardBaseInfo) {

        if (hsaMemberInfoCard.getCardAmount().abs().compareTo(hsaCardBaseInfo.getExcessMoney()) < 0) {
            if (hsaMemberInfoCard.getExcessAmount().compareTo(BigDecimal.ZERO) == 0) {
                hsaMemberInfoCard.setExcessAmount(hsaCardBaseInfo.getExcessMoney().subtract(hsaMemberInfoCard.getCardAmount().abs()));
            } else {
                hsaMemberInfoCard.setExcessAmount(hsaMemberInfoCard.getExcessAmount().add(terMemberCardRechargeQO.getRechargeMoney()));
            }
        }
    }

    @Override
    @RedissonLock(lockName = "RECHARGE_TASK_BUSINESS", tryLock = true, leaseTime = 10)
    public void rechargeTaskBusiness(HeaderUserInfo header, HsaMemberInfoCard hsaMemberInfoCard, HsaMemberConsumption memberConsumptionGuid) {
        if (gradeStateService.isNotEnable(hsaMemberInfoCard.getOperSubjectGuid())) {
            return;
        }
        if (StringUtils.isEmpty(hsaMemberInfoCard.getMemberInfoGuid())) {
            return;
        }
        GrowthValueChangeEvent event = MemberCardAssembler.getGrowthValueChangeEvent(header, hsaMemberInfoCard, memberConsumptionGuid);
        growthValueEvent.send(event);
        log.info("发送成长值消息" + JSON.toJSONString(event));
    }

    private RechargeRespVO getRechargeRespVO(RechargeOrderGiftSummaryVO rechargeOrderGiftSummaryVO,
                                             TerMemberCardRechargeQO terMemberCardRechargeQO,
                                             HsaMemberInfoCard hsaMemberInfoCard,
                                             HsaMemberConsumption hsaMemberConsumption,
                                             HsaMemberConsumptionPayWay payWay) {
        return MemberCardAssembler.getRespVO(rechargeOrderGiftSummaryVO, terMemberCardRechargeQO, hsaMemberInfoCard, hsaMemberConsumption, payWay);
    }


    /**
     * 填充充值消费记录信息
     *
     * @param headerUserInfo          headerUserInfo
     * @param terMemberCardRechargeQO terMemberCardRechargeQO
     * @param memberInfoCard          memberInfoCard
     * @return HsaMemberConsumption
     */
    @Override
    public HsaMemberConsumption getMemberRechargeConsumption(HeaderUserInfo headerUserInfo, TerMemberCardRechargeQO
            terMemberCardRechargeQO, HsaMemberInfoCard memberInfoCard) {
        HsaMemberConsumption memberConsumption = MerchantPayAssembler.fromMemberRechargeConsumption(headerUserInfo,
                terMemberCardRechargeQO, memberInfoCard);
        if (headerUserInfo.getSource() == SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode()
                || SourceTypeEnum.getMallSource().contains(headerUserInfo.getSource())) {
            HsaOperationMemberInfo memberInfo = hsaOperationMemberInfoMapper.queryByGuid(memberInfoCard.getMemberInfoGuid());
            memberConsumption.setOperatorTelName(memberInfo.getUserName() + StringConstant.STR_BIAS + memberInfo.getPhoneNum());
        } else {
            memberConsumption.setOperatorTelName(ThreadLocalCache.getOperatorTelName());
        }

        return memberConsumption;
    }

    /**
     * 获取充值支付方式
     *
     * @param terMemberCardRechargeQO terMemberCardRechargeQO
     * @param memberConsumptionGuid   memberConsumptionGuid
     * @return HsaMemberConsumptionPayWay
     */
    @Override
    public HsaMemberConsumptionPayWay getRechargePayWay(TerMemberCardRechargeQO terMemberCardRechargeQO, String memberConsumptionGuid) {
        HsaMemberConsumptionPayWay memberConsumptionPayWay = new HsaMemberConsumptionPayWay();
        memberConsumptionPayWay.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberConsumptionPayWay.class.getSimpleName()));
        memberConsumptionPayWay.setConsumptionGuid(memberConsumptionGuid);
        memberConsumptionPayWay.setPayAmount(terMemberCardRechargeQO.getRechargeMoney());
        memberConsumptionPayWay.setPayWay(terMemberCardRechargeQO.getPayWay());
        memberConsumptionPayWay.setPayName(PayWayEnum.getPayName(terMemberCardRechargeQO.getPayWay()));
        if (Objects.equals(PayWayEnum.CUSTOMER_PAY.getCode(), terMemberCardRechargeQO.getPayWay())
                && StringUtils.isNotEmpty(terMemberCardRechargeQO.getPayName())) {
            memberConsumptionPayWay.setPayName(terMemberCardRechargeQO.getPayName());
        }
        return memberConsumptionPayWay;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateSecretDTO callbackCreatePhysicalCard(ProducePhysicalCardQO producePhysicalCardQO) {
        final HsaPhysicalCardStrategyRecord record = hsaPhysicalCardStrategyRecordService.queryByGuid(producePhysicalCardQO.getOrderNumber());
        if (Objects.isNull(record)) {
            throw new MemberBaseException("当前支付记录不存在");
        }
        if (!Objects.equals(record.getPayStatus(), PayStatusEnum.APY.getCode())) {
            throw new MemberBaseException("押金未支付，无法开卡");
        }
        if (Objects.nonNull(record.getMakePhysicalCardRecordGuid())) {
            throw new MemberBaseException(systemRoleHelper.getReplace("会员卡已创建，请勿重复操作", ThreadLocalCache.getOperSubjectGuid()));
        }
        final PhysicalCardStrategyVO strategyVO = HsaPhysicalCardStrategyRecordTransform.INSTANCE.toVo(record);
        producePhysicalCardQO.setPhysicalCardStrategyVO(strategyVO);
        //查询绑定会员信息
        HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.selectOne(
                new LambdaQueryWrapper<HsaOperationMemberInfo>()
                        .eq(HsaOperationMemberInfo::getPhoneNum, producePhysicalCardQO.getMemberPhone())
                        .eq(HsaOperationMemberInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
        //封装参数
        final CreateSecretDTO physicalCard = createPhysicalCard(producePhysicalCardQO, hsaOperationMemberInfo);
        if (CreateCardResultEnum.CREATE_RESULT_SUCCESS.equals(physicalCard.getResultEnum())) {
            record.setMakePhysicalCardRecordGuid(physicalCard.getRecordGuid());
            hsaPhysicalCardStrategyRecordService.updateByGuid(record);
        }
        return physicalCard;
    }

    @Override
    public CreateSecretDTO createPhysicalCardByStrategy(ProducePhysicalCardQO producePhysicalCardQO) {
        //校验参数
        final Pair<HsaOperationMemberInfo, Long> hsaOperationMemberInfoLongPair = terMemberCardBusinessService.parameterVerify(producePhysicalCardQO);
        final HsaOperationMemberInfo operationMemberInfo = hsaOperationMemberInfoLongPair.getKey();
        //封装参数
        CreatePhysicalCardSecretQO physicalCardSecretQO = MemberCardAssembler.dataConversion(producePhysicalCardQO, operationMemberInfo);
        //押金策略
        final Long openPhysicalCardStrategyGuid = hsaOperationMemberInfoLongPair.getValue();
        RLock lock = redissonClient.getLock("CREATE_PHYSICAL_CARD_BY_STRATEGY:" + producePhysicalCardQO.getMemberPhone());
        try {
            if (lock.isLocked()) {
                throw new MemberBaseException("会员卡开卡请求参数：" + producePhysicalCardQO.getMemberPhone());
            }
            //查询会员卡
            int count = memberCardOperationService.validateCreatePhysicalCard(physicalCardSecretQO);
            //有押金
            if (Objects.nonNull(openPhysicalCardStrategyGuid)) {
                final HsaDepositStrategyVO strategyVO = hsaDepositStrategyService.getByGuid(openPhysicalCardStrategyGuid);
                final boolean hasDepositAmount = Optional.ofNullable(strategyVO.getDepositAmount()).map(amount -> amount.compareTo(BigDecimal.ZERO) > 0).orElse(false);
                if (hasDepositAmount) {
                    HsaPhysicalCardStrategyRecord strategyRecord =
                            MemberCardAssembler.getHsaPhysicalCardStrategyRecord(producePhysicalCardQO, operationMemberInfo, strategyVO);
                    //开卡押金预下单
                    final String strategyRecordGuid = hsaPhysicalCardStrategyRecordService.saveRecord(strategyRecord);
                    return CreateSecretDTO.builder().orderNumber(strategyRecordGuid).orderAmount(strategyVO.getDepositAmount()).build();
                }
            }
            //直接开卡
            final CreateSecretDTO physicalCardSecret = memberCardOperationService.handlerCreatePhysicalCardSecret(physicalCardSecretQO, count);
            //处理写卡后，写卡参数
            getWriteCardParam(physicalCardSecret, producePhysicalCardQO);
            return physicalCardSecret;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new MemberBaseException(((MemberBaseException) e).getCode(), ((MemberBaseException) e).getDes());
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private CreateSecretDTO createPhysicalCard(ProducePhysicalCardQO producePhysicalCardQO, HsaOperationMemberInfo hsaOperationMemberInfo) {
        //封装参数
        CreatePhysicalCardSecretQO createPhysicalCardSecretQO = MemberCardAssembler.dataConversion(producePhysicalCardQO, hsaOperationMemberInfo);
        //查询会员卡
        CreateSecretDTO physicalCardSecret = memberCardOperationService.createPhysicalCardSecret(createPhysicalCardSecretQO);
        //处理写卡后，写卡参数
        getWriteCardParam(physicalCardSecret, producePhysicalCardQO);
        return physicalCardSecret;
    }

    @Override
    public CreateSecretDTO producePhysicalCardNumber(ProducePhysicalCardQO producePhysicalCardQO) {
        //校验参数
        final Pair<HsaOperationMemberInfo, Long> hsaOperationMemberInfoLongPair = terMemberCardBusinessService.parameterVerify(producePhysicalCardQO);
        //封装参数
        return createPhysicalCard(producePhysicalCardQO, hsaOperationMemberInfoLongPair.getKey());
    }

    /**
     * 写卡后，写卡参数处理
     *
     * @param physicalCardSecret    返回给前端的写卡参数信息
     * @param producePhysicalCardQO 前端请求时参数信息
     */
    private void getWriteCardParam(CreateSecretDTO physicalCardSecret, ProducePhysicalCardQO producePhysicalCardQO) {
        String cardGuid = producePhysicalCardQO.getCardGuid();
        String physicalCardNum = Optional.ofNullable(physicalCardSecret).map(CreateSecretDTO::getCardNumber).orElse("-220");
        //会员卡关联记录信息
        HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMapper.selectOne(
                new LambdaQueryWrapper<HsaMemberInfoCard>()
                        .eq(HsaMemberInfoCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                        .eq(HsaMemberInfoCard::getCardGuid, cardGuid)
                        .eq(HsaMemberInfoCard::getPhysicalCardNum, physicalCardNum));
        if (ObjectUtil.isNull(hsaMemberInfoCard)) {
            return;
        }
        assert physicalCardSecret != null;
        //实体卡、电子卡、会员卡基础信息对象
        CardTimeCombination cardTimeCombination = getCardInfo(hsaMemberInfoCard);
        //获取当前会员卡可使用有效期
        getCardUseTime(cardTimeCombination, physicalCardSecret);
        //获取当前绑定会员卡余额
        getCardBalance(hsaMemberInfoCard, physicalCardSecret);
        //获取当前会员卡可使用范围信息
        getCardStore(hsaMemberInfoCard, cardGuid, physicalCardSecret);
    }

    /**
     * 获取到实体卡、电子卡、会员卡基础信息对象
     *
     * @return
     */
    private CardTimeCombination getCardInfo(HsaMemberInfoCard hsaMemberInfoCard) {
        CardTimeCombination cardTimeCombination = new CardTimeCombination();

        //查询绑定的实体卡信息
        HsaPhysicalCard hsaPhysicalCard = hsaPhysicalCardMapper.selectOne(
                new LambdaQueryWrapper<HsaPhysicalCard>()
                        .eq(HsaPhysicalCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                        .eq(HsaPhysicalCard::getGuid, hsaMemberInfoCard.getPhysicalCardGuid()));
        cardTimeCombination.setHsaPhysicalCard(hsaPhysicalCard);

        //查询绑定的电子卡信息
        HsaElectronicCard hsaElectronicCard = hsaElectronicCardMapper.selectOne(
                new LambdaQueryWrapper<HsaElectronicCard>()
                        .eq(HsaElectronicCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                        .eq(HsaElectronicCard::getGuid, hsaMemberInfoCard.getElectronicCardGuid()));
        cardTimeCombination.setHsaElectronicCard(hsaElectronicCard);

        //会员卡基础信息
        HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.selectOne(
                new LambdaQueryWrapper<HsaCardBaseInfo>()
                        .eq(HsaCardBaseInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                        .eq(HsaCardBaseInfo::getGuid, hsaMemberInfoCard.getCardGuid()));
        cardTimeCombination.setHsaCardBaseInfo(hsaCardBaseInfo);
        return cardTimeCombination;
    }

    /**
     * 获取当前会员卡可使用有效期
     *
     * @param cardTimeCombination 会员卡信息对象集合
     * @param physicalCardSecret  写卡返回的信息对象
     */
    private void getCardUseTime(CardTimeCombination cardTimeCombination, CreateSecretDTO physicalCardSecret) {
        HsaCardBaseInfo hsaCardBaseInfo = cardTimeCombination.getHsaCardBaseInfo();
        if (ObjectUtil.isNull(hsaCardBaseInfo)) {
            return;
        }
        CardTimeFactory cardTimeFactory = new CardTimeFactory();
        CardValidityType cardValidityType = cardTimeFactory.timeHandler(hsaCardBaseInfo.getCardValidity());
        String expiryDate = cardValidityType.cardTimeHandler(
                hsaCardBaseInfo, //会员卡基础信息
                cardTimeCombination.getHsaElectronicCard(),  //会员卡-电子卡信息
                cardTimeCombination.getHsaPhysicalCard());//会员卡-实体卡信息
        physicalCardSecret.getProduceSecretDTO().setExpiryDate(expiryDate);
    }

    /**
     * 获取当前会员卡可使用范围信息
     *
     * @param cardGuid           会员卡guid
     * @param physicalCardSecret 写卡返回的信息对象
     */
    private void getCardStore(HsaMemberInfoCard hsaMemberInfoCard, String cardGuid, CreateSecretDTO physicalCardSecret) {
        List<UseScopeDTO> useScopeList = new ArrayList<>();
        //会员卡绑定的门店信息
        List<StoreCardRuleQO> storeCardRule = hsaMemberInfoCardService.getStoreCardRule(Collections.singletonList(cardGuid),
                hsaMemberInfoCard.getGuid());
        if (ObjectUtil.isNull(storeCardRule) || storeCardRule.isEmpty()) {
            return;
        }
        for (StoreCardRuleQO storeCardRuleQO : storeCardRule) {
            UseScopeDTO useScopeDTO = new UseScopeDTO();
            useScopeDTO.setStoreGuid(storeCardRuleQO.getStoreGuid());
            useScopeDTO.setStoreName(storeCardRuleQO.getStoreName());
            useScopeDTO.setParentGuid(JSONObject.toJSONString(storeCardRuleQO.getStoreBoothCardRuleQOList()));
            useScopeList.add(useScopeDTO);
        }
        physicalCardSecret.getProduceSecretDTO().setUseScope(useScopeList);
    }

    /**
     * 获取会员卡余额
     *
     * @return 已开通的会员卡余额
     */
    private void getCardBalance(HsaMemberInfoCard hsaMemberInfoCard, CreateSecretDTO physicalCardSecret) {
        Optional<HsaMemberInfoCard> memberInfoCard = Optional.of(hsaMemberInfoCard);
        BigDecimal giftAmount = memberInfoCard.map(HsaMemberInfoCard::getGiftAmount).orElse(BigDecimal.ZERO);
        BigDecimal cardAmount = memberInfoCard.map(HsaMemberInfoCard::getCardAmount).orElse(BigDecimal.ZERO);
        BigDecimal subsidyAmount = memberInfoCard.map(HsaMemberInfoCard::getSubsidyAmount).orElse(BigDecimal.ZERO);
        physicalCardSecret.getProduceSecretDTO().setBalance(giftAmount.add(cardAmount).add(subsidyAmount));
    }

    @Override
    public void physicalMarkResult(RegeneratePhysicalCardQO request) {
        HsaPhysicalCard hsaPhysicalCard = hsaPhysicalCardMapper.selectOne(
                new LambdaQueryWrapper<HsaPhysicalCard>()
                        .eq(HsaPhysicalCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                        .eq(HsaPhysicalCard::getCardBindingNum, request.getBindingCode())
                        .eq(HsaPhysicalCard::getCardNum, request.getCardNumber())
        );
        if (ObjectUtil.isNull(hsaPhysicalCard)) {
            return;
        }
        updatePhysicalCreateRecord(request, hsaPhysicalCard);
    }

    private void updatePhysicalCreateRecord(RegeneratePhysicalCardQO request, HsaPhysicalCard hsaPhysicalCard) {
        String recordGuid = hsaPhysicalCard.getMakePhysicalCardRecordGuid();
        if (recordGuid != null) {
            //更新生成实体卡记录表
            HsaPhysicalCardCreateRecord hsaPhysicalCardCreateRecord = recordMapper.selectOne(
                    new LambdaQueryWrapper<HsaPhysicalCardCreateRecord>()
                            .eq(HsaPhysicalCardCreateRecord::getGuid, recordGuid));
            if (ObjectUtil.isNotNull(hsaPhysicalCardCreateRecord)) {
                hsaPhysicalCardCreateRecord.setCreateResult(request.getResult() == 0
                        ?
                        CreateCardResultEnum.MAKE_RESULT_FAIL.getCode()
                        : CreateCardResultEnum.MAKE_RESULT_SUCCESS.getCode());
                recordMapper.updateByGuid(hsaPhysicalCardCreateRecord);
            }
        }
    }

    @Override
    public void regeneratePhysicalCardNumber(RegeneratePhysicalCardQO request) {
        HsaPhysicalCard hsaPhysicalCard = hsaPhysicalCardMapper.selectOne(
                new LambdaQueryWrapper<HsaPhysicalCard>()
                        .eq(HsaPhysicalCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                        .eq(HsaPhysicalCard::getCardNum, request.getCardNumber())
                        .eq(HsaPhysicalCard::getCardBindingNum, request.getBindingCode()));
        if (ObjectUtil.isNull(hsaPhysicalCard)) {
            return;
        }
        if (request.getResult() == 0) {
            String cardGuid = hsaPhysicalCard.getCardGuid();
            String guid = hsaPhysicalCard.getGuid();
            //移除实体卡当前实体卡信息
            hsaPhysicalCardMapper.removeByGuid(guid);

            HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMapper.selectOne(
                    new LambdaQueryWrapper<HsaMemberInfoCard>()
                            .eq(HsaMemberInfoCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                            .eq(HsaMemberInfoCard::getCardGuid, cardGuid)
                            .eq(HsaMemberInfoCard::getPhysicalCardGuid, guid));
            if (!ObjectUtil.isNull(hsaMemberInfoCard)) {
                //移除开通实体卡生成的关联表信息
                hsaMemberInfoCardMapper.removeByGuid(hsaMemberInfoCard.getGuid());
            }
        }
        updatePhysicalCreateRecord(request, hsaPhysicalCard);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    //@RedissonLock(lockName = "CARD_PAY_ORDER", tryLock = true, leaseTime = 20)
    public ConsumptionRespVO payOrder(RequestConfirmPayVO request) {
        //请求头信息
        return new ConsumptionRespVO();
    }


    @Override
    public void sendChangeCardAmountShortMessage(HsaMemberInfoCard memberInfoCard,
                                                 HsaMemberConsumption consumption,
                                                 HsaOperationMemberInfo hsaOperationMemberInfo,
                                                 HsaMemberFundingDetail hsaMemberFundingDetail) {
        SendMessagesConfigVO sendMessagesConfigVO = memberMallToolFeign.
                getMessagesConfigByName(ThreadLocalCache.getOperSubjectGuid(), WechatMsgSendType.AMOUNT_CHANGE.getMsgTitle());

        if (sendMessagesConfigVO.getMessageStatus() == BooleanEnum.TRUE.getCode()
                && Objects.nonNull(memberInfoCard)
                && Objects.nonNull(hsaOperationMemberInfo)) {

            //兼容支付场景
            if (Objects.isNull(hsaMemberFundingDetail)) {
                hsaMemberFundingDetail = hsaMemberFundingDetailMapper.selectOne(new LambdaQueryWrapper<HsaMemberFundingDetail>()
                        .eq(HsaMemberFundingDetail::getMemberConsumptionGuid, consumption.getGuid())
                        .last("LIMIT 1"));
                if (Objects.isNull(hsaMemberFundingDetail)) {
                    log.info("此订单没有资金明细：{}", consumption.getOrderNumber());
                    return;
                }
            }

            SendShortMessageDTO sendShortMessageDTO = new SendShortMessageDTO();
            Map<String, String> map = new HashMap<>();
            sendShortMessageDTO.setEnterpriseId(ThreadLocalCache.getEnterpriseGuid());
            sendShortMessageDTO.setPhoneList(Collections.singletonList(hsaOperationMemberInfo.getPhoneNum()));
            AmountSourceTypeEnum growthValueOperationEnum = AmountSourceTypeEnum.getEnum(hsaMemberFundingDetail.getAmountSourceType());

            if (growthValueOperationEnum.getCode() == AmountSourceTypeEnum.RECHARGE_REFUND.getCode()) {
                growthValueOperationEnum = AmountSourceTypeEnum.RETREAT_CARD;
            }

            log.info("变动类型:{}", JSON.toJSONString(growthValueOperationEnum));

            ShortMessagesConfigDTO messagesConfigDTO = JSON.parseArray(sendMessagesConfigVO.getShortMessageContentJson(), ShortMessagesConfigDTO.class)
                    .stream()
                    .collect(Collectors.toMap(ShortMessagesConfigDTO::getChangeType, Function.identity(), (entity1, entity2) -> entity1))
                    .get(growthValueOperationEnum.getCode());

            log.info("发送短信配置:{}", messagesConfigDTO);

            sendShortMessageDTO.setSmsSign(messagesConfigDTO.getSmsSign());
            sendShortMessageDTO.setSmsTemplateCode(messagesConfigDTO.getSmsTemplateCode());

            //总余额
            BigDecimal totalAmount = memberInfoCard.getCardAmount().add(memberInfoCard.getGiftAmount()).add(memberInfoCard.getSubsidyAmount());
            BigDecimal cardFreezeAmount = getFreezeAmount(memberInfoCard);
            log.info("冻结金额:{}", cardFreezeAmount);
            totalAmount = totalAmount.add(cardFreezeAmount);
            map.put("name", hsaOperationMemberInfo.getUserName());
            switch (Objects.requireNonNull(growthValueOperationEnum)) {
                case RECHARGE:
                    map.put("rechargeAmount", hsaMemberFundingDetail.getRechargeAmount() + "");
                    map.put("giftBalance", hsaMemberFundingDetail.getGiftAmount() + "");
                    break;
                case ADMIN_EDIT:
                    map.put("operationBalance", hsaMemberFundingDetail.getRechargeAmount() + "");
                    break;
                case RETREAT_CARD:
                case RECHARGE_REFUND:
                    map.put("refundAmount", hsaMemberFundingDetail.getRechargeAmount() + "");
                    break;
                case CONSUMPTION:
                    map.put("consumptionAmount", consumption.getOrderPaidAmount() + "");
                    break;
                default:
                    throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_VERIFY);
            }
            map.put("cardBalance", totalAmount + "");
            sendShortMessageDTO.setNoticeContentMap(map);
            shortMessageSendService.send(sendShortMessageDTO);
        }
    }

    @Override
    public void sendChangeCardAmountMsg(HsaMemberInfoCard memberInfoCard,
                                        HsaMemberConsumption consumption,
                                        HsaOperationMemberInfo hsaOperationMemberInfo,
                                        HsaMemberFundingDetail hsaMemberFundingDetail) {
        // 变动时间:time6,变动原因:thing7,当前余额:amount8,变动金额:amount5
        //发送消息
        MessagesSendQO messagesSendQO = new MessagesSendQO();
        if (Objects.nonNull(memberInfoCard) && Objects.nonNull(hsaOperationMemberInfo)) {
            BigDecimal amount = memberInfoCard.getCardAmount().add(memberInfoCard.getGiftAmount()).add(memberInfoCard.getSubsidyAmount());
            // 查询金额
            CardFreezeBalanceAmountQO balanceAmountQO = new CardFreezeBalanceAmountQO();
            balanceAmountQO.setMemberInfoCardGuid(memberInfoCard.getGuid());
            balanceAmountQO.setIsRefresh(BooleanEnum.FALSE.getCode());
            BigDecimal freightAmount = hsaCardRechargeGiftDetailService.getMemberCardFreezeAmount(balanceAmountQO);
            amount = amount.add(freightAmount);

            HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(memberInfoCard.getCardGuid());
            messagesSendQO.setTemplateName(WechatMsgSendType.AMOUNT_CHANGE.getMsgTitle());
            messagesSendQO.setCardName(hsaCardBaseInfo.getCardName());
            messagesSendQO.setCardImage(hsaCardBaseInfo.getCardImage());
            messagesSendQO.setPrams(generateAppletsParamNew(consumption, hsaMemberFundingDetail, amount, memberInfoCard));
            messagesSendQO.setMpParams(generateMpParam(consumption, hsaMemberFundingDetail, amount));
            log.info("发送小程序资金变动模板消息参数:{}", messagesSendQO.getPrams());
            dealSendBase(consumption, hsaOperationMemberInfo, messagesSendQO);
        }
    }

    private Map<String, String> generateAppletsParamNew(HsaMemberConsumption consumption,
                                                        HsaMemberFundingDetail hsaMemberFundingDetail,
                                                        BigDecimal amount,
                                                        HsaMemberInfoCard memberInfoCard) {
        Map<String, String> params = new HashMap<>();
        BigDecimal rechargeAmount;
        String amountType;
        if (consumption.getConsumptionType() == 0) {
            params.put(WechatMsgParamConstant.InnerAmountChangeConstant.MP_CHANGE_REASON, consumption.getIsCancel() == 1 ? "退款" : "充值");
            rechargeAmount = hsaMemberFundingDetail.getRechargeAmount();
            if (Objects.nonNull(hsaMemberFundingDetail.getGiftAmount())) {
                rechargeAmount = rechargeAmount.add(hsaMemberFundingDetail.getGiftAmount());
            }
            amountType = "+" + rechargeAmount;
        } else {
            params.put(WechatMsgParamConstant.InnerAmountChangeConstant.MP_CHANGE_REASON, "消费");
            rechargeAmount = consumption.getOrderPaidAmount();
            amountType = "-" + rechargeAmount;
        }
        params.put("amount1", amountType);
        params.put("date4", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        params.put("thing7", memberInfoCard.getCardName());
        params.put("amount2", amount + "");
        return params;
    }

    /**
     * 构建公众号资金变动模板消息参数
     */
    private Map<String, String> generateMpParam(HsaMemberConsumption consumption,
                                                HsaMemberFundingDetail hsaMemberFundingDetail, BigDecimal amount) {
        Map<String, String> params = new HashMap<>();
        BigDecimal rechargeAmount;
        String amountType;
        // 门店名称
        params.put(WechatMsgParamConstant.InnerAmountChangeConstant.MP_STORE_NAME, hsaMemberFundingDetail.getStoreName());
        params.put(WechatMsgParamConstant.InnerAmountChangeConstant.MP_CHANGE_TIME, ValidTimeUtils.getDate());
        if (consumption.getConsumptionType() == 0
                || consumption.getIsCancel() == BooleanEnum.TRUE.getCode()) {
            rechargeAmount = hsaMemberFundingDetail.getRechargeAmount();
            if (Objects.nonNull(hsaMemberFundingDetail.getGiftAmount())) {
                rechargeAmount = rechargeAmount.add(hsaMemberFundingDetail.getGiftAmount());
            }
            amountType = "+" + rechargeAmount;
        } else {
            rechargeAmount = consumption.getOrderPaidAmount();
            amountType = "-" + rechargeAmount;
        }
        params.put(WechatMsgParamConstant.InnerAmountChangeConstant.MP_CHANGE_AMOUNT, amountType + "");
        params.put(WechatMsgParamConstant.InnerAmountChangeConstant.MP_CURRENT_AMOUNT, amount + "");
        return params;
    }

    private void dealSendBase(HsaMemberConsumption consumption, HsaOperationMemberInfo hsaOperationMemberInfo, MessagesSendQO messagesSendQO) {
        messagesSendQO.setOrderNum(consumption.getOrderNumber());
        messagesSendQO.setMemberInfoCardGuid(consumption.getMemberInfoCardGuid());
        messagesSendQO.setPhone(hsaOperationMemberInfo.getPhoneNum());

        messagesSendQO.setOperSubjectGuid(hsaOperationMemberInfo.getOperSubjectGuid());
        messagesSendQO.setEnterpriseGuid(hsaOperationMemberInfo.getEnterpriseGuid());
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        memberBaseThreadExecutor.execute(() -> {
            // 远程调用需要
            ThreadLocalCache.put(JSON.toJSONString(headerUserInfo));
            wechatSendService.send(messagesSendQO);
        });
    }


    /**
     * 保存消费记录支付方式
     *
     * @param requestPayInfoList    支付方式集合
     * @param memberConsumptionGuid 消费guid
     */
    private void savePayWay(List<RequestPayInfoDTO> requestPayInfoList, String memberConsumptionGuid) {
        payInfoDTO2PayWay(requestPayInfoList, memberConsumptionGuid);
    }

    /**
     * 从confirmPayReqDTO中获取支付方式信息
     *
     * @param requestPayInfoList    支付方式集合
     * @param memberConsumptionGuid 消费guid
     * @return List<HsaMemberConsumptionPayWay>
     */
    private void payInfoDTO2PayWay(List<RequestPayInfoDTO> requestPayInfoList, String memberConsumptionGuid) {
        if (CollUtil.isNotEmpty(requestPayInfoList)) {
            requestPayInfoList.forEach(e -> {
                HsaMemberConsumptionPayWay payWay = new HsaMemberConsumptionPayWay();
                payWay.setConsumptionGuid(memberConsumptionGuid)
                        .setGuid(guidGeneratorUtil.getStringGuid(HsaMemberConsumptionPayWay.class.getSimpleName()))
                        .setPayAmount(e.getPayAmount());
                //自定义支付之外的
                if (PayWayEnum.CUSTOMER_PAY.getCode() == e.getPayWay()) {
                    payWay.setPayCode(String.valueOf(e.getPayWay()));
                    payWay.setPayWay(e.getPayWay());
                    payWay.setPayName(e.getPayName());
                } else {
                    payWay.setPayCode(e.getPayCode());
                    payWay.setPayWay(e.getPayWay());
                    payWay.setPayName(PayWayEnum.getPayName(e.getPayWay()));
                }
                hsaMemberConsumptionPayWayMapper.insert(payWay);
            });
        }
    }

    @Override
    public HsaMemberInfoCard getHsaMemberInfoCard(RequestConfirmPayVO request, Integer isCredit, HsaOperationMemberInfo hsaOperationMemberInfo) {
        RequestBaseInfoDTO requestBaseInfo = request.getRequestBaseInfo();
        //挂账校验
        if (isCredit == BooleanEnum.TRUE.getCode() && Objects.nonNull(request.getIsCredit()) && request.getIsCredit() == BooleanEnum.TRUE.getCode()) {
            handleCreditBusiness(request, hsaOperationMemberInfo);
        }
        HsaMemberInfoCard memberInfoCard = getHsaMemberInfoCard(requestBaseInfo);
        log.info("当前会员卡======>{}", JSONObject.toJSONString(memberInfoCard));
        checkBusinessService.checkMemberCard(request, hsaOperationMemberInfo, requestBaseInfo, memberInfoCard);
        return memberInfoCard;
    }


    private HsaMemberInfoCard getHsaMemberInfoCard(RequestBaseInfoDTO requestBaseInfo) {
        HsaMemberInfoCard memberInfoCard = null;
        if (StringUtils.isNotBlank(requestBaseInfo.getMemberInfoCardGuid())) {
            memberInfoCard = hsaMemberInfoCardMapper.queryByGuid(requestBaseInfo.getMemberInfoCardGuid());
        } else if (StringUtils.isNotBlank(requestBaseInfo.getCardNum())) {
            memberInfoCard = hsaMemberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                    .eq(HsaMemberInfoCard::getOperSubjectGuid, requestBaseInfo.getOperSubjectGuid())
                    .eq(requestBaseInfo.getCardType() == CardTypeEnum.CARD_TYPE_EQUITY.getCode(), HsaMemberInfoCard::getElectronicCardNum, requestBaseInfo.getCardNum())
                    .eq(requestBaseInfo.getCardType() == CardTypeEnum.CARD_TYPE_MAIN.getCode(), HsaMemberInfoCard::getPhysicalCardNum, requestBaseInfo.getCardNum()));
        } else {
            if (StringUtils.isNotBlank(requestBaseInfo.getMemberInfoCardGuid()) && StringUtils.isNotBlank(requestBaseInfo.getCardNum())) {
                throw new MemberBaseException(systemRoleHelper.getReplace(MEMBER_CARD_NOT, ThreadLocalCache.getOperSubjectGuid()));
            }
        }
        return memberInfoCard;
    }

    private void handleCreditBusiness(RequestConfirmPayVO request, HsaOperationMemberInfo hsaOperationMemberInfo) {

        HsaCreditUser hsaCreditUser = hsaCreditUserMapper.queryByGuid(request.getCreditUserGuid());
        log.info("当前挂账用户信息======>{}", JSON.toJSONString(hsaCreditUser));
        if (ObjectUtils.isEmpty(hsaCreditUser))
            throw new MemberBaseException("挂账用户信息不存在");
        HsaCreditInfo hsaCreditInfo = hsaCreditInfoMapper.queryByGuid(hsaCreditUser.getCreditInfoGuid());
        log.info("当前挂账信息======>{}", JSON.toJSONString(hsaCreditInfo));
        checkBusinessService.checkCreditUserCondition(request, hsaCreditUser, hsaCreditInfo, hsaOperationMemberInfo);
        //可用金额
        BigDecimal remainingAmount;
        if (hsaCreditUser.getSinglePersonUpperLimit().compareTo(BigDecimal.ZERO) == NumberConstant.NUMBER_0) {
            remainingAmount = hsaCreditInfoService.getRemainingAmount(hsaCreditInfo.getGuid(), hsaCreditInfo.getCreditLimitedAmount(), hsaCreditInfo.getCreditLimitedSet());
        } else {
            remainingAmount = hsaCreditUser.getSinglePersonUpperLimit().subtract(hsaCreditUser.getTotalCredit());
        }
        checkRemainingAmount(request, remainingAmount);
        if (hsaCreditUser.getSingleCountUpperLimit().compareTo(BigDecimal.ZERO) > NumberConstant.NUMBER_0 && request.getCreditPayAmount().compareTo(hsaCreditUser.getSingleCountUpperLimit()) > NUMBER_0) {
            log.error("===========>单笔超过挂账上限：" + hsaCreditUser.getSingleCountUpperLimit());
            throw new MemberBaseException(MemberTerminalExceptionEnum.ERROR_CREDIT_SINGLE_OVER.getCode(),
                    MemberTerminalExceptionEnum.ERROR_CREDIT_SINGLE_OVER.getDes() + "账户单笔上限 ¥" + hsaCreditUser.getSingleCountUpperLimit());
        }
        BigDecimal totalCredit = Objects.nonNull(hsaCreditUser.getTotalCredit()) ?
                hsaCreditUser.getTotalCredit().add(request.getCreditPayAmount()) : request.getCreditPayAmount();
        hsaCreditUser.setTotalCredit(totalCredit);
        HsaCreditOrderRecord hsaCreditOrderRecord = MerchantPayAssembler.fromHsaCreditOrderRecord(
                request,
                hsaOperationMemberInfo,
                hsaCreditUser,
                hsaCreditInfo);
        hsaCreditOrderRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaCreditOrderRecord.class.getSimpleName()));
        HsaCreditFundingDetail hsaCreditFundingDetail = MerchantPayAssembler.fromHsaCreditFundingDetail(
                hsaCreditInfo,
                hsaCreditOrderRecord,
                NumberConstant.NUMBER_1,
                NUMBER_0);
        hsaCreditFundingDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaCreditFundingDetail.class.getSimpleName()));
        hsaCreditFundingDetailMapper.insert(hsaCreditFundingDetail);
        hsaCreditUserMapper.updateByGuid(hsaCreditUser);
        hsaCreditOrderRecordMapper.insert(hsaCreditOrderRecord);
        if (StringUtils.isEmpty(request.getRequestBaseInfo().getMemberInfoGuid())) {
            request.getRequestBaseInfo().setMemberInfoGuid(hsaCreditUser.getMemberInfoGuid());
        }
    }

    private void checkRemainingAmount(RequestConfirmPayVO request, BigDecimal remainingAmount) {
        if (remainingAmount.compareTo(new BigDecimal("-1")) != NUMBER_0 && request.getCreditPayAmount().compareTo(remainingAmount) > NUMBER_0) {
            log.error("===========>剩余挂账金额不足：" + remainingAmount);
            throw new MemberBaseException(MemberTerminalExceptionEnum.ERROR_CREDIT_INSUFFICIENT_AMOUNT_REMAINING.getCode(), MemberTerminalExceptionEnum.ERROR_CREDIT_INSUFFICIENT_AMOUNT_REMAINING.getDes() + "剩余挂账金额 ¥" + remainingAmount);
        }
    }

    /**
     * 设置最后消费时间
     */
    private void setLastConsumptionTime(HsaOperationMemberInfo memberInfo) {
        if (Objects.nonNull(memberInfo)) {
            //查找会员
            memberInfo.setLastConsumptionTime(LocalDateTime.now());
            // 修改会员最后消费时间
            hsaOperationMemberInfoMapper.updateByGuid(memberInfo);
        }
    }

    /**
     * 补贴金额优先扣减先到期的金额
     *
     * @param memberInfoCard      memberInfoCard
     * @param subsidyReduceAmount 补贴应当扣减金额
     */
    @Override
    public void subsidyMoneyReduceProcessor(HsaMemberInfoCard memberInfoCard, BigDecimal subsidyReduceAmount) {
        List<HsaMemberFundingDetail> hsaMemberFundingDetailList = hsaMemberFundingDetailMapper.selectList(new LambdaQueryWrapper<HsaMemberFundingDetail>()
                .eq(HsaMemberFundingDetail::getMemberInfoGuid, memberInfoCard.getMemberInfoGuid())
                .eq(HsaMemberFundingDetail::getAmountSubsidyFundingType, 0)
                .eq(HsaMemberFundingDetail::getAmountSourceType, AmountSourceTypeEnum.SUBSIDY_GRANT.getCode())
                .eq(HsaMemberFundingDetail::getMemberInfoCardGuid, memberInfoCard.getGuid()));
        //需要更新的补贴明细记录剩余金额
        List<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords = Lists.newArrayList();
        if (CollUtil.isNotEmpty(hsaMemberFundingDetailList)) {
            //无过期时间
            List<HsaMemberFundingDetail> permanentMemberFundingDetailList = Lists.newArrayList();
            //有效期限
            List<HsaMemberFundingDetail> deadlineMemberFundingDetailList = Lists.newArrayList();
            //分类补贴金
            for (HsaMemberFundingDetail in : hsaMemberFundingDetailList) {
                if (in.getAmountSourceType() == AmountSourceTypeEnum.SUBSIDY_GRANT.getCode() && Objects.nonNull(in.getOutOfDate())) {
                    deadlineMemberFundingDetailList.add(in);
                } else {
                    permanentMemberFundingDetailList.add(in);
                }
            }
            //获取补贴明细记录Guid
            Map<String, HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecordMap = getStringHsaSubsidyActivityDetailRecordMap(BooleanEnum.TRUE.getCode(), hsaMemberFundingDetailList);
            if (CollUtil.isNotEmpty(hsaSubsidyActivityDetailRecordMap)) {
                dealSubsidyActivityDetailRecord(memberInfoCard, subsidyReduceAmount, deadlineMemberFundingDetailList, hsaSubsidyActivityDetailRecords, hsaSubsidyActivityDetailRecordMap, permanentMemberFundingDetailList);
            } else {
                memberInfoCard.setSubsidyAmount(memberInfoCard.getSubsidyAmount().subtract(subsidyReduceAmount));
            }
        } else {
            memberInfoCard.setSubsidyAmount(memberInfoCard.getSubsidyAmount().subtract(subsidyReduceAmount));
        }
    }

    private void dealSubsidyActivityDetailRecord(HsaMemberInfoCard memberInfoCard, BigDecimal subsidyReduceAmount, List<HsaMemberFundingDetail> deadlineMemberFundingDetailList, List<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords, Map<String, HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecordMap, List<HsaMemberFundingDetail> permanentMemberFundingDetailList) {
        SubsidyAbatementBalanceDTO subsidyAbatementBalanceDTO = new SubsidyAbatementBalanceDTO();
        subsidyAbatementBalanceDTO.setSubsidyAmount(subsidyReduceAmount);
        if (CollUtil.isNotEmpty(deadlineMemberFundingDetailList)) {
            listSort(deadlineMemberFundingDetailList);
            //遍历依次扣减
            checkSubsidyMoneyAbatement(hsaSubsidyActivityDetailRecords, hsaSubsidyActivityDetailRecordMap, deadlineMemberFundingDetailList, subsidyAbatementBalanceDTO);
        }
        //永久有效的补贴明细记录
        //若有效期时间的补贴记录扣减不够则永久有效的资金继续扣减
        if (CollUtil.isNotEmpty(permanentMemberFundingDetailList)
                && subsidyAbatementBalanceDTO.getSubsidyAmount().compareTo(BigDecimal.ZERO) > 0) {
            checkSubsidyMoneyAbatement(hsaSubsidyActivityDetailRecords, hsaSubsidyActivityDetailRecordMap, permanentMemberFundingDetailList, subsidyAbatementBalanceDTO);
        }
        //持卡数据可退补贴金额实际扣减
        memberInfoCard.setRetreatSubsidyAmount(memberInfoCard.getRetreatSubsidyAmount().subtract(subsidyAbatementBalanceDTO.getSubsidyAmountBack()).compareTo(BigDecimal.ZERO) > 0 ?
                memberInfoCard.getRetreatSubsidyAmount().subtract(subsidyAbatementBalanceDTO.getSubsidyAmountBack()) : BigDecimal.ZERO);
        //持卡数据补贴金额实际扣减
        memberInfoCard.setSubsidyAmount(memberInfoCard.getSubsidyAmount().subtract(subsidyAbatementBalanceDTO.getSubsidyAmountNotBack()));
        //若补贴发放的金额不足以扣减
        if (subsidyAbatementBalanceDTO.getSubsidyAmountNotBack().compareTo(subsidyReduceAmount) < 0) {
            BigDecimal count = subsidyReduceAmount.subtract(subsidyAbatementBalanceDTO.getSubsidyAmountNotBack());
            memberInfoCard.setSubsidyAmount(memberInfoCard.getSubsidyAmount().subtract(count));
        }
        //持久化
        if (CollUtil.isNotEmpty(hsaSubsidyActivityDetailRecords)) {
            hsaSubsidyActivityDetailRecords.forEach(hsaSubsidyActivityDetailRecordService::updateByGuid);
        }
    }

    @Override
    public Boolean checkCardPayPassword(TerCheckPassword terCheckPassword) {
        HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMapper.queryByGuid(terCheckPassword.getMemberInfoCardGuid());
        return getBoolean(terCheckPassword, hsaMemberInfoCard);
    }

    @Override
    public Boolean checkCardPayPasswordRule() {
        HsaCardBalanceRule hsaCardBalanceRule = hsaCardBalanceRuleMapper.selectOne(new LambdaQueryWrapper<HsaCardBalanceRule>()
                .eq(HsaCardBalanceRule::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
        if (Objects.isNull(hsaCardBalanceRule)) {
            return true;
        }
        return hsaCardBalanceRule.getUseCheck() == NumberConstant.NUMBER_1;
    }

    @Override
    public ConsumptionRespVO cashPayOrder(RequestConfirmPayVO requestConfirmPay) {
        log.info("现金支付参数:{}", requestConfirmPay);

        // 1. 初始化基础数据
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        String memberConsumptionGuid = initializeMemberConsumptionGuid(requestConfirmPay);

        // 2. 验证并获取会员信息
        HsaOperationMemberInfo hsaOperationMemberInfo = validateAndGetMemberInfo(requestConfirmPay.getRequestBaseInfo());

        // 3. 保存消费记录
        HsaMemberConsumption memberConsumption = saveConsumptionRecord(requestConfirmPay, headerUserInfo, hsaOperationMemberInfo);

        // 4. 处理业务逻辑
        processCashPayOrderBusiness(requestConfirmPay, memberConsumptionGuid, memberConsumption, hsaOperationMemberInfo);

        // 5. 构建并返回响应结果
        return buildConsumptionResponse(requestConfirmPay.getRequestBaseInfo(), memberConsumptionGuid, hsaOperationMemberInfo);
    }

    /**
     * 初始化会员消费记录GUID
     *
     * @param requestConfirmPay 支付确认请求
     * @return 会员消费记录GUID
     */
    private String initializeMemberConsumptionGuid(RequestConfirmPayVO requestConfirmPay) {
        String memberConsumptionGuid = guidGeneratorUtil.getStringGuid(HsaMemberConsumption.class.getSimpleName());
        requestConfirmPay.setMemberConsumptionGuid(memberConsumptionGuid);
        return memberConsumptionGuid;
    }

    /**
     * 验证并获取会员信息
     *
     * @param requestBaseInfo 请求基础信息
     * @return 会员操作信息
     */
    private HsaOperationMemberInfo validateAndGetMemberInfo(RequestBaseInfoDTO requestBaseInfo) {
        HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();

        // 如果请求包含会员信息，则查询并验证会员状态
        if (Objects.nonNull(requestBaseInfo) && StringUtils.isNotBlank(requestBaseInfo.getMemberInfoGuid())) {
            hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(requestBaseInfo.getMemberInfoGuid());

            // 检查会员账户状态
            if (hsaOperationMemberInfo.getAccountState() == 1) {
                throw new MemberBaseException(systemRoleHelper.getReplace(
                    MemberTerminalExceptionEnum.GLOBAL_BAD_STATUS,
                    ThreadLocalCache.getOperSubjectGuid()));
            }
        }

        return hsaOperationMemberInfo;
    }

    /**
     * 保存消费记录
     *
     * @param requestConfirmPay 支付确认请求
     * @param headerUserInfo 请求头用户信息
     * @param hsaOperationMemberInfo 会员操作信息
     * @return 会员消费记录
     */
    private HsaMemberConsumption saveConsumptionRecord(RequestConfirmPayVO requestConfirmPay,
                                                      HeaderUserInfo headerUserInfo,
                                                      HsaOperationMemberInfo hsaOperationMemberInfo) {
        return hsaMemberConsumptionService.saveConsumptionRecord(
            requestConfirmPay,
            null,
            headerUserInfo,
            null,
            hsaOperationMemberInfo
        );
    }

    /**
     * 处理现金支付订单业务逻辑
     *
     * @param requestConfirmPay 支付确认请求
     * @param memberConsumptionGuid 会员消费记录GUID
     * @param memberConsumption 会员消费记录
     * @param hsaOperationMemberInfo 会员操作信息
     */
    private void processCashPayOrderBusiness(RequestConfirmPayVO requestConfirmPay,
                                           String memberConsumptionGuid,
                                           HsaMemberConsumption memberConsumption,
                                           HsaOperationMemberInfo hsaOperationMemberInfo) {
        // 1. 更新最后消费时间
        setLastConsumptionTime(hsaOperationMemberInfo);

        // 2. 异步保存支付方式
        memberBaseThreadExecutor.execute(() ->
            savePayWay(requestConfirmPay.getRequestPayInfoList(), memberConsumptionGuid)
        );

        // 3. 发布消费订单限购支付事件
        publishPurchaseOrderStateEvent(memberConsumption);

        // 4. 刷新会员标签
        refreshMemberLabelsIfNeeded(hsaOperationMemberInfo);
    }

    /**
     * 发布消费订单限购支付事件
     *
     * @param memberConsumption 会员消费记录
     */
    private void publishPurchaseOrderStateEvent(HsaMemberConsumption memberConsumption) {
        PurchaseOrderStateDto orderStateDto = PurchaseOrderStateDto.builder()
                .orderState(ConsumptionOrderStateEnum.PAY.getCode())
                .orderNumber(memberConsumption.getOrderNumber())
                .consumptionOrderGuid(memberConsumption.getGuid())
                .operSubjectGuid(memberConsumption.getOperSubjectGuid())
                .build();

        consumptionOrderPublisher.publish(
            ConsumptionOrderEventEnum.PURCHASE_ORDER_STATE,
            JacksonUtils.writeValueAsString(orderStateDto)
        );
    }

    /**
     * 刷新会员标签（如果需要）
     *
     * @param hsaOperationMemberInfo 会员操作信息
     */
    private void refreshMemberLabelsIfNeeded(HsaOperationMemberInfo hsaOperationMemberInfo) {
        // 如果会员信息存在，则刷新标签
        if (StringUtils.isNotBlank(hsaOperationMemberInfo.getGuid())) {
            hsaLabelSettingService.refreshLabel(
                Lists.newArrayList(hsaOperationMemberInfo.getGuid()),
                null,
                BooleanEnum.FALSE.getCode(),
                null,
                LabelTriggerTypeEnum.CONSUMPTION_INFO.getCode()
            );
        }
    }

    /**
     * 构建消费响应结果
     *
     * @param requestBaseInfo 请求基础信息
     * @param memberConsumptionGuid 会员消费记录GUID
     * @param hsaOperationMemberInfo 会员操作信息
     * @return 消费响应VO
     */
    private ConsumptionRespVO buildConsumptionResponse(RequestBaseInfoDTO requestBaseInfo,
                                                     String memberConsumptionGuid,
                                                     HsaOperationMemberInfo hsaOperationMemberInfo) {
        ConsumptionRespVO consumptionRespVO = new ConsumptionRespVO();

        // 设置会员手机号
        consumptionRespVO.setMemberPhone(
            Objects.nonNull(hsaOperationMemberInfo) ? hsaOperationMemberInfo.getPhoneNum() : null
        );

        // 设置消费记录GUID
        consumptionRespVO.setMemberConsumptionGuid(memberConsumptionGuid);

        // 设置门店名称
        if (Objects.nonNull(requestBaseInfo)) {
            consumptionRespVO.setStoreName(requestBaseInfo.getStoreName());
        }

        return consumptionRespVO;
    }

    /**
     * 消费权益回调
     *
     * @param terOrderCallbackQO memberConsumptionGuid
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    @RedissonLock(lockName = "RIGHTS_CALLBACK", tryLock = true, leaseTime = 10)
    public Integer payOrderRightsCallback(TerOrderCallbackQO terOrderCallbackQO) {
        log.info("消费权益回调{}", terOrderCallbackQO.getOrderNum());
        HsaMemberConsumption hsaMemberConsumption = payService.rightsCallback(terOrderCallbackQO);
        if (Objects.isNull(hsaMemberConsumption)) {
            return BooleanEnum.FALSE.getCode();
        }
        log.info("此订单已完成，memberConsumptionGuid={}", hsaMemberConsumption);

        //用餐点评提醒 门店名称:thing1,消费金额:amount6,用餐时间:time7,点评提醒:thing5
        sendEvaluateMsg(hsaMemberConsumption);

        final String userStr = ThreadLocalCache.get();
        memberBaseThreadExecutor.execute(() -> {
            ThreadLocalCache.put(userStr);
            sendGrowthEvent(hsaMemberConsumption);
        });
        memberBaseThreadExecutor.execute(() -> {
                    ThreadLocalCache.put(userStr);
                    sendIntegralEvent(hsaMemberConsumption);
                }
        );
        // 如果是零售来的订单 才需要去处理订单完成分销逻辑
        if (ThreadLocalCache.getSystem() == SystemEnum.RETAIL.getCode()) {
            memberBaseThreadExecutor.execute(() -> {
                ThreadLocalCache.put(userStr);
                sendDistributeEvent(hsaMemberConsumption, null, DistributeTypeEnum.NOT_REFUND);
            });
        }

        return BooleanEnum.TRUE.getCode();
    }

    @Override
    public void afterOrderDiscountCallback(AfterOrderDiscountCallbackQO afterOrderDiscountCallbackQO) {
        log.info("订单完成优惠回调参数:{}", JSON.toJSONString(afterOrderDiscountCallbackQO));
        OrderPayDiscountDTO orderPayDiscountDTO = new OrderPayDiscountDTO();

        BeanUtils.copyProperties(afterOrderDiscountCallbackQO, orderPayDiscountDTO);
        orderPayDiscountDTO.setOrderNo(afterOrderDiscountCallbackQO.getOrderNo());
        orderPayDiscountDTO.setOperSubjectGuid(afterOrderDiscountCallbackQO.getOperSubjectGuid());
        orderPayDiscountDTO.setPayTime(LocalDateTime.now());
        orderPayDiscountDTO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        if (StringUtils.isNotEmpty(afterOrderDiscountCallbackQO.getMemberGuid())) {
            HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(afterOrderDiscountCallbackQO.getMemberGuid());
            orderPayDiscountDTO.setOperSubjectGuid(hsaOperationMemberInfo.getOperSubjectGuid());
            orderPayDiscountDTO.setMemberInfoGuid(hsaOperationMemberInfo.getGuid());
            orderPayDiscountDTO.setUserName(hsaOperationMemberInfo.getUserName());
            orderPayDiscountDTO.setPhoneNum(hsaOperationMemberInfo.getPhoneNum());
            orderPayDiscountDTO.setMemberIntegral(hsaOperationMemberInfo.getMemberIntegral());
        }
        //支付后优惠处理
        settlementCmdService.afterOrderDiscount(orderPayDiscountDTO);
    }

    @Override
    public void barkOrderDiscountCallback(BarkOrderDiscountCallbackQO barkOrderDiscountCallbackQO) {
        long startTime = System.currentTimeMillis();
        // 调用接口
        barkOrderDiscountCallbackQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        log.info("订单退款优惠回调参数:{}", JSON.toJSONString(barkOrderDiscountCallbackQO));
        settlementCmdService.barkOrderDiscount(barkOrderDiscountCallbackQO);
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        log.info("接口调用耗时: {} ms", duration);
    }

    private void sendEvaluateMsg(HsaMemberConsumption hsaMemberConsumption) {
        if (StringUtils.isNotBlank(hsaMemberConsumption.getMemberInfoGuid())) {
            HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(hsaMemberConsumption.getMemberInfoGuid());
            //发送消息
            // 模版替换：餐厅名称:thing8,消费金额:amount6,消费时间:date5,点评提醒:thing7
            MessagesSendQO messagesSendQO = new MessagesSendQO();
            Map<String, String> prams = new HashMap<>();
            prams.put("thing8", hsaMemberConsumption.getStoreName());
            prams.put("amount6", hsaMemberConsumption.getOrderPaidAmount() + "");
            prams.put("date5", ValidTimeUtils.getDate());
            prams.put("thing7", "请您对餐厅服务进行评价!");
            messagesSendQO.setTemplateName("用餐点评提醒");
            messagesSendQO.setPrams(prams);
            dealSendBase(hsaMemberConsumption, hsaOperationMemberInfo, messagesSendQO);
        }
    }

    private void sendIntegralEvent(HsaMemberConsumption hsaMemberConsumption) {
        IntegralChangeDTO event = IntegralEventAssembler.getIntegralChangeDTO(hsaMemberConsumption);

        //获取卡支付金额
        HsaMemberConsumptionPayWay hsaMemberConsumptionPayWay = hsaMemberConsumptionPayWayMapper.selectOne(new LambdaQueryWrapper<HsaMemberConsumptionPayWay>()
                .eq(HsaMemberConsumptionPayWay::getConsumptionGuid, hsaMemberConsumption.getGuid())
                .eq(HsaMemberConsumptionPayWay::getPayWay, PayWayEnum.CARD_BALANCE_PAY.getCode()));

        if (Objects.nonNull(hsaMemberConsumptionPayWay)) {
            event.setCardConsumptionAmount(hsaMemberConsumptionPayWay.getPayAmount());
        } else {
            event.setCardConsumptionAmount(BigDecimal.ZERO);
        }

        if (StringUtils.isEmpty(hsaMemberConsumption.getMemberInfoGuid())) {
            log.info("会员卡消费完成，会员卡GUID为空，不处理积分赠送");
            return;
        }

        if (StringUtils.isNotEmpty(hsaMemberConsumption.getMemberInfoCardGuid())) {
            HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMapper.queryByGuid(hsaMemberConsumption.getMemberInfoCardGuid());
            event.setCardGuid(hsaMemberInfoCard.getCardGuid());
        }

        HsaOperationMemberInfo memberInfo = hsaOperationMemberInfoMapper.queryByGuid(hsaMemberConsumption.getMemberInfoGuid());
        HeaderUserInfo userInfo = IntegralEventAssembler.getIntegralUserInfo(memberInfo);
        event.setUserInfo(userInfo);

        integralTaskProcessorService.consumptionTaskBusinessProcessor(event);
        log.info("积分消费赠送处理完成event={}", event);
    }


    @Override
    public List<TerCardDetailsVO> queryCardDetails(String memberInfoGuid, String storeGuid) {
        List<HsaMemberInfoCard> hsaMemberInfoCardList = hsaMemberInfoCardMapper.selectList(
                new LambdaQueryWrapper<HsaMemberInfoCard>()
                        .eq(HsaMemberInfoCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                        .eq(HsaMemberInfoCard::getMemberInfoGuid, memberInfoGuid));
        if (CollUtil.isEmpty(hsaMemberInfoCardList)) {
            return Lists.newArrayList();
        }
        //会员卡基础信息集合
        Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap = getHsaCardBaseInfoMap(hsaMemberInfoCardList);
        //会员卡可退金额集合
        Map<String, BigDecimal> returnAmountMap = Maps.newHashMap();
        //会员卡开卡规则信息
        Map<String, ReturnCardRuleDTO> returnCardRuleMap = Maps.newHashMap();
        getOpenCardRuleInfo(hsaMemberInfoCardList, returnAmountMap, returnCardRuleMap);
        //当前用户拥有的实体卡信息
        Map<String, HsaPhysicalCard> physicalCardMap = getPhysicalCardMap(hsaMemberInfoCardList);
        //当前用户拥有的电子卡信息
        Map<String, HsaElectronicCard> electronicCardMap = getElectronicCardMap(hsaMemberInfoCardList);

        List<TerCardDetailsVO> terCardDetailsVos = Lists.newArrayList();
        for (HsaMemberInfoCard hsaMemberInfoCard : hsaMemberInfoCardList) {
            //一体机会员卡详情处理
            TerCardDetailsVO terCardDetailsVo = terCardDetailsHandler(hsaMemberInfoCard, returnCardRuleMap);
            //设置会员卡剩余可以超额次数或者金额以及会员卡基础信息
            MemberCardAssembler.setCardExcess(terCardDetailsVo, hsaMemberInfoCard, hsaCardBaseInfoMap);
            //设置会员卡过期时间
            MemberCardAssembler.setCardOverdueTime(terCardDetailsVo, hsaMemberInfoCard, physicalCardMap, electronicCardMap, hsaCardBaseInfoMap);
            //设置会员卡是否适用门店
            setStoreInfo(terCardDetailsVo, hsaMemberInfoCard, storeGuid);
            //设置会员卡可退金额
            setRefundAmount(terCardDetailsVo, hsaMemberInfoCard, returnAmountMap);
            terCardDetailsVos.add(terCardDetailsVo);
        }
        //过滤过期会员卡，以及排序
        return terCardDetailsVos.stream().filter(obj ->
                        (ObjectUtil.notEqual(obj.getElectronicCardState(), EletronicCardStateEnum.HAVE_EXPIRED.getCode())
                                && ObjectUtil.isNotNull(obj.getElectronicCardState()))
                                || (ObjectUtil.notEqual(obj.getPhysicalCardState(), PhysicalCardStateEnum.HAVE_EXPIRED.getCode())
                                && ObjectUtil.isNotNull(obj.getPhysicalCardState())))
                .sorted(Comparator.comparing(TerCardDetailsVO::getGmtCreate))
                .sorted(Comparator.comparing(TerCardDetailsVO::getDefaultCard).reversed())
                .collect(Collectors.toList());
    }

    @Override
    public PageResult getMemberUploadPage(MemberCardExcelQO qo) {
        PageHelper.startPage(qo.getCurrentPage(), qo.getPageSize());
        List<HsaMemberCardExcel> list = hsaMemberCardExcelMapper.selectList(new LambdaQueryWrapper<HsaMemberCardExcel>()
                .orderByAsc(HsaMemberCardExcel::getCardNum));
        return PageUtil.getPageResult(new PageInfo<>(list));
    }

    @Override
    public List<EndConsumptionInfoDTO> queryMemberOrder(EndConsumptionInfoQO request) {
        return new ArrayList<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cardBindingUid(TerCardUidQO terCardUidQO) {
        HsaPhysicalCard hsaPhysicalCard = hsaPhysicalCardMapper.selectOne(new LambdaQueryWrapper<HsaPhysicalCard>()
                .eq(HsaPhysicalCard::getCardNum, terCardUidQO.getCardNum())
                .eq(HsaPhysicalCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));

        if (Objects.isNull(hsaPhysicalCard)) {
            throw new MemberBaseException("实体卡不存在");
        }

        //之前绑定过
        HsaPhysicalCard physicalCard = Optional.ofNullable(getHsaPhysicalCard(terCardUidQO.getUid())).orElse(new HsaPhysicalCard());

        if (StringUtils.isNotEmpty(physicalCard.getGuid()) && !physicalCard.getGuid().equals(hsaPhysicalCard.getGuid())) {
            //清空
            physicalCard.setUid(null);
            hsaPhysicalCardMapper.updateByGuid(physicalCard);
        }

        hsaPhysicalCard.setUid(terCardUidQO.getUid());
        return hsaPhysicalCardMapper.updateByGuid(hsaPhysicalCard);
    }

    @Override
    public String getCardNumByUid(String uid) {
        HsaPhysicalCard hsaPhysicalCard = getHsaPhysicalCard(uid);

        return Objects.nonNull(hsaPhysicalCard) ? hsaPhysicalCard.getCardNum() : null;
    }

    private HsaPhysicalCard getHsaPhysicalCard(String uid) {
        return hsaPhysicalCardMapper.selectOne(new LambdaQueryWrapper<HsaPhysicalCard>()
                .eq(HsaPhysicalCard::getUid, uid)
                .eq(HsaPhysicalCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
    }

    private Map<String, HsaElectronicCard> getElectronicCardMap(List<HsaMemberInfoCard> hsaMemberInfoCardList) {
        List<String> electronicCardGuids = hsaMemberInfoCardList.stream()
                .map(HsaMemberInfoCard::getElectronicCardGuid)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<HsaElectronicCard> hsaElectronicCards = hsaElectronicCardMapper.selectList(new LambdaQueryWrapper<HsaElectronicCard>()
                .eq(HsaElectronicCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .in(HsaElectronicCard::getGuid, electronicCardGuids));
        return hsaElectronicCards.stream()
                .collect(Collectors.toMap(HsaElectronicCard::getGuid, Function.identity(), (obj, obj1) -> obj));
    }

    private Map<String, HsaPhysicalCard> getPhysicalCardMap(List<HsaMemberInfoCard> hsaMemberInfoCardList) {
        List<String> physicalCardGuids = hsaMemberInfoCardList.stream()
                .map(HsaMemberInfoCard::getPhysicalCardGuid)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<HsaPhysicalCard> hsaPhysicalCards = hsaPhysicalCardMapper.selectList(new LambdaQueryWrapper<HsaPhysicalCard>()
                .eq(HsaPhysicalCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .in(HsaPhysicalCard::getGuid, physicalCardGuids));
        return hsaPhysicalCards.stream()
                .collect(Collectors.toMap(HsaPhysicalCard::getGuid, Function.identity(), (obj, obj1) -> obj));
    }

    private void getOpenCardRuleInfo(List<HsaMemberInfoCard> hsaMemberInfoCardList,
                                     Map<String, BigDecimal> returnAmountMap, Map<String, ReturnCardRuleDTO> returnCardRuleMap) {
        List<String> cardGuids = hsaMemberInfoCardList.stream()
                .map(HsaMemberInfoCard::getCardGuid).distinct().collect(Collectors.toList());
        List<ReturnCardRuleDTO> returnCardRules = hsaCardOpenRuleMapper.queryReturnAmount(cardGuids, ThreadLocalCache.getOperSubjectGuid());
        if (CollUtil.isEmpty(returnCardRules)) {
            return;
        }
        //获取开关规则相关数据
        Map<String, ReturnCardRuleDTO> collect = returnCardRules.stream().collect(Collectors.toMap(
                ReturnCardRuleDTO::getCardGuid, Function.identity(), (obj, obj1) -> obj));
        returnCardRuleMap.putAll(collect);

        //获取实体卡退卡金额Map
        Map<String, BigDecimal> collect1 = returnCardRules.stream().filter(x -> Objects.nonNull(x.getRefundAmount())).collect(Collectors.toMap(
                ReturnCardRuleDTO::getCardGuid, ReturnCardRuleDTO::getRefundAmount));
        returnAmountMap.putAll(collect1);
    }

    private Map<String, HsaCardBaseInfo> getHsaCardBaseInfoMap(List<HsaMemberInfoCard> hsaMemberInfoCardList) {
        //会员卡基础guids
        List<String> cardBaseGuids = hsaMemberInfoCardList.stream()
                .map(HsaMemberInfoCard::getCardGuid).distinct().collect(Collectors.toList());
        List<HsaCardBaseInfo> hsaCardBaseInfos = hsaCardBaseInfoMapper.selectList(new LambdaQueryWrapper<HsaCardBaseInfo>()
                .eq(HsaCardBaseInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .in(HsaCardBaseInfo::getGuid, cardBaseGuids));
        return hsaCardBaseInfos.stream()
                .collect(Collectors.toMap(HsaCardBaseInfo::getGuid, Function.identity(), (obj, obj1) -> obj));
    }

    /**
     * 一体机会员卡详情处理
     *
     * @param hsaMemberInfoCard 会员卡关联表
     * @return 一体机会员卡详情信息
     */
    private TerCardDetailsVO terCardDetailsHandler(HsaMemberInfoCard hsaMemberInfoCard,
                                                   Map<String, ReturnCardRuleDTO> returnCardRuleMap) {
        TerCardDetailsVO terCardDetailsVo = new TerCardDetailsVO();
        //电子卡信息封装
        MemberCardAssembler.electronicCardWrapper(terCardDetailsVo, hsaMemberInfoCard, returnCardRuleMap);
        //过期余额
        terCardDetailsVo.setOverdueAmount(getOverdueAmount(hsaMemberInfoCard));
        //实体卡信息封装
        physicalCardWrapper(terCardDetailsVo, hsaMemberInfoCard);
        return terCardDetailsVo;
    }

    private void setRefundAmount(TerCardDetailsVO terCardDetailsVo, HsaMemberInfoCard hsaMemberInfoCard,
                                 Map<String, BigDecimal> returnAmountMap) {
        String cardGuid = Optional.of(hsaMemberInfoCard).map(HsaMemberInfoCard::getCardGuid).orElse("");
        if (Objects.nonNull(returnAmountMap) && returnAmountMap.containsKey(cardGuid)) {
            terCardDetailsVo.setRefundAmount(returnAmountMap.get(cardGuid));
        }

    }


    /**
     * 设置会员卡是否适用门店
     *
     * @param terCardDetailsVo  会员卡详情
     * @param hsaMemberInfoCard 会员卡关联数据表
     * @param storeGuid         当前门店guid
     */
    private void setStoreInfo(TerCardDetailsVO terCardDetailsVo, HsaMemberInfoCard hsaMemberInfoCard, String storeGuid) {
        terCardDetailsVo.setIsStoreFit(Boolean.FALSE);
        String cardGuid = Optional.of(hsaMemberInfoCard).map(HsaMemberInfoCard::getCardGuid).orElse("");
        //设置门店范围
        int applicableAllStore = Optional.of(hsaMemberInfoCard)
                .map(HsaMemberInfoCard::getApplicableAllStore).orElse(-1);
        //全部适用门店
        if (applicableAllStore == BooleanEnum.TRUE.getCode()) {
            terCardDetailsVo.setIsStoreFit(Boolean.TRUE);
            terCardDetailsVo.setStoreNum(ALL_STORE_MESSAGE);
        }
        //部分适用门店
        if (applicableAllStore == BooleanEnum.FALSE.getCode()) {
            List<StoreCardRuleQO> storeCardRuleQOList = hsaMemberInfoCardService.getStoreCardRule(Collections.
                    singletonList(cardGuid), hsaMemberInfoCard.getGuid());
            if (CollUtil.isEmpty(storeCardRuleQOList)) {
                return;
            }
            Optional<StoreCardRuleQO> first = storeCardRuleQOList.stream()
                    .filter(x -> StringUtils.isNotBlank(x.getStoreGuid()) && x.getStoreGuid().equals(storeGuid))
                    .findFirst();
            if (first.isPresent()) {
                terCardDetailsVo.setIsStoreFit(Boolean.TRUE);
            } else {
                //判断当前档口是否使用门店
                terCardDetailsVo.setIsStoreFit(isStallFit(storeCardRuleQOList, storeGuid));
            }
            String message = String.format(USE_STORE_MESSAGE, storeCardRuleQOList.size()).intern();
            terCardDetailsVo.setStoreNum(message);
        }
    }

    /**
     * 判断当前档口是否使用门店
     *
     * @param storeCardRuleList 门店信息集合
     * @param storeGuid         当前门店
     * @return true:适用  false:不适用
     */
    private boolean isStallFit(List<StoreCardRuleQO> storeCardRuleList, String storeGuid) {
        if (CollUtil.isEmpty(storeCardRuleList)) {
            return Boolean.FALSE;
        }
        for (StoreCardRuleQO storeCardRule : storeCardRuleList) {
            List<StoreBoothCardRuleQO> storeBoothCardRuleList = storeCardRule.getStoreBoothCardRuleQOList();
            if (CollUtil.isEmpty(storeBoothCardRuleList)) {
                continue;
            }
            Optional<StoreBoothCardRuleQO> first = storeBoothCardRuleList.stream()
                    .filter(x -> StringUtils.isNotBlank(x.getStoreGuid()) && x.getStoreGuid().equals(storeGuid))
                    .findFirst();
            if (first.isPresent()) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 实体卡信息封装
     *
     * @param terCardDetailsVo  会员卡详情信息
     * @param hsaMemberInfoCard 会员卡关联表对象
     */
    private void physicalCardWrapper(TerCardDetailsVO terCardDetailsVo, HsaMemberInfoCard hsaMemberInfoCard) {
        terCardDetailsVo.setPhysicalCardNum(hsaMemberInfoCard.getPhysicalCardNum());
        terCardDetailsVo.setPhysicalCardState(hsaMemberInfoCard.getPhysicalCardState());
        terCardDetailsVo.setPhysicalCardActivationTime(hsaMemberInfoCard.getPhysicalCardActivationTime());
        terCardDetailsVo.setDefaultCard(NUMBER_0);
        terCardDetailsVo.setGmtCreate(hsaMemberInfoCard.getGmtCreate());
        if (Objects.nonNull(hsaMemberInfoCard.getDefaultCard())) {
            terCardDetailsVo.setDefaultCard(hsaMemberInfoCard.getDefaultCard());
        }
    }

    /**
     * 过期余额处理
     *
     * @param hsaMemberInfoCard 会员卡关联表
     * @return 过期余额金额
     */
    private BigDecimal getOverdueAmount(HsaMemberInfoCard hsaMemberInfoCard) {
        List<String> subsidyDetailRecordGuids = hsaMemberFundingDetailMapper.soonExpiredSubsidyAmount(hsaMemberInfoCard.getGuid());
        if (CollectionUtils.isEmpty(subsidyDetailRecordGuids)) {
            return BigDecimal.ZERO;
        }
        //即将过期余额
        return hsaSubsidyActivityDetailRecordMapper.findAllSoonOverdueSubsidy(subsidyDetailRecordGuids);
    }

    private void sendGrowthEvent(HsaMemberConsumption hsaMemberConsumption) {
        if (gradeStateService.isNotEnable(hsaMemberConsumption.getOperSubjectGuid())) {
            return;
        }
        EventGrowthChangeDTO eventGrowthChangeDTO = new EventGrowthChangeDTO();
        BeanUtils.copyProperties(hsaMemberConsumption, eventGrowthChangeDTO);
        GrowthValueChangeEvent event = getGrowthValueChangeEvent(eventGrowthChangeDTO);
        log.info("消费成长值任务处理：{}", event);
        growthValueEvent.send(event);
    }

    private void sendDistributeEvent(HsaMemberConsumption hsaMemberConsumption, String newConsumptionGuid, DistributeTypeEnum distributeTypeEnum) {
        if (gradeStateService.isNotEnable(hsaMemberConsumption.getOperSubjectGuid())) {
            return;
        }
        SendMemberConsumptionDistributeEvent event = BeanUtil.copyProperties(hsaMemberConsumption, SendMemberConsumptionDistributeEvent.class);
        event.setConsumptionGuid(hsaMemberConsumption.getGuid());
        event.setDistributeRecordType(distributeTypeEnum);
        event.setNewConsumptionGuid(newConsumptionGuid);
        log.info("订单分销任务处理：{}", event);
        distributeEvent.send(event);
    }


    private GrowthValueChangeEvent getGrowthValueChangeEvent(EventGrowthChangeDTO eventGrowthChangeDTO) {
        GrowthValueChangeEvent event = getValueChangeEvent(eventGrowthChangeDTO);

        setUserInfo(eventGrowthChangeDTO, event);

        if (StringUtils.isNotBlank(eventGrowthChangeDTO.getStoreGuid())) {
            event.setStoreGuid(org.springframework.util.StringUtils.isEmpty(eventGrowthChangeDTO.getStallGuid())
                    ? eventGrowthChangeDTO.getStoreGuid() : eventGrowthChangeDTO.getStallGuid());
            event.setStoreName(eventGrowthChangeDTO.getStoreName());
        }
        event.setSource(eventGrowthChangeDTO.getOrderSource());
        event.setOperatorAccountName(eventGrowthChangeDTO.getOperatorTelName());

        if (StringUtils.isNotBlank(eventGrowthChangeDTO.getMemberInfoCardGuid())) {
            HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMapper.queryByGuid(eventGrowthChangeDTO.getMemberInfoCardGuid());
            event.setCardGuid(hsaMemberInfoCard.getCardGuid());
        }
        return event;
    }

    private void setUserInfo(EventGrowthChangeDTO eventGrowthChangeDTO, GrowthValueChangeEvent event) {
        HsaOperationMemberInfo memberInfo = hsaOperationMemberInfoMapper.queryByGuid(eventGrowthChangeDTO.getMemberInfoGuid());
        HeaderUserInfo userInfo = new HeaderUserInfo();
        userInfo.setOperSubjectGuid(memberInfo.getOperSubjectGuid());
        userInfo.setUserName(memberInfo.getUserName());
        userInfo.setTel(memberInfo.getPhoneNum());
        userInfo.setUserGuid(memberInfo.getGuid());
        event.setUserInfo(userInfo);
    }

    private static GrowthValueChangeEvent getValueChangeEvent(EventGrowthChangeDTO eventGrowthChangeDTO) {
        GrowthValueChangeEvent event = new GrowthValueChangeEvent();
        event.setMemberInfoGuidList(Collections.singletonList(eventGrowthChangeDTO.getMemberInfoGuid()));
        event.setMemberConsumptionGuid(eventGrowthChangeDTO.getGuid());
        event.setChangeType(SumValueChangeEnum.TASK.getCode());
        event.setTaskType(TaskTypeEnum.CONSUMPTION_TASK.getCode());
        event.setOperSubjectGuid(eventGrowthChangeDTO.getOperSubjectGuid());
        event.setOrderNumber(eventGrowthChangeDTO.getOrderNumber());
        event.setOrderType(eventGrowthChangeDTO.getOrderType());
        event.setOrderAmount(eventGrowthChangeDTO.getOrderPaidAmount());
        return event;
    }

    private Boolean getBoolean(TerCheckPassword terCheckPassword, HsaMemberInfoCard hsaMemberInfoCard) {
        if (StringUtils.isEmpty(terCheckPassword.getPassword())) {
            throw new MemberBaseException(MemberTerminalExceptionEnum.ERROR_MEMBER_PASSWORD_EMPTY);
        }
        String pwd = SecureUtil.md5(terCheckPassword.getPassword()).toUpperCase();
        return hsaMemberInfoCard.getCardPayPassword().equals(pwd);
    }

    /**
     * 获取补贴明细
     *
     * @param type                            1 消费 0 回退
     * @param deadlineMemberFundingDetailList
     * @return
     */
    private Map<String, HsaSubsidyActivityDetailRecord> getStringHsaSubsidyActivityDetailRecordMap(Integer type, List<HsaMemberFundingDetail> deadlineMemberFundingDetailList) {
        Map<String, HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecordMap = new HashMap<>();
        List<String> subsidyDetailRecordGuidList = deadlineMemberFundingDetailList
                .stream()
                .map(HsaMemberFundingDetail::getSubsidyDetailRecordGuid)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(subsidyDetailRecordGuidList)) {
            return hsaSubsidyActivityDetailRecordMapper.selectList(new LambdaQueryWrapper<HsaSubsidyActivityDetailRecord>()
                            .in(HsaSubsidyActivityDetailRecord::getGuid, subsidyDetailRecordGuidList)
                            .gt(type == BooleanEnum.TRUE.getCode(), HsaSubsidyActivityDetailRecord::getSubsidyResidueMoney, BigDecimal.ZERO)
                            .eq(HsaSubsidyActivityDetailRecord::getIsWithdraw, BooleanEnum.FALSE.getCode()))
                    .stream()
                    .collect(Collectors.toMap(HsaSubsidyActivityDetailRecord::getGuid, Function.identity(), (entity1, entity2) -> entity1));
        }
        return hsaSubsidyActivityDetailRecordMap;
    }

    /**
     * 计算补贴明细记录扣减金额
     */
    private void checkSubsidyMoneyAbatement(List<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords,
                                            Map<String, HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecordMap,
                                            List<HsaMemberFundingDetail> hsaMemberFundingDetailPerpetual,
                                            SubsidyAbatementBalanceDTO subsidyAbatementBalanceDTO) {
        //补贴可退金额
        BigDecimal subsidyAmountBack = subsidyAbatementBalanceDTO.getSubsidyAmountBack();
        //补贴不可退金额
        BigDecimal subsidyAmountNotBack = subsidyAbatementBalanceDTO.getSubsidyAmountNotBack();
        for (HsaMemberFundingDetail hsaMemberFundingDetail : hsaMemberFundingDetailPerpetual) {
            if (isaBoolean(hsaSubsidyActivityDetailRecordMap, hsaMemberFundingDetail)) {
                HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord = hsaSubsidyActivityDetailRecordMap.get(hsaMemberFundingDetail.getSubsidyDetailRecordGuid());
                BigDecimal count = hsaSubsidyActivityDetailRecord.getSubsidyResidueMoney().subtract(subsidyAbatementBalanceDTO.getSubsidyAmount());
                if (count.compareTo(BigDecimal.ZERO) >= 0) {
                    countGreater(hsaSubsidyActivityDetailRecords, subsidyAbatementBalanceDTO, hsaSubsidyActivityDetailRecord, subsidyAmountBack, subsidyAmountNotBack, count);
                    break;
                } else {
                    if (hsaSubsidyActivityDetailRecord.getIsRetreat() == 1) {
                        subsidyAmountBack = subsidyAmountBack.add(hsaSubsidyActivityDetailRecord.getSubsidyResidueMoney());
                        subsidyAbatementBalanceDTO.setSubsidyAmountBack(subsidyAmountBack);
                    } else {
                        subsidyAmountNotBack = subsidyAmountNotBack.add(hsaSubsidyActivityDetailRecord.getSubsidyResidueMoney());
                        subsidyAbatementBalanceDTO.setSubsidyAmountNotBack(subsidyAmountNotBack);
                    }

                    hsaSubsidyActivityDetailRecord.setSubsidyResidueMoney(BigDecimal.ZERO);
                    subsidyAbatementBalanceDTO.setSubsidyAmount(count.abs());
                    hsaSubsidyActivityDetailRecords.add(hsaSubsidyActivityDetailRecord);
                }
            }
        }
    }

    private static void countGreater(List<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords, SubsidyAbatementBalanceDTO subsidyAbatementBalanceDTO, HsaSubsidyActivityDetailRecord hsaSubsidyActivityDetailRecord, BigDecimal subsidyAmountBack, BigDecimal subsidyAmountNotBack, BigDecimal count) {
        if (hsaSubsidyActivityDetailRecord.getIsRetreat() == 1) {
            subsidyAmountBack = subsidyAmountBack.add(subsidyAbatementBalanceDTO.getSubsidyAmount());
            subsidyAbatementBalanceDTO.setSubsidyAmountBack(subsidyAmountBack);
        } else {
            subsidyAmountNotBack = subsidyAmountNotBack.add(subsidyAbatementBalanceDTO.getSubsidyAmount());
            subsidyAbatementBalanceDTO.setSubsidyAmountNotBack(subsidyAmountNotBack);
        }

        hsaSubsidyActivityDetailRecord.setSubsidyResidueMoney(count);
        hsaSubsidyActivityDetailRecords.add(hsaSubsidyActivityDetailRecord);

        subsidyAbatementBalanceDTO.setSubsidyAmount(BigDecimal.ZERO);
    }

    private static boolean isaBoolean(Map<String, HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecordMap, HsaMemberFundingDetail hsaMemberFundingDetail) {
        return CollUtil.isNotEmpty(hsaSubsidyActivityDetailRecordMap) && hsaSubsidyActivityDetailRecordMap.containsKey(hsaMemberFundingDetail.getSubsidyDetailRecordGuid());
    }


    /**
     * 通过补贴金过期时间倒序
     *
     * @param list list
     */
    private static void listSort(List<HsaMemberFundingDetail> list) {
        list.sort((o1, o2) -> {
            try {
                LocalDateTime dt1 = o1.getOutOfDate();
                LocalDateTime dt2 = o2.getOutOfDate();
                if (dt1.isBefore(dt2)) {
                    return -1;
                } else if (dt1.isAfter(dt2)) {
                    return 1;
                } else {
                    return 0;
                }
            } catch (Exception e) {
                return 1;
            }

        });
    }


    private static final String RECHARGE_REFUND = "RECHARGE_REFUND:";

    /**
     * 充值退款
     *
     * @param refundDTO 退款参数
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AggRefundResult rechargeRefund(RechargeRefundDTO refundDTO) {
        RLock lock = redissonClient.getLock(RECHARGE_REFUND + refundDTO.getConsumptionGuid());
        try {
            if (lock.isLocked()) {
                log.warn("会员卡退款操作：{}", refundDTO.getConsumptionGuid());
                return AggRefundResult.buildFail(MemberAccountExceptionEnum.ORDER_REFUNDED_REPEAT.getDes());
            }
            lock.lock();
            final HsaMemberConsumption memberConsumption = hsaMemberConsumptionMapper.queryByGuid(refundDTO.getConsumptionGuid());
            if (Objects.isNull(memberConsumption) || !Objects.equals(memberConsumption.getConsumptionType(), 0)) {
                log.warn("订单{}退款失败，不存在交易记录或订单状态有误", refundDTO.getConsumptionGuid());
                return AggRefundResult.buildFail("不存在交易记录或订单状态有误");
            }
            if (Objects.nonNull(memberConsumption.getRefundTime())) {
                log.warn("订单已退款,memberConsumption={}", memberConsumption.getGuid());
                return AggRefundResult.buildFail(MemberAccountExceptionEnum.ORDER_REFUNDED.getDes());
            }
            final HsaMemberFundingDetail memberFundingDetail = hsaMemberFundingDetailMapper.queryByConsumptionGuid(memberConsumption.getGuid());
            if (Objects.isNull(memberFundingDetail)) {
                log.warn("订单{}退款失败，不存在交易记录或订单状态有误", refundDTO.getConsumptionGuid());
                return AggRefundResult.buildFail("不存在交易记录或订单状态有误");
            }
            if (memberFundingDetail.getGmtCreate().plusHours(72).isBefore(LocalDateTime.now())) {
                return AggRefundResult.buildFail("订单已超过时限，不可退款");
            }
            AggRefundResult buildFail = getAggRefundResult(refundDTO, memberConsumption);
            if (buildFail != null) {
                return buildFail;
            }
            //卡操作
            return handlerMemberInfoCard(memberFundingDetail, memberConsumption, refundDTO);

        } catch (Exception e) {
            log.warn("充值退款异常！", e);
            return AggRefundResult.buildFail("操作异常");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private static final String GIFT_CARD_ERROR = "充值订单相关赠品已使用，订单不可退款";

    private static final Integer AGG_SUCCESS = 200;

    /**
     * 退款卡操作
     *
     * @param memberFundingDetail 会员消费记录
     * @param memberConsumption   会员消费记录
     * @param refundDTO           退款参数
     * @return
     */
    private AggRefundResult handlerMemberInfoCard
    (HsaMemberFundingDetail memberFundingDetail, HsaMemberConsumption memberConsumption, RechargeRefundDTO refundDTO) {
        RLock lockCard = redissonClient.getLock("CARD_RECHARGE_REFUND:" + memberConsumption.getMemberInfoCardGuid());
        try {
            if (!lockCard.tryLock(NumberConstant.NUMBER_30, NumberConstant.NUMBER_30, TimeUnit.SECONDS)) {
                log.warn("会员卡退款操作：{}", memberConsumption.getMemberInfoCardGuid());
                return AggRefundResult.buildFail(MemberAccountExceptionEnum.ORDER_REFUNDED_REPEAT.getDes());
            }
            //合卡后消费记录不存在
            HsaMemberInfoCard hsaMemberInfoCard = Optional.ofNullable(hsaMemberInfoCardMapper.queryByGuid(memberConsumption.getMemberInfoCardGuid()))
                    .orElse(getMemberInfoCardInfo(memberConsumption));
            if (Objects.isNull(hsaMemberInfoCard)) {
                return AggRefundResult.buildFail(systemRoleHelper.getReplace("会员卡不存在，无法退款", ThreadLocalCache.getOperSubjectGuid()));
            }

            //实退款
            final BigDecimal residueRecharge = hsaMemberInfoCard.getCardAmount().subtract(memberFundingDetail.getRechargeAmount());
            //回退后
            hsaMemberInfoCard.setCardAmount(residueRecharge);

            //判断是否有赠送金额退款
            List<HsaCardRechargeGiftDetail> noRefreshGiftDetails = hsaMemberInfoCardService.checkGiftAmount(memberFundingDetail, hsaMemberInfoCard);

            //判断退款金额和可退金额
            if (hsaMemberInfoCard.getCardAmount().compareTo(BigDecimal.ZERO) < 0) {
                log.warn("充值订单guid{}退款失败，卡余额不足", memberConsumption.getGuid());
                return AggRefundResult.buildFail(GIFT_CARD_ERROR);
            }
            //赠送余额处理
            if (memberFundingDetail.getGiftAmount().compareTo(BigDecimal.ZERO) > 0
                    && hsaMemberInfoCard.getGiftAmount().compareTo(BigDecimal.ZERO) < 0) {
                log.warn("充值订单{}退款失败赠送余额不足，赠送余额{}，退赠送{},", memberConsumption.getGuid(), hsaMemberInfoCard.getGiftAmount(), memberFundingDetail.getGiftAmount());
                return AggRefundResult.buildFail(GIFT_CARD_ERROR);
            }

            //发起零售退款，同步操作，成功后退充值记录
            CrmRefundPayDTO crmRefundPayDTO = getCrmRefundPayDTO(memberConsumption, refundDTO);
            //除小程序，都传门店id
            setStoreId(memberConsumption, crmRefundPayDTO);
            //充值退款不传门店，食堂支付配置取默认配置
            crmRefundPayDTO.toOrderSource(memberConsumption.getOrderSource());

            memberConsumption.setRefundTime(LocalDateTime.now());
            HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(memberConsumption.getMemberInfoGuid());
            //充值赠送退款
            saveRechargeRefundData(memberConsumption, hsaMemberInfoCard, memberFundingDetail, noRefreshGiftDetails, refundDTO.getRemark(), hsaOperationMemberInfo);
            //刷新
            ConsumptionRespVO consumptionRespVO = new ConsumptionRespVO();
            consumptionRespVO.setRedisConsumptionGuid(memberConsumption.getGuid());
            consumptionRespVO.setOperSubjectGuid(memberConsumption.getOperSubjectGuid());
            consumptionRespVO.setMemberInfoCardGuid(memberConsumption.getMemberInfoCardGuid());
            payService.dealRefresh(consumptionRespVO);
            memberConsumption.setIsCancel(BooleanEnum.TRUE.getCode());
            sendChangeCardAmountMsg(hsaMemberInfoCard, memberConsumption, hsaOperationMemberInfo, memberFundingDetail);
            return AggRefundResult.buildSuccess();
        } catch (Exception e) {
            log.warn("充值退款异常！", e);
            return AggRefundResult.buildFail("操作异常");
        } finally {
            if (lockCard.isLocked() && lockCard.isHeldByCurrentThread()) {
                lockCard.unlock();
            }
        }
    }

    private static CrmRefundPayDTO getCrmRefundPayDTO(HsaMemberConsumption memberConsumption, RechargeRefundDTO refundDTO) {
        return CrmRefundPayDTO.builder()
                .order_code(memberConsumption.getOrderNumber())
                .reason(refundDTO.getRemark())
                .operating_subject_id(ThreadLocalCache.getOperSubjectGuid())
                .pos_token(refundDTO.getToken())
                .system(ThreadLocalCache.getSystem())
                .enterprise_id(ThreadLocalCache.getEnterpriseGuid())
                .build();
    }


    /**
     * 设置退款时，门店id
     *
     * @param memberConsumption 消费记录
     * @param crmRefundPayDTO   退款
     */
    private void setStoreId(HsaMemberConsumption memberConsumption, CrmRefundPayDTO crmRefundPayDTO) {
        if (!Objects.equals(memberConsumption.getOrderSource(), SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode())) {
            crmRefundPayDTO.setStore_id(memberConsumption.getStoreGuid());
        }
    }

    /**
     * 合卡后，memberInfoGuid可能会被删除
     *
     * @param memberConsumption 消费
     * @return
     */
    private HsaMemberInfoCard getMemberInfoCardInfo(HsaMemberConsumption memberConsumption) {
        if (memberConsumption.getCardNum() != null) {
            //通过cardNum 去查询
            final HsaPhysicalCard physicalCard = hsaPhysicalCardMapper.getByCardNum(memberConsumption.getCardNum(), memberConsumption.getOperSubjectGuid());
            if (Objects.nonNull(physicalCard)) {
                return hsaMemberInfoCardMapper.queryByPhysicalCard(physicalCard.getGuid());
            }
            HsaElectronicCard electronicCard = hsaElectronicCardMapper.getByCardNum(memberConsumption.getCardNum(), memberConsumption.getOperSubjectGuid());
            if (Objects.nonNull(electronicCard)) {
                return hsaMemberInfoCardMapper.queryByElectronicCard(electronicCard.getGuid());
            }
        }
        log.error("异常：消费记录{}，没有cardNum", memberConsumption.getGuid());
        return null;
    }

    /**
     * 获取退款结果
     *
     * @param refundDTO         退款参数
     * @param memberConsumption 消费
     * @return
     */
    private AggRefundResult getAggRefundResult(RechargeRefundDTO refundDTO, HsaMemberConsumption memberConsumption) {
        HsaMemberConsumption refundConsumption = hsaMemberConsumptionMapper.selectOne(new LambdaQueryWrapper<HsaMemberConsumption>()
                .eq(HsaMemberConsumption::getOrderNumber, memberConsumption.getOrderNumber())
                .eq(HsaMemberConsumption::getIsCancel, BooleanEnum.TRUE.getCode()));
        if (Objects.nonNull(refundConsumption)) {
            if (Objects.equals(refundConsumption.getIsComplete(), 0)) {
                log.warn("订单{}正在退款中", refundDTO.getConsumptionGuid());
                return AggRefundResult.buildFail("正在退款中，若长时间未到账请联系系统管理员");
            }
            log.info("订单已退款：{}，memberConsumption={}", refundConsumption.getIsComplete(), refundConsumption.getOrderNumber());
            return AggRefundResult.buildFail(MemberAccountExceptionEnum.ORDER_REFUNDED.getDes());
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AggRefundResult rechargeRefundCallback(RechargeRefundCallbackDTO refundDTO) {
        RLock lock = redissonClient.getLock("CARD_RECHARGE_REFUND:" + refundDTO.getOrderNumber());
        try {
            if (!lock.tryLock(NumberConstant.NUMBER_30, NumberConstant.NUMBER_30, TimeUnit.SECONDS)) {
                log.warn("会员卡充值退款重复：{}", JacksonUtils.writeValueAsString(refundDTO));
                return AggRefundResult.buildFail(MemberAccountExceptionEnum.ORDER_REFUNDED_REPEAT.getDes());
            }
            HsaMemberConsumption refundConsumption = hsaMemberConsumptionMapper.selectOne(new LambdaQueryWrapper<HsaMemberConsumption>()
                    .eq(HsaMemberConsumption::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                    .eq(HsaMemberConsumption::getOrderNumber, refundDTO.getOrderNumber())
                    .eq(HsaMemberConsumption::getIsCancel, BooleanEnum.TRUE.getCode()));
            if (Objects.isNull(refundConsumption)) {
                return AggRefundResult.buildFail("充值记录异常，无法退款");
            }
            final HsaMemberConsumption memberConsumption = hsaMemberConsumptionMapper.selectOne(
                    new LambdaQueryWrapper<HsaMemberConsumption>().eq(HsaMemberConsumption::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                            .eq(HsaMemberConsumption::getOrderNumber, refundDTO.getOrderNumber())
                            .eq(HsaMemberConsumption::getIsCancel, BooleanEnum.FALSE.getCode())
            );
            if (refundDTO.getState() == BooleanEnum.TRUE.getCode()) {
                //退款失败操作
                memberConsumption.setRefundTime(null);
                hsaMemberConsumptionMapper.updateIsNull(memberConsumption);
                hsaMemberConsumptionMapper.removeByGuid(refundConsumption.getGuid());
                return AggRefundResult.build(OrderRefundConditionEnum.REFUND_CLOSURE_MERCHANT.getCode(), "充值退款取消成功");
            }
            if (Objects.equals(refundConsumption.getIsComplete(), BooleanEnum.TRUE.getCode())) {
                log.error("订单已退款 {}，重复调用", refundDTO.getOrderNumber());
                return AggRefundResult.buildFail("订单已退款，重复调用");
            }
            return handlerRefundRechargeCallback(refundDTO, refundConsumption, memberConsumption);
        } catch (Exception e) {
            log.error("充值退款回调异常：{}", e.getMessage());
            return AggRefundResult.buildFail("异常，无法退款");
        } finally {
            lock.unlock();
        }
    }

    @Override
    public List<MemberPhoneMathVO> memberPhoneMatch(String phoneNum) {
        List<MemberPhoneMathVO> memberPhoneMathVOList = Lists.newArrayList();
        if (StringUtils.isEmpty(phoneNum) || phoneNum.length() != NUMBER_4) {
            return memberPhoneMathVOList;
        }

        return hsaOperationMemberInfoMapper.memberPhoneMatch(phoneNum, ThreadLocalCache.getOperSubjectGuid());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ConsumptionRespVO> batchMemberCardPayRecord(List<RequestMemberCardPayVO> requests) {
        List<ConsumptionRespVO> results = new ArrayList<>();
        try {
            for (RequestMemberCardPayVO request : requests) {
                ConsumptionRespVO result = this.memberOrderRecord(request);
                results.add(result);
            }
        } catch (Exception e) {
            log.error("批量会员卡支付失败", e);
            throw new MemberBaseException(((MemberBaseException) e).getCode(), ((MemberBaseException) e).getDes());
        }
        return results;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ConsumptionRespVO memberOrderRecord(RequestMemberCardPayVO request) {
        RequestConfirmPayVO requestConfirmPayVO = MerchantPayAssembler.fromRequestMemberCardPayVO(request);
        ConsumptionRespVO consumptionRespVO = new ConsumptionRespVO();
        //请求头信息
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        log.info("消费记录参数headerUserInfo:{}", JSON.toJSONString(headerUserInfo));
        log.info("消费记录参数:{}", JSON.toJSONString(request));

        checkOrderNumPay(request.getOrderNumber(), headerUserInfo.getOperSubjectGuid());

        //会员消费记录guid
        String memberConsumptionGuid = guidGeneratorUtil.getStringGuid(HsaMemberConsumption.class.getSimpleName());
        requestConfirmPayVO.setMemberConsumptionGuid(memberConsumptionGuid);
        log.info("生成新的消费记录GUID，memberConsumptionGuid={}", memberConsumptionGuid);

        HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(request.getMemberInfoGuid());
        if (ObjectUtils.isEmpty(hsaOperationMemberInfo)) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberTerminalExceptionEnum.ERROR_MEMBER_NOT_EXIT, ThreadLocalCache.getOperSubjectGuid()));
        }


        //保存消费记录
        final HsaMemberConsumption memberConsumption = hsaMemberConsumptionService.saveConsumptionRecord(requestConfirmPayVO, null, headerUserInfo, null, hsaOperationMemberInfo);

        //设置最后消费时间
        setLastConsumptionTime(hsaOperationMemberInfo);

        //设置支付方式
        memberBaseThreadExecutor.execute(() -> savePayWay(request.getRequestPayInfoList(), memberConsumptionGuid));

        //发布消费订单限购支付事件
        final PurchaseOrderStateDto orderStateDto = PurchaseOrderStateDto.builder()
                .orderState(ConsumptionOrderStateEnum.PAY.getCode())
                .orderNumber(memberConsumption.getOrderNumber())
                .consumptionOrderGuid(memberConsumption.getGuid())
                .operSubjectGuid(memberConsumption.getOperSubjectGuid()).build();
        consumptionOrderPublisher.publish(ConsumptionOrderEventEnum.PURCHASE_ORDER_STATE, JacksonUtils.writeValueAsString(orderStateDto));

        //刷新标签
        hsaLabelSettingService.refreshLabel(Lists.newArrayList(hsaOperationMemberInfo.getGuid()), null,
                BooleanEnum.FALSE.getCode(), null, LabelTriggerTypeEnum.CONSUMPTION_INFO.getCode());

        consumptionRespVO.setMemberPhone(hsaOperationMemberInfo.getPhoneNum())
                .setOrderNumber(request.getOrderNumber());

        return consumptionRespVO;
    }

    @Override
    public Boolean checkMatchingCardNum(RequestCheckMatchingCardVO request) {
        log.info("匹配二维码扫码={}", JSON.toJSONString(request));
        Object content = redisTemplate.opsForValue().get(request.getCardNumKey());
        if (null == content) {
            log.info("二维码不存在");
            return false;
        }
        String loginNum = content.toString();
        log.info("匹配二维码扫码，二维码卡号======>{}", loginNum);
        return request.getCardNum().equals(loginNum);
    }


    @Override
    public BigDecimal queryCardBalance(String memberInfoCardGuid) {
        HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMapper.queryByGuid(memberInfoCardGuid);
        return hsaMemberInfoCard.getCardAmount()
                .add(hsaMemberInfoCard.getGiftAmount())
                .add(hsaMemberInfoCard.getSubsidyAmount());
    }

    @Override
    public List<TerLoginMemberCardVO> queryCardByMember(RequestMemberCardQO requestMemberCardQO) {

        HsaOperationMemberInfo hsaOperationMemberInfo =
                hsaOperationMemberInfoMapper.queryByGuid(requestMemberCardQO.getMemberInfoGuid());

        List<HsaMemberInfoCard> hsaMemberInfoCardList = hsaMemberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .in(HsaMemberInfoCard::getMemberInfoGuid, hsaOperationMemberInfo.getGuid()));

        HsaCardBalanceRule hsaCardBalanceRule = hsaCardBalanceRuleMapper.selectOne(new LambdaQueryWrapper<HsaCardBalanceRule>()
                .eq(HsaCardBalanceRule::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));

        //校验卡状态
        TerCheckMemberCardQO checkMemberCardQO = new TerCheckMemberCardQO();
        BeanUtils.copyProperties(requestMemberCardQO, checkMemberCardQO);
        checkMemberCardQO.setIsFilterStatus(BooleanEnum.TRUE.getCode());
        List<TerLoginMemberCardVO> terLoginMemberCardVOS = terMemberCardBusinessService.doCheckMemberInfoCardList(
                hsaMemberInfoCardList,
                hsaOperationMemberInfo,
                hsaCardBalanceRule,
                checkMemberCardQO);

        terMemberCardBusinessService.dealFreeze(terLoginMemberCardVOS, requestMemberCardQO.getStoreGuid(), hsaOperationMemberInfo);

        //设置当前操作的卡
        if (CollUtil.isNotEmpty(terLoginMemberCardVOS)) {
            List<TerLoginMemberCardVO> cardVOS = terLoginMemberCardVOS
                    .stream()
                    .filter(in -> in.getMemberInfoCardGuid().equals(requestMemberCardQO.getMemberInfoCardGuid()))
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(cardVOS)) {
                TerLoginMemberCardVO cardVO = cardVOS.get(0);
                cardVO.setIsOpt(BooleanEnum.TRUE.getCode());
            } else {
                TerLoginMemberCardVO cardVO = terLoginMemberCardVOS.get(0);
                cardVO.setIsOpt(BooleanEnum.TRUE.getCode());
            }
        }


        return terLoginMemberCardVOS;
    }

    @Override
    public CheckMemberCardPayVO preCheckMemberCardPay(CheckMemberCardPayQO checkMemberCardPayQO) {
        log.info("预校验会员卡支付={}", JSON.toJSONString(checkMemberCardPayQO));
        CheckMemberCardPayVO checkMemberCardPayVO = new CheckMemberCardPayVO(BooleanEnum.FALSE.getCode());

        //冻结中积分
        checkIntegral(checkMemberCardPayQO, checkMemberCardPayQO.getMemberInfoGuid(), checkMemberCardPayVO);

        //校验余额
        checkBalance(checkMemberCardPayQO, checkMemberCardPayVO);

        //校验优惠券
        checkCoupon(checkMemberCardPayQO, checkMemberCardPayVO);

        log.info("预校验会员结果={}", JSON.toJSONString(checkMemberCardPayVO));
        return checkMemberCardPayVO;
    }

    private void checkCoupon(CheckMemberCardPayQO checkMemberCardPayQO, CheckMemberCardPayVO checkMemberCardPayVO) {
        if (CollUtil.isNotEmpty(checkMemberCardPayQO.getCouponDiscountCodeList())) {
            List<HsaMemberOrderDiscount> hsaMemberOrderDiscounts = hsaMemberOrderDiscountMapper.selectList(new LambdaQueryWrapper<HsaMemberOrderDiscount>()
                    .eq(HsaMemberOrderDiscount::getMemberGuid, checkMemberCardPayQO.getMemberInfoGuid())
                    .eq(HsaMemberOrderDiscount::getDiscountState, BooleanEnum.FALSE.getCode())
                    .in(HsaMemberOrderDiscount::getDiscountId, checkMemberCardPayQO.getCouponDiscountCodeList()));

            if (CollUtil.isNotEmpty(hsaMemberOrderDiscounts)) {
                checkMemberCardPayVO.setIsDiscountChanged(BooleanEnum.TRUE.getCode());
            }
        }

        if (CollUtil.isNotEmpty(checkMemberCardPayQO.getPayCouponPayQOS())) {
            checkExchangeCoupon(checkMemberCardPayQO, checkMemberCardPayVO);
        }
    }

    private void checkExchangeCoupon(CheckMemberCardPayQO checkMemberCardPayQO, CheckMemberCardPayVO checkMemberCardPayVO) {
        List<String> discountOptionId = checkMemberCardPayQO.getPayCouponPayQOS()
                .stream().map(PayCouponExchangePayQO::getDiscountId).collect(Collectors.toList());
        //占用的次数
        Map<String, Integer> codeMap = hsaMemberOrderDiscountService.getUsedCouponNum(
                discountOptionId,
                BooleanEnum.FALSE.getCode(),
                SettlementDiscountOptionEnum.COUPON_EXCHANGE.getCode(),
                null);

        List<HsaMemberCouponLink> hsaMemberCouponLinks = hsaMemberCouponLinkMapper.selectList(new LambdaQueryWrapper<HsaMemberCouponLink>()
                .in(HsaMemberCouponLink::getCode, discountOptionId));

        List<HsaMemberCouponLink> hsaMemberCouponLinkList = hsaMemberCouponLinks.stream().filter(in -> in.getState() == CouponMemberStateEnum.APPLY.getCode() ||
                        in.getState() == CouponMemberStateEnum.OVER.getCode())
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(hsaMemberCouponLinkList)) {
            checkMemberCardPayVO.setIsDiscountChanged(BooleanEnum.TRUE.getCode());
            return;
        }

        Map<String, HsaMemberCouponLink> hsaMemberCouponLinkMap = hsaMemberCouponLinks.stream()
                .collect(Collectors.toMap(HsaMemberCouponLink::getCode, Function.identity(), (entity1, entity2) -> entity1));

        //已使用的次数
        List<String> memberCouponLinkGuids = hsaMemberCouponLinks.stream().map(HsaMemberCouponLink::getGuid).collect(Collectors.toList());
        List<HsaMemberCouponUse> hsaMemberCouponUseList = hsaMemberCouponUseMapper.selectList(new LambdaQueryWrapper<HsaMemberCouponUse>()
                .in(HsaMemberCouponUse::getMemberCouponLinkGuid, memberCouponLinkGuids));
        Map<String, List<HsaMemberCouponUse>> hsaMemberCouponUseMap = hsaMemberCouponUseList.stream()
                .collect(Collectors.groupingBy(HsaMemberCouponUse::getMemberCouponLinkGuid));

        checkDiscountChanged(checkMemberCardPayQO, checkMemberCardPayVO, hsaMemberCouponLinkMap, codeMap, hsaMemberCouponUseMap);
    }

    private static void checkDiscountChanged(CheckMemberCardPayQO checkMemberCardPayQO,
                                             CheckMemberCardPayVO checkMemberCardPayVO,
                                             Map<String, HsaMemberCouponLink> hsaMemberCouponLinkMap,
                                             Map<String, Integer> codeMap,
                                             Map<String, List<HsaMemberCouponUse>> hsaMemberCouponUseMap) {
        for (PayCouponExchangePayQO payCouponPayQO : checkMemberCardPayQO.getPayCouponPayQOS()) {
            String discountId = payCouponPayQO.getDiscountId();
            HsaMemberCouponLink hsaMemberCouponLink = hsaMemberCouponLinkMap.get(payCouponPayQO.getDiscountId());

            if (Objects.nonNull(hsaMemberCouponLink.getExchangeLimit())
                    && hsaMemberCouponLink.getExchangeLimit() == BooleanEnum.TRUE.getCode()) {
                //限制次数
                int limitNumber = hsaMemberCouponLink.getExchangeTimes();
                log.info("券：{}，限制次数为：{}", hsaMemberCouponLink.getCode(), limitNumber);
                //已使用总次数
                int totalUsedNumber = payCouponPayQO.getUseTimes();
                log.info("券：{}，当前使用次数为：{}", hsaMemberCouponLink.getCode(), totalUsedNumber);

                //占用次数
                if (codeMap.containsKey(discountId)) {
                    log.info("券：{}，占用次数为：{}", hsaMemberCouponLink.getCode(), codeMap.get(discountId));
                    totalUsedNumber = totalUsedNumber + codeMap.get(discountId);
                }
                //已使用次数
                if (hsaMemberCouponUseMap.containsKey(hsaMemberCouponLink.getGuid())) {
                    log.info("券：{}，已使用次数为：{}", hsaMemberCouponLink.getCode(), hsaMemberCouponUseMap.get(hsaMemberCouponLink.getGuid()).size());
                    List<HsaMemberCouponUse> hsaMemberCouponUses = hsaMemberCouponUseMap.get(hsaMemberCouponLink.getGuid());
                    totalUsedNumber = totalUsedNumber + hsaMemberCouponUses.size();
                }
                log.info("券：{}，已消耗总次数为：{}", hsaMemberCouponLink.getCode(), totalUsedNumber);
                if (limitNumber - totalUsedNumber < 0) {
                    checkMemberCardPayVO.setIsDiscountChanged(BooleanEnum.TRUE.getCode());
                    break;
                }
            }
        }
    }

    private void checkBalance(CheckMemberCardPayQO checkMemberCardPayQO, CheckMemberCardPayVO checkMemberCardPayVO) {
        if (StringUtils.isNotEmpty(checkMemberCardPayQO.getMemberInfoCardGuid())) {
            HsaMemberInfoCard memberInfoCard = hsaMemberInfoCardMapper.queryByGuid(checkMemberCardPayQO.getMemberInfoCardGuid());
            HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(memberInfoCard.getCardGuid());
            BigDecimal checkBalancePayAmount = terMemberCardBusinessService.checkBalanceAmount(
                    checkMemberCardPayQO.getCardBalancePayAmount(),
                    memberInfoCard,
                    hsaCardBaseInfo);
            if (checkBalancePayAmount.compareTo(BigDecimal.ZERO) != 0) {
                checkMemberCardPayVO.setIsBalanceSufficient(BooleanEnum.FALSE.getCode());
                checkMemberCardPayVO.setInsufficientBalance(checkBalancePayAmount);
            } else {
                checkMemberCardPayVO.setIsBalanceSufficient(BooleanEnum.TRUE.getCode());
            }
        }
    }

    private void checkIntegral(CheckMemberCardPayQO checkMemberCardPayQO,
                               String memberInfoGuid,
                               CheckMemberCardPayVO checkMemberCardPayVO) {
        if (Objects.nonNull(checkMemberCardPayQO.getIntegral())) {

            int usableIntegral = getUsableIntegral(memberInfoGuid);

            checkMemberCardPayVO.setIsIntegralSufficient(usableIntegral - checkMemberCardPayQO.getIntegral() >= 0 ?
                    BooleanEnum.TRUE.getCode() :
                    BooleanEnum.FALSE.getCode());
        }
    }

    /**
     * 获取会员当前可用积分
     * @param memberInfoGuid 会员guid
     * @return 可用积分
     */
    @Override
    public Integer getUsableIntegral(String memberInfoGuid) {
        HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(memberInfoGuid);

        //冻结的积分
        Integer freezeIntegral = hsaIntegralDetailMapper.sumIntegralProtectDateIntegral(hsaOperationMemberInfo.getGuid(),
                ThreadLocalCache.getOperSubjectGuid(), ConsumptionOrderStateEnum.REFUND.getDes());

        //占用的积分 只统计还未支付的优惠
        final LambdaQueryWrapper<HsaMemberOrderDiscount> wrapper =
                new LambdaQueryWrapper<HsaMemberOrderDiscount>()
                        .eq(HsaMemberOrderDiscount::getMemberGuid, hsaOperationMemberInfo.getGuid())
                        .eq(HsaMemberOrderDiscount::getIsPay, BooleanEnum.FALSE.getCode())
                        .eq(HsaMemberOrderDiscount::getDiscountState, BooleanEnum.FALSE.getCode())
                        .eq(HsaMemberOrderDiscount::getDiscountOption, SettlementDiscountOptionEnum.INTEGRAL_EXPLAIN.getCode())
                        .eq(HsaMemberOrderDiscount::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid());
        List<HsaMemberOrderDiscount> hsaMemberOrderDiscounts = hsaMemberOrderDiscountMapper.selectList(wrapper);

        //可用积分
        int usableIntegral = com.holderzone.member.common.util.verify.ObjectUtil.objToInt(hsaOperationMemberInfo.getMemberIntegral())
                - com.holderzone.member.common.util.verify.ObjectUtil.objToInt(freezeIntegral);

        if (CollUtil.isNotEmpty(hsaMemberOrderDiscounts)) {
            int integralNum = hsaMemberOrderDiscounts
                    .stream()
                    .map(HsaMemberOrderDiscount::getDiscountDynamic)
                    .mapToInt(Integer::parseInt)
                    .sum();

            usableIntegral = com.holderzone.member.common.util.verify.ObjectUtil.objToInt(usableIntegral)
                    - com.holderzone.member.common.util.verify.ObjectUtil.objToInt(integralNum);
        }

        usableIntegral = Math.max(usableIntegral, 0);
        return usableIntegral;
    }

    private void checkOrderNumPay(String request, String operSubjectGuid) {
        Integer count = hsaMemberConsumptionMapper.selectCount(new LambdaQueryWrapper<HsaMemberConsumption>()
                .eq(HsaMemberConsumption::getOrderNumber, request)
                .eq(HsaMemberConsumption::getOperSubjectGuid, operSubjectGuid));
        if (com.holderzone.member.common.util.verify.ObjectUtil.objToInt(count) > 0) {
            log.info("此消费订单已存在，orderNumber={}", request);
            throw new MemberBaseException("此消费订单已存在");
        }
    }

    /**
     * 退款处理
     *
     * @param refundDTO         退款
     * @param refundConsumption 退款消费
     * @param memberConsumption 消费
     * @return
     */
    private AggRefundResult handlerRefundRechargeCallback(RechargeRefundCallbackDTO refundDTO,
                                                          HsaMemberConsumption refundConsumption,
                                                          HsaMemberConsumption memberConsumption) {
        //合卡后消费记录不存在
        HsaMemberInfoCard hsaMemberInfoCard = Optional.ofNullable(hsaMemberInfoCardMapper.queryByGuid(refundConsumption.getMemberInfoCardGuid()))
                .orElse(getMemberInfoCardInfo(refundConsumption));
        if (Objects.isNull(hsaMemberInfoCard)) {
            return AggRefundResult.buildFail(systemRoleHelper.getReplace("会员卡不存在，无法退款", ThreadLocalCache.getOperSubjectGuid()));
        }
        final HsaMemberFundingDetail memberFundingDetail = hsaMemberFundingDetailMapper.queryByConsumptionGuid(memberConsumption.getGuid());
        final BigDecimal residueRecharge = hsaMemberInfoCard.getCardAmount().subtract(memberFundingDetail.getRechargeAmount());
        //回退后
        hsaMemberInfoCard.setCardAmount(residueRecharge);

        //判断是否有赠送金额退款
        List<HsaCardRechargeGiftDetail> noRefreshGiftDetails = hsaMemberInfoCardService.checkGiftAmount(memberFundingDetail, hsaMemberInfoCard);

        //判断退款金额和可退金额
        if (hsaMemberInfoCard.getCardAmount().compareTo(BigDecimal.ZERO) < 0) {
            log.warn("充值订单order{}退款失败，卡余额不足", refundDTO.getOrderNumber());
            return AggRefundResult.buildFail(GIFT_CARD_ERROR);
        }
        if (memberFundingDetail.getGiftAmount().compareTo(BigDecimal.ZERO) > 0 && hsaMemberInfoCard.getGiftAmount().compareTo(BigDecimal.ZERO) < 0) {
            log.warn("充值订单{}退款失败，赠送余额{}，退赠送{},", memberConsumption.getGuid(), hsaMemberInfoCard.getGiftAmount(), memberFundingDetail.getGiftAmount());
            return AggRefundResult.buildFail(GIFT_CARD_ERROR);
        }
        refundConsumption.setIsComplete(BooleanEnum.TRUE.getCode());
        memberConsumption.setRefundTime(LocalDateTime.now());
        HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(hsaMemberInfoCard.getMemberInfoGuid());
        final String oldFundingDetailGuid = getMemberFundingDetail(hsaMemberInfoCard, memberFundingDetail, refundConsumption.getRemark(), hsaOperationMemberInfo, refundConsumption.getGuid());
        log.info("充值回退后的会员资金来往明细，memberFundingDetail={}", JSON.toJSONString(memberFundingDetail));
        //相关库表操作
        handlerRechargeGiftAmount(oldFundingDetailGuid, noRefreshGiftDetails, memberConsumption, hsaOperationMemberInfo)
        ;
        //删除充值赠送标签
        removeGiftActivityMemberLabel(oldFundingDetailGuid, memberFundingDetail.getMemberInfoGuid());
        //成长值、积分回退
        refundProcessor(memberConsumption, hsaOperationMemberInfo);
        hsaMemberConsumptionMapper.updateByGuid(memberConsumption);
        hsaMemberConsumptionMapper.updateByGuid(refundConsumption);
        //保存退款消费记录
        log.info("充值回调：回退后的会员消费来往明细，memberConsumption={}", JSON.toJSONString(memberConsumption));
        hsaMemberFundingDetailService.save(memberFundingDetail);
        hsaMemberInfoCardMapper.updateByGuid(hsaMemberInfoCard);
        //刷新
        ConsumptionRespVO consumptionRespVO = new ConsumptionRespVO();
        consumptionRespVO.setOperSubjectGuid(memberConsumption.getOperSubjectGuid());
        consumptionRespVO.setRedisConsumptionGuid(memberConsumption.getGuid());
        consumptionRespVO.setMemberInfoCardGuid(memberConsumption.getMemberInfoCardGuid());
        payService.dealRefresh(consumptionRespVO);
        return AggRefundResult.buildSuccess();
    }

    /**
     * 权益回退
     *
     * @param memberConsumption      消费
     * @param hsaOperationMemberInfo 会员
     */
    private void refundProcessor(HsaMemberConsumption memberConsumption, HsaOperationMemberInfo hsaOperationMemberInfo) {
        final String userStr = ThreadLocalCache.get();
        memberBaseThreadExecutor.execute(() -> {
            ThreadLocalCache.put(userStr);
            //成长值回退
            memberGrowthProcessorManage.growthValueBackNewProcessor(memberConsumption, hsaOperationMemberInfo);
            //积分回退
            memberIntegralProcessorManage.integralValueBackNewProcessor(memberConsumption, hsaOperationMemberInfo);
        });
    }


    /**
     * 保存充值退款数据
     *
     * @param memberConsumption    充值记录
     * @param hsaMemberInfoCard    卡
     * @param memberFundingDetail  明细
     * @param noRefreshGiftDetails 赠送记录
     * @param remark               备注
     */
    private void saveRechargeRefundData(HsaMemberConsumption memberConsumption,
                                        HsaMemberInfoCard hsaMemberInfoCard,
                                        HsaMemberFundingDetail memberFundingDetail,
                                        List<HsaCardRechargeGiftDetail> noRefreshGiftDetails,
                                        String remark,
                                        HsaOperationMemberInfo hsaOperationMemberInfo) {
        //生成新的消费guid
        String newMemberConsumptionGuid = guidGeneratorUtil.getStringGuid(HsaMemberConsumption.class.getSimpleName());
        final String oldFundingDetailGuid = getMemberFundingDetail(hsaMemberInfoCard, memberFundingDetail, remark, hsaOperationMemberInfo, newMemberConsumptionGuid);

        log.info("充值回退后的会员资金来往明细，memberFundingDetail={}", JSON.toJSONString(memberFundingDetail));
        //相关库表操作
        log.info("需要取消的赠送记录：{}", JSON.toJSONString(noRefreshGiftDetails));
        handlerRechargeGiftAmount(oldFundingDetailGuid, noRefreshGiftDetails, memberConsumption, hsaOperationMemberInfo);
        //删除充值赠送标签
        removeGiftActivityMemberLabel(oldFundingDetailGuid, memberFundingDetail.getMemberInfoGuid());
        //不处理超额
        handlerRechargeRefundRepository(memberConsumption, memberFundingDetail,
                newMemberConsumptionGuid, hsaOperationMemberInfo, hsaMemberInfoCard, remark);
    }

    /**
     * 获取会员资金来往明细guid
     *
     * @param hsaMemberInfoCard        卡
     * @param memberFundingDetail      明细
     * @param remark                   备注
     * @param hsaOperationMemberInfo   会员
     * @param newMemberConsumptionGuid 新消费guid
     * @return
     */
    private String getMemberFundingDetail(HsaMemberInfoCard hsaMemberInfoCard,
                                          HsaMemberFundingDetail memberFundingDetail,
                                          String remark,
                                          HsaOperationMemberInfo hsaOperationMemberInfo,
                                          String newMemberConsumptionGuid) {
        final HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        final String oldFundingDetailGuid = memberFundingDetail.getGuid();
        //生成资金记录
        memberFundingDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberFundingDetail.class.getSimpleName()));

        MerchantPayAssembler.getMemberFundingDetail(hsaMemberInfoCard,
                memberFundingDetail,
                remark,
                hsaOperationMemberInfo,
                newMemberConsumptionGuid,
                headerUserInfo);

        //赠送余额 = 卡余额 + 活动冻结
        setCardGiftResidualBalanceByRefund(hsaMemberInfoCard, memberFundingDetail, oldFundingDetailGuid);
        return oldFundingDetailGuid;
    }


    /**
     * 处理充值退款
     *
     * @param memberConsumption        充值记录
     * @param memberFundingDetail      会员资金来往明细
     * @param newMemberConsumptionGuid 新消费guid
     * @param hsaOperationMemberInfo   会员
     * @param hsaMemberInfoCard        卡
     * @param remark                   备注
     */
    private void handlerRechargeRefundRepository(HsaMemberConsumption memberConsumption, HsaMemberFundingDetail memberFundingDetail,
                                                 String newMemberConsumptionGuid, HsaOperationMemberInfo hsaOperationMemberInfo,
                                                 HsaMemberInfoCard hsaMemberInfoCard, String remark) {
        refundProcessor(memberConsumption, hsaOperationMemberInfo);

        hsaMemberConsumptionMapper.updateByGuid(memberConsumption);
        //保存退款消费记录
        HsaMemberConsumption newMemberConsumption = MerchantPayAssembler.fromConsumerRefund(newMemberConsumptionGuid, memberConsumption, ThreadLocalCache.getHeaderUserInfo(), hsaMemberInfoCard);
        memberConsumption.setRefundTime(LocalDateTime.now());
        newMemberConsumption.setRemark(remark);
        log.info("充值消费回退后的会员消费来往明细，memberConsumption={}", JSON.toJSONString(memberConsumption));
        hsaMemberConsumptionMapper.insert(newMemberConsumption);
        hsaMemberFundingDetailService.save(memberFundingDetail);
        hsaMemberInfoCardMapper.updateByGuid(hsaMemberInfoCard);

        //短信推送
        sendChangeCardAmountShortMessage(hsaMemberInfoCard, newMemberConsumption, hsaOperationMemberInfo, memberFundingDetail);
        sendChangeCardAmountMsg(hsaMemberInfoCard, newMemberConsumption, hsaOperationMemberInfo, memberFundingDetail);
    }

    /**
     * 设置卡赠送剩余余额
     *
     * @param hsaMemberInfoCard    卡
     * @param memberFundingDetail  会员资金来往明细
     * @param oldFundingDetailGuid 会员资金来往明细guid
     */
    private void setCardGiftResidualBalanceByRefund(HsaMemberInfoCard hsaMemberInfoCard, HsaMemberFundingDetail memberFundingDetail, String oldFundingDetailGuid) {
        CardFreezeBalanceAmountQO cardFreeze = new CardFreezeBalanceAmountQO();
        cardFreeze.setPhysicalCardGuid(hsaMemberInfoCard.getPhysicalCardGuid());
        cardFreeze.setElectronicCardGuid(hsaMemberInfoCard.getElectronicCardGuid());
        cardFreeze.setMemberInfoGuid(hsaMemberInfoCard.getMemberInfoGuid());
        cardFreeze.setIsRefresh(BooleanEnum.FALSE.getCode());
        cardFreeze.setExcludeMemberFundingDetail(oldFundingDetailGuid);
        BigDecimal cardFreezeAmount = hsaCardRechargeGiftDetailService.getMemberCardFreezeAmount(cardFreeze);
        memberFundingDetail.setCardGiftResidualBalance(hsaMemberInfoCard.getGiftAmount().add(cardFreezeAmount));
    }

    /**
     * 处理充值送卡金额
     *
     * @param oldFundingDetailGuid 会员资金来往明细guid
     * @param noRefreshGiftDetails 不再刷新的送卡金额
     */
    private void handlerRechargeGiftAmount(String oldFundingDetailGuid,
                                           List<HsaCardRechargeGiftDetail> noRefreshGiftDetails,
                                           HsaMemberConsumption memberConsumption,
                                           HsaOperationMemberInfo hsaOperationMemberInfo) {
        if (!CollUtil.isEmpty(noRefreshGiftDetails)) {
            //记录不再刷新
            noRefreshGiftDetails.forEach(d -> d.setIsRefresh(GiftDetailRefreshEnum.CANCEL.getCode()));
            log.info("开始取消赠送记录：{}", JSON.toJSONString(noRefreshGiftDetails));
            hsaCardRechargeGiftDetailService.updateBatchById(noRefreshGiftDetails);
        }

        barkOrderRechargeGiftDetail(memberConsumption, hsaOperationMemberInfo);

        //作废已发放的优惠券
        int num = hsaMemberCouponLinkMapper.updateStateByConsumptionGuid(ThreadLocalCache.getOperSubjectGuid(), memberConsumption.getGuid());

        log.info("充值退款，作废已发放的优惠券{}张，memberConsumptionGuid={}", num, memberConsumption.getGuid());

        //定时充值赠送金额取消
        hsaRechargeGiftAmountRecordService.updateRechargeStatus(ThreadLocalCache.getOperSubjectGuid(), oldFundingDetailGuid, BooleanEnum.FALSE.getCode());
    }

    /**
     * 回退充值赠送的相关赠品
     * 包括积分、成长值、会员卡的回退处理
     *
     * @param memberConsumption     会员消费记录
     * @param hsaOperationMemberInfo 会员操作信息
     */
    private void barkOrderRechargeGiftDetail(HsaMemberConsumption memberConsumption, HsaOperationMemberInfo hsaOperationMemberInfo) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        log.info("充值退款：开始回退充值赠送的相关权益，memberConsumption={}, headerUserInfo={}",
                JSON.toJSONString(memberConsumption),
                JSON.toJSONString(headerUserInfo));
        if (Objects.isNull(headerUserInfo.getSource())) {
            headerUserInfo.setSource(memberConsumption.getOrderSource());
            ThreadLocalCache.put(JSON.toJSONString(headerUserInfo));
        }
        // 1. 回退充值赠送的积分
        rollbackRechargeGiftIntegral(memberConsumption, hsaOperationMemberInfo);

        // 2. 回退充值赠送的成长值
        rollbackRechargeGiftGrowthValue(memberConsumption, hsaOperationMemberInfo);

        // 3. 回退充值赠送的会员卡
        rollbackRechargeGiftMemberCard(memberConsumption);
    }

    /**
     * 回退充值赠送的积分
     * 查询订单相关的积分明细并执行作废操作
     *
     * @param memberConsumption     会员消费记录
     * @param hsaOperationMemberInfo 会员操作信息
     */
    private void rollbackRechargeGiftIntegral(HsaMemberConsumption memberConsumption, HsaOperationMemberInfo hsaOperationMemberInfo) {
        List<HsaIntegralDetail> integralDetailList = getRechargeGiftIntegralDetails(memberConsumption);

        if (CollUtil.isNotEmpty(integralDetailList)) {
            log.info("充值退款：开始回退积分，共{}条记录", integralDetailList.size());

            for (HsaIntegralDetail integralDetail : integralDetailList) {
                processIntegralRollback(integralDetail, hsaOperationMemberInfo);
            }

            log.info("充值退款：积分回退完成");
        }
    }

    /**
     * 获取充值赠送的积分明细列表
     *
     * @param memberConsumption 会员消费记录
     * @return 积分明细列表
     */
    private List<HsaIntegralDetail> getRechargeGiftIntegralDetails(HsaMemberConsumption memberConsumption) {
        return hsaIntegralDetailMapper.selectList(new LambdaQueryWrapper<HsaIntegralDetail>()
                .eq(HsaIntegralDetail::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaIntegralDetail::getOrderNumber, memberConsumption.getOrderNumber())
                .eq(HsaIntegralDetail::getChangeType, AmountSourceTypeEnum.RECHARGE_GIFT.getCode()));
    }

    /**
     * 处理积分回退操作
     *
     * @param integralDetail        积分明细
     * @param hsaOperationMemberInfo 会员操作信息
     */
    private void processIntegralRollback(HsaIntegralDetail integralDetail, HsaOperationMemberInfo hsaOperationMemberInfo) {
        hsaCardRechargeGiftDetailService.updateIntegralValue(
                integralDetail.getOrderNumber(),
                integralDetail.getStoreName(),
                integralDetail.getIntegral(),
                integralDetail.getDeclaration(),
                1, // 减少积分
                AmountSourceTypeEnum.RECHARGE_REFUND.getCode(),
                hsaOperationMemberInfo);
    }

    /**
     * 回退充值赠送的成长值
     * 查询订单相关的成长值明细并执行作废操作
     *
     * @param memberConsumption     会员消费记录
     * @param hsaOperationMemberInfo 会员操作信息
     */
    private void rollbackRechargeGiftGrowthValue(HsaMemberConsumption memberConsumption, HsaOperationMemberInfo hsaOperationMemberInfo) {
        List<HsaGrowthValueDetail> growthValueDetails = getRechargeGiftGrowthValueDetails(memberConsumption);

        if (CollUtil.isNotEmpty(growthValueDetails)) {
            log.info("充值退款：开始回退成长值，共{}条记录", growthValueDetails.size());

            for (HsaGrowthValueDetail growthValueDetail : growthValueDetails) {
                processGrowthValueRollback(growthValueDetail, hsaOperationMemberInfo);
            }

            log.info("充值退款：成长值回退完成");
        }
    }

    /**
     * 获取充值赠送的成长值明细列表
     *
     * @param memberConsumption 会员消费记录
     * @return 成长值明细列表
     */
    private List<HsaGrowthValueDetail> getRechargeGiftGrowthValueDetails(HsaMemberConsumption memberConsumption) {
        return hsaGrowthValueDetailMapper.selectList(new LambdaQueryWrapper<HsaGrowthValueDetail>()
                .eq(HsaGrowthValueDetail::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaGrowthValueDetail::getOrderNumber, memberConsumption.getOrderNumber())
                .eq(HsaGrowthValueDetail::getChangeType, AmountSourceTypeEnum.RECHARGE_GIFT.getCode()));
    }

    /**
     * 处理成长值回退操作
     *
     * @param growthValueDetail     成长值明细
     * @param hsaOperationMemberInfo 会员操作信息
     */
    private void processGrowthValueRollback(HsaGrowthValueDetail growthValueDetail, HsaOperationMemberInfo hsaOperationMemberInfo) {
        hsaCardRechargeGiftDetailService.updateGrowthValue(
                growthValueDetail.getStoreName(),
                growthValueDetail.getRecordDeclaration(),
                1, // 减少成长值
                hsaOperationMemberInfo,
                growthValueDetail.getOrderNumber(),
                growthValueDetail.getGrowthValue(),
                AmountSourceTypeEnum.RECHARGE_REFUND.getCode());
    }

    /**
     * 回退充值赠送的会员卡
     * 删除无充值记录的赠送会员卡
     *
     * @param memberConsumption 会员消费记录
     */
    private void rollbackRechargeGiftMemberCard(HsaMemberConsumption memberConsumption) {
        List<HsaCardRechargeGiftDetail> giftDetails = getRechargeGiftCardDetails(memberConsumption);

        if (CollUtil.isNotEmpty(giftDetails)) {
            log.info("充值退款：开始回退会员卡，共{}条记录", giftDetails.size());

            List<String> giveCardGuids = extractGiveCardGuids(giftDetails);
            List<HsaMemberInfoCard> memberInfoCards = getMemberInfoCardsByCardGuids(giveCardGuids, memberConsumption);

            if (CollUtil.isNotEmpty(memberInfoCards)) {
                List<String> removableCardGuids = filterRemovableCards(memberInfoCards, memberConsumption);
                removeGiftMemberCards(removableCardGuids);
            }

            log.info("充值退款：会员卡回退完成");
        }
    }

    /**
     * 获取充值赠送的会员卡明细列表
     *
     * @param memberConsumption 会员消费记录
     * @return 会员卡赠送明细列表
     */
    private List<HsaCardRechargeGiftDetail> getRechargeGiftCardDetails(HsaMemberConsumption memberConsumption) {
        return hsaCardRechargeGiftDetailMapper.selectList(new LambdaQueryWrapper<HsaCardRechargeGiftDetail>()
                .select(HsaCardRechargeGiftDetail::getGiveCardGuid)
                .eq(HsaCardRechargeGiftDetail::getOrderNum, memberConsumption.getOrderNumber())
                .isNotNull(HsaCardRechargeGiftDetail::getGiveCardGuid));
    }

    /**
     * 提取赠送卡GUID列表
     *
     * @param giftDetails 赠送明细列表
     * @return 赠送卡GUID列表
     */
    private List<String> extractGiveCardGuids(List<HsaCardRechargeGiftDetail> giftDetails) {
        return giftDetails.stream()
                .map(HsaCardRechargeGiftDetail::getGiveCardGuid)
                .collect(Collectors.toList());
    }

    /**
     * 根据卡GUID获取会员信息卡列表
     *
     * @param giveCardGuids     赠送卡GUID列表
     * @param memberConsumption 会员消费记录
     * @return 会员信息卡列表
     */
    private List<HsaMemberInfoCard> getMemberInfoCardsByCardGuids(List<String> giveCardGuids, HsaMemberConsumption memberConsumption) {
        return hsaMemberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .in(HsaMemberInfoCard::getCardGuid, giveCardGuids)
                .eq(HsaMemberInfoCard::getMemberInfoGuid, memberConsumption.getMemberInfoGuid()));
    }

    /**
     * 筛选可删除的会员卡
     * 只删除没有充值记录的赠送卡
     *
     * @param memberInfoCards   会员信息卡列表
     * @param memberConsumption 会员消费记录
     * @return 可删除的会员卡GUID列表
     */
    private List<String> filterRemovableCards(List<HsaMemberInfoCard> memberInfoCards, HsaMemberConsumption memberConsumption) {
        // 获取有充值记录的会员卡GUID列表
        Set<String> cardsWithFundingRecords = getCardsWithFundingRecords(memberInfoCards, memberConsumption);

        // 筛选出没有充值记录的卡
        List<String> removableCardGuids = Lists.newArrayList();
        for (HsaMemberInfoCard memberInfoCard : memberInfoCards) {
            if (cardsWithFundingRecords.contains(memberInfoCard.getGuid())) {
                log.info("充值退款：回退会员卡，memberInfoCardGuid={}，有充值记录，不处理", memberInfoCard.getGuid());
            } else {
                removableCardGuids.add(memberInfoCard.getGuid());
            }
        }

        return removableCardGuids;
    }

    /**
     * 获取有充值记录的会员卡GUID集合
     *
     * @param memberInfoCards   会员信息卡列表
     * @param memberConsumption 会员消费记录
     * @return 有充值记录的会员卡GUID集合
     */
    private Set<String> getCardsWithFundingRecords(List<HsaMemberInfoCard> memberInfoCards, HsaMemberConsumption memberConsumption) {
        List<String> memberInfoCardGuids = memberInfoCards.stream()
                .map(HsaMemberInfoCard::getGuid)
                .collect(Collectors.toList());

        List<HsaMemberFundingDetail> fundingDetailList = hsaMemberFundingDetailMapper.selectList(
                new LambdaQueryWrapper<HsaMemberFundingDetail>()
                        .in(HsaMemberFundingDetail::getMemberInfoCardGuid, memberInfoCardGuids)
                        .eq(HsaMemberFundingDetail::getMemberInfoGuid, memberConsumption.getMemberInfoGuid()));

        return Optional.ofNullable(fundingDetailList)
                .orElse(Collections.emptyList())
                .stream()
                .map(HsaMemberFundingDetail::getMemberInfoCardGuid)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 删除赠送的会员卡
     *
     * @param removableCardGuids 可删除的会员卡GUID列表
     */
    private void removeGiftMemberCards(List<String> removableCardGuids) {
        if (CollUtil.isNotEmpty(removableCardGuids)) {
            hsaMemberInfoCardMapper.removeByGuids(removableCardGuids);
            log.info("充值退款：删除赠送会员卡，共{}张", removableCardGuids.size());
        }
    }

    /**
     * 删除赠送会员标签
     *
     * @param oldFundingDetailGuid 明细
     * @param memberInfoGuid       会员
     */
    private void removeGiftActivityMemberLabel(String oldFundingDetailGuid, String memberInfoGuid) {
        //取消标签
        final String userStr = ThreadLocalCache.get();
        CompletableFuture.runAsync(() -> {
            //需求确认：退款一次，就删除相关任务标签
            // 需求变更：活动id+会员查询记录，如果没有，则删除标签，有则忽略
            final List<String> memberLabels = hsaCardRechargeGiftDetailService.getActivityDetailsMemberLabels(oldFundingDetailGuid);
            if (CollUtil.isEmpty(memberLabels)) {
                return;
            }
            log.info("充值退款：删除充值赠送活动标签：{}", JacksonUtils.writeValueAsString(memberLabels));
            ThreadLocalCache.put(userStr);
            UpdateLabelStatusQO qo = new UpdateLabelStatusQO();
            qo.setIsConnection(0);
            qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            qo.setLabelGuids(Lists.newArrayList(memberLabels));
            qo.setMemberGuid(memberInfoGuid);
            hsaMemberLabelService.updateCorrelationStatus(qo);
        }, memberBaseThreadExecutor);
    }
}
