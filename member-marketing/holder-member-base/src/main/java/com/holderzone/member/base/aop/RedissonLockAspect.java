package com.holderzone.member.base.aop;

import com.holderzone.member.common.annotation.RedissonLock;
import com.holderzone.member.common.config.redisson.RedissonLockCallback;
import com.holderzone.member.common.config.redisson.RedissonLockTemplate;
import com.holderzone.member.common.exception.MemberBaseException;
import org.apache.commons.beanutils.PropertyUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 */
@Aspect
@Component
public class RedissonLockAspect {
    @Autowired
    private RedissonLockTemplate lockTemplate;

    @AfterThrowing(value = "lockAspect()", throwing="ex")
    public void afterThrowing(Throwable ex) {
        if(ex instanceof MemberBaseException){
            throw (MemberBaseException)ex;
        }
        throw new MemberBaseException(ex.getMessage());
    }

    @Pointcut("@annotation(com.holderzone.member.common.annotation.RedissonLock)")
    public void lockAspect() {
        // default implementation ignored
    }

    @Around(value = "lockAspect()")
    public Object doAround(ProceedingJoinPoint pjp) throws Throwable {
//使用了注解的方法
        String methodName = pjp.getSignature().getName() ;

        Class[] parameterTypes = ((MethodSignature)pjp.getSignature()).getMethod().getParameterTypes();
        //切点所在的类
        Class targetClass = pjp.getTarget().getClass();

        Object[] arguments = pjp.getArgs();

        Method method = targetClass.getMethod(methodName, parameterTypes);

        final String lockName = getLockName(method, arguments);

        return lock(pjp, method, lockName);
    }



    /**
     * 从方法参数获取数据
     *
     * @param param
     * @param arg 方法的参数数组
     * @return
     */
    public Object getParam(Object arg, String param) {
        if (isNotEmpty(param) && arg != null) {
            try {
                return PropertyUtils.getProperty(arg, param);
            } catch (NoSuchMethodException e) {
                e.printStackTrace();
                throw new MemberBaseException(arg + "没有属性" + param + "或未实现get方法。");
            } catch (Exception e) {
                e.printStackTrace();
                throw new MemberBaseException();
            }
        }
        return null;
    }

    public Object lock(ProceedingJoinPoint pjp, final String lockName, boolean fairLock) {
        return lockTemplate.lock(new RedissonLockCallback<Object>() {
            @Override
            public Object process() {
                return proceed(pjp);
            }

            @Override
            public String getLockName() {
                return lockName;
            }
        }, fairLock);
    }

    public Object lock(ProceedingJoinPoint pjp, Method method, final String lockName) {

        RedissonLock annotation = method.getAnnotation(RedissonLock.class);

        boolean fairLock = annotation.fairLock();

        boolean tryLock = annotation.tryLock();

        if (tryLock) {
            return tryLock(pjp, annotation, lockName, fairLock);
        } else {
            return lock(pjp,lockName, fairLock);
        }
    }
    public String getLockName(Method method, Object[] objects) {
        Objects.requireNonNull(method);
        RedissonLock annotation = method.getAnnotation(RedissonLock.class);
        String param = annotation.param();
        String name = annotation.lockName();

        if (isNotEmpty(name)) {
            String preName = annotation.lockNamePre(),
                    postName = annotation.lockNamePost(),
                    separator = annotation.separator();

            StringBuilder lName = new StringBuilder();
            if (isNotEmpty(preName)) {
                lName.append(preName).append(separator);
            }
            lName.append(name);
            if (isNotEmpty(postName)) {
                lName.append(separator).append(postName);
            }
            name = lName.toString();
            return name;
        }

        if (objects.length > 0) {
            if (isNotEmpty(param)) {
                Object arg;
                if (annotation.argNum() > 0) {
                    arg = objects[annotation.argNum() - 1];
                } else {
                    arg = objects[0];
                }
                name = String.valueOf(getParam(arg, param));
            } else if (annotation.argNum() > 0) {
                name = objects[annotation.argNum() - 1].toString();
            }
        }
        return name;
    }
    public Object proceed(ProceedingJoinPoint pjp) {
        try {
            return pjp.proceed();
        } catch (MemberBaseException e){
            throw e;
        }catch (Throwable throwable) {
            throw new MemberBaseException(throwable.getMessage());
        }
    }
    public Object tryLock(ProceedingJoinPoint pjp, RedissonLock annotation, final String lockName, boolean fairLock) {
        TimeUnit timeUnit = annotation.timeUnit();

        long leaseTime = annotation.leaseTime();

        long waitTime = annotation.waitTime();

        return lockTemplate.tryLock(new RedissonLockCallback<Object>() {

            @Override
            public String getLockName() {
                return lockName;
            }
            @Override
            public Object process() {
                return proceed(pjp);
            }

        }, waitTime, leaseTime, timeUnit, fairLock);
    }
    private boolean isEmpty(Object str) {
        return str == null || "".equals(str);
    }

    private boolean isNotEmpty(Object str) {
        return !isEmpty(str);
    }
}
