package com.holderzone.member.base.entity.card;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.annotation.FieldLabel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

 /**
 * <AUTHOR>
 * @description 会员关联会员卡实体
 * @date 2021/8/31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaMemberInfoCard implements Serializable {
    private static final long serialVersionUID = 1L;

    @FieldLabel
    private Long id;

     /**
     * 会员持卡GUID
     */
     @FieldLabel
    private String guid;

     /**
     *
     * 运营主体GUID
     */
     @FieldLabel
    private String operSubjectGuid;

     /**
     *
     * 企业GUID
     */
     @FieldLabel
    private String enterpriseGuid;

     /**
     *
     * 会员GUID
     */
     @FieldLabel
    @TableField(strategy = FieldStrategy.IGNORED)
    private String memberInfoGuid;


     /**
     *
     * 实体卡GUID
     */
     @FieldLabel
    @TableField(strategy = FieldStrategy.IGNORED)
    private String physicalCardGuid;

     /**
     *
     * 电子卡GUID
     */
     @FieldLabel
    @TableField(strategy = FieldStrategy.IGNORED)
    private String electronicCardGuid;

     /**
     *
     * 会员卡GUID
     */
     @FieldLabel
    private String cardGuid;

     /**
     *
     * 卡名称
     */
     @FieldLabel
    private String cardName;

     /**
     *
     * 补贴余额
     */
     @FieldLabel
    private BigDecimal subsidyAmount;

     /**
     *
     * 可退补贴余额
     */
     @FieldLabel
    private BigDecimal retreatSubsidyAmount;

     /**
     *
     * 充值余额
     */
     @FieldLabel
    private BigDecimal cardAmount;

     /**
     *
     * 赠送余额
     */
     @FieldLabel
    private BigDecimal giftAmount;

     /**
     *
     * 超额使用余额
     */
     @FieldLabel
    private BigDecimal excessAmount;

     /**
     *
     * 超额使用次数
     */
     @FieldLabel
    private Integer excessTimes;

     /**
      *
      * 创建时间
      */
     @FieldLabel
    private LocalDateTime gmtCreate;

     /**
      *
      * 更新时间
      */
     @FieldLabel
    private LocalDateTime gmtModified;

     /**
     *
     * 0永久有效1固定时间
     */
     @FieldLabel
    private Integer cardValidity;

    /**
     * 有效期
     */
     @FieldLabel
    private LocalDate cardValidityDate;

     /**
     *
     * 是否适用于所有门店(1:全部门店；0:部分门店(外关联表))
     */
     @FieldLabel
    private Integer applicableAllStore;

     /**
     *
     * 卡颜色
     */
     @FieldLabel
    private String cardColor;

     /**
     *
     * 卡图片
     */
     @FieldLabel
    private String cardImage;

     /**
     *
     * 充值说明
     */
     @FieldLabel
    private String cardRechargeExplain;

     /**
     *
     * 使用须知
     */
     @FieldLabel
    private String cardEmployExplain;

     /**
     *
     * 卡默认支付密码
     */
     @FieldLabel
    private String cardPayPassword;

     /**
     *
     * 是否支持实体卡
     */
     @FieldLabel
    private Integer isSupportPhysicalCard;

     /**
     *
     * 实体卡是否可退（实体卡相关）
     */
     @FieldLabel
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer isPhysicalCardRetreat;

     /**
     *
     * 是否是默认卡 1：是
     */
     @FieldLabel
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer defaultCard;

     /**
     *
     * 电子卡号
     */
     @FieldLabel
    @TableField(strategy = FieldStrategy.IGNORED)
    private String electronicCardNum;

     /**
     *
     * 实体卡号
     */
     @FieldLabel
    @TableField(strategy = FieldStrategy.IGNORED)
    private String physicalCardNum;

     /**
     *
     * 绑定账户手机号
     * 若已绑定会员则是账户手机号，若未绑定则是实体卡手机号
     */
     @FieldLabel
    @TableField(strategy = FieldStrategy.IGNORED)
    private String memberPhoneNum;

     /**
     *
     * 实体卡激活时间
     */
     @FieldLabel
    @TableField(strategy = FieldStrategy.IGNORED)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime physicalCardActivationTime;

     /**
     *
     * 实体卡状态 -1 未激活  0已冻结 1 正常 2 已过期 3 未激活->已冻结
     */
     @FieldLabel
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer physicalCardState;

     /**
     *
     * 电子卡状态 0已冻结 1 正常 2 已过期
     */
     @FieldLabel
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer electronicCardState;

     /**
     *
     * 电子卡开卡时间
     */
     @FieldLabel
    @TableField(strategy = FieldStrategy.IGNORED)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime electronicOpenTime;

     /**
     *
     * 付款账户默认选择
     */
     @FieldLabel
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer defaultChoose;
}
