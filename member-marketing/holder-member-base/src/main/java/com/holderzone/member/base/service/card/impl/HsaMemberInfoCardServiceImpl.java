package com.holderzone.member.base.service.card.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.aimilin.bean.ExcelResult;
import com.aimilin.bean.ExcelType;
import com.aimilin.utils.ExcelWriteUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.assembler.MemberCardAssembler;
import com.holderzone.member.base.assembler.MemberFundingDetailAssembler;
import com.holderzone.member.base.client.RequestGoalgoService;
import com.holderzone.member.base.client.StoreBaseService;
import com.holderzone.member.base.dto.StoreCoordinateDTO;
import com.holderzone.member.base.dto.CardGrantEquitiesDTO;
import com.holderzone.member.base.entity.card.*;
import com.holderzone.member.base.entity.credit.HsaCreditUser;
import com.holderzone.member.base.entity.member.HsaCardBalanceRule;
import com.holderzone.member.base.entity.member.HsaMemberConsumption;
import com.holderzone.member.base.entity.member.HsaMemberFundingDetail;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.entity.recharge.HsaCardRechargeGiftDetail;
import com.holderzone.member.base.entity.system.HsaStoreRuleInfo;
import com.holderzone.member.base.factory.CardTimeFactory;
import com.holderzone.member.base.helper.RechargeGiftActivityHelper;
import com.holderzone.member.base.mapper.activity.HsaSubsidyActivityDetailRecordMapper;
import com.holderzone.member.base.mapper.card.*;
import com.holderzone.member.base.mapper.credit.HsaCreditUserMapper;
import com.holderzone.member.base.mapper.grade.HsaBusinessEquitiesMapper;
import com.holderzone.member.base.mapper.member.*;
import com.holderzone.member.base.mapper.system.HsaStoreRuleInfoMapper;
import com.holderzone.member.base.service.card.*;
import com.holderzone.member.base.service.card.business.TerCardCheckBusinessService;
import com.holderzone.member.base.service.card.business.TerMemberCardBusinessService;
import com.holderzone.member.base.service.credit.HsaCreditInfoService;
import com.holderzone.member.base.service.gift.HsaCardRechargeGiftDetailService;
import com.holderzone.member.base.service.grade.HsaBusinessEquitiesService;
import com.holderzone.member.base.service.grade.IHsaMemberGradeInfoService;
import com.holderzone.member.base.service.growth.AppletGrowthService;
import com.holderzone.member.base.service.member.HsaLabelSettingService;
import com.holderzone.member.base.service.member.HsaMemberFundingDetailService;
import com.holderzone.member.base.service.send.ShortMessageSendService;
import com.holderzone.member.base.transform.card.HsaMemberInfoCardTransform;
import com.holderzone.member.base.transform.member.MemberInfoTransform;
import com.holderzone.member.base.util.StringBaseHandlerUtil;
import com.holderzone.member.base.util.ValidTimeUtils;
import com.holderzone.member.common.annotation.RedissonLock;
import com.holderzone.member.common.client.FileOssService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.QueryStoreBasePage;
import com.holderzone.member.common.dto.base.ResStoreBaseInfo;
import com.holderzone.member.common.dto.base.StoreBaseInfo;
import com.holderzone.member.common.dto.card.*;
import com.holderzone.member.common.dto.coupon.MemberCouponStoreDTO;
import com.holderzone.member.common.dto.coupon.MemberCouponStoreListDTO;
import com.holderzone.member.common.dto.credit.AppletPaymentCreditDTO;
import com.holderzone.member.common.dto.excel.FileDto;
import com.holderzone.member.common.dto.excel.MemberInfoCardExcelVO;
import com.holderzone.member.common.dto.member.*;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.DateUnitEnum;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.SystemPermissionEnum;
import com.holderzone.member.common.enums.card.*;
import com.holderzone.member.common.enums.equities.BusinessTypeEnum;
import com.holderzone.member.common.enums.exception.CardOperationExceptionEnum;
import com.holderzone.member.common.enums.gift.GiftDetailRefreshEnum;
import com.holderzone.member.common.enums.member.*;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.qo.base.SingleDataQO;
import com.holderzone.member.common.qo.card.*;
import com.holderzone.member.common.qo.equities.MemberCalculatePreMoneyQO;
import com.holderzone.member.common.qo.growth.AppletGrowthStorePageQO;
import com.holderzone.member.common.qo.growth.AppletGrowthStoreQO;
import com.holderzone.member.common.qo.growth.AppletStoreListQO;
import com.holderzone.member.common.qo.growth.AppletStoreQO;
import com.holderzone.member.common.qo.member.ImportRechargeAmountQO;
import com.holderzone.member.common.qo.member.MemberCardPasswordQO;
import com.holderzone.member.common.qo.tool.MessagesSendQO;
import com.holderzone.member.common.qrcode.QrCodeSupport;
import com.holderzone.member.common.qrcode.QrcodeServer;
import com.holderzone.member.common.util.DistanceUtils;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.number.NumberUtil;
import com.holderzone.member.common.util.number.SerialNumberBuilder;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.util.verify.VerifyUtil;
import com.holderzone.member.common.vo.card.*;
import com.holderzone.member.common.vo.gift.RechargeGiftActivityVO;
import com.holderzone.member.common.vo.grade.GradeEquitiesVO;
import com.holderzone.member.common.vo.member.MemberLabelVO;
import com.holderzone.member.common.vo.store.AppletStoreVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会员卡绑定服务类
 * @date 2021/9/1
 */
@Service
@Slf4j
public class HsaMemberInfoCardServiceImpl extends HolderBaseServiceImpl<HsaMemberInfoCardMapper, HsaMemberInfoCard> implements HsaMemberInfoCardService {


    private final HsaMemberInfoCardMapper memberInfoCardMapper;

    private final GuidGeneratorUtil guidGeneratorUtil;

    @Lazy
    @Resource
    private HsaCardRechargeGiftDetailService hsaCardRechargeGiftDetailService;

    @Lazy
    @Resource
    private IHsaMemberGradeInfoService hsaMemberGradeInfoService;

    @Lazy
    @Resource
    private RequestGoalgoService hsaRequestGoalgoService;

    @Lazy
    @Resource
    private SystemRoleHelper systemRoleHelper;

    @Lazy
    @Autowired
    private HsaElectronicCardService hsaElectronicCardService;

    private final HsaCardBaseInfoService baseInfoService;

    @Autowired
    private HsaStoreCardRuleMapper hsaStoreCardRuleMapper;

    @Resource
    private HsaStoreRuleInfoMapper hsaStoreRuleInfoMapper;

    @Lazy
    @Autowired
    private HsaStoreCardRuleService hsaStoreCardRuleService;

    @Lazy
    @Autowired
    private StoreBaseService storeBaseService;

    @Lazy
    @Autowired
    private HsaMemberFundingDetailService hsaMemberFundingDetailService;

    @Autowired
    private HsaMemberFundingDetailMapper hsaMemberFundingDetailMapper;

    @Autowired
    private HsaSubsidyActivityDetailRecordMapper hsaSubsidyActivityDetailRecordMapper;

    @Lazy
    @Autowired
    private HsaCardInfoService hsaCardInfoService;

    @Lazy
    @Autowired
    private HsaCreditInfoService hsaCreditInfoService;

    private final HsaPhysicalCardMapper hsaPhysicalCardMapper;

    private final HsaPhysicalCardService hsaPhysicalCardService;

    private final HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    private final HsaCardOpenRuleMapper hsaCardOpenRuleMapper;

    private final HsaCardBaseInfoMapper hsaCardBaseInfoMapper;

    private final HsaCardBalanceRuleMapper hsaCardBalanceRuleMapper;

    @Resource
    private ShortMessageSendService sendService;

    @Resource
    private RequestGoalgoService operatingSubjectService;

    @Resource
    private TerCardCheckBusinessService checkBusinessService;

    @Resource
    private TerMemberCardBusinessService terMemberCardBusinessService;

    @Lazy
    @Resource
    private HsaBusinessEquitiesService hsaBusinessEquitiesService;

    @Resource
    private HsaCreditUserMapper hsaCreditUserMapper;

    @Resource
    public Executor memberBaseThreadExecutor;

    HsaMemberLabelMapper hsaMemberLabelMapper;

    @Resource
    private QrCodeSupport qrCodeSupport;

    @Resource
    private HsaBusinessEquitiesMapper hsaBusinessEquitiesMapper;

    private final SerialNumberBuilder serialNumberBuilder;

    private final HsaLabelSettingService hsaLabelSettingService;

    private final HsaElectronicCardMapper hsaElectronicCardMapper;

    private final HsaDepositStrategyMapper hsaDepositStrategyMapper;

    private final AppletGrowthService appletGrowthService;

    @Resource
    private HsaCardBalanceRuleMapper balanceRuleMapper;

    @Lazy
    @Resource
    private RechargeGiftActivityHelper activityHelper;

    @Autowired
    @Lazy
    private HsaCardInfoService cardInfoService;

    public static final String SEND_CHANNEL = "%53%";

    private static final int DEFAULT_CARD = 1;

    /**
     * 全部门店
     */
    private static final int ALL_STORE = 1;

    /**
     * 资金扣减
     */
    private static final int REDUCE = 1;

    /**
     * 退卡并退款（余额清零）
     */
    private static final int CLEAR_ALL = 0;

    /**
     * 仅退卡（余额保留至电子卡）
     */
    private static final int SAVE_TO_ELECTRONIC = 1;

    /**
     * 会员手机号
     */
    private static final int MEMBER_PHONE_NUM = 11;

    /**
     * 会员卡号
     */
    private static final int MEMBER_CARD_NUM = 12;

    private final FileOssService fileOssService;

    @Resource
    private ExternalSupport externalSupport;

    public HsaMemberInfoCardServiceImpl(HsaMemberInfoCardMapper memberInfoCardMapper,
                                        GuidGeneratorUtil guidGeneratorUtil, HsaCardBaseInfoService baseInfoService,
                                        HsaPhysicalCardMapper hsaPhysicalCardMapper,
                                        @Lazy HsaPhysicalCardService hsaPhysicalCardService,
                                        HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper,
                                        HsaCardOpenRuleMapper hsaCardOpenRuleMapper, HsaCardBaseInfoMapper hsaCardBaseInfoMapper,
                                        HsaCardBalanceRuleMapper hsaCardBalanceRuleMapper,
                                        HsaMemberLabelMapper hsaMemberLabelMapper, HsaLabelSettingService hsaLabelSettingService, QrcodeServer qrcodeServer,
                                        SerialNumberBuilder serialNumberBuilder, HsaElectronicCardMapper hsaElectronicCardMapper,
                                        HsaDepositStrategyMapper hsaDepositStrategyMapper,
                                        FileOssService fileOssService,
                                        @Lazy AppletGrowthService appletGrowthService) {
        this.memberInfoCardMapper = memberInfoCardMapper;
        this.guidGeneratorUtil = guidGeneratorUtil;
        this.baseInfoService = baseInfoService;
        this.hsaPhysicalCardMapper = hsaPhysicalCardMapper;
        this.hsaPhysicalCardService = hsaPhysicalCardService;
        this.hsaOperationMemberInfoMapper = hsaOperationMemberInfoMapper;
        this.hsaCardOpenRuleMapper = hsaCardOpenRuleMapper;
        this.hsaCardBaseInfoMapper = hsaCardBaseInfoMapper;
        this.hsaCardBalanceRuleMapper = hsaCardBalanceRuleMapper;
        this.hsaMemberLabelMapper = hsaMemberLabelMapper;
        this.hsaLabelSettingService = hsaLabelSettingService;
        this.serialNumberBuilder = serialNumberBuilder;
        this.hsaElectronicCardMapper = hsaElectronicCardMapper;
        this.hsaDepositStrategyMapper = hsaDepositStrategyMapper;
        this.fileOssService = fileOssService;
        this.appletGrowthService = appletGrowthService;
    }

    @Override
    public List<HsaMemberInfoCard> savePhysicalCardBind(List<HsaMemberInfoCardDTO> memberInfoCardList) {
        if (CollectionUtil.isEmpty(memberInfoCardList)) {
            return new ArrayList<>();
        }
        List<String> cardGuidList = memberInfoCardList.stream().map(HsaMemberInfoCardDTO::getCardGuid).collect(Collectors.toList());
        List<String> memberGuidList = memberInfoCardList.stream().map(HsaMemberInfoCardDTO::getMemberInfoGuid).collect(Collectors.toList());
        //查询已存在的绑定会员
        //查询已存在的绑定会员
        List<String> memberInfoGuidList = memberInfoCardMapper.findEntityCardGuidByGuidList(cardGuidList, memberGuidList);

        //过滤出需要更新的会员
        List<HsaMemberInfoCardDTO> updateList = memberInfoCardList.stream()
                .filter(s -> ObjectUtil.isNotNull(s.getMemberInfoGuid()) && memberInfoGuidList.contains(s.getMemberInfoGuid()))
                .collect(Collectors.toList());
        //过滤出需要插入的会员
        List<HsaMemberInfoCardDTO> insertList = memberInfoCardList.stream()
                .filter(s -> !memberInfoGuidList.contains(s.getMemberInfoGuid()) ||
                        ObjectUtil.isNull(s.getMemberInfoGuid())).collect(Collectors.toList());

        insertList.forEach(s -> s.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberInfoCardService.class.getSimpleName())));
        insertList.forEach(s -> s.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberInfoCardService.class.getSimpleName())));
        List<HsaMemberInfoCard> memberInfoCardList1 = new ArrayList<>();
        //更新数据
        if (CollUtil.isNotEmpty(updateList)) {
            buildValidityDate(updateList);
            memberInfoCardMapper.updateByCardGuidList(updateList);
            mergeStore(updateList);
            List<String> cardGuids = updateList.stream().map(HsaMemberInfoCardDTO::getCardGuid).collect(Collectors.toList());
            List<String> memberGuids = updateList.stream().filter(t -> !StringUtils.isEmpty(t.getMemberInfoGuid()))
                    .map(HsaMemberInfoCardDTO::getMemberInfoGuid).collect(Collectors.toList());
            memberInfoCardList1 = memberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                    .in(HsaMemberInfoCard::getCardGuid, cardGuids)
                    .in(HsaMemberInfoCard::getMemberInfoGuid, memberGuids));
        }
        if (CollUtil.isNotEmpty(insertList)) {
            buildValidityDate(insertList);
            hsaElectronicCardService.saveStoreCardRule(insertList);
            List<HsaMemberInfoCard> addMemberInfoCardList = HsaMemberInfoCardTransform.INSTANCE.dTO2Entity(insertList);
            this.saveBatch(addMemberInfoCardList);
            memberInfoCardList1.addAll(addMemberInfoCardList);

            //需要赠送会员卡一次性权益
            sendCardRights(addMemberInfoCardList);
        }
        return memberInfoCardList1;
    }

    /**
     * 只针对新开卡用户进行处理
     *
     * @param addMemberInfoCardList addMemberInfoCardList
     */
    private void sendCardRights(List<HsaMemberInfoCard> addMemberInfoCardList) {
        Map<String, List<HsaMemberInfoCard>> hsaMap = addMemberInfoCardList.stream()
                .collect(Collectors.groupingBy(HsaMemberInfoCard::getCardGuid));
        hsaMap.forEach((key, value) -> {
            CardGrantEquitiesDTO cardGrantEquitiesDTO = new CardGrantEquitiesDTO();
            cardGrantEquitiesDTO.setCardGuid(key);
            cardGrantEquitiesDTO.setHsaMemberInfoCardList(value);
            hsaBusinessEquitiesService.grantCardRights(cardGrantEquitiesDTO);
        });
    }

    private void mergeStore(List<HsaMemberInfoCardDTO> updateList) {
        Map<String, HsaMemberInfoCard> hsaMemberInfoCardMap = memberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                        .in(HsaMemberInfoCard::getCardGuid, updateList.stream().map(HsaMemberInfoCardDTO::getCardGuid).collect(Collectors.toList()))
                        .in(HsaMemberInfoCard::getMemberInfoGuid, updateList.stream().map(HsaMemberInfoCardDTO::getMemberInfoGuid).collect(Collectors.toList())))
                .stream()
                .collect(Collectors.toMap(HsaMemberInfoCard::getMemberInfoGuid, Function.identity(), (entity1, entity2) -> entity1));
        for (HsaMemberInfoCardDTO hsaMemberInfoCardDTO : updateList) {
            if (CollUtil.isNotEmpty(hsaMemberInfoCardMap) && hsaMemberInfoCardMap.containsKey(hsaMemberInfoCardDTO.getMemberInfoGuid())) {
                HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMap.get(hsaMemberInfoCardDTO.getMemberInfoGuid());
                //门店更新
                hsaStoreCardRuleService.mergeStoreProcessor(hsaMemberInfoCard, hsaMemberInfoCard.getCardGuid());
            }
        }
    }

    private void buildValidityDate(List<HsaMemberInfoCardDTO> list) {
        List<String> collectCardGuid = list.stream().map(HsaMemberInfoCardDTO::getCardGuid).collect(Collectors.toList());
        List<HsaCardBaseInfo> baseInfos = baseInfoService.queryByGuids(collectCardGuid);
        Map<String, HsaCardBaseInfo> infoMap = baseInfos.stream().collect(Collectors.toMap(HsaCardBaseInfo::getGuid, obj -> obj));
        for (HsaMemberInfoCardDTO dto : list) {
            HsaCardBaseInfo info = infoMap.get(dto.getCardGuid());
            if (ObjectUtil.isNotNull(info)) {
                dto.setCardValidity(info.getCardValidity() == 0 ? 0 : 1);
                if (ObjectUtil.equal(CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_DAY.getCode(), info.getCardValidity())) {
                    //根据时间单位计算有效期
                    dto.setCardValidityDate(ValidTimeUtils.buildValidityDate(info.getValidityUnit(), info.getCardValidityTime()));
                }
                if (ObjectUtil.equal(CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_YEAR.getCode(), info.getCardValidity())) {
                    dto.setCardValidityDate(info.getCardValidityDate());
                }
            }
        }
    }

    @Override
    public void updateElectronicCardBind(List<MemberCardBindDTO> entities2BindDTOs) {
        List<String> collectCardGuid = entities2BindDTOs.stream().map(MemberCardBindDTO::getCardGuid).collect(Collectors.toList());
        List<HsaCardBaseInfo> baseInfos = baseInfoService.queryByGuids(collectCardGuid);
        Map<String, HsaCardBaseInfo> infoMap = baseInfos.stream().collect(Collectors.toMap(HsaCardBaseInfo::getGuid, obj -> obj));

        List<HsaMemberInfoCardDTO> memberInfoCardList = new ArrayList<>();

        for (MemberCardBindDTO bindDTO : entities2BindDTOs) {
            HsaCardBaseInfo info = infoMap.get(bindDTO.getCardGuid());
            if (ObjectUtil.isNotNull(info)) {
                bindDTO.setCardValidity(info.getCardValidity() == 0 ? 0 : 1);
                if (ObjectUtil.equal(CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_DAY.getCode(), info.getCardValidity())) {
                    //根据时间单位计算有效期
                    bindDTO.setCardValidityDate(ValidTimeUtils.buildValidityDate(info.getValidityUnit(), info.getCardValidityTime()));
                }
                if (ObjectUtil.equal(CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_YEAR.getCode(), info.getCardValidity())) {
                    bindDTO.setCardValidityDate(info.getCardValidityDate());
                }
            }

            HsaMemberInfoCardDTO hsaMemberInfoCard = new HsaMemberInfoCardDTO();
            hsaMemberInfoCard.setCardGuid(bindDTO.getCardGuid());
            hsaMemberInfoCard.setMemberInfoGuid(bindDTO.getMemberInfoGuid());
            memberInfoCardList.add(hsaMemberInfoCard);
        }
        memberInfoCardMapper.updateElectronicCardBind(entities2BindDTOs);
        //更新电子卡关系去更新绑定门店
        mergeStore(memberInfoCardList);
    }

    @Override
    public List<MemberCardGuidDTO> findCardGuidByGuidList(List<String> cardGuidList, String memberGuid) {
        return memberInfoCardMapper.findCardGuidByGuidList(cardGuidList, Collections.singletonList(memberGuid));
    }

    @Override
    public List<HsaMemberInfoCard> batchSave(List<HsaMemberInfoCardDTO> insertCollect) {
        buildValidityDate(insertCollect);
        List<HsaMemberInfoCard> hsaMemberInfoCards = HsaMemberInfoCardTransform.INSTANCE.dTO2Entity(insertCollect);
        this.saveBatch(hsaMemberInfoCards);

        //需要赠送会员卡一次性权益
        sendCardRights(hsaMemberInfoCards);
        return hsaMemberInfoCards;
    }

    @Override
    public PageResult listAbleECardPage(AbleOpenECardQO qo) {
        if (ObjectUtil.isNull(qo) || ObjectUtil.isNull(qo.getMemberInfoGuid())) {
            throw new MemberBaseException(CardOperationExceptionEnum.CREATE_PHYSICAL_CARD_SECRET_PARAMS_NULL);
        }
        //进行分页
        PageHelper.startPage(qo.getCurrentPage(), qo.getPageSize());
        qo.setEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid());
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        List<AbleECardVO> list = memberInfoCardMapper.listAbleECard(qo);
        return PageUtil.getPageResult(new PageInfo<>(list));
    }

    @Override
    public PageResult listOwnCardPage(OwnCardQO qo) {
        if (ObjectUtil.isNull(qo) || ObjectUtil.isNull(qo.getMemberInfoGuid())) {
            throw new MemberBaseException(CardOperationExceptionEnum.CREATE_PHYSICAL_CARD_SECRET_PARAMS_NULL);
        }
        //进行分页
        PageHelper.startPage(qo.getCurrentPage(), qo.getPageSize());
        List<OwnCardVO> list = getOwnCardVOList(qo);

        //TODO 后续需要统计门店信息
        return PageUtil.getPageResult(new PageInfo<>(list));
    }

    private List<OwnCardVO> getOwnCardVOList(OwnCardQO qo) {
        List<OwnCardVO> list = memberInfoCardMapper.listOwnCardPage(qo);
        if (CollUtil.isNotEmpty(list)) {
            Map<String, HsaCardOpenRule> hsaCardOpenRuleMap = hsaCardOpenRuleMapper.selectList(new LambdaQueryWrapper<HsaCardOpenRule>()
                            .in(HsaCardOpenRule::getCardGuid, list.stream().map(OwnCardVO::getCardGuid).collect(Collectors.toSet())))
                    .stream()
                    .collect(Collectors.toMap(HsaCardOpenRule::getCardGuid, Function.identity(), (entity1, entity2) -> entity1));
            forDealOwnCardList(list, hsaCardOpenRuleMap);
        }
        return list;
    }

    private void forDealOwnCardList(List<OwnCardVO> list, Map<String, HsaCardOpenRule> hsaCardOpenRuleMap) {
        list.forEach(in -> {
            if (CollUtil.isNotEmpty(hsaCardOpenRuleMap) && hsaCardOpenRuleMap.containsKey(in.getCardGuid())) {
                HsaCardOpenRule hsaCardOpenRule = hsaCardOpenRuleMap.get(in.getCardGuid());
                in.setIsSupportElectronicCard(hsaCardOpenRule.getIsSupportElectronicCard());
                in.setIsSupportPhysicalCard(hsaCardOpenRule.getIsSupportPhysicalCard());
            }
            if (in.getApplicableAllStore() == BooleanEnum.FALSE.getCode()) {
                List<String> ss = new ArrayList<>();
                ss.add(in.getCardGuid());
                List<StoreCardRuleQO> aa = getStoreCardRule(ss, in.getMemberInfoCardGuid());
                in.setStoreList(aa);
            }
            // 退卡规则
            in.setRefundCardRule(0);
            HsaCardBalanceRule hsaCardBalanceRule = hsaCardBalanceRuleMapper.selectOne(
                    new LambdaQueryWrapper<HsaCardBalanceRule>()
                            .eq(HsaCardBalanceRule::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
            if (!ObjectUtils.isEmpty(hsaCardBalanceRule)) {
                in.setRefundCardRule(hsaCardBalanceRule.getRefundCardRule());
            }

            //冻结金额
            CardFreezeBalanceAmountQO freezeBalanceAmountQO = new CardFreezeBalanceAmountQO();
            freezeBalanceAmountQO.setMemberInfoCardGuid(in.getMemberInfoCardGuid());
            BigDecimal freezeAmount = hsaCardRechargeGiftDetailService.getMemberCardFreezeAmount(freezeBalanceAmountQO);
            in.setGiftAmount(in.getGiftAmount().add(freezeAmount));
            in.setCardBalance(in.getCardBalance().add(freezeAmount));

            //获取卡权益
            in.setEquitiesDetailVOS(hsaBusinessEquitiesService.getEquitiesName(in.getCardGuid()));
        });
    }

    @Override
    public List<MiniProgramCardDTO> listMiniProgramCard(ListMiniProgramCardQO qo) {
        List<MiniProgramCardDTO> list = new ArrayList<>();
        StopWatch watch1 = new StopWatch();
        StopWatch watch2 = new StopWatch();
        switch (qo.getType()) {
            case 0:
                watch1.start("Type0正常-" + qo.getMemberInfoGuid());
                list = findNormalCard(qo, watch2);
                //冻结金额计算
                watch2.start("dealFreeze");
                dealFreeze(list);
                watch2.stop();
                watch1.stop();
                break;
            case 1:
                watch1.start("Type1已过期-" + qo.getMemberInfoGuid());
                watch2.start("Type1已过期-findMiniProgramCard");
                list = memberInfoCardMapper.findMiniProgramCard(qo);
                watch2.stop();
                watch2.start("dealFreeze");
                dealFreeze(list);
                watch2.stop();
                watch1.stop();
                break;
            case 2:
                watch1.start("Type2未开通-" + qo.getMemberInfoGuid());
                qo.setSendChannel(SEND_CHANNEL);
                watch2.start("findUnRelationMiniProgramCard");
                list = memberInfoCardMapper.findUnRelationMiniProgramCard(qo);
                watch2.stop();
                //查询已开通会员卡
                qo.setType(2);
                watch2.start("Type2未开通-findMiniProgramCard");
                list.addAll(memberInfoCardMapper.findMiniProgramCard(qo));
                watch2.stop();
                watch2.start("for-setMoney");
                replenishMiniProgramCardDTO(list);
                watch2.stop();
                watch1.stop();
                break;
            default:
        }
        if (CollUtil.isNotEmpty(list)) {
            watch1.start("listIsNotEmpty-" + qo.getMemberInfoGuid());
            // 优化逻辑参考方法：hsaMemberGradeInfoService.addGradeEquities()
            batchAddGradeEquities(qo, list);
            watch1.stop();
        }

        log.warn("watch1耗时：{}", watch1.prettyPrint());
        log.warn("watch2耗时:{}", watch2.prettyPrint());
        log.info("查询会员卡列表返回参数：{}", list);
        return list;
    }

    private void replenishMiniProgramCardDTO(List<MiniProgramCardDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> cardGuidList = list.stream()
                .map(MiniProgramCardDTO::getCardGuid)
                .distinct()
                .collect(Collectors.toList());
        List<HsaStoreCardRule> storeCardRuleList = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .select(HsaStoreCardRule::getGuid, HsaStoreCardRule::getCardGuid)
                .in(HsaStoreCardRule::getCardGuid, cardGuidList)
                .isNull(HsaStoreCardRule::getMemberInfoCardGuid)
                .isNull(HsaStoreCardRule::getParentGuid));
        Map<String, List<HsaStoreCardRule>> storeListMap = storeCardRuleList.stream()
                .collect(Collectors.groupingBy(HsaStoreCardRule::getCardGuid));
        for (MiniProgramCardDTO dto : list) {
            setDTOMoney(dto);
            List<HsaStoreCardRule> cardRuleList = storeListMap.get(dto.getCardGuid());
            dto.setStoreNum(CollectionUtils.isEmpty(cardRuleList) ? NumberConstant.NUMBER_0 : cardRuleList.size());
        }
    }

    private void batchAddGradeEquities(ListMiniProgramCardQO qo, List<MiniProgramCardDTO> list) {
        List<String> memberGradeGuidList = list.stream()
                .map(MiniProgramCardDTO::getCardGuid)
                .distinct()
                .collect(Collectors.toList());
        //查询当前会员等级所有权益
        List<GradeEquitiesVO> gradeEquitiesVOList = hsaBusinessEquitiesMapper.batchCardEquitieInfo(memberGradeGuidList);
        if (!CollUtil.isEmpty(gradeEquitiesVOList)) {
            for (GradeEquitiesVO gradeEquitieInfo : gradeEquitiesVOList) {
                String equitiesImg = gradeEquitieInfo.getEquitiesImg();
                if (StringUtils.isNotBlank(equitiesImg)) {
                    String[] split = equitiesImg.split(StringConstant.COMMA);
                    List<String> iconList = Arrays.stream(split).collect(Collectors.toList());
                    gradeEquitieInfo.setIcon(iconList);
                }
                //set微信小程序所需要的参数信息
                hsaMemberGradeInfoService.setWeChatInfo(qo.getMemberInfoGuid(), gradeEquitieInfo);
            }
            Map<String, List<GradeEquitiesVO>> gradeEquitiesMap = gradeEquitiesVOList.stream()
                    .collect(Collectors.groupingBy(GradeEquitiesVO::getMemberGradeInfoGuid));
            list.forEach(dto -> {
                List<GradeEquitiesVO> equitiesVOList = new ArrayList<>();
                List<GradeEquitiesVO> mapList = gradeEquitiesMap.get(dto.getCardGuid());
                if (!CollectionUtils.isEmpty(mapList)) {
                    equitiesVOList.addAll(mapList);
                }
                dto.setGradeEquitiesVOList(equitiesVOList);
            });
        }
    }

    private void dealFreeze(List<MiniProgramCardDTO> list) {
        for (MiniProgramCardDTO miniProgramCardDTO : list) {
            if (StringUtils.isNotBlank(miniProgramCardDTO.getMemberInfoCardGuid())) {
                CardFreezeBalanceAmountQO cardQO = new CardFreezeBalanceAmountQO();
                cardQO.setMemberInfoCardGuid(miniProgramCardDTO.getMemberInfoCardGuid());
                cardQO.setIsRefresh(BooleanEnum.FALSE.getCode());
                BigDecimal amount = hsaCardRechargeGiftDetailService.getMemberCardFreezeAmount(cardQO);
                miniProgramCardDTO.setFreezeAmount(amount);
            }
        }
    }

    private List<MiniProgramCardDTO> findNormalCard(ListMiniProgramCardQO qo, StopWatch watch2) {
        watch2.start("findMiniProgramCard");
        List<MiniProgramCardDTO> list = memberInfoCardMapper.findMiniProgramCard(qo);
        watch2.stop();
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        HsaCardBalanceRule hsaCardBalanceRule = hsaCardBalanceRuleMapper.selectOne(new LambdaQueryWrapper<HsaCardBalanceRule>()
                .eq(HsaCardBalanceRule::getOperSubjectGuid, qo.getOperSubjectGuid()));
        watch2.start("addQrCode");
        addQrCode(list, hsaCardBalanceRule);
        watch2.stop();
        //过滤适用门店的会员卡
        if (StringUtils.isNotBlank(qo.getStoreGuid())) {
            watch2.start("usableStoreFilter");
            usableStoreFilter(list, qo);
            watch2.stop();
        }
        final boolean isApplet = Objects.equals(ThreadLocalCache.getSource(), SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode());
        //可充值的卡
        final List<String> cardGuidList = list.stream().map(MiniProgramCardDTO::getCardGuid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cardGuidList)) {
            return list;
        }
        watch2.start("getActivityList");
        final List<RechargeGiftActivityVO> activityList = activityHelper.getActivityList(qo.getMemberInfoGuid(), cardGuidList, qo.getStoreGuid());
        watch2.stop();
        if (CollectionUtils.isEmpty(activityList)) {
            return list;
        }
        watch2.start("parallelStream-forEach");
        list.parallelStream().forEach(cardDTO -> {
            if (isRechargeCard(isApplet, cardDTO)) {
                //充值赠送提示
                String rechargeTips = activityHelper.buildRechargeTipsByCard(cardDTO.getCardGuid(), activityList);
                cardDTO.setRechargeTips(rechargeTips);
            }
        });
        watch2.stop();
        return list;
    }

    /**
     * 卡可充值，且小程序可充值,卡未禁用、且已领电子卡或已领实体卡启用
     *
     * @param isApplet
     * @param cardDTO
     * @return
     */
    private static boolean isRechargeCard(boolean isApplet, MiniProgramCardDTO cardDTO) {
        return Objects.equals(cardDTO.getCardStatus(), CardStatusEnum.ENABLE.getCode())
                && (
                Objects.equals(cardDTO.getCardState(), EletronicCardStateEnum.NORMAL.getCode())
                        ||
                        Objects.equals(cardDTO.getPhysicalCardState(), PhysicalCardStateEnum.NORMAL.getCode())
        )
                && Objects.equals(cardDTO.getIsPreStored(), 1)
                && (!isApplet || Objects.equals(cardDTO.getAppletRecharge(), 1));
    }

    @Override
    public List<MiniProgramCardDTO> listBySettlement(ListMiniProgramCardQO qo) {
        //① 当前会员账户中已开通的使用状态"启用中"的、状态"正常"的会员电子卡
        List<MiniProgramCardDTO> list = memberInfoCardMapper.findPayMiniProgramCard(qo);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        //② 当前下单门店/档口在适用范围内的会员卡
        return filterSettlementCardStore(qo, list);
    }

    /**
     * 过滤门店不对的卡
     *
     * @param qo   门店入参
     * @param list 卡列表
     * @return 卡
     */
    private List<MiniProgramCardDTO> filterSettlementCardStore(ListMiniProgramCardQO qo,
                                                               List<MiniProgramCardDTO> list) {
        //部分门店
        List<String> partStoreList = list.stream()
                .filter(x -> x.getApplicableAllStore() == BooleanEnum.FALSE.getCode())
                .map(MiniProgramCardDTO::getOwnGuid)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(partStoreList)) {
            return list;
        }
        //不满足条件的会员卡集合
        Set<String> partStoreCards = hsaStoreCardRuleMapper.selectList(
                        new LambdaQueryWrapper<HsaStoreCardRule>()
                                .eq(HsaStoreCardRule::getOperSubjectGuid, qo.getOperSubjectGuid())
                                .eq(HsaStoreCardRule::getStoreGuid, qo.getStoreGuid())
                                .in(HsaStoreCardRule::getMemberInfoCardGuid, partStoreList))
                .stream()
                .map(HsaStoreCardRule::getMemberInfoCardGuid)
                .collect(Collectors.toSet());
        //移除不存在门店
        list.removeIf(card -> card.getApplicableAllStore() == BooleanEnum.FALSE.getCode() && !partStoreCards.contains(card.getOwnGuid()));
        return list;
    }

    @Override
    public List<AppletPaymentAccountVO> queryAppletPaymentAccount(ListMiniProgramCardQO qo) {
        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        //可付款的会员卡
        List<AppletPaymentAccountVO> appletPaymentAccountVOS = memberInfoCardMapper.
                queryAppletPaymentAccount(operSubjectGuid, qo.getMemberInfoGuid());
        if (CollUtil.isNotEmpty(appletPaymentAccountVOS)) {
            dealAppletPaymentAccountVOS(qo, operSubjectGuid, appletPaymentAccountVOS);
        }

        //返回的付款账户
        List<AppletPaymentAccountVO> list = new ArrayList<>();
        //可付款的挂账账户
        List<AppletPaymentCreditDTO> appletPaymentCredits = hsaCreditUserMapper.queryAppletPaymentAccount(operSubjectGuid,
                qo.getMemberInfoGuid());
        //可选挂账账户
        List<AppletPaymentAccountVO> optionalCreditAccounts = new ArrayList<>();
        getAppletPaymentAccountVO(qo, appletPaymentCredits, optionalCreditAccounts);
        //付款账户显示顺序 优先显示可选会员卡→可选挂账账户→不可选会员卡→不可选挂账账户
        list.addAll(appletPaymentAccountVOS);
        list.addAll(optionalCreditAccounts
                .stream()
                .sorted(Comparator.comparing(AppletPaymentAccountVO::getAccountMoney)
                        .reversed())
                .collect(Collectors.toList()));
        return list;
    }

    private void dealAppletPaymentAccountVOS(ListMiniProgramCardQO qo, String operSubjectGuid, List<AppletPaymentAccountVO> appletPaymentAccountVOS) {
        HsaCardBalanceRule hsaCardBalanceRule = hsaCardBalanceRuleMapper.selectOne(new LambdaQueryWrapper<HsaCardBalanceRule>()
                .eq(HsaCardBalanceRule::getOperSubjectGuid, operSubjectGuid));
        //包含：充值活动
        Map<String, MiniProgramCardDTO> miniProgramCardDTOMaps = this.listMiniProgramCard(
                        new ListMiniProgramCardQO()
                                .setMemberInfoGuid(qo.getMemberInfoGuid())
                                .setStoreGuid(qo.getStoreGuid())
                                .setType(NumberConstant.NUMBER_0)
                                .setOperSubjectGuid(operSubjectGuid)
                                .setGiftActivity(false)
                )
                .stream()
                .collect(Collectors.toMap(MiniProgramCardDTO::getOwnGuid, Function.identity(), (entity1, entity2) -> entity1));
        int isPassword = getIsPassword(hsaCardBalanceRule);
        appletPaymentAccountVOS.forEach(in -> {
            MiniProgramCardDTO miniProgramCardDTO = miniProgramCardDTOMaps.get(in.getOwnGuid());
            if (Objects.nonNull(miniProgramCardDTO)) {
                in.setCardState(miniProgramCardDTO.getCardState())
                        .setCardStatus(miniProgramCardDTO.getCardStatus())
                        .setIsUsableStore(miniProgramCardDTO.getIsUsableStore())
                        .setCardGuid(miniProgramCardDTO.getCardGuid())
                        .setQrCode(miniProgramCardDTO.getQrCode())
                        .setIsPassword(isPassword)
                        .setRechargeTips(miniProgramCardDTO.getRechargeTips())
                ;
            }
            in.setType(NumberConstant.NUMBER_0);
        });
    }

    private void getAppletPaymentAccountVO(ListMiniProgramCardQO qo, List<AppletPaymentCreditDTO> appletPaymentCredits, List<AppletPaymentAccountVO> optionalCreditAccounts) {
        for (AppletPaymentCreditDTO item : appletPaymentCredits) {
            AppletPaymentAccountVO appletPaymentAccountVO = new AppletPaymentAccountVO();
            appletPaymentAccountVO.setOwnGuid(item.getOwnGuid());
            appletPaymentAccountVO.setName(item.getName());
            appletPaymentAccountVO.setType(1);
            appletPaymentAccountVO.setSingleCountUpperLimit(item.getSingleCountUpperLimit());
            dealAccountMoney(item, appletPaymentAccountVO);
            appletPaymentAccountVO.setDefaultChoose(item.getDefaultChoose());
            Integer applicableAllStore = item.getApplicableAllStore();
            if (Objects.nonNull(applicableAllStore) && BooleanEnum.FALSE.getCode() == applicableAllStore) {
                // 门店和档口平级展示
                appletPaymentAccountVO.setIsUsableStore(isUsableStore(qo.getStoreGuid(), item.getCreditGuid()));
            } else {
                appletPaymentAccountVO.setIsUsableStore(Boolean.TRUE);
            }
            optionalCreditAccounts.add(appletPaymentAccountVO);
        }
    }

    private void dealAccountMoney(AppletPaymentCreditDTO item, AppletPaymentAccountVO appletPaymentAccountVO) {
        if (item.getSinglePersonUpperLimit().compareTo(BigDecimal.ZERO) == NumberConstant.NUMBER_0) {
            appletPaymentAccountVO.setAccountMoney(hsaCreditInfoService.getRemainingAmount(item.getCreditGuid(), item.getCreditLimitedAmount(), item.getCreditLimitedSet()));
        } else {
            appletPaymentAccountVO.setAccountMoney(item.getSinglePersonUpperLimit().subtract(item.getTotalCredit()));
        }
    }

    private static int getIsPassword(HsaCardBalanceRule hsaCardBalanceRule) {
        int isPassword;
        if (Objects.nonNull(hsaCardBalanceRule)) {
            if (hsaCardBalanceRule.getUseCheck() == NumberConstant.NUMBER_1
                    && hsaCardBalanceRule.getAppletsCheckState() == NumberConstant.NUMBER_1) {
                isPassword = BooleanEnum.TRUE.getCode();
            } else {
                isPassword = BooleanEnum.FALSE.getCode();
            }
        } else {
            isPassword = BooleanEnum.TRUE.getCode();
        }
        return isPassword;
    }

    /**
     * 判断当前门店是否可以使用挂账账户
     *
     * @param storeGuid      会员卡信息
     * @param creditInfoGuid 挂账信息guid
     */
    private Boolean isUsableStore(String storeGuid, String creditInfoGuid) {
        List<HsaStoreRuleInfo> applyStoreList = hsaStoreRuleInfoMapper.selectList(
                new LambdaQueryWrapper<HsaStoreRuleInfo>()
                        .eq(HsaStoreRuleInfo::getStoreGuid, storeGuid)
                        .eq(HsaStoreRuleInfo::getTypeGuid, creditInfoGuid));
        return CollUtil.isNotEmpty(applyStoreList);
    }

    @Override
    public boolean setDefaultChoose(String memberGuid, int type, String typeGuid) {

        //将会员卡所有默认选择变为非默认
        memberInfoCardMapper.updateDefaultChooseByMemberGuid(memberGuid);
        //将挂账账户所有默认选择变为非默认
        hsaCreditUserMapper.updateDefaultChooseByMemberGuid(memberGuid);
        //会员卡
        if (type == 0) {
            HsaMemberInfoCard newDefaultCard = this.queryByGuid(typeGuid);
            newDefaultCard.setDefaultChoose(BooleanEnum.TRUE.getCode());
            this.updateByGuid(newDefaultCard);
        } else {
            HsaCreditUser hsaCreditUser = hsaCreditUserMapper.queryByGuid(typeGuid);
            hsaCreditUser.setDefaultChoose(BooleanEnum.TRUE.getCode());
            hsaCreditUserMapper.updateByGuid(hsaCreditUser);
        }
        return true;
    }

    /**
     * 过滤适用门店的会员卡
     *
     * @param list 会员卡信息
     * @param qo   请求参数
     */
    private void usableStoreFilter(List<MiniProgramCardDTO> list, ListMiniProgramCardQO qo) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<String> memberCardGuids = list.stream().filter(x -> x.getApplicableAllStore() == 0)
                .map(MiniProgramCardDTO::getMemberInfoCardGuid).collect(Collectors.toList());
        if (CollUtil.isEmpty(memberCardGuids)) {
            return;
        }
        //不满足条件的会员卡集合
        List<String> removeCard = new ArrayList<>();
        List<String> hsaStoreCardRules = hsaStoreCardRuleMapper.selectList(
                        new LambdaQueryWrapper<HsaStoreCardRule>()
                                .eq(HsaStoreCardRule::getOperSubjectGuid, qo.getOperSubjectGuid())
                                .eq(HsaStoreCardRule::getStoreGuid, qo.getStoreGuid())
                                .in(HsaStoreCardRule::getMemberInfoCardGuid, memberCardGuids))
                .stream().map(HsaStoreCardRule::getMemberInfoCardGuid).collect(Collectors.toList());
        if (CollUtil.isEmpty(hsaStoreCardRules)) {
            removeCard.addAll(memberCardGuids);
        } else {
            List<String> collect = memberCardGuids.stream()
                    .filter(x -> !hsaStoreCardRules.contains(x)).collect(Collectors.toList());
            removeCard.addAll(collect);
        }
        if (CollUtil.isEmpty(removeCard)) {
            return;
        }
        for (MiniProgramCardDTO miniProgramCard : list) {
            String memberInfoCardGuid = miniProgramCard.getMemberInfoCardGuid();
            int applicableAllStore = miniProgramCard.getApplicableAllStore();
            //不适用门店的设置为false
            if (applicableAllStore == 0 && removeCard.contains(memberInfoCardGuid)) {
                miniProgramCard.setIsUsableStore(false);
            }
        }
    }

    private List<MiniProgramCardDTO> addQrCode(List<MiniProgramCardDTO> list, HsaCardBalanceRule hsaCardBalanceRule) {
        //如果没有默认会员卡，这个第一个为默认会员卡并保存
        List<String> defaultCardGuids = list
                .stream()
                .filter(x -> Objects.nonNull(x.getDefaultCard()) && x.getDefaultCard() == 1)
                .map(MiniProgramCardDTO::getMemberInfoCardGuid)
                .collect(Collectors.toList());

        //若没有默认再设置默认  历史问题修改
        if (CollUtil.isEmpty(defaultCardGuids)) {
            MiniProgramCardDTO cardDTO = list.get(0);
            memberInfoCardMapper.updateDefaultCardByGuid(cardDTO.getMemberInfoCardGuid());
        }

        list.forEach(defaultCardDto -> {
            if (Objects.nonNull(hsaCardBalanceRule)) {
                if (hsaCardBalanceRule.getUseCheck() != NumberConstant.NUMBER_0) {
                    defaultCardDto.setIsPassword(hsaCardBalanceRule.getAppletsCheckState());
                } else {
                    defaultCardDto.setIsPassword(BooleanEnum.FALSE.getCode());
                }
            } else {
                defaultCardDto.setIsPassword(BooleanEnum.TRUE.getCode());
            }
            defaultCardDto.setCardType(StringUtils.isNotBlank(defaultCardDto.getElectronicCardNum()) ? 1 : 0);
            defaultCardDto.setQrCode(qrCodeSupport.getQrStr(defaultCardDto.getElectronicCardNum(), QrcodeTypeEnum.MEMBER.getDes()));
            defaultCardDto.setStoreNum(hsaStoreCardRuleMapper.selectCount(new LambdaQueryWrapper<HsaStoreCardRule>()
                    .eq(HsaStoreCardRule::getCardGuid, defaultCardDto.getCardGuid())
                    .eq(HsaStoreCardRule::getMemberInfoCardGuid, defaultCardDto.getOwnGuid())
                    .isNull(HsaStoreCardRule::getParentGuid)));
        });
        return list;
    }

    @Override
    public Integer queryOpenCardRuleStatus(String cardGuid) {
        HsaCardOpenRule rule = hsaCardOpenRuleMapper.selectOne(new LambdaQueryWrapper<HsaCardOpenRule>()
                .eq(HsaCardOpenRule::getCardGuid, cardGuid));
        if (Objects.isNull(rule)) {
            return null;
        }
        return rule.getSendStatus();
    }

    @Override
    @Transactional
    public void setDefaultValue(String memberGuid, String newGuid) {

        if (StringUtils.isEmpty(newGuid)) {
            throw new MemberBaseException(CardOperationExceptionEnum.CREATE_PHYSICAL_CARD_SECRET_PARAMS_NULL);
        }
        memberInfoCardMapper.updateDefaultCardByMemberGuid(memberGuid);
        HsaMemberInfoCard newDefaultCard = this.queryByGuid(newGuid);
        newDefaultCard.setDefaultCard(DEFAULT_CARD);
        this.updateByGuid(newDefaultCard);
    }

    /**
     * 设置应付金额、立省金额
     *
     * @param dto 小程序会员卡dto
     */
    private void setMoney(MiniProgramCardDTO dto) {
        setDTOMoney(dto);
        dto.setStoreNum(hsaStoreCardRuleMapper.selectCount(new LambdaQueryWrapper<HsaStoreCardRule>()
                .eq(HsaStoreCardRule::getCardGuid, dto.getCardGuid())
                .isNull(HsaStoreCardRule::getMemberInfoCardGuid)
                .isNull(HsaStoreCardRule::getParentGuid)));
    }

    private void setDTOMoney(MiniProgramCardDTO dto) {
        //卡面值金额
        BigDecimal cardValueMoney = Objects.isNull(dto.getCardValueMoney()) ? BigDecimal.ZERO :
                dto.getCardValueMoney();
        //自主开通直接付款金额
        BigDecimal selfPaymentMoney = Objects.isNull(dto.getSelfPaymentMoney()) ? BigDecimal.ZERO :
                dto.getSelfPaymentMoney();
        //自主开通充值金额
        BigDecimal selfRechargeMoney = Objects.isNull(dto.getSelfRechargeMoney()) ? BigDecimal.ZERO :
                dto.getSelfRechargeMoney();
        if (dto.getElectronicOpenWay() == ElectronicCardOpenWayEnum.SATISFY_CONDITION_AUTO.getCode()) {
            dto.setShouldPayMoney(BigDecimal.ZERO);
            dto.setEconomyMoney(cardValueMoney);
        } else {
            if (dto.getSelfType() == SelfTypeEnum.FREE.getCode()) {
                dto.setShouldPayMoney(BigDecimal.ZERO);
                dto.setEconomyMoney(cardValueMoney);
            } else if (dto.getSelfType() == SelfTypeEnum.DIRECT_PAY.getCode()) {
                dto.setShouldPayMoney(selfPaymentMoney);
                dto.setEconomyMoney(cardValueMoney.subtract(selfPaymentMoney));
            } else {
                dto.setShouldPayMoney(selfRechargeMoney);
                dto.setEconomyMoney(cardValueMoney);
            }
        }
    }

    @Override
    public AppletMemberCardDetail queryMiniProgramCardDetail(String guid,
                                                             String memberGuid,
                                                             String longitude,
                                                             String latitude) {
        // 参数校验
        validateCardDetailParams(guid, memberGuid);

        // 获取会员卡信息
        HsaMemberInfoCard memberCardInfo = getMemberCardInfo(guid);

        // 获取操作主体GUID
        final String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();

        // 构建基础卡详情对象
        AppletMemberCardDetail appletMemberCardDetail = buildBaseCardDetail(memberCardInfo);

        // 设置门店信息
        List<HsaStoreCardRule> storeCardRules = buildStoreInfo(guid, memberCardInfo, longitude, latitude, appletMemberCardDetail);

        // 构建使用须知
        NoticeForUseDTO noticeForUseDTO = buildNoticeForUse(memberCardInfo, storeCardRules);

        // 获取卡配置信息并设置基础信息
        setCardConfigInfo(memberGuid, appletMemberCardDetail, memberCardInfo, noticeForUseDTO, operSubjectGuid);

        // 设置金额相关信息
        setAmountRelatedInfo(guid, memberCardInfo, appletMemberCardDetail);

        // 设置充值提示
        setRechargeTips(memberGuid, memberCardInfo, appletMemberCardDetail);

        // 设置会员卡权益
        setCardEquities(memberGuid, appletMemberCardDetail);

        return appletMemberCardDetail;
    }

    /**
     * 参数校验
     */
    private void validateCardDetailParams(String guid, String memberGuid) {
        if (StringUtils.isEmpty(guid) || StringUtils.isEmpty(memberGuid)) {
            throw new MemberBaseException(CardOperationExceptionEnum.CREATE_PHYSICAL_CARD_SECRET_PARAMS_NULL);
        }
    }

    /**
     * 获取会员卡信息
     */
    private HsaMemberInfoCard getMemberCardInfo(String guid) {
        HsaMemberInfoCard memberCardInfo = memberInfoCardMapper.queryByGuid(guid);
        if (Objects.isNull(memberCardInfo)) {
            throw new MemberBaseException(systemRoleHelper.getReplace(CardOperationExceptionEnum.MEMBER_CARD_NULL,
                    ThreadLocalCache.getOperSubjectGuid()));
        }
        return memberCardInfo;
    }

    /**
     * 构建基础卡详情对象
     */
    private AppletMemberCardDetail buildBaseCardDetail(HsaMemberInfoCard memberCardInfo) {
        AppletMemberCardDetail appletMemberCardDetail = new AppletMemberCardDetail();

        // 设置基础信息
        setAppletMemberCardDetail(appletMemberCardDetail, memberCardInfo);

        // 设置实体卡信息
        setPhysicalCardInfo(memberCardInfo, appletMemberCardDetail);

        // 设置适用门店标识
        appletMemberCardDetail.setApplicableAllStore(memberCardInfo.getApplicableAllStore());

        return appletMemberCardDetail;
    }

    /**
     * 构建门店信息
     */
    private List<HsaStoreCardRule> buildStoreInfo(String guid, HsaMemberInfoCard memberCardInfo,
                                                  String longitude, String latitude,
                                                  AppletMemberCardDetail appletMemberCardDetail) {
        // 获取门店规则
        List<HsaStoreCardRule> hsaStoreCardRules = hsaStoreCardRuleMapper.selectList(
                new LambdaQueryWrapper<HsaStoreCardRule>()
                        .eq(HsaStoreCardRule::getMemberInfoCardGuid, memberCardInfo.getGuid())
                        .isNull(HsaStoreCardRule::getParentGuid));

        // 获取门店基础信息
        List<StoreBaseInfo> storeBaseInfos = getStoreBaseInfos(guid, memberCardInfo, hsaStoreCardRules, longitude, latitude);

        // 设置门店信息到返回对象
        if (CollUtil.isNotEmpty(storeBaseInfos)) {
            List<ResStoreBaseInfo> resStoreBaseInfos = convertToResStoreBaseInfos(storeBaseInfos);
            appletMemberCardDetail.setStoreBaseInfos(resStoreBaseInfos);
        }

        return hsaStoreCardRules;
    }

    /**
     * 获取门店基础信息
     */
    private List<StoreBaseInfo> getStoreBaseInfos(String guid, HsaMemberInfoCard memberCardInfo,
                                                  List<HsaStoreCardRule> hsaStoreCardRules,
                                                  String longitude, String latitude) {
        List<StoreBaseInfo> storeBaseInfos = Lists.newArrayList();
        // 获取所有门店
        storeBaseInfos.addAll(externalSupport.storeServer(SystemEnum.MALL.getCode())
                .storePage(new QueryStoreBasePage()).getData());
        storeBaseInfos.addAll(externalSupport.storeServer(SystemEnum.RETAIL.getCode())
                .storePage(new QueryStoreBasePage()).getData());
        storeBaseInfos.addAll(externalSupport.storeServer(SystemEnum.REPAST.getCode())
                .storePage(new QueryStoreBasePage()).getData());
        if (CollUtil.isNotEmpty(hsaStoreCardRules) &&
                StringUtils.isNotEmpty(longitude) &&
                StringUtils.isNotEmpty(latitude)) {
            MemberCardStoreDataQO dao = new MemberCardStoreDataQO();
            dao.setOwnGuid(guid);
            dao.setCardGuid(memberCardInfo.getCardGuid());
            List<StoreBaseInfo> storeBaseInfoList = hsaStoreCardRuleMapper.findStoreCardRule(dao);
            List<String> storeGuidList = storeBaseInfoList.stream().map(StoreBaseInfo::getStoreId)
                    .collect(Collectors.toList());
            storeBaseInfos = storeBaseInfos.stream().filter(in -> storeGuidList.contains(in.getId()))
                    .collect(Collectors.toList());
        }


        // 经纬度距离计算
        return calculateDistance(storeBaseInfos, longitude, latitude);
    }

    /**
     * 转换门店信息格式
     */
    private List<ResStoreBaseInfo> convertToResStoreBaseInfos(List<StoreBaseInfo> storeBaseInfos) {
        return storeBaseInfos.stream()
                .map(this::convertStoreBaseInfo)
                .collect(Collectors.toList());
    }

    /**
     * 转换单个门店信息
     */
    private ResStoreBaseInfo convertStoreBaseInfo(StoreBaseInfo storeBaseInfo) {
        ResStoreBaseInfo resStoreBaseInfo = new ResStoreBaseInfo();
        BeanUtils.copyProperties(storeBaseInfo, resStoreBaseInfo);
        if (StringUtils.isEmpty(resStoreBaseInfo.getStoreId())) {
            resStoreBaseInfo.setStoreId(storeBaseInfo.getId());
        }
        resStoreBaseInfo.setStoreLogo(storeBaseInfo.getStoreLogo());
        return resStoreBaseInfo;
    }

    /**
     * 构建使用须知
     */
    private NoticeForUseDTO buildNoticeForUse(HsaMemberInfoCard memberCardInfo,
                                              List<HsaStoreCardRule> storeCardRules) {
        NoticeForUseDTO noticeForUseDTO = new NoticeForUseDTO();

        // 设置基础信息
        noticeForUseDTO.setCardEmployExplain(memberCardInfo.getCardEmployExplain());
        noticeForUseDTO.setStoreNum(storeCardRules.size());

        // 设置门店ID列表
        if (CollUtil.isNotEmpty(storeCardRules)) {
            List<String> storeIdList = storeCardRules.stream()
                    .filter(Objects::nonNull)
                    .map(HsaStoreCardRule::getStoreGuid)
                    .collect(Collectors.toList());
            noticeForUseDTO.setStoreIdList(storeIdList);
        }

        // 设置有效期
        String validateDate = memberCardInfo.getCardValidity() == 0 ? "永久有效" :
                Optional.ofNullable(memberCardInfo.getCardValidityDate())
                        .map(LocalDate::toString)
                        .orElse("");
        noticeForUseDTO.setValidDate(validateDate);

        return noticeForUseDTO;
    }

    /**
     * 设置卡配置信息
     */
    private void setCardConfigInfo(String memberGuid, AppletMemberCardDetail appletMemberCardDetail,
                                   HsaMemberInfoCard memberCardInfo, NoticeForUseDTO noticeForUseDTO,
                                   String operSubjectGuid) {
        // 获取卡基础信息和开卡规则
        HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(memberCardInfo.getCardGuid());
        HsaCardOpenRule hsaCardOpenRule = hsaCardOpenRuleMapper.selectOne(
                new LambdaQueryWrapper<HsaCardOpenRule>()
                        .eq(HsaCardOpenRule::getCardGuid, memberCardInfo.getCardGuid()));

        // 设置卡基础信息
        setCardBaseInfo(memberGuid, appletMemberCardDetail, hsaCardOpenRule, hsaCardBaseInfo, noticeForUseDTO, operSubjectGuid);

        // 设置超额信息
        setExcessInfo(memberCardInfo, hsaCardBaseInfo, noticeForUseDTO);
    }

    /**
     * 设置超额信息
     */
    private void setExcessInfo(HsaMemberInfoCard memberCardInfo, HsaCardBaseInfo hsaCardBaseInfo,
                               NoticeForUseDTO noticeForUseDTO) {
        noticeForUseDTO.setIsExcess(hsaCardBaseInfo.getIsExcess());
        noticeForUseDTO.setExcessType(hsaCardBaseInfo.getExcessType());

        int isExcess = Optional.of(hsaCardBaseInfo).map(HsaCardBaseInfo::getIsExcess).orElse(-1);
        int excessType = Optional.of(hsaCardBaseInfo).map(HsaCardBaseInfo::getExcessType).orElse(-1);

        if (isExcess == 1 && excessType == 0) {
            noticeForUseDTO.setExcessMoney(new BigDecimal(memberCardInfo.getExcessTimes()));
        } else if (isExcess == 1 && excessType == 1) {
            noticeForUseDTO.setExcessMoney(memberCardInfo.getExcessAmount());
        }
    }

    /**
     * 设置金额相关信息
     */
    private void setAmountRelatedInfo(String guid, HsaMemberInfoCard memberCardInfo,
                                      AppletMemberCardDetail appletMemberCardDetail) {
        // 即将过期余额
        BigDecimal soonExpiredAmount = getSoonExpiredAmount(guid);
        appletMemberCardDetail.setSoonExpiredAmount(soonExpiredAmount);

        // 冻结金额
        BigDecimal freezeAmount = getFreezeAmount(memberCardInfo);
        appletMemberCardDetail.setFreezeAmount(freezeAmount);

        // 本月支出
        BigDecimal currentMonthConsume = computeCurrentMonthConsume(guid, memberCardInfo.getMemberInfoGuid());
        appletMemberCardDetail.setCurrentMonthConsume(currentMonthConsume);
    }

    /**
     * 获取即将过期金额
     */
    private BigDecimal getSoonExpiredAmount(String guid) {
        List<String> subsidyDetailRecordGuids = hsaMemberFundingDetailMapper.soonExpiredSubsidyAmount(guid);
        return CollectionUtils.isEmpty(subsidyDetailRecordGuids) ? BigDecimal.ZERO :
                hsaSubsidyActivityDetailRecordMapper.findAllSoonOverdueSubsidy(subsidyDetailRecordGuids);
    }

    /**
     * 获取冻结金额
     */
    private BigDecimal getFreezeAmount(HsaMemberInfoCard memberCardInfo) {
        CardFreezeBalanceAmountQO cardQO = new CardFreezeBalanceAmountQO();
        cardQO.setElectronicCardGuid(memberCardInfo.getElectronicCardGuid());
        cardQO.setPhysicalCardGuid(memberCardInfo.getPhysicalCardGuid());
        cardQO.setMemberInfoGuid(memberCardInfo.getMemberInfoGuid());
        cardQO.setIsRefresh(BooleanEnum.FALSE.getCode());

        return hsaCardRechargeGiftDetailService.getMemberCardFreezeAmount(cardQO);
    }

    /**
     * 设置充值提示
     */
    private void setRechargeTips(String memberGuid, HsaMemberInfoCard memberCardInfo,
                                 AppletMemberCardDetail appletMemberCardDetail) {
        String rechargeTips = activityHelper.buildRechargeTipsByCard(memberGuid, memberCardInfo.getCardGuid());
        if (StringUtils.isNotEmpty(rechargeTips)){
            //截取掉  充值有礼，
            String result3 = rechargeTips.replaceFirst("充值有礼，", "");
            log.info("小程序会员卡详情页，充值提示，截取前：{}，截取后：{}", rechargeTips, result3);
            appletMemberCardDetail.setRechargeTips(result3);
        }
    }

    /**
     * 设置会员卡权益
     */
    private void setCardEquities(String memberGuid, AppletMemberCardDetail appletMemberCardDetail) {
        List<GradeEquitiesVO> gradeEquitiesVOList = new ArrayList<>();
        hsaMemberGradeInfoService.addGradeEquities(
                appletMemberCardDetail.getCardGuid(),
                gradeEquitiesVOList,
                memberGuid,
                BusinessTypeEnum.CARD_EQUITIES.getCode());
        appletMemberCardDetail.setGradeEquitiesVOList(gradeEquitiesVOList);
    }

    private void setCardBaseInfo(String memberGuid, AppletMemberCardDetail appletMemberCardDetail, HsaCardOpenRule hsaCardOpenRule, HsaCardBaseInfo hsaCardBaseInfo, NoticeForUseDTO noticeForUseDTO, String operSubjectGuid) {
        appletMemberCardDetail.setIsSupportElectronicCard(hsaCardOpenRule.getIsSupportElectronicCard());
        appletMemberCardDetail.setAppletRecharge(hsaCardBaseInfo.getAppletRecharge());
        appletMemberCardDetail.setIsPreStored(hsaCardBaseInfo.getIsPreStored());
        appletMemberCardDetail.setSendStatus(reQuerySendStatus(hsaCardOpenRule));
        appletMemberCardDetail.setOpenCardPath(JSONArray.parseArray(hsaCardOpenRule.getOpenCardPath(), String.class));
        appletMemberCardDetail.setNoticeForUseDTO(noticeForUseDTO);
        appletMemberCardDetail.setOwnGuidList(memberInfoCardMapper.findAllOwnGuid(memberGuid, operSubjectGuid));
    }

    private void setAppletMemberCardDetail(AppletMemberCardDetail appletMemberCardDetail, HsaMemberInfoCard memberCardInfo) {
        appletMemberCardDetail.setOwnGuid(memberCardInfo.getGuid());
        appletMemberCardDetail.setCardGuid(memberCardInfo.getCardGuid());
        appletMemberCardDetail.setCardName(memberCardInfo.getCardName());
        appletMemberCardDetail.setCardMoney(balanceHandler(memberCardInfo.getCardAmount(),
                memberCardInfo.getGiftAmount(), memberCardInfo.getSubsidyAmount()));
        appletMemberCardDetail.setElectronicCardNum(memberCardInfo.getElectronicCardNum());
        appletMemberCardDetail.setCardImage(memberCardInfo.getCardImage());
        appletMemberCardDetail.setQrCode(getQrCode(memberCardInfo.getElectronicCardNum(), null).getCardQrCode());
        appletMemberCardDetail.setElectronicCardState(memberCardInfo.getElectronicCardState());
        appletMemberCardDetail.setPhysicalCardState(memberCardInfo.getPhysicalCardState());
    }

    private void setPhysicalCardInfo(HsaMemberInfoCard memberCardInfo, AppletMemberCardDetail appletMemberCardDetail) {
        if (!StringUtils.isEmpty(memberCardInfo.getPhysicalCardGuid())) {
            HsaPhysicalCard hsaPhysicalCard = hsaPhysicalCardMapper.queryByGuid(memberCardInfo.getPhysicalCardGuid());
            if (Objects.nonNull(hsaPhysicalCard)) {
                appletMemberCardDetail.setReturnableCashPledge(hsaPhysicalCard.getReturnableCashPledge());
                appletMemberCardDetail.setPhysicalCardGuid(hsaPhysicalCard.getGuid());
                appletMemberCardDetail.setPhysicalCardNum(hsaPhysicalCard.getCardNum());
            }
        }
    }

    private Integer reQuerySendStatus(HsaCardOpenRule hsaCardOpenRule) {
        //卡数量统计
        if (hsaCardOpenRule.getSendCountLimit() != 0 &&  //发卡数量有限制
                hsaCardOpenRule.getSurplusSendOpenCountLimit() <= 0 &&  //并且发卡数量<=0
                SendCardStateEnum.CARD_STATE_START.getCode() == hsaCardOpenRule.getSendStatus()) {
            hsaCardOpenRule.setSendStatus(3);  //如果剩余可发卡数量小于等于0，那么状态为  已停止（3）
        }
        return hsaCardOpenRule.getSendStatus();
    }

    /**
     * 计算本月消费
     *
     * @param guid 会员卡guid
     * @return 查询结果
     */
    private BigDecimal computeCurrentMonthConsume(String guid, String memberGuid) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.set(Calendar.DAY_OF_MONTH, 1);
        String startTime = new SimpleDateFormat(DateUtil.PATTERN_DATE).format(cal.getTime()) + " 00:00:00";
        cal.roll(Calendar.DAY_OF_MONTH, -1);
        String endTime = new SimpleDateFormat(DateUtil.PATTERN_DATE).format(cal.getTime()) + " 23:59:59";
        //先判断卡是否已绑定
        List<FundingDTO> fundingDTOs = hsaMemberFundingDetailMapper.getCurrentMonthConsume(guid, startTime, endTime, memberGuid);
        if (CollectionUtils.isEmpty(fundingDTOs)) {
            return BigDecimal.ZERO;
        }
        BigDecimal money = BigDecimal.ZERO;
        for (FundingDTO fundingDTO : fundingDTOs) {
            money = hsaMemberFundingDetailService.computeExpenditureBalance(fundingDTO, money);
        }
        return money;
    }

    @Override
    public List<AbleECardVO> listAllAbleECard() {
        //通过企业和运营主体查询可用得会员卡
        return memberInfoCardMapper.listAllAbleECard(ThreadLocalCache.getOperSubjectGuid());
    }

    @Override
    public List<AbleECardVO> listAllAbleECardByChannel() {
        //通过企业和运营主体查询可用得会员卡 过滤渠道
        return memberInfoCardMapper.listAllAbleECardByChannel(ThreadLocalCache.getOperSubjectGuid());
    }

    @Override
    public List<AbleEntityCardVO> listAbleEntityCard() {
        //查询可用开通的实体卡集合
        final HeaderUserInfo userInfo = ThreadLocalCache.getHeaderUserInfo();
        List<AbleEntityCardVO> ableEntityCardVOS = memberInfoCardMapper.listAbleEntityCard(
                userInfo.getOperSubjectGuid(), StringBaseHandlerUtil.mysqlJsonValue(userInfo.getSource()));
        log.info("运营主体：{},source:{}", userInfo.getOperSubjectGuid(), userInfo.getSource());
        if (ObjectUtil.isNull(ableEntityCardVOS) || ableEntityCardVOS.isEmpty()) {
            return ableEntityCardVOS;
        }
        List<String> strategyGuidList = ableEntityCardVOS.stream()
                .map(AbleEntityCardVO::getOpenPhysicalCardStrategyGuid)
                .collect(Collectors.toList()); //获取会员卡押金策略Guid集合
        //根据押金策略guid集合查询对应的押金策略信息集合
        List<PhysicalCardStrategyVO> physicalCardStrategyVOS = hsaDepositStrategyMapper
                .listByCardStrategyGuid(strategyGuidList, ThreadLocalCache.getOperSubjectGuid());

        if (ObjectUtil.isNull(physicalCardStrategyVOS) || physicalCardStrategyVOS.isEmpty()) {
            return ableEntityCardVOS;
        }
        //将会员卡押金策略封装到会员卡信息中
        cardStrategyHandler(ableEntityCardVOS, physicalCardStrategyVOS);
        return ableEntityCardVOS;
    }

    /**
     * 将会员卡押金策略封装到会员卡信息中
     *
     * @param ableEntityCardVOS       实体卡集合
     * @param physicalCardStrategyVOS 会员卡押金策略集合
     */
    public void cardStrategyHandler(List<AbleEntityCardVO> ableEntityCardVOS, List<PhysicalCardStrategyVO> physicalCardStrategyVOS) {
        if (CollUtil.isEmpty(physicalCardStrategyVOS) || CollUtil.isEmpty(ableEntityCardVOS)) {
            return;
        }
        final Map<String, PhysicalCardStrategyVO> strategyVOMap = physicalCardStrategyVOS.stream()
                .collect(Collectors.toMap(PhysicalCardStrategyVO::getGuid, Function.identity(), (obj, obj1) -> obj));
        for (AbleEntityCardVO entityCardVO : ableEntityCardVOS) {
            if (Objects.isNull(entityCardVO)) {
                continue;
            }
            // strategyGuid:会员卡的押金策略guid
            String strategyGuid = entityCardVO.getOpenPhysicalCardStrategyGuid();
            final PhysicalCardStrategyVO cardStrategyVo = strategyVOMap.get(strategyGuid);
            entityCardVO.setPhysicalCardStrategyVO(cardStrategyVo);
        }
    }

    @Override
    public void updatePhysicalBind(String ownGuid, String physicalGuid) {
        HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setPhysicalCardGuid(physicalGuid);
        hsaMemberInfoCard.setGuid(ownGuid);
        this.updateByGuid(hsaMemberInfoCard);
    }

    @Override
    public void updateMemberAccount(String ownGuid, String guid) {
        HsaMemberInfoCard hsaMemberInfoCard = new HsaMemberInfoCard();
        hsaMemberInfoCard.setMemberInfoGuid(guid);
        hsaMemberInfoCard.setGuid(ownGuid);
        this.updateByGuid(hsaMemberInfoCard);
    }

    @Override
    public void cancelMemberInfoCard(CancelPhysicalCardQO qo) {
        HsaMemberInfoCard hsaMemberInfoCard = this.queryByGuid(qo.getOwnGuid());
        //判断是否删除
        boolean haveECard = StringUtils.isBlank(hsaMemberInfoCard.getElectronicCardGuid());
        if (ObjectUtil.equal(BooleanEnum.FALSE.getCode(), qo.getCancelBalanceType())) {
            //全部删除
        }
        if (ObjectUtil.equal(BooleanEnum.TRUE.getCode(), qo.getCancelBalanceType())) {
            //全部删除
        }
    }

    @Override
    public Page<QueryMemberInfoCardVO> queryMemberInfoCard(QueryMemberInfoCardQO query) {
        Long startTime = System.currentTimeMillis();
        Page<QueryMemberInfoCardVO> queryCardInfoPageVOPage = new Page<>(query.getCurrentPage(), query.getPageSize());
        query.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        PageMethod.startPage(query.getCurrentPage(), query.getPageSize());
        List<QueryMemberInfoCardVO> queryMemberInfoCardVOS = memberInfoCardMapper.queryMemberInfoCard(query);
        Long endTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>>>>>>>>>>>>第一个逻辑耗时：    " + (endTime - startTime));
        if (CollUtil.isEmpty(queryMemberInfoCardVOS)) {
            return queryCardInfoPageVOPage;
        }
        Map<String, HsaOperationMemberInfo> hsaOperationMemberInfoMap = getStringHsaOperationMemberInfoMap(queryMemberInfoCardVOS);

        Set<String> cardSet = queryMemberInfoCardVOS.stream()
                .map(QueryMemberInfoCardVO::getCardGuid)
                .collect(Collectors.toSet());

        Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap = hsaCardBaseInfoMapper.selectList(new LambdaQueryWrapper<HsaCardBaseInfo>()
                        .in(HsaCardBaseInfo::getGuid, cardSet))
                .stream()
                .collect(Collectors.toMap(HsaCardBaseInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));

        Map<String, HsaCardOpenRule> hsaCardOpenRuleMap = hsaCardOpenRuleMapper.selectList(new LambdaQueryWrapper<HsaCardOpenRule>()
                        .in(HsaCardOpenRule::getCardGuid, cardSet))
                .stream()
                .collect(Collectors.toMap(HsaCardOpenRule::getCardGuid, Function.identity(), (entity1, entity2) -> entity1));
        List<String> electronicCardGuid = queryMemberInfoCardVOS.stream().map(QueryMemberInfoCardVO::getElectronicCardGuid).collect(Collectors.toList());
        Map<String, HsaElectronicCard> hsaElectronicCardMap = new HashMap<>();
        hsaElectronicCardMap = getStringHsaElectronicCardMap(electronicCardGuid, hsaElectronicCardMap);

        List<String> physicalCardGuid = queryMemberInfoCardVOS.stream().map(QueryMemberInfoCardVO::getPhysicalCardGuid).collect(Collectors.toList());
        Map<String, HsaPhysicalCard> hsaPhysicalCardMap = new HashMap<>();
        hsaPhysicalCardMap = getStringHsaPhysicalCardMap(electronicCardGuid, hsaPhysicalCardMap, physicalCardGuid);
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATE);
        Map<String, HsaElectronicCard> finalHsaElectronicCardMap = hsaElectronicCardMap;
        Map<String, HsaPhysicalCard> finalHsaPhysicalCardMap = hsaPhysicalCardMap;
        dealMemberInfoCardVOS(queryMemberInfoCardVOS, df, hsaCardBaseInfoMap, hsaCardOpenRuleMap, finalHsaElectronicCardMap, finalHsaPhysicalCardMap, hsaOperationMemberInfoMap);
        queryCardInfoPageVOPage.setRecords(queryMemberInfoCardVOS);
        PageResult pageResult = PageUtil.getPageResult(new PageInfo<>(queryMemberInfoCardVOS));
        queryCardInfoPageVOPage.setTotal(pageResult.getTotal());
        return queryCardInfoPageVOPage;
    }

    private void dealMemberInfoCardVOS(List<QueryMemberInfoCardVO> queryMemberInfoCardVOS,
                                       DateTimeFormatter df,
                                       Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap,
                                       Map<String, HsaCardOpenRule> hsaCardOpenRuleMap,
                                       Map<String, HsaElectronicCard> finalHsaElectronicCardMap,
                                       Map<String, HsaPhysicalCard> finalHsaPhysicalCardMap,
                                       Map<String, HsaOperationMemberInfo> hsaOperationMemberInfoMap) {
        queryMemberInfoCardVOS.forEach(in -> {
            in.setAmount(in.getCardAmount().add(in.getGiftAmount()).add(in.getSubsidyAmount()));
            //计算有效期
            calculateValid(in, df, hsaCardBaseInfoMap);


            //实体卡是否可退
            checkCardBack(in, hsaCardOpenRuleMap);
            // 退卡规则
            in.setRefundCardRule(CLEAR_ALL);

            setRefundCardRule(in);
            //门店范围
            setStoreList(in);
            //电子卡相关
            setElectronicCard(in, finalHsaElectronicCardMap);

            //实体卡相关
            String name = null;
            String phone = null;
            if (CollUtil.isNotEmpty(finalHsaPhysicalCardMap) && finalHsaPhysicalCardMap.containsKey(in.getPhysicalCardGuid())) {
                HsaPhysicalCard hsaPhysicalCard = finalHsaPhysicalCardMap.get(in.getPhysicalCardGuid());
                in.setPhysicalCardSource(String.valueOf(hsaPhysicalCard.getSource()));
                in.setCashName(hsaPhysicalCard.getCashName());
                in.setCashPledge(hsaPhysicalCard.getCashPledge());
                in.setReturnableCashPledge(hsaPhysicalCard.getReturnableCashPledge());
                name = hsaPhysicalCard.getUserName();
                phone = hsaPhysicalCard.getPhoneNum();
            }

            //会员相关
            setMemberInfo(in, hsaOperationMemberInfoMap, phone, name);

            //是否绑定
            setIsBindingInfo(in);

            //累加冻结
            CardFreezeBalanceAmountQO cardFre = new CardFreezeBalanceAmountQO();
            cardFre.setElectronicCardGuid(in.getElectronicCardGuid());
            cardFre.setPhysicalCardGuid(in.getPhysicalCardGuid());
            cardFre.setMemberInfoGuid(in.getMemberInfoGuid());
            BigDecimal freezeAmount = hsaCardRechargeGiftDetailService.getMemberCardFreezeAmount(cardFre);

            in.setAmount(in.getAmount().add(freezeAmount));

            in.setGiftAmount(in.getGiftAmount().add(freezeAmount));
        });
    }

    private void setStoreList(QueryMemberInfoCardVO in) {
        if (in.getApplicableAllStore() == BooleanEnum.FALSE.getCode()) {
            List<String> ss = new ArrayList<>();
            ss.add(in.getCardGuid());
            List<StoreCardRuleQO> storeCardRuleQOList = getStoreCardRule(ss, in.getMemberCardGuid());
            in.setStoreList(storeCardRuleQOList);
        }
    }

    private static void setElectronicCard(QueryMemberInfoCardVO in, Map<String, HsaElectronicCard> finalHsaElectronicCardMap) {
        if (CollUtil.isNotEmpty(finalHsaElectronicCardMap) && finalHsaElectronicCardMap.containsKey(in.getElectronicCardGuid())) {
            HsaElectronicCard hsaElectronicCard = finalHsaElectronicCardMap.get(in.getElectronicCardGuid());
            in.setElectronicSource(String.valueOf(hsaElectronicCard.getSource()));
            in.setElectronicOpenCardWay(String.valueOf(hsaElectronicCard.getOpenCardWay()));
            in.setElectronicOpenWay(String.valueOf(hsaElectronicCard.getOpenWay()));
        }
    }

    private static void setIsBindingInfo(QueryMemberInfoCardVO in) {
        if (StringUtils.isNotBlank(in.getMemberInfoGuid()) || StringUtils.isNotBlank(in.getMemberPhone())) {
            in.setIsBindingInfo(BooleanEnum.TRUE.getCode());
        } else {
            in.setIsBindingInfo(BooleanEnum.FALSE.getCode());
        }
    }

    private void setRefundCardRule(QueryMemberInfoCardVO in) {
        HsaCardBalanceRule hsaCardBalanceRule = hsaCardBalanceRuleMapper.selectOne(
                new LambdaQueryWrapper<HsaCardBalanceRule>()
                        .eq(HsaCardBalanceRule::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
        if (!ObjectUtils.isEmpty(hsaCardBalanceRule)) {
            in.setRefundCardRule(hsaCardBalanceRule.getRefundCardRule());
        }
        if (StringUtils.isEmpty(in.getElectronicCardNum())) {
            in.setRefundCardRule(CLEAR_ALL);
        }
    }

    private static void setMemberInfo(QueryMemberInfoCardVO in, Map<String, HsaOperationMemberInfo> hsaOperationMemberInfoMap, String phone, String name) {
        if (CollUtil.isNotEmpty(hsaOperationMemberInfoMap) && hsaOperationMemberInfoMap.containsKey(in.getMemberInfoGuid())) {
            HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMap.get(in.getMemberInfoGuid());
            in.setPhoneCountryCode(StringUtils.isNotBlank(hsaOperationMemberInfo.getPhoneCountryCode()) ? hsaOperationMemberInfo.getPhoneCountryCode() : "86");
            in.setMemberPhone(StringUtils.isNotBlank(hsaOperationMemberInfo.getPhoneNum()) ? hsaOperationMemberInfo.getPhoneNum() : phone);
            in.setMemberName(StringUtils.isNotBlank(hsaOperationMemberInfo.getUserName()) ? hsaOperationMemberInfo.getUserName() : name);
        } else {
            //默认加86
            in.setPhoneCountryCode(StringConstant.STR_DEFAULT_PHONE_COUNTRY_CODE);
            in.setMemberPhone(phone);
            in.setMemberName(name);
        }
    }

    private static void checkCardBack(QueryMemberInfoCardVO in, Map<String, HsaCardOpenRule> hsaCardOpenRuleMap) {
        if (CollUtil.isNotEmpty(hsaCardOpenRuleMap) && hsaCardOpenRuleMap.containsKey(in.getCardGuid())) {
            HsaCardOpenRule hsaCardOpenRule = hsaCardOpenRuleMap.get(in.getCardGuid());
            if (ObjectUtil.isNotNull(hsaCardOpenRule.getIsPhysicalCardRetreat())) {
                in.setIsPhysicalCardRetreat(hsaCardOpenRule.getIsPhysicalCardRetreat());
            }
            in.setIsSupportElectronicCard(hsaCardOpenRule.getIsSupportElectronicCard());
            in.setIsSupportPhysicalCard(hsaCardOpenRule.getIsSupportPhysicalCard());
        }
    }

    private Map<String, HsaPhysicalCard> getStringHsaPhysicalCardMap(List<String> electronicCardGuid, Map<String, HsaPhysicalCard> hsaPhysicalCardMap, List<String> physicalCardGuid) {
        if (CollUtil.isNotEmpty(electronicCardGuid)) {
            hsaPhysicalCardMap = hsaPhysicalCardMapper.selectList(new LambdaQueryWrapper<HsaPhysicalCard>()
                            .in(HsaPhysicalCard::getGuid, physicalCardGuid))
                    .stream()
                    .collect(Collectors.toMap(HsaPhysicalCard::getGuid, Function.identity(), (entity1, entity2) -> entity1));
        }
        return hsaPhysicalCardMap;
    }

    private Map<String, HsaElectronicCard> getStringHsaElectronicCardMap(List<String> electronicCardGuid, Map<String, HsaElectronicCard> hsaElectronicCardMap) {
        if (CollUtil.isNotEmpty(electronicCardGuid)) {
            hsaElectronicCardMap = hsaElectronicCardMapper.selectList(new LambdaQueryWrapper<HsaElectronicCard>()
                            .in(HsaElectronicCard::getGuid, electronicCardGuid))
                    .stream()
                    .collect(Collectors.toMap(HsaElectronicCard::getGuid, Function.identity(), (entity1, entity2) -> entity1));
        }
        return hsaElectronicCardMap;
    }

    private Map<String, HsaOperationMemberInfo> getStringHsaOperationMemberInfoMap(List<QueryMemberInfoCardVO> queryMemberInfoCardVOS) {
        Set<String> queryMemberInfoCardVOSet = queryMemberInfoCardVOS.stream()
                .filter(in -> StringUtils.isNotBlank(in.getMemberInfoGuid()))
                .map(QueryMemberInfoCardVO::getMemberInfoGuid)
                .collect(Collectors.toSet());
        Map<String, HsaOperationMemberInfo> hsaOperationMemberInfoMap = new HashMap<>();
        if (CollUtil.isNotEmpty(queryMemberInfoCardVOSet)) {
            hsaOperationMemberInfoMap = hsaOperationMemberInfoMapper.selectList(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                            .in(HsaOperationMemberInfo::getGuid, queryMemberInfoCardVOSet))
                    .stream()
                    .collect(Collectors.toMap(HsaOperationMemberInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));
        }
        return hsaOperationMemberInfoMap;
    }

    private void calculateValid(QueryMemberInfoCardVO in, DateTimeFormatter df, Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap) {
        if (Objects.nonNull(in.getCardValidity())) {
            if (!"0".equals(in.getCardValidity())) {
                if (StringUtils.isNotBlank(in.getElectronicCardGuid()) && StringUtils.isEmpty(in.getPhysicalCardGuid())) {
                    in.setCardValidityTime(df.format(in.getElectronicOpenCardTime()) + "至" + df.format(in.getCardValidityDate()));
                } else if (StringUtils.isNotBlank(in.getPhysicalCardGuid()) && StringUtils.isEmpty(in.getElectronicCardGuid())) {
                    //是否激活
                    cardValidityTime(in, df, hsaCardBaseInfoMap);
                } else {
                    in.setCardValidityTime(df.format(in.getCardCreateDate()) + "至" + df.format(in.getCardValidityDate()));
                }
            } else {
                in.setCardValidityTime("永久有效");
            }
        } else {
            getValidityDate(hsaCardBaseInfoMap, in, df);
        }
    }

    private void cardValidityTime(QueryMemberInfoCardVO in, DateTimeFormatter df, Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap) {
        if (ObjectUtil.isNotNull(in.getPhysicalActivationTime())) {
            in.setCardValidityTime(df.format(in.getPhysicalActivationTime()) + "至" + df.format(in.getCardValidityDate()));
        } else {
            getValidityDate(hsaCardBaseInfoMap, in, df);
        }
    }

    public void getValidityDate(Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap, QueryMemberInfoCardVO in, DateTimeFormatter df) {
        if (CollUtil.isNotEmpty(hsaCardBaseInfoMap) && hsaCardBaseInfoMap.containsKey(in.getCardGuid())) {
            HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMap.get(in.getCardGuid());
            if (hsaCardBaseInfo.getCardValidity() == 1) {
                in.setCardValidityTime("领取后" + hsaCardBaseInfo.getCardValidityTime() + DateUnitEnum.getNameByCode(hsaCardBaseInfo.getValidityUnit()));
            } else if (hsaCardBaseInfo.getCardValidity() == 2) {
                in.setCardValidityTime(df.format(in.getCardCreateDate()) + "至" + df.format(in.getCardValidityDate()));
            }
        }
    }

    @Override
    public void updateMemberRechargeAmount(List<ImportRechargeAmountQO> importMemberInfoQOS) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        List<String> phoneNumList = importMemberInfoQOS.stream().map(ImportRechargeAmountQO::getPhoneNum).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(phoneNumList)) {
            return;
        }

        memberInfoCardMapper.updateCardAmountByCardGuidList(importMemberInfoQOS, headerUserInfo.getOperSubjectGuid());

    }

    /**
     * 批量修改余额
     *
     * @param memberCardBalanceVO memberCardBalanceVO
     * @return Boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateMemberCardBalance(UpdateMemberCardBalanceQO memberCardBalanceVO) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        List<String> memberInfoCardGuidList = memberCardBalanceVO.getMemberCardGuid();
        if (CollUtil.isEmpty(memberInfoCardGuidList)) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_NOT_NULL_CARD_GUID, ThreadLocalCache.getOperSubjectGuid()));
        }
        List<HsaMemberInfoCard> hsaMemberInfoCards = memberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .in(HsaMemberInfoCard::getGuid, memberInfoCardGuidList));
        Set<String> cardSet = hsaMemberInfoCards.stream().map(HsaMemberInfoCard::getCardGuid).collect(Collectors.toSet());
        //会员guid集合
        Set<String> memberGuids = hsaMemberInfoCards.stream().filter(x -> Objects.nonNull(x.getMemberInfoGuid()))
                .map(HsaMemberInfoCard::getMemberInfoGuid).collect(Collectors.toSet());
        Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap = hsaCardBaseInfoMapper.selectList(new LambdaQueryWrapper<HsaCardBaseInfo>()
                        .in(HsaCardBaseInfo::getGuid, cardSet))
                .stream()
                .collect(Collectors.toMap(HsaCardBaseInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));
        //实充最小值
        BigDecimal minCardAmount = hsaMemberInfoCards.stream().map(HsaMemberInfoCard::getCardAmount).min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
        //赠送最小值
        BigDecimal minGiftAmount = hsaMemberInfoCards.stream().map(HsaMemberInfoCard::getGiftAmount).min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
        //补贴最小值
        BigDecimal minSubsidyAmount = hsaMemberInfoCards.stream().map(HsaMemberInfoCard::getSubsidyAmount).min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);

        Map<String, HsaMemberInfoCard> hsaMemberInfoCardMap = hsaMemberInfoCards.stream().collect(Collectors.toMap(HsaMemberInfoCard::getGuid, Function.identity(), (entity1, entity2) -> entity1));

        List<HsaMemberInfoCard> hsaMemberInfoCardList = Lists.newArrayList();
        List<HsaMemberFundingDetail> hsaMemberFundingDetailList = Lists.newArrayList();
        //批量处理
        MemberCardBalanceQO memberCardBalanceQO = new MemberCardBalanceQO();

        memberCardBalanceQO.setHeaderUserInfo(headerUserInfo);
        memberCardBalanceQO.setMinCardAmount(minCardAmount);

        memberCardBalanceQO.setMinGiftAmount(minGiftAmount);

        memberCardBalanceQO.setMinSubsidyAmount(minSubsidyAmount);
        for (String memberCardGuid : memberInfoCardGuidList) {
            forMemberInfoCardGuidList(memberCardBalanceQO, memberCardBalanceVO, hsaCardBaseInfoMap, hsaMemberInfoCardMap, hsaMemberInfoCardList, hsaMemberFundingDetailList, memberCardGuid);
        }
        if (CollUtil.isNotEmpty(hsaMemberInfoCardList)) {
            memberInfoCardMapper.batchUpdateBalance(hsaMemberInfoCardList);
            hsaMemberFundingDetailService.saveBatch(hsaMemberFundingDetailList);

            updateMemberCardBalanceAfterHandler(memberCardBalanceVO, hsaMemberInfoCardList, hsaMemberFundingDetailList);
        }
        hsaLabelSettingService.refreshLabel(Lists.newArrayList(memberGuids), null,
                BooleanEnum.FALSE.getCode(),
                null,
                LabelTriggerTypeEnum.RECHARGE_INFO.getCode());
        return true;
    }

    private void forMemberInfoCardGuidList(
            MemberCardBalanceQO memberCardBalanceQO,
            UpdateMemberCardBalanceQO memberCardBalanceVO,
            Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap,
            Map<String, HsaMemberInfoCard> hsaMemberInfoCardMap,
            List<HsaMemberInfoCard> hsaMemberInfoCardList,
            List<HsaMemberFundingDetail> hsaMemberFundingDetailList,
            String memberCardGuid) {
        StringBuilder strThrow = new StringBuilder();
        HeaderUserInfo headerUserInfo = memberCardBalanceQO.getHeaderUserInfo();

        BigDecimal minCardAmount = memberCardBalanceQO.getMinCardAmount();

        BigDecimal minGiftAmount = memberCardBalanceQO.getMinGiftAmount();

        BigDecimal minSubsidyAmount = memberCardBalanceQO.getMinSubsidyAmount();
        if (hsaMemberInfoCardMap.containsKey(memberCardGuid)) {
            HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMap.get(memberCardGuid);
            HsaMemberFundingDetail memberFundingDetail = MemberFundingDetailAssembler.getHsaMemberFundingDetail(headerUserInfo, hsaMemberInfoCard);
            memberFundingDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberFundingDetail.class.getSimpleName()));
            memberFundingDetail.setRemark(memberCardBalanceVO.getRemark());
            if (Objects.nonNull(memberCardBalanceVO.getAmountSourceType())) {
                memberFundingDetail.setAmountSourceType(memberCardBalanceVO.getAmountSourceType());
            }
            if (memberCardBalanceVO.getAmountFundingType() == 0) {
                setSubsidyAmount(memberCardBalanceVO, hsaCardBaseInfoMap, hsaMemberInfoCard, memberFundingDetail);
            } else {
                setStrThrow(memberCardBalanceVO, minCardAmount, minGiftAmount, minSubsidyAmount, strThrow, hsaMemberInfoCard, memberFundingDetail);
            }
            if (StringUtils.isNotBlank(strThrow.toString())) {
                throw new MemberBaseException(com.holderzone.member.common.util.verify.ObjectUtil.strValue(strThrow.toString()));
            }
            memberFundingDetail.setCardSubsidyResidualBalance(hsaMemberInfoCard.getSubsidyAmount())
                    .setCardRechargeResidualBalance(hsaMemberInfoCard.getCardAmount())
                    .setCardGiftResidualBalance(hsaMemberInfoCard.getGiftAmount());


            //获取卡余额
            CardFreezeBalanceAmountQO cardFreeze = new CardFreezeBalanceAmountQO();
            cardFreeze.setPhysicalCardGuid(hsaMemberInfoCard.getPhysicalCardGuid());
            cardFreeze.setElectronicCardGuid(hsaMemberInfoCard.getElectronicCardGuid());
            cardFreeze.setMemberInfoGuid(hsaMemberInfoCard.getMemberInfoGuid());
            cardFreeze.setIsRefresh(BooleanEnum.FALSE.getCode());
            memberFundingDetail.setCardGiftResidualBalance(hsaMemberInfoCard.getGiftAmount()
                    .add(hsaCardRechargeGiftDetailService.getMemberCardFreezeAmount(cardFreeze)));
            hsaMemberInfoCardList.add(hsaMemberInfoCard);
            hsaMemberFundingDetailList.add(memberFundingDetail);
        }
    }

    private static void setSubsidyAmount(UpdateMemberCardBalanceQO memberCardBalanceVO, Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap, HsaMemberInfoCard hsaMemberInfoCard, HsaMemberFundingDetail memberFundingDetail) {
        if (ObjectUtil.isNotNull(memberCardBalanceVO.getGiftAmount()) && BigDecimalUtil.greaterThanZero(memberCardBalanceVO.getGiftAmount())) {
            memberFundingDetail.setAmountGiftFundingType(NumberConstant.NUMBER_0);
            memberFundingDetail.setGiftAmount(memberCardBalanceVO.getGiftAmount());
            hsaMemberInfoCard.setGiftAmount(hsaMemberInfoCard.getGiftAmount().add(memberCardBalanceVO.getGiftAmount()));
        }
        setCardAmount(memberCardBalanceVO, hsaCardBaseInfoMap, hsaMemberInfoCard, memberFundingDetail);
        if (ObjectUtil.isNotNull(memberCardBalanceVO.getSubsidyAmount()) && BigDecimalUtil.greaterThanZero(memberCardBalanceVO.getSubsidyAmount())) {
            memberFundingDetail.setAmountSubsidyFundingType(NumberConstant.NUMBER_0);
            memberFundingDetail.setSubsidyAmount(memberCardBalanceVO.getSubsidyAmount());
            hsaMemberInfoCard.setSubsidyAmount(hsaMemberInfoCard.getSubsidyAmount().add(memberCardBalanceVO.getSubsidyAmount()));
            hsaMemberInfoCard.setRetreatSubsidyAmount(hsaMemberInfoCard.getRetreatSubsidyAmount().add(memberCardBalanceVO.getSubsidyAmount()));
        }
    }

    private void setStrThrow(UpdateMemberCardBalanceQO memberCardBalanceVO, BigDecimal minCardAmount, BigDecimal minGiftAmount, BigDecimal minSubsidyAmount, StringBuilder strThrow, HsaMemberInfoCard hsaMemberInfoCard, HsaMemberFundingDetail memberFundingDetail) {
        if (ObjectUtil.isNotNull(memberCardBalanceVO.getRechargeAmount())) {
            if (hsaMemberInfoCard.getCardAmount().subtract(memberCardBalanceVO.getRechargeAmount()).compareTo(BigDecimal.ZERO) >= 0) {
                memberFundingDetail.setAmountRechargeFundingType(NumberConstant.NUMBER_1);
                memberFundingDetail.setRechargeAmount(memberCardBalanceVO.getRechargeAmount());
                hsaMemberInfoCard.setCardAmount(hsaMemberInfoCard.getCardAmount().subtract(memberCardBalanceVO.getRechargeAmount()));
                memberFundingDetail.setCardRechargeResidualBalance(hsaMemberInfoCard.getCardAmount());
            } else {
                strThrow.append(StringConstant.STR_RECHARGE_BALANCE).append(minCardAmount.toString()).append("元/");
            }
        }
        if (ObjectUtil.isNotNull(memberCardBalanceVO.getGiftAmount())) {
            if (hsaMemberInfoCard.getGiftAmount().subtract(memberCardBalanceVO.getGiftAmount()).compareTo(BigDecimal.ZERO) >= 0) {
                memberFundingDetail.setAmountGiftFundingType(NumberConstant.NUMBER_1);
                memberFundingDetail.setGiftAmount(memberCardBalanceVO.getGiftAmount());
                hsaMemberInfoCard.setGiftAmount(hsaMemberInfoCard.getGiftAmount().subtract(memberCardBalanceVO.getGiftAmount()));
            } else {
                strThrow.append(StringConstant.STR_GIFT_BALANCE).append(minGiftAmount.toString()).append("元/");
            }
        }
        if (ObjectUtil.isNotNull(memberCardBalanceVO.getSubsidyAmount())) {
            if (hsaMemberInfoCard.getSubsidyAmount().subtract(memberCardBalanceVO.getSubsidyAmount()).compareTo(BigDecimal.ZERO) >= 0) {
                memberFundingDetail.setAmountSubsidyFundingType(NumberConstant.NUMBER_1);
                memberFundingDetail.setSubsidyAmount(memberCardBalanceVO.getSubsidyAmount());
                //补贴扣减逻辑
                hsaCardInfoService.subsidyMoneyReduceProcessor(hsaMemberInfoCard, memberCardBalanceVO.getSubsidyAmount());
            } else {
                strThrow.append(StringConstant.STR_SUBSIDY_BALANCE).append(minSubsidyAmount.toString()).append("元");
            }
        }
    }

    private static void setCardAmount(UpdateMemberCardBalanceQO memberCardBalanceVO, Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap, HsaMemberInfoCard hsaMemberInfoCard, HsaMemberFundingDetail memberFundingDetail) {
        if (ObjectUtil.isNotNull(memberCardBalanceVO.getRechargeAmount()) && BigDecimalUtil.greaterThanZero(memberCardBalanceVO.getRechargeAmount())) {
            memberFundingDetail.setAmountRechargeFundingType(NumberConstant.NUMBER_0);
            memberFundingDetail.setRechargeAmount(memberCardBalanceVO.getRechargeAmount());
            //恢复超额及次数
            if (isaBoolean(hsaCardBaseInfoMap, hsaMemberInfoCard)) {
                HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMap.get(hsaMemberInfoCard.getCardGuid());
                // 超额次数冲正 则更新   金额 反之
                hsaMemberInfoCard.setCardAmount(hsaMemberInfoCard.getCardAmount().add(memberCardBalanceVO.getRechargeAmount()));
                if (hsaCardBaseInfo.getIsExcess() == BooleanEnum.TRUE.getCode()) {
                    if (hsaCardBaseInfo.getExcessType() == 0 && hsaMemberInfoCard.getCardAmount().compareTo(BigDecimal.ZERO) >= 0) {
                        hsaMemberInfoCard.setExcessTimes(hsaCardBaseInfo.getExcessMoney().intValue());
                    } else {
                        checkExcessAmount(memberCardBalanceVO, hsaMemberInfoCard, hsaCardBaseInfo);
                    }
                }
            } else {
                hsaMemberInfoCard.setCardAmount(hsaMemberInfoCard.getCardAmount().add(memberCardBalanceVO.getRechargeAmount()));
            }
        }
    }

    private static boolean isaBoolean(Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap, HsaMemberInfoCard hsaMemberInfoCard) {
        return hsaCardBaseInfoMap.containsKey(hsaMemberInfoCard.getCardGuid()) && hsaMemberInfoCard.getCardAmount().compareTo(BigDecimal.ZERO) < 0;
    }

    private static void checkExcessAmount(UpdateMemberCardBalanceQO memberCardBalanceVO, HsaMemberInfoCard hsaMemberInfoCard, HsaCardBaseInfo hsaCardBaseInfo) {
        if (hsaMemberInfoCard.getCardAmount().compareTo(BigDecimal.ZERO) >= 0) {
            hsaMemberInfoCard.setExcessAmount(hsaCardBaseInfo.getExcessMoney());
        } else {
            if (hsaMemberInfoCard.getCardAmount().abs().compareTo(hsaCardBaseInfo.getExcessMoney()) < 0) {
                if (hsaMemberInfoCard.getExcessAmount().compareTo(BigDecimal.ZERO) == 0) {
                    hsaMemberInfoCard.setExcessAmount(hsaCardBaseInfo.getExcessMoney().subtract(hsaMemberInfoCard.getCardAmount().abs()));
                } else {
                    hsaMemberInfoCard.setExcessAmount(hsaMemberInfoCard.getExcessAmount().add(memberCardBalanceVO.getRechargeAmount()));
                }
            }
        }
    }

    /**
     * 批量条件导出数据
     *
     * @param memberInfoCardExcelQO memberInfoCardExcelQO
     * @return String
     */
    @Override
    public String exportMemberInfoCard(MemberInfoCardExcelQO memberInfoCardExcelQO) {
        List<QueryMemberInfoCardVO> queryMemberInfoCardVOList;
        QueryMemberInfoCardQO query = new QueryMemberInfoCardQO();
        query.setCurrentPage(NumberConstant.NUMBER_0);
        query.setPageSize(NumberConstant.NUMBER_9999999);
        query.setMemberInfoCardGuid(memberInfoCardExcelQO.getMemberInfoCardGuid());
        if (!CollUtil.isNotEmpty(memberInfoCardExcelQO.getMemberInfoCardGuid())) {
            BeanUtils.copyProperties(memberInfoCardExcelQO, query);
        }
        query.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        int count = memberInfoCardMapper.queryMemberInfoCardCount(query);
        if (count > NumberConstant.NUMBER_20000) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_MAX_EXPORT_EXCEL);
        }
        queryMemberInfoCardVOList = queryMemberInfoCard(query).getRecords();
        if (CollectionUtil.isEmpty(queryMemberInfoCardVOList)) {
            return null;
        }
        List<MemberInfoCardExcelVO> memberInfoCardExcelVOS = toMemberInfoCardExcelVO(queryMemberInfoCardVOList);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATE);
        String formatDateTime = LocalDateTime.now().format(formatter);
        ExcelResult excelResult = com.aimilin.utils.BeanUtils.toResult(memberInfoCardExcelVOS);
        byte[] write = ExcelWriteUtils.write(excelResult, ExcelType.XLSX);
        try {
            FileDto fileDto = new FileDto();
            fileDto.setFileContent(com.holderzone.framework.security.SecurityManager.entryptBase64(write));
            fileDto.setFileName("已领会员卡" + formatDateTime + "." + ExcelType.XLSX);
            return fileOssService.upload(fileDto).replace("http", "https");
        } catch (Exception e) {
            log.error("上传文件失败");
            e.printStackTrace();
        }
        return null;
    }

    private List<MemberInfoCardExcelVO> toMemberInfoCardExcelVO(List<QueryMemberInfoCardVO> queryMemberInfoCardVOList) {
        boolean phonePermission;  //是否有查看完整手机号权限
        try {
            phonePermission = operatingSubjectService.phonePermission(SystemPermissionEnum.MEMBER_PERMISSION.getDes());
        } catch (Exception e) {
            phonePermission = false;
        }
        List<MemberInfoCardExcelVO> memberInfoCardExcelVOS = Lists.newArrayList();
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME);
        for (QueryMemberInfoCardVO queryMemberInfoCardVO : queryMemberInfoCardVOList) {
            MemberInfoCardExcelVO excelVO = MemberCardAssembler.getMemberInfoCardExcelVO(queryMemberInfoCardVO, memberInfoCardExcelVOS, df, phonePermission);
            memberInfoCardExcelVOS.add(excelVO);
        }
        return memberInfoCardExcelVOS;
    }


    @Override
    public List<MemberInfoCardExcelVO> queryExportMemberInfoCard(QueryMemberInfoCardQO query) {
        query.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        query.setCurrentPage(NumberConstant.NUMBER_0);
        query.setPageSize(NumberConstant.NUMBER_9999999);
        int count = memberInfoCardMapper.queryMemberInfoCardCount(query);
        if (count > NumberConstant.NUMBER_20000) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_MAX_EXPORT_EXCEL);
        }
        List<QueryMemberInfoCardVO> queryMemberInfoCardVOList = queryMemberInfoCard(query).getRecords();
        if (CollectionUtil.isEmpty(queryMemberInfoCardVOList)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_DATA);
        }
        return toMemberInfoCardExcelVO(queryMemberInfoCardVOList);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean retreatPhysicalCard(RetreatPhysicalCardQO retreatPhysicalCardQO) {
        HsaMemberInfoCard hsaMemberInfoCard = queryByGuid(retreatPhysicalCardQO.getMemberCardGuid());
        HsaPhysicalCard hsaPhysicalCard = hsaPhysicalCardMapper.selectOne(new LambdaQueryWrapper<HsaPhysicalCard>()
                .eq(HsaPhysicalCard::getGuid, hsaMemberInfoCard.getPhysicalCardGuid()));
        if (ObjectUtil.isNull(hsaPhysicalCard)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_CARD_NOT_EXIST);
        }
        hsaPhysicalCard.setMemberBindingState(BooleanEnum.FALSE.getCode());
        hsaPhysicalCard.setCardState(PhysicalCardStateEnum.NOT_ACTIVATE.getCode());
        hsaPhysicalCard.setUserName(null);
        hsaPhysicalCard.setActivationTime(null);
        hsaPhysicalCard.setPhoneNum(null);
        hsaPhysicalCard.setBindUserTime(null);
        if (StringUtils.isEmpty(hsaMemberInfoCard.getElectronicCardGuid())) {
            hsaMemberInfoCard.setMemberPhoneNum(null);
            hsaMemberInfoCard.setPhysicalCardState(PhysicalCardStateEnum.NOT_ACTIVATE.getCode());
            hsaMemberInfoCard.setPhysicalCardActivationTime(null);
            hsaMemberInfoCard.setSubsidyAmount(BigDecimal.ZERO);
            hsaMemberInfoCard.setGiftAmount(BigDecimal.ZERO);
            hsaMemberInfoCard.setCardAmount(BigDecimal.ZERO);
            hsaMemberInfoCard.setMemberInfoGuid(null);
            //清空冻结金额
            hsaCardRechargeGiftDetailService.clearMemberCardFreezeAmount(hsaMemberInfoCard.getGuid());
        } else {
            //拆分会员持卡记录
            splitMemberInfoCard(retreatPhysicalCardQO, hsaMemberInfoCard, hsaPhysicalCard);
        }
        //退款明细，余额清零
        if (retreatPhysicalCardQO.getIsClearBalance() == BooleanEnum.TRUE.getCode()) {
            hsaMemberFundingDetailService.returnCardAddFundingDetail(hsaMemberInfoCard, retreatPhysicalCardQO, hsaPhysicalCard.getCardNum());
        }
        memberBaseThreadExecutor.execute(() -> {
            hsaPhysicalCardMapper.updateByGuid(hsaPhysicalCard);
            updateByGuid(hsaMemberInfoCard);
        });
        return true;
    }

    private void splitMemberInfoCard(RetreatPhysicalCardQO retreatPhysicalCardQO, HsaMemberInfoCard hsaMemberInfoCard, HsaPhysicalCard hsaPhysicalCard) {
        if (retreatPhysicalCardQO.getIsClearBalance() == BooleanEnum.TRUE.getCode()) {
            hsaMemberInfoCard.setSubsidyAmount(BigDecimal.ZERO)
                    .setCardAmount(BigDecimal.ZERO)
                    .setGiftAmount(BigDecimal.ZERO);
            //设置剩余可退补贴余额
            hsaMemberInfoCard.setRetreatSubsidyAmount(BigDecimal.ZERO);

            //清空冻结金额
            hsaCardRechargeGiftDetailService.clearMemberCardFreezeAmount(hsaMemberInfoCard.getGuid());
        }
        hsaMemberInfoCard.setIsPhysicalCardRetreat(null);
        hsaMemberInfoCard.setPhysicalCardActivationTime(null);
        hsaMemberInfoCard.setPhysicalCardGuid(null);
        hsaMemberInfoCard.setPhysicalCardNum(null);
        hsaMemberInfoCard.setPhysicalCardState(null);
        if (StringUtils.isEmpty(hsaMemberInfoCard.getElectronicCardGuid())) {
            hsaMemberInfoCard.setMemberInfoGuid(null);
        }
        //分离
        if (StringUtils.isNotBlank(hsaMemberInfoCard.getElectronicCardGuid())) {
            HsaMemberInfoCard memberInfoCard = MemberCardAssembler.getHsaMemberInfoCard(hsaMemberInfoCard, hsaPhysicalCard);
            memberInfoCard.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberInfoCard.class.getSimpleName()));
            memberInfoCardMapper.insert(memberInfoCard);
            //门店范围
            hsaStoreCardRuleService.saveStoreCardRule(memberInfoCard.getCardGuid(), memberInfoCard.getGuid(), hsaMemberInfoCard.getGuid());
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean bindingPhysicalCard(BindingPhysicalCardQO bindingPhysicalCardQO) {
        LocalDateTime now = LocalDateTime.now();
        //会员卡关联信息
        HsaMemberInfoCard hsaMemberInfoCard = queryByGuid(bindingPhysicalCardQO.getMemberCardGuid());
        HsaPhysicalCard hsaPhysicalCard;
        //小程序进来 ThreadLocalCache拿不到运营主体
        String operSubjectGuid = !StringUtils.isEmpty(bindingPhysicalCardQO.getOperSubjectGuid())
                ? bindingPhysicalCardQO.getOperSubjectGuid() : ThreadLocalCache.getOperSubjectGuid();
        putHeaderUserInfo(hsaMemberInfoCard);
        //实体开卡信息
        HsaPhysicalCard card = hsaPhysicalCardMapper.selectOne(new LambdaQueryWrapper<HsaPhysicalCard>()
                .eq(HsaPhysicalCard::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaPhysicalCard::getCardNum, bindingPhysicalCardQO.getCardNum()));
        if (ObjectUtil.isNull(card)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_CARD_NUM);
        }
        //实体开卡信息
        if (ObjectUtil.notEqual(card.getCardBindingNum(), bindingPhysicalCardQO.getCardBindingNum())) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_FIND_CARD);
        }
        //是否已绑定
        hsaPhysicalCard = hsaPhysicalCardMapper.selectOne(new LambdaQueryWrapper<HsaPhysicalCard>()
                .eq(HsaPhysicalCard::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaPhysicalCard::getCardNum, card.getCardNum()));
        //会员信息
        HsaOperationMemberInfo memberInfo = hsaOperationMemberInfoMapper.queryByGuid(hsaMemberInfoCard.getMemberInfoGuid());
        //实体卡开卡规则信息
        HsaCardOpenRule hsaCardOpenRule = hsaCardOpenRuleMapper.selectOne(new LambdaQueryWrapper<HsaCardOpenRule>()
                .eq(HsaCardOpenRule::getCardGuid, card.getCardGuid()));
        HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(hsaMemberInfoCard.getCardGuid());

        //校验卡是否匹配、绑定
        checkBusinessService.checkBindingAndMatching(hsaMemberInfoCard, card, hsaPhysicalCard);

        //再次校验是否有此会员卡
        HsaPhysicalCard physicalCard = hsaPhysicalCardMapper.selectOne(new LambdaQueryWrapper<HsaPhysicalCard>()
                .eq(HsaPhysicalCard::getCardNum, bindingPhysicalCardQO.getCardNum())
                .eq(HsaPhysicalCard::getCardBindingNum, bindingPhysicalCardQO.getCardBindingNum())
                .eq(HsaPhysicalCard::getMemberBindingState, BooleanEnum.FALSE.getCode()));
        HsaMemberInfoCard memberPhysicalCard = null;
        if (ObjectUtil.isNotNull(physicalCard)) {
            memberPhysicalCard = memberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                    .eq(HsaMemberInfoCard::getPhysicalCardGuid, physicalCard.getGuid()));
            hsaPhysicalCard = physicalCard;
        } else {
            hsaPhysicalCard = new HsaPhysicalCard();
            hsaPhysicalCard.setGuid(guidGeneratorUtil.getStringGuid(HsaPhysicalCard.class.getSimpleName()));
        }
        MemberCardAssembler.getPhysicalCard(bindingPhysicalCardQO, hsaPhysicalCard, card, memberInfo, now);
        hsaMemberInfoCard.setPhysicalCardGuid(hsaPhysicalCard.getGuid())
                .setIsPhysicalCardRetreat(hsaCardOpenRule.getIsPhysicalCardRetreat())
                .setIsSupportPhysicalCard(BooleanEnum.TRUE.getCode())
                .setGmtCreate(now)
                .setGmtModified(now)
                .setPhysicalCardNum(hsaPhysicalCard.getCardNum())
                .setPhysicalCardState(hsaPhysicalCard.getCardState())
                .setMemberPhoneNum(memberInfo.getPhoneNum());
        //合并操作
        if (Objects.nonNull(memberPhysicalCard)) {
            mergeMemberCard(memberPhysicalCard, hsaMemberInfoCard, hsaCardBaseInfo);
        }
        //保存实体卡信息
        if (ObjectUtil.isNotNull(hsaPhysicalCardService.queryByGuid(hsaPhysicalCard.getGuid()))) {
            hsaPhysicalCardService.updateByGuid(hsaPhysicalCard);
        } else {
            hsaPhysicalCardService.save(hsaPhysicalCard);
        }
        //更新会员卡关联信息
        return updateByGuid(hsaMemberInfoCard);
    }

    private void putHeaderUserInfo(HsaMemberInfoCard hsaMemberInfoCard) {
        if (StringUtils.isEmpty(ThreadLocalCache.getOperSubjectGuid())) {
            HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(hsaMemberInfoCard.getMemberInfoGuid());
            if (Objects.nonNull(hsaOperationMemberInfo)) {
                HeaderUserInfo headerUserInfo = new HeaderUserInfo();
                headerUserInfo.setUserName(hsaOperationMemberInfo.getUserName());
                headerUserInfo.setTel(hsaOperationMemberInfo.getPhoneNum());
                headerUserInfo.setEnterpriseGuid(hsaOperationMemberInfo.getEnterpriseGuid());
                headerUserInfo.setOperSubjectGuid(hsaOperationMemberInfo.getOperSubjectGuid());
                ThreadLocalCache.put(JSON.toJSONString(headerUserInfo));
            }
        }
    }

    private void mergeMemberCard(HsaMemberInfoCard memberPhysicalCard,
                                 HsaMemberInfoCard hsaMemberInfoCard,
                                 HsaCardBaseInfo hsaCardBaseInfo) {
        if (memberPhysicalCard.getApplicableAllStore() == 1 || hsaMemberInfoCard.getApplicableAllStore() == 1) {
            hsaMemberInfoCard.setApplicableAllStore(1);
        } else {
            hsaStoreCardRuleService.mergeStoreProcessor(hsaMemberInfoCard, memberPhysicalCard);
        }

        hsaMemberInfoCard
                .setApplicableAllStore(memberPhysicalCard.getApplicableAllStore() == 1 ? 1 : hsaMemberInfoCard.getApplicableAllStore())
                .setGiftAmount(hsaMemberInfoCard.getGiftAmount().add(memberPhysicalCard.getGiftAmount()))
                .setCardAmount(hsaMemberInfoCard.getCardAmount().add(memberPhysicalCard.getCardAmount()))
                .setSubsidyAmount(hsaMemberInfoCard.getSubsidyAmount().add(memberPhysicalCard.getSubsidyAmount()))
                .setCardEmployExplain(hsaCardBaseInfo.getCardEmployExplain())
                .setRetreatSubsidyAmount(hsaMemberInfoCard.getRetreatSubsidyAmount().add(memberPhysicalCard.getRetreatSubsidyAmount()));
        //超额合并处理
        mergeExcess(hsaMemberInfoCard, hsaCardBaseInfo, memberPhysicalCard);
        if (hsaMemberInfoCard.getCardValidity() == 1) {
            hsaMemberInfoCard.setCardValidity(1);
            hsaMemberInfoCard.setCardValidityDate(hsaMemberInfoCard.getCardValidityDate().isAfter(memberPhysicalCard.getCardValidityDate()) ? hsaMemberInfoCard.getCardValidityDate() : memberPhysicalCard.getCardValidityDate());
        } else {
            hsaMemberInfoCard.setCardValidity(0);
        }
        hsaMemberInfoCard.setGmtCreate(hsaMemberInfoCard.getGmtCreate().isBefore(memberPhysicalCard.getGmtCreate()) ? hsaMemberInfoCard.getGmtCreate() : memberPhysicalCard.getGmtCreate());
        memberInfoCardMapper.delete(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getGuid, memberPhysicalCard.getGuid()));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public PhysicalCardResultVO bindingPhysicalCardByCardNum(BindingPhysicalCardQO bindingPhysicalCardQO) {
        LocalDateTime now = LocalDateTime.now();
        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        // 通过卡号查询实体卡信息
        HsaPhysicalCard card = hsaPhysicalCardMapper.selectOne(new LambdaQueryWrapper<HsaPhysicalCard>()
                .eq(HsaPhysicalCard::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaPhysicalCard::getCardNum, bindingPhysicalCardQO.getCardNum()));
        // 会员信息
        HsaOperationMemberInfo memberInfo = null;
        if (StringUtils.isNotEmpty(bindingPhysicalCardQO.getMemberInfoGuid())) {
            memberInfo = hsaOperationMemberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                    .eq(HsaOperationMemberInfo::getGuid, bindingPhysicalCardQO.getMemberInfoGuid()));
        } else {
            memberInfo = hsaOperationMemberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                    .eq(HsaOperationMemberInfo::getOperSubjectGuid, operSubjectGuid)
                    .eq(HsaOperationMemberInfo::getPhoneNum, ThreadLocalCache.getHeaderUserInfo().getTel()));
            bindingPhysicalCardQO.setMemberInfoGuid(memberInfo.getGuid());
        }
        checkBusinessService.checkPhysicalCard(card, bindingPhysicalCardQO);
        HsaCardBaseInfo cardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(card.getCardGuid());
        if (CardStatusEnum.FORBIDDEN.getCode() == cardBaseInfo.getCardStatus()) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_DISABLE_CARD);
        }
        HsaMemberInfoCard memberInfoCard = baseMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaMemberInfoCard::getCardGuid, card.getCardGuid())
                .eq(HsaMemberInfoCard::getPhysicalCardNum, card.getCardNum()));

        HsaMemberInfoCard memberInfoCardByMember = baseMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaMemberInfoCard::getCardGuid, card.getCardGuid())
                .eq(HsaMemberInfoCard::getMemberInfoGuid, memberInfo.getGuid()));
        // 校验卡是否匹配
        boolean isRepeat = Objects.nonNull(memberInfoCardByMember) && StringUtils.isNotEmpty(memberInfoCardByMember.getPhysicalCardNum());
        if (isRepeat) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_CARD_REPEAT);
        }
        // 开卡规则
        HsaCardOpenRule hsaCardOpenRule = hsaCardOpenRuleMapper.selectOne(new LambdaQueryWrapper<HsaCardOpenRule>()
                .eq(HsaCardOpenRule::getCardGuid, card.getCardGuid()));

        card.setMemberBindingState(BooleanEnum.TRUE.getCode());
        card.setMemberInfoGuid(memberInfo.getGuid());
        card.setPhoneNum(memberInfo.getPhoneNum());
        card.setUserName(memberInfo.getUserName());
        card.setGmtCreate(now);
        card.setGmtModified(now);
        card.setBindUserTime(now);
        //合并操作
        if (ObjectUtil.isNotNull(memberInfoCardByMember)) {
            mergeMemberInfoCard(card, memberInfoCardByMember, memberInfoCard, cardBaseInfo);
        } else {
            //开通电子卡
            if (ObjectUtil.isNotNull(card.getIssueElectronicCard()) && card.getIssueElectronicCard() == BooleanEnum.TRUE.getCode()) {
                openElectronic(memberInfoCard.getGuid(), memberInfo.getGuid(), BooleanEnum.FALSE.getCode());
                // 查询卡信息
                memberInfoCard = queryByGuid(memberInfoCard.getGuid());
            }
        }
        memberInfoCard.setPhysicalCardGuid(card.getGuid())
                .setIsPhysicalCardRetreat(hsaCardOpenRule.getIsPhysicalCardRetreat())
                .setIsSupportPhysicalCard(BooleanEnum.TRUE.getCode())
                .setGmtModified(now)
                .setPhysicalCardNum(card.getCardNum())
                .setPhysicalCardState(card.getCardState())
                .setMemberInfoGuid(memberInfo.getGuid())
                .setMemberPhoneNum(memberInfo.getPhoneNum());
        // 保存实体卡信息
        hsaPhysicalCardService.updateByGuid(card);
        // 更新会员卡关联信息
        updateByGuid(memberInfoCard);
        // 返回卡信息
        PhysicalCardResultVO resultVO = new PhysicalCardResultVO();
        resultVO.setMemberInfoCardGuid(memberInfoCard.getGuid());
        resultVO.setCardImage(memberInfoCard.getCardImage());
        resultVO.setCardNum(card.getCardNum());
        resultVO.setCardName(memberInfoCard.getCardName());
        resultVO.setCurrentBalance(balanceHandler(memberInfoCard.getCardAmount(), memberInfoCard.getGiftAmount(), memberInfoCard.getSubsidyAmount()));
        return resultVO;
    }

    private void mergeMemberInfoCard(HsaPhysicalCard card, HsaMemberInfoCard memberInfoCardByMember,
                                     HsaMemberInfoCard memberInfoCard, HsaCardBaseInfo cardBaseInfo) {
        // 存在电子卡
        // 实体卡
        HsaMemberInfoCard memberPhysicalCard = new HsaMemberInfoCard();
        BeanUtils.copyProperties(memberInfoCard, memberPhysicalCard);

        // 电子卡
        BeanUtils.copyProperties(memberInfoCardByMember, memberInfoCard);
        if (memberPhysicalCard.getApplicableAllStore() == 1 || memberInfoCard.getApplicableAllStore() == 1) {
            memberInfoCard.setApplicableAllStore(1);
        } else {
            hsaStoreCardRuleService.mergeStoreProcessor(memberInfoCard, memberPhysicalCard);
        }
        memberInfoCard
                .setApplicableAllStore(memberPhysicalCard.getApplicableAllStore() == 1 ? 1 : memberInfoCard.getApplicableAllStore())
                .setGiftAmount(memberInfoCard.getGiftAmount().add(memberPhysicalCard.getGiftAmount()))
                .setCardAmount(memberInfoCard.getCardAmount().add(memberPhysicalCard.getCardAmount()))
                .setSubsidyAmount(memberInfoCard.getSubsidyAmount().add(memberPhysicalCard.getSubsidyAmount()))
                .setRetreatSubsidyAmount(memberInfoCard.getRetreatSubsidyAmount().add(memberPhysicalCard.getRetreatSubsidyAmount()));

        // 超额合并处理
        mergeExcess(memberInfoCard, cardBaseInfo, memberPhysicalCard);
        if (memberInfoCard.getCardValidity() == 1) {
            memberInfoCard.setCardValidityDate(memberInfoCard.getCardValidityDate().isAfter(memberPhysicalCard.getCardValidityDate()) ? memberInfoCard.getCardValidityDate() : memberPhysicalCard.getCardValidityDate());
        } else {
            memberInfoCard.setCardValidity(0);
        }
        memberInfoCard.setGmtCreate(card.getGmtCreate().isBefore(memberPhysicalCard.getGmtCreate()) ? card.getGmtCreate() : memberPhysicalCard.getGmtCreate());
        memberInfoCardMapper.delete(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getGuid, memberPhysicalCard.getGuid()));
    }

    /**
     * 超额合并处理
     */
    private void mergeExcess(HsaMemberInfoCard hsaMemberInfoCard, HsaCardBaseInfo hsaCardBaseInfo, HsaMemberInfoCard memberPhysicalCard) {
        if (hsaCardBaseInfo.getIsExcess() == BooleanEnum.TRUE.getCode()) {
            if (hsaCardBaseInfo.getExcessType() == 0) {
                int num = (hsaCardBaseInfo.getExcessMoney().intValue() - hsaMemberInfoCard.getExcessTimes()) +
                        (hsaCardBaseInfo.getExcessMoney().intValue() - memberPhysicalCard.getExcessTimes());
                if (num >= hsaCardBaseInfo.getExcessMoney().intValue()) {
                    hsaMemberInfoCard.setExcessTimes(0);
                } else {
                    hsaMemberInfoCard.setExcessTimes(hsaCardBaseInfo.getExcessMoney().intValue() - num);
                }
            } else {
                BigDecimal amount = hsaCardBaseInfo.getExcessMoney().subtract(hsaMemberInfoCard.getExcessAmount())
                        .add(hsaCardBaseInfo.getExcessMoney().subtract(memberPhysicalCard.getExcessAmount()));
                if (amount.compareTo(hsaCardBaseInfo.getExcessMoney()) >= 0) {
                    hsaMemberInfoCard.setExcessAmount(BigDecimal.ZERO);
                } else {
                    hsaMemberInfoCard.setExcessAmount(hsaCardBaseInfo.getExcessMoney().subtract(amount));
                }
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean bindingMemberInfo(BindingMemberInfoQO bindingMemberInfoQO) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        HsaMemberInfoCard hsaMemberInfoCard = queryByGuid(bindingMemberInfoQO.getMemberCardGuid());
        if (Objects.isNull(hsaMemberInfoCard)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_CARD_NOT_EXIST);
        }
        HsaPhysicalCard hsaPhysicalCard = hsaPhysicalCardMapper.queryByGuid(hsaMemberInfoCard.getPhysicalCardGuid());
        HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(hsaMemberInfoCard.getCardGuid());
        HsaPhysicalCard physicalCard = getHsaPhysicalCard(bindingMemberInfoQO, hsaMemberInfoCard, headerUserInfo);
        if (ObjectUtil.isNotNull(physicalCard)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_CARD_PHONE_REPETITION);
        }
        //绑定账户是否已有电子卡
        HsaMemberInfoCard memberElectronicCard = getHsaMemberInfoCard(bindingMemberInfoQO, hsaMemberInfoCard, headerUserInfo);
        if (ObjectUtil.isNotNull(memberElectronicCard)) {
            handlerElectronicCard(memberElectronicCard, hsaMemberInfoCard, hsaCardBaseInfo, hsaPhysicalCard);
        } else {
            //无电子卡
            handlerNoElectronicCard(bindingMemberInfoQO, hsaMemberInfoCard, hsaPhysicalCard);
        }
        if (ObjectUtil.isNotNull(hsaPhysicalCard)) {
            hsaPhysicalCard.setBindUserTime(LocalDateTime.now());  //解决绑定用户时间未生效问题
        }
        return hsaPhysicalCardMapper.updateByGuid(hsaPhysicalCard);
    }

    private HsaPhysicalCard getHsaPhysicalCard(BindingMemberInfoQO bindingMemberInfoQO, HsaMemberInfoCard hsaMemberInfoCard, HeaderUserInfo headerUserInfo) {
        return hsaPhysicalCardMapper.selectOne(new LambdaQueryWrapper<HsaPhysicalCard>()
                .eq(HsaPhysicalCard::getPhoneNum, bindingMemberInfoQO.getPhoneNum())
                .eq(HsaPhysicalCard::getCardGuid, hsaMemberInfoCard.getCardGuid())
                .eq(HsaPhysicalCard::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid()));
    }

    private HsaMemberInfoCard getHsaMemberInfoCard(BindingMemberInfoQO bindingMemberInfoQO, HsaMemberInfoCard hsaMemberInfoCard, HeaderUserInfo headerUserInfo) {
        return memberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getMemberPhoneNum, bindingMemberInfoQO.getPhoneNum())
                .eq(HsaMemberInfoCard::getCardGuid, hsaMemberInfoCard.getCardGuid())
                .isNotNull(HsaMemberInfoCard::getElectronicCardGuid)
                .isNull(HsaMemberInfoCard::getPhysicalCardGuid)
                .eq(HsaMemberInfoCard::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid()));
    }

    private void handlerElectronicCard(HsaMemberInfoCard memberElectronicCard, HsaMemberInfoCard hsaMemberInfoCard, HsaCardBaseInfo hsaCardBaseInfo, HsaPhysicalCard hsaPhysicalCard) {
        setElectronicCard(memberElectronicCard, hsaMemberInfoCard);

        //超额处理
        mergeExcess(memberElectronicCard, hsaCardBaseInfo, hsaMemberInfoCard);
        if (hsaMemberInfoCard.getCardValidity() == 1) {
            memberElectronicCard.setCardValidity(1);
            //过期时间取最大
            memberElectronicCard.setCardValidityDate(hsaMemberInfoCard.getCardValidityDate().isAfter(memberElectronicCard.getCardValidityDate())
                    ? hsaMemberInfoCard.getCardValidityDate()
                    : memberElectronicCard.getCardValidityDate());
        } else {
            memberElectronicCard.setCardValidity(0);
        }
        hsaPhysicalCard.setMemberInfoGuid(memberElectronicCard.getMemberInfoGuid());
        hsaPhysicalCard.setMemberBindingState(BooleanEnum.TRUE.getCode());
        //门店范围取交集
        hsaStoreCardRuleService.mergeMemberCardStore(memberElectronicCard, hsaMemberInfoCard);
        memberInfoCardMapper.delete(new LambdaQueryWrapper<HsaMemberInfoCard>().eq(HsaMemberInfoCard::getGuid, hsaMemberInfoCard.getGuid()));
        memberInfoCardMapper.updateByGuid(memberElectronicCard);
    }

    private static void setElectronicCard(HsaMemberInfoCard memberElectronicCard, HsaMemberInfoCard hsaMemberInfoCard) {
        memberElectronicCard.setPhysicalCardGuid(hsaMemberInfoCard.getPhysicalCardGuid())
                .setPhysicalCardNum(hsaMemberInfoCard.getPhysicalCardNum())
                .setPhysicalCardState(hsaMemberInfoCard.getPhysicalCardState())
                .setPhysicalCardActivationTime(hsaMemberInfoCard.getPhysicalCardActivationTime())
                .setIsPhysicalCardRetreat(hsaMemberInfoCard.getIsPhysicalCardRetreat())
                .setIsSupportPhysicalCard(hsaMemberInfoCard.getIsSupportPhysicalCard())
                .setGmtCreate(hsaMemberInfoCard.getGmtCreate().isBefore(memberElectronicCard.getGmtCreate()) ? hsaMemberInfoCard.getGmtCreate() : memberElectronicCard.getGmtCreate())
                .setGmtModified(LocalDateTime.now())
                .setCardAmount(memberElectronicCard.getCardAmount().add(hsaMemberInfoCard.getCardAmount()))
                .setGiftAmount(memberElectronicCard.getGiftAmount().add(hsaMemberInfoCard.getGiftAmount()))
                .setSubsidyAmount(memberElectronicCard.getSubsidyAmount().add(hsaMemberInfoCard.getSubsidyAmount()))
                .setRetreatSubsidyAmount(memberElectronicCard.getRetreatSubsidyAmount().add(hsaMemberInfoCard.getRetreatSubsidyAmount()));
    }

    private void handlerNoElectronicCard(BindingMemberInfoQO bindingMemberInfoQO, HsaMemberInfoCard hsaMemberInfoCard, HsaPhysicalCard hsaPhysicalCard) {
        HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .eq(HsaOperationMemberInfo::getPhoneCountryCode, StringConstant.STR_DEFAULT_PHONE_COUNTRY_CODE)
                .eq(HsaOperationMemberInfo::getPhoneNum, bindingMemberInfoQO.getPhoneNum())
                .eq(HsaOperationMemberInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
        if (ObjectUtil.isNull(hsaOperationMemberInfo)) {
            hsaPhysicalCard.setMemberBindingState(BooleanEnum.TRUE.getCode());
            hsaPhysicalCard.setPhoneNum(bindingMemberInfoQO.getPhoneNum());
            hsaPhysicalCard.setUserName(bindingMemberInfoQO.getMemberName());
        } else {
            HsaMemberInfoCard memberInfoCard = memberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                    .eq(HsaMemberInfoCard::getCardGuid, hsaMemberInfoCard.getCardGuid())
                    .eq(HsaMemberInfoCard::getMemberInfoGuid, hsaOperationMemberInfo.getGuid())
                    .eq(HsaMemberInfoCard::getPhysicalCardGuid, hsaPhysicalCard.getGuid()));
            if (ObjectUtil.isNotNull(memberInfoCard)) {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_CARD_PHONE_REPETITION);
            }
            if (hsaOperationMemberInfo.getAccountState() == BooleanEnum.TRUE.getCode()) {
                throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_ACCOUNT_IS_DISABLED.getDes(), ThreadLocalCache.getOperSubjectGuid()));
            }
            hsaPhysicalCard.setMemberInfoGuid(hsaOperationMemberInfo.getGuid());
            hsaPhysicalCard.setMemberBindingState(BooleanEnum.TRUE.getCode());
            hsaPhysicalCard.setPhoneNum(hsaOperationMemberInfo.getPhoneNum());
            hsaPhysicalCard.setUserName(hsaOperationMemberInfo.getUserName());
            hsaMemberInfoCard.setMemberInfoGuid(hsaOperationMemberInfo.getGuid());
            hsaMemberInfoCard.setPhysicalCardNum(hsaPhysicalCard.getCardNum());
            hsaMemberInfoCard.setPhysicalCardState(hsaPhysicalCard.getCardState());
            hsaMemberInfoCard.setPhysicalCardActivationTime(hsaPhysicalCard.getActivationTime());
            //开通电子卡
            if (ObjectUtil.isNotNull(hsaPhysicalCard.getIssueElectronicCard()) && hsaPhysicalCard.getIssueElectronicCard() == BooleanEnum.TRUE.getCode())
                openElectronic(hsaMemberInfoCard.getGuid(), hsaMemberInfoCard.getMemberInfoGuid(), BooleanEnum.FALSE.getCode());
        }
        hsaMemberInfoCard.setMemberPhoneNum(bindingMemberInfoQO.getPhoneNum());
        memberInfoCardMapper.updateByGuid(hsaMemberInfoCard);

        //会员卡权益
        this.sendCardRights(Collections.singletonList(hsaMemberInfoCard));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedissonLock(lockName = "OPEN_ELECTRONIC", tryLock = true, leaseTime = 10)
    public Boolean openElectronic(String memberCardGuid, String memberInfoGuid, Integer isOverlay) {
        LocalDateTime now = LocalDateTime.now();
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        HsaMemberInfoCard hsaMemberInfoCard = queryByGuid(memberCardGuid);
        if (StringUtils.isNotBlank(Optional.ofNullable(hsaMemberInfoCard).map(HsaMemberInfoCard::getElectronicCardGuid).orElse(null))) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NUMBER_EQUITY_CARDS_EXIST);
        }
        HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(hsaMemberInfoCard.getCardGuid());
        HsaCardOpenRule hsaCardOpenRule = hsaCardOpenRuleMapper.selectOne(new LambdaQueryWrapper<HsaCardOpenRule>()
                .eq(HsaCardOpenRule::getCardGuid, hsaMemberInfoCard.getCardGuid()));
        //校验数量
        boolean isOverOpenCountLimit = hsaCardOpenRule.getSendCountLimit() == BooleanEnum.TRUE.getCode()
                && hsaCardOpenRule.getSurplusSendOpenCountLimit() <= 0;
        if (isOverOpenCountLimit) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NUMBER_CARDS_ISSUED);
        }
        String physicalCardGuid = hsaMemberInfoCard.getPhysicalCardGuid();
        HsaPhysicalCard hsaPhysicalCard = null;
        if (StringUtils.isNotBlank(physicalCardGuid)) {
            hsaPhysicalCard = hsaPhysicalCardMapper.selectOne(new LambdaQueryWrapper<HsaPhysicalCard>()
                    .eq(HsaPhysicalCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                    .eq(HsaPhysicalCard::getGuid, physicalCardGuid));
        }
        Integer issueElectronicCard = Optional.ofNullable(hsaPhysicalCard).map(HsaPhysicalCard::getIssueElectronicCard).orElse(null);
        HsaElectronicCard electronicCard = MemberCardAssembler.getHsaElectronicCard(memberInfoGuid, hsaMemberInfoCard, headerUserInfo, issueElectronicCard, now);
        electronicCard.setCardNum(serialNumberBuilder.getSequence(null));
        electronicCard.setGuid(guidGeneratorUtil.getStringGuid(HsaElectronicCard.class.getSimpleName()));
        HsaOperationMemberInfo info = hsaOperationMemberInfoMapper.queryByGuid(hsaMemberInfoCard.getMemberInfoGuid());
        MemberCardAssembler.disposeMemberInfoCard(memberInfoGuid, isOverlay, hsaMemberInfoCard, electronicCard, now, hsaCardBaseInfo, info);
        updateByGuid(hsaMemberInfoCard);
        hsaElectronicCardMapper.insert(electronicCard);
        hsaCardOpenRule.setSendOpenCountLimit(hsaCardOpenRule.getSendOpenCountLimit() + 1);
        hsaCardOpenRule.setSurplusSendOpenCountLimit(hsaCardOpenRule.getSurplusSendOpenCountLimit() - 1);
        hsaCardOpenRuleMapper.updateByGuid(hsaCardOpenRule);
        if (hsaMemberInfoCard.getApplicableAllStore() == 0) {
            //门店范围合并
            hsaStoreCardRuleService.mergeStoreProcessor(hsaMemberInfoCard, hsaMemberInfoCard.getCardGuid());
        }
        List<HsaMemberInfoCard> list = Lists.newArrayList(hsaMemberInfoCard);
        Map<String, BigDecimal> moneyMap = new HashMap<>();
        moneyMap.put(hsaCardBaseInfo.getGuid(), hsaCardBaseInfo.getCardValueMoney());
        //增加消费明显
        hsaElectronicCardService.addOpenCardBalanceRecord(list, moneyMap, SourceTypeEnum.ADD_BACKGROUND.getCode(),
                CardTypeEnum.CARD_TYPE_EQUITY.getCode());
        return true;
    }

    /**
     * 批量修改会员卡密码
     *
     * @param memberCardPasswordQO 请求对象
     * @return 操作结果
     */
    @Override
    public int updatePassword(MemberCardPasswordQO memberCardPasswordQO) {
        List<HsaMemberInfoCard> hsaMemberInfoCardList = validateMemberCardByGuids(memberCardPasswordQO.getGuids());
        List<String> guisList = hsaMemberInfoCardList.stream()
                .map(HsaMemberInfoCard::getGuid)
                .collect(Collectors.toList());
        return memberInfoCardMapper.batchUpdateByGuid(guisList, SecureUtil.md5(memberCardPasswordQO.getPassword()).toUpperCase());
    }

    @Override
    public MemberInfoCardDetailsVO getDetailsByGuid(String guid) {
        MemberInfoCardDetailsVO memberInfoCardDetailsVO = new MemberInfoCardDetailsVO();
        HsaMemberInfoCard hsaMemberInfoCard = this.queryByGuid(guid);
        if (ObjectUtil.isNull(hsaMemberInfoCard)) {  //会员卡关联表不能为空
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_TO_RECEIVE);
        }
        HsaCardBaseInfo hsaCardBaseInfo = baseInfoService.queryByGuid(hsaMemberInfoCard.getCardGuid());
        if (ObjectUtil.isNull(hsaCardBaseInfo)) {   //会员卡不能为空
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_CARD_NOT_EXIST);
        }
        HsaElectronicCard hsaElectronicCard = hsaElectronicCardMapper.selectOne(  //查询电子卡
                new LambdaQueryWrapper<HsaElectronicCard>()
                        .eq(HsaElectronicCard::getGuid, hsaMemberInfoCard.getElectronicCardGuid()));
        //查询实体卡
        HsaPhysicalCard hsaPhysicalCard = hsaPhysicalCardService.queryByGuid(hsaMemberInfoCard.getPhysicalCardGuid());

        //会员卡-基础信息
        memberInfoCardDetailsVO.setMemberCardBaseInfoDTO( //封装会员卡基础信息
                carBaseInfoHandler(hsaCardBaseInfo, hsaMemberInfoCard, hsaElectronicCard, hsaPhysicalCard));

        //会员卡-电子卡信息
        memberInfoCardDetailsVO.setMemberCardElectronicDTO(electronicCardHandler(hsaElectronicCard));

        //会员卡-实体卡信息
        memberInfoCardDetailsVO.setMemberCardPhysicalDTO(physicalCardHandler(hsaPhysicalCard));
        return memberInfoCardDetailsVO;
    }

    @Override
    public int batchUpdateElectronicCardState(List<String> guids, Integer status) {
        return memberInfoCardMapper.updateElectronicCardState(guids, status);
    }

    @Override
    public int batchUpdatePhysicalCardState(List<String> guids, Integer status) {
        return memberInfoCardMapper.updatePhysicalCardState(guids, status);
    }

    @Override
    public int updatePhysicalCardState(String guid, Integer status) {
        return memberInfoCardMapper.updatePhysicalCardStateByGuid(guid, status);
    }

    /**
     * 实体卡前端数据处理
     *
     * @param hsaPhysicalCard 实体卡信息
     * @return 操作结果
     */
    public MemberCardPhysicalDTO physicalCardHandler(HsaPhysicalCard hsaPhysicalCard) {
        MemberCardPhysicalDTO memberCardPhysicalDTO = new MemberCardPhysicalDTO();

        if (ObjectUtil.isNull(hsaPhysicalCard)) {
            return memberCardPhysicalDTO;
        }
        BeanUtil.copyProperties(hsaPhysicalCard, memberCardPhysicalDTO);
        //查询实体卡绑定的押金策略
        memberCardPhysicalDTO.setCashPledge(hsaPhysicalCard.getCashPledge());
        memberCardPhysicalDTO.setReturnableCashPledge(hsaPhysicalCard.getReturnableCashPledge());
        memberCardPhysicalDTO.setDepositStrategyName(hsaPhysicalCard.getCashName());
        memberCardPhysicalDTO.setDepositAmount(hsaPhysicalCard.getCashPledge());
        int isRefundDeposit = Optional.ofNullable(hsaPhysicalCard.getReturnableCashPledge()).map(amount -> {
            if (amount.compareTo(BigDecimal.ZERO) > 0) {
                return 1;
            }
            return 0;
        }).orElse(0);
        memberCardPhysicalDTO.setIsRefundDeposit(isRefundDeposit);
        memberCardPhysicalDTO.setRefundAmount(hsaPhysicalCard.getReturnableCashPledge());
        // 退卡规则
        memberCardPhysicalDTO.setRefundCardRule(CLEAR_ALL);
        HsaCardBalanceRule hsaCardBalanceRule = hsaCardBalanceRuleMapper.selectOne(
                new LambdaQueryWrapper<HsaCardBalanceRule>()
                        .eq(HsaCardBalanceRule::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
        if (!ObjectUtils.isEmpty(hsaCardBalanceRule)) {
            memberCardPhysicalDTO.setRefundCardRule(hsaCardBalanceRule.getRefundCardRule());
        }
        return memberCardPhysicalDTO;
    }

    /**
     * 电子卡前端数据处理
     *
     * @param hsaElectronicCard 电子卡信息
     * @return 操作结果
     */
    public MemberCardElectronicDTO electronicCardHandler(HsaElectronicCard hsaElectronicCard) {
        MemberCardElectronicDTO memberCardElectronicDTO = new MemberCardElectronicDTO();

        if (ObjectUtil.isNull(hsaElectronicCard)) {
            return memberCardElectronicDTO;
        }
        BeanUtil.copyProperties(hsaElectronicCard, memberCardElectronicDTO);
        //电子卡开卡规则方式
        OpenCardRuleDTO openCardRuleDTO = new OpenCardRuleDTO();
        HsaCardOpenRule hsaCardOpenRule = hsaCardOpenRuleMapper.selectOne(
                new LambdaQueryWrapper<HsaCardOpenRule>()
                        .eq(HsaCardOpenRule::getCardGuid, hsaElectronicCard.getCardGuid()));
        BeanUtil.copyProperties(hsaCardOpenRule, openCardRuleDTO);
        return memberCardElectronicDTO;
    }


    /**
     * 会员卡基础信息前端数据处理
     *
     * @param hsaCardBaseInfo 会员卡基础信息
     * @param hsaMemberInfoCard 会员卡关联信息
     * @param hsaElectronicCard 会员卡-电子卡信息
     * @param hsaPhysicalCard 会员卡-实体卡信息
     * @return 会员卡基础信息DTO
     */
    public MemberCardBaseInfoDTO carBaseInfoHandler(HsaCardBaseInfo hsaCardBaseInfo,
                                                    HsaMemberInfoCard hsaMemberInfoCard,
                                                    HsaElectronicCard hsaElectronicCard,
                                                    HsaPhysicalCard hsaPhysicalCard) {
        // 参数校验
        if (hsaMemberInfoCard == null) {
            throw new IllegalArgumentException("会员卡关联信息不能为空");
        }
        
        // 基础信息转换
        MemberCardBaseInfoDTO memberCardBaseInfoDTO = HsaMemberInfoCardTransform.INSTANCE.cardBaseInfo2Entity(hsaMemberInfoCard);
        
        // 设置会员信息
        setMemberInfo(memberCardBaseInfoDTO, hsaMemberInfoCard, hsaPhysicalCard);
        
        // 设置卡基础信息
        setCardBaseInfo(memberCardBaseInfoDTO, hsaCardBaseInfo, hsaMemberInfoCard);
        
        // 设置超额支付信息
        setExcessPaymentInfo(memberCardBaseInfoDTO, hsaCardBaseInfo, hsaMemberInfoCard);
        
        // 设置余额相关信息
        setBalanceInfo(memberCardBaseInfoDTO, hsaMemberInfoCard, hsaCardBaseInfo, hsaElectronicCard, hsaPhysicalCard);
        
        // 设置冻结金额
        setFreezeAmount(memberCardBaseInfoDTO, hsaMemberInfoCard);
        
        return memberCardBaseInfoDTO;
    }
    
    /**
     * 设置会员信息
     * 
     * @param memberCardBaseInfoDTO DTO对象
     * @param hsaMemberInfoCard 会员卡关联信息
     * @param hsaPhysicalCard 实体卡信息
     */
    private void setMemberInfo(MemberCardBaseInfoDTO memberCardBaseInfoDTO, 
                               HsaMemberInfoCard hsaMemberInfoCard, 
                               HsaPhysicalCard hsaPhysicalCard) {
        // 优先从会员信息中获取
        HsaOperationMemberInfo memberInfo = hsaOperationMemberInfoMapper.queryByGuid(hsaMemberInfoCard.getMemberInfoGuid());
        
        if (memberInfo != null) {
            memberCardBaseInfoDTO.setMemberName(memberInfo.getUserName());
            memberCardBaseInfoDTO.setPhone(memberInfo.getPhoneNum());
            memberCardBaseInfoDTO.setPhoneCountryCode(memberInfo.getPhoneCountryCode());
        } else if (hsaPhysicalCard != null) {
            // 从实体卡信息中获取
            memberCardBaseInfoDTO.setMemberName(hsaPhysicalCard.getUserName());
            memberCardBaseInfoDTO.setPhone(hsaPhysicalCard.getPhoneNum());
            memberCardBaseInfoDTO.setPhoneCountryCode("86"); // 默认中国区号
        }
    }
    
    /**
     * 设置卡基础信息
     * 
     * @param memberCardBaseInfoDTO DTO对象
     * @param hsaCardBaseInfo 卡基础信息
     * @param hsaMemberInfoCard 会员卡关联信息
     */
    private void setCardBaseInfo(MemberCardBaseInfoDTO memberCardBaseInfoDTO, 
                                 HsaCardBaseInfo hsaCardBaseInfo, 
                                 HsaMemberInfoCard hsaMemberInfoCard) {
        if (hsaCardBaseInfo == null) {
            return;
        }
        
        // 设置基础属性
        memberCardBaseInfoDTO.setCardImage(hsaCardBaseInfo.getCardImage());
        memberCardBaseInfoDTO.setApplicableAllStore(hsaMemberInfoCard.getApplicableAllStore());
        memberCardBaseInfoDTO.setIsPreStored(hsaCardBaseInfo.getIsPreStored());
        memberCardBaseInfoDTO.setAppletRecharge(hsaCardBaseInfo.getAppletRecharge());
        memberCardBaseInfoDTO.setCardStatus(hsaCardBaseInfo.getCardStatus());
        
        // 设置门店列表（仅部分门店适用时）
        if (hsaMemberInfoCard.getApplicableAllStore() == BooleanEnum.FALSE.getCode()) {
            List<String> cardGuids = Collections.singletonList(hsaCardBaseInfo.getGuid());
            memberCardBaseInfoDTO.setStoreList(getStoreCardRule(cardGuids, hsaMemberInfoCard.getGuid()));
        }
    }
    
    /**
     * 设置超额支付信息
     * 
     * @param memberCardBaseInfoDTO DTO对象
     * @param hsaCardBaseInfo 卡基础信息
     * @param hsaMemberInfoCard 会员卡关联信息
     */
    private void setExcessPaymentInfo(MemberCardBaseInfoDTO memberCardBaseInfoDTO, 
                                      HsaCardBaseInfo hsaCardBaseInfo, 
                                      HsaMemberInfoCard hsaMemberInfoCard) {
        if (hsaCardBaseInfo == null) {
            return;
        }
        
        memberCardBaseInfoDTO.setIsExcess(hsaCardBaseInfo.getIsExcess());
        memberCardBaseInfoDTO.setExcessMoney(hsaCardBaseInfo.getExcessMoney());
        memberCardBaseInfoDTO.setExcessType(hsaCardBaseInfo.getExcessType());
        
        // 设置会员超额金额
        if (hsaCardBaseInfo.getExcessType() != null) {
            BigDecimal memberExcessMoney = (hsaCardBaseInfo.getExcessType() == 0) 
                    ? new BigDecimal(hsaMemberInfoCard.getExcessTimes())
                    : hsaMemberInfoCard.getExcessAmount();
            memberCardBaseInfoDTO.setMemberExcessMoney(memberExcessMoney);
        }
    }
    
    /**
     * 设置余额相关信息
     * 
     * @param memberCardBaseInfoDTO DTO对象
     * @param hsaMemberInfoCard 会员卡关联信息
     * @param hsaCardBaseInfo 卡基础信息
     * @param hsaElectronicCard 电子卡信息
     * @param hsaPhysicalCard 实体卡信息
     */
    private void setBalanceInfo(MemberCardBaseInfoDTO memberCardBaseInfoDTO, 
                                HsaMemberInfoCard hsaMemberInfoCard,
                                HsaCardBaseInfo hsaCardBaseInfo,
                                HsaElectronicCard hsaElectronicCard,
                                HsaPhysicalCard hsaPhysicalCard) {
        // 计算当前余额
        BigDecimal currentBalance = balanceHandler(
                hsaMemberInfoCard.getCardAmount(),   // 实充金额
                hsaMemberInfoCard.getGiftAmount(),   // 赠送金额
                hsaMemberInfoCard.getSubsidyAmount() // 补贴金额
        );
        memberCardBaseInfoDTO.setCurrentBalance(currentBalance);
        
        // 设置有效期
        String validTime = cardTimeHandler(hsaCardBaseInfo, hsaElectronicCard, hsaPhysicalCard);
        memberCardBaseInfoDTO.setValidTime(validTime);
    }
    
    /**
     * 设置冻结金额
     * 
     * @param memberCardBaseInfoDTO DTO对象
     * @param hsaMemberInfoCard 会员卡关联信息
     */
    private void setFreezeAmount(MemberCardBaseInfoDTO memberCardBaseInfoDTO, 
                                 HsaMemberInfoCard hsaMemberInfoCard) {
        // 构建查询对象
        CardFreezeBalanceAmountQO cardQO = new CardFreezeBalanceAmountQO();
        cardQO.setElectronicCardGuid(hsaMemberInfoCard.getElectronicCardGuid());
        cardQO.setPhysicalCardGuid(hsaMemberInfoCard.getPhysicalCardGuid());
        cardQO.setMemberInfoGuid(hsaMemberInfoCard.getMemberInfoGuid());
        
        // 获取冻结金额
        BigDecimal freezeAmount = hsaCardRechargeGiftDetailService.getMemberCardFreezeAmount(cardQO);
        
        // 累加冻结金额到余额和赠送金额
        BigDecimal newCurrentBalance = memberCardBaseInfoDTO.getCurrentBalance().add(freezeAmount);
        BigDecimal newGiftAmount = memberCardBaseInfoDTO.getGiftAmount().add(freezeAmount);
        
        memberCardBaseInfoDTO.setCurrentBalance(newCurrentBalance);
        memberCardBaseInfoDTO.setGiftAmount(newGiftAmount);
    }

    /**
     * 获取会员卡下适用门店
     *
     * @param cardGuid hsaCardBaseInfo
     */
    @Override
    public List<StoreCardRuleQO> getStoreCardRule(List<String> cardGuid, String memberInfoCardGuid) {
        List<StoreCardRuleQO> saveStoreCardRuleQOList = Lists.newArrayList();
        List<HsaStoreCardRule> hsaStoreCardRules;
        //门店级
        if (StringUtils.isNotBlank(memberInfoCardGuid)) {
            hsaStoreCardRules = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                    .in(!CollectionUtil.isEmpty(cardGuid), HsaStoreCardRule::getCardGuid, cardGuid)
                    .eq(StringUtils.isNotBlank(memberInfoCardGuid), HsaStoreCardRule::getMemberInfoCardGuid, memberInfoCardGuid)
                    .isNull(HsaStoreCardRule::getParentGuid));
        } else {
            hsaStoreCardRules = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                    .in(!CollUtil.isEmpty(cardGuid), HsaStoreCardRule::getCardGuid, cardGuid)
                    .isNull(HsaStoreCardRule::getMemberInfoCardGuid)
                    .isNull(HsaStoreCardRule::getParentGuid));
        }
        if (CollUtil.isNotEmpty(hsaStoreCardRules)) {

            List<String> parentGuidList = hsaStoreCardRules.stream().map(HsaStoreCardRule::getGuid).collect(Collectors.toList());

            //档口级
            Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap = hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                            .in(HsaStoreCardRule::getParentGuid, parentGuidList))
                    .stream().collect(Collectors.groupingBy(HsaStoreCardRule::getParentGuid));
            if (CollUtil.isNotEmpty(hsaStoreCardRules)) {
                forStoreCardRules(hsaStoreCardRules, hsaStoreCardRuleMap, saveStoreCardRuleQOList);
            }
        }
        return saveStoreCardRuleQOList;
    }

    private static void forStoreCardRules(List<HsaStoreCardRule> hsaStoreCardRules, Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap, List<StoreCardRuleQO> saveStoreCardRuleQOList) {
        for (HsaStoreCardRule hsaStoreCardRule : hsaStoreCardRules) {
            StoreCardRuleQO storeCardRuleQO = new StoreCardRuleQO();
            storeCardRuleQO.setStoreGuid(String.valueOf(hsaStoreCardRule.getStoreGuid()));
            storeCardRuleQO.setStoreName(hsaStoreCardRule.getStoreName());
            storeCardRuleQO.setGuid(String.valueOf(hsaStoreCardRule.getGuid()));
            storeCardRuleQO.setCardGuid(String.valueOf(hsaStoreCardRule.getCardGuid()));
            storeCardRuleQO.setStoreNumber(hsaStoreCardRule.getStoreNumber());
            storeCardRuleQO.setAddress(hsaStoreCardRule.getAddress());
            storeCardRuleQO.setAddress_point(hsaStoreCardRule.getAddressPoint());
            storeCardRuleQO.setTime(hsaStoreCardRule.getTime());
            storeCardRuleQO.setSystem(hsaStoreCardRule.getSystem());
            //处理档口数据
            if (CollUtil.isNotEmpty(hsaStoreCardRuleMap) && hsaStoreCardRuleMap.containsKey(hsaStoreCardRule.getGuid())) {
                List<StoreBoothCardRuleQO> storeBoothCardRuleQOList = Lists.newArrayList();
                List<HsaStoreCardRule> ruleList = hsaStoreCardRuleMap.get(hsaStoreCardRule.getGuid());
                ruleList.forEach(in -> {
                    StoreBoothCardRuleQO storeBoothCardRuleQO = new StoreBoothCardRuleQO();
                    storeBoothCardRuleQO.setStoreGuid(String.valueOf(in.getStoreGuid()));
                    storeBoothCardRuleQO.setAddressPoint(in.getAddressPoint());
                    storeBoothCardRuleQO.setStoreName(in.getStoreName());
                    storeBoothCardRuleQO.setGuid(in.getGuid());
                    storeBoothCardRuleQO.setCardGuid(hsaStoreCardRule.getCardGuid());
                    storeCardRuleQO.setStoreNumber(in.getStoreNumber());
                    storeBoothCardRuleQOList.add(storeBoothCardRuleQO);
                });
                storeCardRuleQO.setStoreBoothCardRuleQOList(storeBoothCardRuleQOList);
            }
            saveStoreCardRuleQOList.add(storeCardRuleQO);
        }
    }

    @Override
    public PageResult findStoreCardRule(MemberCardStoreDataQO request) {

        int applicableAllStore;
        if (!StringUtils.isEmpty(request.getOwnGuid())) {
            HsaMemberInfoCard memberInfoCard = memberInfoCardMapper.queryByGuid(request.getOwnGuid());
            applicableAllStore = memberInfoCard.getApplicableAllStore();
        } else {
            HsaCardBaseInfo hsaCardBaseInfo = baseInfoService.queryByGuid(request.getCardGuid());
            applicableAllStore = hsaCardBaseInfo.getApplicableAllStore();
        }
        List<StoreBaseInfo> list;
        if (applicableAllStore == ALL_STORE) {
            list = storeBaseService.appletGetStoreInfo();
            return PageUtil.getPageResult(new PageInfo<>(list));
        } else {
            PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
            list = hsaStoreCardRuleMapper.findStoreCardRule(request);
        }
        list = calculateDistance(list, request.getLongitude(), request.getLatitude());
        return PageUtil.getPageResult(new PageInfo<>(list));
    }

    /**
     * 门店根据经纬度，计算门店距离
     *
     * @param storeBaseInfos 门店集合
     * @param longitude      用户经度
     * @param latitude       用户纬度
     * @return 计算距离后的门店信息（按距离排序）
     */
    private List<StoreBaseInfo> calculateDistance(List<StoreBaseInfo> storeBaseInfos, String longitude, String latitude) {
        if (CollUtil.isEmpty(storeBaseInfos)) {
            return Collections.emptyList();
        }

        // 校验用户经纬度参数
        if (!isValidCoordinate(longitude, latitude)) {
            // 如果用户经纬度无效，设置所有门店距离为-1
            storeBaseInfos.forEach(store -> store.setDistance(new BigDecimal(-1)));
            return storeBaseInfos;
        }

        // 并行计算距离以提高性能
        storeBaseInfos.parallelStream().forEach(storeBaseInfo -> {
            BigDecimal distance = calculateSingleStoreDistance(storeBaseInfo, longitude, latitude);
            storeBaseInfo.setDistance(distance);
        });

        // 按距离排序：有效距离按从小到大，无效距离(-1)排在最后
        return storeBaseInfos.stream()
                .sorted((store1, store2) -> {
                    BigDecimal dist1 = store1.getDistance();
                    BigDecimal dist2 = store2.getDistance();

                    // 处理无效距离(-1)
                    boolean dist1Invalid = dist1.compareTo(BigDecimal.ZERO) < 0;
                    boolean dist2Invalid = dist2.compareTo(BigDecimal.ZERO) < 0;

                    if (dist1Invalid && dist2Invalid) {
                        return 0; // 都是无效距离，保持原序
                    } else if (dist1Invalid) {
                        return 1; // dist1无效，排在后面
                    } else if (dist2Invalid) {
                        return -1; // dist2无效，排在后面
                    } else {
                        return dist1.compareTo(dist2); // 正常距离比较
                    }
                })
                .collect(Collectors.toList());
    }

    /**
     * 计算单个门店的距离
     *
     * @param storeBaseInfo 门店信息
     * @param userLongitude 用户经度
     * @param userLatitude  用户纬度
     * @return 距离（公里），无效时返回-1
     */
    private BigDecimal calculateSingleStoreDistance(StoreBaseInfo storeBaseInfo, String userLongitude, String userLatitude) {
        try {
            String addressPoint = getStoreAddressPoint(storeBaseInfo);
            if (StringUtils.isEmpty(addressPoint)) {
                return new BigDecimal(-1);
            }

            // 解析门店坐标
            StoreCoordinateDTO coordinate = parseStoreCoordinate(addressPoint);
            if (coordinate == null) {
                return new BigDecimal(-1);
            }

            // 使用WGS84模型计算精确距离
            return DistanceUtils.getDistanceWGS84(
                    coordinate.getLongitude(),
                    coordinate.getLatitude(),
                    userLongitude,
                    userLatitude
            );
        } catch (Exception e) {
            log.warn("计算门店距离失败: storeId={}, error={}",
                    storeBaseInfo.getId(), e.getMessage());
            return new BigDecimal(-1);
        }
    }

    /**
     * 获取门店地址坐标点
     *
     * @param storeBaseInfo 门店信息
     * @return 坐标点字符串
     */
    private String getStoreAddressPoint(StoreBaseInfo storeBaseInfo) {
        // 优先使用 address_point，其次使用 addressPoint
        return StringUtils.isNotEmpty(storeBaseInfo.getAddress_point()) ?
                storeBaseInfo.getAddress_point() :
                storeBaseInfo.getAddressPoint();
    }

    /**
     * 解析门店坐标字符串
     *
     * @param addressPoint 坐标字符串，格式：纬度,经度
     * @return 坐标对象，解析失败返回null
     */
    private StoreCoordinateDTO parseStoreCoordinate(String addressPoint) {
        if (StringUtils.isEmpty(addressPoint)) {
            return null;
        }

        String[] coordinates = addressPoint.split(StringConstant.COMMA);
        if (coordinates.length != 2) {
            log.warn("门店坐标格式错误: {}", addressPoint);
            return null;
        }

        try {
            String latitude = coordinates[0].trim();   // 纬度
            String longitude = coordinates[1].trim();  // 经度

            if (!isValidCoordinate(longitude, latitude)) {
                return null;
            }

            return new StoreCoordinateDTO(latitude, longitude);
        } catch (Exception e) {
            log.warn("解析门店坐标失败: {}, error: {}", addressPoint, e.getMessage());
            return null;
        }
    }

    /**
     * 校验经纬度坐标是否有效
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return 是否有效
     */
    private boolean isValidCoordinate(String longitude, String latitude) {
        if (StringUtils.isEmpty(longitude) || StringUtils.isEmpty(latitude)) {
            return false;
        }

        try {
            double lon = Double.parseDouble(longitude);
            double lat = Double.parseDouble(latitude);

            // 经度范围：-180 到 180
            // 纬度范围：-90 到 90
            return lon >= -180 && lon <= 180 && lat >= -90 && lat <= 90;
        } catch (NumberFormatException e) {
            return false;
        }
    }



    /**
     * 处理会员卡有效使用时间
     * 使用有效期：显示当前会员电子卡、实体卡总使用有效期；电子卡从开通成功开始计算，实体卡从激活成功开始计算；
     * ① 仅存在会员电子卡：使用有效期=电子卡使用有效期；
     * ② 仅存在会员实体卡：使用有效期=实体卡使用有效期；
     * ③ 存在实体卡+电子卡：使用有效期=实体卡+电子卡合计覆盖的使用有效期范围；（如任一存在"永久有效"即默认为永久有效）；
     *
     * @param hsaCardBaseInfo   会员卡基础信息
     * @param hsaElectronicCard 会员卡-电子卡信息
     * @param hsaPhysicalCard   会员卡-实体卡信息
     */
    public String cardTimeHandler(HsaCardBaseInfo hsaCardBaseInfo,
                                  HsaElectronicCard hsaElectronicCard,
                                  HsaPhysicalCard hsaPhysicalCard) {
        if (ObjectUtil.isNull(hsaCardBaseInfo)) {
            return null;
        }
        CardTimeFactory cardTimeFactory = new CardTimeFactory();
        CardValidityType cardValidityType = cardTimeFactory.timeHandler(hsaCardBaseInfo.getCardValidity());
        return cardValidityType.cardTimeHandler(hsaCardBaseInfo, hsaElectronicCard, hsaPhysicalCard);
    }

    /**
     * @param cardAmount    充值余额
     * @param giftAmount    赠送余额
     * @param subsidyAmount 补贴余额
     * @return
     */
    @Override
    public BigDecimal balanceHandler(BigDecimal cardAmount, BigDecimal giftAmount, BigDecimal subsidyAmount) {
        BigDecimal cardBalance = BigDecimalUtil.nonNullValue(cardAmount);
        BigDecimal giftBalance = BigDecimalUtil.nonNullValue(giftAmount);
        BigDecimal subsidyBalance = BigDecimalUtil.nonNullValue(subsidyAmount);
        return cardBalance.add(giftBalance).add(subsidyBalance);
    }

    /**
     * 校验当前会员卡是否存在
     *
     * @param guids
     * @return 返回对应会员卡信息
     */
    private List<HsaMemberInfoCard> validateMemberCardByGuids(List<String> guids) {
        if (!CollUtil.isNotEmpty(guids)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_CARD_NOT_EXIST);
        }
        List<HsaMemberInfoCard> hsaCardBaseInfos = memberInfoCardMapper.selectList(
                new LambdaQueryWrapper<HsaMemberInfoCard>()
                        .in(HsaMemberInfoCard::getGuid, guids));
        if (!CollUtil.isNotEmpty(hsaCardBaseInfos)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_CARD_NOT_EXIST);
        }
        return hsaCardBaseInfos;
    }

    @Override
    public List<AppletMemberCardVO> appletQueryMemberCard(String memberGuid) {
        if (com.holderzone.member.common.util.verify.ObjectUtil.checkParam(memberGuid)) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_NOT_NULL_MEMBER_GUID, ThreadLocalCache.getOperSubjectGuid()));
        }
        //会员卡信息
        List<HsaMemberInfoCard> memberInfoCards = memberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getGuid, memberGuid));
        List<AppletMemberCardVO> appletMemberCardVOS = Lists.newArrayList();
        if (CollectionUtils.isEmpty(memberInfoCards)) {
            return appletMemberCardVOS;
        }
        for (HsaMemberInfoCard memberInfoCard : memberInfoCards) {
            appletMemberCardVOS.add(new AppletMemberCardVO()
                    .setCardGuid(memberInfoCard.getCardGuid())
                    .setCardName(memberInfoCard.getCardName())
                    .setMemberInfoCardGuid(memberInfoCard.getGuid())
                    .setCardType(StringUtils.isNotBlank(memberInfoCard.getElectronicCardGuid()) ? 1 : 0)
                    .setSubsidyAmount(memberInfoCard.getSubsidyAmount())
                    .setCardAmount(memberInfoCard.getCardAmount())
                    .setGiftAmount(memberInfoCard.getGiftAmount())
                    .setTotalAmount(balanceHandler(memberInfoCard.getSubsidyAmount(),
                            memberInfoCard.getCardAmount(), memberInfoCard.getGiftAmount())));
        }
        return appletMemberCardVOS;
    }


    @Override
    public boolean editPassword(EditPasswordQO qo) {
        if (StringUtils.isEmpty(qo.getElectronicCardNum()) && StringUtils.isEmpty(qo.getPhysicalCardNum())) {
            throw new BusinessException("卡号不能为空");
        }
        HsaMemberInfoCard hsaMemberInfoCard = null;
        if (StringUtils.isNotEmpty(qo.getElectronicCardNum())) {
            hsaMemberInfoCard = memberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                    .eq(HsaMemberInfoCard::getElectronicCardNum, qo.getElectronicCardNum()));
            if (Objects.isNull(hsaMemberInfoCard)) {
                throw new MemberBaseException(CardOperationExceptionEnum.ELECTRONIC_CARD_NULL);
            }
        } else {
            hsaMemberInfoCard = memberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                    .eq(HsaMemberInfoCard::getPhysicalCardNum, qo.getPhysicalCardNum()));
            if (Objects.isNull(hsaMemberInfoCard)) {
                throw new MemberBaseException(CardOperationExceptionEnum.PHYSICAL_CARD_NULL);
            }
        }
        if (!Objects.equals(hsaMemberInfoCard.getCardPayPassword(), SecureUtil.md5(qo.getOldPassword()).toUpperCase())) {
            throw new MemberBaseException(CardOperationExceptionEnum.PASSWORD_ERROR);
        }
        hsaMemberInfoCard.setCardPayPassword(SecureUtil.md5(qo.getNewPassword()).toUpperCase());
        return this.updateByGuid(hsaMemberInfoCard);
    }

    @Override
    public CardQrCodeVO getQrCode(String cardNum, String creditUserGuid) {
        CardQrCodeVO cardQrCodeVO = new CardQrCodeVO();
        if (StringUtils.isNotEmpty(cardNum)) {
            //电子卡信息
            HsaMemberInfoCard electronicCard = this.getOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                    .eq(HsaMemberInfoCard::getElectronicCardNum, cardNum));
            if (ObjectUtil.isNull(electronicCard)) {
                throw new MemberBaseException(CardOperationExceptionEnum.ELECTRONIC_CARD_NULL);
            }
            cardQrCodeVO.setCardQrCode(qrCodeSupport.getQrStr(cardNum, QrcodeTypeEnum.MEMBER.getDes()));
            cardQrCodeVO.setCardBarcode(qrCodeSupport.getBarcode(cardNum, QrcodeTypeEnum.MEMBER.getDes()));
            return cardQrCodeVO;
        }
        if (StringUtils.isNotEmpty(creditUserGuid)) {
            cardQrCodeVO.setCardQrCode(qrCodeSupport.getQrStr(creditUserGuid, QrcodeTypeEnum.CREDIT.getDes()));
            cardQrCodeVO.setCardBarcode(qrCodeSupport.getBarcode(creditUserGuid, QrcodeTypeEnum.CREDIT.getDes()));
        }

        return cardQrCodeVO;
    }


    @Override
    public Boolean updateStoreScope(UpdateStoreCardRuleQO updateStoreCardRuleQO) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        List<HsaMemberInfoCard> hsaMemberInfoCards = memberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .in(HsaMemberInfoCard::getGuid, updateStoreCardRuleQO.getMemberInfoCardGuid()));
        if (CollectionUtils.isEmpty(hsaMemberInfoCards)) {
            return false;
        }
        List<String> memberInfoCardGuid = hsaMemberInfoCards.stream().map(HsaMemberInfoCard::getGuid).collect(Collectors.toList());
        hsaStoreCardRuleMapper.delete(new LambdaQueryWrapper<HsaStoreCardRule>()
                .in(HsaStoreCardRule::getMemberInfoCardGuid, memberInfoCardGuid));

        List<StoreCardRuleQO> storeBoothCardRuleQOList = updateStoreCardRuleQO.getStoreCardRuleQOList();
        //批量处理
        List<HsaStoreCardRule> hsaStoreCardRules = Lists.newArrayList();
        if (updateStoreCardRuleQO.getApplicableAllStore() == BooleanEnum.FALSE.getCode()) {
            forMemberInfoCards(hsaMemberInfoCards, storeBoothCardRuleQOList, headerUserInfo, hsaStoreCardRules);
        } else {
            hsaMemberInfoCards.forEach(in -> in.setApplicableAllStore(BooleanEnum.TRUE.getCode()));
        }
        if (CollUtil.isNotEmpty(hsaMemberInfoCards)) {
            memberInfoCardMapper.batchUpdateByApplicableAllStore(hsaMemberInfoCards);
        }
        if (CollUtil.isNotEmpty(hsaStoreCardRules)) {
            hsaStoreCardRuleService.saveBatch(hsaStoreCardRules);
        }
        return true;
    }

    private void forMemberInfoCards(List<HsaMemberInfoCard> hsaMemberInfoCards, List<StoreCardRuleQO> storeBoothCardRuleQOList, HeaderUserInfo headerUserInfo, List<HsaStoreCardRule> hsaStoreCardRules) {
        for (HsaMemberInfoCard memberCardGuid : hsaMemberInfoCards) {

            storeBoothCardRuleQOList.forEach(in -> {
                HsaStoreCardRule hsaStoreCardRule = getHsaStoreCardRule(headerUserInfo, memberCardGuid, in);
                hsaStoreCardRules.add(hsaStoreCardRule);
                memberCardGuid.setApplicableAllStore(BooleanEnum.FALSE.getCode());
                if (CollUtil.isNotEmpty(in.getStoreBoothCardRuleQOList())) {
                    in.getStoreBoothCardRuleQOList().forEach(bo -> {
                        HsaStoreCardRule rule = getHsaStoreCardRule(headerUserInfo, memberCardGuid, bo, hsaStoreCardRule);
                        hsaStoreCardRules.add(rule);
                    });
                }
            });
        }
    }

    private HsaStoreCardRule getHsaStoreCardRule(HeaderUserInfo headerUserInfo, HsaMemberInfoCard memberCardGuid, StoreBoothCardRuleQO bo, HsaStoreCardRule hsaStoreCardRule) {
        HsaStoreCardRule rule = new HsaStoreCardRule();
        rule.setCardGuid(memberCardGuid.getCardGuid())
                .setGuid(guidGeneratorUtil.getStringGuid(HsaStoreCardRule.class.getSimpleName()))
                .setOperSubjectGuid(headerUserInfo.getOperSubjectGuid())
                .setStoreName(bo.getStoreName())
                .setStoreGuid(bo.getStoreGuid())
                .setAddress(bo.getAddress())
                .setTime(bo.getTime())
                .setAddressPoint(bo.getAddressPoint())
                .setStoreNumber(bo.getStoreNumber())
                .setParentGuid(hsaStoreCardRule.getGuid())
                .setMemberInfoCardGuid(memberCardGuid.getGuid());
        return rule;
    }

    private HsaStoreCardRule getHsaStoreCardRule(HeaderUserInfo headerUserInfo, HsaMemberInfoCard memberCardGuid, StoreCardRuleQO in) {
        HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
        hsaStoreCardRule.setCardGuid(memberCardGuid.getCardGuid())
                .setGuid(guidGeneratorUtil.getStringGuid(HsaStoreCardRule.class.getSimpleName()))
                .setOperSubjectGuid(headerUserInfo.getOperSubjectGuid())
                .setStoreGuid(in.getStoreGuid())
                .setStoreName(in.getStoreName())
                .setAddress(in.getAddress())
                .setAddressPoint(in.getAddress_point())
                .setTime(in.getTime())
                .setMemberInfoCardGuid(memberCardGuid.getGuid())
                .setStoreNumber(in.getStoreNumber());
        return hsaStoreCardRule;
    }

    @Override
    public CardWeChatRechargeDataVO getCardWeChatRechargeData(String memberInfoCardGuid, String storeGuid) {
        CardWeChatRechargeDataVO vo = new CardWeChatRechargeDataVO();
        HsaMemberInfoCard hsaMemberInfoCard = queryByGuid(memberInfoCardGuid);
        HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(hsaMemberInfoCard.getCardGuid());
        BeanUtils.copyProperties(hsaCardBaseInfo, vo);
        vo.setCardName(hsaCardBaseInfo.getCardName());
        vo.setBalance(hsaMemberInfoCard.getCardAmount().add(hsaMemberInfoCard.getGiftAmount()).add(hsaMemberInfoCard.getSubsidyAmount()));


        List<String> rechargeMoneys = JSON.parseArray(hsaCardBaseInfo.getRechargeMoneys(), String.class);
        vo.setRechargeMoneys(rechargeMoneys);

        List<RechargeGiftActivityVO> rechargeGiftActivityList = Lists.newArrayList();
        Long startTime = System.currentTimeMillis();
        // 充值活动档位列表
        List<RechargeThresholdVO> rechargeThresholdList = activityHelper.buildRechargeThresholdByCard(
                hsaMemberInfoCard.getMemberInfoGuid(), hsaMemberInfoCard.getCardGuid(), rechargeMoneys, storeGuid,
                rechargeGiftActivityList, hsaMemberInfoCard.getGuid());
        vo.setRechargeThresholdList(rechargeThresholdList);
        Long endTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>>>>>>>>>>>>逻辑耗时1：    " + (endTime - startTime));
        // 充值活动说明
        Long startTime2 = System.currentTimeMillis();
        List<RechargeActivityDescVO> rechargeActivityDesc = activityHelper.buildActivityDesc(rechargeGiftActivityList);
        Long endTime2 = System.currentTimeMillis();
        log.info(">>>>>>>>>>>>>>>>>>>>>逻辑耗时2：    " + (endTime2 - startTime2));
        vo.setRechargeActivityDescList(rechargeActivityDesc);

        Long startTime3 = System.currentTimeMillis();
        CardFreezeBalanceAmountQO cardQO = new CardFreezeBalanceAmountQO();
        cardQO.setMemberInfoCardGuid(hsaMemberInfoCard.getGuid());
        cardQO.setIsRefresh(BooleanEnum.FALSE.getCode());
        BigDecimal freezeAmount = hsaCardRechargeGiftDetailService.getMemberCardFreezeAmount(cardQO);
        Long endTime3 = System.currentTimeMillis();
        log.info(">>>>>>>>>>>>>>>>>>>>>逻辑耗时3：    " + (endTime3 - startTime3));
        vo.setBalance(vo.getBalance().add(freezeAmount));
        return vo;
    }

    /**
     * 根据会员卡金额查询预计到账金额
     */
    @Override
    public RechargeThresholdVO calculatePreMoney(MemberCalculatePreMoneyQO preMoneyQO) {
        return activityHelper.calculatePreMoney(preMoneyQO);
    }

    @Override
    public List<HsaCardRechargeGiftDetail> checkGiftAmount(HsaMemberFundingDetail memberFundingDetail,
                                                           HsaMemberInfoCard hsaMemberInfoCard) {
        // 如果没有赠送金额，直接返回空列表
        if (memberFundingDetail.getGiftAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return Collections.emptyList();
        }

        // 查询未刷新的赠送明细
        List<HsaCardRechargeGiftDetail> refreshGiftDetails = hsaCardRechargeGiftDetailService
                .getNoRefreshDetails(memberFundingDetail.getGuid());

        // 如果没有赠送明细，返回空列表
        if (CollUtil.isEmpty(refreshGiftDetails)) {
            return Collections.emptyList();
        }

        // 处理已生效的赠送明细
        processRefreshedGiftDetails(refreshGiftDetails, hsaMemberInfoCard);
        
        // 处理立即生效的赠送明细
        processImmediateGiftDetails(refreshGiftDetails, hsaMemberInfoCard);

        return refreshGiftDetails;
    }
    
    /**
     * 处理已生效的赠送明细
     * 
     * @param refreshGiftDetails 赠送明细列表
     * @param hsaMemberInfoCard 会员卡信息
     */
    private void processRefreshedGiftDetails(List<HsaCardRechargeGiftDetail> refreshGiftDetails, 
                                           HsaMemberInfoCard hsaMemberInfoCard) {
        // 筛选已生效的赠送明细
        List<HsaCardRechargeGiftDetail> refreshedDetails = refreshGiftDetails.stream()
                .filter(item -> Objects.nonNull(item.getIsRefresh()) 
                        && item.getIsRefresh().equals(GiftDetailRefreshEnum.REFRESH.getCode()))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(refreshedDetails)) {
            return;
        }

        // 计算已生效赠送金额总和
        BigDecimal refreshedAmount = refreshedDetails.stream()
                .map(HsaCardRechargeGiftDetail::getMoneyAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 回退赠送金额
        BigDecimal residueGift = hsaMemberInfoCard.getGiftAmount().subtract(refreshedAmount);
        hsaMemberInfoCard.setGiftAmount(residueGift);
        
        log.info("处理已生效赠送明细，回退金额: {}, 剩余赠送金额: {}", refreshedAmount, residueGift);
    }
    
    /**
     * 处理立即生效的赠送明细
     * 
     * @param refreshGiftDetails 赠送明细列表
     * @param hsaMemberInfoCard 会员卡信息
     */
    private void processImmediateGiftDetails(List<HsaCardRechargeGiftDetail> refreshGiftDetails, 
                                           HsaMemberInfoCard hsaMemberInfoCard) {
        // 筛选立即生效的赠送明细（isRefresh为null表示立即生效）
        List<HsaCardRechargeGiftDetail> immediateDetails = refreshGiftDetails.stream()
                .filter(item -> Objects.isNull(item.getIsRefresh()))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(immediateDetails)) {
            return;
        }

        // 计算立即生效赠送金额总和
        BigDecimal immediateAmount = immediateDetails.stream()
                .map(HsaCardRechargeGiftDetail::getMoneyAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 回退赠送金额
        BigDecimal residueGift = hsaMemberInfoCard.getGiftAmount().subtract(immediateAmount);
        hsaMemberInfoCard.setGiftAmount(residueGift);
        
        log.info("处理立即生效赠送明细，回退金额: {}, 剩余赠送金额: {}", immediateAmount, residueGift);
    }

    @Override
    public void setHsaMemberInfoCardValidityDate(HsaMemberInfoCard hsaMemberInfoCard, HsaCardBaseInfo hsaCardBaseInfo, LocalDateTime now) {
        // 设置卡有效性：0-永久有效，1-有限期
        int cardValidity = hsaCardBaseInfo.getCardValidity() == CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_ALWAYS.getCode() 
                ? CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_ALWAYS.getCode() 
                : 1;
        hsaMemberInfoCard.setCardValidity(cardValidity);
        
        // 根据不同的有效期类型设置有效期日期
        setCardValidityDate(hsaMemberInfoCard, hsaCardBaseInfo);
        
        // 设置实体卡状态和激活时间
        hsaMemberInfoCard.setPhysicalCardState(PhysicalCardStateEnum.NORMAL.getCode());
        hsaMemberInfoCard.setPhysicalCardActivationTime(now);
    }
    
    /**
     * 根据卡基础信息设置卡有效期日期
     * 
     * @param hsaMemberInfoCard 会员卡信息
     * @param hsaCardBaseInfo 卡基础信息
     */
    private void setCardValidityDate(HsaMemberInfoCard hsaMemberInfoCard, HsaCardBaseInfo hsaCardBaseInfo) {
        int cardValidityType = hsaCardBaseInfo.getCardValidity();
        
        // 永久有效，无需设置有效期日期
        if (cardValidityType == CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_ALWAYS.getCode()) {
            return;
        }
        
        // 按天数计算有效期
        if (cardValidityType == CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_DAY.getCode()) {
            setValidityDateByUnit(hsaMemberInfoCard, hsaCardBaseInfo);
        } 
        // 按固定日期设置有效期
        else if (cardValidityType == CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_YEAR.getCode()) {
            hsaMemberInfoCard.setCardValidityDate(hsaCardBaseInfo.getCardValidityDate());
        }
    }
    
    /**
     * 根据时间单位设置有效期日期
     * 
     * @param hsaMemberInfoCard 会员卡信息
     * @param hsaCardBaseInfo 卡基础信息
     */
    private void setValidityDateByUnit(HsaMemberInfoCard hsaMemberInfoCard, HsaCardBaseInfo hsaCardBaseInfo) {
        LocalDate currentDate = LocalDate.now();
        Integer validityUnit = hsaCardBaseInfo.getValidityUnit();
        Integer validityTime = hsaCardBaseInfo.getCardValidityTime();
        
        if (validityUnit == null || validityTime == null) {
            log.warn("卡有效期单位或时间为空，cardGuid: {}", hsaCardBaseInfo.getGuid());
            return;
        }
        
        LocalDate validityDate;
        if (DateUnitEnum.DAY.getCode().equals(validityUnit)) {
            validityDate = currentDate.plusDays(validityTime);
        } else if (DateUnitEnum.WEEK.getCode().equals(validityUnit)) {
            validityDate = currentDate.plusWeeks(validityTime);
        } else if (DateUnitEnum.MONTH.getCode().equals(validityUnit)) {
            validityDate = currentDate.plusMonths(validityTime);
        } else if (DateUnitEnum.YEAR.getCode().equals(validityUnit)) {
            validityDate = currentDate.plusYears(validityTime);
        } else {
            log.warn("不支持的时间单位: {}, cardGuid: {}", validityUnit, hsaCardBaseInfo.getGuid());
            return;
        }
        
        hsaMemberInfoCard.setCardValidityDate(validityDate);
    }

    @Override
    public void setBindingMemberCard(BindingMemberCardVO bindingMemberCardVO, HsaMemberInfoCard hsaMemberInfoCard) {
        HsaPhysicalCard hsaPhysicalCard = hsaPhysicalCardMapper.selectOne(new LambdaQueryWrapper<HsaPhysicalCard>()
                .eq(HsaPhysicalCard::getCardNum, hsaMemberInfoCard.getPhysicalCardNum())
                .eq(HsaPhysicalCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
        bindingMemberCardVO
                .setIsBindingMember(hsaPhysicalCard.getMemberBindingState())
                .setCardName(hsaMemberInfoCard.getCardName())
                .setCardGuid(hsaMemberInfoCard.getCardGuid())
                .setCardNum(hsaMemberInfoCard.getPhysicalCardNum())
                .setCardAmount(hsaMemberInfoCard.getCardAmount().add(hsaMemberInfoCard.getGiftAmount()).add(hsaMemberInfoCard.getSubsidyAmount()))
                .setPhoneNum(hsaMemberInfoCard.getMemberPhoneNum())
                .setCardStatus(hsaMemberInfoCard.getPhysicalCardState())
                .setCardValidityDate(hsaMemberInfoCard.getCardValidity() == 0 ? null : hsaMemberInfoCard.getCardValidityDate())
                .setCardValidity(hsaMemberInfoCard.getCardValidity())
                .setMemberInfoCardGuid(hsaMemberInfoCard.getGuid());

        List<StoreCardRuleVO> saveStoreCardRuleQOList = terMemberCardBusinessService.getStoreCardRuleVOS(hsaMemberInfoCard);
        bindingMemberCardVO.setStoreCardRuleVO(saveStoreCardRuleQOList);
    }

    @Override
    public List<PayMemberCardVO> listPayMemberCard(MemberCardQueryDTO dto) {
        List<PayMemberCardVO> memberCardList = memberInfoCardMapper.listPayMemberCard(dto);
        if (CollUtil.isNotEmpty(memberCardList)) {
            //第一张为默认卡
            memberCardList.get(0).setDefaultCard(NumberConstant.NUMBER_1);
            
            // 如果传入了门店GUID列表，需要校验门店适用性
            if (CollUtil.isNotEmpty(dto.getStoreGuidList())) {
                memberCardList = filterCardsByStoreScope(memberCardList, dto.getStoreGuidList());
            }
        }

        return memberCardList;
    }
    
    /**
     * 根据门店范围过滤会员卡
     * 
     * @param memberCardList 会员卡列表
     * @param storeGuidList 门店GUID列表
     * @return 过滤后的会员卡列表
     */
    private List<PayMemberCardVO> filterCardsByStoreScope(List<PayMemberCardVO> memberCardList, List<String> storeGuidList) {
        if (CollUtil.isEmpty(memberCardList)) {
            return memberCardList;
        }
        if (CollUtil.isEmpty(storeGuidList)) {
            return Collections.emptyList();
        }
        
        // 获取部分门店适用的会员卡GUID列表（利用SQL查询中已获取的applicableAllStore字段）
        List<String> partStoreCardGuids = memberCardList.stream()
                .filter(card -> card.getApplicableAllStore() != null && card.getApplicableAllStore() == BooleanEnum.FALSE.getCode())
                .map(PayMemberCardVO::getMemberInfoCardGuid)
                .collect(Collectors.toList());
        
        // 如果没有部分门店适用的会员卡，直接返回原列表
        if (CollUtil.isEmpty(partStoreCardGuids)) {
            return memberCardList;
        }
        
        // 查询部分门店适用的会员卡在指定门店的适用情况
        // 需要统计每个会员卡在多少个门店中有效，只有在所有门店都有效的卡才能通过
        List<HsaStoreCardRule> storeCardRules = hsaStoreCardRuleMapper.selectList(
                new LambdaQueryWrapper<HsaStoreCardRule>()
                        .eq(HsaStoreCardRule::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                        .in(HsaStoreCardRule::getStoreGuid, storeGuidList)
                        .in(HsaStoreCardRule::getMemberInfoCardGuid, partStoreCardGuids)
                        .select(HsaStoreCardRule::getMemberInfoCardGuid, HsaStoreCardRule::getStoreGuid)
        );
        
        // 统计每个会员卡在哪些门店中有效
        Map<String, Set<String>> cardStoreMap = new HashMap<>();
        for (HsaStoreCardRule rule : storeCardRules) {
            String cardGuid = rule.getMemberInfoCardGuid();
            String storeGuid = rule.getStoreGuid();
            if (StringUtils.isNotEmpty(cardGuid) && StringUtils.isNotEmpty(storeGuid)) {
                cardStoreMap.computeIfAbsent(cardGuid, k -> new HashSet<>()).add(storeGuid);
            }
        }
        
        // 只有在所有门店都有效的会员卡才能通过
        Set<String> storeGuidSet = new HashSet<>(storeGuidList);
        Set<String> validPartStoreCardGuids = new HashSet<>();
        for (Map.Entry<String, Set<String>> entry : cardStoreMap.entrySet()) {
            String cardGuid = entry.getKey();
            Set<String> validStores = entry.getValue();
            
            // 检查该会员卡是否在所有要求的门店中都有效
            if (validStores.containsAll(storeGuidSet)) {
                validPartStoreCardGuids.add(cardGuid);
            }
        }
        
        // 过滤会员卡：适用所有门店的卡保留，部分门店适用的卡需要在所有指定门店都有效
        return memberCardList.stream()
                .filter(card -> {
                    Integer applicableAllStore = card.getApplicableAllStore();
                    
                    // 适用于所有门店，直接通过
                    if (applicableAllStore != null && applicableAllStore == BooleanEnum.TRUE.getCode()) {
                        return true;
                    }
                    
                    // 部分门店适用，需要在所有指定门店都有效
                    return validPartStoreCardGuids.contains(card.getMemberInfoCardGuid());
                })
                .collect(Collectors.toList());
    }

    @Override
    public Boolean updatePwd(MemberCardPwdUpdateDTO dto) {
        HsaMemberInfoCard memberInfoCard = memberInfoCardMapper.queryByGuid(dto.getMemberInfoCardGuid());
        if (memberInfoCard == null) {
            throw new MemberBaseException(CardOperationExceptionEnum.MEMBER_CARD_NULL);
        }

        if (!memberInfoCard.getCardPayPassword().equals(SecureUtil.md5(dto.getOldPassword()).toUpperCase())) {
            throw new MemberBaseException(CardOperationExceptionEnum.PASSWORD_ERROR);
        }

        memberInfoCard.setCardPayPassword(SecureUtil.md5(dto.getNewPassword()).toUpperCase());
        return this.updateByGuid(memberInfoCard);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateDefault(MemberCardDefaultUpdateDTO dto) {
        HsaMemberInfoCard memberInfoCard = this.queryByGuid(dto.getMemberInfoCardGuid());
        if (!memberInfoCard.getMemberInfoGuid().equals(dto.getMemberInfoGuid())) {
            return Boolean.FALSE;
        }

        memberInfoCardMapper.updateDefaultCardByMemberGuid(dto.getMemberInfoGuid());
        memberInfoCard.setDefaultCard(DEFAULT_CARD);
        return this.updateByGuid(memberInfoCard);
    }

    @Override
    public Boolean memberCardPwdCheck(MemberCardPwdCheckDTO dto) {
        // 是否需要校验密码
        HsaCardBalanceRule cardBalanceRule = balanceRuleMapper.selectOne(new LambdaQueryWrapper<HsaCardBalanceRule>()
                .eq(HsaCardBalanceRule::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
        // 需要校验密码
        if (cardBalanceRule != null
                && cardBalanceRule.getUseCheck() == NumberConstant.NUMBER_1
                && cardBalanceRule.getAppletsCheckState() == NumberConstant.NUMBER_1) {

            // 校验密码
            HsaMemberInfoCard memberInfoCard = memberInfoCardMapper.queryByGuid(dto.getMemberInfoCardGuid());
            if (memberInfoCard == null) {
                return Boolean.FALSE;
            }

            String cardPayPassword = memberInfoCard.getCardPayPassword();
            if (!dto.getPassword().equals(cardPayPassword)) {
                return Boolean.FALSE;
            }
        }

        return Boolean.TRUE;
    }

    @Override
    public MemberCardQrCodeVO getQrCode(MemberCardInfoQueryDTO dto) {
        MemberCardQrCodeVO cardQrCodeVO = new MemberCardQrCodeVO();

        // 校验密码
        HsaMemberInfoCard memberInfoCard = memberInfoCardMapper.queryByGuid(dto.getMemberInfoCardGuid());
        if (memberInfoCard == null || StrUtil.isEmpty(memberInfoCard.getElectronicCardNum())) {
            return cardQrCodeVO;
        }

        // 二维码
        cardQrCodeVO.setCardQrCode(qrCodeSupport.getQrStr(memberInfoCard.getElectronicCardNum(), QrcodeTypeEnum.MEMBER.getDes()));
        // 条形码
        cardQrCodeVO.setCardBarcode(qrCodeSupport.getBarcode(memberInfoCard.getElectronicCardNum(), QrcodeTypeEnum.MEMBER.getDes()));
        return cardQrCodeVO;
    }

    @Override
    public PageResult getAppletStoreList(AppletStoreQO request) {
        PageResult storePage = new PageResult();
        Integer type = request.getType();
        log.info("type :{}", type);
        switch (AppletStoreTypeEnum.getEnumByCode(type)) {
            case GROWTH_COMMODITY_STORE_TYPE:
                AppletGrowthStoreQO appletGrowthStore = new AppletGrowthStoreQO();
                BeanUtils.copyProperties(request, appletGrowthStore);
                storePage = appletGrowthService.listGrowthStoreByStrategyId(appletGrowthStore);
                break;
            case GROWTH_STORE_TYPE:
                AppletGrowthStorePageQO appletGrowthStoreQo = new AppletGrowthStorePageQO();
                BeanUtils.copyProperties(request, appletGrowthStoreQo);
                storePage = appletGrowthService.listGrowthValueStore(appletGrowthStoreQo);
                break;
            case MEMBER_CARD_STORE_TYPE:
                MemberCardStoreDataQO cardStoreData = new MemberCardStoreDataQO();
                BeanUtils.copyProperties(request, cardStoreData);
                storePage = this.findStoreCardRule(cardStoreData);
                break;
            default:
                break;
        }
        return storePage;
    }

    @Override
    public List<HsaMemberInfoCard> getMemberInfoCardList(List<String> memberInfoGuids, String cardGuid) {
        return memberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .in(HsaMemberInfoCard::getMemberInfoGuid, memberInfoGuids)
                .eq(HsaMemberInfoCard::getCardGuid, cardGuid));
    }

    @Override
    public List<HsaMemberInfoCard> getMemberInfoCards(List<String> memberInfoGuids, List<String> cardGuids) {
        return memberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .in(HsaMemberInfoCard::getMemberInfoGuid, memberInfoGuids)
                .in(HsaMemberInfoCard::getCardGuid, cardGuids));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdate(List<HsaMemberInfoCard> activityList) {
        int rows = memberInfoCardMapper.batchUpdateValue(activityList);
        log.info("批量更新了：{} 张会员卡信息", rows);
    }

    @Override
    public void subsidyRecycling(List<SubsidyRecyclingQO> activityList) {
        int rows = memberInfoCardMapper.subsidyRecycling(activityList);
        log.info("批量更新了：{} 张会员卡信息", rows);
    }

    @Override
    public void batchUpdateCard(List<HsaMemberInfoCard> activityList) {
        int rows = memberInfoCardMapper.batchUpdate(activityList);
        log.info("批量更新了：{} 张会员卡信息", rows);
    }

    @Override
    public Boolean updateMemberCard(HsaMemberInfoCard hsaMemberInfoCard) {
        return memberInfoCardMapper.updateByGuid(hsaMemberInfoCard);
    }

    @Override
    public PageResult getAppletStoreListByIds(AppletStoreListQO request) {
        List<String> storeIds = request.getStoreIds();
        List<StoreBaseInfo> storeBaseInfos = storeBaseService.appletGetStoreInfo();
        //如果门店ids不为空，查询部分门店，否则查询所有门店
        if (!CollUtil.isEmpty(storeIds)) {
            if (CollUtil.isEmpty(storeBaseInfos)) {
                return PageUtil.getPageResult(new PageInfo<>(Collections.emptyList()));
            }
            storeBaseInfos = storeBaseInfos.stream().filter(x -> Objects.nonNull(x.getId()) && storeIds.contains(x.getId()))
                    .collect(Collectors.toList());
        }
        //门店根据经纬度，计算距离
        storeBaseInfos = calculateDistance(storeBaseInfos, request.getLongitude(), request.getLatitude());
        return PageUtil.getPageResult(new PageInfo<>(toAppletStoreList(storeBaseInfos)));
    }

    @Override
    public PageResult getMemberCouponStoreList(MemberCouponStoreListDTO dto) {
        List<MemberCouponStoreDTO> stores = dto.getStores();
        if (CollUtil.isEmpty(stores)) {
            return new PageResult<>(dto.getCurrentPage(), dto.getPageSize(), 0).setRecords(Collections.emptyList());
        }

        Map<String, List<MemberCouponStoreDTO>> systemMap = stores.stream().collect(Collectors.groupingBy(MemberCouponStoreDTO::getSystem));
        List<StoreBaseInfo> allStoreList = new ArrayList<>();
        for (Map.Entry<String, List<MemberCouponStoreDTO>> entry : systemMap.entrySet()) {
            String systemName = entry.getKey();
            List<String> storeIds = entry.getValue().stream().map(MemberCouponStoreDTO::getStoreGuid).collect(Collectors.toList());
            List<StoreBaseInfo> storeList = externalSupport.storeServer(SystemEnum.getSystemCodeBySystemName(systemName))
                    .listStoreAndStall(new QueryStoreBasePage().setStoreIds(storeIds));
            if (CollUtil.isNotEmpty(storeList)) {
                storeList = storeList.stream().filter(x -> storeIds.contains(x.getStoreGuid())).collect(Collectors.toList());
                allStoreList.addAll(storeList);
            }
        }

        // 关键字过滤
        if (StrUtil.isNotBlank(dto.getKeywords())) {
            allStoreList = allStoreList.stream()
                    .filter(x -> StrUtil.contains(x.getStoreName(), dto.getKeywords()))
                    .collect(Collectors.toList());
        }

        if (CollUtil.isEmpty(allStoreList)) {
            return new PageResult<>(dto.getCurrentPage(), dto.getPageSize(), 0).setRecords(Collections.emptyList());
        }

        // 距离排序和id排序
        allStoreList = calculateDistance(allStoreList, dto.getLongitude(), dto.getLatitude());
        allStoreList = allStoreList.stream()
                .sorted(Comparator.comparing((StoreBaseInfo s) -> {
                    BigDecimal distance = s.getDistance();
                    return (distance == null || distance.compareTo(BigDecimal.valueOf(-1)) == 0) ? BigDecimal.valueOf(Long.MAX_VALUE) : distance;
                }).thenComparing(StoreBaseInfo::getId))
                .collect(Collectors.toList());

        // 手动分页
        int total = allStoreList.size();
        int current = dto.getCurrentPage() == null ? 1 : dto.getCurrentPage();
        int size = dto.getPageSize() == null ? 20 : dto.getPageSize();
        int fromIndex = Math.max((current - 1) * size, 0);
        int toIndex = Math.min(fromIndex + size, total);
        List<StoreBaseInfo> pageList = fromIndex < toIndex ? allStoreList.subList(fromIndex, toIndex) : Collections.emptyList();

        PageResult<StoreBaseInfo> pageResult = new PageResult<>(current, size, total);
        pageResult.setRecords(pageList);
        return pageResult;
    }

    private List<AppletStoreVO> toAppletStoreList(List<StoreBaseInfo> storeBaseInfos) {
        if (CollUtil.isEmpty(storeBaseInfos)) {
            return Collections.emptyList();
        }
        List<AppletStoreVO> list = Lists.newArrayList();
        storeBaseInfos.forEach(s -> {
            AppletStoreVO appletStore = new AppletStoreVO();
            appletStore.setStoreLogo(s.getStoreLogo());
            appletStore.setStoreName(s.getStoreName());
            appletStore.setStoreNumber(s.getStoreNumber());
            appletStore.setTime(s.getTime());
            appletStore.setAddress(s.getAddress());
            appletStore.setDistance(s.getDistance());
            appletStore.setId(s.getId());
            list.add(appletStore);
        });
        return list;
    }


    @Override
    public MiniProgramCardDTO getQrCodeOpenCardDetail(String cardGuid, String memberGuid) {
        MiniProgramCardDTO dto = memberInfoCardMapper.findQrCodeOpenCardDetail(SEND_CHANNEL, cardGuid);
        if (Objects.isNull(dto)) {
            throw new MemberBaseException(systemRoleHelper.getReplace(CardOperationExceptionEnum.MEMBER_CARD_CLOSE_OPEN,
                    ThreadLocalCache.getOperSubjectGuid()));
        }
        setMoney(dto);
        HsaMemberInfoCard hsaMemberInfoCard = memberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getCardGuid, cardGuid)
                .eq(HsaMemberInfoCard::getMemberInfoGuid, memberGuid)
                .isNotNull(HsaMemberInfoCard::getElectronicCardGuid));
        if (Objects.nonNull(hsaMemberInfoCard)) {
            dto.setOwnGuid(hsaMemberInfoCard.getGuid());
            dto.setApplicableAllStore(hsaMemberInfoCard.getApplicableAllStore());
            dto.setStoreNum(hsaStoreCardRuleMapper.selectCount(new LambdaQueryWrapper<HsaStoreCardRule>().
                    eq(HsaStoreCardRule::getCardGuid, cardGuid)
                    .eq(HsaStoreCardRule::getMemberInfoCardGuid, hsaMemberInfoCard.getGuid())
                    .isNull(HsaStoreCardRule::getParentGuid)));
        } else {
            HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(cardGuid);
            dto.setApplicableAllStore(hsaCardBaseInfo.getApplicableAllStore());
            dto.setStoreNum(hsaStoreCardRuleMapper.selectCount(new LambdaQueryWrapper<HsaStoreCardRule>().
                    eq(HsaStoreCardRule::getCardGuid, cardGuid)
                    .isNull(HsaStoreCardRule::getMemberInfoCardGuid)
                    .isNull(HsaStoreCardRule::getParentGuid)));
        }

        HsaCardOpenRule hsaCardOpenRule = hsaCardOpenRuleMapper.selectOne(new LambdaQueryWrapper<HsaCardOpenRule>()
                .eq(HsaCardOpenRule::getCardGuid, cardGuid));
        dto.setIsSupportElectronicCard(hsaCardOpenRule.getIsSupportElectronicCard());

        //会员卡权益
        List<GradeEquitiesVO> gradeEquitiesVOList = new ArrayList<>();
        hsaMemberGradeInfoService.
                addGradeEquities(cardGuid, gradeEquitiesVOList, memberGuid, BusinessTypeEnum.CARD_EQUITIES.getCode());

        dto.setGradeEquitiesVOList(gradeEquitiesVOList);
        return dto;
    }

    /**
     * 查询会员及拥有卡详情
     *
     * @param request data：会员手机号/会员实体卡卡号
     * @return 会员信息，卡信息列表
     */
    @Override
    public MemberInfoOwnCardVO queryMemberInfoOwnCard(SingleDataQO request) {
        VerifyUtil.isTure(ObjectUtil.isNull(request) || ObjectUtil.isNull(request.getData())
                || ObjectUtil.isNull(request.getStoreGuid())).trueHandle(() -> {
            throw new MemberBaseException(CardOperationExceptionEnum.CREATE_PHYSICAL_CARD_SECRET_PARAMS_NULL);
        });
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();

        // 11位手机号/12位卡号
        String keyWord = request.getData();
        HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();
        if (MEMBER_PHONE_NUM == keyWord.length()) {
            VerifyUtil.verify(NumberUtil.isPhoneNum11(keyWord), MemberAccountExceptionEnum.ERROR_MEMBER_PHONE);
            hsaOperationMemberInfo = hsaOperationMemberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                    .eq(HsaOperationMemberInfo::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid())
                    .eq(HsaOperationMemberInfo::getIsDelete, BooleanEnum.FALSE.getCode())
                    .eq(HsaOperationMemberInfo::getPhoneNum, keyWord)
            );
            VerifyUtil.verify(!ObjectUtils.isEmpty(hsaOperationMemberInfo), MemberAccountExceptionEnum.ERROR_NOT_EXIST_MEMBER);
        } else if (MEMBER_CARD_NUM == keyWord.length()) {
            HsaPhysicalCard physicalCard = hsaPhysicalCardMapper.selectOne(new LambdaQueryWrapper<HsaPhysicalCard>()
                    .eq(HsaPhysicalCard::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid())
                    .eq(HsaPhysicalCard::getCardNum, keyWord)
                    .eq(HsaPhysicalCard::getIsDelete, 0)
            );
            VerifyUtil.isTure(ObjectUtils.isEmpty(physicalCard)).trueHandle(() -> {
                throw new MemberBaseException(systemRoleHelper.getReplace(CardOperationExceptionEnum.MEMBER_CREDIT_NO,
                        ThreadLocalCache.getOperSubjectGuid()));
            });
            HsaMemberInfoCard memberInfoCard = memberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                    .eq(HsaMemberInfoCard::getPhysicalCardGuid, physicalCard.getGuid())
                    .eq(HsaMemberInfoCard::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid())
            );
            if (!ObjectUtils.isEmpty(memberInfoCard)) {
                hsaOperationMemberInfo = hsaOperationMemberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                        .eq(HsaOperationMemberInfo::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid())
                        .eq(HsaOperationMemberInfo::getIsDelete, BooleanEnum.FALSE.getCode())
                        .eq(HsaOperationMemberInfo::getGuid, memberInfoCard.getMemberInfoGuid())
                );
            }
            // 未查询到会员信息一律当成不记名卡处理
            if (ObjectUtils.isEmpty(memberInfoCard)
                    || ObjectUtils.isEmpty(hsaOperationMemberInfo)
                    || StringUtils.isEmpty(hsaOperationMemberInfo.getGuid())) {
                return handleNoNameCard(headerUserInfo, physicalCard);
            }
        } else {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_EXPORT_INPUT);
        }

        // 查询会员信息
        VerifyUtil.verify(!ObjectUtils.isEmpty(hsaOperationMemberInfo), MemberAccountExceptionEnum.ERROR_USER_NOT_REGISTERED);

        VerifyUtil.verify(hsaOperationMemberInfo.getAccountState() != 1, systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_ACCOUNT_IS_DISABLED, ThreadLocalCache.getOperSubjectGuid()));
        String memberInfoGuid = hsaOperationMemberInfo.getGuid();
        MemberInfoOwnCardVO vo = MemberInfoTransform.INSTANCE.memberInfo2InfoOwnCardVO(hsaOperationMemberInfo);

        // 查询会员标签
        List<MemberLabelVO> list = hsaMemberLabelMapper.listMemberLabel(memberInfoGuid);
        vo.setLabelList(list);

        // 查询已拥有卡列表
        OwnCardQO ownCardQO = new OwnCardQO();
        ownCardQO.setMemberInfoGuid(memberInfoGuid);
        ownCardQO.setPageSize(999);
        List<OwnCardVO> ownCardVOList = getOwnCardVOList(ownCardQO);
        log.info("查询会员有的卡,memberInfoGuid={},ownCardVOList={}", memberInfoGuid, JacksonUtils.writeValueAsString(ownCardVOList));
        if (CollectionUtils.isEmpty(ownCardVOList)) {
            vo.setOwnCardList(ownCardVOList);
            return vo;
        }
        Map<String, List<StoreCardRuleQO>> storeCardMap = ownCardVOList.stream()
                .filter(c -> BooleanEnum.FALSE.getCode() == c.getApplicableAllStore())
                .flatMap(c -> c.getStoreList().stream())
                .collect(Collectors.groupingBy(StoreCardRuleQO::getCardGuid));
        // 过滤卡状态
        ownCardVOList.removeIf(c -> ObjectUtils.isEmpty(c.getOwnPhysicalCard()));
        // 最新需求不校验卡状态
        ownCardVOList.removeIf(c -> BooleanEnum.FALSE.getCode() == c.getOwnPhysicalCard().getIsPhysicalCardRetreat());
        // 过滤关联门店
        ownCardVOList.removeIf(c -> BooleanEnum.FALSE.getCode() == c.getApplicableAllStore() &&
                (ObjectUtils.isEmpty(storeCardMap.get(c.getCardGuid())) ||
                        !storeCardMap.get(c.getCardGuid()).stream()
                                .map(StoreCardRuleQO::getStoreGuid)
                                .collect(Collectors.toSet())
                                .contains(request.getStoreGuid()))
        );
        vo.setOwnCardList(ownCardVOList);
        return vo;
    }

    private MemberInfoOwnCardVO handleNoNameCard(HeaderUserInfo headerUserInfo, HsaPhysicalCard physicalCard) {
        MemberInfoOwnCardVO onceVO = new MemberInfoOwnCardVO();
        onceVO.setPhoneNum(physicalCard.getPhoneNum());
        onceVO.setUserName(physicalCard.getUserName());
        onceVO.setAccountState(0);
        onceVO.setMemberInfoGuid("");
        onceVO.setLabelList(Lists.newArrayList());
        List<OwnCardVO> ownCardList = new ArrayList<>();
        HsaMemberInfoCard memberInfoCard = memberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getPhysicalCardGuid, physicalCard.getGuid()));

        OwnCardVO onceCardVO = getOwnCardVO(headerUserInfo, physicalCard, memberInfoCard);

        ownCardList.add(onceCardVO);
        onceVO.setOwnCardList(ownCardList);
        return onceVO;
    }

    private OwnCardVO getOwnCardVO(HeaderUserInfo headerUserInfo, HsaPhysicalCard physicalCard, HsaMemberInfoCard memberInfoCard) {
        OwnCardVO onceCardVO = new OwnCardVO();
        onceCardVO.setOwnGuid(null);
        onceCardVO.setCardGuid(memberInfoCard.getCardGuid());
        onceCardVO.setCardName(memberInfoCard.getCardName());
        onceCardVO.setCardBalance(memberInfoCard.getCardAmount().add(memberInfoCard.getGiftAmount()).add(memberInfoCard.getSubsidyAmount()));
        onceCardVO.setApplicableAllStore(memberInfoCard.getApplicableAllStore());
        onceCardVO.setPeriodOfValidity("永久有效");
        if (0 != memberInfoCard.getCardValidity()) {
            onceCardVO.setPeriodOfValidity(LocalDateTime.now() + "至" + memberInfoCard.getCardValidityDate());
        }
        onceCardVO.setOwnElectronicCard(new OwnElectronicCardDTO());
        OwnPhysicalCardDTO physicalCardDTO = memberInfoCardMapper.getPhysicalCard(physicalCard.getGuid());
        if (ObjectUtils.isEmpty(physicalCardDTO)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_CARD_NOT_EXIST);
        }
        // 最新需求不校验卡状态
//        checkCardState(physicalCardDTO.getCardState());
        if (BooleanEnum.FALSE.getCode() == physicalCardDTO.getIsPhysicalCardRetreat()) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_CARD_NOT_RETURN, ThreadLocalCache.getOperSubjectGuid()));
        }
        onceCardVO.setOwnPhysicalCard(physicalCardDTO);
        HsaCardOpenRule cardOpenRule = hsaCardOpenRuleMapper.selectOne(new LambdaQueryWrapper<HsaCardOpenRule>()
                .eq(HsaCardOpenRule::getCardGuid, memberInfoCard.getCardGuid()));
        if (!ObjectUtils.isEmpty(cardOpenRule)) {
            onceCardVO.setIsSupportElectronicCard(cardOpenRule.getIsSupportElectronicCard());
            onceCardVO.setIsSupportPhysicalCard(cardOpenRule.getIsSupportPhysicalCard());
        }
        onceCardVO.setSubsidyAmount(memberInfoCard.getSubsidyAmount());
        onceCardVO.setRetreatSubsidyAmount(memberInfoCard.getRetreatSubsidyAmount());
        onceCardVO.setCardAmount(memberInfoCard.getCardAmount());
        onceCardVO.setGiftAmount(memberInfoCard.getGiftAmount());
        onceCardVO.setMemberInfoCardGuid(memberInfoCard.getGuid());
        if (memberInfoCard.getApplicableAllStore() == BooleanEnum.FALSE.getCode()) {
            List<String> cardList = new ArrayList<>();
            cardList.add(memberInfoCard.getCardGuid());
            List<StoreCardRuleQO> storeList = getStoreCardRule(cardList, memberInfoCard.getGuid());
            if (CollectionUtils.isEmpty(storeList)) {
                throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_CARD_NOT_APPLY, ThreadLocalCache.getOperSubjectGuid()));
            }
            Set<String> storeGuidList = storeList.stream()
                    .map(StoreCardRuleQO::getStoreGuid)
                    .collect(Collectors.toSet());
            if (!storeGuidList.contains(headerUserInfo.getStoreGuid())) {
                throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_CARD_NOT_APPLY, ThreadLocalCache.getOperSubjectGuid()));
            }
            onceCardVO.setStoreList(storeList);
        }
        onceCardVO.setRefundCardRule(0);

        //冻结金额
        CardFreezeBalanceAmountQO balanceAmountQO = new CardFreezeBalanceAmountQO();
        balanceAmountQO.setMemberInfoCardGuid(onceCardVO.getMemberInfoCardGuid());
        BigDecimal freezeAmount = hsaCardRechargeGiftDetailService.getMemberCardFreezeAmount(balanceAmountQO);
        onceCardVO.setGiftAmount(onceCardVO.getGiftAmount().add(freezeAmount));
        onceCardVO.setCardBalance(onceCardVO.getCardBalance().add(freezeAmount));
        return onceCardVO;
    }

    private void updateMemberCardBalanceAfterHandler(UpdateMemberCardBalanceQO memberCardBalanceVO,
                                                     List<HsaMemberInfoCard> hsaMemberInfoCardList,
                                                     List<HsaMemberFundingDetail> hsaMemberFundingDetailList) {
        Map<String, HsaMemberInfoCard> memberInfoCardMap = hsaMemberInfoCardList.stream()
                .collect(Collectors.toMap(HsaMemberInfoCard::getGuid, Function.identity(), (entity1, entity2) -> entity1));
        HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(hsaMemberInfoCardList.get(0).getMemberInfoGuid());
        // 短信发送
        sendChangeBalanceShortMessage(memberCardBalanceVO, hsaOperationMemberInfo, memberInfoCardMap);
        // 微信消息通知
        sendChangeBalanceWechatMessage(hsaOperationMemberInfo, memberInfoCardMap, hsaMemberFundingDetailList);
    }

    private void sendChangeBalanceShortMessage(UpdateMemberCardBalanceQO memberCardBalanceVO,
                                               HsaOperationMemberInfo hsaOperationMemberInfo,
                                               Map<String, HsaMemberInfoCard> memberInfoCardMap) {
        List<MessagesSendQO> messagesSendList = Lists.newArrayList();
        BigDecimal rechargeAmount = Optional.ofNullable(memberCardBalanceVO.getRechargeAmount()).orElse(BigDecimal.ZERO);
        BigDecimal giftAmount = Optional.ofNullable(memberCardBalanceVO.getGiftAmount()).orElse(BigDecimal.ZERO);
        BigDecimal subsidyAmount = Optional.ofNullable(memberCardBalanceVO.getSubsidyAmount()).orElse(BigDecimal.ZERO);
        BigDecimal totalAmount = rechargeAmount.add(giftAmount).add(subsidyAmount);
        for (String memberCardGuid : memberCardBalanceVO.getMemberCardGuid()) {
            MessagesSendQO messagesSendQO = new MessagesSendQO();
            messagesSendQO.setMemberInfoCardGuid(memberCardGuid);
            messagesSendQO.setMemberName(hsaOperationMemberInfo.getUserName());
            messagesSendQO.setAmountFundingType(memberCardBalanceVO.getAmountFundingType());
            messagesSendQO.setAmountSourceType(AmountSourceTypeEnum.ADMIN_EDIT.getCode());
            messagesSendQO.setChangeAmount(totalAmount);
            if (memberInfoCardMap.containsKey(memberCardGuid)) {
                HsaMemberInfoCard hsaMemberInfoCard = memberInfoCardMap.get(memberCardGuid);
                messagesSendQO.setCardBalance(hsaMemberInfoCard.getCardAmount().add(hsaMemberInfoCard.getGiftAmount()).add(hsaMemberInfoCard.getSubsidyAmount()));
            }
            messagesSendQO.setPhone(hsaOperationMemberInfo.getPhoneNum());
            messagesSendQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            messagesSendQO.setEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid());
            messagesSendList.add(messagesSendQO);
        }
        //发送短信消息
        sendService.adminEditSendBatch(messagesSendList);
    }

    private void sendChangeBalanceWechatMessage(HsaOperationMemberInfo hsaOperationMemberInfo,
                                                Map<String, HsaMemberInfoCard> memberInfoCardMap,
                                                List<HsaMemberFundingDetail> hsaMemberFundingDetailList) {
        Map<String, HsaMemberFundingDetail> memberFundingDetailMap = hsaMemberFundingDetailList.stream()
                .collect(Collectors.toMap(HsaMemberFundingDetail::getMemberInfoCardGuid, Function.identity(),
                        (entity1, entity2) -> entity1));
        for (Map.Entry<String, HsaMemberInfoCard> entry : memberInfoCardMap.entrySet()) {
            HsaMemberInfoCard memberInfoCard = entry.getValue();
            HsaMemberFundingDetail hsaMemberFundingDetail = memberFundingDetailMap.get(entry.getKey());

            HsaMemberConsumption memberConsumption = new HsaMemberConsumption();
            memberConsumption.setConsumptionType(ConsumptionTypeEnum.TYPE_RECHARGE.getCode());
            memberConsumption.setIsCancel(BooleanEnum.FALSE.getCode());
            memberConsumption.setMemberInfoCardGuid(entry.getKey());
            try {
                cardInfoService.sendChangeCardAmountMsg(memberInfoCard, memberConsumption, hsaOperationMemberInfo, hsaMemberFundingDetail);
            } catch (Exception e) {
                log.error("sendChangeCardAmountMsg error, ", e);
            }
        }
    }

}
