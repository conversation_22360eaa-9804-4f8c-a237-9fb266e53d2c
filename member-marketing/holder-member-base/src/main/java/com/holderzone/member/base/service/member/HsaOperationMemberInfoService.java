package com.holderzone.member.base.service.member;

import com.holderzone.member.base.dto.MemberUploadExcelVO;
import com.holderzone.member.base.entity.card.HsaCardOpenRule;
import com.holderzone.member.base.entity.growth.HsaGrowthValueTask;
import com.holderzone.member.base.entity.integral.HsaIntegralDetail;
import com.holderzone.member.base.entity.integral.HsaIntegralTask;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.dto.ali.AliSynMemberDTO;
import com.holderzone.member.common.dto.business.MemberSeqFiledDTO;
import com.holderzone.member.common.dto.member.MemberAddDTO;
import com.holderzone.member.common.dto.member.MemberQueryDTO;
import com.holderzone.member.common.dto.member.MemberUpdateDTO;
import com.holderzone.member.common.dto.member.MemberWechatDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.partner.PartnerReserveMemberInfoDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.qo.integral.UpdateIntegralQO;
import com.holderzone.member.common.qo.member.*;
import com.holderzone.member.common.vo.grade.MemberGradeInfoBaseVO;
import com.holderzone.member.common.vo.member.*;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;


/**
 * <p>
 * 运营主体会员信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-12
 */
public interface HsaOperationMemberInfoService extends IHolderBaseService<HsaOperationMemberInfo> {

    String saveMemberInfo(SaveOperationMemberInfoQO request);

    /**
     * 保存
     *
     * @param request request model
     * @return boolean
     */
    String saveOperationMemberInfo(SaveOperationMemberInfoQO request);


    String saveOperationMemberInfoImpl(SaveOperationMemberInfoQO request);


    /**
     * 推送至老门店
     */
    void synSaasMember(AliSynMemberDTO aliSynMemberDTO, String openId);

    /**
     * 查询关联餐饮云会员信息
     */
    ResponseMemberInfo getSaasMember(String phone, String openId);

    HsaIntegralDetail dealIntegralTask(HsaOperationMemberInfo operationMemberInfo, HeaderUserInfo headerUserInfo,
                                       HsaIntegralTask hsaIntegralTask, Integer sourceType);

    /**
     * 调整积分
     *
     * @param request 调整积分请求参数
     * @return 操作结果
     */
    Boolean updateMemberIntegral(UpdateIntegralQO request);

    /**
     * 查询会员列表 一体机 人脸筛查
     *
     * @param memberListQO 查询会员列表QO
     * @return 会员列表返回结果
     */
    PageResult<TerFaceMemberInfoVO> terListMemberInfo(TerFaceMemberListQO memberListQO);

    /**
     * 人脸绑定
     *
     * @param request
     */
    boolean memberFaceRegistered(MemberHumanFace request);


    /**
     * 人脸清除
     *
     * @param request
     */
    boolean memberFaceClear(MemberHumanFace request);


    /**
     * 人脸绑定 一体机使用
     *
     * @param request
     */
    boolean terMemberFaceRegistered(MemberHumanFace request);


    /**
     * 更新
     *
     * @param request request model
     * @return boolean
     */
    boolean updateByMemberGuid(UpdateOperationMemberInfoQO request);

    /**
     * 会员导入
     *
     * @param fileUrl 会员信息url地址
     */
    MemberUploadExcelVO sendMemberUploadExcelUrl(String fileUrl, String fileName);

    MemberUploadExcelVO memberUploadExcelUrl(String fileUrl, String fileName);


    /**
     * 通过会员guid查询，会员信息详情
     *
     * @param guid
     */
    OperationMemberDetailVO getOperationMemberDetail(String guid);

    /**
     * 修改会员密码
     *
     * @param request 修改的会员密码
     * @return 操作结果
     */
    boolean updateOperationMemberPassword(OperationMemberPasswordQO request);

    /**
     * 修改会员账户密码
     *
     * @param request 修改会员账户密码
     * @return 操作结果
     */
    boolean updateMemberAccountPassword(OperationMemberPasswordQO request);

    /**
     * 修改会员账户状态(启用或者禁用)
     *
     * @param request 修改的会员状态信息
     * @return 操作结果
     */
    boolean updateOperationMemberState(OperationMemberStateQO request);

    /**
     * 更变会员绑定的门店
     *
     * @param request 修改的会员门店信息
     */
    boolean updateOperationMemberStore(OperationMemberStoreQO request);

    /**
     * 会员解除门店
     *
     * @param guid 会员guid
     */
    int relieveOperationMemberStore(String guid);

    /**
     * 修改会员手机号码
     *
     * @param request 修改的会员手机信息
     * @return 操作结果
     */
    int updateOperationMemberPhone(OperationMemberPhoneQO request);


    /**
     * 查询会员列表
     *
     * @param memberListQO 查询会员列表QO
     * @return 会员列表返回结果
     */
    PageResult listMemberInfo(MemberListQO memberListQO);

    /**
     * 查询会员列表
     */
    List<MemberInfoVO> listMemberInfoByGuids(MemberListQO query);

    /**
     * 查询会员列表
     */
    PageResult<MemberInfoVO> listMemberInfoByPhone(MemberListQO query);

    /**
     * 校验注册开卡逻辑
     *
     * @param hsaCardOpenRule
     * @param sourceType
     * @return
     */
    Boolean checkOpenCard(HsaCardOpenRule hsaCardOpenRule, Integer sourceType, Set<String> hsaMemberLabelList);

    /**
     * 批量调整会员成长值
     *
     * @param request request
     * @return
     */
    Boolean updateMemberGrowth(RequestMemberGrowthValue request);

    /**
     * 成长值基础任务处理
     *
     * @param operationMemberInfos operationMemberInfos
     * @param sourceType           sourceType
     * @param businessType         businessType
     */
    HsaGrowthValueTask sendMemberGrowthValue(List<HsaOperationMemberInfo> operationMemberInfos, Integer sourceType,
                                             Integer businessType, HeaderUserInfo headerUserInfo);

    /**
     * 实时计算会员等级
     *
     * @param growthValue
     * @return
     */
    MemberGradeInfoBaseVO calculateExtraAmount(Integer growthValue);

    /**
     * 获取等级账户列表
     *
     * @param request 筛选条件
     * @return 等级账户列表
     */
    PageResult getAccountList(GradeAccountQO request);

    /**
     * 导出等级账户列表信息
     *
     * @param request  筛选条件
     * @param response 响应对象
     */
    void accountExport(GradeAccountQO request, HttpServletResponse response);

    void initMemberGrade();

    /**
     * 通过会员guid查询当前会员等级的权益记录列表
     *
     * @param memberInfoGuid 会员guid
     * @return 权益记录列表
     */
    List<GradeEquitiesRecordVO> getGradeEquitiesRecordList(String memberInfoGuid);


    /**
     * 通过会员guid以及运营主体查询会员列表
     *
     * @param guids           guids
     * @param operSubjectGuid 运营主体
     * @return 会员列表
     */
    List<HsaOperationMemberInfo> getOperationMemberInfoList(List<String> guids, String operSubjectGuid);

    /**
     * 查看会员手机相关信息列表
     *
     * @param guids           会员guids
     * @param operSubjectGuid 运营主体
     * @return 操作结果
     */
    List<MemberPhoneVO> getMemberPhoneList(List<String> guids, String operSubjectGuid);

    /**
     * 通过运营主体查询会员列表
     *
     * @param operSubjectGuid 运营主体
     * @return 操作结果
     */
    List<HsaOperationMemberInfo> getMemberListBySubject(@RequestParam("operSubjectGuid") String operSubjectGuid);

    /**
     * 通过会员guid查询会员列表
     *
     * @param guids 会员guids
     * @return 操作结果
     */
    List<HsaOperationMemberInfo> getMemberListByGuids(List<String> guids);

    /**
     * 通过关键字查询会员guid
     *
     * @param keywords 关键字
     * @return 操作结果
     */
    List<String> getMemberInfoGuids(String keywords, String operSubjectGuid);

    /**
     * 获取当前运营主体下所有用户guid
     *
     * @param operSubjectGuid 运营主体
     * @return 用户guid集合
     */
    List<String> getMemberGuidBySubject(String operSubjectGuid);

    /**
     * 获取会员积分商品抵扣状态
     *
     * @param memberInfoGuid 会员guid
     * @return 抵扣状态
     */
    Integer getIntegralDeduction(String memberInfoGuid);

    void initializeGrowthValue(MultipartFile file, String operSubjectGuid) throws IOException;

    /**
     * 根据字段编号更新会员基础信息
     *
     * @param seqFiled 更新数据以及字段编号
     */
    void updateBySeqFiled(MemberSeqFiledDTO seqFiled);

    String findMemberNameByGuid(String memberGuid);

    /**
     * 增加角色类型
     *
     * @param phoneNum 手机号
     * @param roleType 角色类型
     */
    void addRoleType(String phoneNum, String roleType);

    /**
     * 删除角色类型
     *
     * @param phoneNum 手机号
     * @param roleType 角色类型
     */
    void deleteRoleType(String phoneNum, String roleType);

    List<HsaOperationMemberInfo> listByPhones(String operSubjectGuid, List<String> phoneNums);

    List<PartnerReserveMemberInfoDTO> queryMemberByGuidList(List<String> memberGuidList);

    List<PartnerReserveMemberInfoDTO> queryMemberByPhoneList(List<String> phoneList);

    void updateConsumptionTime(String memberGuid, LocalDateTime now);

    Boolean initMemberPinyin(List<String> operSubjectGuid);

    /**
     * 查询会员信息以及亲属人脸
     * @param acquireMemberFaceQO acquireMemberFaceQO
     * @return AcquireMemberFaceVO
     */
    AcquireMemberFaceVO getAcquireMemberFace(AcquireMemberFaceQO acquireMemberFaceQO);


    Boolean checkMemberAccount(String memberAccount);

    /**
     * 获取会员信息
     *
     * @param queryDTO 手机号
     * @return 会员信息
     */
    MemberBasicInfoVO getMemberInfo(MemberQueryDTO queryDTO);

    /**
     * 获取会员信息
     *
     * @param queryDTO openId
     * @return 会员信息
     */
    MemberBasicInfoVO getMemberByOpenid(MemberQueryDTO queryDTO);


    /**
     * 获取会员账户信息
     *
     * @param queryDTO 查询参数--主要用到guid
     * @return 会员信息
     */
    MemberAccountInfoVO getMemberAccountInfo(MemberQueryDTO queryDTO);

    /**
     * 新增会员信息
     *
     * @param addDTO 请求参数
     * @return 会员信息
     */
    MemberBasicInfoVO addMemberInfo(MemberAddDTO addDTO);

    /**
     * 更新会员信息
     *
     * @param updateDTO 请求参数
     */
    void updateMemberInfo(MemberUpdateDTO updateDTO);

    /**
     * 更新会员付费等级信息
     * @param memberInfoGuid 会员id
     * @param memberPaidGradeGuid 会员等级id
     * @param memberPaidGradeName 会员等级名称
     */
    void updatePaidGrade (String memberInfoGuid, String memberPaidGradeGuid, String memberPaidGradeName);

    int isDoubleGrowthValue(Integer growthValue, HsaOperationMemberInfo hsaOperationMemberInfo, HsaIntegralDetail hsaIntegralDetail);

    /**
     * 获取会员列表（按等级类型：付费或免费）
     *
     * @param gradeType 等级类型 1:付费会员 0:免费会员
     * @return 会员列表
     */
    List<MemberBasicInfoVO> listMembersByGradeType(Integer gradeType);

    /**
     * 批量获取会员基础信息
     * @param queryDTO 查询参数
     * @return 会员信息
     */
    List<MemberBasicInfoVO> batchGetMemberInfo (MemberQueryDTO queryDTO);
}
