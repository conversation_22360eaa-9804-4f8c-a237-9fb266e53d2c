package com.holderzone.member.base.dto;

import com.holderzone.member.base.entity.grade.HsaBusinessEquities;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 会员卡数据权益传递dto（内部）
 *
 * <AUTHOR>
 */
@Data
public class CardEquitiesListDTO implements Serializable {

    private static final long serialVersionUID = -8843490727780965047L;
    /**
     * hsa_member_info_card guid
     */
    private String ownGuid;

    /**
     * 会员卡guid
     */
    private String cardGuid;

    /**
     * 会员卡名称
     */
    private String cardName;

    /**
     * 会员卡类型 0 实体卡 1电子卡
     */
    private Integer cardType;


    /**
     * 获取卡剩余冻结金额
     */
    private BigDecimal freezeAmount;

    /**
     * 账户金额
     */
    private BigDecimal accountMoney;

    /**
     * 剩余可超金额
     */
    private BigDecimal excessAmount;

    /**
     * 剩余可超次数
     */
    private Integer excessTimes;

    /**
     * 权益
     */
    private HsaBusinessEquities equities;

}
