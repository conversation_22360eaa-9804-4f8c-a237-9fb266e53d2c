package com.holderzone.member.base.dto;

import com.holderzone.member.base.entity.member.HsaMemberOrderDiscount;
import com.holderzone.member.common.module.settlement.apply.dto.OrderPayDiscountDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 下单锁定优惠
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Data
public class SettlementPayAfterDiscountDTO extends OrderPayDiscountDTO implements Serializable {


    private static final long serialVersionUID = -5239525768641874058L;

    /**
     * 主体
     */
    @ApiModelProperty("主体guid")
    private String operSubjectGuid;

    /**
     * 会员 guid
     */
    @ApiModelProperty("会员guid")
    private String memberInfoGuid;

    /**
     * 当前 订单号 : 计算时必传
     * orderNum
     */
    @ApiModelProperty("当前订单号")
    @NotBlank(message = "订单号必传！")
    private String orderNo;

    /**
     * 应用业务 0、预订餐 1、点餐 2、自助餐 3、快速收款 4、外卖 5、卡包 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.growth.ApplyBusinessEnum
     */
    @NotNull(message = "订单类型不能为空")
    @ApiModelProperty(value = "0：预定订单 1：点餐订单 2：自助餐 3：快速收款 14：自助小碗菜'", required = true)
    private Integer orderType;

    private Integer codeType;

    //============= 操作人信息

    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    private String userName;

    /**
     * 用户名
     */
    @ApiModelProperty("手机号")
    private String phoneNum;

    /**
     * 当时积分
     */
    @ApiModelProperty("会员积分")
    private Integer memberIntegral;


    /**
     * 支付时间
     */
    @ApiModelProperty("支付时间")
    private LocalDateTime payTime;

    /**
     * 支付优惠
     */
    @ApiModelProperty("支付优惠")
    private Map<Integer, List<HsaMemberOrderDiscount>> orderDiscountByOptionMap;
}
