package com.holderzone.member.base.service.growth.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.holderzone.member.base.client.RequestGoalgoService;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.grade.HsaGiftBagBaseInfo;
import com.holderzone.member.base.entity.grade.HsaGradeGiftBag;
import com.holderzone.member.base.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.base.entity.growth.HsaGrowthValueDetail;
import com.holderzone.member.base.entity.member.HsaMemberConsumption;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.entity.recharge.HsaCardRechargeGiftDetail;
import com.holderzone.member.base.mapper.card.HsaMemberInfoCardMapper;
import com.holderzone.member.base.mapper.gift.HsaCardRechargeGiftDetailMapper;
import com.holderzone.member.base.mapper.grade.HsaGiftBagBaseInfoMapper;
import com.holderzone.member.base.mapper.grade.HsaMemberGradeInfoMapper;
import com.holderzone.member.base.mapper.growth.HsaGrowthValueDetailMapper;
import com.holderzone.member.base.mapper.member.HsaMemberConsumptionMapper;
import com.holderzone.member.base.mapper.member.HsaMemberLabelMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.growth.HsaGrowthValueDetailService;
import com.holderzone.member.base.transform.growth.GrowthValueDetailTransform;
import com.holderzone.member.base.util.EasyExcelUtils;
import com.holderzone.member.base.util.StringBaseHandlerUtil;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.FileConstant;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.growth.HsaGrowthValueDetailDTO;
import com.holderzone.member.common.dto.growth.MemberGrowthDetailExportDTO;
import com.holderzone.member.common.dto.growth.PartnerMemberGrowthDetailExportDTO;
import com.holderzone.member.common.dto.member.MemberRelationLabelDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.enums.RegisterChannelEnum;
import com.holderzone.member.common.enums.SystemPermissionEnum;
import com.holderzone.member.common.enums.card.ConsumptionTypeEnum;
import com.holderzone.member.common.enums.grade.UpgradeTypeEnum;
import com.holderzone.member.common.enums.growth.SumValueChangeEnum;
import com.holderzone.member.common.enums.growth.TaskActionEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.member.RoleTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.qo.growth.GrowthValueDetailMemberQO;
import com.holderzone.member.common.qo.growth.GrowthValueDetailQO;
import com.holderzone.member.common.qo.growth.GrowthValueDetailRequest;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import com.holderzone.member.common.vo.excel.ExcelGrowthValueDetailVO;
import com.holderzone.member.common.vo.gift.RechargeGiftActivityVO;
import com.holderzone.member.common.vo.growth.GrowthLevelVO;
import com.holderzone.member.common.vo.growth.GrowthValueDetailMemberVO;
import com.holderzone.member.common.vo.growth.GrowthValueDetailVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.member.common.constant.NumberConstant.NUMBER_0;

/**
 * <AUTHOR>
 * @description 成长值明细操作类
 * @date 2021/11/23
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class HsaGrowthValueDetailServiceImpl extends HolderBaseServiceImpl<HsaGrowthValueDetailMapper, HsaGrowthValueDetail> implements HsaGrowthValueDetailService {

    private final HsaGrowthValueDetailMapper detailMapper;

    private final HsaOperationMemberInfoMapper memberInfoMapper;

    private final SystemRoleHelper systemRoleHelper;

    private final HsaMemberInfoCardMapper hsaMemberInfoCardMapper;

    private final HsaMemberGradeInfoMapper hsaMemberGradeInfoMapper;

    private final HsaGiftBagBaseInfoMapper hsaGiftBagBaseInfoMapper;

    private final HsaMemberLabelMapper hsaMemberLabelMapper;

    private final HsaMemberConsumptionMapper hsaMemberConsumptionMapper;

    private final GuidGeneratorUtil guidGeneratorUtil;

    private final ExternalSupport externalSupport;

    private static final String GROWTH_VALUE_SHEET = "成长值完成记录";

    private static final String CONTENT_TYPE = "application/vnd.ms-excel";

    private static final String UTF_8 = "utf-8";

    private static final String HEADER = "Content-disposition";

    private static final String ATTACHMENT = "attachment;filename=";

    private static final String PAY_ORDER = "充值订单";

    private final RedissonClient redissonClient;

    private final RequestGoalgoService goalgoService;

    private final MemberMarketingFeign memberMarketingFeign;

    private final HsaCardRechargeGiftDetailMapper hsaCardRechargeGiftDetailMapper;

    @Override
    public PageResult<GrowthValueDetailMemberVO> listMemberGrowth(GrowthValueDetailMemberQO qo) {
        PageHelper.startPage(qo.getCurrentPage(), qo.getPageSize());
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        List<GrowthValueDetailMemberVO> pageList = detailMapper.listMemberGrowth(qo);
        //去查询门店信息
        if (CollUtil.isNotEmpty(pageList)) {
            enrichRechargeGiftActivityInfoForGrowth(pageList);
        }
        return PageUtil.pageResult(new PageInfo<>(pageList));
    }

    @Override
    public GrowthLevelVO memberGrowthLevel(String guid, String roleType) {
        if (StringUtils.isEmpty(roleType)) {
            roleType = RoleTypeEnum.MEMBER.name();
        }
        Integer invalidGrowthValue = detailMapper.sumInvalidGrowthValue(guid, ThreadLocalCache.getOperSubjectGuid());
        //会员信息
        GrowthLevelVO memberGradeInfo = memberInfoMapper.getMemberGradeInfo(ThreadLocalCache.getOperSubjectGuid(), guid, roleType);
        memberGradeInfo.setInvalidGrowthValue(invalidGrowthValue);
        return memberGradeInfo;
    }

    @Override
    public PageResult getGrowthValueDetailList(GrowthValueDetailRequest request) {
        request.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        PageMethod.startPage(request.getCurrentPage(), request.getPageSize());
        List<GrowthValueDetailVO> growthValueDetailList = detailMapper.getGrowthValueDetailList(request);
        //处理成长值任务完成明细记录数据参数
        setDetailListParams(growthValueDetailList);
        return PageUtil.getPageResult(new PageInfo<>(growthValueDetailList));
    }

    /**
     * 处理成长值任务完成明细记录数据参数
     *
     * @param growthValueDetailList 明细记录列表
     */
    private void setDetailListParams(List<GrowthValueDetailVO> growthValueDetailList) {
        if (CollUtil.isEmpty(growthValueDetailList)) {
            return;
        }
        //会员guid集合
        List<String> memberInfoGuids = growthValueDetailList.stream()
                .map(GrowthValueDetailVO::getMemberInfoGuid).distinct().collect(Collectors.toList());
        //通过会员guid,查询所拥有的会员卡集合
        Map<String, List<String>> cardNameMap = hsaMemberInfoCardMapper.selectList(
                        new LambdaQueryWrapper<HsaMemberInfoCard>()
                                .eq(HsaMemberInfoCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                                .in(HsaMemberInfoCard::getMemberInfoGuid, memberInfoGuids))
                .stream().collect(Collectors.groupingBy(HsaMemberInfoCard::getMemberInfoGuid, Collectors.mapping(HsaMemberInfoCard::getCardName, Collectors.toList())));
        //会员消费记录GUID集合
        List<String> memberConsumptionGuidList = growthValueDetailList.stream()
                .map(GrowthValueDetailVO::getMemberConsumptionGuid).distinct().collect(Collectors.toList());
        //会员消费记录map
        Map<String, HsaMemberConsumption> memberConsumptionMap = hsaMemberConsumptionMapper.selectList(
                        new LambdaQueryWrapper<HsaMemberConsumption>()
                                .eq(HsaMemberConsumption::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                                .in(HsaMemberConsumption::getGuid, memberConsumptionGuidList))
                .stream().collect(Collectors.toMap(HsaMemberConsumption::getGuid, Function.identity(), (obj, obj1) -> obj));
        //通过会员guid,查询所拥有的会员标签集合
        Map<String, List<String>> labelNameMap = hsaMemberLabelMapper.findMemberAllLabel(memberInfoGuids).stream()
                .collect(Collectors.groupingBy(MemberRelationLabelDTO::getMemberGuid, Collectors.mapping(MemberRelationLabelDTO::getLabelName, Collectors.toList())));
        //给成长值明细列表set补充数据
        for (GrowthValueDetailVO growthValueDetailVO : growthValueDetailList) {
            //设置会员卡集合
            String memberInfoGuid = growthValueDetailVO.getMemberInfoGuid();
            if (Objects.nonNull(cardNameMap) && cardNameMap.containsKey(memberInfoGuid)) {
                growthValueDetailVO.setMemberCards(cardNameMap.get(memberInfoGuid));
            }
            //设置会员标签集合
            if (Objects.nonNull(labelNameMap) && labelNameMap.containsKey(memberInfoGuid)) {
                growthValueDetailVO.setMemberLabels(labelNameMap.get(memberInfoGuid));
            }
            //任务动作
            Integer taskAction = growthValueDetailVO.getTaskAction();
            //会员下消费记录guid
            String memberConsumptionGuid = growthValueDetailVO.getMemberConsumptionGuid();
            //记录说明
            String recordDeclaration = growthValueDetailVO.getRecordDeclaration();
            //处理记录说明字段信息
            String declaration = recordDeclaration(taskAction, memberConsumptionGuid, recordDeclaration, memberConsumptionMap);
            growthValueDetailVO.setRecordDeclaration(declaration);
        }
    }

    /**
     * 处理记录说明字段信息
     *
     * @param taskAction            任务动作 0:注册 1:完善个人信息 2:单笔消费金额 3:消费指定商品 4:累计消费金额 5:累计消费笔数 6:单笔充值金额 7:累计充值金额
     * @param memberConsumptionGuid 会员消费记录GUID
     * @param recordDeclaration     记录说明
     * @param memberConsumptionMap  会员消费集合
     * @return 记录说明信息
     */
    private String recordDeclaration(Integer taskAction, String memberConsumptionGuid,
                                     String recordDeclaration, Map<String, HsaMemberConsumption> memberConsumptionMap) {
        if (Objects.nonNull(taskAction) &&
                (TaskActionEnum.REGISTER.getCode() == taskAction || TaskActionEnum.PERFECT_PERSONAL_DETAILS.getCode() == taskAction)) {
            return TaskActionEnum.getNameByCode(taskAction);
        }
        if (StringUtils.isEmpty(memberConsumptionGuid)) {
            return "";
        }
        if (Objects.nonNull(memberConsumptionMap) && memberConsumptionMap.containsKey(memberConsumptionGuid)) {
            HsaMemberConsumption hsaMemberConsumption = memberConsumptionMap.get(memberConsumptionGuid);
            //消费类型，0充值，1消费
            Integer consumptionType = hsaMemberConsumption.getConsumptionType();
            //订单类型 0：预定订单 1：点餐订单 2：自助餐 3：快速收款
            Integer orderType = hsaMemberConsumption.getOrderType();
            //订单编号
            String orderNumber = hsaMemberConsumption.getOrderNumber();
            if (ConsumptionTypeEnum.TYPE_RECHARGE.getCode() == consumptionType) {
                return PAY_ORDER + "+" + orderNumber;
            } else {
                String desByCode = externalSupport.memberServer(ThreadLocalCache.getSystem())
                        .getConsumptionOrderType(orderType);
                return desByCode + "+" + orderNumber;
            }
        }
        return recordDeclaration;
    }

    @Override
    public void exportMemberGrowth(GrowthValueDetailMemberQO qo) {
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        //查询条数
        Integer count = detailMapper.listExportMemberGrowthCount(qo);
        if (count > NumberConstant.NUMBER_20000) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_SUBSIDY_MAX_EXPORT_EXCEL);
        }
//        if(count == NumberConstant.NUMBER_0){
//            throw new MallBaseException(MemberAccountExceptionEnum.ERROR_NOT_DATA);
//        }
        //查询数据
        HsaOperationMemberInfo memberDetail = memberInfoMapper.queryByGuid(qo.getMemberInfoGuid());
        if (ThreadLocalCache.isPartner()) {
            List<PartnerMemberGrowthDetailExportDTO> partnerExportList = detailMapper.listPartnerExportMemberGrowth(qo);
            if (!CollectionUtils.isEmpty(partnerExportList)) {
                partnerExportList.forEach(p -> p.setPhone(memberDetail.getPhoneNum()));
            }
            toExport(memberDetail.getPhoneNum(), null, partnerExportList);
        } else {
            List<MemberGrowthDetailExportDTO> exportList = detailMapper.listExportMemberGrowth(qo);
            if (!CollectionUtils.isEmpty(exportList)) {
                exportList.forEach(e -> e.setPhone(memberDetail.getPhoneNum()));
            }
            toExport(memberDetail.getPhoneNum(), exportList, null);
        }
    }

    @Override
    public void exportGrowthValueDetailList(GrowthValueDetailRequest request) {
        request.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        PageMethod.startPage(NumberConstant.NUMBER_1, NumberConstant.NUMBER_20001);
        RLock lock = redissonClient.getLock("growth_value_detail_list");
        try {
            if (!lock.tryLock(NumberConstant.NUMBER_59, NumberConstant.NUMBER_20, TimeUnit.SECONDS)) {
                return;
            }
            List<ExcelGrowthValueDetailVO> excelGrowthValueDetailVOList = detailMapper.exportGrowthValueDetailList(request);
            if (excelGrowthValueDetailVOList.size() > NumberConstant.NUMBER_20000) {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_SUBSIDY_MAX_EXPORT_EXCEL);
            }
            HttpServletResponse response = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getResponse();
            assert response != null;
            setDetailParams(excelGrowthValueDetailVOList); //给导出的成长值明细记录封装数据
            exportData(excelGrowthValueDetailVOList, response);
        } catch (Exception e) {
            log.error(MemberAccountExceptionEnum.ERROR_GROWTH_VALUE_EXPORT.getDes());
            throw new MemberBaseException(e.getMessage());
        } finally {
            if (lock != null && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    @Override
    public List<ExcelGrowthValueDetailVO> queryExcelGrowthValueData(GrowthValueDetailRequest request) {
        request.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        PageMethod.startPage(1, NumberConstant.NUMBER_20001);
        List<ExcelGrowthValueDetailVO> excelGrowthValueDetailVOList = detailMapper.exportGrowthValueDetailList(request);
        if (excelGrowthValueDetailVOList.size() == 0) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_DATA);
        }
        if (excelGrowthValueDetailVOList.size() > NumberConstant.NUMBER_20000) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_SUBSIDY_MAX_EXPORT_EXCEL);
        }
        //给导出的成长值明细记录封装数据
        setDetailParams(excelGrowthValueDetailVOList);
        return excelGrowthValueDetailVOList;
    }

    @Override
    public HsaGrowthValueDetailDTO getGrowthValueDetail(Long id, String memberInfoGuid, Integer code) {
        LambdaQueryWrapper<HsaGrowthValueDetail> query = new LambdaQueryWrapper<HsaGrowthValueDetail>()
                .eq(HsaGrowthValueDetail::getGrowthValueTaskId, id)
                .eq(HsaGrowthValueDetail::getMemberInfoGuid, memberInfoGuid)
                .eq(HsaGrowthValueDetail::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaGrowthValueDetail::getTaskAction, code);
        HsaGrowthValueDetail valueDetail = this.getOne(query);
        return GrowthValueDetailTransform.INSTANCE.entityToDTO(valueDetail);
    }

    @Override
    public void saveHsaGrowthValueDetail(HsaOperationMemberInfo operationMemberInfo,
                                         Integer sourceType, String operSubjectGuid,
                                         HsaGradeGiftBag hsaGradeGiftBag,
                                         HsaMemberGradeInfo hsaMemberGradeInfo,
                                         List<HsaMemberGradeInfo> hsaMemberGradeInfoList) {
        HsaGiftBagBaseInfo hsaGiftBagBaseInfo = hsaGiftBagBaseInfoMapper.selectOne(new LambdaQueryWrapper<HsaGiftBagBaseInfo>()
                .eq(HsaGiftBagBaseInfo::getOperSubjectGuid, operSubjectGuid));
        HsaMemberGradeInfo memberGradeInfo = hsaMemberGradeInfoMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                .eq(HsaMemberGradeInfo::getEffective, NumberConstant.NUMBER_1)
                .eq(HsaMemberGradeInfo::getGuid, hsaGradeGiftBag.getMemberGradeInfoGuid())
                .in(HsaMemberGradeInfo::getIsDelete, NumberConstant.NUMBER_0, NumberConstant.NUMBER_2));
        HsaGrowthValueDetail hsaGrowthValueDetail = getHsaGrowthValueDetail(operationMemberInfo, sourceType, operSubjectGuid, hsaGradeGiftBag, hsaMemberGradeInfo, memberGradeInfo, hsaGiftBagBaseInfo);
        HsaMemberGradeInfo gradeInfo = calculateExtraAmount(operationMemberInfo.getMemberGrowthValue(), hsaMemberGradeInfoList);
        if (Objects.nonNull(gradeInfo)) {
            hsaGrowthValueDetail.setCurrentMemberLevel(
                    gradeInfo.getName() +
                            StringConstant.LEFT_BRACKET +
                            StringConstant.VIP +
                            gradeInfo.getVipGrade() +
                            StringConstant.RIGHT_BRACKET);
            //送完成长值以后，需要把冗余的等级信息也更新了
            operationMemberInfo.setMemberGradeInfoName(gradeInfo.getName());
            operationMemberInfo.setMemberGradeInfoGuid(gradeInfo.getGuid());
        }
        detailMapper.insert(hsaGrowthValueDetail);
    }

    private HsaGrowthValueDetail getHsaGrowthValueDetail(HsaOperationMemberInfo operationMemberInfo, Integer sourceType, String operSubjectGuid, HsaGradeGiftBag hsaGradeGiftBag, HsaMemberGradeInfo hsaMemberGradeInfo, HsaMemberGradeInfo memberGradeInfo, HsaGiftBagBaseInfo hsaGiftBagBaseInfo) {
        HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        hsaGrowthValueDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaGrowthValueDetail.class.getSimpleName()));
        hsaGrowthValueDetail.setGrowthValueSourceType(sourceType);
        hsaGrowthValueDetail.setOperatorAccountName(hsaMemberGradeInfo.getOperatorName());
        hsaGrowthValueDetail.setOperSubjectGuid(operSubjectGuid);
        hsaGrowthValueDetail.setRecordDeclaration(memberGradeInfo.getName() + StringConstant.STR_BIAS_TWO + hsaGiftBagBaseInfo.getEquitiesName());
        hsaGrowthValueDetail.setGrowthValue(hsaGradeGiftBag.getValue());
        hsaGrowthValueDetail.setChangeType(SumValueChangeEnum.BAG.getCode());
        hsaGrowthValueDetail.setMemberInfoGuid(operationMemberInfo.getGuid());
        hsaGrowthValueDetail.setGrowthValidity(NumberConstant.NUMBER_0);
        hsaGrowthValueDetail.setGrowthValueType(NumberConstant.NUMBER_0);
        hsaGrowthValueDetail.setRemainGrowthValue(operationMemberInfo.getMemberGrowthValue());
        hsaGrowthValueDetail.setRecordRemainGrowthValue(hsaGradeGiftBag.getValue());
        return hsaGrowthValueDetail;
    }

    @Override
    public void savePartnerGrowthValueDetail(GrowthValueDetailQO growthValueDetailQO) {
        HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        hsaGrowthValueDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaGrowthValueDetail.class.getSimpleName()));
        hsaGrowthValueDetail.setGrowthValueSourceType(53);
        hsaGrowthValueDetail.setOperatorAccountName(growthValueDetailQO.getPhone());
        hsaGrowthValueDetail.setOperSubjectGuid(growthValueDetailQO.getOperSubjectGuid());
        hsaGrowthValueDetail.setCurrentMemberLevel(
                growthValueDetailQO.getCurrentGradeName() +
                        StringConstant.LEFT_BRACKET +
                        StringConstant.VIP +
                        growthValueDetailQO.getTemporaryVipGrade() +
                        StringConstant.RIGHT_BRACKET);
        hsaGrowthValueDetail.setRecordDeclaration("微信支付");
        hsaGrowthValueDetail.setGrowthValue(0);
        hsaGrowthValueDetail.setChangeType(growthValueDetailQO.getChangeType());
        hsaGrowthValueDetail.setMemberInfoGuid(growthValueDetailQO.getMemberGuid());
        hsaGrowthValueDetail.setGrowthValidity(NumberConstant.NUMBER_0);
        hsaGrowthValueDetail.setGrowthValueType(NumberConstant.NUMBER_0);
        hsaGrowthValueDetail.setRemainGrowthValue(0);
        hsaGrowthValueDetail.setExpireTime(growthValueDetailQO.getExpireTime());
        hsaGrowthValueDetail.setNum(growthValueDetailQO.getNum());
        hsaGrowthValueDetail.setUnit(growthValueDetailQO.getUnit());
        hsaGrowthValueDetail.setUpgradeType(UpgradeTypeEnum.PAY.getCode());
        hsaGrowthValueDetail.setRoleType(growthValueDetailQO.getRoleType());
        detailMapper.insert(hsaGrowthValueDetail);
    }

    /**
     * 给导出的成长值明细记录封装数据
     *
     * @param excelGrowthValueDetailVOList 导出的成长值明细记录列表
     */
    private void setDetailParams(List<ExcelGrowthValueDetailVO> excelGrowthValueDetailVOList) {
        if (CollUtil.isEmpty(excelGrowthValueDetailVOList)) {
            return;
        }
        //会员消费记录GUID集合
        List<String> memberConsumptionGuids = excelGrowthValueDetailVOList.stream()
                .map(ExcelGrowthValueDetailVO::getMemberConsumptionGuid).distinct().collect(Collectors.toList());
        //会员消费记录map
        Map<String, HsaMemberConsumption> memberConsumptionMap = hsaMemberConsumptionMapper.selectList(
                        new LambdaQueryWrapper<HsaMemberConsumption>()
                                .eq(HsaMemberConsumption::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                                .in(HsaMemberConsumption::getGuid, memberConsumptionGuids))
                .stream().collect(Collectors.toMap(HsaMemberConsumption::getGuid, Function.identity(), (obj, obj1) -> obj));
        //会员guid集合
        List<String> memberInfoGuids = excelGrowthValueDetailVOList.stream()
                .map(ExcelGrowthValueDetailVO::getMemberInfoGuid).distinct().collect(Collectors.toList());
        //通过会员guid,查询所拥有的会员标签集合
        Map<String, List<String>> labelNameMap = hsaMemberLabelMapper.findMemberAllLabel(memberInfoGuids).stream()
                .collect(Collectors.groupingBy(MemberRelationLabelDTO::getMemberGuid, Collectors.mapping(MemberRelationLabelDTO::getLabelName, Collectors.toList())));
        //通过会员guid,查询所拥有的会员卡集合
        Map<String, List<String>> cardNameMap = hsaMemberInfoCardMapper.selectList(
                        new LambdaQueryWrapper<HsaMemberInfoCard>()
                                .eq(HsaMemberInfoCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                                .in(HsaMemberInfoCard::getMemberInfoGuid, memberInfoGuids))
                .stream().collect(Collectors.groupingBy(HsaMemberInfoCard::getMemberInfoGuid, Collectors.mapping(HsaMemberInfoCard::getCardName, Collectors.toList())));
        int number = 1; //序号
        boolean phonePermission = goalgoService.phonePermission(SystemPermissionEnum.MEMBER_PERMISSION.getDes());
        for (ExcelGrowthValueDetailVO excelGrowthValueDetailVO : excelGrowthValueDetailVOList) {

            excelGrowthValueDetailVO.setNumber(number);
            number = number + 1;
            //设置导出的会员手机号信息（区号+手机号）
            String phoneNum = excelGrowthValueDetailVO.getPhoneNum();
            String phoneCountryCode = excelGrowthValueDetailVO.getPhoneCountryCode();
            String phone = StringBaseHandlerUtil.phoneCountryCodeHandler(phoneCountryCode, phoneNum, phonePermission);
            excelGrowthValueDetailVO.setPhoneNum(phone);
            //任务动作
            Integer taskAction = excelGrowthValueDetailVO.getTaskActionStatus();
            //会员下消费记录guid
            String memberConsumptionGuid = excelGrowthValueDetailVO.getMemberConsumptionGuid();
            //记录说明
            String recordDeclaration = excelGrowthValueDetailVO.getRecordDeclaration();
            String declaration = recordDeclaration(taskAction, memberConsumptionGuid, recordDeclaration, memberConsumptionMap);
            excelGrowthValueDetailVO.setRecordDeclaration(declaration);
            excelGrowthValueDetailVO.setSourceType(RegisterChannelEnum.getNameByCode(excelGrowthValueDetailVO.getSourceTypeCode()));
            List<String> cardNames = cardNameMap.get(excelGrowthValueDetailVO.getMemberInfoGuid());
            excelGrowthValueDetailVO.setMemberCards(CollectionUtils.isEmpty(cardNames) ? "-" : cardNames.toString());
            List<String> labelNames = labelNameMap.get(excelGrowthValueDetailVO.getMemberInfoGuid());
            excelGrowthValueDetailVO.setMemberLabels(CollectionUtils.isEmpty(labelNames) ? "-" : labelNames.toString());
        }

    }

    private void exportData(List<ExcelGrowthValueDetailVO> excelGrowthValueDetailVOList, HttpServletResponse response) {
        String fileName = GROWTH_VALUE_SHEET + //表名称
                DateUtil.formatLocalDateTime(LocalDateTime.now()  //导出时间
                        , DateUtil.DATE_PATTERN);
        try {
            response.setContentType(CONTENT_TYPE);
            response.setCharacterEncoding(UTF_8);
            fileName = URLEncoder.encode(fileName, UTF_8);
            response.setHeader(HEADER, ATTACHMENT + fileName + ExcelTypeEnum.XLSX.getValue());
            EasyExcelUtils.writeExcelWithModel(response.getOutputStream(), excelGrowthValueDetailVOList, ExcelGrowthValueDetailVO.class, FileConstant.DEFAULT_SHEET, null);
        } catch (IOException e) {
            log.error("成长值任务完成记录明细报错!" + e.getMessage());
        }
    }

    private void toExport(String phoneNum, List<MemberGrowthDetailExportDTO> exportList,
                          List<PartnerMemberGrowthDetailExportDTO> partnerExportList) {
        try {
            HttpServletResponse response = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getResponse();
            assert response != null;
            response.setContentType("application/octet-stream;charset=utf-8");
            String fileName = phoneNum + FileConstant.UPLOAD_MEMBER_GROWTH_NAME + DateUtil.formatLocalDateTime(LocalDateTime.now()
                    , DateUtil.DATE_PATTERN) + ExcelTypeEnum.XLSX.getValue();
            response.setHeader("content-disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream()).build();

            if (ThreadLocalCache.isPartner()) {
                WriteSheet bindWriteSheet = EasyExcelFactory.writerSheet(1, FileConstant.DEFAULT_SHEET).head(PartnerMemberGrowthDetailExportDTO.class).build();
                excelWriter.write(partnerExportList, bindWriteSheet);
            } else {
                WriteSheet bindWriteSheet = EasyExcelFactory.writerSheet(1, FileConstant.DEFAULT_SHEET).head(MemberGrowthDetailExportDTO.class).build();
                excelWriter.write(exportList, bindWriteSheet);
            }

            excelWriter.finish();
        } catch (Exception e) {
            log.warn(MemberAccountExceptionEnum.MEMBER_GROWTH_EXPORT_ERROR.getDes());
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.MEMBER_GROWTH_EXPORT_ERROR, ThreadLocalCache.getOperSubjectGuid()));
        }
    }

    /**
     * 为充值赠送类型的成长值记录丰富活动信息
     *
     * @param growthValueDetailMemberList 会员成长值明细列表
     */
    private void enrichRechargeGiftActivityInfoForGrowth(List<GrowthValueDetailMemberVO> growthValueDetailMemberList) {
        // 过滤出充值赠送类型的记录
        List<GrowthValueDetailMemberVO> rechargeGiftList = growthValueDetailMemberList.stream()
                .filter(detail -> detail.getChangeType() == SumValueChangeEnum.RECHARGE_GIFT.getCode())
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(rechargeGiftList)) {
            return;
        }

        // 获取订单号列表
        List<String> orderNumbers = rechargeGiftList.stream()
                .map(GrowthValueDetailMemberVO::getOrderNumber)
                .filter(StringUtils::hasText)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(orderNumbers)) {
            return;
        }

        // 根据订单号查询充值赠送记录
        List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetails = hsaCardRechargeGiftDetailMapper.selectList(
                new LambdaQueryWrapper<HsaCardRechargeGiftDetail>()
                        .in(HsaCardRechargeGiftDetail::getOrderNum, orderNumbers));

        if (CollUtil.isEmpty(hsaCardRechargeGiftDetails)) {
            return;
        }

        // 批量查询活动信息并创建映射关系
        Map<String, String> activityInfoMap = batchQueryActivityInfoForGrowth(hsaCardRechargeGiftDetails);

        // 创建订单号到活动信息的映射
        Map<String, HsaCardRechargeGiftDetail> orderToGiftDetailMap = hsaCardRechargeGiftDetails.stream()
                .collect(Collectors.toMap(
                        HsaCardRechargeGiftDetail::getOrderNum,
                        detail -> detail,
                        (existing, replacement) -> existing)); // 如果有重复订单号，保留第一个

        // 为每个充值赠送记录设置活动名称和GUID
        setActivityInfoForGrowthRecords(rechargeGiftList, orderToGiftDetailMap, activityInfoMap);
    }

    //充值有礼活动
    private static final String RECHARGE_GIFT_ACTIVITY_DEFAULT_NAME_GROWTH = "充值有礼活动";

    /**
     * 批量查询活动信息
     *
     * @param hsaCardRechargeGiftDetails 充值赠送明细列表
     * @return 活动GUID到活动名称的映射
     */
    private Map<String, String> batchQueryActivityInfoForGrowth(List<HsaCardRechargeGiftDetail> hsaCardRechargeGiftDetails) {
        // 获取活动GUID列表
        List<String> activityGuidList = hsaCardRechargeGiftDetails.stream()
                .map(HsaCardRechargeGiftDetail::getActivityGuid)
                .filter(StringUtils::hasText)
                .distinct()
                .collect(Collectors.toList());

        Map<String, String> activityInfoMap = new HashMap<>();

        if (CollUtil.isEmpty(activityGuidList)) {
            return activityInfoMap;
        }

        setActivityInfoMapForGrowth(activityGuidList, activityInfoMap);

        return activityInfoMap;
    }

    private void setActivityInfoMapForGrowth(List<String> activityGuidList, Map<String, String> activityInfoMap) {
        try {
            // 批量查询活动信息
            List<RechargeGiftActivityVO> activityVOList = memberMarketingFeign.queryRechargeGiftActivityByGuidList(activityGuidList);
            if (CollUtil.isNotEmpty(activityVOList)) {
                for (RechargeGiftActivityVO activityVO : activityVOList) {
                    if (activityVO != null && StringUtils.hasText(activityVO.getGuid())) {
                        String activityName = StringUtils.hasText(activityVO.getActivityName()) ?
                                activityVO.getActivityName() : RECHARGE_GIFT_ACTIVITY_DEFAULT_NAME_GROWTH;
                        activityInfoMap.put(activityVO.getGuid(), activityName);
                    }
                }
            }

            // 为未找到的活动GUID设置默认名称
            for (String activityGuid : activityGuidList) {
                if (!activityInfoMap.containsKey(activityGuid)) {
                    activityInfoMap.put(activityGuid, RECHARGE_GIFT_ACTIVITY_DEFAULT_NAME_GROWTH);
                }
            }
        } catch (Exception e) {
            log.warn("批量查询活动信息失败，activityGuidList: {}", activityGuidList, e);
        }
    }

    /**
     * 为充值赠送成长值记录设置活动信息
     *
     * @param rechargeGiftList 充值赠送记录列表
     * @param orderToGiftDetailMap 订单号到充值赠送明细的映射
     * @param activityInfoMap 活动GUID到活动名称的映射
     */
    private void setActivityInfoForGrowthRecords(List<GrowthValueDetailMemberVO> rechargeGiftList,
                                                 Map<String, HsaCardRechargeGiftDetail> orderToGiftDetailMap,
                                                 Map<String, String> activityInfoMap) {
        for (GrowthValueDetailMemberVO detailVO : rechargeGiftList) {
            HsaCardRechargeGiftDetail giftDetail = orderToGiftDetailMap.get(detailVO.getOrderNumber());
            if (giftDetail != null && StringUtils.hasText(giftDetail.getActivityGuid())) {
                detailVO.setActivityGuid(giftDetail.getActivityGuid());
                String activityName = activityInfoMap.get(giftDetail.getActivityGuid());
                detailVO.setActivityName(StringUtils.hasText(activityName) ? activityName : RECHARGE_GIFT_ACTIVITY_DEFAULT_NAME_GROWTH);
            }

        }
    }

    /**
     * 定位当前成长值所在等级
     *
     * @param memberGrowthValue      memberGrowthValue
     * @param hsaMemberGradeInfoList hsaMemberGradeInfoList
     * @return HsaExtraAwardRule
     */
    private HsaMemberGradeInfo calculateExtraAmount(Integer memberGrowthValue, List<HsaMemberGradeInfo> hsaMemberGradeInfoList) {
        HsaMemberGradeInfo awardRule = null;
        if (Objects.isNull(memberGrowthValue)) {
            memberGrowthValue = NUMBER_0;
        }
        for (int i = 0; i < hsaMemberGradeInfoList.size(); i++) {
            if (memberGrowthValue < hsaMemberGradeInfoList.get(i).getGrowthValue()) {
                if (i == 0) {
                    awardRule = hsaMemberGradeInfoList.get(i);
                } else {
                    awardRule = hsaMemberGradeInfoList.get(i - 1);
                }
                break;
            }
            if (i == hsaMemberGradeInfoList.size() - 1) {
                awardRule = hsaMemberGradeInfoList.get(i);
            }
        }
        return awardRule;
    }
}
