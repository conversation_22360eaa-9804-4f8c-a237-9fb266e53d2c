package com.holderzone.member.base.dto;

import com.holderzone.member.base.entity.grade.HsaBusinessEquities;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.common.qo.equities.CalculateCumulativeDiscountQO;
import com.holderzone.member.common.qo.equities.CalculateMemberPriceCommodityQO;
import com.holderzone.member.common.vo.equities.CalculateMemberPriceCommodityVO;
import com.holderzone.member.common.vo.equities.MembershipBenefitsVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MembershipBenefitsDTO implements Serializable {


    private CalculateMemberPriceCommodityQO memberPriceCommodityQO;


    private List<MembershipBenefitsVO> membershipBenefitsVOS;


    private HsaOperationMemberInfo hsaOperationMemberInfo;


    private HsaBusinessEquities gradeEquities;


    private CalculateMemberPriceCommodityVO memberPriceCommodityVO;


    private CalculateCumulativeDiscountQO cumulativeDiscountQO;
}
