package com.holderzone.member.base.util;

import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.service.cache.CacheService;
import com.holderzone.member.common.dto.user.HeaderUserInfo;

import java.util.Objects;

/**
 * 会员工具类
 *
 * <AUTHOR>
 * @date 2022/6/14
 **/
public final class MemberUtil {

    private MemberUtil() {

    }

    public static void cacheMember(CacheService cacheService, HsaOperationMemberInfo hsaOperationMemberInfo) {
        //缓存会员
        cacheService.createMember(hsaOperationMemberInfo.getGuid(), new HeaderUserInfo()
                .setUserGuid(hsaOperationMemberInfo.getGuid())
                .setUserName(hsaOperationMemberInfo.getUserName())
                .setTel(hsaOperationMemberInfo.getPhoneNum())
                .setAccountState(hsaOperationMemberInfo.getAccountState())
                .setRoleType(hsaOperationMemberInfo.getRoleType())
        );
    }

    public static HeaderUserInfo getMember(CacheService cacheService, String memberGuid) {
        //缓存会员
        return cacheService.getTokenMember(memberGuid);
    }

    /**
     * 更新账号状态
     *
     * @param memberGuid
     * @param accountState
     */
    public static void updateMemberAccountState(CacheService cacheService, String memberGuid, Integer accountState) {
        final HeaderUserInfo member = getMember(cacheService, memberGuid);
        if (Objects.isNull(member.getUserGuid())) {
            return;
        }
        member.setAccountState(accountState);
        //缓存会员
        cacheService.createMember(memberGuid, member);
    }

    /**
     * 更新账号角色列表
     */
    public static void updateMemberRoleType(CacheService cacheService, String memberGuid, String roleType) {
        final HeaderUserInfo member = getMember(cacheService, memberGuid);
        if (Objects.isNull(member.getUserGuid())) {
            return;
        }
        member.setRoleType(roleType);
        //缓存会员
        cacheService.createMember(memberGuid, member);
    }

    /**
     * 更新账号状态
     *
     * @param memberGuid
     * @param tel
     */
    public static void updateMemberTel(CacheService cacheService, String memberGuid, String tel) {
        final HeaderUserInfo wxMember = getMember(cacheService, memberGuid);
        if (Objects.isNull(wxMember.getUserGuid())) {
            return;
        }
        wxMember.setTel(tel);
        //缓存会员
        cacheService.createMember(memberGuid, wxMember);
    }
}
