package com.holderzone.member.base.dto.context;

import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponUse;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementLockedDiscountReqDTO;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 兑换券折扣处理上下文
 * <p>
 * 封装兑换券处理过程中需要的各种数据映射关系，包括：
 * - 优惠券占用次数映射
 * - 优惠券基本信息映射
 * - 优惠券已使用记录映射
 * - 选中优惠券映射
 * </p>
 */
@Data
public class ExchangeDiscountContext {

    /**
     * 优惠券占用次数映射
     * key: 优惠券代码 (discountOptionId)
     * value: 占用次数
     */
    private final Map<String, Integer> occupiedCountMap;

    /**
     * 优惠券基本信息映射
     * key: 优惠券代码 (code)
     * value: 优惠券详细信息
     */
    private final Map<String, HsaMemberCouponLink> couponLinkMap;

    /**
     * 优惠券已使用记录映射
     * key: 优惠券GUID (memberCouponLinkGuid)
     * value: 使用记录列表
     */
    private final Map<String, List<HsaMemberCouponUse>> usedCountMap;

    /**
     * 选中优惠券映射
     * key: 优惠券GUID (couponGuid)
     * value: 锁定的折扣请求信息
     */
    private final Map<String, SettlementLockedDiscountReqDTO> checkDiscountMap;

    /**
     * 构造函数
     *
     * @param occupiedCountMap 优惠券占用次数映射
     * @param couponLinkMap 优惠券基本信息映射
     * @param usedCountMap 优惠券已使用记录映射
     * @param checkDiscountMap 选中优惠券映射
     */
    public ExchangeDiscountContext(Map<String, Integer> occupiedCountMap,
                                   Map<String, HsaMemberCouponLink> couponLinkMap,
                                   Map<String, List<HsaMemberCouponUse>> usedCountMap,
                                   Map<String, SettlementLockedDiscountReqDTO> checkDiscountMap) {
        this.occupiedCountMap = occupiedCountMap;
        this.couponLinkMap = couponLinkMap;
        this.usedCountMap = usedCountMap;
        this.checkDiscountMap = checkDiscountMap;
    }
}