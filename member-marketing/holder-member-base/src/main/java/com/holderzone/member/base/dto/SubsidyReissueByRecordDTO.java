package com.holderzone.member.base.dto;

import com.google.common.collect.Lists;
import com.holderzone.member.base.entity.activity.HsaSubsidyActivity;
import com.holderzone.member.base.entity.activity.HsaSubsidyActivityDetailRecord;
import com.holderzone.member.base.entity.activity.HsaSubsidyActivityRecord;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.member.HsaMemberConsumption;
import com.holderzone.member.base.entity.member.HsaMemberConsumptionPayWay;
import com.holderzone.member.base.entity.member.HsaMemberFundingDetail;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.qo.card.TerMemberCardRechargeQO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 补发处理DTO
 * </p>
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SubsidyReissueByRecordDTO implements Serializable {

    /**
     * 补贴记录明细数据
     */
    @ApiModelProperty(value = "补贴活动记录明细数据")
    private List<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords;

    /**
     * 补贴活动
     */
    private Map<String, HsaSubsidyActivity> hsaSubsidyActivityMap;

    /**
     * 活动补贴记录
     */
    private Map<String, HsaSubsidyActivityRecord> hsaSubsidyActivityRecordMap;

    /**
     * 补贴会员数据
     */
    private Map<String, HsaOperationMemberInfo> hsaOperationMemberInfoMap;

    /**
     * 卡基础数据
     */
    private Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap;

    /**
     *补贴记录明细
     */
    private List<HsaSubsidyActivityDetailRecord> subsidyActivityDetailRecords;

    /**
     * 资金明细
     */
    private List<HsaMemberFundingDetail> hsaMemberFundingDetails;

    /**
     * 会员持卡
     */
    private List<HsaMemberInfoCard> hsaMemberInfoCards = Lists.newArrayList();

    /**
     * 补贴记录
     */
    private List<HsaSubsidyActivityRecord> hsaSubsidyActivityRecords = Lists.newArrayList();


    public SubsidyReissueByRecordDTO(List<HsaSubsidyActivityDetailRecord> hsaSubsidyActivityDetailRecords,
                                     Map<String, HsaSubsidyActivity> hsaSubsidyActivityMap,
                                     Map<String, HsaSubsidyActivityRecord> hsaSubsidyActivityRecordMap,
                                     Map<String, HsaOperationMemberInfo> hsaOperationMemberInfoMap,
                                     Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap,
                                     List<HsaSubsidyActivityDetailRecord> subsidyActivityDetailRecords) {
        this.hsaSubsidyActivityDetailRecords = hsaSubsidyActivityDetailRecords;
        this.hsaSubsidyActivityMap = hsaSubsidyActivityMap;
        this.hsaSubsidyActivityRecordMap = hsaSubsidyActivityRecordMap;
        this.hsaOperationMemberInfoMap = hsaOperationMemberInfoMap;
        this.hsaCardBaseInfoMap = hsaCardBaseInfoMap;
        this.subsidyActivityDetailRecords = subsidyActivityDetailRecords;
        this.hsaMemberFundingDetails = Lists.newArrayList();
    }
}
