package com.holderzone.member.base.dto;

import com.beust.jcommander.internal.Lists;
import com.holderzone.member.base.entity.growth.HsaSuspendTaskTimeQuantum;
import com.holderzone.member.base.entity.integral.HsaIntegralTask;
import com.holderzone.member.common.dto.integral.IntegralChangeDTO;
import com.holderzone.member.common.enums.growth.TaskActionEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class IntegralTaskApplyDTO implements Serializable {

    /**
     * 成长值变更参数
     */
    private IntegralChangeDTO event;

    /**
     * 积分任务
     */
    private HsaIntegralTask hsaIntegralTask;

    /**
     * 任务动作枚举
     */
    private TaskActionEnum taskActionEnum;

    /**
     * 积分任务暂停时间段
     */
    private Map<String, List<HsaSuspendTaskTimeQuantum>> suspendMap;

    /**
     * 积分累计充值赠送
     */
    private List<Integer> sumIntegral = Lists.newArrayList();
}
