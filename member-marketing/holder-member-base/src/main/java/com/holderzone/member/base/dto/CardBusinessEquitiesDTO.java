package com.holderzone.member.base.dto;

import com.google.common.collect.Lists;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.grade.HsaMemberEquitiesReceiveRecord;
import com.holderzone.member.base.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.base.entity.growth.HsaGrowthValueDetail;
import com.holderzone.member.base.entity.integral.HsaIntegralDetail;
import com.holderzone.member.common.vo.grade.MemberGradeRelationVO;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 会员卡权益赠送业务
 */
@Data
public class CardBusinessEquitiesDTO implements Serializable {

    /**
     * 会员等级关联guid
     */
    private Map<String, String> memberGradeInfoGuidMap;

    /**
     * 当前会员已有积分成长值
     */
    private transient Map<String, MemberGradeRelationVO> memberGradeRelationVOMap;

    /**
     * 会员信息
     */
    private Map<String, HsaMemberGradeInfo> hsaMemberGradeInfoMap;

    /**
     * 会员卡信息
     */
    private Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap = new HashMap<>();

    /**
     * 会员累计赠成长值
     */
    private Map<String, Integer> growthValueTotalMap = new HashMap<>();

    /**
     * 会员累计赠送积分
     */
    private Map<String, Integer> integralValueTotalMap = new HashMap<>();

    /**
     * 成长值明细记录
     */
    private List<HsaGrowthValueDetail> hsaGrowthValueDetails = Lists.newArrayList();

    /**
     * 积分明细记录
     */
    private List<HsaIntegralDetail> hsaIntegralDetails = Lists.newArrayList();

    /**
     * 权益信息记录
     */
    private List<HsaMemberEquitiesReceiveRecord> hsaMemberEquitiesReceiveRecords = Lists.newArrayList();


    /**
     * 会员实际累计赠成长值
     */
    private Map<String, Integer> growthRealityValueTotalMap = new HashMap<>();

    /**
     * 会员实际累计赠送积分
     */
    private Map<String, Integer> integralRealityValueTotalMap = new HashMap<>();


    /**
     * 是否一次性赠送 1 是 0 否
     */
    private Integer isDisposable;
}
