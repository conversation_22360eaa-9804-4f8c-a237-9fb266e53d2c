package com.holderzone.member.base.dto;

import com.holderzone.member.base.entity.growth.HsaGrowthValueDetail;
import com.holderzone.member.base.entity.member.*;
import com.holderzone.member.common.dto.card.MemberCardOpenDTO;
import com.holderzone.member.common.dto.excel.HsmMemberUploadExcel;
import com.holderzone.member.common.qo.grade.AdjustGradeQO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class MemberInfoImportSuccessDTO implements Serializable {

    private static final long serialVersionUID = 6557223919458621173L;

    private HsmMemberUploadExcel memberUploadExcel;
    private HsaOperationMemberInfo operationMemberInfo;
    private List<HsaOperationMemberInfo> batchSaveMemberInfoList;
    private List<HsaMemberCertificateInfo> hsaMemberCertificateInfos;
    private List<MemberCardOpenDTO> cardOpenList;
    private List<HsaGrowthValueDetail> hsaGrowthValueDetails;
    private AdjustGradeQO adjustGradeQO;

}
