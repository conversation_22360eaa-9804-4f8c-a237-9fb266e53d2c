package com.holderzone.member.base.entity.member;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 运营主体会员信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HsaOperationMemberInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会员GUID
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 会员账号，运营主体账号以OXXX123456789012，前缀不足补充在后面
     */
    private String memberAccount;

    /**
     * 会员编号
     */
    private String memberNum;

    /**
     * 会员等级guid
     */
    private String memberGradeInfoGuid;

    /**
     * 会员等级name
     */
    private String memberGradeInfoName;

    /**
     * 会员付费等级guid
     */
    private String memberPaidGradeInfoGuid;

    /**
     * 会员付费等级name
     */
    private String memberPaidGradeInfoName;

    /**
     * 手机国家编码
     */
    private String phoneCountryCode;

    /**
     * 手机号码
     */
    private String phoneNum;

    /**
     * 顾客姓名
     */
    private String userName;

    /**
     * 微信是否授权 0未授权，1授权
     * @see com.holderzone.member.common.enums.member.AuthorizationStateEnum
     */
    private int wechatState;

    /**
     * 支付宝是否授权 0未授权，1授权
     * @see com.holderzone.member.common.enums.member.AuthorizationStateEnum
     */
    private int aliState;

    /**
     * 顾客昵称
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String aliNickName;

    /**
     * 头像url
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String aliHeadImgUrl;

    /**
     * 顾客昵称
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String nickName;



    /**
     * 性别，值为1时是男性，值为2时是女性，值为0时是未知
     */
    private Integer sex;

    /**
     * 出生日期
     */
    private LocalDate birthday;

//    /**
//     * 证件类型，0身份证，1军人证，2护照，3港澳台通行证，4其它
//     */
//    private Integer certificateType;
//
//    /**
//     * 证件号码
//     */
//    private String certificateNum;

//    /**
//     * 证件信息
//     */
//    private String certificateInfoJson;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 所属单位
     */
    private String workName;

    /**
     * 所属部门
     */
    private String departmentName;

    /**
     * 省编码
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String provinceCode;

    /**
     * 省名字
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String provinceName;

    /**
     * 市编码
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String cityCode;

    /**
     * 市名字
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String cityName;

    /**
     * 区编码
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String areaCode;

    /**
     * 区名字
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String areaName;

    /**
     * 联系地址
     */
    private String contactAddress;

    /**
     * 用户密码
     */
    private String password;

    /**
     * 支付密码
     */
    private String payPassword;

    /**
     * 禁用原因
     */
    private String reasonForDisable;

    /**
     * 最后消费时间
     */
    private LocalDateTime lastConsumptionTime;

    /**
     * 操作人guid
     */
    private String operatorGuid;

    /**
     * 操作人名字
     */
    private String operatorName;

    /**
     * 企业GUID
     */
    private String enterpriseGuid;

    /**
     * 会员来源企业名字
     */
    private String enterpriseName;

    /**
     * 品牌GUID
     */
    private String brandGuid;

    /**
     * 会员来源品牌名字
     */
    private String brandName;

    /**
     * 门店GUID
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String storeGuid;

    /**
     * 会员来源门店名称
     */
    private String storeName;

    /**
     * 会员来源归属，0门店，1品牌，2企业，3运营主体，4掌控者平台
     *  @see com.holderzone.member.common.enums.member.SourceAscriptionTypeEnum
     */
    private Integer sourceAscription;

    /**
     * 会员注册来源,0后台添加,1POS机注册,2一体机注册,3后台导入，微网站(21微信扫码点餐，24微信注册.25微信C端后台注册)，微信小程序(51和惠多，52翼惠天下，53赚餐)
     * @see com.holderzone.member.common.enums.member.SourceTypeEnum
     */
    private Integer sourceType;

    /**
     * 账号状态,0正常,1冻结
     */
    private Integer accountState;

    /**
     * 是否删除,0未删除,1已删除
     */
    private Integer isDelete;

    /**
     * 头像url
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String headImgUrl;

    /**
     * 会员账户余额
     */
    private BigDecimal memberAccountMoney;

    /**
     * 会员账户赠送余额
     */
    private BigDecimal memberGiftAccountMoney;

    /**
     * 会员积分
     */
    private Integer memberIntegral;

    /**
     * 会员成长值
     */
    private Integer memberGrowthValue;

    /**
     * 完善个人信息dto
     *
     * @see com.holderzone.member.common.dto.integral.PerfectPersonalDetailDTO
     */
    private String perfectPersonalDetailJson;

    /**
     * 会员补贴总金额
     */
    private BigDecimal memberSubsidyAmountMoney;


    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    /**
     * 会员等级升级时间
     */
    private LocalDateTime upgradeTime;

    private LocalDateTime expireTime;

    /**
     * 是否开启积分购物默认抵扣（0：未开启  1：开启）默认为未开启
     */
    private Integer integralDeduction;

    /**
     * 人脸识别（用户id）
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String userId;

    /**
     * 人脸识别（请求标识码，随机数，唯一）
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String logId;

    /**
     * 人脸识别（图片的唯一表示）
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String faceToken;

    /**
     * 公司地址
     */
    private String companyAddress;

    /**
     * 所在行业
     */
    private String industry;

    /**
     * 所在行业codes
     */
    private String industryCodes;

    /**
     * 职位名称
     */
    private String jobTitle;

    /**
     * 个人简介
     */
    private String introduction;

    /**
     * 角色类型
     *
     * @see com.holderzone.member.common.enums.member.RoleTypeEnum
     */
    private String roleType;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 视频
     */
    private String videoJson;

    /**
     * 全拼
     */
    private String pinyin;


    /**
     * 资料项
     */
    private String dataItemJson;
}
