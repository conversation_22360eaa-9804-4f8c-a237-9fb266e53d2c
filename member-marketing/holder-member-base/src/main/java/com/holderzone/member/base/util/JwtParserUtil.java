package com.holderzone.member.base.util;


import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.dto.user.IPaasTokenRequestBO;
import com.holderzone.member.common.dto.user.TokenRequestBO;
import com.holderzone.member.common.exception.FileIllegalException;
import com.holderzone.member.common.util.verify.CustomJwtEncoder;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import com.holderzone.framework.security.SecurityManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import com.holderzone.framework.exception.DecoderException;

import javax.annotation.Resource;
import javax.crypto.SecretKey;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Date;

import static com.holderzone.member.common.util.verify.CustomJwtEncoder.UTF_8;


/**
 * <AUTHOR>
 * @date 2021.08.19
 */
@Component
public class JwtParserUtil {

    public static final String OPEN_ID = "openId";

    public static final String SESSION_KEY = "sessionKey";

    public static final String UNION_ID = "unionId";

    public static final String PHONE_NUMBER = "phoneNumber";

    public static final String ACCOUNT = "account";

    /**
     * 秘钥key
     */
    public static final String JWT_SECRET = "7786df7fc3a34e26a61c034d5ec8245d";

    /**
     * 服务端指定秘钥Key
     */
    @Value("${token.server.secret}")
    private String javaJsonWebProfile;

    @Value("${token.ttl}")
    public long JWT_REFRESH_TTL;

    private static final String TOKEN_PREFIX = "token_";

    @Resource
    private RedisTemplate redisTemplate;

    /**
     * 解析签名(jwt的第三部分)
     *
     * @param token
     * @return
     */
    public String acquireSign(String token) {
        return token.split("\\.")[2];
    }

    public TokenRequestBO acquirePayload(String token) {
        try {
            byte[] decode = Base64.getDecoder().decode((token.split("\\.")[1]).getBytes(UTF_8));
            TokenRequestBO tokenRequestBO = JacksonUtils.toObject(TokenRequestBO.class, decode);
            return tokenRequestBO;
        } catch (UnsupportedEncodingException e) {
            throw new FileIllegalException("编码错误", e);
        }
    }

    public IPaasTokenRequestBO acquirePayloadPass(String token) {
        try {
            byte[] decode = Base64.getDecoder().decode((token.split("\\.")[1]).getBytes(StandardCharsets.UTF_8));
            return JacksonUtils.toObject(IPaasTokenRequestBO.class, decode);
        } catch (Exception e) {
            throw new FileIllegalException("编码错误", e);
        }
    }

    public String acquirePassPreferredUsernameToken(String token) {
        IPaasTokenRequestBO paasTokenRequestBO = acquirePayloadPass(token);
        return paasTokenRequestBO.getPreferred_username();
    }

    /**
     * @param bytes
     * @return
     */
    public Object toObject(byte[] bytes) {
        Object obj = null;
        try {
            ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
            ObjectInputStream ois = new ObjectInputStream(bis);
            obj = ois.readObject();
            ois.close();
            bis.close();
        } catch (IOException ex) {
            ex.printStackTrace();
        } catch (ClassNotFoundException ex) {
            ex.printStackTrace();
        }
        return obj;
    }

    public String acquireUserFromToken(String token) {
        TokenRequestBO tokenRequestBO = acquirePayload(token);
        return tokenRequestBO.getOpenId();
    }


    public String acquireStoreFromToken(String token) {
        TokenRequestBO tokenRequestBO = acquirePayload(token);
        return tokenRequestBO.getSessionKey();
    }

    public String acquireDeviceFromToken(String token) {
        TokenRequestBO tokenRequestBO = acquirePayload(token);
        return tokenRequestBO.getUnionId();
    }

    public String getEncryptedData(String token) {
        TokenRequestBO tokenRequestBO = acquirePayload(token);
        return tokenRequestBO.getEncryptedData();
    }

    /**
     * 小程序生成token
     *
     * @param nowMillis  nowMillis
     * @param openId     openId
     * @param sessionKey sessionKey
     * @param unionId    unionId
     * @return token
     */
    public String createTokenForClient(long nowMillis, String openId, String sessionKey, String unionId) {
        Claims claims = Jwts.claims();
        claims.put(JwtParserUtil.OPEN_ID, openId == null ? "" : openId);
        claims.put(JwtParserUtil.UNION_ID, unionId == null ? "" : unionId);
        claims.put(JwtParserUtil.SESSION_KEY, sessionKey == null ? "" : sessionKey);
        claims.setIssuedAt(new Date(nowMillis));
        SecretKey key = generateKey();
        JwtBuilder builder = Jwts.builder()
                .base64UrlEncodeWith(new CustomJwtEncoder())
                .setClaims(claims)
                .signWith(key);
        return builder.compact();
    }

    public SecretKey generateKey() {
        try {
            return Keys.hmacShaKeyFor(SecurityManager.decryptBase64ToByte(javaJsonWebProfile + JWT_SECRET));
        } catch (DecoderException e) {
            throw new BusinessException("生成密钥错误", e);
        }
    }


    /**
     * 小程序生成token
     *
     * @param nowMillis  nowMillis
     * @param accountNum     accountNum
     * @param phoneNum phoneNum
     * @return token
     */
    public String createTokenForClient(long nowMillis,String accountNum, String phoneNum) {
        Claims claims = Jwts.claims();
        claims.put(JwtParserUtil.PHONE_NUMBER, phoneNum == null ? "" : phoneNum);
        claims.put(JwtParserUtil.ACCOUNT, accountNum == null ? "" : accountNum);
        claims.setIssuedAt(new Date(nowMillis));
        SecretKey key = generateKey();
        JwtBuilder builder = Jwts.builder()
                .base64UrlEncodeWith(new CustomJwtEncoder())
                .setClaims(claims)
                .signWith(key);
        return builder.compact();
    }
}
