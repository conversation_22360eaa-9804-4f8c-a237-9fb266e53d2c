package com.holderzone.member.base.dto;


import com.holderzone.member.base.entity.activity.HsaSubsidyActivity;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;

import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 补发处理DTO
 * </p>
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SubsidyCheckConditionDTO implements Serializable {

    /**
     * 补贴时间
     */
    private LocalDateTime now;

    /**
     * 会员卡guid
     */
    private String cardGuid;

    /**
     * 会员卡基础数据
     */
    private Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap;

    /**
     * 会员数据
     */
    private Map<String, HsaOperationMemberInfo> hsaOperationMemberInfoMap;

    /**
     * 会员卡数据
     */
    private Map<String, HsaMemberInfoCard> hsaMemberInfoCardMap;

    private HsaSubsidyActivity hsaSubsidyActivity;
}
