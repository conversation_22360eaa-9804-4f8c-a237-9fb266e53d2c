package com.holderzone.member.base.dto;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.holderzone.member.base.entity.activity.HsaSubsidyActivity;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * <p>
 * 补发处理DTO
 * </p>
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CreationSubsidyDetailRecordDTO implements Serializable {

    /**
     * 补贴卡
     */
    private String cardGuid;


    /**
     * 补贴活动guid
     */
    private String subsidyActivityGuid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 补贴机制时间(整点触发时间或补发时间或立即发放)
     */
    private LocalDateTime subsidyTime;

    /**
     * 补贴记录guid
     */
    private String subsidyActivityRecordGuid;




    // 补贴活动数据》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》

    /**
     * 活动数据
     */
    private HsaSubsidyActivity hsaSubsidyActivity;

    /**
     * 活动名称
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String activityName;

    /**
     * 活动编号
     */
    private String activityNum;

    /**
     * 补贴金是否可退 0 不可退  1 可退
     */
    private Integer isRetreat;

    /**
     * 补贴金额
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal subsidyMoney;

    /**
     * 操作人员账号
     */
    @ApiModelProperty("操作人员账号")
    private String operatorTelName;

    /**
     * 补贴会员guid
     */
    private String memberInfoGuid;

    /**
     * 补贴失败原因
     */
    private String error;

    /**
     * 补贴成功时间
     */
    private LocalDateTime now;

    /**
     * 补贴结果 0 失败  1 成功
     */
    private Integer subsidyResult;

    /**
     * 补贴金到期时间
     */
    private LocalDateTime validityExpireTime;

    /**
     * memberInfoCardGuid
     */
    private String memberInfoCardGuid;

    /**
     * 补贴剩余金额
     */
    private BigDecimal subsidyResidueMoney;
}
