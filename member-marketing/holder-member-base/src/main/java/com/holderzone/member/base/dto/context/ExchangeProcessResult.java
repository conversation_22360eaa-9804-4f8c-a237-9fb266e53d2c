package com.holderzone.member.base.dto.context;

import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponUse;
import lombok.Data;

import java.util.List;

/**
 * 兑换券处理结果
 * <p>
 * 封装兑换券处理过程的执行结果，包括：
 * - 生成的使用记录列表
 * - 需要更新状态的优惠券列表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class ExchangeProcessResult {

    /**
     * 生成的使用记录列表
     * 记录兑换券的具体使用情况
     */
    private final List<HsaMemberCouponUse> useRecords;

    /**
     * 需要更新状态的优惠券列表
     * 当兑换券达到使用次数上限时，需要更新状态为已使用
     */
    private final List<HsaMemberCouponLink> couponsToUpdate;

    /**
     * 构造函数
     *
     * @param useRecords 使用记录列表
     * @param couponsToUpdate 需要更新状态的优惠券列表
     */
    public ExchangeProcessResult(List<HsaMemberCouponUse> useRecords, List<HsaMemberCouponLink> couponsToUpdate) {
        this.useRecords = useRecords;
        this.couponsToUpdate = couponsToUpdate;
    }
}