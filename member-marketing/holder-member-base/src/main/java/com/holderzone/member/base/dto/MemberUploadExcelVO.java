package com.holderzone.member.base.dto;

import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.common.qo.grade.AdjustGradeQO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MemberUploadExcelVO implements Serializable {

    @ApiModelProperty("成功条数")
    private Integer success;

    @ApiModelProperty("失败条数")
    private Integer fail;

    @ApiModelProperty("导入记录guid")
    private String importRecordGuid;

    private List<HsaOperationMemberInfo> hsaOperationMemberInfoList;

    @ApiModelProperty("失败信息地址")
    private String failUrl;

    private List<AdjustGradeQO> adjustGrades;
}
