package com.holderzone.member.base.service.member.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.aimilin.bean.ExcelResult;
import com.aimilin.bean.ExcelType;
import com.aimilin.utils.ExcelWriteUtils;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.assembler.MemberAssembler;
import com.holderzone.member.base.assembler.MemberCardAssembler;
import com.holderzone.member.base.dto.MemberInfoImportDTO;
import com.holderzone.member.base.dto.MemberInfoImportSuccessDTO;
import com.holderzone.member.base.dto.MemberUploadExcelVO;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.card.HsaCardOpenRule;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.card.HsaPhysicalCard;
import com.holderzone.member.base.entity.grade.*;
import com.holderzone.member.base.entity.growth.HsaGrowthValueDetail;
import com.holderzone.member.base.entity.growth.HsaGrowthValueTask;
import com.holderzone.member.base.entity.integral.HsaIntegralDetail;
import com.holderzone.member.base.entity.integral.HsaIntegralTask;
import com.holderzone.member.base.entity.member.*;
import com.holderzone.member.base.event.GrowthValueEvent;
import com.holderzone.member.base.event.MemberGradeChangeEvent;
import com.holderzone.member.base.helper.RechargeGiftActivityHelper;
import com.holderzone.member.base.listener.CustomerImportRelationLabelListener;
import com.holderzone.member.base.mapper.card.*;
import com.holderzone.member.base.mapper.coupon.HsaMemberCouponLinkMapper;
import com.holderzone.member.base.mapper.grade.*;
import com.holderzone.member.base.mapper.growth.HsaGrowthValueDetailMapper;
import com.holderzone.member.base.mapper.growth.HsaGrowthValueTaskMapper;
import com.holderzone.member.base.mapper.integral.HsaIntegralDetailMapper;
import com.holderzone.member.base.mapper.integral.HsaIntegralTaskMapper;
import com.holderzone.member.base.mapper.member.*;
import com.holderzone.member.base.service.assembler.MemberIntegralAssembler;
import com.holderzone.member.base.service.base.HsaApplyDictionariesService;
import com.holderzone.member.base.service.cache.CacheService;
import com.holderzone.member.base.service.card.HsaElectronicCardService;
import com.holderzone.member.base.service.card.HsaMemberInfoCardService;
import com.holderzone.member.base.service.grade.*;
import com.holderzone.member.base.service.growth.HsaGrowthValueDetailService;
import com.holderzone.member.base.service.integral.HsaIntegralDetailService;
import com.holderzone.member.base.service.integral.IntegralTaskProcessorService;
import com.holderzone.member.base.service.integral.converter.IntegralCommodityClassifyConverter;
import com.holderzone.member.base.service.member.*;
import com.holderzone.member.base.service.system.OperationLogService;
import com.holderzone.member.base.transform.grade.GradeTransform;
import com.holderzone.member.base.transform.member.MemberInfoTransform;
import com.holderzone.member.base.util.*;
import com.holderzone.member.common.annotation.FunctionLabel;
import com.holderzone.member.common.client.FileOssService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.*;
import com.holderzone.member.common.dto.ali.AliSynMemberDTO;
import com.holderzone.member.common.dto.business.CustomizeFieldDTO;
import com.holderzone.member.common.dto.business.EducationalDTO;
import com.holderzone.member.common.dto.business.MemberSeqFiledDTO;
import com.holderzone.member.common.dto.business.PersonalDTO;
import com.holderzone.member.common.dto.card.ECardCompareExcelDTO;
import com.holderzone.member.common.dto.card.MemberAccountDTO;
import com.holderzone.member.common.dto.card.MemberCardOpenDTO;
import com.holderzone.member.common.dto.card.MemberPhoneDTO;
import com.holderzone.member.common.dto.event.GrowthValueChangeEvent;
import com.holderzone.member.common.dto.excel.FileDto;
import com.holderzone.member.common.dto.excel.HsmMemberUploadExcel;
import com.holderzone.member.common.dto.excel.HsmMemberUploadExcelError;
import com.holderzone.member.common.dto.excel.HsmPartnerMemberUploadExcelError;
import com.holderzone.member.common.dto.grade.MemberGradeRelationDTO;
import com.holderzone.member.common.dto.grade.PayRuleDTO;
import com.holderzone.member.common.dto.integral.MemberIntegralDTO;
import com.holderzone.member.common.dto.integral.PerfectPersonalDetailDTO;
import com.holderzone.member.common.dto.integral.RecordRemainIntegralDTO;
import com.holderzone.member.common.dto.label.MemberLabelRecordDTO;
import com.holderzone.member.common.dto.member.*;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.partner.PartnerReserveMemberInfoDTO;
import com.holderzone.member.common.dto.system.OperationLogDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.*;
import com.holderzone.member.common.enums.card.ElectronicCardOpenTypeEnum;
import com.holderzone.member.common.enums.card.ElectronicCardOpenWayEnum;
import com.holderzone.member.common.enums.card.ElectronicOpenWayEnum;
import com.holderzone.member.common.enums.card.SendCardStateEnum;
import com.holderzone.member.common.enums.equities.BusinessTypeEnum;
import com.holderzone.member.common.enums.equities.EquitiesRuleTypeEnum;
import com.holderzone.member.common.enums.exception.ExceptionEnum;
import com.holderzone.member.common.enums.exception.MemberInfoCardExceptionEnum;
import com.holderzone.member.common.enums.grade.*;
import com.holderzone.member.common.enums.growth.*;
import com.holderzone.member.common.enums.member.*;
import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.*;
import com.holderzone.member.common.qo.card.ListMiniProgramCardQO;
import com.holderzone.member.common.qo.card.MiniProgramCardDTO;
import com.holderzone.member.common.qo.card.OpenElectronicCardQO;
import com.holderzone.member.common.qo.coupon.MemberCouponQO;
import com.holderzone.member.common.qo.equities.MemberEquitiesRecordQO;
import com.holderzone.member.common.qo.grade.AdjustGradeQO;
import com.holderzone.member.common.qo.grade.MemberGradeCardQO;
import com.holderzone.member.common.qo.integral.UpdateIntegralQO;
import com.holderzone.member.common.qo.integral.UpdateIntegralValueQO;
import com.holderzone.member.common.qo.mall.order.MemberSynOrderQO;
import com.holderzone.member.common.qo.member.*;
import com.holderzone.member.common.util.ChineseSubstring;
import com.holderzone.member.common.util.JsonFileUtil;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.excel.MemberReadExcelUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.number.NumberUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import com.holderzone.member.common.util.transaction.SpringContextUtils;
import com.holderzone.member.common.util.verify.VerifyUtil;
import com.holderzone.member.common.vo.base.CountryVO;
import com.holderzone.member.common.vo.base.JobVO;
import com.holderzone.member.common.vo.card.CardInfoBasicVO;
import com.holderzone.member.common.vo.cloud.OperSubjectCloudVO;
import com.holderzone.member.common.vo.excel.ExcelGradeAccountVO;
import com.holderzone.member.common.vo.feign.FeignModel;
import com.holderzone.member.common.vo.grade.*;
import com.holderzone.member.common.vo.member.*;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.member.common.constant.NumberConstant.*;

/**
 * <p>
 * 运营主体会员信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-12
 */
@Slf4j
@Service
@AllArgsConstructor
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
public class HsaOperationMemberInfoServiceImpl extends HolderBaseServiceImpl<HsaOperationMemberInfoMapper, HsaOperationMemberInfo> implements HsaOperationMemberInfoService {

    // 会员或顾客初始化密码
    private static final String payPassword = SecureUtil.md5("123456").toUpperCase();

    private static final String TOKEN_PREFIX = "token_";

    public static final String SAVE_MEMBER_INFO = "SAVE_MEMBER_INFO:";

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private final HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Resource
    private final HsaMemberInfoWeChatMapper hsaMemberInfoWeChatMapper;

    @Resource
    private HsaMemberImportRecordMapper hsaMemberImportRecordMapper;

    @Lazy
    @Resource
    private HsaControlledGradeStateService gradeStateService;

    @Resource
    private RechargeGiftActivityHelper rechargeGiftActivityHelper;

    @Resource
    private GrowthValueEvent growthValueEvent;

    @Resource
    private MemberGradeChangeEvent memberGradeChangeEvent;

    @Lazy
    @Resource
    private SystemRoleHelper systemRoleHelper;

    @Resource
    private MemberIntegralAssembler memberIntegralAssembler;

    @Lazy
    @Resource
    private HsaMemberImportRecordErrorService hsaMemberImportRecordErrorService;

    @Resource
    private HsaMemberCertificateInfoMapper hsaMemberCertificateInfoMapper;

    @Resource
    private HsaApplyDictionariesService hsaApplyDictionariesService;

    @Resource
    private FileOssService fileOssService;

    @Lazy
    @Resource
    private HsaMemberStoreModificationRecordService hsaMemberStoreModificationRecordService;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Lazy
    @Resource
    private HsaMemberCertificateInfoService hsaMemberCertificateInfoService;

    private final HsaMemberInfoCardService hsaMemberInfoCardService;

    @Lazy
    @Resource
    private HsaMemberGradeCardService hsaMemberGradeCardService;

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private MemberMarketingFeign memberMarketingFeign;

    @Lazy
    @Resource
    private HsaElectronicCardService electronicCardService;

    @Resource
    public Executor memberBaseThreadExecutor;

    @Resource
    private SaasStoreFeign saasStoreFeign;

    private static final String reg = "^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$";

    @Resource
    private MemberMallToolFeign memberMallToolFeign;

    @Resource
    private HsaCardOpenRuleMapper hsaCardOpenRuleMapper;

    @Resource
    private HsaCardBaseInfoMapper hsaCardBaseInfoMapper;

    @Resource
    private HsaMemberInfoCardMapper hsaMemberInfoCardMapper;

    @Resource
    private HsaLabelSettingService hsaLabelSettingService;

    @Resource
    private MemberMallFeign memberMallFeign;

    @Resource
    private HsaMemberGradeInfoMapper hsaMemberGradeInfoMapper;

    @Resource
    private HsaLabelSettingMapper hsaLabelSettingMapper;

    @Resource
    private HsaMemberLabelService hsaMemberLabelService;

    @Resource
    private HsaPhysicalCardMapper hsaPhysicalCardMapper;

    @Resource
    private HsaElectronicCardMapper hsaElectronicCardMapper;

    @Resource
    private HsaGradeGiftBagMapper hsaGradeGiftBagMapper;

    @Resource
    private HsaMemberLabelMapper hsaMemberLabelMapper;

    @Resource
    private HsaMemberConsumptionMapper hsaMemberConsumptionMapper;

    @Resource
    private HsaGrowthValueTaskMapper hsaGrowthValueTaskMapper;

    @Resource
    private HsaGrowthValueDetailMapper hsaGrowthValueDetailMapper;

    @Resource
    private HsaGrowthValueDetailService hsaGrowthValueDetailService;

    @Resource
    private HsaMemberGradeChangeDetailService hsaMemberGradeChangeDetailService;

    @Resource
    private HsaMemberGradeRightsRecordMapper hsaMemberGradeRightsRecordMapper;

    @Resource
    private HsaGiftBagBaseInfoMapper hsaGiftBagBaseInfoMapper;

    @Resource
    private HsaBusinessEquitiesMapper hsaBusinessEquitiesMapper;

    @Autowired
    @Lazy
    private IntegralTaskProcessorService integralTaskProcessorService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private HsaMemberEquitiesReceiveRecordMapper hsaMemberEquitiesReceiveRecordMapper;

    @Resource
    private HsaIntegralDetailService hsaIntegralDetailService;

    @Resource
    private HsaIntegralTaskMapper hsaIntegralTaskMapper;

    @Resource
    private HsaMemberLabelRecordService hsaMemberLabelRecordService;

    /**
     * 英文分号 ;
     */
    private static final String SEMICOLON = ";";

    private static final String NO_AFFILIATED_STORE = "无归属门店";

    @Resource
    private HsaIntegralDetailMapper hsaIntegralDetailMapper;

    @Resource
    private HsaBusinessEquitiesService hsaBusinessEquitiesService;

    @Resource
    private CacheService cacheService;

    @Resource
    private HsaKinsfolkManagementService hsaKinsfolkManagementService;

    @Resource
    private HsaKinsfolkManagementMapper hsaKinsfolkManagementMapper;

    @Resource
    private BaiDuYunUtil baiDuYunUtil;

    @Autowired
    @Lazy
    private HsaMemberEducationService educationService;
    @Resource
    private HsaMemberPersonalService personalService;
    @Resource
    private HsaCustomizeFieldService customizeFieldService;
    @Resource
    private HsaMemberLabelService labelService;
    @Resource
    private PartnerPlatformFeign partnerPlatformFeign;
    @Autowired
    @Lazy
    private IHsaMemberGradeRelationService memberGradeRelationService;

    @Autowired
    @Lazy
    private IHsaMemberGradePayRecordService memberGradePayRecordService;

    @Autowired
    private IHsaMemberGradeInfoService memberGradeInfoService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private ExternalSupport externalSupport;

    @Resource
    private HsaMemberCouponLinkMapper memberCouponLinkMapper;

    @Resource
    private HsaMemberGradeCardMapper hsaMemberGradeCardMapper;

    @Override
    public String saveMemberInfo(SaveOperationMemberInfoQO request) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        HsaOperationMemberInfo operationMemberInfo = new HsaOperationMemberInfo();
        //成长值基础任务
        if (operationMemberInfo.getSourceType() != SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode()) {
            memberBaseThreadExecutor.execute(() -> sendMemberGrowthValue(Collections.singletonList(operationMemberInfo), operationMemberInfo.getSourceType(), 0, headerUserInfo));
        }
        return operationMemberInfo.getGuid();
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public String saveOperationMemberInfo(SaveOperationMemberInfoQO request) {
        // 会员注册增加重复提交锁
        RLock lock = redissonClient.getLock(SAVE_MEMBER_INFO + request.getPhoneNum());
        try {
            if (lock.isLocked()) {
                throw new MemberBaseException(String.format("手机号%s重复注册，稍后再试", request.getPhoneNum()));
            }
            lock.lock();
            HsaOperationMemberInfoService hsaOperationMemberInfoService = SpringContextUtils.getBean(HsaOperationMemberInfoService.class);
            return hsaOperationMemberInfoService.saveOperationMemberInfoImpl(request);
        } catch (MemberBaseException baseException) {
            throw baseException;
        } catch (Exception exception) {
            log.error("注册会员失败exception=", exception);
            throw new MemberBaseException(exception.getMessage());
        } finally {
            //释放锁资源
            if (lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public String saveOperationMemberInfoImpl(SaveOperationMemberInfoQO request) {
        HsaOperationMemberInfo operationMemberInfo = new HsaOperationMemberInfo();
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        log.info("会员注册参数 ：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", JSON.toJSONString(request));
        ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));
        //统一参数
        resourcesUnified(request, headerUserInfo);
        List<HsaMemberCertificateInfo> hsaMemberCertificateInfos = Lists.newArrayList();
        // 验证
        validateOperSubjectByPhone(hsaMemberCertificateInfos, request);
        memberInfoPackage(request, operationMemberInfo);
        //初始化积分
        UpdateIntegralValueQO updateIntegralValueQO = new UpdateIntegralValueQO(
                SumValueChangeEnum.INITIAL.getCode(),
                NumberConstant.NUMBER_0,
                request.getMemberIntegral(),
                null,
                request.getStoreName(),
                null);
        initMemberIntegral(operationMemberInfo, ThreadLocalCache.getHeaderUserInfo(), updateIntegralValueQO);
        String memberInfoGuid = operationMemberInfo.getGuid();
        if (ThreadLocalCache.isPartner()) {
            // 角色类型处理
            dealPartner(request, operationMemberInfo, memberInfoGuid, headerUserInfo);
        }
        //初始成长值记录以及等级
        sendInitialMemberGrowthValue(operationMemberInfo, headerUserInfo, request);
        //完善了个人信息
        PerfectPersonalDetailDTO perfectPersonalDetailDTO = new PerfectPersonalDetailDTO();
        if (operationMemberInfo.getSex() != 0) {
            perfectPersonalDetailDTO.setPerfectSexTime(DateUtil.formatLocalDateTime(LocalDateTime.now(), DateUtil.PATTERN_DATETIME));
        }
        if (!Objects.isNull(operationMemberInfo.getBirthday())) {
            perfectPersonalDetailDTO.setPerfectBirthdayTime(DateUtil.formatLocalDateTime(LocalDateTime.now(), DateUtil.PATTERN_DATETIME));
        }
        operationMemberInfo.setPerfectPersonalDetailJson(JSON.toJSONString(perfectPersonalDetailDTO));

        //同步支付宝会员
        AliSynMemberDTO aliSynMemberDTO = new AliSynMemberDTO();
        aliSynMemberDTO.setAvatar(operationMemberInfo.getHeadImgUrl())
                .setPhone(operationMemberInfo.getPhoneNum())
                .setNickName(operationMemberInfo.getNickName());
        synSaasMember(aliSynMemberDTO, request.getOpenId());
        save(operationMemberInfo);

        memberBaseThreadExecutor.execute(() -> dealPosition(request, hsaMemberCertificateInfos, operationMemberInfo, headerUserInfo, memberInfoGuid));
        return operationMemberInfo.getGuid();
    }

    private void dealPartner(SaveOperationMemberInfoQO request, HsaOperationMemberInfo operationMemberInfo, String memberInfoGuid, HeaderUserInfo headerUserInfo) {
        List<String> roleTypeList = Lists.newArrayList(RoleTypeEnum.MEMBER.name());
        List<String> merchantPhoneNumList = partnerPlatformFeign.getAvailablePhones(Lists.newArrayList(operationMemberInfo.getPhoneNum()));
        if (CollectionUtils.isNotEmpty(merchantPhoneNumList) && merchantPhoneNumList.contains(operationMemberInfo.getPhoneNum())) {
            roleTypeList.add(RoleTypeEnum.MERCHANT.name());
        }
        operationMemberInfo.setRoleType(RoleTypeEnum.getRoleTypeToDO(roleTypeList));

        // 保存名片信息
        memberBaseThreadExecutor.execute(() -> saveBusinessInfo(request, memberInfoGuid, headerUserInfo));
    }

    private void dealPosition(SaveOperationMemberInfoQO request, List<HsaMemberCertificateInfo> hsaMemberCertificateInfos, HsaOperationMemberInfo operationMemberInfo, HeaderUserInfo headerUserInfo, String memberInfoGuid) {
        ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));
        //保存证件信息
        saveMemberCertificateInfo(hsaMemberCertificateInfos, operationMemberInfo);
        //是否绑定电子卡
        Map<String, HsaPhysicalCard> hsaPhysicalCards = isOpenCard(request, operationMemberInfo);
        //去开通电子卡
        memberBaseThreadExecutor.execute(() -> openRegisterCard(request, operationMemberInfo, hsaPhysicalCards, headerUserInfo));

        List<HsaLabelSetting> hsaLabelSettings = null;
        if (CollectionUtils.isNotEmpty(request.getMenberLable())) {
            hsaLabelSettings = hsaLabelSettingService.queryByGuids(request.getMenberLable());
        }
        //新增会员，添加标签
        hsaLabelSettingService.refreshLabel(Collections.singletonList(memberInfoGuid), hsaLabelSettings, BooleanEnum.FALSE.getCode(), operationMemberInfo.getSourceType(), LabelTriggerTypeEnum.REGISTER_INFO.getCode());
        //保存日志信息
        OperationLogDTO logDTO = getOperationLogDTO(headerUserInfo, operationMemberInfo);
        operationLogService.dealBusinessLog(logDTO);
        //成长值基础任务
        if (operationMemberInfo.getSourceType() != SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode()) {
            memberBaseThreadExecutor.execute(() -> sendMemberGrowthValue(Collections.singletonList(operationMemberInfo), operationMemberInfo.getSourceType(), 0, headerUserInfo));
        }
        // 付费会员等级卡信息初始化 生成付费记录
        if (ObjectUtil.isNotEmpty(operationMemberInfo.getMemberPaidGradeInfoGuid())) {
            hsaMemberGradeCardService.addCardAndSavePurchaseHis(new MemberGradeCardQO().setMemberInfoGuid(memberInfoGuid)
                    .setGradeGuid(operationMemberInfo.getMemberPaidGradeInfoGuid())
                    .setGradeName(operationMemberInfo.getMemberPaidGradeInfoName())
                    .setEffectiveDurationType(request.getEffectiveDurationType())
                    .setChangeType(GradeHisChangeTypeEnum.PAID_MEMBER_INIT.getValue()));
        }
    }

    @Override
    public void synSaasMember(AliSynMemberDTO aliSynMemberDTO, String openId) {
        // 检查日志，同步支付宝渠道会员
        log.info("同步支付宝渠道会员:{}", ThreadLocalCache.getSource());
        // 检查日志，operSubjectGuid
        log.info("operSubjectGuid:{}", ThreadLocalCache.getOperSubjectGuid());

        // 检查条件，获取开关信息
        if (ThreadLocalCache.getSource() == SourceTypeEnum.ADD_ALI_PAY.getCode()) {
            OperSubjectCloudVO oerOperSubjectCloudVO = memberMallToolFeign.queryByOperSubiectGuid(ThreadLocalCache.getOperSubjectGuid());
            // 检查日志，oerOperSubjectCloudVO
            log.info("oerOperSubjectCloudVO:{}", JSON.toJSONString(oerOperSubjectCloudVO));

            // 判断是否存在多门店操作SubjectGuid
            if (StringUtil.isNotEmpty(oerOperSubjectCloudVO.getMultiOperSubiectGuid())) {

                // 设置入参
                aliSynMemberDTO
                        .setUserId(openId)
                        .setOperSubjectGuid(oerOperSubjectCloudVO.getMultiOperSubiectGuid())
                        .setEnterpriseGuid(oerOperSubjectCloudVO.getMultiEnterpriseGuid());
                // 检查日志，同步门店会员入参数
                log.info("同步门店会员入参数:{}", JSON.toJSONString(aliSynMemberDTO));

                // 调用门店会员同步API
                FeignModel<Boolean> booleanFeignModel = saasStoreFeign.synSaasMember(aliSynMemberDTO);
                // 检查日志，同步门店会员返回参数
                log.info("同步门店会员返回参数:{}", JSON.toJSONString(booleanFeignModel));

                // 检查返回结果，如果门店会员同步返回失败，则抛出异常
                if (booleanFeignModel.getCode() != BooleanEnum.FALSE.getCode()) {
                    throw new MemberBaseException("同步门店会员失败");
                }
            }
        }
    }

    @Override
    public ResponseMemberInfo getSaasMember(String phone, String openId) {
        // 查询关联餐饮云配置
        OperSubjectCloudVO oerOperSubjectCloudVO = memberMallToolFeign.queryByOperSubiectGuid(ThreadLocalCache.getOperSubjectGuid());
        log.info("查询关联餐饮云配置返回:{}", JacksonUtils.writeValueAsString(oerOperSubjectCloudVO));
        // 判断是否存在多门店操作SubjectGuid
        if (Objects.isNull(oerOperSubjectCloudVO) || StringUtil.isEmpty(oerOperSubjectCloudVO.getMultiOperSubiectGuid())) {
            return null;
        }
        log.info("查询关联餐饮云会员信息入参,phone:{}, openId:{}, multiOperSubiectGuid:{}", phone, openId, oerOperSubjectCloudVO.getMultiOperSubiectGuid());
        FeignModel<ResponseMemberInfo> responseOperationMemberInfoFeignModel = saasStoreFeign
                .getMemberInfoByOpenIdAndPhone(openId, phone, oerOperSubjectCloudVO.getMultiOperSubiectGuid());
        log.info("查询关联餐饮云会员信息返回:{}", JacksonUtils.writeValueAsString(responseOperationMemberInfoFeignModel));
        if (responseOperationMemberInfoFeignModel.getCode() != BooleanEnum.FALSE.getCode()) {
            log.error("查询关联餐饮云会员信息异常, e:{}", responseOperationMemberInfoFeignModel.getMessage());
            return null;
        }
        return responseOperationMemberInfoFeignModel.getData();
    }


    private void saveBusinessInfo(SaveOperationMemberInfoQO request, String memberGuid, HeaderUserInfo headerUserInfo) {
        ThreadLocalCache.put(JSON.toJSONString(headerUserInfo));
        saveOrUpdateBusinessInfo(request.getEducationalExperience(), request.getPersonalExperience(), request.getCustomize(), memberGuid);
    }

    private void saveOrUpdateBusinessInfo(List<EducationalDTO> educationalExperience,
                                          List<PersonalDTO> personalExperience, List<CustomizeFieldDTO> customize,
                                          String memberGuid) {
        educationService.batchHandleEducational(educationalExperience, memberGuid);
        personalService.batchHandlePersonal(personalExperience, memberGuid);
        if (CollectionUtils.isNotEmpty(customize)) {
            customize.forEach(c -> customizeFieldService.dealData(c, memberGuid));
        }
    }

    /**
     * 会员注册 初始成长值
     *
     * @param operationMemberInfo operationMemberInfo
     */
    private void sendInitialMemberGrowthValue(HsaOperationMemberInfo operationMemberInfo, HeaderUserInfo headerUserInfo, SaveOperationMemberInfoQO request) {
        HsaControlledGradeState hsaControlledGradeState = gradeStateService.getOne(new LambdaQueryWrapper<HsaControlledGradeState>()
                .eq(HsaControlledGradeState::getOperSubjectGuid, operationMemberInfo.getOperSubjectGuid())
                .eq(HsaControlledGradeState::getRoleType, RoleTypeEnum.MEMBER.name())
                .eq(HsaControlledGradeState::getIsDelete, BooleanEnum.FALSE.getCode())
        );
        HsaGrowthValueDetail hsaGrowthValueDetail = constructGrowthValueDetail(operationMemberInfo, headerUserInfo);
        if (ThreadLocalCache.isPartner()) {
            if (StringUtils.isEmpty(request.getMemberGradeInfoGuid())) {
                hsaGrowthValueDetail.setUpgradeType(UpgradeTypeEnum.PAY.getCode());
                hsaGrowthValueDetail.setMemberInfoGradeGuid("");
                hsaGrowthValueDetail.setCurrentMemberLevel("");
            } else {
                HsaMemberGradeInfo memberGradeInfo = hsaMemberGradeInfoMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                        .eq(HsaMemberGradeInfo::getGuid, request.getMemberGradeInfoGuid())
                        .eq(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode()));
                complementGrowthValueDetail(memberGradeInfo, hsaGrowthValueDetail);
                if (UpgradeTypeEnum.PAY.getCode() == memberGradeInfo.getUpgradeType()) {
                    hsaGrowthValueDetail.setExpireTime(DateUtil.getFirstExpireTime(request.getNum(), request.getUnit()));
                }
            }
            hsaGrowthValueDetail.setNum(request.getNum());
            hsaGrowthValueDetail.setUnit(request.getUnit());
            hsaGrowthValueDetailService.save(hsaGrowthValueDetail);
            initMemberGrade(request, operationMemberInfo, hsaControlledGradeState);
            return;
        }
        if (Objects.nonNull(operationMemberInfo.getMemberGrowthValue()) && operationMemberInfo.getMemberGrowthValue() > 0) {
            HsaMemberGradeInfo hsaMemberGradeInfo = getHsaMemberGradeInfo(operationMemberInfo, hsaControlledGradeState);
            complementGrowthValueDetail(hsaMemberGradeInfo, hsaGrowthValueDetail);
            hsaGrowthValueDetailService.save(hsaGrowthValueDetail);
        } else {
            operationMemberInfo.setMemberGrowthValue(NUMBER_0);
        }
        initMemberGrade(request, operationMemberInfo, hsaControlledGradeState);
    }

    private HsaMemberGradeInfo getHsaMemberGradeInfo(HsaOperationMemberInfo operationMemberInfo, HsaControlledGradeState hsaControlledGradeState) {
        HsaMemberGradeInfo hsaMemberGradeInfo;
        if (StringUtils.isEmpty(operationMemberInfo.getMemberGradeInfoGuid()) || Objects.equals(hsaControlledGradeState.getState(), EnableEnum.NOT_ENABLE.getCode())) {
            hsaMemberGradeInfo = hsaMemberGradeInfoMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                    .eq(HsaMemberGradeInfo::getOperSubjectGuid, operationMemberInfo.getOperSubjectGuid())
                    .eq(HsaMemberGradeInfo::getRoleType, RoleTypeEnum.MEMBER.name())
                    .in(HsaMemberGradeInfo::getIsDelete, NUMBER_0, NUMBER_2)
                    .eq(HsaMemberGradeInfo::getEffective, NUMBER_1)
                    .eq(HsaMemberGradeInfo::getVipGrade, NUMBER_1));
        } else {
            hsaMemberGradeInfo = hsaMemberGradeInfoMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                    .eq(HsaMemberGradeInfo::getGuid, operationMemberInfo.getMemberGradeInfoGuid())
                    .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), NUMBER_2)
                    .eq(HsaMemberGradeInfo::getEffective, BooleanEnum.TRUE.getCode()));
        }
        return hsaMemberGradeInfo;
    }

    private void complementGrowthValueDetail(HsaMemberGradeInfo memberGradeInfo, HsaGrowthValueDetail hsaGrowthValueDetail) {
        if (!ObjectUtils.isEmpty(memberGradeInfo)) {
            hsaGrowthValueDetail.setCurrentMemberLevel(memberGradeInfo.getName() + StringConstant.LEFT_BRACKET +
                    StringConstant.VIP + memberGradeInfo.getVipGrade() + StringConstant.RIGHT_BRACKET);
            hsaGrowthValueDetail.setMemberInfoGradeGuid(memberGradeInfo.getGuid());
            hsaGrowthValueDetail.setUpgradeType(memberGradeInfo.getUpgradeType());
        }
    }

    private HsaGrowthValueDetail constructGrowthValueDetail(HsaOperationMemberInfo operationMemberInfo, HeaderUserInfo headerUserInfo) {
        HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        hsaGrowthValueDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaGrowthValueDetail.class.getSimpleName()));
        hsaGrowthValueDetail.setGrowthValueSourceType(operationMemberInfo.getSourceType());
        if (operationMemberInfo.getSourceType() == SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode()) {
            hsaGrowthValueDetail.setOperatorAccountName(operationMemberInfo.getPhoneNum());
        } else {
            hsaGrowthValueDetail.setOperatorAccountName(headerUserInfo.getUserName() + StringConstant.STR_BIAS + headerUserInfo.getTel());
            hsaGrowthValueDetail.setStoreGuid(headerUserInfo.getStoreGuid());
            hsaGrowthValueDetail.setStoreName(headerUserInfo.getStoreName());
        }
        hsaGrowthValueDetail.setOperSubjectGuid(operationMemberInfo.getOperSubjectGuid());
        hsaGrowthValueDetail.setChangeType(SumValueChangeEnum.INITIAL.getCode());
        hsaGrowthValueDetail.setRecordDeclaration(RegisterChannelEnum.getNameByCode(operationMemberInfo.getSourceType()));
        hsaGrowthValueDetail.setGrowthValue(ObjectUtils.isEmpty(operationMemberInfo.getMemberGrowthValue()) ?
                NUMBER_0 : operationMemberInfo.getMemberGrowthValue());
        hsaGrowthValueDetail.setMemberInfoGuid(operationMemberInfo.getGuid());
        hsaGrowthValueDetail.setGrowthValidity(NUMBER_0);
        hsaGrowthValueDetail.setGrowthValueType(NUMBER_0);
        hsaGrowthValueDetail.setRemainGrowthValue(ObjectUtils.isEmpty(operationMemberInfo.getMemberGrowthValue()) ?
                NUMBER_0 : operationMemberInfo.getMemberGrowthValue());
        hsaGrowthValueDetail.setRecordRemainGrowthValue(ObjectUtils.isEmpty(operationMemberInfo.getMemberGrowthValue()) ?
                NUMBER_0 : operationMemberInfo.getMemberGrowthValue());
        hsaGrowthValueDetail.setRoleType(RoleTypeEnum.MEMBER.name());
        return hsaGrowthValueDetail;
    }

    /**
     * 初始化积分、积分调整
     *
     * @param operationMemberInfo 运营主体会员信息表
     * @param headerUserInfo      请求头用户信息
     */
    private void initMemberIntegral(HsaOperationMemberInfo operationMemberInfo,
                                    HeaderUserInfo headerUserInfo,
                                    UpdateIntegralValueQO updateIntegralValueQO) {
        if ((Objects.nonNull(operationMemberInfo.getMemberIntegral())
                && operationMemberInfo.getMemberIntegral() != 0)
                || updateIntegralValueQO.getIntegralType() == 1) {
            //初始化积分记录
            HsaIntegralDetail hsaIntegralDetail = memberIntegralAssembler.initIntegralDetail(
                    operationMemberInfo,
                    headerUserInfo,
                    updateIntegralValueQO);
            hsaIntegralDetailService.save(hsaIntegralDetail);
        } else {
            operationMemberInfo.setMemberIntegral(NUMBER_0);
        }
        if (updateIntegralValueQO.getChangeType() == SumValueChangeEnum.INITIAL.getCode()) {
            checkRegisterAndPerfectInfo(operationMemberInfo, headerUserInfo, operationMemberInfo.getSourceType());
        }
    }


    private void checkRegisterAndPerfectInfo(HsaOperationMemberInfo operationMemberInfo, HeaderUserInfo headerUserInfo, Integer sourceType) {
        //注册任务
        HsaIntegralTask registerTask = hsaIntegralTaskMapper.findTaskByTaskAction(operationMemberInfo.getOperSubjectGuid(), TaskActionEnum.REGISTER.getCode());
        dealIntegralTask(operationMemberInfo, headerUserInfo, registerTask, sourceType);

        //完善个人信息任务
        HsaIntegralTask perfectPersonalDetailsTask = hsaIntegralTaskMapper.findTaskByTaskAction(operationMemberInfo.getOperSubjectGuid(), TaskActionEnum.PERFECT_PERSONAL_DETAILS.getCode());
        dealIntegralTask(operationMemberInfo, headerUserInfo, perfectPersonalDetailsTask, sourceType);
    }

    @Override
    public HsaIntegralDetail dealIntegralTask(HsaOperationMemberInfo operationMemberInfo,
                                              HeaderUserInfo headerUserInfo,
                                              HsaIntegralTask hsaIntegralTask,
                                              Integer sourceType) {
        HsaIntegralDetail hsaIntegralDetail = new HsaIntegralDetail();
        if (!checkHsaIntegralTask(operationMemberInfo, hsaIntegralTask, sourceType)) {
            calculateIntegralDetail(operationMemberInfo, headerUserInfo, hsaIntegralTask, sourceType, hsaIntegralDetail);
        }
        return hsaIntegralDetail;
    }

    private void calculateIntegralDetail(HsaOperationMemberInfo operationMemberInfo,
                                         HeaderUserInfo headerUserInfo,
                                         HsaIntegralTask hsaIntegralTask,
                                         Integer sourceType,
                                         HsaIntegralDetail hsaIntegralDetail) {
        Integer integralValueVo = hsaIntegralTask.getIntegralValue();
        integralValueVo = isDoubleGrowthValue(integralValueVo, operationMemberInfo, hsaIntegralDetail);
        operationMemberInfo.setMemberIntegral(com.holderzone.member.common.util.verify.ObjectUtil.objToInt(operationMemberInfo.getMemberIntegral()) + integralValueVo);
        boolean issueCode = memberIntegralAssembler.getMemberIntegralDetailNew(operationMemberInfo, headerUserInfo, hsaIntegralTask, sourceType, hsaIntegralDetail, integralValueVo);
        if (issueCode) {
            hsaOperationMemberInfoMapper.updateByGuid(operationMemberInfo);
        }
    }

    private boolean checkHsaIntegralTask(HsaOperationMemberInfo operationMemberInfo, HsaIntegralTask hsaIntegralTask, Integer sourceType) {
        if (Objects.isNull(hsaIntegralTask)) {
            return true;
        }
        //注册任务 不满足条件
        if (hsaIntegralTask.getTaskAction() == TaskActionEnum.REGISTER.getCode() && !checkGrowValueRegisterTask(sourceType, hsaIntegralTask.getTaskValidityType(), hsaIntegralTask.getStartFixedTaskValidityDate(), hsaIntegralTask.getEndFixedTaskValidityDate(), hsaIntegralTask.getSourceTypeJson())) {
            return true;
        }
        //完善个人信息 不满足条件
        return hsaIntegralTask.getTaskAction() == TaskActionEnum.PERFECT_PERSONAL_DETAILS.getCode() && !checkGrowValuePerfectTask(hsaIntegralTask.getTaskValidityType(), hsaIntegralTask.getStartFixedTaskValidityDate(), hsaIntegralTask.getEndFixedTaskValidityDate(), hsaIntegralTask.getPersonalDetailsTypeJson(), operationMemberInfo);
    }

    /**
     * 是否获取翻倍成长值
     *
     * @param growthValue            成长值
     * @param hsaOperationMemberInfo 操作会员
     * @param hsaIntegralDetail      积分详情记录
     * @return 获取的成长值
     */
    @Override
    public int isDoubleGrowthValue(Integer growthValue, HsaOperationMemberInfo hsaOperationMemberInfo, HsaIntegralDetail hsaIntegralDetail) {

        // 会员等级被禁用,不能使用成长值
        if (gradeStateService.isNotEnable(hsaOperationMemberInfo.getOperSubjectGuid())) {
            return growthValue;
        }
        // 会员guid
        String memberInfoGuid = hsaOperationMemberInfo.getGuid();
        String memberGradeInfoGuid = getMemberGradeInfoGuid(hsaOperationMemberInfo);
        //查询等级权益基础信息
        DoubleGradeEquitiesVO equitiesInfo = hsaMemberGradeInfoMapper.getGradeEquitiesGuid(hsaOperationMemberInfo.getOperSubjectGuid(), memberGradeInfoGuid, EquitiesRuleTypeEnum.DOUBLE_INTEGRAL.getCode());
        //判断
        if (Objects.isNull(growthValue) || Objects.isNull(equitiesInfo) || Objects.isNull(equitiesInfo.getEquitiesGuid())) {
            return growthValue;
        }
        //设置请求
        DoubleValueRequest doubleValueRequest = setDoubleValueRequest(hsaOperationMemberInfo, growthValue, equitiesInfo.getEquitiesGuid());
        Long doubleGrowthValue = hsaBusinessEquitiesService.isDoubleGrowthValue(doubleValueRequest);
        //双倍成长值为空
        if (Objects.isNull(doubleGrowthValue) || doubleGrowthValue < 0) {
            return growthValue;
        }

        growthValue = (int) (doubleGrowthValue + growthValue);
        hsaIntegralDetail.setMultipleEquitiesGuid(equitiesInfo.getEquitiesGuid());
        hsaIntegralDetail.setMultipleIntegral(equitiesInfo.getDoubleNumber());
        hsaIntegralDetail.setMultipleIntegralValue(doubleGrowthValue.intValue());
        log.info("memberInfoGuid：{},获得翻倍成长值:{}", memberInfoGuid, growthValue);
        return growthValue;
    }

    private String getMemberGradeInfoGuid(HsaOperationMemberInfo hsaOperationMemberInfo) {
        String memberGradeInfoGuid = hsaOperationMemberInfo.getMemberGradeInfoGuid();

        // 注册时，如果未输入成长值，会员等级guid会没有
        // 所以需要给一个默认会员等级
        if (StringUtils.isEmpty(memberGradeInfoGuid)) {
            HsaMemberGradeInfo hsaMemberGradeInfo = hsaMemberGradeInfoMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradeInfo>().eq(HsaMemberGradeInfo::getOperSubjectGuid, hsaOperationMemberInfo.getOperSubjectGuid()).in(HsaMemberGradeInfo::getIsDelete, NUMBER_0, NUMBER_2).eq(HsaMemberGradeInfo::getEffective, NUMBER_1)
                    .eq(HsaMemberGradeInfo::getType, GradeTypeEnum.FREE.getCode())
                    .eq(HsaMemberGradeInfo::getVipGrade, NUMBER_1));
            memberGradeInfoGuid = hsaMemberGradeInfo.getGuid();
        }
        return memberGradeInfoGuid;
    }

    /**
     * 封装翻倍成长值请求参数
     *
     * @param hsaOperationMemberInfo 会员信息
     * @param growthValue            增加的成长值
     * @param equitiesGuid           权益guid
     * @return 封装数据
     */
    private DoubleValueRequest setDoubleValueRequest(HsaOperationMemberInfo hsaOperationMemberInfo, Integer growthValue, String equitiesGuid) {
        if (Objects.isNull(growthValue) || Objects.isNull(hsaOperationMemberInfo)) {
            return null;
        }
        DoubleValueRequest doubleValueRequest = new DoubleValueRequest();
        doubleValueRequest.setMemberInfoGuid(hsaOperationMemberInfo.getGuid());
        doubleValueRequest.setGrowthValue(growthValue);
        doubleValueRequest.setGradeEquitiesGuid(equitiesGuid);
        doubleValueRequest.setOperSubjectGuid(hsaOperationMemberInfo.getOperSubjectGuid());
        return doubleValueRequest;
    }

    /**
     * 查询人脸会员列表
     *
     * @param memberListQO 查询会员列表QO
     * @return
     */
    @Override
    public PageResult<TerFaceMemberInfoVO> terListMemberInfo(TerFaceMemberListQO memberListQO) {
        PageResult<TerFaceMemberInfoVO> pageResult = new PageResult<>();
        pageResult.setCurrent(memberListQO.getCurrentPage());
        pageResult.setSize(memberListQO.getPageSize());

        memberListQO.setCurrentPage((memberListQO.getCurrentPage() - 1) * memberListQO.getPageSize());


        memberListQO.setOperSubjectGuid(ThreadLocalCache.getHeaderUserInfo().getOperSubjectGuid());
        int num = hsaOperationMemberInfoMapper.terMemberInfoNum(memberListQO);
        pageResult.setTotal(num);
        if (num == 0) {
            return pageResult;
        }
        List<TerFaceMemberInfoVO> terListMemberInfo = hsaOperationMemberInfoMapper.terListMemberInfo(memberListQO);
        pageResult.setRecords(terListMemberInfo);
        return pageResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    //@RedissonLock(lockName = "UPDATE_MEMBER_INTEGRAL", tryLock = true, leaseTime = 10)
    public Boolean updateMemberIntegral(UpdateIntegralQO request) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        log.info("headerUserInfo:{}", headerUserInfo);
        List<HsaOperationMemberInfo> memberList = hsaOperationMemberInfoMapper.queryByGuids(request.getMemberInfoGuids());
        if (CollectionUtils.isEmpty(memberList)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_EXIST_MEMBER);
        }
        if (request.getIntegral() == 0) {
            throw new MemberBaseException(MemberAccountExceptionEnum.INTEGRAL_REDUCE_NOT_BE_ZERO);
        }
        //处理会员积分变动
        forDealMemberIntegral(request, memberList, headerUserInfo);

        hsaOperationMemberInfoMapper.batchUpdateIntegral(memberList);
        //新增会员，添加标签
        hsaLabelSettingService.refreshLabel(request.getMemberInfoGuids(), null, BooleanEnum.FALSE.getCode(), null, LabelTriggerTypeEnum.INTEGRAL_INFO.getCode());
        return Boolean.TRUE;
    }

    private void forDealMemberIntegral(UpdateIntegralQO request, List<HsaOperationMemberInfo> memberList, HeaderUserInfo headerUserInfo) {
        for (HsaOperationMemberInfo memberInfo : memberList) {
            Integer memberIntegral;
            //积分扣减
            if (request.getIntegralType() == 1) {
                if (Objects.isNull(memberInfo.getMemberIntegral()) || memberInfo.getMemberIntegral() == 0) {
                    continue;
                }
                if (memberInfo.getMemberIntegral() - request.getIntegral() < 0) {
                    memberIntegral = memberInfo.getMemberIntegral();
                } else {
                    memberIntegral = request.getIntegral();
                }
                memberInfo.setMemberIntegral(memberInfo.getMemberIntegral() - memberIntegral);
                reduceIntegral(memberInfo.getGuid(), memberIntegral);
            } else {
                memberIntegral = request.getIntegral();
                memberInfo.setMemberIntegral(memberInfo.getMemberIntegral() + memberIntegral);
            }

            int sumValueChange = SumValueChangeEnum.SYSTEM.getCode();
            if (Objects.nonNull(request.getSourceType())) {
                sumValueChange = request.getSourceType();
            }

            String storeName = headerUserInfo.getStoreName();
            if (StringUtils.isEmpty(storeName)) {
                storeName = StringConstant.STR_BIAS_TWO;
            }

            UpdateIntegralValueQO updateIntegralValueQO = new UpdateIntegralValueQO(
                    sumValueChange,
                    request.getIntegralType(),
                    request.getIntegral(),
                    request.getRemark(),
                    storeName,
                    request.getOrderNumber());
            initMemberIntegral(
                    memberInfo,
                    headerUserInfo,
                    updateIntegralValueQO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean memberFaceRegistered(MemberHumanFace request) {
        BaiduFaceEntryDTO baiduFaceEntryDTO = baiDuYunUtil.faceEntry(request.getMemberInfoGuid(), request.getGroupId(), request.getImage());
        if (Objects.isNull(baiduFaceEntryDTO)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_FACE_ENTRY_ERROR);
        }
        Map<String, Object> resultMap = baiduFaceEntryDTO.getResult();
        if (Objects.isNull(resultMap) || !resultMap.containsKey(StringConstant.FACE_TOKEN)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_FACE_ENTRY_ERROR);
        }

        HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(request.getMemberInfoGuid());
        if (org.apache.commons.lang.StringUtils.isNotBlank(hsaOperationMemberInfo.getFaceToken())) {
            baiDuYunUtil.faceToDelete(hsaOperationMemberInfo.getLogId(), hsaOperationMemberInfo.getUserId(),
                    request.getGroupId(), hsaOperationMemberInfo.getFaceToken());
        }
        String logId = baiduFaceEntryDTO.getLogId();
        String faceToken = resultMap.get(StringConstant.FACE_TOKEN).toString();

        hsaOperationMemberInfo.setLogId(logId);
        hsaOperationMemberInfo.setFaceToken(faceToken);
        hsaOperationMemberInfo.setUserId(request.getMemberInfoGuid());
        hsaOperationMemberInfoMapper.updateByGuid(hsaOperationMemberInfo);
        return true;
    }

    @Override
    public boolean terMemberFaceRegistered(MemberHumanFace request) {
        HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(request.getMemberInfoGuid());
        hsaOperationMemberInfo.setUserId(request.getMemberInfoGuid());
        hsaOperationMemberInfo.setFaceToken(request.getMemberInfoGuid());
        return hsaOperationMemberInfoMapper.updateByGuid(hsaOperationMemberInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean memberFaceClear(MemberHumanFace request) {
        HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(request.getMemberInfoGuid());
        if (org.apache.commons.lang.StringUtils.isEmpty(hsaOperationMemberInfo.getFaceToken())) {
            return false;
        }
        //人脸机删除
        if (hsaOperationMemberInfo.getGuid().equals(hsaOperationMemberInfo.getFaceToken())) {
            hsaOperationMemberInfo.setUserId(null);
            hsaOperationMemberInfo.setFaceToken(null);
            return hsaOperationMemberInfoMapper.updateByGuid(hsaOperationMemberInfo);
        }
        // 删除百度云 ---人脸识别库中的当前用户人脸信息
        String logId = hsaOperationMemberInfo.getLogId();
        String userId = hsaOperationMemberInfo.getUserId();
        if (org.apache.commons.lang3.StringUtils.isEmpty(logId) || org.apache.commons.lang3.StringUtils.isEmpty(userId)) {
            log.error("核心参数为空 logId：{} ,userId:{}", logId, userId);
            return false;
        }
        baiDuYunUtil.faceToDelete(hsaOperationMemberInfo.getLogId(), hsaOperationMemberInfo.getUserId(), request.getGroupId(), hsaOperationMemberInfo.getFaceToken());
        hsaOperationMemberInfo.setLogId(null);
        hsaOperationMemberInfo.setUserId(null);
        hsaOperationMemberInfo.setFaceToken(null);
        return hsaOperationMemberInfoMapper.updateByGuid(hsaOperationMemberInfo);
    }

    private void reduceIntegral(String memberGuid, Integer reduceIntegral) {

        List<RecordRemainIntegralDTO> recordRemainIntegrals = hsaIntegralDetailMapper.queryByIntegralValidityDate(memberGuid);
        if (CollectionUtils.isEmpty(recordRemainIntegrals)) {
            log.error("积分错误会员guid============>{}", memberGuid);
            return;
        }
        //当前扣减发生剩余记录变化的数据
        List<RecordRemainIntegralDTO> changeDataList = new ArrayList<>();
        for (RecordRemainIntegralDTO recordRemainIntegral : recordRemainIntegrals) {
            //扣减完毕
            if (reduceIntegral == 0) {
                break;
            }
            //本次没扣减完
            if ((reduceIntegral - recordRemainIntegral.getRecordRemainIntegral()) > 0) {
                reduceIntegral = reduceIntegral - recordRemainIntegral.getRecordRemainIntegral();
                recordRemainIntegral.setRecordRemainIntegral(0);
            } else {//本次扣减完毕
                recordRemainIntegral.setRecordRemainIntegral(recordRemainIntegral.getRecordRemainIntegral() - reduceIntegral);
                reduceIntegral = 0;
            }
            changeDataList.add(recordRemainIntegral);
        }
        //记录明细的剩余积分记录不够扣
        if (reduceIntegral != 0) {
            log.error("积分错误会员guid============>{}", memberGuid);
            log.error("积分扣减剩余不可抵扣积分============>{}", reduceIntegral);
        }
        //更新记录剩余可用积分
        if (StringUtils.isEmpty(changeDataList)) {
            return;
        }
        hsaIntegralDetailMapper.batchUpdateRecordRemainIntegral(changeDataList);

    }


    private void getHsaGrowthValueDetail(String memberGuid, Integer sourceType, String operSubjectGuid, HsaMemberGradeInfo hsaMemberGradeInfo, HsaBusinessEquities hsaBusinessEquities, int growthValue, int totalGrowthValue) {
        HsaGrowthValueDetail hsaGrowthValueDetail = new HsaGrowthValueDetail();
        hsaGrowthValueDetail.setGuid(guidGeneratorUtil.getGuid(HsaGrowthValueDetail.class.getSimpleName()).toString());
        hsaGrowthValueDetail.setOperatorAccountName(hsaMemberGradeInfo.getOperatorName());
        hsaGrowthValueDetail.setGrowthValueSourceType(sourceType);
        hsaGrowthValueDetail.setGrowthValue(growthValue);
        hsaGrowthValueDetail.setOperSubjectGuid(operSubjectGuid);
        hsaGrowthValueDetail.setChangeType(SumValueChangeEnum.GIVE_GROWTH_VALUE.getCode());
        hsaGrowthValueDetail.setMemberInfoGuid(memberGuid);
        hsaGrowthValueDetail.setGrowthValueType(NumberConstant.NUMBER_0);
        hsaGrowthValueDetail.setGrowthValidity(NumberConstant.NUMBER_0);
        hsaGrowthValueDetail.setRemainGrowthValue(totalGrowthValue);
        hsaGrowthValueDetail.setCurrentMemberLevel(hsaMemberGradeInfo.getName() + StringConstant.LEFT_BRACKET + StringConstant.VIP + hsaMemberGradeInfo.getVipGrade() + StringConstant.RIGHT_BRACKET);
        hsaGrowthValueDetail.setRecordDeclaration(hsaMemberGradeInfo.getName() + StringConstant.STR_BIAS_TWO + hsaBusinessEquities.getEquitiesName() + StringConstant.STR_BIAS_TWO + hsaBusinessEquities.getEquitiesNumber());
        hsaGrowthValueDetail.setRecordRemainGrowthValue(growthValue);
        hsaGrowthValueDetailMapper.insert(hsaGrowthValueDetail);
    }

    private void getHsaIntegralValueDetail(String memberGuid, Integer sourceType, String operSubjectGuid, HsaMemberGradeInfo hsaMemberGradeInfo, HsaBusinessEquities hsaBusinessEquities, int growthValue, int totalGrowthValue) {
        HsaIntegralDetail hsaIntegralDetail = new HsaIntegralDetail();
        hsaIntegralDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaIntegralDetail.class.getSimpleName()));
        hsaIntegralDetail.setIntegralSourceType(sourceType);
        hsaIntegralDetail.setOperatorAccountName(hsaMemberGradeInfo.getOperatorName());
        hsaIntegralDetail.setOperSubjectGuid(operSubjectGuid);
        hsaIntegralDetail.setCurrentMemberLevel(hsaMemberGradeInfo.getName() + StringConstant.LEFT_BRACKET + StringConstant.VIP + hsaMemberGradeInfo.getVipGrade() + StringConstant.RIGHT_BRACKET);
        hsaIntegralDetail.setRecordDeclaration(hsaMemberGradeInfo.getName() + StringConstant.STR_BIAS_TWO + hsaBusinessEquities.getEquitiesName() + StringConstant.STR_BIAS_TWO + hsaBusinessEquities.getEquitiesNumber());
        hsaIntegralDetail.setDeclaration(hsaMemberGradeInfo.getName() + StringConstant.STR_BIAS_TWO + hsaBusinessEquities.getEquitiesName() + StringConstant.STR_BIAS_TWO + hsaBusinessEquities.getEquitiesNumber());
        hsaIntegralDetail.setIntegral(growthValue);
        hsaIntegralDetail.setChangeType(SumValueChangeEnum.GIVE_GROWTH_VALUE.getCode());
        hsaIntegralDetail.setMemberInfoGuid(memberGuid);
        hsaIntegralDetail.setIntegralValidity(NumberConstant.NUMBER_0);
        hsaIntegralDetail.setIntegralType(NumberConstant.NUMBER_0);
        hsaIntegralDetail.setCurrentIntegral(totalGrowthValue);
        hsaIntegralDetail.setRecordRemainIntegral(growthValue);
        hsaIntegralDetailMapper.insert(hsaIntegralDetail);
    }

    private void getHsaMemberEquitiesReceiveRecord(String operSubjectGuid, String memberGuid, String memberGradeGuid, String gradeEquitiesGuid, Integer giveGrowthValueNumber, Integer type) {
        HsaMemberEquitiesReceiveRecord hsaMemberEquitiesReceiveRecord = new HsaMemberEquitiesReceiveRecord();
        hsaMemberEquitiesReceiveRecord.setGuid(guidGeneratorUtil.getGuid(HsaGrowthValueDetail.class.getSimpleName()).toString());
        hsaMemberEquitiesReceiveRecord.setOperSubjectGuid(operSubjectGuid);
        hsaMemberEquitiesReceiveRecord.setMemberGradeGuid(memberGradeGuid);
        hsaMemberEquitiesReceiveRecord.setMemberInfoGuid(memberGuid);
        hsaMemberEquitiesReceiveRecord.setGradeEquitiesGuid(gradeEquitiesGuid);
        hsaMemberEquitiesReceiveRecord.setGiveGrowthValueNumber(giveGrowthValueNumber);
        hsaMemberEquitiesReceiveRecord.setGmtCreate(LocalDateTime.now());
        hsaMemberEquitiesReceiveRecord.setGmtModified(LocalDateTime.now());
        hsaMemberEquitiesReceiveRecord.setType(type);
        hsaMemberEquitiesReceiveRecordMapper.insert(hsaMemberEquitiesReceiveRecord);
    }

    /**
     * 基础任务处理
     * 注册、完善个人信息
     *
     * @param operationMemberInfos operationMemberInfos
     * @param businessType         0 会员注册 1 信息完善
     */
    @Override
    public HsaGrowthValueTask sendMemberGrowthValue(List<HsaOperationMemberInfo> operationMemberInfos, Integer sourceType, Integer businessType, HeaderUserInfo headerUserInfo) {
        log.info("成长值基础任务处理会员数量->>>>>{}", operationMemberInfos.size());
        if (gradeStateService.isNotEnable(operationMemberInfos.get(0).getOperSubjectGuid())) {
            return null;
        }
        if (businessType == 0) {
            //注册
            sendRegisterProcessor(operationMemberInfos, sourceType, headerUserInfo);
        }
        //完善信息
        return sendPerfectMemberDataProcessor(operationMemberInfos, sourceType, headerUserInfo);
    }


    @Override
    public MemberGradeInfoBaseVO calculateExtraAmount(Integer growthValue) {
        MemberGradeInfoBaseVO vo = new MemberGradeInfoBaseVO();
        if (Objects.isNull(growthValue)) {
            return vo;
        }

        //判断当前会员等级是否启用
        if (gradeStateService.isNotEnable()) {
            HsaMemberGradeInfo hsaMemberGradeInfo = hsaMemberGradeInfoMapper.selectOne(
                    new LambdaQueryWrapper<HsaMemberGradeInfo>()
                            .eq(HsaMemberGradeInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                            .in(HsaMemberGradeInfo::getIsDelete, NUMBER_0, NUMBER_2)
                            .eq(HsaMemberGradeInfo::getEffective, NUMBER_1)
                            .eq(HsaMemberGradeInfo::getVipGrade, NUMBER_1)
                            .eq(HsaMemberGradeInfo::getRoleType, RoleTypeEnum.MEMBER.name())
            );
            BeanUtils.copyProperties(hsaMemberGradeInfo, vo);
            return vo;
        }
        //获取主体下等级信息
        List<HsaMemberGradeInfo> hsaMemberGradeInfoList = getHsaMemberGradeInfos(ThreadLocalCache.getOperSubjectGuid(), RoleTypeEnum.MEMBER.name());
        BeanUtils.copyProperties(calculateExtraAmount(growthValue, hsaMemberGradeInfoList), vo);
        return vo;
    }

    /**
     * 定位当前成长值所在等级
     *
     * @return HsaExtraAwardRule
     */
    @FunctionLabel
    private HsaMemberGradeInfo calculateExtraAmount(Integer memberGrowth, List<HsaMemberGradeInfo> GradeInfoList) {
        if (ThreadLocalCache.isPartner()) {
            return new HsaMemberGradeInfo();
        }
        HsaMemberGradeInfo awardRule = null;
        //判断是否为空
        if (Objects.isNull(memberGrowth)) {
            memberGrowth = 0;
        }
        for (int i = 0; i < GradeInfoList.size(); i++) {
            //判断成长值大小
            if (memberGrowth < GradeInfoList.get(i).getGrowthValue()) {
                if (i == 0) {
                    awardRule = GradeInfoList.get(i);
                } else {
                    awardRule = GradeInfoList.get(i - 1);
                }
                break;
            }
            if (i == GradeInfoList.size() - 1) {
                awardRule = GradeInfoList.get(i);
            }
        }
        return awardRule;
    }

    private List<HsaMemberGradeInfo> getHsaMemberGradeInfos(String operSubjectGuid, String roleType) {
        return hsaMemberGradeInfoMapper.selectList(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                .eq(HsaMemberGradeInfo::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaMemberGradeInfo::getRoleType, roleType)
                .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), NUMBER_2)
                .eq(HsaMemberGradeInfo::getEffective, NUMBER_1)
                .eq(HsaMemberGradeInfo::getType, NUMBER_0)
                .orderByAsc(HsaMemberGradeInfo::getVipGrade));
    }

    @Override
    public PageResult getAccountList(GradeAccountQO request) {
        request.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        PageMethod.startPage(request.getCurrentPage(), request.getPageSize());
        List<GradeAccountListVO> accountList = getAccountListVOS(request);
        if (CollUtil.isEmpty(accountList)) {
            return PageUtil.getPageResult(new PageInfo<>());
        }
        gradeAccountHandler(accountList, request);
        return PageUtil.getPageResult(new PageInfo<>(accountList));
    }

    private List<GradeAccountListVO> getAccountListVOS(GradeAccountQO request) {
        List<GradeAccountListVO> accountList = Lists.newArrayList();
        if (ThreadLocalCache.isPartner()) {
            accountList = getDealPartner(request, accountList);
        } else {
            accountList = hsaOperationMemberInfoMapper.getAccountList(request);
        }
        return accountList;
    }

    private List<GradeAccountListVO> getDealPartner(GradeAccountQO request, List<GradeAccountListVO> accountList) {
        request.setMemberGradeGuid(request.getMemberGrade());
        request.setMemberGrade(null);
        VerifyUtil.isTure(ObjectUtils.isEmpty(request.getRoleType())).trueHandle(() -> request.setRoleType(RoleTypeEnum.MEMBER.name()));
        //判断用户身份
        if (request.getRoleType().equals("MERCHANT")) {
            List<String> merPhone = memberGradeRelationService.getMerchantMember(request.getMemberGradeGuid());
            if (CollectionUtils.isNotEmpty(merPhone)) {
                request.setPhoneNum(merPhone);
            }
            accountList = CollectionUtils.isNotEmpty(request.getPhoneNum()) ? hsaOperationMemberInfoMapper.getAccountListByPartner(request) : accountList;
        } else {
            accountList = hsaOperationMemberInfoMapper.getAccountListByPartner(request);
        }
        return accountList;
    }

    @Override
    public void accountExport(GradeAccountQO request, HttpServletResponse response) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        request.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        //会员guids(如果不为空，则导出集合中的会员账户信息)
        List<String> memberInfoGuids = request.getMemberInfoGuids();
        List<GradeAccountListVO> accountList;
        if (CollUtil.isNotEmpty(memberInfoGuids)) {
            //查询选中的会员等级账户信息
            accountList = hsaOperationMemberInfoMapper.getSelectedList(request);
        } else {
            PageMethod.startPage(1, StringConstant.NUM_VALUE);
            accountList = getAccountListVOS(request);
        }
        if (accountList.size() > StringConstant.FIVE_THOUSAND) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_SUBSIDY_MAX_EXPORT_EXCEL);
        }
        gradeAccountHandler(accountList, request);
        //导出数据
        exportData(accountList, response, request.getMemberGradeName());
    }

    @Override
    public void initMemberGrade() {
        Map<String, List<HsaOperationMemberInfo>> hsaOperationMemberInfoMap = hsaOperationMemberInfoMapper.selectList(new LambdaQueryWrapper<>()).stream().collect(Collectors.groupingBy(HsaOperationMemberInfo::getOperSubjectGuid));
        //默认等级
        List<HsaMemberGradeInfo> hsaMemberGradeInfoList = hsaMemberGradeInfoMapper.selectList(new LambdaQueryWrapper<HsaMemberGradeInfo>().eq(HsaMemberGradeInfo::getVipGrade, NUMBER_1)
                .eq(HsaMemberGradeInfo::getType, GradeTypeEnum.FREE.getCode()));

        List<HsaOperationMemberInfo> updateOperationMemberInfos = Lists.newArrayList();
        if (CollUtil.isNotEmpty(hsaOperationMemberInfoMap)) {
            for (HsaMemberGradeInfo hsaMemberGradeInfo : hsaMemberGradeInfoList) {
                if (hsaOperationMemberInfoMap.containsKey(hsaMemberGradeInfo.getOperSubjectGuid())) {
                    List<HsaOperationMemberInfo> hsaOperationMemberInfoList = hsaOperationMemberInfoMap.get(hsaMemberGradeInfo.getOperSubjectGuid());
                    for (HsaOperationMemberInfo operationMemberInfo : hsaOperationMemberInfoList) {
                        operationMemberInfo
                                .setMemberGradeInfoGuid(hsaMemberGradeInfo.getGuid())
                                .setMemberGradeInfoName(hsaMemberGradeInfo.getName());
                        updateOperationMemberInfos.add(operationMemberInfo);
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(updateOperationMemberInfos)) {
            updateOperationMemberInfos.forEach(hsaOperationMemberInfoMapper::updateByGuid);
        }
    }

    @Override
    public List<GradeEquitiesRecordVO> getGradeEquitiesRecordList(String memberInfoGuid) {
        List<GradeEquitiesRecordVO> gradeEquitiesRecordVO = new ArrayList<>();
        List<HsaMemberGradeChangeDetail> memberGradeChangeDetailList = hsaMemberGradeChangeDetailService.list(new LambdaQueryWrapper<HsaMemberGradeChangeDetail>().eq(HsaMemberGradeChangeDetail::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()).eq(HsaMemberGradeChangeDetail::getMemberInfoGuid, memberInfoGuid).orderByDesc(HsaMemberGradeChangeDetail::getGmtCreate));
        if (CollUtil.isEmpty(memberGradeChangeDetailList)) {
            return gradeEquitiesRecordVO;
        }
        String memberGradeGuid = hsaOperationMemberInfoMapper.getMemberGradeGuid(ThreadLocalCache.getOperSubjectGuid(), memberInfoGuid);
        for (HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail : memberGradeChangeDetailList) {
            GradeEquitiesRecordVO gradeEquitiesRecord = new GradeEquitiesRecordVO();
            BeanUtils.copyProperties(hsaMemberGradeChangeDetail, gradeEquitiesRecord);
            //封装会员等级信息
            setGradeInfo(hsaMemberGradeChangeDetail, gradeEquitiesRecord);
            //封装会员等级权益信息
            setGradeEquities(hsaMemberGradeChangeDetail, gradeEquitiesRecord);
            //判断会员等级是否是当前等级
            gradeEquitiesRecord.setCurrentMember(false);
            String currentGradeGuid = Optional.of(hsaMemberGradeChangeDetail).map(HsaMemberGradeChangeDetail::getAfterChangeGradeGuid).orElse("");
            if (!StringUtils.isEmpty(memberGradeGuid) && memberGradeGuid.equals(currentGradeGuid)) {
                gradeEquitiesRecord.setCurrentMember(true);
            }
            gradeEquitiesRecordVO.add(gradeEquitiesRecord);
        }
        return gradeEquitiesRecordVO;
    }

    @Override
    public List<HsaOperationMemberInfo> getOperationMemberInfoList(List<String> guids, String operSubjectGuid) {
        return hsaOperationMemberInfoMapper.selectList(new LambdaQueryWrapper<HsaOperationMemberInfo>().eq(!StringUtils.isEmpty(operSubjectGuid), HsaOperationMemberInfo::getOperSubjectGuid, operSubjectGuid).in(HsaOperationMemberInfo::getGuid, guids));
    }

    @Override
    public List<MemberPhoneVO> getMemberPhoneList(List<String> guids, String operSubjectGuid) {
        return hsaOperationMemberInfoMapper.getMemberPhoneList(guids, operSubjectGuid);
    }

    @Override
    public List<HsaOperationMemberInfo> getMemberListBySubject(String operSubjectGuid) {
        return hsaOperationMemberInfoMapper.selectList(new LambdaQueryWrapper<HsaOperationMemberInfo>().eq(HsaOperationMemberInfo::getOperSubjectGuid, operSubjectGuid));
    }

    @Override
    public List<HsaOperationMemberInfo> getMemberListByGuids(List<String> guids) {
        return hsaOperationMemberInfoMapper.selectList(new LambdaQueryWrapper<HsaOperationMemberInfo>().in(HsaOperationMemberInfo::getGuid, guids));
    }

    @Override
    public List<String> getMemberInfoGuids(String keywords, String operSubjectGuid) {
        return hsaOperationMemberInfoMapper.getMemberInfoGuids(keywords, operSubjectGuid);
    }

    @Override
    public List<String> getMemberGuidBySubject(String operSubjectGuid) {
        return hsaOperationMemberInfoMapper.getMemberGuidBySubject(operSubjectGuid);
    }

    @Override
    public Integer getIntegralDeduction(String memberInfoGuid) {
        return hsaOperationMemberInfoMapper.getIntegralDeduction(memberInfoGuid);
    }

    /**
     * 封装会员等级信息
     *
     * @param hsaMemberGradeChangeDetail 会员等级变更记录
     * @param gradeEquitiesRecord        会员等级权益信息
     */
    private void setGradeInfo(HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail, GradeEquitiesRecordVO gradeEquitiesRecord) {
        if (Objects.isNull(hsaMemberGradeChangeDetail)) {
            return;
        }
        //会员等级
        Integer vipGrade = hsaMemberGradeChangeDetail.getAfterChangeGradeVipGrade();
        //会员等级guid
        String gradeGuid = hsaMemberGradeChangeDetail.getAfterChangeGradeGuid();
        //会员等级名称
        String gradeName = hsaMemberGradeChangeDetail.getAfterChangeGradeName();

        gradeEquitiesRecord.setVipGrade(vipGrade);
        gradeEquitiesRecord.setGradeGuid(gradeGuid);
        gradeEquitiesRecord.setGradeName(gradeName);
    }

    /**
     * 封装会员等级权益信息
     *
     * @param hsaMemberGradeChangeDetail 会员等级变更记录
     * @param gradeEquitiesRecord        会员等级权益信息
     */
    private void setGradeEquities(HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail, GradeEquitiesRecordVO gradeEquitiesRecord) {
        if (Objects.isNull(hsaMemberGradeChangeDetail)) {
            return;
        }
        //获取会员最新等级
        String memberGradeInfoGuid = hsaOperationMemberInfoMapper.queryByGuid(hsaMemberGradeChangeDetail.getMemberInfoGuid()).getMemberGradeInfoGuid();
        if (StringUtils.isEmpty(memberGradeInfoGuid)) {
            return;
        }
        List<String> gradeEquitiesName;
        List<String> equitiesNames;
        equitiesNames = getEquitiesNames(hsaMemberGradeChangeDetail, memberGradeInfoGuid);

        //通过会员升级时间查询等级权益,如果没有查询到权益信息，就查询当前等级应用的权益信息
        if (CollUtil.isEmpty(equitiesNames)) {
            //查询当前会员等级所有权益
            equitiesNames = hsaMemberGradeRightsRecordMapper.getCurrentEquitiesName(ThreadLocalCache.getOperSubjectGuid(), hsaMemberGradeChangeDetail.getAfterChangeGradeGuid());
        }
        if (CollUtil.isEmpty(equitiesNames) || equitiesNames.isEmpty()) {
            gradeEquitiesName = new ArrayList<>();
        } else {
            gradeEquitiesName = new ArrayList<>(equitiesNames);
        }
        //查询当前会员等级绑定的升级礼包
        List<GiftBagPreviewVO> gradeGiftBagList = hsaGradeGiftBagMapper.getGradeGiftBagList(ThreadLocalCache.getOperSubjectGuid(), hsaMemberGradeChangeDetail.getAfterChangeGradeGuid());
        HsaGiftBagBaseInfo hsaGiftBagBaseInfo = null;
        if (CollUtil.isNotEmpty(gradeGiftBagList)) {
            //升级礼包基础信息（用于获取升级礼包名称）
            hsaGiftBagBaseInfo = hsaGiftBagBaseInfoMapper.selectOne(new LambdaQueryWrapper<HsaGiftBagBaseInfo>().eq(HsaGiftBagBaseInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
        }
        String giftBagName = Optional.ofNullable(hsaGiftBagBaseInfo).map(HsaGiftBagBaseInfo::getEquitiesName).orElse(null);
        if (!StringUtils.isEmpty(giftBagName)) {
            gradeEquitiesName.add(giftBagName);
        }

        gradeEquitiesRecord.setGradeEquities(gradeEquitiesName);
    }

    private List<String> getEquitiesNames(HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail, String memberGradeInfoGuid) {
        List<String> equitiesNames;
        if (memberGradeInfoGuid.equals(hsaMemberGradeChangeDetail.getAfterChangeGradeGuid())) {
            equitiesNames = hsaBusinessEquitiesMapper.selectList(
                            new LambdaQueryWrapper<HsaBusinessEquities>()
                                    .eq(HsaBusinessEquities::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                                    .eq(HsaBusinessEquities::getMemberGradeInfoGuid, hsaMemberGradeChangeDetail.getAfterChangeGradeGuid())
                                    .eq(HsaBusinessEquities::getBusinessType, BusinessTypeEnum.GRADE_EQUITIES)
                                    .eq(HsaBusinessEquities::getEffective, BooleanEnum.TRUE.getCode())
                                    .in(HsaBusinessEquities::getIsDelete, MemberGradeStateEnum.NOT_DELETED.getCode(),
                                            MemberGradeStateEnum.REMOVE_BUT_VALID.getCode()))
                    .stream().map(HsaBusinessEquities::getEquitiesName)
                    .collect(Collectors.toList());
        } else {
            MemberEquitiesRecordQO memberEquitiesRecord = new MemberEquitiesRecordQO();
            memberEquitiesRecord.setGradeGuid(hsaMemberGradeChangeDetail.getAfterChangeGradeGuid());
            memberEquitiesRecord.setMemberInfoGuid(hsaMemberGradeChangeDetail.getMemberInfoGuid());
            memberEquitiesRecord.setUpgradeTime(hsaMemberGradeChangeDetail.getGmtCreate());
            memberEquitiesRecord.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            //查询会员升级时的会员等级权益名称
            equitiesNames = hsaMemberGradeRightsRecordMapper.getGradeEquitiesName(memberEquitiesRecord);
        }
        return equitiesNames;
    }

    /**
     * 导出数据
     *
     * @param accountList 等级用户列表数据集合
     */
    private void exportData(List<GradeAccountListVO> accountList, HttpServletResponse response, String memberGradeName) {
        String tableName = getTableName(memberGradeName);
        String timeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String fileName = String.format(tableName, timeStr);
        try {
            response.setContentType(StringConstant.CONTENT_TYPE);
            response.setCharacterEncoding(StringConstant.UTF_8);
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader(StringConstant.HEADER, StringConstant.ATTACHMENT + fileName + ExcelTypeEnum.XLSX.getValue());
            //导出的数据集合
            int serial = 1;
            List<ExcelGradeAccountVO> excelDataList = GradeTransform.INSTANCE.gradeAccountTOExcel(accountList);
            for (ExcelGradeAccountVO excelGradeAccountVO : excelDataList) {
                excelGradeAccountVO.setId(String.valueOf(serial++));
                excelGradeAccountVO.setCardList(StringBaseHandlerUtil.arrayConvert(excelGradeAccountVO.getCardLists()));
                excelGradeAccountVO.setAvailableEquitie(StringBaseHandlerUtil.arrayConvert(excelGradeAccountVO.getAvailableEquities()));
            }
            //导出数据字段信息处理
            EasyExcelUtils.writeExcelWithModel(response.getOutputStream(), excelDataList, ExcelGradeAccountVO.class, "sheet1");
        } catch (IOException e) {
            log.error("等级用户导出错误异常!" + e.getMessage());
        }
    }

    private String getTableName(String memberGradeName) {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isEmpty(memberGradeName)) {
            sb.append(GradeMessageInfoEnum.GRADE_NAME.getDes());
        } else {
            sb.append(memberGradeName);
        }
        sb.append("%s.xlsx");
        return sb.toString();
    }

    /**
     * 会员等级账户列表字段信息处理
     *
     * @param accountList 等级账户列表
     * @param request     请求参数
     */
    private void gradeAccountHandler(List<GradeAccountListVO> accountList, GradeAccountQO request) {
        if (CollUtil.isEmpty(accountList)) {
            return;
        }
        //会员Guids(用于查询会员账户关联的会员卡信息)
        List<String> memberInfoGuids = accountList.stream().map(GradeAccountListVO::getGuid).distinct().collect(Collectors.toList());
        //会员关联会员卡信息Map(key:会员账户guid value:会员卡集合)
        Map<String, List<String>> carNameMap = hsaMemberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                        .eq(HsaMemberInfoCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                        .in(HsaMemberInfoCard::getMemberInfoGuid, memberInfoGuids))
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(HsaMemberInfoCard::getMemberInfoGuid, Collectors.mapping(HsaMemberInfoCard::getCardName, Collectors.toList())));
        //会员等级guid
        List<String> memberGradeGuids = accountList.stream().map(GradeAccountListVO::getMemberGradeInfoGuid).distinct().collect(Collectors.toList());
        //权益名称Map
        Map<String, List<String>> equitiesNameVOMap = hsaBusinessEquitiesMapper.getGradeEquitiesInfosByGradeGuids(memberGradeGuids)
                .stream()
                .collect(Collectors.groupingBy(GradeEquitiesInfoVO::getGradeGuid, Collectors.mapping(GradeEquitiesInfoVO::getName, Collectors.toList())));

        HsaGiftBagBaseInfo hsaGiftBagBaseInfo = null;
        //查询当前会员等级绑定的升级礼包
        List<GiftBagPreviewVO> gradeGiftBagList = hsaGradeGiftBagMapper.getGradeGiftBagList(ThreadLocalCache.getOperSubjectGuid(), request.getMemberGrade());
        if (CollUtil.isNotEmpty(gradeGiftBagList)) {
            //升级礼包基础信息（用于获取升级礼包名称）
            hsaGiftBagBaseInfo = hsaGiftBagBaseInfoMapper.selectOne(new LambdaQueryWrapper<HsaGiftBagBaseInfo>()
                    .eq(HsaGiftBagBaseInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
        }

        for (GradeAccountListVO gradeAccountListVO : accountList) {
            List<String> gradeEquitiesNames = new ArrayList<>();
            String guid = gradeAccountListVO.getGuid();
            if (Objects.nonNull(carNameMap) && carNameMap.containsKey(guid)) {
                List<String> cardNames = carNameMap.get(guid);
                gradeAccountListVO.setCardLists(cardNames);
            }
            //添加升级礼包名称
            if (Objects.nonNull(hsaGiftBagBaseInfo)) {
                String equitiesName = hsaGiftBagBaseInfo.getEquitiesName();
                gradeEquitiesNames.add(equitiesName);
            }
            //添加权益名称
            if (Objects.nonNull(equitiesNameVOMap) && equitiesNameVOMap.containsKey(gradeAccountListVO.getMemberGradeInfoGuid())) {
                List<String> gradeEquitiesNameList = equitiesNameVOMap.get(gradeAccountListVO.getMemberGradeInfoGuid());
                gradeEquitiesNames.addAll(gradeEquitiesNameList);
            }
            gradeAccountListVO.setAvailableEquities(gradeEquitiesNames);
            //给手机号加区号和查看权限
            String phoneCountryCode = gradeAccountListVO.getPhoneCountryCode();
            String userPhone = gradeAccountListVO.getPhoneNum();
            String codeUserPhone = StringBaseHandlerUtil.phoneCountryCodeHandler(phoneCountryCode, userPhone, request.getPhonePermission());
            gradeAccountListVO.setPhoneNum(codeUserPhone);
        }
    }

    /**
     * 完善信息任务处理
     *
     * @param operationMemberInfos operationMemberInfos
     * @param sourceType           sourceType
     * @param headerUserInfo       headerUserInfo
     * @return
     */
    private HsaGrowthValueTask sendPerfectMemberDataProcessor(List<HsaOperationMemberInfo> operationMemberInfos, Integer sourceType, HeaderUserInfo headerUserInfo) {
        //完善信息
        HsaGrowthValueTask hsaGrowthValueTask = hsaGrowthValueTaskMapper.selectOne(new LambdaQueryWrapper<HsaGrowthValueTask>()
                .eq(HsaGrowthValueTask::getOperSubjectGuid, operationMemberInfos.get(0).getOperSubjectGuid())
                .eq(HsaGrowthValueTask::getTaskType, TaskTypeEnum.BASE_TASK.getCode())
                .eq(HsaGrowthValueTask::getTaskAction, TaskActionEnum.PERFECT_PERSONAL_DETAILS.getCode())
                .eq(HsaGrowthValueTask::getIsEnable, BooleanEnum.TRUE.getCode()).eq(HsaGrowthValueTask::getIsDelete, BooleanEnum.FALSE.getCode()));
        List<String> memberGuid = Lists.newArrayList();
        if (Objects.isNull(hsaGrowthValueTask)) {
            log.info("完善信息成长值任务不存在：{}", operationMemberInfos);
            if (CollUtil.isNotEmpty(operationMemberInfos)) {
                memberGuid = operationMemberInfos.stream().map(HsaOperationMemberInfo::getGuid).collect(Collectors.toList());
                GrowthValueChangeEvent event = MemberCardAssembler.getGrowthValueChangeEvent(operationMemberInfos, sourceType, headerUserInfo, memberGuid, hsaGrowthValueTask);
                log.info("完善信息成长值任务处理event：{}", event);
                growthValueEvent.send(event);
            }
            return null;
        }

        for (HsaOperationMemberInfo operationMemberInfo : operationMemberInfos) {
            if (checkGrowValuePerfectTask(hsaGrowthValueTask.getTaskValidityType(), hsaGrowthValueTask.getStartFixedTaskValidityDate(), hsaGrowthValueTask.getEndFixedTaskValidityDate(), hsaGrowthValueTask.getPersonalDetailsTypeJson(), operationMemberInfo)) {
                memberGuid.add(operationMemberInfo.getGuid());
            }
        }
        if (CollUtil.isNotEmpty(memberGuid)) {
            GrowthValueChangeEvent event = MemberCardAssembler.getGrowthValueChangeEvent(operationMemberInfos, sourceType, headerUserInfo, memberGuid, hsaGrowthValueTask);
            log.info("完善信息成长值任务处理event：{}", event);
            growthValueEvent.send(event);
            return hsaGrowthValueTask;
        }
        return null;
    }


    /**
     * 注册任务处理
     *
     * @param hsaOperationMemberInfos hsaOperationMemberInfos
     * @param sourceType              sourceType
     * @param headerUserInfo          headerUserInfo
     */
    private int sendRegisterProcessor(List<HsaOperationMemberInfo> hsaOperationMemberInfos, Integer sourceType, HeaderUserInfo headerUserInfo) {
        HsaGrowthValueTask hsaGrowthValueTask = hsaGrowthValueTaskMapper.selectOne(new LambdaQueryWrapper<HsaGrowthValueTask>().eq(HsaGrowthValueTask::getOperSubjectGuid, hsaOperationMemberInfos.get(0).getOperSubjectGuid()).eq(HsaGrowthValueTask::getTaskType, TaskTypeEnum.BASE_TASK.getCode()).eq(HsaGrowthValueTask::getTaskAction, TaskActionEnum.REGISTER.getCode()).eq(HsaGrowthValueTask::getIsEnable, BooleanEnum.TRUE.getCode()).eq(HsaGrowthValueTask::getIsDelete, BooleanEnum.FALSE.getCode()));
        if (Objects.isNull(hsaGrowthValueTask)) {
            log.info("注册成长值任务不存在：{}", hsaOperationMemberInfos);
            return NUMBER_0;
        }
        if (checkGrowValueRegisterTask(sourceType, hsaGrowthValueTask.getTaskValidityType(), hsaGrowthValueTask.getStartFixedTaskValidityDate(), hsaGrowthValueTask.getEndFixedTaskValidityDate(), hsaGrowthValueTask.getSourceTypeJson())) {
            GrowthValueChangeEvent event = MemberCardAssembler.getGrowthValueChangeEvent(hsaOperationMemberInfos, sourceType, headerUserInfo, hsaGrowthValueTask);
            log.info("注册成长值任务处理event：{}", event);
            growthValueEvent.send(event);
            return NUMBER_1;
        }
        return NUMBER_0;
    }


    /**
     * 校验注册任务
     *
     * @param sourceType sourceType
     * @return boolean
     */
    private boolean checkGrowValueRegisterTask(Integer sourceType, Integer taskValidityType, LocalDateTime startFixedTaskValidityDate, LocalDateTime endFixedTaskValidityDate, String sourceTypeJson) {
        //任务有效期
        if (taskValidityType == TaskValidityTypeEnum.FIXED_VALIDITY.getCode()
                && (LocalDateTime.now().isBefore(startFixedTaskValidityDate) || LocalDateTime.now().isAfter(endFixedTaskValidityDate))) {
            return false;
        }
        //任务渠道
        List<String> sourceTypes = Arrays.asList(sourceTypeJson.split(StringConstant.COMMA));
        return sourceTypes.contains(String.valueOf(sourceType));
    }

    /**
     * 校验完善信息任务
     *
     * @return boolean
     */
    private boolean checkGrowValuePerfectTask(Integer taskValidityType, LocalDateTime startFixedTaskValidityDate, LocalDateTime endFixedTaskValidityDate, String personalDetailsTypeJson, HsaOperationMemberInfo operationMemberInfo) {
        //任务有效期
        if (taskValidityType == 1
                && (LocalDateTime.now().isBefore(startFixedTaskValidityDate) || LocalDateTime.now().isAfter(endFixedTaskValidityDate))) {
            return false;

        }
        //限制规则性别、生日
        String[] sourceTypes = personalDetailsTypeJson.split(StringConstant.COMMA);
        PerfectPersonalDetailDTO perfectPersonalDetailDTO = JSONObject.parseObject(operationMemberInfo.getPerfectPersonalDetailJson(), PerfectPersonalDetailDTO.class);
        //两个条件
        if (sourceTypes.length == 2) {
            //之前已经完成任务
            if (Objects.nonNull(perfectPersonalDetailDTO) && !StringUtils.isEmpty(perfectPersonalDetailDTO.
                    getPerfectSexTime()) && !StringUtils.isEmpty(perfectPersonalDetailDTO.getPerfectBirthdayTime())) {
                return false;
            }
            //本次不满足
            if (operationMemberInfo.getSex() == SexEnum.SEX_WEIZHI.getCode() || Objects.isNull(operationMemberInfo.getBirthday())) {
                return false;
            }
        }
        //单条件
        if (sourceTypes.length == 1) {
            return checkSourceType(sourceTypes, perfectPersonalDetailDTO, operationMemberInfo);
        }
        return true;
    }

    private boolean checkSourceType(String[] sourceTypes, PerfectPersonalDetailDTO perfectPersonalDetailDTO, HsaOperationMemberInfo operationMemberInfo) {
        int sourceType = Integer.parseInt(sourceTypes[0]);
        //单条件性别
        if (PersonalDetailsTypeEnum.SEX.getCode() == sourceType) {
            return checkSex(perfectPersonalDetailDTO, operationMemberInfo);
        }
        //单条件生日
        if (PersonalDetailsTypeEnum.BIRTHDAY.getCode() == sourceType) {
            return checkBirthday(operationMemberInfo, perfectPersonalDetailDTO);
        }
        return true;
    }

    private static boolean checkSex(PerfectPersonalDetailDTO perfectPersonalDetailDTO, HsaOperationMemberInfo operationMemberInfo) {
        //之前已经完成任务
        if (Objects.nonNull(perfectPersonalDetailDTO) && !StringUtils.isEmpty(perfectPersonalDetailDTO.getPerfectSexTime())) {
            return false;
        }
        //之前没有完成，本次也没完成
        return operationMemberInfo.getSex() != SexEnum.SEX_WEIZHI.getCode();
    }

    private static boolean checkBirthday(HsaOperationMemberInfo operationMemberInfo, PerfectPersonalDetailDTO perfectPersonalDetailDTO) {
        //之前已经完成任务
        if (Objects.nonNull(perfectPersonalDetailDTO) && !StringUtils.isEmpty(perfectPersonalDetailDTO.getPerfectBirthdayTime())) {
            return false;
        }
        //之前没有完成，本次也没完成
        return !Objects.isNull(operationMemberInfo.getBirthday());
    }

    private OperationLogDTO getOperationLogDTO(HeaderUserInfo headerUserInfo, HsaOperationMemberInfo operationMemberInfo) {
        return OperationLogDTO.builder().contentGuidList(Collections.singletonList(operationMemberInfo.getGuid())).headerUserInfo(headerUserInfo).logType(OperationLogTypeEnum.MEMBER_ACCOUNT).extraContent(LogContentConstant.REGISTERED_SUCCESS).build();
    }

    /**
     * 判断用户之前是否存在勾选需要开通电子卡的实体卡
     *
     * @param request             request
     * @param operationMemberInfo operationMemberInfo
     * @return Map<String, HsaPhysicalCard>
     */
    private Map<String, HsaPhysicalCard> isOpenCard(SaveOperationMemberInfoQO request,
                                                    HsaOperationMemberInfo operationMemberInfo) {
        List<HsaPhysicalCard> hsaPhysicalCard = hsaPhysicalCardMapper.selectList(new LambdaQueryWrapper<HsaPhysicalCard>().eq(HsaPhysicalCard::getPhoneNum, request.getPhoneNum()).eq(HsaPhysicalCard::getOperSubjectGuid, request.getOperSubjectGuid()));
        List<HsaPhysicalCard> physicalCards = bindingCardProcessor(
                operationMemberInfo.getGuid(),
                hsaPhysicalCard, BooleanEnum.FALSE.getCode());
        //避免重复开卡
        Map<String, HsaPhysicalCard> hsaPhysicalCardMap = new HashMap<>();
        if (CollUtil.isNotEmpty(physicalCards)) {
            physicalCards.forEach(in -> hsaPhysicalCardMap.put(in.getCardGuid(), in));
        }
        return hsaPhysicalCardMap;
    }

    /**
     * 处理账户之前实体卡绑定电子卡
     *
     * @param operationMemberInfoGuid operationMemberInfo
     * @param hsaPhysicalCard         hsaPhysicalCard
     * @return List<HsaPhysicalCard>
     */
    private List<HsaPhysicalCard> bindingCardProcessor(String operationMemberInfoGuid,
                                                       List<HsaPhysicalCard> hsaPhysicalCard,
                                                       Integer isOverlay) {
        List<HsaPhysicalCard> physicalCards = new ArrayList<>();
        List<HsaPhysicalCard> physicalCardList = new ArrayList<>();
        if (CollUtil.isNotEmpty(hsaPhysicalCard)) {
            hsaPhysicalCard.forEach(in -> in.setMemberInfoGuid(operationMemberInfoGuid));
            Set<String> cardGuidList = hsaPhysicalCard.stream().map(HsaPhysicalCard::getCardGuid).collect(Collectors.toSet());
            Map<String, HsaCardOpenRule> hsaCardOpenRuleMap = hsaCardOpenRuleMapper.selectList(new LambdaQueryWrapper<HsaCardOpenRule>().in(HsaCardOpenRule::getCardGuid, cardGuidList)).stream().collect(Collectors.toMap(HsaCardOpenRule::getCardGuid, Function.identity(), (entity1, entity2) -> entity1));

            //校验数量
            dealForPhysicalCard(hsaPhysicalCard, isOverlay, hsaCardOpenRuleMap, physicalCardList, physicalCards);
            if (CollUtil.isNotEmpty(physicalCards)) {
                List<String> guid = physicalCards.stream().map(HsaPhysicalCard::getGuid).collect(Collectors.toList());
                List<String> hsaMemberInfoCards = hsaMemberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>().in(HsaMemberInfoCard::getPhysicalCardGuid, guid)).stream().map(HsaMemberInfoCard::getGuid).collect(Collectors.toList());
                for (String in : hsaMemberInfoCards) {
                    hsaMemberInfoCardService.openElectronic(in, operationMemberInfoGuid, isOverlay);
                }
            }
            if (CollUtil.isNotEmpty(physicalCardList)) {
                // 关联实体卡
                List<String> guid = physicalCardList.stream().map(HsaPhysicalCard::getGuid).collect(Collectors.toList());
                List<HsaMemberInfoCard> hsaMemberInfoCards = hsaMemberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>().in(HsaMemberInfoCard::getPhysicalCardGuid, guid));
                hsaMemberInfoCards.forEach(in -> in.setMemberInfoGuid(operationMemberInfoGuid));
                hsaMemberInfoCardMapper.updateBatchMemberInfoGuid(hsaMemberInfoCards, operationMemberInfoGuid);
            }
            hsaPhysicalCardMapper.updateBatchMemberInfoGuid(hsaPhysicalCard, operationMemberInfoGuid);

        }
        return physicalCards;
    }

    private static void dealForPhysicalCard(List<HsaPhysicalCard> hsaPhysicalCard, Integer isOverlay, Map<String, HsaCardOpenRule> hsaCardOpenRuleMap, List<HsaPhysicalCard> physicalCardList, List<HsaPhysicalCard> physicalCards) {
        for (HsaPhysicalCard physicalCard : hsaPhysicalCard) {
            if (CollUtil.isNotEmpty(hsaCardOpenRuleMap) && hsaCardOpenRuleMap.containsKey(physicalCard.getCardGuid())) {
                HsaCardOpenRule hsaCardOpenRule = hsaCardOpenRuleMap.get(physicalCard.getCardGuid());
                //未勾选开通电子卡也非注册开通电子的情况 需要验证
                if (checkCard(isOverlay, physicalCard, hsaCardOpenRule, physicalCardList)) continue;
                addPhysicalCards(physicalCard, hsaCardOpenRule, physicalCards);
            }
        }
    }

    private static void addPhysicalCards(HsaPhysicalCard physicalCard, HsaCardOpenRule hsaCardOpenRule, List<HsaPhysicalCard> physicalCards) {
        if (hsaCardOpenRule.getSendCountLimit() == BooleanEnum.TRUE.getCode()) {
            if (hsaCardOpenRule.getSurplusSendOpenCountLimit() > 0) {
                physicalCards.add(physicalCard);
            }
        } else {
            physicalCards.add(physicalCard);
        }
    }

    private static boolean checkCard(Integer isOverlay, HsaPhysicalCard physicalCard, HsaCardOpenRule hsaCardOpenRule, List<HsaPhysicalCard> physicalCardList) {
        if (hsaCardOpenRule.getIsSupportElectronicCard() == BooleanEnum.FALSE.getCode()) {
            return true;
        }
        if (BooleanEnum.TRUE.getCode() == isOverlay && hsaCardOpenRule.getElectronicOpenWay() != ElectronicCardOpenWayEnum.EGISTRATION_COMPLETE_AUTO.getCode()) {
            return true;
        }
        if (Objects.isNull(physicalCard.getIssueElectronicCard()) || physicalCard.getIssueElectronicCard() == BooleanEnum.FALSE.getCode()) {
            physicalCardList.add(physicalCard);
            return true;
        }
        return false;
    }

    /**
     * 电子卡注册开卡规则校验
     *
     * @param request             request
     * @param operationMemberInfo operationMemberInfo
     */
    public void openRegisterCard(SaveOperationMemberInfoQO request, HsaOperationMemberInfo operationMemberInfo, Map<String, HsaPhysicalCard> hsaPhysicalCardMap, HeaderUserInfo headerUserInfo) {
        List<String> card = Lists.newArrayList();
        List<MemberCardOpenDTO> memberCardOpenDTOS = Lists.newArrayList();
        if (CollUtil.isNotEmpty(request.getCardGuidList())) {
            card = getCardGuid(request, memberCardOpenDTOS);
        }
        List<HsaCardOpenRule> hsaCardOpenRules = hsaCardOpenRuleMapper.selectList(new LambdaQueryWrapper<HsaCardOpenRule>().eq(HsaCardOpenRule::getOperSubjectGuid, request.getOperSubjectGuid()).eq(HsaCardOpenRule::getIsSupportElectronicCard, BooleanEnum.TRUE.getCode()).eq(HsaCardOpenRule::getElectronicOpenWay, ElectronicOpenWayEnum.REGISTER_OPEN.getCode()).eq(HsaCardOpenRule::getSendStatus, SendCardStateEnum.CARD_STATE_START.getCode()).notIn(CollUtil.isNotEmpty(card), HsaCardOpenRule::getCardGuid, card));
        dealCardOpenRules(request, memberCardOpenDTOS, hsaCardOpenRules);
        if (CollUtil.isNotEmpty(memberCardOpenDTOS)) {
            dealMemberCardOpenDTOS(request, operationMemberInfo, hsaPhysicalCardMap, headerUserInfo, memberCardOpenDTOS);
        }
    }

    private static List<String> getCardGuid(SaveOperationMemberInfoQO request, List<MemberCardOpenDTO> memberCardOpenDTOS) {
        List<String> card;
        request.getCardGuidList().forEach(in -> {
            in.setElectronicOpenCardWay(ElectronicCardOpenWayEnum.SPECIFY_USER_OPEN.getCode());
            in.setOpenWay(ElectronicCardOpenTypeEnum.DESIGNATED_OPEN.getCode());
            in.setSource(request.getSourceType());
            in.setEnterpriseGuid(request.getEnterpriseGuid());
            in.setOperSubjectGuid(request.getOperSubjectGuid());
            in.setMemberPhoneNum(request.getPhoneNum());
        });
        memberCardOpenDTOS.addAll(request.getCardGuidList());
        card = request.getCardGuidList().stream().map(MemberCardOpenDTO::getCardGuid).collect(Collectors.toList());
        return card;
    }

    private void dealMemberCardOpenDTOS(SaveOperationMemberInfoQO request, HsaOperationMemberInfo operationMemberInfo, Map<String, HsaPhysicalCard> hsaPhysicalCardMap, HeaderUserInfo headerUserInfo, List<MemberCardOpenDTO> memberCardOpenDTOS) {
        List<MemberCardOpenDTO> memberCardOpenDTOArrayList = Lists.newArrayList();
        //防重 若之前实体卡已绑定开通电子卡 则注册不在开通电子卡
        if (CollUtil.isNotEmpty(hsaPhysicalCardMap)) {
            addCardOpenDTOArrayList(hsaPhysicalCardMap, memberCardOpenDTOS, memberCardOpenDTOArrayList);
        } else {
            dealElectronicCard(request, operationMemberInfo, headerUserInfo, memberCardOpenDTOS);
        }
        if (CollUtil.isNotEmpty(memberCardOpenDTOArrayList)) {
            openElectronicCard(operationMemberInfo.getGuid(), memberCardOpenDTOArrayList, headerUserInfo);
        }
    }

    private void dealElectronicCard(SaveOperationMemberInfoQO request, HsaOperationMemberInfo operationMemberInfo, HeaderUserInfo headerUserInfo, List<MemberCardOpenDTO> memberCardOpenDTOS) {
        //此处判定用户是否存在实体卡 若存在则走自定义流程 此时面值需要叠加
        List<HsaPhysicalCard> hsaPhysicalCard = hsaPhysicalCardMapper.selectList(new LambdaQueryWrapper<HsaPhysicalCard>().eq(HsaPhysicalCard::getPhoneNum, request.getPhoneNum()).eq(HsaPhysicalCard::getOperSubjectGuid, request.getOperSubjectGuid()));
        Map<String, HsaPhysicalCard> physicalCardMap = hsaPhysicalCard.stream().collect(Collectors.toMap(HsaPhysicalCard::getCardGuid, Function.identity(), (entity1, entity2) -> entity1));
        //正常注册需要发放的卡
        List<MemberCardOpenDTO> normalCardOpenDTOArrayList = Lists.newArrayList();
        //走自定义流程的绑定卡
        List<HsaPhysicalCard> hsaPhysicalCardArrayList = Lists.newArrayList();
        //分流处理开卡
        for (MemberCardOpenDTO memberCardOpenDTO : memberCardOpenDTOS) {
            if (CollUtil.isNotEmpty(physicalCardMap) && physicalCardMap.containsKey(memberCardOpenDTO.getCardGuid())) {
                hsaPhysicalCardArrayList.add(physicalCardMap.get(memberCardOpenDTO.getCardGuid()));
            } else {
                normalCardOpenDTOArrayList.add(memberCardOpenDTO);
            }
        }
        dealBindingCardProcessor(operationMemberInfo, hsaPhysicalCard, hsaPhysicalCardArrayList);
        if (CollUtil.isNotEmpty(normalCardOpenDTOArrayList)) {
            openElectronicCard(operationMemberInfo.getGuid(), normalCardOpenDTOArrayList, headerUserInfo);
        }
    }

    private void dealBindingCardProcessor(HsaOperationMemberInfo operationMemberInfo, List<HsaPhysicalCard> hsaPhysicalCard, List<HsaPhysicalCard> hsaPhysicalCardArrayList) {
        if (CollUtil.isNotEmpty(hsaPhysicalCardArrayList)) {
            bindingCardProcessor(operationMemberInfo.getGuid(), hsaPhysicalCardArrayList, BooleanEnum.TRUE.getCode());
        } else if (CollUtil.isNotEmpty(hsaPhysicalCard)) {
            bindingCardProcessor(operationMemberInfo.getGuid(), hsaPhysicalCard, BooleanEnum.TRUE.getCode());
        }
    }

    private static void addCardOpenDTOArrayList(Map<String, HsaPhysicalCard> hsaPhysicalCardMap, List<MemberCardOpenDTO> memberCardOpenDTOS, List<MemberCardOpenDTO> memberCardOpenDTOArrayList) {
        for (MemberCardOpenDTO memberCardOpenDTO : memberCardOpenDTOS) {
            if (!hsaPhysicalCardMap.containsKey(memberCardOpenDTO.getCardGuid())) {
                memberCardOpenDTOArrayList.add(memberCardOpenDTO);
            }
        }
    }

    private void dealCardOpenRules(SaveOperationMemberInfoQO request, List<MemberCardOpenDTO> memberCardOpenDTOS, List<HsaCardOpenRule> hsaCardOpenRules) {
        if (CollUtil.isNotEmpty(hsaCardOpenRules)) {
            List<String> cardGuidList = hsaCardOpenRules.stream().map(HsaCardOpenRule::getCardGuid).collect(Collectors.toList());
            //卡名称
            Map<String, String> hsaCardBaseNameMap = hsaCardBaseInfoMapper.selectList(new LambdaQueryWrapper<HsaCardBaseInfo>().in(HsaCardBaseInfo::getGuid, cardGuidList)).stream().collect(Collectors.toMap(HsaCardBaseInfo::getGuid, HsaCardBaseInfo::getCardName, (entity1, entity2) -> entity1));
            getMemberCardOpenDTOS(request, memberCardOpenDTOS, hsaCardOpenRules, hsaCardBaseNameMap);
        }
    }

    private void getMemberCardOpenDTOS(SaveOperationMemberInfoQO request, List<MemberCardOpenDTO> memberCardOpenDTOS, List<HsaCardOpenRule> hsaCardOpenRules, Map<String, String> hsaCardBaseNameMap) {
        hsaCardOpenRules.forEach(in -> {
            if (hsaCardBaseNameMap.containsKey(in.getCardGuid())) {
                String cardName = hsaCardBaseNameMap.get(in.getCardGuid());
                Set<String> labelGuid;
                if (CollUtil.isNotEmpty(request.getMenberLable())) {
                    labelGuid = new HashSet<>(request.getMenberLable());
                } else {
                    labelGuid = new HashSet<>();
                }
                if (Boolean.TRUE.equals(checkOpenCard(in, request.getSourceType(), labelGuid))) {
                    MemberAssembler.addMemberCardOpenDTOS(request, memberCardOpenDTOS, in, cardName);
                }
            }
        });
    }


    public void openElectronicCard(String memberGuid, List<MemberCardOpenDTO> cardGuidList, HeaderUserInfo headerUserInfo) {
        if (CollectionUtil.isEmpty(cardGuidList)) {
            return;
        }
        //构建传递参数
        OpenElectronicCardQO qo = new OpenElectronicCardQO();
        qo.setCardGuidList(cardGuidList);
        qo.setMemberInfoGuid(Collections.singletonList(String.valueOf(memberGuid)));
        memberBaseThreadExecutor.execute(() -> {
            try {
                ThreadLocalCache.put(JSON.toJSONString(headerUserInfo));
                electronicCardService.batchOpen(qo);
            } catch (Exception e) {
                log.warn(MemberInfoCardExceptionEnum.OPEN_ELECTRONIC_CARD_FAIL.getDes());
            }
        });
    }

    /**
     * 电子开卡时效、限量校验
     *
     * @param hsaCardOpenRule hsaCardOpenRule
     * @return Boolean
     */
    public Boolean checkOpenCard(HsaCardOpenRule hsaCardOpenRule, Integer sourceType, Set<String> hsaMemberLabelList) {
        LocalDateTime now = LocalDateTime.now();
        //发卡数量不限   开卡时间不限  所有会员都可开通
        if (isaBoolean(hsaCardOpenRule)) {

            return true;
            //发卡数量不限、固定时间段开卡
        } else if (isaBoolean1(hsaCardOpenRule)) {
            if (now.isAfter(hsaCardOpenRule.getOpenCardStartTime()) && now.isBefore(hsaCardOpenRule.getOpenCardEndTime())) {
                return MemberBusinessUtil.checkOpenCardScopeType(hsaCardOpenRule, sourceType, hsaMemberLabelList);
            }
        } else if (isaBoolean2(hsaCardOpenRule)) {
            if (hsaCardOpenRule.getSurplusSendOpenCountLimit() > 0) {
                return MemberBusinessUtil.checkOpenCardScopeType(hsaCardOpenRule, sourceType, hsaMemberLabelList);
            }
        } else if (isaBoolean3(hsaCardOpenRule)) {
            return MemberBusinessUtil.checkOpenCardScopeType(hsaCardOpenRule, sourceType, hsaMemberLabelList);
        } else if (isaBoolean4(hsaCardOpenRule)) {
            return MemberBusinessUtil.checkOpenCardScopeType(hsaCardOpenRule, sourceType, hsaMemberLabelList);

        }
        return false;
    }

    private static boolean isaBoolean4(HsaCardOpenRule hsaCardOpenRule) {
        return hsaCardOpenRule.getSendCountLimit() == BooleanEnum.TRUE.getCode() && hsaCardOpenRule.getOpenCardTimeType() == 0 && (hsaCardOpenRule.getSurplusSendOpenCountLimit() > 0);
    }

    private static boolean isaBoolean3(HsaCardOpenRule hsaCardOpenRule) {
        return hsaCardOpenRule.getSendCountLimit() == BooleanEnum.FALSE.getCode() && hsaCardOpenRule.getOpenCardTimeType() == 0;
    }

    private static boolean isaBoolean2(HsaCardOpenRule hsaCardOpenRule) {
        return hsaCardOpenRule.getSendCountLimit() == BooleanEnum.TRUE.getCode() && hsaCardOpenRule.getOpenCardTimeType() == 1;
    }

    private static boolean isaBoolean1(HsaCardOpenRule hsaCardOpenRule) {
        return hsaCardOpenRule.getSendCountLimit() == BooleanEnum.FALSE.getCode() && hsaCardOpenRule.getOpenCardTimeType() == 1;
    }

    private static boolean isaBoolean(HsaCardOpenRule hsaCardOpenRule) {
        return hsaCardOpenRule.getSendCountLimit() == BooleanEnum.FALSE.getCode() && hsaCardOpenRule.getOpenCardTimeType() == 0 && hsaCardOpenRule.getOpenCardScopeType() == 0;
    }

    @Override
    public Boolean updateMemberGrowth(RequestMemberGrowthValue request) {
        GrowthValueChangeEvent requestEvent = MemberCardAssembler.getGrowthValueChangeEvent(request);
        return growthValueEvent.send(requestEvent);
    }

    public void memberInfoPackage(SaveOperationMemberInfoQO request, HsaOperationMemberInfo operationMemberInfo) {
        BeanUtils.copyProperties(request, operationMemberInfo);
        String guid = guidGeneratorUtil.getStringGuid(HsaOperationMemberInfo.class.getSimpleName());
        MemberAssembler.getOperationMemberInfo(request, operationMemberInfo, guid);

        if (StringUtils.isEmpty(request.getMemberAccount())) {
            operationMemberInfo.setMemberAccount(createMemberAccount());
        }
    }


    /**
     * 初始化会员等级
     *
     * @param request             request
     * @param operationMemberInfo operationMemberInfo
     */
    private void initMemberGrade(SaveOperationMemberInfoQO request, HsaOperationMemberInfo operationMemberInfo,
                                 HsaControlledGradeState hsaControlledGradeState) {
        LocalDateTime now = LocalDateTime.now();
        HsaMemberGradeInfo hsaMemberGradeInfo = hsaMemberGradeInfoMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                .eq(HsaMemberGradeInfo::getOperSubjectGuid, request.getOperSubjectGuid())
                .eq(HsaMemberGradeInfo::getType, GradeTypeEnum.FREE.getCode())
                .eq(HsaMemberGradeInfo::getRoleType, RoleTypeEnum.MEMBER.name())
                .in(HsaMemberGradeInfo::getIsDelete, NUMBER_0, NUMBER_2)
                .eq(HsaMemberGradeInfo::getEffective, NUMBER_1)
                .eq(HsaMemberGradeInfo::getVipGrade, NUMBER_1));
        if (ThreadLocalCache.isPartner()) {
            if (StringUtils.isEmpty(request.getMemberGradeInfoGuid())) {
                return;
            }
            hsaMemberGradeInfo = hsaMemberGradeInfoMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                    .eq(HsaMemberGradeInfo::getGuid, request.getMemberGradeInfoGuid())
                    .eq(HsaMemberGradeInfo::getType, GradeTypeEnum.FREE.getCode())
                    .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), NUMBER_2)
                    .eq(HsaMemberGradeInfo::getEffective, BooleanEnum.TRUE.getCode())
            );
            VerifyUtil.throwException(ObjectUtils.isEmpty(hsaMemberGradeInfo)).throwMessage("未查询到等级信息");
            MemberGradeRelationDTO relationDTO = new MemberGradeRelationDTO();
            relationDTO.setMemberGuidList(Lists.newArrayList(operationMemberInfo.getGuid()));
            relationDTO.setMemberInfoGradeGuid(hsaMemberGradeInfo.getGuid());
            relationDTO.setRoleType(RoleTypeEnum.MEMBER.name());
            relationDTO.setNum(request.getNum());
            relationDTO.setUnit(request.getUnit());
            relationDTO.setSourceType(ThreadLocalCache.getHeaderUserInfo().getSource());
            memberGradeRelationService.batchSaveMember(relationDTO);

            // 保存付费记录
            savePayRecord(request, operationMemberInfo, hsaMemberGradeInfo);
        }
        //获取主体下等级信息
        List<HsaMemberGradeInfo> hsaMemberGradeInfoList = getHsaMemberGradeInfos(ThreadLocalCache.getOperSubjectGuid(), RoleTypeEnum.MEMBER.name());
        //默认等级
        if (Objects.equals(request.getSourceType(), SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode()) ||
                StringUtils.isEmpty(request.getMemberGradeInfoGuid()) || hsaControlledGradeState.getState() == EnableEnum.NOT_ENABLE.getCode()) {
            defaultGradeProcessor(request, operationMemberInfo, hsaControlledGradeState, hsaMemberGradeInfo, hsaMemberGradeInfoList);
        } else {
            hsaMemberGradeInfo = hsaMemberGradeInfoMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                    .eq(HsaMemberGradeInfo::getGuid, operationMemberInfo.getMemberGradeInfoGuid())
                    .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), NUMBER_2)
                    .eq(HsaMemberGradeInfo::getEffective, BooleanEnum.TRUE.getCode()));
            effectiveGradeHandle(request, operationMemberInfo, hsaControlledGradeState, hsaMemberGradeInfo, hsaMemberGradeInfoList);
        }
        //周期赠送 一次性赠送成长值的权益
        cycleGive(operationMemberInfo, hsaControlledGradeState, hsaMemberGradeInfo);

        // 开一张免费的会员卡
        HsaMemberGradeCard hsaMemberGradeCard = initFreeGradeCard(operationMemberInfo, hsaMemberGradeInfo);
        hsaMemberGradeCardService.save(hsaMemberGradeCard);
        //等级变更
        if (ObjectUtil.isEmpty(operationMemberInfo.getMemberGradeInfoGuid())) {
            operationMemberInfo.setMemberGradeInfoGuid(hsaMemberGradeInfo.getGuid());
            operationMemberInfo.setMemberGradeInfoName(hsaMemberGradeInfo.getName());
        }
        operationMemberInfo.setUpgradeTime(now);
        hsaMemberGradeChangeDetailService.encapsulationBusiness(operationMemberInfo, hsaMemberGradeInfo, now);
    }

    private HsaMemberGradeCard initFreeGradeCard(HsaOperationMemberInfo operationMemberInfo, HsaMemberGradeInfo hsaMemberGradeInfo) {
        String gradeCardGuid = guidGeneratorUtil.getStringGuid(HsaMemberGradeCard.class.getSimpleName());
        HsaMemberGradeCard hsaMemberGradeCard = new HsaMemberGradeCard()
                .setGradeGuid(operationMemberInfo.getMemberGradeInfoGuid() == null ? hsaMemberGradeInfo.getGuid() : operationMemberInfo.getMemberGradeInfoGuid())
                .setGradeType(GradeTypeEnum.FREE.getCode())
                .setVipGrade(hsaMemberGradeInfo.getVipGrade())
                .setOpenType(GradeCardOpenTypeEnum.PLATFORM_GIVE.getValue())
                .setMemberCardStatus(GradeCardStatusEnum.NORMAL.getCode())
                .setMemberInfoGuid(operationMemberInfo.getGuid())
                .setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        hsaMemberGradeCard.setGuid(gradeCardGuid);
        return hsaMemberGradeCard;
    }

    private void effectiveGradeHandle(SaveOperationMemberInfoQO request, HsaOperationMemberInfo operationMemberInfo,
                                      HsaControlledGradeState hsaControlledGradeState, HsaMemberGradeInfo hsaMemberGradeInfo,
                                      List<HsaMemberGradeInfo> hsaMemberGradeInfoList) {
        if (hsaMemberGradeInfo.getVipGrade() == NUMBER_1) {
            defaultGradeProcessor(request, operationMemberInfo, hsaControlledGradeState, hsaMemberGradeInfo, hsaMemberGradeInfoList);
        } else {
            //获取等级区间
            List<HsaMemberGradeInfo> scopeMemberGradeInfoList = hsaMemberGradeInfoMapper.selectList(
                    new LambdaQueryWrapper<HsaMemberGradeInfo>()
                            .eq(HsaMemberGradeInfo::getOperSubjectGuid, request.getOperSubjectGuid())
                            .ge(HsaMemberGradeInfo::getVipGrade, NUMBER_1)
                            .le(HsaMemberGradeInfo::getVipGrade, hsaMemberGradeInfo.getVipGrade())
                            .eq(HsaMemberGradeInfo::getType, hsaMemberGradeInfo.getType())
                            .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), NUMBER_2)
                            .eq(HsaMemberGradeInfo::getRoleType, RoleTypeEnum.MEMBER.name())
                            .eq(HsaMemberGradeInfo::getEffective, NUMBER_1)
            );

            List<String> memberGradeInfoGuidList = scopeMemberGradeInfoList.stream()
                    .map(HsaMemberGradeInfo::getGuid)
                    .collect(Collectors.toList());
            //获取礼包
            List<HsaGradeGiftBag> hsaGradeGiftBags = hsaGradeGiftBagMapper.selectList(
                    new LambdaQueryWrapper<HsaGradeGiftBag>()
                            .in(HsaGradeGiftBag::getMemberGradeInfoGuid, memberGradeInfoGuidList)
                            .in(HsaGradeGiftBag::getIsDelete, BooleanEnum.FALSE.getCode(), NUMBER_2)
                            .eq(HsaGradeGiftBag::getEffective, NUMBER_1)
            );
            if (!CollUtil.isEmpty(hsaGradeGiftBags)) {
                gradeRightsChangeProcessor(request, operationMemberInfo, hsaMemberGradeInfo, hsaGradeGiftBags, hsaMemberGradeInfoList);
            }

        }
    }

    private void cycleGive(HsaOperationMemberInfo operationMemberInfo, HsaControlledGradeState hsaControlledGradeState, HsaMemberGradeInfo hsaMemberGradeInfo) {
        List<HsaBusinessEquities> hsaBusinessEquitiesList = hsaBusinessEquitiesMapper.selectList(new LambdaQueryWrapper<HsaBusinessEquities>()
                .eq(HsaBusinessEquities::getSetPeriod, DataUnitEnum.FOREVER.getCode())
                .in(HsaBusinessEquities::getIsDelete, BooleanEnum.FALSE.getCode(), NUMBER_2)
                .eq(HsaBusinessEquities::getEffective, BooleanEnum.TRUE.getCode())
                .eq(HsaBusinessEquities::getMemberGradeInfoGuid, hsaMemberGradeInfo.getGuid())
                .eq(HsaBusinessEquities::getOperSubjectGuid, operationMemberInfo.getOperSubjectGuid()));
        log.info("周期赠送权益信息：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>" + JSON.toJSONString(hsaBusinessEquitiesList));
        if (hsaControlledGradeState.getState() == EnableEnum.ENABLE.getCode() && CollUtil.isNotEmpty(hsaBusinessEquitiesList)) {
            for (HsaBusinessEquities gradeEquities : hsaBusinessEquitiesList) {
                int type = gradeEquities.getEquitiesRuleType() == EquitiesRuleTypeEnum.GIVE_INTEGRAL.getCode() ? NUMBER_1 : NUMBER_0;
                if (gradeEquities.getEquitiesRuleType() == EquitiesRuleTypeEnum.GIVE_INTEGRAL.getCode()) {
                    getHsaIntegralValueDetail(operationMemberInfo.getGuid(), SourceTypeEnum.ADD_BACKGROUND.getCode(),
                            operationMemberInfo.getOperSubjectGuid(), hsaMemberGradeInfo, gradeEquities,
                            gradeEquities.getTotalGiveNumber(), gradeEquities.getTotalGiveNumber());
                    operationMemberInfo.setMemberIntegral(operationMemberInfo.getMemberIntegral() + gradeEquities.getTotalGiveNumber());
                    getHsaMemberEquitiesReceiveRecord(operationMemberInfo.getOperSubjectGuid(), operationMemberInfo.getGuid(),
                            hsaMemberGradeInfo.getGuid(), gradeEquities.getEquitiesGuid(), gradeEquities.getTotalGiveNumber(), type);
                } else {
                    getHsaGrowthValueDetail(operationMemberInfo.getGuid(), SourceTypeEnum.ADD_BACKGROUND.getCode(),
                            operationMemberInfo.getOperSubjectGuid(), hsaMemberGradeInfo, gradeEquities,
                            gradeEquities.getTotalGiveNumber(), gradeEquities.getTotalGiveNumber());
                    operationMemberInfo.setMemberGrowthValue(operationMemberInfo.getMemberGrowthValue() + gradeEquities.getTotalGiveNumber());
                    getHsaMemberEquitiesReceiveRecord(operationMemberInfo.getOperSubjectGuid(), operationMemberInfo.getGuid(),
                            hsaMemberGradeInfo.getGuid(), gradeEquities.getEquitiesGuid(), gradeEquities.getTotalGiveNumber(), type);
                }

            }
        }
    }

    private void savePayRecord(SaveOperationMemberInfoQO request, HsaOperationMemberInfo operationMemberInfo, HsaMemberGradeInfo hsaMemberGradeInfo) {
        HsaMemberGradePayRecord memberGradePayRecord = new HsaMemberGradePayRecord();
        memberGradePayRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberGradePayRecord.class.getSimpleName()));
        memberGradePayRecord.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        memberGradePayRecord.setMemberInfoGradeGuid(request.getMemberGradeInfoGuid());
        memberGradePayRecord.setMemberInfoGradeName(hsaMemberGradeInfo.getName());
        memberGradePayRecord.setPayTime(LocalDateTime.now());
        memberGradePayRecord.setExpireTime(DateUtil.getFirstExpireTime(request.getNum(), request.getUnit()));
        memberGradePayRecord.setRoleType(RoleTypeEnum.MEMBER.name());
        memberGradePayRecord.setNum(request.getNum());
        memberGradePayRecord.setUnit(request.getUnit());
        memberGradePayRecord.setChangeType(GradeRecordChangeEnum.INITIAL_GRADE.getCode());
        memberGradePayRecord.setRemark(RegisterChannelEnum.getNameByCode(operationMemberInfo.getSourceType()));
        memberGradePayRecord.setMemberInfoGuid(operationMemberInfo.getGuid());
        memberGradePayRecord.setMemberInfoName(operationMemberInfo.getUserName());
        memberGradePayRecord.setPhoneNum(operationMemberInfo.getPhoneNum());
        memberGradePayRecordService.save(memberGradePayRecord);
    }

    private void defaultGradeProcessor(SaveOperationMemberInfoQO request, HsaOperationMemberInfo operationMemberInfo, HsaControlledGradeState hsaControlledGradeState, HsaMemberGradeInfo hsaMemberGradeInfo, List<HsaMemberGradeInfo> hsaMemberGradeInfoList) {
        List<HsaGradeGiftBag> hsaGradeGiftBags = hsaGradeGiftBagMapper
                .selectList(new LambdaQueryWrapper<HsaGradeGiftBag>()
                        .eq(HsaGradeGiftBag::getMemberGradeInfoGuid, hsaMemberGradeInfo.getGuid())
                        .in(HsaGradeGiftBag::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                        .eq(HsaGradeGiftBag::getEffective, NUMBER_1));
        if (CollUtil.isEmpty(hsaGradeGiftBags)) {
            log.info("此等级区间不存在升级礼包：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>等级guid{}", hsaMemberGradeInfo.getGuid());
            return;
        }
        if (hsaControlledGradeState.getState() == EnableEnum.ENABLE.getCode()) {
            gradeRightsChangeProcessor(request, operationMemberInfo, hsaMemberGradeInfo, hsaGradeGiftBags, hsaMemberGradeInfoList);
        }
    }

    /**
     * 等级升级礼包权益赠送
     *
     * @param request
     * @param operationMemberInfo
     * @param hsaMemberGradeInfo
     * @param hsaGradeGiftBags
     */
    private void gradeRightsChangeProcessor(SaveOperationMemberInfoQO request, HsaOperationMemberInfo operationMemberInfo,
                                            HsaMemberGradeInfo hsaMemberGradeInfo, List<HsaGradeGiftBag> hsaGradeGiftBags,
                                            List<HsaMemberGradeInfo> hsaMemberGradeInfoList) {
        HsaGiftBagBaseInfo hsaGiftBagBaseInfo = hsaGiftBagBaseInfoMapper.selectOne(new LambdaQueryWrapper<HsaGiftBagBaseInfo>().eq(HsaGiftBagBaseInfo::getOperSubjectGuid, operationMemberInfo.getOperSubjectGuid()));
        if (hsaGiftBagBaseInfo.getEquitiesRule() == GiftBagEquitiesRuleEnum.SELF_COLLECTION.getCode()) {
            return;
        }

        //礼包赠送
        for (HsaGradeGiftBag hsaGradeGiftBag : hsaGradeGiftBags) {
            HsaMemberGradeRightsRecord hsaMemberGradeRightsRecord = new HsaMemberGradeRightsRecord();
            hsaMemberGradeRightsRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberGradeRightsRecord.class.getSimpleName())).setMemberInfoGuid(operationMemberInfo.getGuid()).setOperSubjectGuid(request.getOperSubjectGuid()).setMemberGradeGuid(hsaGradeGiftBag.getMemberGradeInfoGuid()).setRightsGuid(hsaGradeGiftBag.getGuid()).setRightsType(GradeRightsTypeEnum.GRADE_RIGHTS_BAG.getCode());
            if (hsaGradeGiftBag.getType() == NUMBER_0) {
                operationMemberInfo.setMemberGrowthValue(operationMemberInfo.getMemberGrowthValue() + hsaGradeGiftBag.getValue());
                //会员成长值明细记录 and 同步会员上冗余的等级信息 赠送完礼包后 可能升级了
                hsaGrowthValueDetailService.saveHsaGrowthValueDetail(operationMemberInfo, request.getSourceType(), request.getOperSubjectGuid(), hsaGradeGiftBag, hsaMemberGradeInfo, hsaMemberGradeInfoList);
            } else {
                //积分
                operationMemberInfo.setMemberIntegral(Objects.nonNull(operationMemberInfo.getMemberIntegral()) ? operationMemberInfo.getMemberIntegral() + hsaGradeGiftBag.getValue() : NUMBER_0 + hsaGradeGiftBag.getValue());
                //会员成长值明细记录
                saveHsaIntegralValueDetail(operationMemberInfo, request.getSourceType(), request.getOperSubjectGuid(),
                        hsaGradeGiftBag, hsaMemberGradeInfo, hsaMemberGradeInfoList);
            }
            hsaMemberGradeRightsRecordMapper.insert(hsaMemberGradeRightsRecord);
        }
    }

    public void saveHsaIntegralValueDetail(HsaOperationMemberInfo operationMemberInfo, Integer sourceType,
                                           String operSubjectGuid, HsaGradeGiftBag hsaGradeGiftBag,
                                           HsaMemberGradeInfo hsaMemberGradeInfo, List<HsaMemberGradeInfo> hsaMemberGradeInfoList) {
        HsaGiftBagBaseInfo hsaGiftBagBaseInfo = hsaGiftBagBaseInfoMapper.selectOne(new LambdaQueryWrapper<HsaGiftBagBaseInfo>()
                .eq(HsaGiftBagBaseInfo::getOperSubjectGuid, operSubjectGuid));
        HsaMemberGradeInfo memberGradeInfo = hsaMemberGradeInfoMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                .eq(HsaMemberGradeInfo::getEffective, NUMBER_1)
                .eq(HsaMemberGradeInfo::getGuid, hsaGradeGiftBag.getMemberGradeInfoGuid())
                .in(HsaMemberGradeInfo::getIsDelete, NUMBER_0, NUMBER_2));
        HsaIntegralDetail hsaIntegralDetail = new HsaIntegralDetail();
        hsaIntegralDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaGrowthValueDetail.class.getSimpleName()));
        hsaIntegralDetail.setIntegralSourceType(sourceType);
        hsaIntegralDetail.setOperatorAccountName(hsaMemberGradeInfo.getOperatorName());
        hsaIntegralDetail.setOperSubjectGuid(operSubjectGuid);
        if (!Objects.equals(SystemEnum.PARTNER.getCode(), ThreadLocalCache.getHeaderUserInfo().getSystem()) || ObjectUtils.isEmpty(hsaMemberGradeInfo)) {
            hsaMemberGradeInfo = calculateExtraAmount(operationMemberInfo.getMemberGrowthValue(), hsaMemberGradeInfoList);
        }
        hsaIntegralDetail.setCurrentMemberLevel(hsaMemberGradeInfo.getName() + StringConstant.LEFT_BRACKET + StringConstant.VIP + hsaMemberGradeInfo.getVipGrade() + StringConstant.RIGHT_BRACKET);
        hsaIntegralDetail.setRecordDeclaration(memberGradeInfo.getName() + StringConstant.STR_BIAS_TWO + hsaGiftBagBaseInfo.getEquitiesName());
        hsaIntegralDetail.setDeclaration(SumValueChangeEnum.BAG.getDes() + StringConstant.STR_BIAS_TWO + hsaGiftBagBaseInfo.getEquitiesName());
        hsaIntegralDetail.setIntegral(hsaGradeGiftBag.getValue());
        hsaIntegralDetail.setChangeType(SumValueChangeEnum.BAG.getCode());
        hsaIntegralDetail.setMemberInfoGuid(operationMemberInfo.getGuid());
        hsaIntegralDetail.setIntegralValidity(NUMBER_0);
        hsaIntegralDetail.setIntegralType(NUMBER_0);
        hsaIntegralDetail.setCurrentIntegral(operationMemberInfo.getMemberIntegral());
        hsaIntegralDetail.setRecordRemainIntegral(hsaGradeGiftBag.getValue());
        hsaIntegralDetailMapper.insert(hsaIntegralDetail);
    }

    public void resourcesUnified(SaveOperationMemberInfoQO request, HeaderUserInfo headerUserInfo) {
        if (Objects.nonNull(headerUserInfo)) {
            request.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid()).setEnterpriseGuid(headerUserInfo.getEnterpriseGuid()).setEnterpriseName(headerUserInfo.getEnterpriseName()).setOperatorGuid(headerUserInfo.getUserGuid()).setOperatorName(headerUserInfo.getUserName());
            if (ObjectUtil.isNotNull(headerUserInfo.getSource())) {
                request.setSourceType(headerUserInfo.getSource());
            }
            headerUserInfo.setStoreGuid(request.getStoreGuid());
            headerUserInfo.setStoreName(request.getStoreName());
        }
    }

    private final static String SEPARATOR = "/";

    @Override
    @Transactional(rollbackFor = Exception.class)
/**
 * 根据会员GUID更新会员信息
 *
 * @param request 更新会员信息的请求参数
 * @return 更新结果，true表示成功，false表示失败
 */
    public boolean updateByMemberGuid(UpdateOperationMemberInfoQO request) {
        // 获取当前线程的用户信息
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        // 验证会员GUID并获取会员信息
        HsaOperationMemberInfo operationMemberInfo = validateMemberByGuid(request.getGuid());

        // 验证敏感词
        validateSensitiveWords(request);

        // 备份原始会员信息
        HsaOperationMemberInfo originMemberInfo = new HsaOperationMemberInfo();
        BeanUtil.copyProperties(operationMemberInfo, originMemberInfo);
        BeanUtil.copyProperties(request, operationMemberInfo);

        // 设置统一参数
        operationMemberInfo.setOperatorName(getOperationName());

        // 保存证件信息
        String certificateLog = saveCertificateByGuid(request.getCertificateInfoDTOList(), operationMemberInfo.getGuid(), operationMemberInfo.getOperSubjectGuid());

        // 处理积分任务
        handleIntegralTasks(headerUserInfo, operationMemberInfo);

        // 更新个人详细信息
        updatePersonalDetails(request, operationMemberInfo);

        // 同步数据项
        synchronizeDataItems(operationMemberInfo);

        // 更新会员信息
        boolean result = this.updateByGuid(operationMemberInfo);

        if (result) {
            // 处理更新后的任务
            handlePostUpdateTasks(request, headerUserInfo, operationMemberInfo, originMemberInfo, certificateLog);
        }

        // 刷新会员标签
        refreshMemberLabels(operationMemberInfo);

        return result;
    }

    private void validateSensitiveWords(UpdateOperationMemberInfoQO request) {
        if (!StringUtils.isEmpty(request.getUserName()) || !StringUtils.isEmpty(request.getDepartmentName())) {
            String stringBuilder = request.getUserName() + request.getDepartmentName();
            hsaApplyDictionariesService.checkKeyWords(stringBuilder);
        }
    }

    private void handleIntegralTasks(HeaderUserInfo headerUserInfo, HsaOperationMemberInfo operationMemberInfo) {
        List<HsaIntegralTask> hsaIntegralTaskList = hsaIntegralTaskMapper.selectList(
                new LambdaQueryWrapper<HsaIntegralTask>()
                        .eq(HsaIntegralTask::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid())
                        .eq(HsaIntegralTask::getIsDelete, BooleanEnum.FALSE.getCode())
                        .eq(HsaIntegralTask::getIsEnable, BooleanEnum.TRUE.getCode())
                        .eq(HsaIntegralTask::getTaskAction, TaskActionEnum.PERFECT_PERSONAL_DETAILS.getCode())
        );

        HsaIntegralTask perfectPersonalDetailsTask = null;
        for (HsaIntegralTask integralTask : hsaIntegralTaskList) {
            // 计算并设置任务状态
            Integer taskStatus = IntegralCommodityClassifyConverter.getTaskStatus(
                    integralTask.getTaskValidityType(),
                    integralTask.getStartFixedTaskValidityDate(),
                    integralTask.getEndFixedTaskValidityDate(),
                    integralTask.getIsEnable());
            if (taskStatus == TaskStatusEnum.UNDERWAY.getCode()) {
                perfectPersonalDetailsTask = integralTask;
                break;
            }
        }
        dealIntegralTask(operationMemberInfo, headerUserInfo, perfectPersonalDetailsTask, operationMemberInfo.getSourceType());
        sendMemberGrowthValue(Collections.singletonList(operationMemberInfo), operationMemberInfo.getSourceType(), 1, headerUserInfo);
    }

    private void updatePersonalDetails(UpdateOperationMemberInfoQO request, HsaOperationMemberInfo operationMemberInfo) {
        PerfectPersonalDetailDTO perfectPersonalDetailDTO = new PerfectPersonalDetailDTO();
        if (operationMemberInfo.getSex() != 0) {
            perfectPersonalDetailDTO.setPerfectSexTime(DateUtil.formatLocalDateTime(LocalDateTime.now(), DateUtil.PATTERN_DATETIME));
        }
        if (!Objects.isNull(operationMemberInfo.getBirthday())) {
            perfectPersonalDetailDTO.setPerfectBirthdayTime(DateUtil.formatLocalDateTime(LocalDateTime.now(), DateUtil.PATTERN_DATETIME));
        }
        String industryCodes = Optional.ofNullable(request.getIndustryCodes())
                .map(StringBaseHandlerUtil::arrayConvert)
                .orElse("");
        operationMemberInfo.setIndustryCodes(industryCodes);
        operationMemberInfo.setPerfectPersonalDetailJson(JSONObject.toJSONString(perfectPersonalDetailDTO));
        operationMemberInfo.setPinyin(ChineseSubstring.getFullSpell(operationMemberInfo.getUserName()));
    }

    private void synchronizeDataItems(HsaOperationMemberInfo operationMemberInfo) {
        List<DataItemSetVO> dataItemDTOS = JSON.parseArray(operationMemberInfo.getDataItemJson(), DataItemSetVO.class);
        if (CollUtil.isNotEmpty(dataItemDTOS)) {
            for (DataItemSetVO dataItemDTO : dataItemDTOS) {
                if (DataItemEnum.NUMBER.getInfoName().equals(dataItemDTO.getInfoName())) {
                    dataItemDTO.setValue(operationMemberInfo.getMemberNum());
                }
                if (DataItemEnum.DEPARTMENT.getInfoName().equals(dataItemDTO.getInfoName())) {
                    dataItemDTO.setValue(operationMemberInfo.getDepartmentName());
                }
            }
            operationMemberInfo.setDataItemJson(JSON.toJSONString(dataItemDTOS));
        }
    }

    private void handlePostUpdateTasks(UpdateOperationMemberInfoQO request, HeaderUserInfo headerUserInfo,
                                       HsaOperationMemberInfo operationMemberInfo, HsaOperationMemberInfo originMemberInfo,
                                       String certificateLog) {
        if (SystemEnum.PARTNER.getCode() == headerUserInfo.getSystem()) {
            memberBaseThreadExecutor.execute(() -> updateBusinessInfo(request, operationMemberInfo.getGuid(), headerUserInfo));
        }

        String changeArea = buildAreaString(request.getProvinceName(), request.getCityName(), request.getAreaName());
        String originArea = buildAreaString(originMemberInfo.getProvinceName(), originMemberInfo.getCityName(), originMemberInfo.getAreaName());

        StringBuilder builder = new StringBuilder();
        String regionLog = operationLogService.compareData(originArea, changeArea, LogFieldConstant.MEMBER_REGION);
        builder.append(regionLog == null ? "" : regionLog).append(certificateLog == null ? "" : certificateLog);

        OperationLogDTO logDTO = OperationLogDTO.builder()
                .contentGuidList(Collections.singletonList(operationMemberInfo.getGuid()))
                .headerUserInfo(headerUserInfo)
                .originData(originMemberInfo)
                .changeData(request)
                .logType(OperationLogTypeEnum.MEMBER_ACCOUNT)
                .extraContent(builder.toString())
                .build();
        operationLogService.dealBusinessLog(logDTO);
    }

    private String buildAreaString(String province, String city, String area) {
        if (StringUtils.isEmpty(province)) {
            return null;
        }
        return province + SEPARATOR + city + SEPARATOR + area;
    }

    private void refreshMemberLabels(HsaOperationMemberInfo operationMemberInfo) {
        hsaLabelSettingService.refreshLabel(
                Collections.singletonList(operationMemberInfo.getGuid()),
                null,
                BooleanEnum.FALSE.getCode(),
                null,
                LabelTriggerTypeEnum.ALL.getCode()
        );
    }

    private void updateBusinessInfo(UpdateOperationMemberInfoQO request, String memberGuid, HeaderUserInfo headerUserInfo) {
        ThreadLocalCache.put(JSON.toJSONString(headerUserInfo));
        saveOrUpdateBusinessInfo(request.getEducationalExperience(), request.getPersonalExperience(), request.getCustomize(), memberGuid);
        if (CollectionUtils.isNotEmpty(request.getToDeleteEducational())) {
            request.getToDeleteEducational().forEach(e -> educationService.deleteByGuid(e));
        }
        if (CollectionUtils.isNotEmpty(request.getToDeletePersonal())) {
            request.getToDeletePersonal().forEach(p -> personalService.deleteByGuid(p));
        }
    }

    private void saveMemberCertificateInfo(List<HsaMemberCertificateInfo> hsaMemberCertificateInfos, HsaOperationMemberInfo operationMemberInfo) {
        if (CollectionUtils.isNotEmpty(hsaMemberCertificateInfos)) {
            hsaMemberCertificateInfos.forEach(in -> {
                in.setOperationMemberInfoGuid(operationMemberInfo.getGuid());
                in.setOperSubjectGuid(operationMemberInfo.getOperSubjectGuid());
                hsaMemberCertificateInfoMapper.insert(in);
            });
        }
    }

    /**
     * 会员导入
     *
     * @param fileUrl 会员信息url地址
     */
    @Override
    public MemberUploadExcelVO sendMemberUploadExcelUrl(String fileUrl, String fileName) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        HsaOperationMemberInfoService memberInfoService = (HsaOperationMemberInfoService) AopContext.currentProxy();
        //导入
        MemberUploadExcelVO memberUploadExcelVO = memberInfoService.memberUploadExcelUrl(fileUrl, fileName);
        if (memberUploadExcelVO.getSuccess() > 0) {
            //成长值任务
            memberBaseThreadExecutor.execute(() -> sendMemberGrowthValue(memberUploadExcelVO.getHsaOperationMemberInfoList(), memberUploadExcelVO.getHsaOperationMemberInfoList().get(0).getSourceType(), 0, headerUserInfo));
            // 保存会员等级
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(memberUploadExcelVO.getAdjustGrades())) {
                memberUploadExcelVO.getAdjustGrades().forEach(e -> memberGradeInfoService.batchAdjustGrade(e));
            }
        }

        //不返回
        memberUploadExcelVO.setHsaOperationMemberInfoList(null);
        memberUploadExcelVO.setAdjustGrades(null);
        return memberUploadExcelVO;
    }


    /**
     * 会员导入
     *
     * @param fileUrl 会员信息url地址
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MemberUploadExcelVO memberUploadExcelUrl(String fileUrl, String fileName) {

        List<HsmMemberUploadExcel> read = MemberReadExcelUtil.read(fileUrl);


        //手机号验证
        Map<String, String> memberPhoneMap = new HashMap<>();
        checkMemberPhone(read, memberPhoneMap);
        //账户ID校验
        Map<String, String> checkAccountNumMap = new HashMap<>();
        checkMemberAccount(read, checkAccountNumMap);

        //获取证件号
        Map<String, HsaMemberCertificateInfo> operationMemberCertificateNumMap = hsaMemberCertificateInfoMapper.selectList(new LambdaQueryWrapper<HsaMemberCertificateInfo>().eq(HsaMemberCertificateInfo::getOperSubjectGuid, ThreadLocalCache.getHeaderUserInfo().getOperSubjectGuid())).stream().collect(Collectors.toMap(HsaMemberCertificateInfo::getCertificateNum, Function.identity(), (entity1, entity2) -> entity1));
        //获取电子卡信息
        Map<String, ECardCompareExcelDTO> cardMap = null;
        Map<String, Integer> cardCount = new HashMap<>();  //可开通会员卡数量
        List<ECardCompareExcelDTO> cardList = electronicCardService.listAllECard(ThreadLocalCache.getHeaderUserInfo().getOperSubjectGuid());
        if (CollUtil.isNotEmpty(cardList)) {
            cardMap = cardList.stream().collect(Collectors.toMap(ECardCompareExcelDTO::getCardName, Function.identity(), (entity1, entity2) -> entity1));
            for (ECardCompareExcelDTO eCardCompareExcelDTO : cardList) {
                cardCount.put(eCardCompareExcelDTO.getCardGuid(), eCardCompareExcelDTO.getSurplusSendOpenCountLimit());
            }
        }
        List<MemberCardOpenDTO> cardOpenList = Lists.newArrayList();
        //成功导入的会员数据
        List<HsaOperationMemberInfo> hsaOperationMemberInfoList = Lists.newArrayList();
        //失败的会员数据
        List<HsaMemberImportRecordError> hsaMemberImportRecordErrorList = Lists.newArrayList();
        //导入记录
        HsaMemberImportRecord hsaMemberImportRecord = new HsaMemberImportRecord();
        hsaMemberImportRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberImportRecord.class.getSimpleName()));
        //返回表单失败数据
        List<HsmMemberUploadExcelError> hsmMemberUploadExcelErrorList = Lists.newArrayList();
        List<HsmPartnerMemberUploadExcelError> hsmPartnerMemberUploadExcelErrorList = Lists.newArrayList();
        //会员证件信息
        List<HsaMemberCertificateInfo> hsaMemberCertificateInfos = Lists.newArrayList();
        //初始成长值
        List<HsaGrowthValueDetail> hsaGrowthValueDetails = Lists.newArrayList();
        // 逐个保存会员，如有错误导入数据，生成错误文件并上传到云存储
        MemberUploadExcelVO responseMemberUploadExcel = new MemberUploadExcelVO();
        //手机号校验
        Map<String, String> checkIphoneNumMap = new HashMap<>();

        //证件校验
        Map<String, CertificateInfo> certificateInfoMap = new HashMap<>();
        //手动关联标签map key：会员guid   value：关联的标签集合
        Map<String, List<HsaLabelSetting>> manualRelationLabelMap = new HashMap<>();
        //需要自动关联标签的会员guid
        List<String> relationLabelMembers = Lists.newArrayList();
        //运营主体下的所有标签
        Map<String, HsaLabelSetting> labelMap = hsaLabelSettingMapper.selectList(new LambdaQueryWrapper<HsaLabelSetting>().eq(HsaLabelSetting::getOperSubjectGuid, ThreadLocalCache.getHeaderUserInfo().getOperSubjectGuid())).stream().collect(Collectors.toMap(HsaLabelSetting::getLabelName, Function.identity(), (entity1, entity2) -> entity1));
        // 查询运营主体下会员等级
        List<AdjustGradeQO> adjustGrades = Lists.newArrayList();
        Map<String, HsaMemberGradeInfoVO> memberGradeEffectiveMap = queryMemberGradeEffective();
        // 查询所在行业
        List<JobVO> jobList = JSON.parseArray(JsonFileUtil.read("job/job.json"), JobVO.class);
        // 查询省市区
        List<CountryVO> countryList = JSON.parseArray(JsonFileUtil.read("country/country.json"), CountryVO.class);

        //验证数据
        for (int i = 0, j = read.size(); i < j; i++) {
            HsmMemberUploadExcel hsmMemberUploadExcel = read.get(i);
            MemberInfoImportDTO importDTO = new MemberInfoImportDTO()
                    .setHsmMemberUploadExcel(hsmMemberUploadExcel)
                    .setOperationMemberCertificateNumMap(operationMemberCertificateNumMap)
                    .setHsaOperationMemberInfoList(hsaOperationMemberInfoList)
                    .setHsaMemberImportRecordErrorList(hsaMemberImportRecordErrorList)
                    .setHsaMemberImportRecord(hsaMemberImportRecord)
                    .setHsaMemberCertificateInfos(hsaMemberCertificateInfos)
                    .setCheckIphoneNumMap(checkIphoneNumMap)
                    .setCertificateInfoMap(certificateInfoMap)
                    .setCardOpenList(cardOpenList)
                    .setCardMap(cardMap)
                    .setCardCount(cardCount)
                    .setMemberPhoneMap(memberPhoneMap)
                    .setManualRelationLabelMap(manualRelationLabelMap)
                    .setRelationLabelMembers(relationLabelMembers)
                    .setLabelMap(labelMap)
                    .setHsaGrowthValueDetails(hsaGrowthValueDetails)
                    .setMemberGradeEffectiveMap(memberGradeEffectiveMap)
                    .setAdjustGrades(adjustGrades)
                    .setJobList(jobList)
                    .setCheckAccountNumMap(checkAccountNumMap)
                    .setCountryList(countryList);
            //数据校验
            HsmMemberUploadExcelError hsmMemberUploadExcelError = memberCheckUploadExcelUrl(importDTO);
            if (ObjectUtil.isNotNull(hsmMemberUploadExcelError)) {
                BeanUtil.copyProperties(hsmMemberUploadExcel, hsmMemberUploadExcelError);
                hsmMemberUploadExcelError.setPhoneCountryCode(hsmMemberUploadExcel.getPhoneCountryCode());
                hsmMemberUploadExcelError.setCertificateType(hsmMemberUploadExcel.getCertificateType());

                hsmMemberUploadExcelError.setAccountId(hsmMemberUploadExcel.getAccountId());
                hsmMemberUploadExcelError.setAccountPassword(hsmMemberUploadExcel.getAccountPassword());

                hsmMemberUploadExcelError.setMemberNum(hsmMemberUploadExcel.getMemberNum());
                hsmMemberUploadExcelErrorList.add(hsmMemberUploadExcelError);

                HsmPartnerMemberUploadExcelError partnerMemberUploadExcelError = new HsmPartnerMemberUploadExcelError();
                BeanUtil.copyProperties(hsmMemberUploadExcel, partnerMemberUploadExcelError);
                partnerMemberUploadExcelError.setErrorMessage(hsmMemberUploadExcelError.getErrorMessage());
                hsmPartnerMemberUploadExcelErrorList.add(partnerMemberUploadExcelError);
                log.info("错误信息-->>>序号:" + (i + 1) + "\t错误原因" + hsmMemberUploadExcelError.getErrorMessage());
            }
        }
        LocalDateTime now = LocalDateTime.now();
        //保存成功会员
        batchSucceedMember(hsaOperationMemberInfoList);
        //去除重复会员卡
        cardOpenList = cardOpenList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(x -> x.getCardGuid() + ";" + x.getMemberInfoGuid()))), ArrayList::new));
        //去保存开通会员卡
        electronicCardService.batchOpenWithExcelMember(cardOpenList);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(hsaOperationMemberInfoList)) {
            OperationLogDTO logDTO = OperationLogDTO.builder().contentGuidList(hsaOperationMemberInfoList.stream().map(HsaOperationMemberInfo::getGuid).collect(Collectors.toList())).headerUserInfo(ThreadLocalCache.getHeaderUserInfo()).logType(OperationLogTypeEnum.MEMBER_ACCOUNT).extraContent(LogContentConstant.REGISTERED_SUCCESS).build();
            operationLogService.dealBusinessLog(logDTO);
        }
        //保存会员证件
        batchSucceedMemberCertificateInfo(hsaMemberCertificateInfos);
        //保存失败会员
        memberBaseThreadExecutor.execute(() -> batchErrorMember(hsaMemberImportRecordErrorList));
        responseMemberUploadExcel.setHsaOperationMemberInfoList(hsaOperationMemberInfoList);
        //保存记录
        hsaMemberImportRecord.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        hsaMemberImportRecord.setImportTime(now);
        hsaMemberImportRecord.setExcelName(fileName);
        hsaMemberImportRecord.setImportErrorNum(hsaMemberImportRecordErrorList.size());
        hsaMemberImportRecord.setImportSucceedNum(hsaOperationMemberInfoList.size());
        hsaMemberImportRecord.setDownloadNum(0);
        hsaMemberImportRecord.setOperatorGuid(ThreadLocalCache.getHeaderUserInfo().getUserGuid());
        hsaMemberImportRecord.setOperatorName(ThreadLocalCache.getHeaderUserInfo().getUserName());
        //上传至阿里oos
        responseMemberUploadExcel.setSuccess(Math.max((read.size() - hsmMemberUploadExcelErrorList.size()), 0));
        responseMemberUploadExcel.setFail(hsmMemberUploadExcelErrorList.size());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formatDateTime = now.format(formatter);
        if (!hsmMemberUploadExcelErrorList.isEmpty()) {
            ExcelResult excelResult = generateUploadErrorList(hsmMemberUploadExcelErrorList, hsmPartnerMemberUploadExcelErrorList);
            byte[] write = ExcelWriteUtils.write(excelResult, ExcelType.XLSX);
            try {
                FileDto fileDto = new FileDto();
                fileDto.setFileContent(com.holderzone.framework.security.SecurityManager.entryptBase64(write));
                fileDto.setFileName(systemRoleHelper.getReplace("批量导入会员失败数据", ThreadLocalCache.getOperSubjectGuid()) + formatDateTime + "." + ExcelType.XLSX);
                String upload = fileOssService.upload(fileDto).replace("http", "https");
                log.info("错误信息文件下载路径->>>>>{}", upload);
                responseMemberUploadExcel.setFailUrl(upload);
                responseMemberUploadExcel.setSuccess(hsaOperationMemberInfoList.size());
                responseMemberUploadExcel.setFail(hsaMemberImportRecordErrorList.size());
                responseMemberUploadExcel.setImportRecordGuid(hsaMemberImportRecord.getGuid());
                hsaMemberImportRecord.setExcelAddress(upload);
            } catch (Exception e) {
                log.error("上传文件失败");
                e.printStackTrace();
                responseMemberUploadExcel.setFailUrl("上传错误信息失败");
            }
        }
        hsaMemberImportRecordMapper.insert(hsaMemberImportRecord);
        //添加会员标签
        addLabel(manualRelationLabelMap, relationLabelMembers, ThreadLocalCache.getHeaderUserInfo());
        responseMemberUploadExcel.setAdjustGrades(adjustGrades);
        return responseMemberUploadExcel;
    }

    private ExcelResult generateUploadErrorList(List<HsmMemberUploadExcelError> hsmMemberUploadExcelErrorList, List<HsmPartnerMemberUploadExcelError> hsmPartnerMemberUploadExcelErrorList) {
        if (ThreadLocalCache.isPartner()) {
            return com.aimilin.utils.BeanUtils.toResult(hsmPartnerMemberUploadExcelErrorList);
        } else {
            return com.aimilin.utils.BeanUtils.toResult(hsmMemberUploadExcelErrorList);
        }
    }


    private Map<String, HsaMemberGradeInfoVO> queryMemberGradeEffective() {
        Map<String, HsaMemberGradeInfoVO> memberGradeMap = Maps.newHashMap();
        QueryMemberEffectiveGradeVO memberEffectiveGradeVO = memberGradeInfoService.queryEffectiveGradeList(RoleTypeEnum.MEMBER.name(), null);
        if (Objects.isNull(memberEffectiveGradeVO) || !memberEffectiveGradeVO.getIsEnable()) {
            return memberGradeMap;
        }
        return memberEffectiveGradeVO.getHsaMemberGradeInfoVOS()
                .stream()
                .collect(Collectors.toMap(HsaMemberGradeInfoVO::getName, Function.identity(), (key1, key2) -> key1));
    }

    /**
     * 会员导出添加标签
     *
     * @param manualRelationLabelMap 手动关联标签map key：会员guid   value：关联的标签集合
     * @param relationLabelMembers   需要自动关联标签的会员guid
     * @param headerUserInfo         登录用户信息
     */
    private void addLabel(Map<String, List<HsaLabelSetting>> manualRelationLabelMap, List<String> relationLabelMembers, HeaderUserInfo headerUserInfo) {
        if (!MapUtil.isEmpty(manualRelationLabelMap)) {
            List<HsaMemberLabel> hsaMemberLabels = new ArrayList<>();
            List<MemberLabelRecordDTO> memberLabelRecordDTOS = new ArrayList<>();
            Set<Map.Entry<String, List<HsaLabelSetting>>> entrySet = manualRelationLabelMap.entrySet();
            for (Map.Entry<String, List<HsaLabelSetting>> entry : entrySet) {
                String memberGuid = entry.getKey();
                List<HsaLabelSetting> hsaLabelSettings = manualRelationLabelMap.get(memberGuid);
                for (HsaLabelSetting HsaLabelSetting : hsaLabelSettings) {
                    HsaMemberLabel label = new HsaMemberLabel().setGuid(guidGeneratorUtil.getStringGuid(HsaMemberLabel.class.getSimpleName())).setOperSubjectGuid(headerUserInfo.getOperSubjectGuid()).setOperatorName(headerUserInfo.getUserName()).setOperatorGuid(headerUserInfo.getUserGuid()).setLabelSettingGuid(HsaLabelSetting.getGuid()).setOperationMemberInfoGuid(memberGuid).setGmtModified(LocalDateTime.now()).setGmtCreate(LocalDateTime.now());
                    hsaMemberLabels.add(label);
                    memberLabelRecordDTOS.add(getMemberLabelRecord(HsaLabelSetting, label));
                }
            }

            if (!CollectionUtils.isEmpty(hsaMemberLabels)) {
                hsaMemberLabelService.saveBatch(hsaMemberLabels);
            }
            if (!CollectionUtils.isEmpty(memberLabelRecordDTOS)) {
                hsaMemberLabelRecordService.batchSaveMemberLabelRecord(memberLabelRecordDTOS, headerUserInfo);
            }

        }
        if (!CollectionUtils.isEmpty(relationLabelMembers)) {
            hsaLabelSettingService.refreshLabel(relationLabelMembers, null, BooleanEnum.FALSE.getCode(), SourceTypeEnum.ADD_BACKGROUND_IMPORT.getCode(), LabelTriggerTypeEnum.REGISTER_INFO.getCode());
        }
    }

    private MemberLabelRecordDTO getMemberLabelRecord(HsaLabelSetting label, HsaMemberLabel hsmMemberLabel) {
        MemberLabelRecordDTO labelRecordQO = new MemberLabelRecordDTO();
        labelRecordQO.setOperSubjectGuid(label.getOperSubjectGuid()).setIsConnection(ConnectionLabelTypeEnum.AUTOMATION.getCode()).setMemberLabelGuid(hsmMemberLabel.getGuid()).setConnectionTime(LocalDateTime.now()).setConnectionType(LabelConnectTypeEnum.MANUAL.getCode()).setMemberInfoGuid(hsmMemberLabel.getOperationMemberInfoGuid()).setLabelSettingGuid(hsmMemberLabel.getLabelSettingGuid()).setLabelType(label.getLabelType()).setLabelName(label.getLabelName());
        return labelRecordQO;
    }

    public void checkMemberPhone(List<HsmMemberUploadExcel> read, Map<String, String> memberPhoneMap) {
        List<MemberPhoneDTO> memberPhoneDTOS = new ArrayList<>();
        List<HsmMemberUploadExcel> hsmMemberUploadExcels = read.stream().filter(in -> StringUtils.hasText(in.getPhoneNum())).collect(Collectors.toList());
        int pointsDataLimit = 500;//限制条数
        int size = hsmMemberUploadExcels.size();
        //判断是否有必要分批
        if (pointsDataLimit < size) {
            int part = size / pointsDataLimit;//分批数
            for (int i = 0; i < part; i++) {
                //500条
                List<HsmMemberUploadExcel> listPage = hsmMemberUploadExcels.subList(0, pointsDataLimit);
                //获取数据
                List<String> phoneNum = listPage.stream().map(HsmMemberUploadExcel::getPhoneNum).collect(Collectors.toList());
                memberPhoneDTOS.addAll(hsaOperationMemberInfoMapper.listByPhone(phoneNum, ThreadLocalCache.getOperSubjectGuid()));
                //剔除
                hsmMemberUploadExcels.subList(0, pointsDataLimit).clear();
            }
            if (!hsmMemberUploadExcels.isEmpty()) {
                //获取数据
                List<String> phoneNum = hsmMemberUploadExcels.stream().map(HsmMemberUploadExcel::getPhoneNum).collect(Collectors.toList());
                memberPhoneDTOS.addAll(hsaOperationMemberInfoMapper.listByPhone(phoneNum, ThreadLocalCache.getOperSubjectGuid()));
            }
        } else {
            //获取数据
            List<String> phoneNum = hsmMemberUploadExcels.stream().map(HsmMemberUploadExcel::getPhoneNum).collect(Collectors.toList());
            memberPhoneDTOS.addAll(hsaOperationMemberInfoMapper.listByPhone(phoneNum, ThreadLocalCache.getOperSubjectGuid()));
        }
        if (CollUtil.isNotEmpty(memberPhoneDTOS)) {
            memberPhoneDTOS.forEach(in -> memberPhoneMap.put(in.getPhoneNum(), in.getPhoneCountryCode()));
        }
    }


    public void checkMemberAccount(List<HsmMemberUploadExcel> read, Map<String, String> memberAccountMap) {
        List<MemberAccountDTO> memberAccountDTOS = Lists.newArrayList();
        List<HsmMemberUploadExcel> hsmMemberUploadExcels = read.stream().filter(in -> StringUtils.hasText(in.getAccountId())).collect(Collectors.toList());
        int pointsDataLimit = 500;//限制条数
        int size = hsmMemberUploadExcels.size();
        //判断是否有必要分批
        if (pointsDataLimit < size) {
            int part = size / pointsDataLimit;
            for (int i = 0; i < part; i++) {
                //500条
                List<HsmMemberUploadExcel> listPage = hsmMemberUploadExcels.subList(0, pointsDataLimit);
                //获取数据
                List<String> accountIdNum = listPage.stream().map(HsmMemberUploadExcel::getAccountId).collect(Collectors.toList());
                memberAccountDTOS.addAll(hsaOperationMemberInfoMapper.listByAccountNum(accountIdNum, ThreadLocalCache.getOperSubjectGuid()));
                //剔除
                hsmMemberUploadExcels.subList(0, pointsDataLimit).clear();
            }
            if (!hsmMemberUploadExcels.isEmpty()) {
                //获取数据
                List<String> accountIdNum = hsmMemberUploadExcels.stream().map(HsmMemberUploadExcel::getAccountId).collect(Collectors.toList());
                memberAccountDTOS.addAll(hsaOperationMemberInfoMapper.listByAccountNum(accountIdNum, ThreadLocalCache.getOperSubjectGuid()));
            }
        } else {
            //获取数据
            List<String> accountIdNum = hsmMemberUploadExcels.stream().map(HsmMemberUploadExcel::getAccountId).collect(Collectors.toList());
            memberAccountDTOS.addAll(hsaOperationMemberInfoMapper.listByAccountNum(accountIdNum, ThreadLocalCache.getOperSubjectGuid()));
        }
        if (CollUtil.isNotEmpty(memberAccountDTOS)) {
            memberAccountDTOS.forEach(in -> memberAccountMap.put(in.getMemberAccount(), in.getMemberAccount()));
        }
    }

    /**
     * 批量保存会员
     *
     * @param batchSaveMemberInfoList batchSaveMemberInfoList
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSucceedMember(List<HsaOperationMemberInfo> batchSaveMemberInfoList) {
        if (CollUtil.isNotEmpty(batchSaveMemberInfoList)) {
            saveBatch(batchSaveMemberInfoList);
        }
    }

    /**
     * 批量保存会员证件信息
     *
     * @param hsaMemberCertificateInfoList hsaMemberCertificateInfoList
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSucceedMemberCertificateInfo(List<HsaMemberCertificateInfo> hsaMemberCertificateInfoList) {
        if (CollUtil.isNotEmpty(hsaMemberCertificateInfoList)) {
            hsaMemberCertificateInfoService.saveBatch(hsaMemberCertificateInfoList);
        }
    }

    /**
     * 批量保存失败会员
     *
     * @param hsaMemberImportRecordErrorList batchSaveMemberInfoList
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchErrorMember(List<HsaMemberImportRecordError> hsaMemberImportRecordErrorList) {
        if (CollUtil.isNotEmpty(hsaMemberImportRecordErrorList)) {
            hsaMemberImportRecordErrorService.saveBatch(hsaMemberImportRecordErrorList);
        }
    }

    /**
     * 整合验证
     *
     * @return HsmMemberUploadExcelError
     */
    private HsmMemberUploadExcelError memberCheckUploadExcelUrl(MemberInfoImportDTO importDTO) {
        HsmMemberUploadExcelError error = new HsmMemberUploadExcelError();
        StringBuilder stringBuilder = new StringBuilder();
        HsaOperationMemberInfo hsaOperationMemberInfo = new HsaOperationMemberInfo();

        //账户ID校验
        checkExcelAccountId(importDTO.getHsmMemberUploadExcel(), stringBuilder, importDTO.getCheckAccountNumMap(), hsaOperationMemberInfo);

        //基础信息校验
        ExcelMemberUtil.memberExcelCheck(importDTO, stringBuilder, hsaOperationMemberInfo);

        // 会员等级及等级有效期
        AdjustGradeQO adjustGradeQO = verifyMemberGrade(importDTO.getHsmMemberUploadExcel(), importDTO.getMemberGradeEffectiveMap(), stringBuilder);

        //会员标签
        Set<String> labelGuidSet = new HashSet<>();
        ExcelMemberUtil.checkExcelMemberLabel(importDTO.getHsmMemberUploadExcel(), importDTO.getLabelMap(), stringBuilder, labelGuidSet);

        // 验证证件类型/证件号码
        ExcelMemberUtil.verifyMemberCertificate(importDTO.getHsmMemberUploadExcel(), stringBuilder, importDTO.getOperationMemberCertificateNumMap(), importDTO.getCertificateInfoMap());

        //校验行业公司部门以及地址
        ExcelMemberUtil.checkExcelIndustrySector(importDTO.getHsmMemberUploadExcel(), stringBuilder, hsaOperationMemberInfo, importDTO.getJobList(), importDTO.getCountryList());

        //校验会员卡
        String memberCards = importDTO.getHsmMemberUploadExcel().getMemberCards();
        List<MemberCardOpenDTO> cardList;
        cardList = verifyMemberCard(importDTO.getCardMap(), importDTO.getCardCount(), memberCards, stringBuilder, labelGuidSet);

        try {
            //保存失败
            if (StringUtils.hasText(stringBuilder.toString())) {
                error.setErrorMessage(stringBuilder.toString());
                importErrorSave(stringBuilder, importDTO.getHsmMemberUploadExcel(), importDTO.getHsaMemberImportRecordErrorList(), importDTO.getHsaMemberImportRecord());
                return error;
            } else {
                //保存成功
                importSuccessSave(importDTO, hsaOperationMemberInfo, cardList, adjustGradeQO);
                return null;
            }
        } catch (Exception e) {
            log.error("导入失败==============", e);
            error.setErrorMessage("【导入失败】" + e.getMessage());
            return error;
        }
    }

    private void importSuccessSave(MemberInfoImportDTO importDTO,
                                   HsaOperationMemberInfo hsaOperationMemberInfo,
                                   List<MemberCardOpenDTO> cardList,
                                   AdjustGradeQO adjustGradeQO) {

        dealImportSuccessDTO(importDTO.getHsmMemberUploadExcel(),
                hsaOperationMemberInfo,
                importDTO.getHsaOperationMemberInfoList(),
                importDTO.getHsaMemberCertificateInfos(),
                cardList,
                importDTO.getHsaGrowthValueDetails(),
                adjustGradeQO);

        //合并会员卡list
        mergeCardList(importDTO.getCardOpenList(), cardList);
        //成功导入的并且没有添加标签的，加入自动关联标签集合
        if (!StringUtils.hasText(importDTO.getHsmMemberUploadExcel().getMemberLabels())) {
            importDTO.getRelationLabelMembers().add(hsaOperationMemberInfo.getGuid());
            //添加到收到关联标签map
        } else {
            importDTO.getRelationLabelMembers().add(hsaOperationMemberInfo.getGuid());
            String[] labelNames = importDTO.getHsmMemberUploadExcel().getMemberLabels().split(SEMICOLON);
            List<HsaLabelSetting> hsaLabelSettings = Lists.newArrayList();
            for (String labelName : labelNames) {
                hsaLabelSettings.add(importDTO.getLabelMap().get(labelName));
            }
            importDTO.getManualRelationLabelMap().put(hsaOperationMemberInfo.getGuid(), hsaLabelSettings);
        }
        // 会员等级导入处理
        appendAdjustGradeQO(hsaOperationMemberInfo, adjustGradeQO, importDTO.getAdjustGrades());
    }

    private void checkExcelAccountId(HsmMemberUploadExcel hsmMemberUploadExcel,
                                     StringBuilder stringBuilder,
                                     Map<String, String> checkAccountNumMap,
                                     HsaOperationMemberInfo hsaOperationMemberInfo) {
        if (StringUtils.hasText(hsmMemberUploadExcel.getAccountId())) {
            ExcelMemberUtil.checkAccount(hsmMemberUploadExcel, stringBuilder, checkAccountNumMap, hsaOperationMemberInfo);
        } else {
            hsaOperationMemberInfo.setMemberAccount(createMemberAccount());
            checkAccountNumMap.put(hsaOperationMemberInfo.getMemberAccount(), hsaOperationMemberInfo.getMemberAccount());
        }
    }

    private void dealImportSuccessDTO(HsmMemberUploadExcel hsmMemberUploadExcel,
                                      HsaOperationMemberInfo hsaOperationMemberInfo,
                                      List<HsaOperationMemberInfo> hsaOperationMemberInfoList,
                                      List<HsaMemberCertificateInfo> hsaMemberCertificateInfos,
                                      List<MemberCardOpenDTO> cardList,
                                      List<HsaGrowthValueDetail> hsaGrowthValueDetails,
                                      AdjustGradeQO adjustGradeQO) {
        copyProperties(hsmMemberUploadExcel, hsaOperationMemberInfo);
        MemberInfoImportSuccessDTO importSuccessDTO = new MemberInfoImportSuccessDTO()
                .setMemberUploadExcel(hsmMemberUploadExcel)
                .setOperationMemberInfo(hsaOperationMemberInfo)
                .setBatchSaveMemberInfoList(hsaOperationMemberInfoList)
                .setHsaMemberCertificateInfos(hsaMemberCertificateInfos)
                .setCardOpenList(cardList)
                .setHsaGrowthValueDetails(hsaGrowthValueDetails)
                .setAdjustGradeQO(adjustGradeQO);
        importSucceedSave(importSuccessDTO);
    }


    private void copyProperties(HsmMemberUploadExcel hsmMemberUploadExcel, HsaOperationMemberInfo hsaOperationMemberInfo) {
        BeanUtils.copyProperties(hsmMemberUploadExcel, hsaOperationMemberInfo);
        if (!StringUtils.isEmpty(hsmMemberUploadExcel.getCompanyName())) {
            hsaOperationMemberInfo.setWorkName(hsmMemberUploadExcel.getCompanyName());
        }
    }

    private void mergeCardList(List<MemberCardOpenDTO> cardOpenList, List<MemberCardOpenDTO> cardList) {
        if (CollUtil.isNotEmpty(cardList)) {
            cardOpenList.addAll(cardList);
        }
    }

    private List<MemberCardOpenDTO> verifyMemberCard(Map<String, ECardCompareExcelDTO> cardMap, Map<String, Integer> cardCount, String memberCards, StringBuilder stringBuilder, Set<String> labelGuid) {
        if (ObjectUtil.isNull(cardMap)) {
            return null;
        }
        List<MemberCardOpenDTO> cardOpenList = new ArrayList<>();
        List<MemberCardOpenDTO> memberCardOpenDTOS = returnRegisterCard(cardMap, cardCount, labelGuid); //注册时就开通会员卡
        //如果当前会员验证信息没有通过
        if (StringUtils.hasText(stringBuilder.toString())) {
            //回退已经扣减的注册卡的发卡数量
            rollbackCardCount(memberCardOpenDTOS, cardCount);
        } else { //没毛病就把注册时自动开通的电子卡加入cardOpenList
            cardOpenList.addAll(memberCardOpenDTOS);
        }
        if (StringUtils.isEmpty(memberCards)) {
            return cardOpenList;
        }
        String cardString = memberCards.replace(" ", "");  //去除会员卡列表中的空格
        //去校验会员卡信息
        String[] cards = cardString.split(";");
        boolean isOpen;  //当前会员卡是否能开通
        for (String s : cards) {
            ECardCompareExcelDTO card = cardMap.get(s);
            isOpen = cardValidation(s, card, cardCount, stringBuilder, memberCardOpenDTOS); //验证当前会员卡是否能开通
            if (Boolean.FALSE.equals(isOpen)) {
                continue;
            }
            //都满足则加入对象
            MemberCardOpenDTO openDTO = new MemberCardOpenDTO();
            openDTO.setCardGuid(card.getCardGuid());
            openDTO.setCardName(card.getCardName());
            openDTO.setSendCountLimit(card.getSendCountLimit());
            cardOpenList.add(openDTO);
        }

        return cardOpenList;
    }


    private AdjustGradeQO verifyMemberGrade(HsmMemberUploadExcel hsmMemberUploadExcel, Map<String, HsaMemberGradeInfoVO> memberGradeEffectiveMap,
                                            StringBuilder stringBuilder) {
        AdjustGradeQO adjustGradeQO = null;
        String gradeInfo = hsmMemberUploadExcel.getMemberGradeInfo();
        String gradeEffective = hsmMemberUploadExcel.getGradeEffective();
        if (StringUtils.hasText(gradeInfo)) {
            HsaMemberGradeInfoVO memberGradeInfo = memberGradeEffectiveMap.get(gradeInfo);
            VerifyUtil.isTure(Objects.isNull(memberGradeInfo))
                    .trueHandle(() -> stringBuilder.append(systemRoleHelper.getReplace("【会员等级】填写错误", ThreadLocalCache.getOperSubjectGuid()))
                            .append(StringConstant.STR_LINE_FEED));
            if (Objects.nonNull(memberGradeInfo) && StringUtils.hasText(gradeEffective)) {
                adjustGradeQO = queryAdjustGrade(memberGradeInfo, gradeEffective);
                if (Objects.isNull(adjustGradeQO)) {
                    stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_GRADE_EFFECTIVE).append(StringConstant.STR_LINE_FEED);
                }
            } else {
                stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_GRADE_EFFECTIVE).append(StringConstant.STR_LINE_FEED);
            }
        } else {
            VerifyUtil.isTure(StringUtils.hasText(gradeEffective))
                    .trueHandle(() -> stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_GRADE_EFFECTIVE).append(StringConstant.STR_LINE_FEED));
        }
        return adjustGradeQO;
    }

    private AdjustGradeQO queryAdjustGrade(HsaMemberGradeInfoVO memberGradeInfo, String gradeEffective) {
        for (PayRuleDTO payRule : memberGradeInfo.getPayRuleList()) {
            // todo: 需要验证下导入的付费规则匹配新的期限？
            String payRuleStr = EffectiveDurationTypeEnum.fromValue(payRule.getUnit()).getMsg();
            if (payRuleStr.equals(gradeEffective)) {
                AdjustGradeQO adjustGradeQO = new AdjustGradeQO();
                adjustGradeQO.setMemberInfoGradeGuid(memberGradeInfo.getGuid());
                adjustGradeQO.setNum(payRule.getNum());
                adjustGradeQO.setUnit(payRule.getUnit());
                return adjustGradeQO;
            }
        }
        return null;
    }

    private void appendAdjustGradeQO(HsaOperationMemberInfo hsaOperationMemberInfo, AdjustGradeQO adjustGradeQO,
                                     List<AdjustGradeQO> adjustGrades) {
        if (ThreadLocalCache.isPartner() && Objects.nonNull(adjustGradeQO)) {
            hsaOperationMemberInfo.setMemberGradeInfoGuid(adjustGradeQO.getMemberInfoGradeGuid());
            AdjustGradeQO existAdjustGradeQO = adjustGrades
                    .stream()
                    .filter(e -> e.getMemberInfoGradeGuid().equals(adjustGradeQO.getMemberInfoGradeGuid()) &&
                            e.getUnit().equals(adjustGradeQO.getUnit()) && e.getNum().equals(adjustGradeQO.getNum()))
                    .findFirst().orElse(null);
            // 判断集合中是否存在等级
            if (Objects.nonNull(existAdjustGradeQO)) {
                existAdjustGradeQO.getMemberInfoGuidList().add(hsaOperationMemberInfo.getGuid());
            } else {
                AdjustGradeQO currentAdjustGradeQO = new AdjustGradeQO();
                currentAdjustGradeQO.setMemberInfoGradeGuid(adjustGradeQO.getMemberInfoGradeGuid());
                currentAdjustGradeQO.setNum(adjustGradeQO.getNum());
                currentAdjustGradeQO.setUnit(adjustGradeQO.getUnit());
                currentAdjustGradeQO.setRoleType(RoleTypeEnum.MEMBER.name());
                currentAdjustGradeQO.setMemberInfoGuidList(Lists.newArrayList(hsaOperationMemberInfo.getGuid()));
                currentAdjustGradeQO.setSource(SourceTypeEnum.ADD_BACKGROUND_IMPORT.getCode());
                currentAdjustGradeQO.setRemark(SourceTypeEnum.ADD_BACKGROUND_IMPORT.getDes());
                currentAdjustGradeQO.setChangeType(SumValueChangeEnum.INITIAL.getCode());
                adjustGrades.add(currentAdjustGradeQO);
            }
        }
    }

    /**
     * 如果当前会员不能导入，那么回退，注册时扣减的电子卡数量
     *
     * @param memberCardOpenDTOS 注册时电子集合
     * @param cardCount          电子卡发卡数量
     */
    private void rollbackCardCount(List<MemberCardOpenDTO> memberCardOpenDTOS, Map<String, Integer> cardCount) {
        if (ObjectUtils.isEmpty(memberCardOpenDTOS) || memberCardOpenDTOS.isEmpty()) {
            return;
        }
        for (MemberCardOpenDTO memberCardOpenDTO : memberCardOpenDTOS) {
            Integer count = cardCount.get(memberCardOpenDTO.getCardGuid());
            //回退电子卡发卡数量
            cardCount.put(memberCardOpenDTO.getCardGuid(), count + 1);
        }
    }

    /**
     * 会员卡数量验证
     *
     * @param card               当前验证会员卡
     * @param cardCount          当前会员卡可开卡数量
     * @param stringBuilder      错误提示
     * @param memberCardOpenDTOS 注册时就已开通的电子卡
     * @return 是否能开通
     */
    private boolean cardValidation(String cardName, ECardCompareExcelDTO card, Map<String, Integer> cardCount, StringBuilder stringBuilder, List<MemberCardOpenDTO> memberCardOpenDTOS) {
        if (ObjectUtil.isNull(card)) {
            stringBuilder.append(systemRoleHelper.getReplace(ExcelImportConstant.MEMBER_CARD, ThreadLocalCache.getOperSubjectGuid()));
            stringBuilder.append("(").append(cardName).append(")" + ExcelImportConstant.DOES_NOT_EXIST);
            stringBuilder.append(ExcelImportConstant.IMPORT_SEPARATOR);
            rollbackCardCount(memberCardOpenDTOS, cardCount);  //回退注册时电子卡扣减数量
            return false;
        }
        if (ObjectUtil.equal(3, card.getCardStatus())) {
            stringBuilder.append(systemRoleHelper.getReplace(ExcelImportConstant.MEMBER_CARD, ThreadLocalCache.getOperSubjectGuid()));
            stringBuilder.append("(").append(card.getCardName()).append(")").append(ExcelImportConstant.DISABLED);
            stringBuilder.append(ExcelImportConstant.IMPORT_SEPARATOR);
            rollbackCardCount(memberCardOpenDTOS, cardCount);  //回退注册时电子卡扣减数量
            return false;
        }
        if (ObjectUtil.equal(BooleanEnum.FALSE.getCode(), card.getIsSupportElectronicCard())) {
            stringBuilder.append(systemRoleHelper.getReplace(ExcelImportConstant.MEMBER_CARD, ThreadLocalCache.getOperSubjectGuid()));
            stringBuilder.append("(").append(card.getCardName()).append(")").append(ExcelImportConstant.DO_NOT_OPEN);
            stringBuilder.append(ExcelImportConstant.IMPORT_SEPARATOR);
            rollbackCardCount(memberCardOpenDTOS, cardCount);  //回退注册时电子卡扣减数量
            return false;
        }

        // 验证会员卡数量是否足够
        Integer count = cardCount.get(card.getCardGuid());
        Integer sendLimit = Optional.ofNullable(card.getSendCountLimit()).orElse(1);
        log.info("会员卡guid:{},count:{},sendLimit:{}", card.getCardGuid(), count, sendLimit);
        if (sendLimit == 0) {
            return true;
        }
        if (isRepeat(card, memberCardOpenDTOS)) {  //判断注册时，是否已经开通
            return false;
        }
        if (ObjectUtil.isNotNull(count) && count > 0) {  //会员卡数量>0
            cardCount.put(card.getCardGuid(), count - 1);
        } else {
            stringBuilder.append(systemRoleHelper.getReplace(ExcelImportConstant.MEMBER_CARD, ThreadLocalCache.getOperSubjectGuid()));
            stringBuilder.append("(").append(card.getCardName()).append(")").append(ExcelImportConstant.DO_NOT_OPEN);
            stringBuilder.append(ExcelImportConstant.IMPORT_SEPARATOR);
            rollbackCardCount(memberCardOpenDTOS, cardCount);  //回退注册时电子卡扣减数量
            return false;
        }
        return true;
    }

    /**
     * 判断当前是否重复开通电子卡
     *
     * @param card               电子卡
     * @param memberCardOpenDTOS 准备开通的电子卡集合
     * @return 操作结果
     */
    private boolean isRepeat(ECardCompareExcelDTO card, List<MemberCardOpenDTO> memberCardOpenDTOS) {
        if (ObjectUtils.isEmpty(memberCardOpenDTOS) || memberCardOpenDTOS.isEmpty()) {
            return false;
        }
        Optional<MemberCardOpenDTO> memberCard = memberCardOpenDTOS.stream().filter(x -> card.getCardGuid().equals(x.getCardGuid())).findFirst();
        return memberCard.isPresent();
    }

    /**
     * 返回所有注册时开通的电子卡
     *
     * @param cardMap   会员卡集合
     * @param cardCount 会员卡数量集合
     * @return 注册可开通电子卡
     */
    private List<MemberCardOpenDTO> returnRegisterCard(Map<String, ECardCompareExcelDTO> cardMap, Map<String, Integer> cardCount, Set<String> labelGuid) {
        List<MemberCardOpenDTO> cardOpenList = new ArrayList<>();  //需要开通的会员卡集合
        List<String> cardGuids = new ArrayList<>();  //会员卡guid
        cardMap.forEach((k, v) -> {
            cardGuids.add(v.getCardGuid());
        });
        //会员卡对应规则
        List<HsaCardOpenRule> cardOpenRules = hsaCardOpenRuleMapper.selectList(new LambdaQueryWrapper<HsaCardOpenRule>().eq(HsaCardOpenRule::getIsSupportElectronicCard, BooleanEnum.TRUE.getCode()).eq(HsaCardOpenRule::getElectronicOpenWay, ElectronicOpenWayEnum.REGISTER_OPEN.getCode()).eq(HsaCardOpenRule::getSendStatus, SendCardStateEnum.CARD_STATE_START.getCode()).in(CollUtil.isNotEmpty(cardGuids), HsaCardOpenRule::getCardGuid, cardGuids));
        if (!CollUtil.isNotEmpty(cardOpenRules)) {
            return cardOpenList;
        }

        SaveOperationMemberInfoQO memberInfoQO = new SaveOperationMemberInfoQO();  //参数
        memberInfoQO.setSourceType(SourceTypeEnum.ADD_BACKGROUND_IMPORT.getCode());  //0后台添加

        cardMap.forEach((k, v) -> {
            for (HsaCardOpenRule cardOpenRule : cardOpenRules) {
                if (v.getCardGuid().equals(cardOpenRule.getCardGuid())) {
                    // 验证会员卡数量是否足够
                    Integer count = cardCount.get(cardOpenRule.getCardGuid());
                    Integer sendLimit = Optional.ofNullable(cardOpenRule.getSendCountLimit()).orElse(1);
                    if (sendLimit == 1 && count <= 0) {  //会员卡数量<= 0 则不开通
                        break;
                    }
                    if (Boolean.TRUE.equals(checkOpenCard(cardOpenRule, SourceTypeEnum.ADD_BACKGROUND_IMPORT.getCode(), labelGuid))) {  //验证当前会员是否可开通电子卡
                        MemberCardOpenDTO openDTO = new MemberCardOpenDTO();
                        openDTO.setCardGuid(String.valueOf(v.getCardGuid()));
                        openDTO.setCardName(v.getCardName());
                        openDTO.setElectronicOpenCardWay(ElectronicCardOpenWayEnum.EGISTRATION_COMPLETE_AUTO.getCode());
                        openDTO.setOpenWay(ElectronicCardOpenTypeEnum.RECHARGE_OPENING.getCode());
                        openDTO.setSource(ElectronicCardOpenTypeEnum.RECHARGE_OPENING.getCode());
                        openDTO.setEnterpriseGuid(cardOpenRule.getEnterpriseGuid());
                        openDTO.setOperSubjectGuid(cardOpenRule.getOperSubjectGuid());
                        cardOpenList.add(openDTO);

                        cardCount.put(cardOpenRule.getCardGuid(), count - 1); //发卡数量-1
                    }
                    break;
                }
            }
        });
        log.info("当前会员注册时可开通的会员电子卡集合：{}", cardOpenList);
        return cardOpenList;
    }

    private void checkCertificateInfoMap(HsmMemberUploadExcel hsmMemberUploadExcel, Map<String, CertificateInfo> certificateInfoMap, StringBuilder stringBuilder, CertificateTypeEnum certificateTypeEnum) {
        if (CollUtil.isNotEmpty(certificateInfoMap) && certificateInfoMap.containsKey(hsmMemberUploadExcel.getCertificateNum())) {
            CertificateInfo certificateInfo = certificateInfoMap.get(hsmMemberUploadExcel.getCertificateNum());
            if (certificateTypeEnum.getCode() == CertificateTypeEnum.CERTIFICATE_OTHER.getCode()) {
                if (certificateInfo.getCertificateName().equals(hsmMemberUploadExcel.getCertificateType())) {
                    stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_ID_CARD_EXIST + "\r\n");
                }
            } else {
                if (certificateInfo.getCertificateType().equals(certificateTypeEnum.getCode())) {
                    stringBuilder.append(ExcelImportConstant.IMPORT_ERROR_MEMBER_ID_CARD_EXIST + "\r\n");
                }
            }
        }
    }

    /**
     * 保存导入成功用户
     */
    public void importSucceedSave(MemberInfoImportSuccessDTO importSuccessDTO) {
        HsmMemberUploadExcel memberUploadExcel = importSuccessDTO.getMemberUploadExcel();
        HsaOperationMemberInfo operationMemberInfo = importSuccessDTO.getOperationMemberInfo();
        List<HsaOperationMemberInfo> batchSaveMemberInfoList = importSuccessDTO.getBatchSaveMemberInfoList();
        List<HsaMemberCertificateInfo> hsaMemberCertificateInfos = importSuccessDTO.getHsaMemberCertificateInfos();
        List<MemberCardOpenDTO> cardOpenList = importSuccessDTO.getCardOpenList();
        AdjustGradeQO adjustGradeQO = importSuccessDTO.getAdjustGradeQO();
        // 复制联盟、运营主体、企业等信息
        String guid = guidGeneratorUtil.getStringGuid(HsaOperationMemberInfo.class.getSimpleName());
        buildOperationMemberInfo(operationMemberInfo, guid, memberUploadExcel, hsaMemberCertificateInfos);
        batchSaveMemberInfoList.add(operationMemberInfo);
        //添加会员卡信息
        if (CollUtil.isNotEmpty(cardOpenList)) {
            cardOpenList.forEach(obj -> {
                obj.setMemberInfoGuid(String.valueOf(guid));
                obj.setMemberPhoneNum(operationMemberInfo.getPhoneNum());
            });
        }
        VerifyUtil.isTure(hasDefaultMemberGrade(adjustGradeQO))
                .trueHandle(() -> {
                    MemberGradeInfoBaseVO memberGradeInfoBaseVO = this.calculateExtraAmount(operationMemberInfo.getMemberGrowthValue());
                    SaveOperationMemberInfoQO request = new SaveOperationMemberInfoQO();
                    request.setMemberGradeInfoGuid(memberGradeInfoBaseVO.getGuid()).setSourceType(SourceTypeEnum.ADD_BACKGROUND_IMPORT.getCode()).setOperSubjectGuid(operationMemberInfo.getOperSubjectGuid());
                    //解决excel导入添加会员时,报错bug
                    operationMemberInfo.setMemberGradeInfoGuid(memberGradeInfoBaseVO.getGuid());
                    sendInitialMemberGrowthValue(operationMemberInfo, ThreadLocalCache.getHeaderUserInfo(), request);
                });
        //初始化积分
        UpdateIntegralValueQO updateIntegralValueQO = new UpdateIntegralValueQO(
                SumValueChangeEnum.INITIAL.getCode(),
                NumberConstant.NUMBER_0,
                operationMemberInfo.getMemberIntegral(),
                null,
                StringConstant.STR_DEFAULT_STORE,
                null);
        initMemberIntegral(operationMemberInfo, ThreadLocalCache.getHeaderUserInfo(), updateIntegralValueQO);
        //完善了个人信息
        PerfectPersonalDetailDTO perfectPersonalDetailDTO = new PerfectPersonalDetailDTO();
        if (operationMemberInfo.getSex() != 0) {
            perfectPersonalDetailDTO.setPerfectSexTime(DateUtil.formatLocalDateTime(LocalDateTime.now(), DateUtil.PATTERN_DATETIME));
        }
        if (!Objects.isNull(operationMemberInfo.getBirthday())) {
            perfectPersonalDetailDTO.setPerfectBirthdayTime(DateUtil.formatLocalDateTime(LocalDateTime.now(), DateUtil.PATTERN_DATETIME));
        }
        operationMemberInfo.setPerfectPersonalDetailJson(JSON.toJSONString(perfectPersonalDetailDTO));
    }

    private void buildOperationMemberInfo(HsaOperationMemberInfo operationMemberInfo, String guid, HsmMemberUploadExcel memberUploadExcel, List<HsaMemberCertificateInfo> hsaMemberCertificateInfos) {
        BeanUtil.copyProperties(ThreadLocalCache.getHeaderUserInfo(), operationMemberInfo);
        operationMemberInfo.setOperatorGuid(ThreadLocalCache.getHeaderUserInfo().getUserGuid());
        operationMemberInfo.setOperatorName(getOperationName());
        operationMemberInfo.setGuid(guid);


        if (StringUtils.hasText(memberUploadExcel.getBirthday())) {
            LocalDate birthday = LocalDate.parse(memberUploadExcel.getBirthday(), DateTimeFormatter.ofPattern("y.M.d"));
            operationMemberInfo.setBirthday(birthday);
        }
        addMemberCertificateInfos(memberUploadExcel, guid, hsaMemberCertificateInfos);
        addMemberInfo(memberUploadExcel, operationMemberInfo);
        operationMemberInfo.setUserName(Optional.ofNullable(memberUploadExcel.getUserName()).orElse(""));
        operationMemberInfo.setPayPassword(StringUtils.isEmpty(memberUploadExcel.getPayPassword()) ? payPassword : SecureUtil.md5(memberUploadExcel.getPayPassword()).toUpperCase());
        operationMemberInfo.setSourceAscription(SourceAscriptionEnum.OPERATION.getCode());
        operationMemberInfo.setSourceType(SourceTypeEnum.ADD_BACKGROUND_IMPORT.getCode());
        operationMemberInfo.setAccountState(BooleanEnum.FALSE.getCode());
        operationMemberInfo.setIsDelete(BooleanEnum.FALSE.getCode());
        if (StringUtils.hasText(memberUploadExcel.getIntegral())) {
            operationMemberInfo.setMemberIntegral(Integer.parseInt(memberUploadExcel.getIntegral()));
        }
        if (StringUtils.hasText(memberUploadExcel.getGrowthValue())) {
            operationMemberInfo.setMemberGrowthValue(Integer.parseInt(memberUploadExcel.getGrowthValue()));
        }
        operationMemberInfo.setPhoneCountryCode(memberUploadExcel.getPhoneCountryCode());
        operationMemberInfo.setStoreName(NO_AFFILIATED_STORE);
    }

    private void addMemberCertificateInfos(HsmMemberUploadExcel memberUploadExcel, String guid, List<HsaMemberCertificateInfo> hsaMemberCertificateInfos) {
        if (StringUtils.hasText(memberUploadExcel.getCertificateType()) && StringUtils.hasText(memberUploadExcel.getCertificateNum())) {
            CertificateTypeEnum certificateTypeEnum = CertificateTypeEnum.asEnum(memberUploadExcel.getCertificateType());
            HsaMemberCertificateInfo memberCertificateInfo = new HsaMemberCertificateInfo();
            if (certificateTypeEnum.getCode() == CertificateTypeEnum.CERTIFICATE_OTHER.getCode()) {
                memberCertificateInfo.setCertificateName(memberUploadExcel.getCertificateName());
            }
            memberCertificateInfo.setCertificateNum(memberUploadExcel.getCertificateNum());
            memberCertificateInfo.setCertificateType(certificateTypeEnum.getCode());
            memberCertificateInfo.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberCertificateInfo.class.getSimpleName()));
            memberCertificateInfo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            memberCertificateInfo.setOperationMemberInfoGuid(guid);
            hsaMemberCertificateInfos.add(memberCertificateInfo);
        }
    }

    private static void addMemberInfo(HsmMemberUploadExcel memberUploadExcel, HsaOperationMemberInfo operationMemberInfo) {
        if (StringUtils.hasText(memberUploadExcel.getWorkName())) {
            operationMemberInfo.setWorkName(memberUploadExcel.getWorkName());
        }
        if (StringUtils.hasText(memberUploadExcel.getEmail())) {
            operationMemberInfo.setEmail(memberUploadExcel.getEmail());
        }
        if (StringUtils.hasText(memberUploadExcel.getDepartmentName())) {
            operationMemberInfo.setDepartmentName(memberUploadExcel.getDepartmentName());
        }
        if (StringUtils.hasText(memberUploadExcel.getContactAddress())) {
            operationMemberInfo.setContactAddress(memberUploadExcel.getContactAddress());
        }
        if (!ObjectUtils.isEmpty(memberUploadExcel.getMemberAccountMoney())) {
            operationMemberInfo.setMemberAccountMoney(new BigDecimal(memberUploadExcel.getMemberAccountMoney()));
        }
        if (!ObjectUtils.isEmpty(memberUploadExcel.getMemberGiftAccountMoney())) {
            operationMemberInfo.setMemberGiftAccountMoney(new BigDecimal(memberUploadExcel.getMemberGiftAccountMoney()));
        }
        if (!ObjectUtils.isEmpty(memberUploadExcel.getMemberSubsidyAmount())) {
            operationMemberInfo.setMemberSubsidyAmountMoney(new BigDecimal(memberUploadExcel.getMemberSubsidyAmount()));
        }
    }

    /**
     * 是否有导入会员等级
     */
    private boolean hasDefaultMemberGrade(AdjustGradeQO adjustGradeQO) {
        return !ThreadLocalCache.isPartner() || Objects.isNull(adjustGradeQO);
    }

    /**
     * 保存失败用户
     *
     * @param memberUploadExcel 会员导入信息
     */
    public void importErrorSave(StringBuilder stringBuilder, HsmMemberUploadExcel memberUploadExcel, List<HsaMemberImportRecordError> importRecordErrors, HsaMemberImportRecord hsaMemberImportRecord) {
        HsaMemberImportRecordError hsaMemberImportRecordError = new HsaMemberImportRecordError();
        // 复制联盟、运营主体、企业等信息
        BeanUtils.copyProperties(ThreadLocalCache.getHeaderUserInfo(), hsaMemberImportRecordError);
        BeanUtils.copyProperties(memberUploadExcel, hsaMemberImportRecordError);
        hsaMemberImportRecordError.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberImportRecordError.class.getSimpleName()));
        hsaMemberImportRecordError.setMemberImportRecordGuid(hsaMemberImportRecord.getGuid());
        hsaMemberImportRecordError.setErrorCauseJson(stringBuilder.toString());
        hsaMemberImportRecordError.setMemberLabelJson(memberUploadExcel.getMemberLabels());
        hsaMemberImportRecordError.setMemberCardJson(memberUploadExcel.getMemberCards());
        hsaMemberImportRecordError.setMemberIntegral(StringUtils.isEmpty(memberUploadExcel.getIntegral()) ? null : memberUploadExcel.getIntegral());
        hsaMemberImportRecordError.setMemberGrowthValue(StringUtils.isEmpty(memberUploadExcel.getGrowthValue()) ? null : memberUploadExcel.getGrowthValue());
        hsaMemberImportRecordError.setPhoneCountryCode(memberUploadExcel.getPhoneCountryCode());
        hsaMemberImportRecordError.setMemberNum(memberUploadExcel.getMemberNum());
        if (StringUtils.hasText(memberUploadExcel.getMemberAccountMoney())) {
            hsaMemberImportRecordError.setMemberAccountMoney(memberUploadExcel.getMemberAccountMoney());
        }
        if (StringUtils.hasText(memberUploadExcel.getMemberGiftAccountMoney())) {
            hsaMemberImportRecordError.setMemberGiftAccountMoney(memberUploadExcel.getMemberGiftAccountMoney());
        }
        if (StringUtils.hasText(memberUploadExcel.getMemberSubsidyAmount())) {
            hsaMemberImportRecordError.setSubsidyAmount(memberUploadExcel.getMemberSubsidyAmount());
        }

        importRecordErrors.add(hsaMemberImportRecordError);
    }

    /**
     * 生成账号
     *
     * @return String
     */
    public String createMemberAccount() {
        String memberAccount = NumberUtil.buildNumToStr(12);
        HsaOperationMemberInfo one = getMemberInfoAccount(memberAccount);
        if (ObjectUtils.isEmpty(one)) {
            return memberAccount;
        } else {
            return createMemberAccount();
        }
    }


    public Boolean checkMemberAccount(String memberAccount) {
        HsaOperationMemberInfo one = getMemberInfoAccount(memberAccount);

        return ObjectUtils.isEmpty(one);
    }

    @Override
    public MemberBasicInfoVO getMemberInfo(MemberQueryDTO queryDTO) {
        log.info("查询会员信息HeaderUserInfo入参:{}", JSON.toJSONString(ThreadLocalCache.getHeaderUserInfo()));
        Pair<Boolean, HsaMemberInfoWeChat> booleanHsaMemberInfoWeChatPair = queryMemberInfoWeChatRelationMemberGuid(queryDTO);
        if (Boolean.FALSE.equals(booleanHsaMemberInfoWeChatPair.getKey())) {
            return null;
        }
        Pair<Boolean, HsaMemberInfoCard> booleanHsaMemberInfoCardPair = queryRelationMemberGuidByMemberInfoCardGuid(queryDTO);
        if (Boolean.FALSE.equals(booleanHsaMemberInfoCardPair.getKey())) {
            return null;
        }
        booleanHsaMemberInfoCardPair = queryRelationMemberGuidByCardNum(queryDTO);
        if (Boolean.FALSE.equals(booleanHsaMemberInfoCardPair.getKey())) {
            return null;
        }
        // 查询会员信息
        HsaOperationMemberInfo memberInfo = hsaOperationMemberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .eq(HsaOperationMemberInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaOperationMemberInfo::getIsDelete, BooleanEnum.FALSE.getCode())
                .eq(StrUtil.isNotEmpty(queryDTO.getPhoneNum()), HsaOperationMemberInfo::getPhoneNum, queryDTO.getPhoneNum())
                .eq(StrUtil.isNotEmpty(queryDTO.getMemberGuid()), HsaOperationMemberInfo::getGuid, queryDTO.getMemberGuid()));
        log.info("查询会员信息:{}", JSON.toJSONString(memberInfo));
        if (memberInfo == null) {
            return null;
        }
        // 查询微信信息
        HsaMemberInfoWeChat hsaMemberInfoWeChat = booleanHsaMemberInfoWeChatPair.getValue();
        if (Objects.isNull(hsaMemberInfoWeChat)) {
            hsaMemberInfoWeChat = hsaMemberInfoWeChatMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoWeChat>()
                    .eq(HsaMemberInfoWeChat::getOperationMemberInfoGuid, memberInfo.getGuid())
                    .eq(HsaMemberInfoWeChat::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                    .orderByDesc(HsaMemberInfoWeChat::getGmtCreate).last("limit 1"));
        }
        MemberBasicInfoVO memberInfoBasicVO = new MemberBasicInfoVO();
        BeanUtil.copyProperties(memberInfo, memberInfoBasicVO);
        memberInfoBasicVO.setOperSubjectGuid(memberInfo.getOperSubjectGuid());
        memberInfoBasicVO.setIntegral(memberInfo.getMemberIntegral());
        memberInfoBasicVO.setOpenid(Optional.ofNullable(hsaMemberInfoWeChat).map(HsaMemberInfoWeChat::getOpenId).orElse(null));
        memberInfoBasicVO.setGmtCreate(memberInfo.getGmtCreate().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME)));


        // 充值提示处理
        String rechargeTips = rechargeGiftActivityHelper.buildRechargeTipsByAll(memberInfo.getGuid(),
                StringUtils.isEmpty(memberInfo.getMemberPaidGradeInfoGuid()) ? memberInfo.getMemberGradeInfoGuid()
                        : memberInfo.getMemberPaidGradeInfoGuid());
        memberInfoBasicVO.setRechargeTips(rechargeTips);

        ListMiniProgramCardQO cardQO = new ListMiniProgramCardQO();
        cardQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        cardQO.setMemberInfoGuid(memberInfo.getGuid());
        cardQO.setType(0);
        cardQO.setGiftActivity(true);
        List<MiniProgramCardDTO> miniProgramCard = hsaMemberInfoCardMapper.findMiniProgramCard(cardQO);
        memberInfoBasicVO.setValidCardNum(miniProgramCard.size());
        log.info("查询会员返回信息:{}", JSON.toJSONString(memberInfoBasicVO));
        return memberInfoBasicVO;
    }


    /**
     * 通过openId查询memberInfoGuid
     */
    private Pair<Boolean, HsaMemberInfoWeChat> queryMemberInfoWeChatRelationMemberGuid(MemberQueryDTO queryDTO) {
        if (StringUtils.isEmpty(queryDTO.getOpenid())) {
            return Pair.of(true, null);
        }
        HsaMemberInfoWeChat hsaMemberInfoWeChat = hsaMemberInfoWeChatMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoWeChat>()
                .eq(HsaMemberInfoWeChat::getOpenId, queryDTO.getOpenid())
                .eq(HsaMemberInfoWeChat::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .orderByDesc(HsaMemberInfoWeChat::getGmtCreate).last("limit 1"));
        if (hsaMemberInfoWeChat == null) {
            return Pair.of(false, null);
        }
        queryDTO.setMemberGuid(hsaMemberInfoWeChat.getOperationMemberInfoGuid());
        return Pair.of(true, hsaMemberInfoWeChat);
    }


    /**
     * 通过memberInfoCardGuid查询memberInfoGuid
     */
    private Pair<Boolean, HsaMemberInfoCard> queryRelationMemberGuidByMemberInfoCardGuid(MemberQueryDTO queryDTO) {
        if (StringUtils.isEmpty(queryDTO.getMemberInfoCardGuid())) {
            return Pair.of(true, null);
        }
        HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getGuid, queryDTO.getMemberInfoCardGuid()));
        if (hsaMemberInfoCard == null) {
            return Pair.of(false, null);
        }
        queryDTO.setMemberGuid(hsaMemberInfoCard.getMemberInfoGuid());
        return Pair.of(true, hsaMemberInfoCard);
    }


    /**
     * 通过cardNum查询memberInfoGuid
     */
    private Pair<Boolean, HsaMemberInfoCard> queryRelationMemberGuidByCardNum(MemberQueryDTO queryDTO) {
        if (StringUtils.isEmpty(queryDTO.getCardNum())) {
            return Pair.of(true, null);
        }
        HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getElectronicCardNum, queryDTO.getCardNum())
                .eq(HsaMemberInfoCard::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
        if (hsaMemberInfoCard == null) {
            return Pair.of(false, null);
        }
        queryDTO.setMemberGuid(hsaMemberInfoCard.getMemberInfoGuid());
        return Pair.of(true, hsaMemberInfoCard);
    }

    @Override
    public MemberBasicInfoVO getMemberByOpenid(MemberQueryDTO queryDTO) {
        // 查询微信信息
        HsaMemberInfoWeChat hsaMemberInfoWeChat = hsaMemberInfoWeChatMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoWeChat>()
                .eq(HsaMemberInfoWeChat::getOpenId, queryDTO.getOpenid())
                .eq(HsaMemberInfoWeChat::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .orderByDesc(HsaMemberInfoWeChat::getGmtCreate).last("limit 1"));
        if (hsaMemberInfoWeChat == null) {
            return null;
        }
        HsaOperationMemberInfo memberInfo = hsaOperationMemberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .eq(HsaOperationMemberInfo::getGuid, hsaMemberInfoWeChat.getOperationMemberInfoGuid()));

        MemberBasicInfoVO memberInfoBasicVO = new MemberBasicInfoVO();
        BeanUtil.copyProperties(memberInfo, memberInfoBasicVO);
        memberInfoBasicVO.setIntegral(memberInfo.getMemberIntegral());
        memberInfoBasicVO.setOpenid(Optional.ofNullable(hsaMemberInfoWeChat).map(HsaMemberInfoWeChat::getOpenId).orElse(null));
        memberInfoBasicVO.setGmtCreate(memberInfo.getGmtCreate().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME)));
        log.info("查询会员返回信息:{}", JSON.toJSONString(memberInfoBasicVO));
        return memberInfoBasicVO;
    }

    @Override
    public MemberAccountInfoVO getMemberAccountInfo(MemberQueryDTO queryDTO) {
        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        // 查询会员信息
        HsaOperationMemberInfo memberInfo = hsaOperationMemberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .eq(HsaOperationMemberInfo::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaOperationMemberInfo::getIsDelete, BooleanEnum.FALSE.getCode())
                .eq(StrUtil.isNotEmpty(queryDTO.getPhoneNum()), HsaOperationMemberInfo::getPhoneNum, queryDTO.getPhoneNum())
                .eq(StrUtil.isNotEmpty(queryDTO.getMemberGuid()), HsaOperationMemberInfo::getGuid, queryDTO.getMemberGuid()));
        if (memberInfo == null) {
            return new MemberAccountInfoVO();
        }

        MemberAccountInfoVO memberAccountInfoVO = new MemberAccountInfoVO();
        BeanUtil.copyProperties(memberInfo, memberAccountInfoVO);

        //会员等级 如果付费会员等级不为空，这里是给的付费会员的信息
        memberAccountInfoVO.setMemberGradeInfoName(StrUtil.isNotEmpty(memberInfo.getMemberPaidGradeInfoName())
                ? memberInfo.getMemberPaidGradeInfoName() : memberInfo.getMemberGradeInfoName());
        // 积分
        memberAccountInfoVO.setIntegral(memberInfo.getMemberIntegral());
        // 成长值
        memberAccountInfoVO.setMemberGrowthValue(memberInfo.getMemberGrowthValue());
        List<CardInfoBasicVO> memberCards = hsaMemberInfoCardMapper.listMemberCard(memberInfo.getGuid());
        //会员卡统计
        memberAccountInfoVO.setMemberCardCount(Optional.ofNullable(memberCards)
                .map(List::size)
                .orElse(0));
        //设置余额 todo:这里余额只取了第一张卡
        memberAccountInfoVO.setBalance(Optional.ofNullable(memberCards)
                .filter(cards -> !cards.isEmpty())
                .map(cards -> cards.get(0))
                .map(CardInfoBasicVO::getCardBalance)
                .map(cardBalance -> cardBalance.multiply(new BigDecimal(NumberConstant.NUMBER_10000))
                        .setScale(0, RoundingMode.DOWN)
                        .longValueExact())
                .orElse(0L));
        //有效的优惠券统计
        memberAccountInfoVO.setUnwrittenCouponCount(memberCouponLinkMapper.countUnwrittenNum(new MemberCouponQO().setOperSubjectGuid(operSubjectGuid).setMemberGuid(queryDTO.getMemberGuid())));

        return memberAccountInfoVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MemberBasicInfoVO addMemberInfo(MemberAddDTO addDTO) {
        String enterpriseGuid = ThreadLocalCache.getEnterpriseGuid();
        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        Integer sourceType = ThreadLocalCache.getSource();

        // 检查会员是否存在
        MemberBasicInfoVO existingMember = getExistingMember(addDTO);
        if (existingMember != null) {
            return existingMember;
        }

        // 创建新会员
        String memberGuid = createNewMember(addDTO, sourceType, operSubjectGuid, enterpriseGuid);

        // 更新微信信息
        updateWechatInfoIfNeeded(addDTO, memberGuid, operSubjectGuid);

        return getMemberInfo(new MemberQueryDTO().setMemberGuid(memberGuid));
    }


    private MemberBasicInfoVO getExistingMember(MemberAddDTO addDTO) {
        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        MemberBasicInfoVO member = null;
        // 根据手机号查询
        if (!StringUtils.isEmpty(addDTO.getPhoneNum())) {
            member = getMemberInfo(new MemberQueryDTO().setPhoneNum(addDTO.getPhoneNum()));
            if (Objects.nonNull(member) && member.getGuid() != null) {
                updateToLastOpenId(addDTO, member, operSubjectGuid);
                member.setOpenid(addDTO.getOpenid());
                return member;
            }
        }
        // 根据openId查询
        if (!StringUtils.isEmpty(addDTO.getOpenid())) {
            member = getMemberInfo(new MemberQueryDTO().setOpenid(addDTO.getOpenid()));
            if (Objects.nonNull(member) && member.getGuid() != null) {
                updateToLastPhone(addDTO, member);
                member.setPhoneNum(addDTO.getPhoneNum());
                member.setOpenid(addDTO.getOpenid());
                return member;
            }
        }
        return member;
    }

    private String createNewMember(MemberAddDTO addDTO, Integer sourceType,
                                   String operSubjectGuid, String enterpriseGuid) {
        SaveOperationMemberInfoQO memberInfo = new SaveOperationMemberInfoQO()
                .setSourceType(sourceType)
                .setUserName(addDTO.getUserName())
                .setSex(addDTO.getSex())
                .setBirthday(addDTO.getBirthday())
                .setProvinceCode(addDTO.getProvinceCode())
                .setProvinceName(addDTO.getProvinceName())
                .setCityCode(addDTO.getCityCode())
                .setCityName(addDTO.getCityName())
                .setAreaCode(addDTO.getAreaCode())
                .setAreaName(addDTO.getAreaName())
                .setContactAddress(addDTO.getContactAddress())
                .setPhoneNum(addDTO.getPhoneNum())
                .setPhoneCountryCode(StringConstant.STR_DEFAULT_PHONE_COUNTRY_CODE)
                .setOperSubjectGuid(operSubjectGuid)
                .setEnterpriseGuid(enterpriseGuid)
                .setUserName(determineUserName(sourceType, addDTO.getUserName(), addDTO.getPhoneNum()));
        if (Boolean.TRUE.equals(addDTO.getCardOpenFlag())) {
            memberInfo.setCardGuidList(addDTO.getCardOpenList());
        }
        return saveOperationMemberInfo(memberInfo);
    }

    private String determineUserName(Integer sourceType, String userName, String phoneNum) {
        if (!StringUtils.isEmpty(userName)) {
            // 如果userName有值，则取传入的
            return userName;
        }
        return SourceTypeEnum.getAllWechatType().contains(sourceType)
                       ? StringConstant.WECHAT_DEFAULT_USERNAME
                       : phoneNum;
    }


    private void updateWechatInfoIfNeeded(MemberAddDTO addDTO, String memberGuid, String operSubjectGuid) {
        if (StrUtil.isNotEmpty(addDTO.getOpenid())) {
            updateWechatInfo(addDTO.getOpenid(), memberGuid, operSubjectGuid);
        }
    }

    private void updateToLastOpenId(MemberAddDTO addDTO, MemberBasicInfoVO memberBasicInfoVO, String operSubjectGuid) {
        if (StrUtil.isNotEmpty(addDTO.getOpenid())) {
            // 更新为最新的openid
            updateWechatInfo(addDTO.getOpenid(), memberBasicInfoVO.getGuid(), operSubjectGuid);
        }
    }

    private void updateToLastPhone(MemberAddDTO addDTO, MemberBasicInfoVO memberBasicInfoVO) {
        OperationMemberPhoneQO operationMemberPhoneQO = new OperationMemberPhoneQO();
        operationMemberPhoneQO.setGuid(memberBasicInfoVO.getGuid());
        operationMemberPhoneQO.setPhone(addDTO.getPhoneNum());
        operationMemberPhoneQO.setPhoneCountryCode(StringConstant.STR_DEFAULT_PHONE_COUNTRY_CODE);
        updateOperationMemberPhone(operationMemberPhoneQO);
    }

    @Override
    public void updateMemberInfo(MemberUpdateDTO updateDTO) {
        // 查询会员信息
        HsaOperationMemberInfo memberInfo = hsaOperationMemberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .eq(HsaOperationMemberInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaOperationMemberInfo::getIsDelete, BooleanEnum.FALSE.getCode())
                .eq(HsaOperationMemberInfo::getGuid, updateDTO.getMemberGuid()));

        if (memberInfo == null) {
            return;
        }

        updateMemberForMemberUpdateDTO(updateDTO, memberInfo);

        // 更新会员信息
        hsaOperationMemberInfoMapper.updateByGuid(memberInfo);

        // 处理积分任务
        handleIntegralTasks(ThreadLocalCache.getHeaderUserInfo(), memberInfo);

        // 刷新会员标签
        refreshMemberLabels(memberInfo);
    }

    private void updateMemberForMemberUpdateDTO(MemberUpdateDTO updateDTO, HsaOperationMemberInfo memberInfo) {
        memberInfo.setUserName(updateDTO.getUserName());
        memberInfo.setHeadImgUrl(updateDTO.getHeadImgUrl());
        memberInfo.setSex(updateDTO.getSex());
        memberInfo.setBirthday(updateDTO.getBirthday());
        Optional.ofNullable(updateDTO.getProvinceCode()).ifPresent(memberInfo::setProvinceCode);
        Optional.ofNullable(updateDTO.getProvinceName()).ifPresent(memberInfo::setProvinceName);
        Optional.ofNullable(updateDTO.getCityCode()).ifPresent(memberInfo::setCityCode);
        Optional.ofNullable(updateDTO.getCityName()).ifPresent(memberInfo::setCityName);
        Optional.ofNullable(updateDTO.getAreaCode()).ifPresent(memberInfo::setAreaCode);
        Optional.ofNullable(updateDTO.getAreaName()).ifPresent(memberInfo::setAreaName);
        Optional.ofNullable(updateDTO.getContactAddress()).ifPresent(memberInfo::setContactAddress);

        memberInfo.setDataItemJson(updateDTO.getDataItemJson());
        List<DataItemSetVO> dataItemDTOS = JSON.parseArray(updateDTO.getDataItemJson(), DataItemSetVO.class);
        log.info("会员数据项：{}", JSON.toJSONString(dataItemDTOS));
        if (CollUtil.isEmpty(dataItemDTOS)) {
            return;
        }
        for (DataItemSetVO dataItemSetVO : dataItemDTOS) {
            if (dataItemSetVO.getInfoName().equals(DataItemEnum.DEPARTMENT.getInfoName())) {
                memberInfo.setDepartmentName(dataItemSetVO.getValue());
            }
            if (dataItemSetVO.getInfoName().equals(DataItemEnum.NUMBER.getInfoName())) {
                memberInfo.setMemberNum(dataItemSetVO.getValue());
            }
        }
    }

    @Override
    public void updatePaidGrade(String memberInfoGuid, String memberPaidGradeGuid, String memberPaidGradName) {
        HsaOperationMemberInfo memberInfo = hsaOperationMemberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .eq(HsaOperationMemberInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaOperationMemberInfo::getGuid, memberInfoGuid));
        if (memberInfo != null && !memberPaidGradeGuid.equals(memberInfo.getMemberGradeInfoGuid())) {
            memberInfo.setMemberGradeInfoGuid(memberPaidGradeGuid);
            memberInfo.setMemberGradeInfoName(memberPaidGradName);
            hsaOperationMemberInfoMapper.updateById(memberInfo);
            if (log.isInfoEnabled()) {
                log.info(">>>>> 更新付费会员等级信息，memberInfoGuid{}", memberInfoGuid);
            }
        }
    }

    private void updateWechatInfo(String openid, String memberGuid, String operSubjectGuid) {
        // 查询会员微信信息
        HsaMemberInfoWeChat wechatInfo = hsaMemberInfoWeChatMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoWeChat>()
                .eq(HsaMemberInfoWeChat::getOperationMemberInfoGuid, memberGuid)
                .eq(HsaMemberInfoWeChat::getOperSubjectGuid, operSubjectGuid));
        if (wechatInfo != null) {
            wechatInfo.setOpenId(openid).setGmtModified(LocalDateTime.now());
            hsaMemberInfoWeChatMapper.updateById(wechatInfo);
        } else {
            wechatInfo = new HsaMemberInfoWeChat()
                    .setGuid(guidGeneratorUtil.getStringGuid(HsaMemberInfoWeChat.class.getSimpleName()))
                    .setOpenId(openid)
                    .setOperationMemberInfoGuid(memberGuid)
                    .setOperSubjectGuid(operSubjectGuid);
            hsaMemberInfoWeChatMapper.insert(wechatInfo);
        }
    }

    private HsaOperationMemberInfo getMemberInfoAccount(String memberAccount) {
        LambdaQueryWrapper<HsaOperationMemberInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(HsaOperationMemberInfo::getMemberAccount, memberAccount);
        return this.getOne(lambdaQueryWrapper);
    }

    private String getOperationName() {
        HeaderUserInfo userInfo = ThreadLocalCache.getHeaderUserInfo();
        if (userInfo != null) {
            return userInfo.getUserName() + "/" + userInfo.getAccount();
        }
        return "";
    }

    /**
     * 将会员修改的证件信息保存到数据库
     *
     * @param certificateInfoList 证件信息集合
     * @param guid                会员guid
     * @param operSubjectGuid     会员主体guid
     */
    private String saveCertificateByGuid(List<CertificateInfo> certificateInfoList, String guid, String operSubjectGuid) {

        List<HsaMemberCertificateInfo> hsaMemberCertificateInfos = new ArrayList<>();
        //验证证件号
        if (CollUtil.isNotEmpty(certificateInfoList)) {
            List<CertificateInfo> CertificateList = certificateInfoList.stream().filter(x -> null != x && !StringUtils.isEmpty(x.getCertificateNum())).collect(Collectors.toList());
            for (CertificateInfo certificateInfo : CertificateList) {
                dealforCertificateList(guid, operSubjectGuid, hsaMemberCertificateInfos, certificateInfo);
            }
            //日志记录
            List<HsaMemberCertificateInfo> list = hsaMemberCertificateInfoService.list(new LambdaQueryWrapper<HsaMemberCertificateInfo>().eq(HsaMemberCertificateInfo::getOperationMemberInfoGuid, guid));
            String logCertificate = buildLogCertificateContent(list, hsaMemberCertificateInfos);
            //将修改的会员证件信息保存数据库
            hsaMemberCertificateInfoService.remove(new LambdaQueryWrapper<HsaMemberCertificateInfo>().eq(HsaMemberCertificateInfo::getOperationMemberInfoGuid, guid));
            hsaMemberCertificateInfoService.saveBatch(hsaMemberCertificateInfos);
            return logCertificate;
        }
        return null;
    }

    private void dealforCertificateList(String guid, String operSubjectGuid, List<HsaMemberCertificateInfo> hsaMemberCertificateInfos, CertificateInfo certificateInfo) {
        HsaMemberCertificateInfo hsaMemberCertificateInfo = hsaMemberCertificateInfoService.getOne(
                new LambdaQueryWrapper<HsaMemberCertificateInfo>()
                        .eq(HsaMemberCertificateInfo::getCertificateNum, certificateInfo.getCertificateNum())
                        .eq(HsaMemberCertificateInfo::getCertificateType, certificateInfo.getCertificateType())
                        .eq(HsaMemberCertificateInfo::getOperSubjectGuid, operSubjectGuid)
                        .ne(HsaMemberCertificateInfo::getOperationMemberInfoGuid, guid)
        );
        if (Objects.nonNull(hsaMemberCertificateInfo) && !StringUtils.isEmpty(hsaMemberCertificateInfo.getCertificateNum())) {
            throw new MemberBaseException(MemberAccountExceptionEnum.THIS_ID_ALREADY_EXISTS.getDes());
        }
        HsaMemberCertificateInfo memberCertificateInfo = new HsaMemberCertificateInfo();
        //自定义证件名称不能为空
        if (CertificateTypeEnum.CERTIFICATE_OTHER.getCode() == certificateInfo.getCertificateType() && Objects.isNull(certificateInfo.getCertificateName())) {
            throw new MemberBaseException(MemberAccountExceptionEnum.CERTIFICATE_NOT_NAME.getDes());
        }

        addMemberCertificateInfo(guid, operSubjectGuid, hsaMemberCertificateInfos, certificateInfo, memberCertificateInfo);
    }

    private void addMemberCertificateInfo(String guid, String operSubjectGuid, List<HsaMemberCertificateInfo> hsaMemberCertificateInfos, CertificateInfo certificateInfo, HsaMemberCertificateInfo memberCertificateInfo) {
        if (CertificateTypeEnum.CERTIFICATE_OTHER.getCode() == certificateInfo.getCertificateType()) {
            memberCertificateInfo.setCertificateName(certificateInfo.getCertificateName());
        } else {
            Integer certificateType = certificateInfo.getCertificateType();
            memberCertificateInfo.setCertificateName(CertificateTypeEnum.getMsgByCode(certificateType));
        }
        memberCertificateInfo.setCertificateNum(certificateInfo.getCertificateNum());
        memberCertificateInfo.setCertificateType(certificateInfo.getCertificateType());
        memberCertificateInfo.setOperationMemberInfoGuid(guid);
        memberCertificateInfo.setOperSubjectGuid(operSubjectGuid);
        memberCertificateInfo.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberCertificateInfo.class.getSimpleName()));
        hsaMemberCertificateInfos.add(memberCertificateInfo);
    }

    private String buildLogCertificateContent(List<HsaMemberCertificateInfo> originalList, List<HsaMemberCertificateInfo> changeList) {
        StringBuilder builder = new StringBuilder();
        Map<Integer, HsaMemberCertificateInfo> collectChange = changeList.stream().collect(Collectors.toMap(HsaMemberCertificateInfo::getCertificateType, obj -> obj));
        Map<Integer, HsaMemberCertificateInfo> collectOriginal = originalList.stream().collect(Collectors.toMap(HsaMemberCertificateInfo::getCertificateType, obj -> obj));
        for (HsaMemberCertificateInfo original : originalList) {
            HsaMemberCertificateInfo info = collectChange.get(original.getCertificateType());
            //删除的字段
            if (ObjectUtil.isNull(info)) {
                builder.append(String.format(LogContentConstant.DELETE_CONTENT, original.getCertificateName(), original.getCertificateNum()));
            }
            //修改的字段
            if (ObjectUtil.isNotNull(info)) {
                String originalNum = original.getCertificateNum();
                String changeNum = info.getCertificateNum();
                if (StringUtils.isEmpty(originalNum) && StringUtils.isEmpty(changeNum) || originalNum.equals(changeNum)) {
                    continue;
                }
                builder.append(String.format(LogContentConstant.UPDATE_CONTENT, original.getCertificateName(), originalNum, changeNum));
            }
        }
        //新增的字段
        for (HsaMemberCertificateInfo change : changeList) {
            HsaMemberCertificateInfo info = collectOriginal.get(change.getCertificateType());
            if (ObjectUtil.isNull(info)) {
                builder.append(String.format(LogContentConstant.ADD_CONTENT, change.getCertificateName(), change.getCertificateNum()));
            }
        }
        return builder.toString();
    }

    /**
     * 通过手机号码验证运营主体或联盟是否存在会员
     */
    private void validateOperSubjectByPhone(List<HsaMemberCertificateInfo> hsaMemberCertificateInfos, SaveOperationMemberInfoQO request) {

        setPhoneCountryCode(request);

        checkMemberPhone(request);

        //验证证件号
        if (CollUtil.isNotEmpty(request.getCertificateInfoList())) {
            for (CertificateInfo certificateInfo : request.getCertificateInfoList()) {
                addMemberCertificateInfos(hsaMemberCertificateInfos, certificateInfo);
            }
        }

        //验证敏感字词
        if (!StringUtils.isEmpty(request.getNickName())
                || !StringUtils.isEmpty(request.getUserName())
                || !StringUtils.isEmpty(request.getDepartmentName())) {
            String stringBuilder = request.getNickName() +
                    request.getUserName() +
                    request.getDepartmentName();
            hsaApplyDictionariesService.checkKeyWords(stringBuilder);
        }
    }

    private void addMemberCertificateInfos(List<HsaMemberCertificateInfo> hsaMemberCertificateInfos, CertificateInfo certificateInfo) {
        HsaMemberCertificateInfo hsaMemberCertificateInfo;
        if (!StringUtils.isEmpty(certificateInfo.getCertificateNum())) {
            if (certificateInfo.getCertificateType() == CertificateTypeEnum.CERTIFICATE_OTHER.getCode()) {
                hsaMemberCertificateInfo = hsaMemberCertificateInfoMapper.selectOne(new LambdaQueryWrapper<HsaMemberCertificateInfo>().eq(HsaMemberCertificateInfo::getCertificateNum, certificateInfo.getCertificateNum()).eq(HsaMemberCertificateInfo::getCertificateType, CertificateTypeEnum.CERTIFICATE_OTHER.getCode()).eq(HsaMemberCertificateInfo::getCertificateName, certificateInfo.getCertificateName()).eq(HsaMemberCertificateInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
            } else {
                hsaMemberCertificateInfo = hsaMemberCertificateInfoMapper.selectOne(new LambdaQueryWrapper<HsaMemberCertificateInfo>().eq(HsaMemberCertificateInfo::getCertificateNum, certificateInfo.getCertificateNum()).eq(HsaMemberCertificateInfo::getCertificateType, certificateInfo.getCertificateType()).eq(HsaMemberCertificateInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
            }
            if (Objects.nonNull(hsaMemberCertificateInfo)) {
                throw new MemberBaseException(MemberAccountExceptionEnum.THIS_ID_ALREADY_EXISTS);
            }
            HsaMemberCertificateInfo memberCertificateInfo = new HsaMemberCertificateInfo();
            if (CertificateTypeEnum.CERTIFICATE_OTHER.getCode() == certificateInfo.getCertificateType()) {
                if (Objects.isNull(certificateInfo.getCertificateName())) {
                    throw new MemberBaseException(MemberAccountExceptionEnum.CERTIFICATE_NOT_NAME);
                }
                memberCertificateInfo.setCertificateName(certificateInfo.getCertificateName());
            }
            memberCertificateInfo.setCertificateNum(certificateInfo.getCertificateNum());
            memberCertificateInfo.setCertificateType(certificateInfo.getCertificateType());
            memberCertificateInfo.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberCertificateInfo.class.getSimpleName()));
            hsaMemberCertificateInfos.add(memberCertificateInfo);
        }
    }

    private void checkMemberPhone(SaveOperationMemberInfoQO request) {
        if (!NumberUtil.isPhoneNum11(request.getPhoneNum())) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_PHONE);
        }
        // 验证会员是否已存在
        int memberCount = this.count(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .eq(HsaOperationMemberInfo::getPhoneNum, request.getPhoneNum())
                .eq(HsaOperationMemberInfo::getPhoneCountryCode, request.getPhoneCountryCode())
                .eq(HsaOperationMemberInfo::getOperSubjectGuid, request.getOperSubjectGuid()));
        if (memberCount > 0) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_EXIST_OPERSUBJECT);
        }
    }

    private static void setPhoneCountryCode(SaveOperationMemberInfoQO request) {
        request.setPhoneCountryCode(StringUtils.isEmpty(request.getPhoneCountryCode()) ? StringConstant.STR_DEFAULT_PHONE_COUNTRY_CODE : request.getPhoneCountryCode());
    }

    @Override
    public OperationMemberDetailVO getOperationMemberDetail(String guid) {
        HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.selectOne(
                new LambdaQueryWrapper<HsaOperationMemberInfo>()
                        .eq(HsaOperationMemberInfo::getGuid, guid)
                        .eq(HsaOperationMemberInfo::getIsDelete, BooleanEnum.FALSE.getCode()));
        if (ObjectUtils.isEmpty(hsaOperationMemberInfo)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_USER_NOT_REGISTERED);
        }
        OperationMemberDetailVO operationMemberDetailVO = MemberInfoTransform.INSTANCE.cardInfo2DTO(hsaOperationMemberInfo);
        operationMemberDetailVO.setSourceName(externalSupport.baseServer(ThreadLocalCache.getSystem()).getSourceTypeEnum(operationMemberDetailVO.getSourceType()));
        operationMemberDetailVO.setIndustryCodes(StringBaseHandlerUtil.strConvertList(hsaOperationMemberInfo.getIndustryCodes()));

        //设置会员等级信息
        setMemberGradeInfo(operationMemberDetailVO);

        //设置会员微信授权信息
        setMemberWechatState(operationMemberDetailVO);

        //查询会员证件信息集合
        List<HsaMemberCertificateInfo> certificateInfoList = hsaMemberCertificateInfoService.list(
                new LambdaQueryWrapper<HsaMemberCertificateInfo>()
                        .eq(HsaMemberCertificateInfo::getOperationMemberInfoGuid, guid));

        List<HsaMemberCertificateInfoDTO> hsaMemberCertificateInfoDTOS =
                MemberInfoTransform.INSTANCE.CertificateInfoToDTO(certificateInfoList);
        operationMemberDetailVO.setCertificateInfoDTOList(hsaMemberCertificateInfoDTOS);

        String area = formatArea(  //所属区域格式化
                operationMemberDetailVO.getProvinceName(), //省名称
                operationMemberDetailVO.getCityName(), //市名称
                operationMemberDetailVO.getAreaName());//区名称
        operationMemberDetailVO.setArea(area);//所属地区=省名称/市名称/区名称
        // 查询会员亲属信息列表
        List<HsaKinsfolkManagementVO> kinsfolkManagementVOList = hsaKinsfolkManagementService.getListByMemberGuid(guid);
        operationMemberDetailVO.setKinsfolkManagementVOList(kinsfolkManagementVOList);
        operationMemberDetailVO.setFaceToken(hsaOperationMemberInfo.getFaceToken());
        operationMemberDetailVO.setMemberNum(hsaOperationMemberInfo.getMemberNum());
        operationMemberDetailVO.setVideoJson(hsaOperationMemberInfo.getVideoJson());
        // 查询会员名片信息
        structureBusinessCardInfo(operationMemberDetailVO);

        // 会员标签
        List<MemberLabelVO> memberLabelList = labelService.listMemberLabelByMemberInfoGuid(guid);
        //所关联的所有标签信息
        operationMemberDetailVO.setMemberLabelList(memberLabelList);
        return operationMemberDetailVO;
    }

    /**
     * 构建个人经历以及教育经历信息、自定义信息
     */
    private void structureBusinessCardInfo(OperationMemberDetailVO operationMemberDetailVO) {
        CompletableFuture<List<EducationalDTO>> educationFuture = CompletableFuture.supplyAsync(() ->
                educationService.listByMemberGuid(operationMemberDetailVO.getGuid(), operationMemberDetailVO.getOperSubjectGuid()), memberBaseThreadExecutor);
        CompletableFuture<List<PersonalDTO>> personalFuture = CompletableFuture.supplyAsync(() ->
                personalService.listByMemberGuid(operationMemberDetailVO.getGuid(), operationMemberDetailVO.getOperSubjectGuid()), memberBaseThreadExecutor);
        CompletableFuture<List<CustomizeFieldDTO>> customizeFuture = CompletableFuture.supplyAsync(() ->
                customizeFieldService.listByMemberGuid(operationMemberDetailVO.getGuid(), operationMemberDetailVO.getOperSubjectGuid()), memberBaseThreadExecutor);
        CompletableFuture<Void> all = CompletableFuture.allOf(educationFuture, personalFuture, customizeFuture);
        try {
            all.get();
            List<EducationalDTO> educationalDTOList = educationFuture.get();
            educationalDTOList.forEach(e -> e.setSchoolTime(Lists.newArrayList(e.getAdmissionTime(), e.getGraduationTime())));
            operationMemberDetailVO.setEducationalExperience(educationalDTOList);
            operationMemberDetailVO.setPersonalExperience(personalFuture.get());
            operationMemberDetailVO.setCustomize(customizeFuture.get());
        } catch (InterruptedException | ExecutionException e) {
            log.info("并行获取会员名片信息异常", e);
            throw new MallBaseException(ExceptionEnum.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 设置会员等级信息
     *
     * @param operationMemberDetailVO 会员信息
     */
    public void setMemberGradeInfo(OperationMemberDetailVO operationMemberDetailVO) {
        String memberGradeInfoGuid = StringUtils.isEmpty(operationMemberDetailVO.getMemberPaidGradeInfoGuid()) ? operationMemberDetailVO.getMemberGradeInfoGuid() : operationMemberDetailVO.getMemberPaidGradeInfoGuid();
        HsaMemberGradeInfo hsaMemberGradeInfo = hsaMemberGradeInfoMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradeInfo>().eq(HsaMemberGradeInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()).eq(HsaMemberGradeInfo::getGuid, memberGradeInfoGuid).eq(HsaMemberGradeInfo::getEffective, BooleanEnum.TRUE.getCode()).in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE, NUMBER_2));
        if (Objects.isNull(hsaMemberGradeInfo)) {
            return;
        }
        operationMemberDetailVO.setMemberGradeInfoName(hsaMemberGradeInfo.getName());
        operationMemberDetailVO.setVipGrade(hsaMemberGradeInfo.getVipGrade());
        operationMemberDetailVO.setGradeIcon(hsaMemberGradeInfo.getGradeIcon());
    }

    /**
     * 设置会员等级信息
     *
     * @param operationMemberDetailVO 会员信息
     */
    public void setMemberWechatState(OperationMemberDetailVO operationMemberDetailVO) {
        // 查询微信信息
        HsaMemberInfoWeChat hsaMemberInfoWeChat = hsaMemberInfoWeChatMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoWeChat>()
                .eq(HsaMemberInfoWeChat::getOperationMemberInfoGuid, operationMemberDetailVO.getGuid())
                .eq(HsaMemberInfoWeChat::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .orderByDesc(HsaMemberInfoWeChat::getGmtCreate).last("limit 1"));
        operationMemberDetailVO.setWechatState(Objects.nonNull(hsaMemberInfoWeChat) ? BooleanEnum.TRUE.getCode() : BooleanEnum.FALSE.getCode());
    }

    /**
     * 所属区域格式化
     *
     * @param provinceName 省名称
     * @param cityName     市名称
     * @param areaName     区名称
     * @return 操作结果
     */
    public String formatArea(String provinceName, String cityName, String areaName) {
        StringBuilder area = new StringBuilder();
        if (!StringUtils.isEmpty(provinceName)) {
            area.append(provinceName).append("/");
        }
        if (!StringUtils.isEmpty(cityName)) {
            area.append(cityName).append("/");
        }
        if (!StringUtils.isEmpty(areaName)) {
            area.append(areaName);
        }
        return area.toString();
    }

    @Override
    public boolean updateOperationMemberPassword(OperationMemberPasswordQO request) {
        //验证会员是否存在
        List<HsaOperationMemberInfo> hsaOperationMemberInfoList = validateMemberByGuids(request.getGuids());
        for (HsaOperationMemberInfo hsaOperationMemberInfo : hsaOperationMemberInfoList) {
            hsaOperationMemberInfo.setPayPassword(SecureUtil.md5(request.getPassword()).toUpperCase());
            updateByGuid(hsaOperationMemberInfo);
        }
        //保存日志信息
        OperationLogDTO logDTO = OperationLogDTO.builder().contentGuidList(hsaOperationMemberInfoList.stream().map(HsaOperationMemberInfo::getGuid).collect(Collectors.toList())).headerUserInfo(ThreadLocalCache.getHeaderUserInfo()).logType(OperationLogTypeEnum.MEMBER_ACCOUNT).extraContent(LogContentConstant.UPDATE_ACCOUNT_PASSWORD).build();
        operationLogService.dealBusinessLog(logDTO);
        return true;
    }

    @Override
    public boolean updateMemberAccountPassword(OperationMemberPasswordQO request) {
        //验证会员是否存在
        List<HsaOperationMemberInfo> hsaOperationMemberInfoList = validateMemberByGuids(request.getGuids());
        for (HsaOperationMemberInfo hsaOperationMemberInfo : hsaOperationMemberInfoList) {
            hsaOperationMemberInfo.setPassword(SecureUtil.md5(String.valueOf(request.getPassword())).toUpperCase());

            updateByGuid(hsaOperationMemberInfo);
        }
        return true;
    }

    @Override
    public boolean updateOperationMemberState(OperationMemberStateQO request) {
        //验证会员是否存在
        List<HsaOperationMemberInfo> hsaOperationMemberInfoList = validateMemberByGuids(request.getGuids());
        Boolean accountState = request.getAccountState();
        int state;
        if (Boolean.TRUE.equals(accountState)) {
            state = (BooleanEnum.FALSE.getCode());
        } else {
            state = (BooleanEnum.TRUE.getCode());
        }
        for (HsaOperationMemberInfo hsaOperationMemberInfo : hsaOperationMemberInfoList) {
            Integer originState = hsaOperationMemberInfo.getAccountState();
            hsaOperationMemberInfo.setAccountState(state);
            updateByGuid(hsaOperationMemberInfo);
            //更新缓存中的状态
            MemberUtil.updateMemberAccountState(cacheService, hsaOperationMemberInfo.getGuid(), state);
            //保存日志信息
            OperationLogDTO logDTO = OperationLogDTO.builder().contentGuidList(Collections.singletonList(hsaOperationMemberInfo.getGuid())).headerUserInfo(ThreadLocalCache.getHeaderUserInfo()).logType(OperationLogTypeEnum.MEMBER_ACCOUNT).field(LogFieldConstant.MEMBER_STATUS).originData(AccountStateEnum.getNameByCode(originState)).changeData(AccountStateEnum.getNameByCode(state)).build();
            operationLogService.dealBusinessLog(logDTO);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOperationMemberStore(OperationMemberStoreQO request) {
        if (CollectionUtil.isEmpty(request.getGuids())) {
            return false;
        }
        //验证会员是否存在
        List<HsaOperationMemberInfo> hsaOperationMemberInfoList = validateMemberByGuids(request.getGuids());
        List<OperationMemberStoreRecordQO> memberStoreRecordQOList = new ArrayList<>();
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        for (HsaOperationMemberInfo hsaOperationMemberInfo : hsaOperationMemberInfoList) {
            //添加修改会员门店的记录信息
            OperationMemberStoreRecordQO operationMemberStoreRecordQO = new OperationMemberStoreRecordQO();
            operationMemberStoreRecordQO.setGuid(hsaOperationMemberInfo.getGuid());
            operationMemberStoreRecordQO.setBeforeModification(hsaOperationMemberInfo.getStoreName());
            operationMemberStoreRecordQO.setAfterModification(request.getAfterModification());
            operationMemberStoreRecordQO.setReason(   // 商家变更-操作人员名称/操作人员账户
                    MemberStoreEnum.BUSINESS_CHANGE.getDes() + "-" + //商家变更
                            headerUserInfo.getUserName() + "/" +  //操作人员名称
                            headerUserInfo.getUserGuid());    //操作人员账户
            memberStoreRecordQOList.add(operationMemberStoreRecordQO);

            //修改会员门店信息
            hsaOperationMemberInfo.setStoreGuid(request.getStoreGuid());
            hsaOperationMemberInfo.setStoreName(request.getAfterModification());
            updateByGuid(hsaOperationMemberInfo);
        }

        return hsaMemberStoreModificationRecordService.saveBatch(memberStoreRecordQOList, request.getGuids()); //保持会员变更门店记录信息
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int relieveOperationMemberStore(String guid) {
        //验证会员是否存在
        HsaOperationMemberInfo hsaOperationMemberInfo = validateMemberByGuid(guid);
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        //添加修改会员门店的记录信息
        List<OperationMemberStoreRecordQO> memberStoreRecordQOList = new ArrayList<>();
        OperationMemberStoreRecordQO operationMemberStoreRecordQO = new OperationMemberStoreRecordQO();
        operationMemberStoreRecordQO.setGuid(hsaOperationMemberInfo.getGuid());
        operationMemberStoreRecordQO.setBeforeModification(hsaOperationMemberInfo.getStoreName());
        operationMemberStoreRecordQO.setAfterModification(MemberStoreEnum.RELIEVE_STORE.getDes());
        operationMemberStoreRecordQO.setReason(   // 商家解绑-操作人员名称/操作人员账户
                MemberStoreEnum.MERCHANTS_UNBUNDLING.getDes() + "-" + //商家解绑
                        headerUserInfo.getUserName() + "/" +  //操作人员名称
                        headerUserInfo.getUserGuid());    //操作人员账户
        memberStoreRecordQOList.add(operationMemberStoreRecordQO);
        hsaMemberStoreModificationRecordService.saveBatch(memberStoreRecordQOList, Lists.newArrayList(guid)); //保持会员变更门店记录信息

        hsaOperationMemberInfo.setStoreName(MemberStoreEnum.RELIEVE_STORE.getDes());
        hsaOperationMemberInfo.setStoreGuid(null);
        return hsaOperationMemberInfoMapper.update(hsaOperationMemberInfo, new LambdaQueryWrapper<HsaOperationMemberInfo>().eq(HsaOperationMemberInfo::getGuid, guid));
    }

    @Override
    public int updateOperationMemberPhone(OperationMemberPhoneQO request) {
        //验证会员是否存在
        HsaOperationMemberInfo hsaOperationMemberInfo = validateMemberByGuid(request.getGuid());
        //验证修改的手机号码是否存在
        validateMemberPhoneByGuid(request);
        String phoneNum = hsaOperationMemberInfo.getPhoneNum();
        if (phoneNum.equals(hsaOperationMemberInfo.getUserName())) {
            hsaOperationMemberInfo.setUserName(request.getPhone());
        }
        hsaOperationMemberInfo.setPhoneNum(request.getPhone());
        hsaOperationMemberInfo.setPhoneCountryCode(request.getPhoneCountryCode());
        HsaMemberInfoWeChat hsaMemberInfoWeChat = hsaMemberInfoWeChatMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoWeChat>()
                .eq(HsaMemberInfoWeChat::getOperationMemberInfoGuid, hsaOperationMemberInfo.getGuid()));
        if (Objects.nonNull(hsaMemberInfoWeChat)) {
            hsaOperationMemberInfo.setWechatState(BooleanEnum.FALSE.getCode());
            hsaOperationMemberInfo.setHeadImgUrl(null);
            hsaOperationMemberInfo.setNickName(null);
            //清除緩存
            String cacheKey = TOKEN_PREFIX + hsaMemberInfoWeChat.getOpenId() + ":" + SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode();
            redisTemplate.delete(cacheKey);
            hsaMemberInfoWeChatMapper.removeByGuid(hsaMemberInfoWeChat.getGuid());
        }
        int update = hsaOperationMemberInfoMapper.update(hsaOperationMemberInfo, new LambdaQueryWrapper<HsaOperationMemberInfo>().eq(HsaOperationMemberInfo::getGuid, request.getGuid()));
        //同步会员卡关联数据
        List<HsaMemberInfoCard> hsaMemberInfoCards = hsaMemberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>().eq(HsaMemberInfoCard::getMemberInfoGuid, hsaOperationMemberInfo.getGuid()));
        List<String> collect = hsaMemberInfoCards.stream().filter(Objects::nonNull).map(HsaMemberInfoCard::getMemberInfoGuid).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            hsaMemberInfoCardMapper.batchUpdatePhone(collect, hsaOperationMemberInfo.getPhoneNum());
        }

        hsaPhysicalCardMapper.batchUpdatePhone(hsaOperationMemberInfo.getGuid(), hsaOperationMemberInfo.getPhoneNum());
        hsaElectronicCardMapper.batchUpdatePhone(hsaOperationMemberInfo.getGuid(), hsaOperationMemberInfo.getPhoneNum());

        MemberSynOrderQO synOrderQO = new MemberSynOrderQO();
        synOrderQO.setMemberInfoGuid(hsaOperationMemberInfo.getGuid());
        synOrderQO.setMemberPhoneNum(hsaOperationMemberInfo.getPhoneNum());
        memberMarketingFeign.memberSynOrder(synOrderQO);
        if (update > 0) {
            //保存日志信息
            OperationLogDTO logDTO = OperationLogDTO.builder().contentGuidList(Collections.singletonList(hsaOperationMemberInfo.getGuid())).headerUserInfo(ThreadLocalCache.getHeaderUserInfo()).logType(OperationLogTypeEnum.MEMBER_ACCOUNT).field(LogFieldConstant.MEMBER_PHONE).originData(phoneNum).changeData(request.getPhone()).build();
            operationLogService.dealBusinessLog(logDTO);
        }
        return update;
    }

    @Override
    public PageResult listMemberInfo(MemberListQO memberListQO) {
        //设置运营主体
        memberListQO.setOperSubjectGuid(ThreadLocalCache.getHeaderUserInfo().getOperSubjectGuid());
        PageHelper.startPage(memberListQO.getCurrentPage(), memberListQO.getPageSize());
        memberListQO.setIsPartner(ThreadLocalCache.isPartner());
        List<String> businessPhoneNum = new ArrayList<>();
        if (ThreadLocalCache.isPartner() && (CollUtil.isNotEmpty(memberListQO.getMemberGradeGuids()) && RoleTypeEnum.MERCHANT.name().equals(memberListQO.getGradeRole()))) {
            businessPhoneNum = partnerPlatformFeign.getAvailablePhonesByOperSubjectGuid(memberListQO.getOperSubjectGuid());
            memberListQO.setBusinessPhoneNum(businessPhoneNum);

        }

        //前端分页少一点：不超过100条
        final List<String> memberGuids = hsaOperationMemberInfoMapper.listMemberInfoGuids(memberListQO);
        if (CollectionUtils.isEmpty(memberGuids)) {
            return PageUtil.getPageResult(new PageInfo<>(memberGuids));
        }
        memberListQO.setMemberGuids(memberGuids);
        List<MemberInfoVO> list = hsaOperationMemberInfoMapper.listMemberInfo(memberListQO);

//        List<String> memberGuids = list.stream().map(MemberInfoVO::getMemberGuid).collect(Collectors.toList());
        //所有标签
        List<MemberRelationLabelDTO> allLabels = hsaMemberLabelMapper.findMemberAllLabel(memberGuids);
        Map<String, String> allLabelMap = allLabels.stream().collect(Collectors.toMap(MemberRelationLabelDTO::getMemberGuid, MemberRelationLabelDTO::getLabelName));
        //所有会员卡
        List<MemberCardDTO> allMembers = hsaMemberInfoCardMapper.findAllMemberCard(memberGuids);
        //消费信息
        List<MemberConsumptionInfoDTO> memberConsumptions = hsaMemberConsumptionMapper.getConsumptionDetailByMemberGuid(memberGuids);
        Map<String, MemberConsumptionInfoDTO> consumptionMap = memberConsumptions.stream().collect(Collectors.toMap(MemberConsumptionInfoDTO::getMemberInfoGuid, Function.identity(), (entity1, entity2) -> entity1));
        //消费次数
        List<MemberConsumptionInfoDTO> memberConsumptionsCount = hsaMemberConsumptionMapper.getConsumptionCountByMemberGuid(memberGuids);
        Map<String, MemberConsumptionInfoDTO> consumptionCountMap = memberConsumptionsCount.stream().collect(Collectors.toMap(MemberConsumptionInfoDTO::getMemberInfoGuid, Function.identity(), (entity1, entity2) -> entity1));

        //所有充值信息
        List<MemberConsumptionInfoDTO> memberRecharges = hsaMemberConsumptionMapper.getRechargeDetailByMemberGuid(memberGuids);
        Map<String, MemberConsumptionInfoDTO> rechargeMap = memberRecharges.stream().collect(Collectors.toMap(MemberConsumptionInfoDTO::getMemberInfoGuid, Function.identity(), (entity1, entity2) -> entity1));
        Map<String, String> allMemberMap = allMembers.stream().collect(Collectors.toMap(MemberCardDTO::getMemberGuid, MemberCardDTO::getMemberCard));
        //累计积分
        Map<String, Integer> integralMap = hsaIntegralDetailMapper.sumIntegralByMemberGuids(memberGuids).stream().collect(Collectors.toMap(MemberIntegralDTO::getMemberInfoGuid, MemberIntegralDTO::getTotalIntegral, (entity1, entity2) -> entity1));
        Map<String, MemberNewGradeDTO> memberNewGradeDTOMap = new HashMap<>();

        for (MemberInfoVO memberInfoVO : list) {
            memberInfoVO.setMemberCard(allMemberMap.get(memberInfoVO.getMemberGuid()));
            memberInfoVO.setMemberLabel(allLabelMap.get(memberInfoVO.getMemberGuid()));
            MemberConsumptionInfoDTO consumptionInfo = consumptionMap.get(memberInfoVO.getMemberGuid());
            if (Objects.nonNull(consumptionInfo)) {
                memberInfoVO.setConsumptionMoney(consumptionInfo.getConsumptionAmount());
            }
            MemberConsumptionInfoDTO consumptionCount = consumptionCountMap.get(memberInfoVO.getMemberGuid());
            if (Objects.nonNull(consumptionCount)) {
                memberInfoVO.setConsumptionCount(consumptionCount.getConsumptionCount());
            }
            MemberConsumptionInfoDTO rechargeInfo = rechargeMap.get(memberInfoVO.getMemberGuid());
            if (Objects.nonNull(rechargeInfo)) {
                memberInfoVO.setRechargeMoney(rechargeInfo.getRechargeAmount());
                memberInfoVO.setRechargeCount(rechargeInfo.getRechargeCount());
            }
            if (integralMap.containsKey(memberInfoVO.getMemberGuid())) {
                memberInfoVO.setTotalMemberIntegral(integralMap.get(memberInfoVO.getMemberGuid()));
            }
            dealPartnerGradeAndRoleType(memberNewGradeDTOMap, businessPhoneNum, memberInfoVO);
            // 有付费等级 显示付费等级名称
            memberInfoVO.setMemberLevel(memberInfoVO.getMemberPaidLevel() == null ? memberInfoVO.getMemberLevel() : memberInfoVO.getMemberPaidLevel());
            memberInfoVO.setSourceTypeName(RegisterChannelEnum.getNameByCode(memberInfoVO.getSourceType()));
        }
        return PageUtil.pageResult(memberGuids, list);
    }

    private static void dealPartnerGradeAndRoleType(Map<String, MemberNewGradeDTO> memberNewGradeDTOMap, List<String> businessPhoneNum, MemberInfoVO memberInfoVO) {
        if (CollectionUtils.isNotEmpty(memberNewGradeDTOMap)
                && memberNewGradeDTOMap.containsKey(memberInfoVO.getMemberGuid())) {
            MemberNewGradeDTO newGradeDTO = memberNewGradeDTOMap.get(memberInfoVO.getMemberGuid());
            if (Objects.nonNull(newGradeDTO) && !StringUtils.isEmpty(newGradeDTO.getMemberInfoGradeName())) {
                memberInfoVO.setMemberLevel(newGradeDTO.getMemberInfoGradeName());
            }
            //商家等级
            if (CollectionUtils.isNotEmpty(businessPhoneNum)
                    && Objects.nonNull(newGradeDTO)
                    && businessPhoneNum.contains(memberInfoVO.getPhoneNum())
                    && !StringUtils.isEmpty(newGradeDTO.getBusinessGradeName())) {
                memberInfoVO.setMemberLevel(StringUtils.isEmpty(memberInfoVO.getMemberLevel()) ? newGradeDTO.getBusinessGradeName() :
                        memberInfoVO.getMemberLevel() + "、" + newGradeDTO.getBusinessGradeName());
            }
        }
    }

    @Override
    public List<MemberInfoVO> listMemberInfoByGuids(MemberListQO query) {
        query.setOperSubjectGuid(ThreadLocalCache.getHeaderUserInfo().getOperSubjectGuid());
        return hsaOperationMemberInfoMapper.listMemberInfo(query);
    }

    @Override
    public PageResult<MemberInfoVO> listMemberInfoByPhone(MemberListQO query) {
        PageHelper.startPage(query.getCurrentPage(), query.getPageSize());
        query.setOperSubjectGuid(ThreadLocalCache.getHeaderUserInfo().getOperSubjectGuid());
        List<MemberInfoVO> memberInfos = hsaOperationMemberInfoMapper.listMemberInfoOnly(query);
        return PageUtil.pageResult(memberInfos);
    }

    /**
     * 校验当前会员是否存在
     *
     * @param guid
     * @return 会员信息
     */
    private HsaOperationMemberInfo validateMemberByGuid(String guid) {
        HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>().eq(HsaOperationMemberInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()).eq(HsaOperationMemberInfo::getGuid, guid).eq(HsaOperationMemberInfo::getIsDelete, BooleanEnum.FALSE.getCode()));
        if (ObjectUtils.isEmpty(hsaOperationMemberInfo)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_EXIST_MEMBER);
        }
        return hsaOperationMemberInfo;
    }

    /**
     * 校验当前会员是否存在
     *
     * @param guids
     * @return 会员信息
     */
    private List<HsaOperationMemberInfo> validateMemberByGuids(List<String> guids) {
        List<HsaOperationMemberInfo> hsaOperationMemberInfoList = hsaOperationMemberInfoMapper.selectList(new LambdaQueryWrapper<HsaOperationMemberInfo>().in(HsaOperationMemberInfo::getGuid, guids).eq(HsaOperationMemberInfo::getIsDelete, BooleanEnum.FALSE.getCode()));
        if (!CollUtil.isNotEmpty(hsaOperationMemberInfoList)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_EXIST_MEMBER);
        }
        return hsaOperationMemberInfoList;
    }

    /**
     * 通过手机号码验证运营主体或联盟是否存在会员
     */
    private void validateMemberPhoneByGuid(OperationMemberPhoneQO request) {
        // 验证新手机号是否在运营主体中存在
        HsaOperationMemberInfo newOperationMemberInfo = hsaOperationMemberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>().eq(HsaOperationMemberInfo::getPhoneNum, request.getPhone()).eq(HsaOperationMemberInfo::getPhoneCountryCode, request.getPhoneCountryCode()).eq(HsaOperationMemberInfo::getOperSubjectGuid, ThreadLocalCache.getHeaderUserInfo().getOperSubjectGuid()).ne(HsaOperationMemberInfo::getGuid, request.getGuid()));
        if (ObjectUtil.isNotNull(newOperationMemberInfo)) {
            Boolean isAlliance = ThreadLocalCache.getHeaderUserInfo().getIsAlliance();
            if (Boolean.TRUE.equals(isAlliance)) {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_EXIST_MAIN_OPERSUBJECT);
            } else {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_EXIST_OPERSUBJECT);
            }
        }
    }


    @Override
    public void initializeGrowthValue(MultipartFile file, String operSubjectGuid) throws IOException {

        InputStream inputStream = file.getInputStream();
        //实例化实现了AnalysisEventListener接口的类
        CustomerImportRelationLabelListener listener = new CustomerImportRelationLabelListener();
        //传入参数
        ExcelReader excelReader = new ExcelReader(inputStream, ExcelTypeEnum.XLSX, null, listener);
        //读取信息
        excelReader.read(new Sheet(NumberConstant.NUMBER_1, NumberConstant.NUMBER_0, InitializeGrowthValueDTO.class));
        //获取数据
        List<Object> list = listener.getDatas();
        if (list.size() == 0) {
            return;
        }
        list.remove(0);
        List<InitializeGrowthValueDTO> importMemberInfoQOS = JSONArray.parseArray(JSONArray.toJSONString(list),
                InitializeGrowthValueDTO.class);
        if (list.size() == 0) {
            return;
        }
        log.info("importMemberInfoQOS===>" + JSONObject.toJSONString(importMemberInfoQOS));
        for (InitializeGrowthValueDTO dto : importMemberInfoQOS) {
            HsaOperationMemberInfo memberInfo = hsaOperationMemberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                    .eq(HsaOperationMemberInfo::getOperSubjectGuid, operSubjectGuid)
                    .eq(HsaOperationMemberInfo::getPhoneNum, dto.getPhone()));
            if (Objects.isNull(memberInfo)) {
                log.error("手机号不存在===>" + dto.getPhone());
                continue;
            }
            Integer growthValue;
            try {
                RequestMemberGrowthValue memberGrowthValue = new RequestMemberGrowthValue();
                memberGrowthValue.setMemberInfoGuidList(com.beust.jcommander.internal.Lists.newArrayList(memberInfo.getGuid()));
                growthValue = com.holderzone.member.common.util.verify.ObjectUtil.objToInt(dto.getGrowthValue());
                //当前的成长值 - 之前的成长值 得到 变化的成长值
                int changeGrowthVale = growthValue - memberInfo.getMemberGrowthValue();
                //没有变化 不做调整
                if (changeGrowthVale == 0) {
                    continue;
                }
                //增加
                Integer growthValueType = changeGrowthVale > 0 ? 0 : 1;
                memberGrowthValue.setGrowthValueType(growthValueType);
                memberGrowthValue.setGrowthValue(Math.abs(changeGrowthVale));
                memberGrowthValue.setRemark("系统批量调整成长值");
                updateMemberGrowth(memberGrowthValue);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }

        }

    }

    @Override
    public void updateBySeqFiled(MemberSeqFiledDTO seqFiled) {
        hsaOperationMemberInfoMapper.updateBySeqFiled(seqFiled);
    }

    @Override
    public String findMemberNameByGuid(String memberGuid) {
        return queryByGuid(memberGuid).getUserName();
    }

    @Override
    public void addRoleType(String phoneNum, String roleType) {
        HsaOperationMemberInfo memberInfo = getHsaOperationMemberInfo(phoneNum, roleType);
        if (ObjectUtils.isEmpty(memberInfo) || memberInfo.getRoleType().contains(roleType)) {
            return;
        }
        memberInfo.setRoleType(memberInfo.getRoleType() + "、" + roleType);
        this.updateByGuid(memberInfo);
        MemberUtil.updateMemberRoleType(cacheService, memberInfo.getGuid(), memberInfo.getRoleType());
    }

    @Override
    public void deleteRoleType(String phoneNum, String roleType) {
        HsaOperationMemberInfo memberInfo = getHsaOperationMemberInfo(phoneNum, roleType);
        if (ObjectUtils.isEmpty(memberInfo) || !memberInfo.getRoleType().contains(roleType)) {
            return;
        }
        List<String> roleTypeList = RoleTypeEnum.getRoleTypeToVO(memberInfo.getRoleType());
        roleTypeList.removeIf(r -> Objects.equals(roleType, r));
        memberInfo.setRoleType(RoleTypeEnum.getRoleTypeToDO(roleTypeList));
        this.updateByGuid(memberInfo);
        MemberUtil.updateMemberRoleType(cacheService, memberInfo.getGuid(), memberInfo.getRoleType());
        //todo 启用等级
    }

    @Override
    public List<HsaOperationMemberInfo> listByPhones(String operSubjectGuid, List<String> phoneNums) {
        if (CollectionUtils.isEmpty(phoneNums)) {
            return Lists.newArrayList();
        }
        QueryWrapper<HsaOperationMemberInfo> qw = new QueryWrapper<>();
        qw.lambda().in(HsaOperationMemberInfo::getPhoneNum, phoneNums);
        qw.lambda().eq(HsaOperationMemberInfo::getOperSubjectGuid, operSubjectGuid);
        qw.lambda().eq(HsaOperationMemberInfo::getIsDelete, BooleanEnum.FALSE.getCode());
        return list(qw);
    }

    @Override
    public List<PartnerReserveMemberInfoDTO> queryMemberByGuidList(List<String> memberGuidList) {
        if (CollectionUtils.isEmpty(memberGuidList)) {
            return new ArrayList<>();
        }
        List<HsaOperationMemberInfo> infoList = this.list(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .in(HsaOperationMemberInfo::getGuid, memberGuidList)
                .eq(HsaOperationMemberInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaOperationMemberInfo::getIsDelete, 0)
                .eq(HsaOperationMemberInfo::getAccountState, 0)
        );

        return getReserveMemberInfoDTOList(memberGuidList, infoList);
    }

    private List<PartnerReserveMemberInfoDTO> getReserveMemberInfoDTOList(List<String> memberGuidList, List<HsaOperationMemberInfo> infoList) {
        // 等级图标查询
        Map<String, List<String>> memberGradeIconMap = memberGradeRelationService.queryMemberGradeIcon(memberGuidList);

        return getPartnerReserveMemberInfoDTOS(infoList, memberGradeIconMap);
    }

    private static List<PartnerReserveMemberInfoDTO> getPartnerReserveMemberInfoDTOS(List<HsaOperationMemberInfo> infoList, Map<String, List<String>> memberGradeIconMap) {
        List<PartnerReserveMemberInfoDTO> respList = new ArrayList<>();
        infoList.forEach(info -> {
            PartnerReserveMemberInfoDTO infoDTO = new PartnerReserveMemberInfoDTO();
            infoDTO.setMemberGuid(info.getGuid());
            infoDTO.setUserName(info.getUserName());
            infoDTO.setPhoneNum(info.getPhoneNum());
            infoDTO.setHeadImgUrl(info.getHeadImgUrl());
            infoDTO.setSex(Objects.isNull(info.getSex()) ? 0 : info.getSex());
            if (!MapUtils.isEmpty(memberGradeIconMap)) {
                List<String> gradeIconList = memberGradeIconMap.get(info.getGuid());
                if (CollectionUtils.isNotEmpty(gradeIconList)) {
                    infoDTO.setGradeIconList(gradeIconList);
                }
            }
            respList.add(infoDTO);
        });
        return respList;
    }

    @Override
    public List<PartnerReserveMemberInfoDTO> queryMemberByPhoneList(List<String> phoneList) {
        if (CollectionUtils.isEmpty(phoneList)) {
            return new ArrayList<>();
        }
        List<HsaOperationMemberInfo> infoList = this.list(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .in(HsaOperationMemberInfo::getPhoneNum, phoneList)
                .eq(HsaOperationMemberInfo::getPhoneCountryCode, "86")
                .eq(HsaOperationMemberInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaOperationMemberInfo::getIsDelete, 0)
                .eq(HsaOperationMemberInfo::getAccountState, 0)
        );
        if (CollectionUtils.isEmpty(infoList)) {
            return new ArrayList<>();
        }
        List<String> memberGuidList = infoList.stream()
                .map(HsaOperationMemberInfo::getGuid)
                .distinct()
                .collect(Collectors.toList());

        return getReserveMemberInfoDTOList(memberGuidList, infoList);
    }

    private HsaOperationMemberInfo getHsaOperationMemberInfo(String phoneNum, String roleType) {
        if (StringUtils.isEmpty(phoneNum) || StringUtils.isEmpty(roleType)) {
            throw new MallBaseException(MemberAccountExceptionEnum.ERROR_VERIFY);
        }
        return this.getOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .eq(HsaOperationMemberInfo::getPhoneNum, phoneNum)
                .eq(HsaOperationMemberInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaOperationMemberInfo::getIsDelete, 0)
        );
    }

    @Override
    public void updateConsumptionTime(String memberGuid, LocalDateTime time) {
        hsaOperationMemberInfoMapper.updateConsumptionTime(memberGuid, time);
    }

    @Override
    public Boolean initMemberPinyin(List<String> operSubjectGuid) {
        List<String> guidList = hsaOperationMemberInfoMapper.queueGuidByOperSubjectGuid(operSubjectGuid);
        List<InitMemberPinyinListDTO> initMemberPinyinListDTOS = queueMemberGuidPinyin(guidList);
        List<InitMemberPinyinListDTO> memberPinyinListDTOS = Lists.newArrayList();
        for (InitMemberPinyinListDTO initMemberPinyinListDTO : initMemberPinyinListDTOS) {
            if (!StringUtils.isEmpty(initMemberPinyinListDTO.getUserName()) && StringUtils.isEmpty(initMemberPinyinListDTO.getPinyin())) {
                initMemberPinyinListDTO.setPinyin(ChineseSubstring.getFullSpell(initMemberPinyinListDTO.getUserName()));
                if (!StringUtils.isEmpty(initMemberPinyinListDTO.getPinyin())) {
                    memberPinyinListDTOS.add(initMemberPinyinListDTO);
                }
            }
            if (memberPinyinListDTOS.size() == 3000) {
                List<InitMemberPinyinListDTO> memberPinyinListSave = memberPinyinListDTOS;
                memberPinyinListDTOS = new ArrayList<>();

                memberBaseThreadExecutor.execute(() ->
                        hsaOperationMemberInfoMapper.updatePinyinByGuid(memberPinyinListSave));
            }
        }
        return true;
    }

    /**
     * 多条件查询会员人脸信息
     *
     * @param acquireMemberFaceQO
     * @return
     */
    @Override
    public AcquireMemberFaceVO getAcquireMemberFace(AcquireMemberFaceQO acquireMemberFaceQO) {
        AcquireMemberFaceVO acquireMemberFaceVO = new AcquireMemberFaceVO();
        KinsfolkMemberFaceVO kinsfolkMemberFaceVO = new KinsfolkMemberFaceVO();
        HsaOperationMemberInfo memberInfo = getHsaOperationMemberInfo(acquireMemberFaceQO, kinsfolkMemberFaceVO, acquireMemberFaceVO);
        if (Objects.isNull(memberInfo)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_MEMBER);
        }

        if (memberInfo.getAccountState() == 1) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberAccountExceptionEnum.ERROR_ACCOUNT_IS_DISABLED.getDes(), ThreadLocalCache.getOperSubjectGuid()));
        }

        getMemberCardInfo(memberInfo, acquireMemberFaceVO);


        getMemberLabelInfo(memberInfo, acquireMemberFaceVO);

        BeanUtils.copyProperties(memberInfo, acquireMemberFaceVO);
        acquireMemberFaceVO.setAccountState(memberInfo.getAccountState());
        acquireMemberFaceVO.setMemberGuid(memberInfo.getGuid());
        return acquireMemberFaceVO;
    }

    private HsaOperationMemberInfo getHsaOperationMemberInfo(AcquireMemberFaceQO acquireMemberFaceQO, KinsfolkMemberFaceVO kinsfolkMemberFaceVO, AcquireMemberFaceVO acquireMemberFaceVO) {
        HsaOperationMemberInfo memberInfo;
        if (!StringUtils.isEmpty(acquireMemberFaceQO.getMemberInfoGuid())) {

            memberInfo = baseMapper.queryByGuid(acquireMemberFaceQO.getMemberInfoGuid());
        } else if (!StringUtils.isEmpty(acquireMemberFaceQO.getMemberPhoneNum())) {

            memberInfo = baseMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                    .eq(HsaOperationMemberInfo::getPhoneNum, acquireMemberFaceQO.getMemberPhoneNum())
                    .eq(HsaOperationMemberInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
        } else {
            memberInfo = getMemberInfoByUserId(acquireMemberFaceQO, kinsfolkMemberFaceVO, acquireMemberFaceVO);

        }
        return memberInfo;
    }

    private HsaOperationMemberInfo getMemberInfoByUserId(AcquireMemberFaceQO acquireMemberFaceQO, KinsfolkMemberFaceVO kinsfolkMemberFaceVO, AcquireMemberFaceVO acquireMemberFaceVO) {
        HsaOperationMemberInfo memberInfo;
        memberInfo = hsaOperationMemberInfoMapper.selectOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .eq(HsaOperationMemberInfo::getUserId, acquireMemberFaceQO.getUserId()));
        if (acquireMemberFaceQO.getUserType() == BooleanEnum.TRUE.getCode()) {
            HsaKinsfolkManagement hsaKinsfolkManagement = hsaKinsfolkManagementMapper.selectOne(new LambdaQueryWrapper<HsaKinsfolkManagement>()
                    .eq(HsaKinsfolkManagement::getUserId, acquireMemberFaceQO.getUserId()));
            if (Objects.nonNull(hsaKinsfolkManagement)) {
                memberInfo = hsaOperationMemberInfoMapper.queryByGuid(hsaKinsfolkManagement.getMemberInfoGuid());

                getKinsfolkMemberFace(kinsfolkMemberFaceVO, hsaKinsfolkManagement);
                acquireMemberFaceVO.setKinsfolkMemberFaceVO(kinsfolkMemberFaceVO);
            }
        }
        return memberInfo;
    }

    private static void getKinsfolkMemberFace(KinsfolkMemberFaceVO kinsfolkMemberFaceVO, HsaKinsfolkManagement hsaKinsfolkManagement) {
        kinsfolkMemberFaceVO.setMemberInfoGuid(hsaKinsfolkManagement.getMemberInfoGuid());
        kinsfolkMemberFaceVO.setName(hsaKinsfolkManagement.getName());
        kinsfolkMemberFaceVO.setPhoneNum(hsaKinsfolkManagement.getPhoneNum());
        kinsfolkMemberFaceVO.setIconUrl(hsaKinsfolkManagement.getIconUrl());
        kinsfolkMemberFaceVO.setUserId(hsaKinsfolkManagement.getUserId());
        kinsfolkMemberFaceVO.setLogId(hsaKinsfolkManagement.getLogId());
        kinsfolkMemberFaceVO.setFaceToken(hsaKinsfolkManagement.getFaceToken());
    }

    private void getMemberLabelInfo(HsaOperationMemberInfo memberInfo, AcquireMemberFaceVO acquireMemberFaceVO) {
        List<MemberLabelVO> memberLabelVOS = hsaMemberLabelMapper.listMemberLabel(memberInfo.getGuid());
        if (CollUtil.isNotEmpty(memberLabelVOS)) {
            List<AcquireMemberLabelVO> acquireMemberLabelVOS = memberLabelVOS.stream().map(in -> {
                AcquireMemberLabelVO acquireMemberLabelVO = new AcquireMemberLabelVO();
                acquireMemberLabelVO.setGuid(in.getLabelGuid())
                        .setName(in.getLabelName());
                return acquireMemberLabelVO;
            }).collect(Collectors.toList());
            acquireMemberFaceVO.setAcquireMemberLabelVO(acquireMemberLabelVOS);
        }
    }

    private void getMemberCardInfo(HsaOperationMemberInfo memberInfo, AcquireMemberFaceVO acquireMemberFaceVO) {
        List<HsaMemberInfoCard> hsaMemberInfoCardList = hsaMemberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .in(HsaMemberInfoCard::getMemberInfoGuid, memberInfo.getGuid()));

        if (CollUtil.isNotEmpty(hsaMemberInfoCardList)) {
            List<AcquireMemberCardVO> acquireMemberCardVOList = hsaMemberInfoCardList.stream().map(in -> {
                AcquireMemberCardVO acquireMemberCardVO = new AcquireMemberCardVO();
                acquireMemberCardVO.setCardName(in.getCardName());
                acquireMemberCardVO.setGuid(in.getGuid());
                acquireMemberCardVO.setCardAmount(in.getCardAmount());
                acquireMemberCardVO.setGiftAmount(in.getGiftAmount());
                acquireMemberCardVO.setSubsidyAmount(in.getSubsidyAmount());
                return acquireMemberCardVO;
            }).collect(Collectors.toList());

            acquireMemberFaceVO.setAcquireMemberCardVO(acquireMemberCardVOList);
        }
    }


    /**
     * 分批处理会员信息
     */
    private List<InitMemberPinyinListDTO> queueMemberGuidPinyin(List<String> memberGuid) {
        List<InitMemberPinyinListDTO> initMemberPinyinListDTOS = Lists.newArrayList();
        int pointsDataLimit = 3000;//限制条数
        int size = memberGuid.size();
        //判断是否有必要分批
        if (pointsDataLimit < size) {
            int part = size / pointsDataLimit;//分批数
            log.info("共有 ： " + size + "条，！" + " 分为 ：" + part + "批");
            for (int i = 0; i < part; i++) {
                //500条
                List<String> guid = memberGuid.subList(0, pointsDataLimit);

                initMemberPinyinListDTOS.addAll(hsaOperationMemberInfoMapper.queueMemberPinyin(guid));
                //剔除
                memberGuid.subList(0, pointsDataLimit).clear();
            }
            if (!memberGuid.isEmpty()) {
                //获取数据
                initMemberPinyinListDTOS.addAll(hsaOperationMemberInfoMapper.queueMemberPinyin(memberGuid));
            }
        } else {
            //获取数据
            initMemberPinyinListDTOS = hsaOperationMemberInfoMapper.queueMemberPinyin(memberGuid);
            log.info("直接获取结果hsaOperationMemberInfos>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", JacksonUtils.writeValueAsString(initMemberPinyinListDTOS));
        }
        return initMemberPinyinListDTOS;
    }

    /**
     * 获取会员列表（按等级类型：付费或免费）
     *
     * @param gradeType 等级类型 1:付费会员 0:免费会员
     * @return 会员列表
     */
    @Override
    public List<MemberBasicInfoVO> listMembersByGradeType(Integer gradeType) {
        // 获取有效的会员等级GUID列表
        List<String> validGradeGuids = hsaMemberGradeInfoMapper.selectList(
                new LambdaQueryWrapper<HsaMemberGradeInfo>()
                        .eq(HsaMemberGradeInfo::getEffective, NUMBER_1)
                        .in(HsaMemberGradeInfo::getIsDelete, NUMBER_0, NUMBER_2)
        ).stream().map(HsaMemberGradeInfo::getGuid).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validGradeGuids)) {
            return Collections.emptyList();
        }

        // 获取状态正常的会员卡GUID列表
        List<String> validMemberGuids = hsaMemberGradeCardMapper.selectList(
                new LambdaQueryWrapper<HsaMemberGradeCard>()
                        .eq(HsaMemberGradeCard::getMemberCardStatus, GradeCardStatusEnum.NORMAL.getCode())
                        .in(HsaMemberGradeCard::getGradeGuid, validGradeGuids)
        ).stream().map(HsaMemberGradeCard::getMemberInfoGuid).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validMemberGuids)) {
            return Collections.emptyList();
        }

        // 查询会员列表
        List<HsaOperationMemberInfo> memberList = hsaOperationMemberInfoMapper.selectList(
                new LambdaQueryWrapper<HsaOperationMemberInfo>()
                        .eq(HsaOperationMemberInfo::getAccountState, EnableEnum.ENABLE.getCode())
                        .in(HsaOperationMemberInfo::getGuid, validMemberGuids)
                        .and(wrapper -> {
                            // 付费会员
                            if (gradeType == NUMBER_1) {
                                wrapper.in(HsaOperationMemberInfo::getMemberPaidGradeInfoGuid, validGradeGuids);
                            } else {
                                // 免费会员
                                wrapper.in(HsaOperationMemberInfo::getMemberGradeInfoGuid, validGradeGuids);
                            }
                            return wrapper;
                        })
        );

        // 转换为VO
        return memberList.stream()
                .map(member -> {
                    MemberBasicInfoVO vo = new MemberBasicInfoVO();
                    BeanUtils.copyProperties(member, vo);
                    return vo;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<MemberBasicInfoVO> batchGetMemberInfo(MemberQueryDTO queryDTO) {
        if (CollUtil.isEmpty(queryDTO.getMemberGuidList())) return null;
        List<HsaOperationMemberInfo> memberInfos = hsaOperationMemberInfoMapper.selectList(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .eq(HsaOperationMemberInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaOperationMemberInfo::getIsDelete, BooleanEnum.FALSE.getCode())
                .in(CollUtil.isNotEmpty(queryDTO.getMemberGuidList()), HsaOperationMemberInfo::getGuid, queryDTO.getMemberGuidList()));
        if (CollUtil.isEmpty(memberInfos)) {
            return null;
        }


        Map<String, HsaOperationMemberInfo> operationMemberInfoMap = memberInfos.stream()
                .collect(Collectors.toMap(HsaOperationMemberInfo::getGuid, Function.identity()));
        List<MemberBasicInfoVO> memberInfoBasicVOS = BeanUtil.copyToList(memberInfos, MemberBasicInfoVO.class);

        // 收集所有等级guid
        Set<String> allGradeGuids = new HashSet<>();
        memberInfoBasicVOS.forEach(basicInfoVO -> {
            wrapAllGradeGuids(basicInfoVO, allGradeGuids);
        });

        //批量查询等级信息
        Map<String, HsaMemberGradeInfoVO> gradeInfoMap = CollUtil.isNotEmpty(allGradeGuids)
                ? memberGradeInfoService.batchGetMemberGradeInfoByGuid(new ArrayList<>(allGradeGuids))
                .stream().collect(Collectors.toMap(HsaMemberGradeInfoVO::getGuid, Function.identity()))
                : new HashMap<>();

        memberInfoBasicVOS.forEach(basicInfoVO -> {
            // 免费会员等级
            if (StrUtil.isNotEmpty(basicInfoVO.getMemberGradeInfoGuid())) {
                setVipGradeAndGradeInfoName(basicInfoVO, gradeInfoMap);
            }
            // 付费会员等级
            if (StrUtil.isNotEmpty(basicInfoVO.getMemberPaidGradeInfoGuid())) {
                HsaMemberGradeInfoVO paidGradeInfo = gradeInfoMap.get(basicInfoVO.getMemberPaidGradeInfoGuid());
                if (paidGradeInfo != null) {
                    basicInfoVO.setPaidVipGrade(paidGradeInfo.getVipGrade());
                    basicInfoVO.setMemberPaidGradeInfoName(paidGradeInfo.getName());
                }
            }

            HsaOperationMemberInfo memberInfo = operationMemberInfoMap.get(basicInfoVO.getGuid());
            if (memberInfo != null) {
                basicInfoVO.setIntegral(memberInfo.getMemberIntegral());
            }
        });

        log.info("批量查询会员返回信息共:{}条", memberInfoBasicVOS.size());
        return memberInfoBasicVOS;
    }

    private static void setVipGradeAndGradeInfoName(MemberBasicInfoVO a, Map<String, HsaMemberGradeInfoVO> gradeInfoMap) {
        HsaMemberGradeInfoVO gradeInfo = gradeInfoMap.get(a.getMemberGradeInfoGuid());
        if (gradeInfo != null) {
            a.setVipGrade(gradeInfo.getVipGrade());
            a.setMemberGradeInfoName(gradeInfo.getName());
        }
    }

    private static void wrapAllGradeGuids(MemberBasicInfoVO a, Set<String> allGradeGuids) {
        if (StrUtil.isNotEmpty(a.getMemberGradeInfoGuid())) {
            allGradeGuids.add(a.getMemberGradeInfoGuid());
        }
        if (StrUtil.isNotEmpty(a.getMemberPaidGradeInfoGuid())) {
            allGradeGuids.add(a.getMemberPaidGradeInfoGuid());
        }
    }
}
