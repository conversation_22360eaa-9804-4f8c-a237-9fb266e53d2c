package com.holderzone.member.base.service.card;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.member.HsaMemberFundingDetail;
import com.holderzone.member.base.entity.recharge.HsaCardRechargeGiftDetail;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.dto.card.HsaMemberInfoCardDTO;
import com.holderzone.member.common.dto.card.MemberCardBindDTO;
import com.holderzone.member.common.dto.card.MemberCardGuidDTO;
import com.holderzone.member.common.dto.coupon.MemberCouponStoreListDTO;
import com.holderzone.member.common.dto.excel.MemberInfoCardExcelVO;
import com.holderzone.member.common.dto.member.*;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.base.SingleDataQO;
import com.holderzone.member.common.qo.card.*;
import com.holderzone.member.common.qo.equities.MemberCalculatePreMoneyQO;
import com.holderzone.member.common.qo.growth.AppletStoreListQO;
import com.holderzone.member.common.qo.growth.AppletStoreQO;
import com.holderzone.member.common.qo.member.ImportRechargeAmountQO;
import com.holderzone.member.common.qo.member.MemberCardPasswordQO;
import com.holderzone.member.common.vo.card.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会员卡服务
 * @date 2021/9/1
 */
public interface HsaMemberInfoCardService extends IHolderBaseService<HsaMemberInfoCard> {

    List<HsaMemberInfoCard> savePhysicalCardBind(List<HsaMemberInfoCardDTO> cardInfo2DTOs);

    void updateElectronicCardBind(List<MemberCardBindDTO> entities2BindDTOs);

    List<MemberCardGuidDTO> findCardGuidByGuidList(List<String> cardGuidList, String memberGuid);

    List<HsaMemberInfoCard> batchSave(List<HsaMemberInfoCardDTO> insertCollect);

    PageResult listAbleECardPage(AbleOpenECardQO qo);

    PageResult listOwnCardPage(OwnCardQO qo);

    List<MiniProgramCardDTO> listMiniProgramCard(ListMiniProgramCardQO qo);

    /**
     * 结算中心查询可用卡
     * @param qo
     * @return
     */
    List<MiniProgramCardDTO> listBySettlement(ListMiniProgramCardQO qo);

    /**
     * 查询小程序付款账户
     *
     * @param qo 会员guid
     * @return 查询结果
     */
    List<AppletPaymentAccountVO> queryAppletPaymentAccount(ListMiniProgramCardQO qo);

    boolean setDefaultChoose(String memberGuid, int type, String typeGuid);

    AppletMemberCardDetail queryMiniProgramCardDetail(String guid, String memberGuid,
                                                      String longitude,
                                                      String latitude);

    List<AbleECardVO> listAllAbleECard();

    List<AbleECardVO> listAllAbleECardByChannel();

    List<AbleEntityCardVO> listAbleEntityCard();

    Integer queryOpenCardRuleStatus(String cardGuid);

    void setDefaultValue(String memberGuid, String newGuid);


    void updatePhysicalBind(String ownGuid, String physicalGuid);

    void updateMemberAccount(String ownGuid, String guid);

    void cancelMemberInfoCard(CancelPhysicalCardQO qo);

    /**
     * 分页查询会员持卡数据
     *
     * @param queryMemberInfoCardPageQO
     * @return
     */
    Page<QueryMemberInfoCardVO> queryMemberInfoCard(QueryMemberInfoCardQO queryMemberInfoCardPageQO);

    /**
     * 批量修改会员卡余额
     *
     * @param memberCardBalanceVO memberCardBalanceVO
     * @return Boolean
     */
    Boolean updateMemberCardBalance(UpdateMemberCardBalanceQO memberCardBalanceVO);

    void updateMemberRechargeAmount(List<ImportRechargeAmountQO> importMemberInfoQOS);

    /**
     * 批量条件导出数据
     *
     * @param memberInfoCardExcelQO memberInfoCardExcelQO
     * @return String
     */
    String exportMemberInfoCard(MemberInfoCardExcelQO memberInfoCardExcelQO);

    /**
     * 查询导出会员卡数据
     *
     * @param query 查询会员持卡
     * @return 查询结果
     */
    List<MemberInfoCardExcelVO> queryExportMemberInfoCard(QueryMemberInfoCardQO query);

    /**
     * 实体卡退卡
     *
     * @param retreatPhysicalCardQO retreatPhysicalCardQO
     * @return Boolean
     */
    Boolean retreatPhysicalCard(RetreatPhysicalCardQO retreatPhysicalCardQO);

    /**
     * 获取会员卡下门店数据
     *
     * @param cardGuid
     * @param memberInfoCardGuid
     * @return
     */
    List<StoreCardRuleQO> getStoreCardRule(List<String> cardGuid, String memberInfoCardGuid);

    /**
     * 小程序查询适用门店
     *
     * @param request
     * @return
     */
    PageResult findStoreCardRule(MemberCardStoreDataQO request);

    /**
     * 绑定实体卡
     *
     * @param bindingPhysicalCardQO bindingPhysicalCardQO
     * @return Boolean
     */
    Boolean bindingPhysicalCard(BindingPhysicalCardQO bindingPhysicalCardQO);

    /**
     * 绑定实体卡
     *
     * @param bindingPhysicalCardQO bindingPhysicalCardQO
     * @return Boolean
     */
    PhysicalCardResultVO bindingPhysicalCardByCardNum(BindingPhysicalCardQO bindingPhysicalCardQO);

    /**
     * 实体卡绑定账户
     *
     * @param bindingMemberInfoQO bindingMemberInfoQO
     * @return Boolean
     */
    Boolean bindingMemberInfo(BindingMemberInfoQO bindingMemberInfoQO);

    /**
     * 开通电子卡
     *
     * @param memberCardGuid memberCardGuid
     * @return Boolean
     */
    Boolean openElectronic(String memberCardGuid, String memberInfoGuid, Integer isOverlay);

    /**
     * 批量修改会员卡密码
     *
     * @param memberCardPasswordQO 请求对象
     * @return 操作结果
     */
    int updatePassword(MemberCardPasswordQO memberCardPasswordQO);

    /**
     * 通过会员卡guid，查询当前会员卡详情
     *
     * @param guid
     */
    MemberInfoCardDetailsVO getDetailsByGuid(String guid);

    /**
     * 批量更新电子卡状态
     *
     * @param guids  guids
     * @param status 状态
     * @return 操作结果
     */
    int batchUpdateElectronicCardState(List<String> guids, Integer status);

    /**
     * 批量更新实体卡状态
     *
     * @param guids  guids
     * @param status 状态
     * @return 操作结果
     */
    int batchUpdatePhysicalCardState(List<String> guids, Integer status);

    /**
     * 更新实体卡状态
     *
     * @param guid   guid
     * @param status 状态
     * @return 操作结果
     */
    int updatePhysicalCardState(String guid, Integer status);


    BigDecimal balanceHandler(BigDecimal cardAmount, BigDecimal giftAmount, BigDecimal subsidyAmount);

    /**
     * 小程序查看会员卡
     *
     * @param memberGuid 会员guid
     * @return 查询结果
     */
    List<AppletMemberCardVO> appletQueryMemberCard(String memberGuid);

    /**
     * 修改密码
     *
     * @param qo 修改密码请求qo
     * @return 操作结果
     */
    boolean editPassword(EditPasswordQO qo);

    CardQrCodeVO getQrCode(String cardNum, String creditUserGuid);

    /**
     * 二维码开卡查询卡详情
     *
     * @param cardGuid   会员卡guid
     * @param memberGuid 会员guid
     * @return 查询结果
     */
    MiniProgramCardDTO getQrCodeOpenCardDetail(String cardGuid, String memberGuid);

    Boolean updateStoreScope(UpdateStoreCardRuleQO updateStoreCardRuleQO);

    /**
     * 小程序充值页面
     *
     * @param memberInfoCardGuid memberInfoCardGuid
     * @return
     */
    CardWeChatRechargeDataVO getCardWeChatRechargeData(String memberInfoCardGuid, String storeGuid);

    /**
     * 获取小程序门店list
     *
     * @param request
     * @return
     */
    PageResult getAppletStoreList(AppletStoreQO request);

    /**
     * 获取小程序门店list
     *
     * @param request
     * @return
     */
    PageResult getAppletStoreListByIds(AppletStoreListQO request);

    /**
     * 查询优惠卷适用门店
     *
     * @param dto 查询条件
     * @return 查询结果
     */
    PageResult getMemberCouponStoreList(MemberCouponStoreListDTO dto);

    /**
     * 通过会员guids和carGuid查询会员卡关联表信息
     *
     * @param memberInfoGuids 会员guids
     * @param cardGuid        会员卡guid
     * @return 会员卡关联表列表
     */
    List<HsaMemberInfoCard> getMemberInfoCardList(List<String> memberInfoGuids, String cardGuid);

    /**
     * 通过会员guids和carGuid查询会员卡关联表信息
     *
     * @param memberInfoGuids 会员guids
     * @param cardGuids       会员卡guids
     * @return 会员卡关联表列表
     */
    List<HsaMemberInfoCard> getMemberInfoCards(List<String> memberInfoGuids, List<String> cardGuids);

    /**
     * 通过guid，更新会员卡关联表
     *
     * @param hsaMemberInfoCard 会员卡关联表
     * @return 操作结果
     */
    Boolean updateMemberCard(HsaMemberInfoCard hsaMemberInfoCard);

    /**
     * 批量更新会员卡关联表信息
     *
     * @param activityList 信息
     */
    void batchUpdate(List<HsaMemberInfoCard> activityList);

    /**
     * 批量回收补贴金额
     *
     * @param activityList 信息
     */
    void subsidyRecycling(List<SubsidyRecyclingQO> activityList);

    /**
     * 批量更新会员卡关联表信息
     *
     * @param activityList 信息
     */
    void batchUpdateCard(List<HsaMemberInfoCard> activityList);

    /**
     * 查询会员及拥有卡详情
     *
     * @param request data：会员手机号/会员实体卡卡号
     * @return 会员信息，卡信息列表
     */
    MemberInfoOwnCardVO queryMemberInfoOwnCard(SingleDataQO request);

    /**
     * 根据会员卡金额查询预计到账金额
     */
    RechargeThresholdVO calculatePreMoney(MemberCalculatePreMoneyQO preMoneyQO);

    List<HsaCardRechargeGiftDetail> checkGiftAmount(HsaMemberFundingDetail memberFundingDetail, HsaMemberInfoCard hsaMemberInfoCard);

    void setHsaMemberInfoCardValidityDate(HsaMemberInfoCard hsaMemberInfoCard, HsaCardBaseInfo hsaCardBaseInfo, LocalDateTime now);

    void setBindingMemberCard(BindingMemberCardVO bindingMemberCardVO, HsaMemberInfoCard hsaMemberInfoCard);

    /**
     * 查询支付会员卡
     *
     * @param dto 请求参数
     * @return 会员卡列表
     */
    List<PayMemberCardVO> listPayMemberCard(MemberCardQueryDTO dto);

    /**
     * 查询会员卡二维码
     *
     * @param dto 请求参数
     * @return 会员卡二维码
     */
    MemberCardQrCodeVO getQrCode(MemberCardInfoQueryDTO dto);

    /**
     * 修改会员卡密码
     *
     * @param dto 请求参数
     * @return 修改结果
     */
    Boolean updatePwd(MemberCardPwdUpdateDTO dto);

    /**
     * 修改默认会员卡
     *
     * @param dto 请求参数
     * @return 修改结果
     */
    Boolean updateDefault(MemberCardDefaultUpdateDTO dto);

    /**
     * 会员卡密码校验
     *
     * <AUTHOR>
     * @date 2025/4/7
     */
    Boolean memberCardPwdCheck(MemberCardPwdCheckDTO dto);
}
