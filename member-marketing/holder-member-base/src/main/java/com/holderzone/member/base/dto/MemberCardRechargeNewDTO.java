package com.holderzone.member.base.dto;

import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.member.HsaMemberConsumption;
import com.holderzone.member.base.entity.member.HsaMemberConsumptionPayWay;
import com.holderzone.member.base.entity.member.HsaMemberFundingDetail;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.qo.card.TerMemberCardRechargeQO;
import com.holderzone.member.common.vo.gift.RechargeOrderGiftSummaryVO;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/12/9
 **/
@Data
public class MemberCardRechargeNewDTO {

    private TerMemberCardRechargeQO terMemberCardRechargeQO;
    private HeaderUserInfo headerUserInfo;
    private HsaMemberConsumption hsaMemberConsumption;
    private HsaMemberInfoCard hsaMemberInfoCard;
    private HsaMemberConsumptionPayWay hsaMemberConsumptionPayWay;

    private HsaOperationMemberInfo hsaOperationMemberInfo;
    private HsaMemberFundingDetail hsaMemberFundingDetail;

    public MemberCardRechargeNewDTO() {

    }

    public MemberCardRechargeNewDTO(TerMemberCardRechargeQO terMemberCardRechargeQO, HeaderUserInfo headerUserInfo, HsaMemberConsumption hsaMemberConsumption, HsaMemberInfoCard hsaMemberInfoCard, HsaMemberConsumptionPayWay hsaMemberConsumptionPayWay, HsaMemberFundingDetail hsaMemberFundingDetail, HsaOperationMemberInfo hsaOperationMemberInfo) {
        this.terMemberCardRechargeQO = terMemberCardRechargeQO;
        this.headerUserInfo = headerUserInfo;
        this.hsaMemberConsumption = hsaMemberConsumption;
        this.hsaMemberInfoCard = hsaMemberInfoCard;
        this.hsaMemberConsumptionPayWay = hsaMemberConsumptionPayWay;
        this.hsaMemberFundingDetail = hsaMemberFundingDetail;
        this.hsaOperationMemberInfo = hsaOperationMemberInfo;
    }
}
