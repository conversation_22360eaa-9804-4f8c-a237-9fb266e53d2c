package com.holderzone.member.base.service.card.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.aimilin.bean.ExcelType;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.member.base.dto.MemberCardSendRuleDTO;
import com.holderzone.member.base.entity.card.*;
import com.holderzone.member.base.entity.member.HsaMemberConsumption;
import com.holderzone.member.base.entity.member.HsaMemberFundingDetail;
import com.holderzone.member.base.entity.member.HsaMemberLabel;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.mapper.card.*;
import com.holderzone.member.base.mapper.member.HsaMemberFundingDetailMapper;
import com.holderzone.member.base.mapper.member.HsaMemberLabelMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.card.*;
import com.holderzone.member.base.service.gift.HsaCardRechargeGiftDetailService;
import com.holderzone.member.base.service.grade.HsaBusinessEquitiesService;
import com.holderzone.member.base.service.member.HsaLabelSettingService;
import com.holderzone.member.base.service.member.HsaMemberFundingDetailService;
import com.holderzone.member.base.service.send.ShortMessageSendService;
import com.holderzone.member.base.service.send.WechatSendService;
import com.holderzone.member.base.transform.card.HsaElectronicCardTransform;
import com.holderzone.member.base.util.EasyExcelUtils;
import com.holderzone.member.common.annotation.RedissonLock;
import com.holderzone.member.common.client.FileOssService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ExcelImportConstant;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.constant.WechatMsgParamConstant;
import com.holderzone.member.common.dto.card.*;
import com.holderzone.member.common.dto.excel.FileDto;
import com.holderzone.member.common.dto.member.ExportOpenCardInfo;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.card.*;
import com.holderzone.member.common.enums.exception.CardOperationExceptionEnum;
import com.holderzone.member.common.enums.exception.MemberInfoCardExceptionEnum;
import com.holderzone.member.common.enums.member.AccountStateEnum;
import com.holderzone.member.common.enums.member.LabelTriggerTypeEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.enums.wechat.WechatMsgSendType;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.card.*;
import com.holderzone.member.common.qo.tool.MessagesSendQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.number.SerialNumberBuilder;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import com.holderzone.member.common.util.transaction.TransactionUtil;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.vo.card.AppletOpenCardVO;
import com.holderzone.member.common.vo.growth.GrowthCommodityBaseVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.member.common.constant.NumberConstant.NUMBER_1;

/**
 * <AUTHOR>
 * @description 电子卡业务
 * @date 2021/9/2
 */
@Service
@Slf4j
@AllArgsConstructor
public class HsaElectronicCardServiceImpl extends HolderBaseServiceImpl<HsaElectronicCardMapper, HsaElectronicCard> implements HsaElectronicCardService {

    private final HsaElectronicCardMapper electronicCardMapper;

    private final HsaCardOpenRuleMapper hsaCardOpenRuleMapper;

    private final HsaCardBaseInfoMapper hsaCardBaseInfoMapper;

    private final HsaMemberLabelMapper hsaMemberLabelMapper;

    private final SerialNumberBuilder serialNumberBuilder;

    private final GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    @Lazy
    private HsaCardRechargeGiftDetailService hsaCardRechargeGiftDetailService;

    private final WechatSendService wechatSendService;

    @Resource
    private ShortMessageSendService sendService;

    @Lazy
    @Autowired
    private HsaMemberInfoCardService hsaMemberInfoCardService;

    @Lazy
    @Autowired
    private HsaLabelSettingService hsaLabelSettingService;

    @Lazy
    @Autowired
    private HsaStoreCardRuleService hsaStoreCardRuleService;

    @Lazy
    @Autowired
    private HsaMemberFundingDetailService hsaMemberFundingDetailService;

    @Resource
    private SystemRoleHelper systemRoleHelper;

    private final Executor memberBaseThreadExecutor;

    private final TransactionUtil transactionUtil;

    @Lazy
    @Resource
    private HsaCardBaseInfoService cardBaseInfoService;

    @Resource
    @Lazy
    private HsaCardOpenRuleService openRuleService;

    private final HsaCardInfoMapper hsaCardInfoMapper;

    private final HsaCardInfoMapper cardInfoMapper;

    private final HsaStoreCardRuleMapper hsaStoreCardRuleMapper;

    private final HsaMemberCardRuleMapper memberCardRuleMapper;

    private final HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    private final HsaMemberInfoCardMapper memberInfoCardMapper;


    private final RedissonClient redissonClient;

    private final FileOssService fileOssService;

    /**
     * 资金明细，增加
     */
    private static final int ADD = 0;


    @Override
    @RedissonLock(lockName = "BATCH_OPEN_ELECTRONIC_CARD", tryLock = true, leaseTime = 10)
    public void openCardWithPhysicalCreate(CreatePhysicalCardSecretQO qo, LocalDateTime now, ProduceSecretDTO produceSecretDTO) {
        try {
            HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
            //去过滤出要开通的会员电子卡
            List<String> memberGuids = qo.getMemberInfoList().stream()
                    .map(CardBindMemberQO::getMemberGuid)
                    .collect(Collectors.toList());
            if (ObjectUtil.isNull(memberGuids) || memberGuids.isEmpty()) {
                return;
            }
            List<String> memberGuidList = electronicCardMapper.listMemberGuidByRange(qo.getCardGuid(), memberGuids);
            List<String> saveList = qo.getMemberInfoList()
                    .stream()
                    .filter(obj -> !memberGuidList.contains(obj.getMemberGuid()))
                    .map(CardBindMemberQO::getMemberGuid)
                    .collect(Collectors.toList());
            //构建保存数据
            List<HsaElectronicCard> electronicCardList = new ArrayList<>();
            //查询可开电子卡数量
            List<CardInfoDetailDTO> availableList = hsaCardInfoMapper.getAvailableElectronicCard(Collections.singletonList(qo.getCardGuid()));
            if (CollUtil.isEmpty(availableList)) {
                return;
            }
            CardInfoDetailDTO cardInfoDetail = availableList.get(NumberConstant.NUMBER_0);
            boolean sendCountLimit = ObjectUtil.equal(cardInfoDetail.getSendCountLimit(), BooleanEnum.TRUE.getCode());
            Integer countLimit = cardInfoDetail.getCountLimit();
            if ((ObjectUtil.isNull(countLimit) || countLimit <= NumberConstant.NUMBER_0) && sendCountLimit) {
                return;
            }
            for (String guid : saveList) {
                if (countLimit <= NumberConstant.NUMBER_0 && sendCountLimit) {
                    continue;
                }
                HsaElectronicCard electronicCard = getHsaElectronicCard(qo, now, guid, headerUserInfo);
                electronicCardList.add(electronicCard);
                countLimit--;
            }
            if (!electronicCardList.isEmpty()) {
                toOpenCard(electronicCardList, cardInfoDetail);
            }
        } catch (Exception e) {
            log.warn(CardOperationExceptionEnum.CREATE_PHYSICAL_CARD_SECRET_BIND_ELECTRONIC_FAIL.getDes());
            e.printStackTrace();
        }
    }

    private HsaElectronicCard getHsaElectronicCard(CreatePhysicalCardSecretQO qo, LocalDateTime now, String guid, HeaderUserInfo headerUserInfo) {
        HsaElectronicCard electronicCard = new HsaElectronicCard();
        electronicCard.setCardGuid(qo.getCardGuid());
        electronicCard.setCardNum(serialNumberBuilder.getSequence(null));
        electronicCard.setEnterpriseGuid(qo.getEnterpriseGuid());
        electronicCard.setOperSubjectGuid(qo.getOperSubjectGuid());
        electronicCard.setGuid(guidGeneratorUtil.getStringGuid(HsaElectronicCardService.class.getSimpleName()));
        electronicCard.setIsDelete(BooleanEnum.FALSE.getCode());
        electronicCard.setMemberInfoGuid(guid);
        electronicCard.setSource(SourceTypeEnum.ADD_BACKGROUND.getCode());
        if (qo.getSourceType() == 1) {
            electronicCard.setSource(SourceTypeEnum.ADD_ONE_MACHINE.getCode());
        } else {
            electronicCard.setSource(qo.getSourceType());
        }
        electronicCard.setCardState(EletronicCardStateEnum.NORMAL.getCode());
        electronicCard.setOpenCardWay(ElectronicCardOpenWayEnum.SPECIFY_USER_OPEN.getCode());
        electronicCard.setGmtModified(now);
        electronicCard.setGmtCreate(now);
        electronicCard.setOpenWay(ElectronicCardOpenTypeEnum.ACTIVATION_OF_PHYSICAL_CARD.getCode());
        electronicCard.setOperationPhone(Optional.of(headerUserInfo).map(HeaderUserInfo::getTel).orElse(null));
        electronicCard.setOperationName(Optional.of(headerUserInfo).map(HeaderUserInfo::getUserName).orElse(null));
        return electronicCard;
    }

    public void toOpenCard(List<HsaElectronicCard> electronicCardList, CardInfoDetailDTO cardInfoDetail) {
        transactionUtil.transactional(s -> {
            //创建电子卡
            saveBatchAndUpdateCount(electronicCardList);
            List<MemberCardBindDTO> memberCardBindDTOS = HsaElectronicCardTransform.INSTANCE.entities2BindDTOs(electronicCardList);
            for (MemberCardBindDTO bindDTO : memberCardBindDTOS) {
                bindDTO.setIsValue(true);
                bindDTO.setCardValidity(cardInfoDetail.getCardValidity() == 0 ? 0 : 1);
                if (ObjectUtil.equal(CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_DAY.getCode(), cardInfoDetail.getCardValidity())) {
                    //根据时间单位计算有效期
                    bindDTO.setCardValidityDate(buildValidityDate(cardInfoDetail.getValidityUnit(), cardInfoDetail.getCardValidityTime()));
                }
                if (ObjectUtil.equal(CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_YEAR.getCode(), cardInfoDetail.getCardValidity())) {
                    bindDTO.setCardValidityDate(cardInfoDetail.getCardValidityDate());
                }
            }
            //更新绑定关系
            hsaMemberInfoCardService.updateElectronicCardBind(memberCardBindDTOS);
        });
    }

    private void saveBatchAndUpdateCount(List<HsaElectronicCard> electronicCardList) {
        if (ObjectUtil.isNull(electronicCardList) || electronicCardList.isEmpty()) {
            return;
        }
        this.saveBatch(electronicCardList);
        //更新数量
        Map<String, List<HsaElectronicCard>> collect = electronicCardList.stream()
                .collect(Collectors.groupingBy(HsaElectronicCard::getCardGuid));
        List<ECardCreateCountDTO> countList = new ArrayList<>();
        collect.forEach((key, value) -> {
            ECardCreateCountDTO count = new ECardCreateCountDTO();
            count.setCardGuid(key);
            count.setCount(value.size());
            countList.add(count);
        });
//        memberBaseThreadExecutor.execute(() -> {
        openRuleService.updateElectronicCreateCount(countList);
//        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedissonLock(lockName = "BATCH_OPEN_ELECTRONIC_CARD", tryLock = true, leaseTime = 10)
    public String batchOpen(OpenElectronicCardQO qo) throws IOException {
        List<ExportOpenCardInfo> exportOpenCardInfoList = new ArrayList<>();
        verify(qo);
        List<String> memberInfoGuids = qo.getMemberInfoGuid();

        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        if (StringUtils.isEmpty(operSubjectGuid)) {
            operSubjectGuid = qo.getCardGuidList().get(0).getOperSubjectGuid();
        }

        Map<String, String> memberPhoneMap = hsaOperationMemberInfoMapper.selectList(
                        new LambdaQueryWrapper<HsaOperationMemberInfo>()
                                .select(HsaOperationMemberInfo::getGuid,
                                        HsaOperationMemberInfo::getPhoneNum
                                )
                                .eq(HsaOperationMemberInfo::getOperSubjectGuid, operSubjectGuid)
                                .in(HsaOperationMemberInfo::getGuid, memberInfoGuids))
                .stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(HsaOperationMemberInfo::getGuid, HsaOperationMemberInfo::getPhoneNum));

        for (String memberGuid : qo.getMemberInfoGuid()) {
            List<MemberCardOpenDTO> cardGuidList = qo.getCardGuidList();
            if (!ObjectUtil.isNull(memberPhoneMap) && memberPhoneMap.containsKey(memberGuid)) {
                String memberPhone = memberPhoneMap.get(memberGuid);
                setMemberPhone(memberPhone, cardGuidList);
            }
            exportOpenCardInfoList.addAll(dealBatchOpen(cardGuidList, memberGuid));
        }
        //发送开通成功短信
        Optional<ExportOpenCardInfo> successData = exportOpenCardInfoList.stream()
                .filter(x -> MiniProgramOpenCardReturnEnum.OPEN_SUCCESS.getDes().equals(x.getOpenStatus())).findFirst();
        if (Boolean.TRUE.equals(successData.isPresent())) {
            dealSuccessMessage(exportOpenCardInfoList, operSubjectGuid);
        }

        Optional<ExportOpenCardInfo> errorData = exportOpenCardInfoList.stream()  //判断是否有开通失败的电子卡
                .filter(x -> MiniProgramOpenCardReturnEnum.OPEN_ERROR.getDes().equals(x.getOpenStatus())).findFirst();
        if (Boolean.TRUE.equals(errorData.isPresent())) {  //如果有开通失败的数据，返回开卡数据的URL地址
            return getUploadUrl(exportOpenCardInfoList).replace("http", "https");
        }
        //新增会员，添加标签
        refreshLabel(qo.getMemberInfoGuid());
        return null;
    }

    private void dealSuccessMessage(List<ExportOpenCardInfo> exportOpenCardInfoList, String operSubjectGuid) {
        List<ExportOpenCardInfo> exportOpenCardInfos = exportOpenCardInfoList.stream()
                .filter(x -> MiniProgramOpenCardReturnEnum.OPEN_SUCCESS.getDes().equals(x.getOpenStatus())).collect(Collectors.toList());
        List<String> cardGuidList = exportOpenCardInfos.stream().map(ExportOpenCardInfo::getCardGuid).collect(Collectors.toList());

        Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap = hsaCardBaseInfoMapper.queryByGuids(cardGuidList)
                .stream()
                .collect(Collectors.toMap(HsaCardBaseInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));

        List<MessagesSendQO> messagesSendList = new ArrayList<>();
        for (ExportOpenCardInfo exportOpenCardInfo : exportOpenCardInfos) {
            HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMap.get(exportOpenCardInfo.getCardGuid());
            MessagesSendQO messagesSendQO = new MessagesSendQO();
            messagesSendQO.setPhone(exportOpenCardInfo.getMemberAccount());
            messagesSendQO.setOperSubjectGuid(operSubjectGuid);
            messagesSendQO.setEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid());
            messagesSendQO.setCardBalance(Objects.nonNull(hsaCardBaseInfo.getCardValueMoney())
                    ? hsaCardBaseInfo.getCardValueMoney() : BigDecimal.ZERO);
            messagesSendList.add(messagesSendQO);
        }
        sendService.openCardSendBatch(messagesSendList);
    }

    private void setMemberPhone(String memberPhone, List<MemberCardOpenDTO> cardGuidList) {
        if (CollUtil.isEmpty(cardGuidList)) {
            return;
        }
        for (MemberCardOpenDTO memberCardOpenDTO : cardGuidList) {
            memberCardOpenDTO.setMemberPhoneNum(memberPhone);
        }
    }


    private String getUploadUrl(List<ExportOpenCardInfo> exportOpenCardInfoList) throws IOException {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String formatDateTime = now.format(formatter);
        byte[] bytes = EasyExcelUtils.exportByExcel(exportOpenCardInfoList, ExportOpenCardInfo.class);
        try {
            FileDto fileDto = new FileDto();
            fileDto.setFileContent(com.holderzone.framework.security.SecurityManager.entryptBase64(bytes));
            fileDto.setFileName(systemRoleHelper.getReplace(ExcelImportConstant.BATCH_OPENING_MEMBERSHIP_CARD, ThreadLocalCache.getOperSubjectGuid()) + formatDateTime + "." + ExcelType.XLSX);
            String upload = fileOssService.upload(fileDto);
            log.info("错误信息文件下载路径->>>>>{}", upload);
            return upload;
        } catch (Exception e) {
            log.error("上传文件失败");
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_FILE_UPLOAD);
        }
    }

    @Override
    public void batchOpenWithExcelMember(List<MemberCardOpenDTO> cardOpenList) {
        if (CollUtil.isEmpty(cardOpenList)) {
            return;
        }
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        memberBaseThreadExecutor.execute(() -> {
            RLock lock = redissonClient.getLock("BATCH_OPEN_ELECTRONIC_CARD");
            try {
                if (!lock.tryLock(NumberConstant.NUMBER_20, NumberConstant.NUMBER_12, TimeUnit.SECONDS)) {
                    return;
                }
                ThreadLocalCache.put(JSON.toJSONString(headerUserInfo));
                //构建需要插入的电子卡信息
                List<HsaElectronicCard> electronicCardList = new ArrayList<>();
                List<HsaMemberInfoCardDTO> memberInfoCardList = new ArrayList<>();
                //查询电子卡面额
                List<String> collectCardGuid = cardOpenList.stream().map(MemberCardOpenDTO::getCardGuid).collect(Collectors.toList());
                //超额相关
                Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap = hsaCardBaseInfoMapper.selectList(new LambdaQueryWrapper<HsaCardBaseInfo>()
                                .in(HsaCardBaseInfo::getGuid, collectCardGuid))
                        .stream()
                        .collect(Collectors.toMap(HsaCardBaseInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));
                //判断是否可以开通电子卡
                List<CardInfoDetailDTO> availableList = hsaCardInfoMapper.getAvailableElectronicCard(collectCardGuid);
                Map<String, Integer> availableMap = availableList.stream().collect(Collectors.toMap(CardInfoDetailDTO::getCardGuid, CardInfoDetailDTO::getCountLimit));

                Map<String, CardInfoDetailDTO> cardInfo = availableList.stream().collect(Collectors.toMap(CardInfoDetailDTO::getCardGuid, obj -> obj));
                Map<String, BigDecimal> cardMoney = availableList.stream().collect(Collectors.toMap(CardInfoDetailDTO::getCardGuid, CardInfoDetailDTO::getCardValueMoney));
                for (MemberCardOpenDTO memberCard : cardOpenList) {
                    HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMap.get(memberCard.getCardGuid());
                    //查询数量
                    Integer limit = availableMap.get(memberCard.getCardGuid());
                    // 发卡数量限制(0表示不限制)
                    Integer sendLimit = Optional.ofNullable(memberCard.getSendCountLimit()).orElse(0);
                    String cardGuid = memberCard.getCardGuid();
                    if (checkSendLimit(sendLimit, limit, cardInfo, cardGuid)) continue;
                    String guid = guidGeneratorUtil.getStringGuid(HsaElectronicCardService.class.getSimpleName());
                    HsaElectronicCard hsaElectronicCard = buildElectronicCard(guid, memberCard.getCardGuid(), memberCard, memberCard.getMemberInfoGuid());
                    electronicCardList.add(hsaElectronicCard);
                    HsaMemberInfoCardDTO hsaMemberInfoCard = buildMemberInfoCard(hsaElectronicCard, memberCard, memberCard.getMemberInfoGuid(), cardInfo.get(memberCard.getCardGuid()), LocalDateTime.now(), hsaCardBaseInfo);
                    hsaMemberInfoCard.setCardAmount(BigDecimalUtil.nonNullValue(cardMoney.get(memberCard.getCardGuid())));
                    memberInfoCardList.add(hsaMemberInfoCard);
                    //减少数量
                    availableMap.put(memberCard.getCardGuid(), limit - NumberConstant.NUMBER_1);
                }
                //保存数据
                saveBatchAndUpdateCount(electronicCardList);
                if (CollUtil.isNotEmpty(memberInfoCardList)) {
                    List<HsaMemberInfoCard> hsaMemberInfoCards = hsaMemberInfoCardService.batchSave(memberInfoCardList);
                    addOpenCardBalanceRecord(hsaMemberInfoCards, cardMoney, SourceTypeEnum.ADD_BACKGROUND.getCode(),
                            CardTypeEnum.CARD_TYPE_EQUITY.getCode());
                    saveStoreCardRule(memberInfoCardList);
                }
            } catch (Exception e) {
                log.warn(CardOperationExceptionEnum.IMPORT_MEMBER_AND_BIND_ELECTRONIC_FAIL.getDes());
            } finally {
                if (lock != null && lock.isLocked()) {
                    lock.unlock();
                }
            }
        });
    }

    private static boolean checkSendLimit(Integer sendLimit, Integer limit, Map<String, CardInfoDetailDTO> cardInfo, String cardGuid) {
        if (sendLimit != 0 && (ObjectUtil.isNull(limit) || limit <= NumberConstant.NUMBER_0)) {
            return true;
        }
        if (ObjectUtil.isNull(cardInfo) || !cardInfo.containsKey(cardGuid)) {
            return true;
        }
        return false;
    }

    /**
     * 保存门店会员卡规则
     *
     * @param memberInfoCardList 会员信息卡列表
     */
    @Override
    public void saveStoreCardRule(List<HsaMemberInfoCardDTO> memberInfoCardList) {
        List<HsaStoreCardRule> hsaStoreCardRuleArrayList = Lists.newArrayList();
        for (HsaMemberInfoCardDTO hsaMemberInfoCardDTO : memberInfoCardList) {
            // 获取父级门店
            List<HsaStoreCardRule> hsaStoreCardRuleList = getStoreCardRuleList(hsaMemberInfoCardDTO);

            // 获取父级下档口
            Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap = getStoreCardRuleMap(hsaStoreCardRuleList);

            // 组装门店数组
            addStoreCardRule(hsaMemberInfoCardDTO, hsaStoreCardRuleList, hsaStoreCardRuleMap, hsaStoreCardRuleArrayList);
        }
        if (CollUtil.isNotEmpty(hsaStoreCardRuleArrayList)) {
            hsaStoreCardRuleService.saveBatch(hsaStoreCardRuleArrayList);
        }
    }


    private void addStoreCardRule(HsaMemberInfoCardDTO hsaMemberInfoCardDTO, List<HsaStoreCardRule> hsaStoreCardRuleList, Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap, List<HsaStoreCardRule> hsaStoreCardRuleArrayList) {
        if (CollUtil.isNotEmpty(hsaStoreCardRuleList)) {
            hsaStoreCardRuleList.forEach(in -> {
                HsaStoreCardRule hsaStoreCardRule = new HsaStoreCardRule();
                BeanUtils.copyProperties(in, hsaStoreCardRule);
                hsaStoreCardRule.setGuid(guidGeneratorUtil.getStringGuid(HsaStoreCardRule.class.getSimpleName()))
                        .setMemberInfoCardGuid(hsaMemberInfoCardDTO.getGuid())
                        .setCardGuid(hsaMemberInfoCardDTO.getCardGuid())
                        .setGmtCreate(LocalDateTime.now())
                        .setGmtModified(LocalDateTime.now());
                //添加档口信息
                addStoreCardRule(hsaMemberInfoCardDTO, in, hsaStoreCardRuleMap, hsaStoreCardRule, hsaStoreCardRuleArrayList);
                hsaStoreCardRuleArrayList.add(hsaStoreCardRule);
            });

        }
    }

    private void addStoreCardRule(HsaMemberInfoCardDTO hsaMemberInfoCardDTO, HsaStoreCardRule in, Map<String, List<HsaStoreCardRule>> hsaStoreCardRuleMap, HsaStoreCardRule hsaStoreCardRule, List<HsaStoreCardRule> hsaStoreCardRuleArrayList) {
        if (CollUtil.isNotEmpty(hsaStoreCardRuleMap) && hsaStoreCardRuleMap.containsKey(in.getGuid())) {
            List<HsaStoreCardRule> storeCardRule = hsaStoreCardRuleMap.get(in.getGuid());
            storeCardRule.forEach(on -> {
                HsaStoreCardRule rule = new HsaStoreCardRule();
                BeanUtils.copyProperties(on, rule);
                rule.setParentGuid(hsaStoreCardRule.getGuid())
                        .setGuid(guidGeneratorUtil.getStringGuid(HsaStoreCardRule.class.getSimpleName()))
                        .setMemberInfoCardGuid(hsaMemberInfoCardDTO.getGuid())
                        .setCardGuid(hsaMemberInfoCardDTO.getCardGuid());
                hsaStoreCardRuleArrayList.add(rule);
            });
        }
    }

    /**
     * 根据给定的 HsaStoreCardRule 列表，返回一个规则映射图。
     * 规则映射图的键为规则的父 GUID，值为与该父 GUID 关联的规则列表。
     *
     * @param hsaStoreCardRuleList HsaStoreCardRule 列表
     * @return 规则映射图
     */
    private Map<String, List<HsaStoreCardRule>> getStoreCardRuleMap(List<HsaStoreCardRule> hsaStoreCardRuleList) {
        // 查询所有满足条件的 HsaStoreCardRule 并按父 GUID 分组
        return hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                        .in(HsaStoreCardRule::getParentGuid, hsaStoreCardRuleList.stream().map(HsaStoreCardRule::getGuid).collect(Collectors.toList()))
                        .isNotNull(HsaStoreCardRule::getParentGuid))
                .stream()
                .collect(Collectors.groupingBy(HsaStoreCardRule::getParentGuid));
    }


    /**
     * 根据会员信息卡获取优惠券规则列表
     *
     * @param hsaMemberInfoCardDTO 会员信息卡DTO对象
     * @return 优惠券规则列表
     */
    private List<HsaStoreCardRule> getStoreCardRuleList(HsaMemberInfoCardDTO hsaMemberInfoCardDTO) {
        return hsaStoreCardRuleMapper.selectList(new LambdaQueryWrapper<HsaStoreCardRule>()
                .eq(HsaStoreCardRule::getCardGuid, hsaMemberInfoCardDTO.getCardGuid())
                .isNull(HsaStoreCardRule::getMemberInfoCardGuid)
                .isNull(HsaStoreCardRule::getParentGuid));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedissonLock(lockName = "BATCH_OPEN_ELECTRONIC_CARD", leaseTime = 10, tryLock = true)
    public AppletOpenCardVO miniProgramOpenCard(MemberCardOpenDTO dto) {
        if (memberInfoCardMapper.selectCount(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getMemberInfoGuid, dto.getMemberInfoGuid())
                .eq(HsaMemberInfoCard::getCardGuid, dto.getCardGuid())
                .isNotNull(HsaMemberInfoCard::getElectronicCardGuid)) > 0) {
            log.info("会员卡已经开通，cardGuid：{}", dto.getCardGuid());
            throw new MemberBaseException(systemRoleHelper.getReplace(CardOperationExceptionEnum.MEMBER_ALREADY_OPEN,
                    ThreadLocalCache.getOperSubjectGuid()));
        }
        //判断是否可以开通电子卡
        CardInfoDetailDTO available = cardInfoMapper.getSelfFreeOpenCard(dto.getCardGuid());
        log.info("电子卡开通条件，available：{}", available);
        AppletOpenCardVO appletOpenCardVO = new AppletOpenCardVO();
        //0:开通成功 1:您的账户已禁用，不可开通 2:会员卡已结束开通 3:开通失败
        if (Objects.isNull(available)) {
            appletOpenCardVO.setCode(MiniProgramOpenCardReturnEnum.OPEN_ERROR.getCode());
            return appletOpenCardVO;
        }
        HsaOperationMemberInfo member = hsaOperationMemberInfoMapper.queryByGuid(dto.getMemberInfoGuid());
        if (Objects.isNull(member) || member.getAccountState() == AccountStateEnum.FREEZE.getCode()) {
            appletOpenCardVO.setCode(MiniProgramOpenCardReturnEnum.NOT_CAN_OPEN.getCode());
            return appletOpenCardVO;
        }
        dto.setMemberPhoneNum(member.getPhoneNum());
        if (available.getCardStatus() == CardStatusEnum.FINISH.getCode() ||
                (available.getSendCountLimit() != NumberConstant.NUMBER_0 && available.getCountLimit() <= NumberConstant.NUMBER_0) ||
                available.getSendStatus() != SendCardStateEnum.CARD_STATE_START.getCode()) {
            appletOpenCardVO.setCode(MiniProgramOpenCardReturnEnum.MEMBER_CARD_CLOSE_OPEN.getCode());
            return appletOpenCardVO;
        }
        if (!(available.getIsSupportElectronicCard() == BooleanEnum.TRUE.getCode()
                && available.getElectronicOpenWay() == ElectronicOpenWayEnum.AUTONOMOUSLY_OPEN.getCode()
                && available.getSelfType() == SelfTypeEnum.FREE.getCode())) {
            appletOpenCardVO.setCode(MiniProgramOpenCardReturnEnum.OPEN_ERROR.getCode());
            return appletOpenCardVO;
        }
        if (!judgeOpenCardScope(available, dto.getMemberInfoGuid())) {
            appletOpenCardVO.setCode(MiniProgramOpenCardReturnEnum.OPEN_ERROR.getCode());
            return appletOpenCardVO;
        }
        HsaMemberInfoCard hsaMemberInfoCardDTO = getMemberPayOpenCard(dto, available, null);
        appletOpenCardVO.setCode(MiniProgramOpenCardReturnEnum.SUCCESS.getCode());
        appletOpenCardVO.setOwnGuid(hsaMemberInfoCardDTO.getGuid());
        sendChangeCardAmountMsg(hsaMemberInfoCardDTO);
        return appletOpenCardVO;
    }

    private void sendChangeCardAmountMsg(HsaMemberInfoCard memberInfoCard) {
        try {
            log.info("发送开卡变动金额消息，memberInfoCard:{}", memberInfoCard);
            // 变动时间:time6,变动原因:thing7,当前余额:amount8,变动金额:amount5
            //发送消息
            MessagesSendQO messagesSendQO = new MessagesSendQO();
            if (Objects.nonNull(memberInfoCard)) {
                BigDecimal cardAmount = Objects.nonNull(memberInfoCard.getCardAmount()) ? memberInfoCard.getCardAmount() : BigDecimal.ZERO;
                BigDecimal giftAmount = Objects.nonNull(memberInfoCard.getGiftAmount()) ? memberInfoCard.getGiftAmount() : BigDecimal.ZERO;
                BigDecimal subsidyAmount = Objects.nonNull(memberInfoCard.getSubsidyAmount()) ? memberInfoCard.getSubsidyAmount() : BigDecimal.ZERO;
                BigDecimal amount = cardAmount.add(giftAmount).add(subsidyAmount);
                // 查询金额
                CardFreezeBalanceAmountQO balanceAmountQO = new CardFreezeBalanceAmountQO();
                balanceAmountQO.setMemberInfoCardGuid(memberInfoCard.getGuid());
                balanceAmountQO.setIsRefresh(BooleanEnum.FALSE.getCode());
                BigDecimal freightAmount = hsaCardRechargeGiftDetailService.getMemberCardFreezeAmount(balanceAmountQO);
                amount = amount.add(freightAmount);

                HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(memberInfoCard.getCardGuid());
                messagesSendQO.setTemplateName(WechatMsgSendType.AMOUNT_CHANGE.getMsgTitle());
                messagesSendQO.setCardName(hsaCardBaseInfo.getCardName());
                messagesSendQO.setCardImage(hsaCardBaseInfo.getCardImage());
                messagesSendQO.setPrams(generateAppletsParamNew(amount, memberInfoCard));
                messagesSendQO.setMemberInfoCardGuid(memberInfoCard.getGuid());
                messagesSendQO.setOperSubjectGuid(memberInfoCard.getOperSubjectGuid());
                messagesSendQO.setEnterpriseGuid(memberInfoCard.getEnterpriseGuid());
                messagesSendQO.setPhone(memberInfoCard.getMemberPhoneNum());
                HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
                memberBaseThreadExecutor.execute(() -> {
                    // 远程调用需要
                    ThreadLocalCache.put(JSON.toJSONString(headerUserInfo));
                    log.info("发送开卡变动金额消息，messagesSendQO:{}", messagesSendQO);
                    wechatSendService.send(messagesSendQO);
                });
            }
        } catch (Exception e) {
            log.error("发送开卡变动金额消息失败，memberInfoCard:{}", memberInfoCard, e);
        }
    }

    private Map<String, String> generateAppletsParamNew(BigDecimal amount,
                                                        HsaMemberInfoCard memberInfoCard) {
        Map<String, String> params = new HashMap<>();
        String amountType;
        params.put(WechatMsgParamConstant.InnerAmountChangeConstant.MP_CHANGE_REASON, "开卡");
        amountType = "+" + memberInfoCard.getCardAmount();

        params.put("amount1", amountType);
        params.put("date4", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        params.put("thing7", memberInfoCard.getCardName());
        params.put("amount2", amount + "");
        return params;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HsaMemberInfoCard getMemberPayOpenCard(MemberCardOpenDTO dto, CardInfoDetailDTO available, HsaCardPayRecord hsaCardPayRecord) {
        String guid = guidGeneratorUtil.getStringGuid(HsaElectronicCardService.class.getSimpleName());
        dto.setSource(Optional.ofNullable(dto.getSource()).orElse(SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode()));
        HsaElectronicCard hsaElectronicCard = appletBuildElectronicCard(guid, dto, hsaCardPayRecord);

        //保存数据
        saveBatchAndUpdateCount(Lists.newArrayList(hsaElectronicCard));
        HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(hsaElectronicCard.getCardGuid());
        //构建会员卡
        HsaMemberInfoCardDTO hsaMemberInfoCardDTO = buildMemberInfoCard(hsaElectronicCard, dto, hsaElectronicCard.getMemberInfoGuid(),
                available, LocalDateTime.now(), hsaCardBaseInfo);
        //查询是否绑定实体卡
        HsaMemberInfoCard hsaMemberInfoCard = memberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getCardGuid, dto.getCardGuid())
                .eq(HsaMemberInfoCard::getMemberInfoGuid, dto.getMemberInfoGuid()));
        Map<String, BigDecimal> cardMoney = new HashMap<>();
        //设置可用卡余额
        cardMoney.put(dto.getCardGuid(), available.getCardValueMoney());
        //如果有实体卡 更新实体卡金额
        if (Objects.nonNull(hsaMemberInfoCard)) {
            hsaMemberInfoCardService.updateElectronicCardBind(toMemberCardBindDTO(hsaMemberInfoCardDTO, available, hsaElectronicCard));
            hsaMemberInfoCard = memberInfoCardMapper.queryByGuid(hsaMemberInfoCard.getGuid());
            addOpenCardBalanceRecord(Lists.newArrayList(hsaMemberInfoCard), cardMoney, SourceTypeEnum.
                    ADD_WECHAT_ZHUANCAN.getCode(), CardTypeEnum.CARD_TYPE_EQUITY.getCode());
        } else {
            List<HsaMemberInfoCardDTO> electronicCardList = Lists.newArrayList(hsaMemberInfoCardDTO);
            List<HsaMemberInfoCard> hsaMemberInfoCards = hsaMemberInfoCardService.batchSave(electronicCardList);
            if (Objects.isNull(hsaCardPayRecord))
                addOpenCardBalanceRecord(hsaMemberInfoCards, cardMoney, SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode(),
                        CardTypeEnum.CARD_TYPE_EQUITY.getCode());
            saveStoreCardRule(electronicCardList);
            hsaMemberInfoCard = new HsaMemberInfoCard();
            BeanUtils.copyProperties(hsaMemberInfoCardDTO, hsaMemberInfoCard);
        }
        MessagesSendQO messagesSendQO = getMessagesSendQO(dto, hsaCardBaseInfo);
        sendService.openCardSendBatch(Collections.singletonList(messagesSendQO));
        refreshLabel(Collections.singletonList(dto.getMemberInfoGuid()));
        return hsaMemberInfoCard;
    }

    private static MessagesSendQO getMessagesSendQO(MemberCardOpenDTO dto, HsaCardBaseInfo hsaCardBaseInfo) {
        MessagesSendQO messagesSendQO = new MessagesSendQO();
        messagesSendQO.setMemberGuid(dto.getMemberInfoGuid());
        messagesSendQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        messagesSendQO.setEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid());
        messagesSendQO.setPhone(dto.getMemberPhoneNum());
        messagesSendQO.setCardBalance(Objects.nonNull(hsaCardBaseInfo.getCardValueMoney())
                ? hsaCardBaseInfo.getCardValueMoney() : BigDecimal.ZERO);
        return messagesSendQO;
    }

    private void refreshLabel(List<String> strings) {
        hsaLabelSettingService.refreshLabel(strings, null,
                BooleanEnum.FALSE.getCode(), null, LabelTriggerTypeEnum.IDENTITY_INFO.getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedissonLock(lockName = "BATCH_OPEN_ELECTRONIC_CARD", leaseTime = 10, tryLock = true)
    public String terminalOpenCard(MemberCardOpenDTO dto) {
        String guid = guidGeneratorUtil.getStringGuid(HsaElectronicCardService.class.getSimpleName());
        HsaElectronicCard hsaElectronicCard = appletBuildElectronicCard(guid, dto, null);
        CardInfoDetailDTO available = cardInfoMapper.getSelfFreeOpenCard(dto.getCardGuid());
        //保存数据
        saveBatchAndUpdateCount(Lists.newArrayList(hsaElectronicCard));
        HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(hsaElectronicCard.getCardGuid());
        //构建会员卡数据
        HsaMemberInfoCardDTO hsaMemberInfoCardDTO = buildMemberInfoCard(hsaElectronicCard, dto, hsaElectronicCard.getMemberInfoGuid(),
                available, LocalDateTime.now(), hsaCardBaseInfo);
        //查询是否绑定实体卡
        HsaMemberInfoCard hsaMemberInfoCard = memberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getCardGuid, dto.getCardGuid())
                .eq(HsaMemberInfoCard::getMemberInfoGuid, dto.getMemberInfoGuid()));
        String ownGuid;
        Map<String, BigDecimal> cardMoney = new HashMap<>();
        //设置卡余额
        cardMoney.put(dto.getCardGuid(), available.getCardValueMoney());
        //如果有实体卡 更新实体卡金额
        if (Objects.nonNull(hsaMemberInfoCard)) {
            hsaMemberInfoCardService.updateElectronicCardBind(toMemberCardBindDTO(hsaMemberInfoCardDTO, available, hsaElectronicCard));
            hsaMemberInfoCard = memberInfoCardMapper.queryByGuid(hsaMemberInfoCard.getGuid());
            addOpenCardBalanceRecord(Lists.newArrayList(hsaMemberInfoCard), cardMoney, dto.getSource(), CardTypeEnum.CARD_TYPE_EQUITY.getCode());
            ownGuid = hsaMemberInfoCard.getGuid();
        } else {
            List<HsaMemberInfoCardDTO> electronicCardList = Lists.newArrayList(hsaMemberInfoCardDTO);
            List<HsaMemberInfoCard> hsaMemberInfoCards = hsaMemberInfoCardService.batchSave(electronicCardList);
            addOpenCardBalanceRecord(hsaMemberInfoCards, cardMoney, SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode(),
                    CardTypeEnum.CARD_TYPE_EQUITY.getCode());
            saveStoreCardRule(electronicCardList);
            ownGuid = hsaMemberInfoCardDTO.getGuid();
        }
        refreshLabel(Collections.singletonList(dto.getMemberInfoGuid()));
        return ownGuid;
    }

    /**
     * 判断开卡权限
     *
     * @param available  卡详情信息
     * @param memberGuid 会员guid
     * @return 判断结果
     */
    private boolean judgeOpenCardScope(CardInfoDetailDTO available, String memberGuid) {
        if (Objects.isNull(available.getOpenCardScopeType())) {
            return false;
        }
        //0所有注册会员
        if (available.getOpenCardScopeType() == NumberConstant.NUMBER_0) {
            return true;
            //1仅满足条件会员
        } else if (available.getOpenCardScopeType() == NumberConstant.NUMBER_1) {
            if (StringUtils.isNotBlank(available.getOpenCardRegisterChannel())) {
                if (isaBoolean(available)) {
                    return false;
                }
                return available.getOpenCardScopeConditionType() == NUMBER_1;
            }

            if (!StringUtils.isEmpty(available.getOpenCardMemberLabelGuid())) {
                Map<String, HsaMemberLabel> labels = hsaMemberLabelMapper.selectList(new LambdaQueryWrapper<HsaMemberLabel>()
                                .eq(HsaMemberLabel::getOperationMemberInfoGuid, memberGuid))
                        .stream()
                        .collect(Collectors.toMap(HsaMemberLabel::getLabelSettingGuid, Function.identity(), (obj, obj1) -> obj));
                return forLabels(available, labels);
            }
            //指定会员
        } else {
            Integer count = memberCardRuleMapper.selectCount(new LambdaQueryWrapper<HsaMemberCardRule>()
                    .eq(HsaMemberCardRule::getOperationMemberInfoGuid, memberGuid)
                    .eq(HsaMemberCardRule::getCardOpenRuleGuid, available.getCardOpenRuleGuid()));
            return count > NumberConstant.NUMBER_0;
        }
        return true;
    }

    private static boolean isaBoolean(CardInfoDetailDTO available) {
        return !available.getOpenCardRegisterChannel().contains(com.holderzone.member.common.util.verify.ObjectUtil.
                objToString(ThreadLocalCache.getHeaderUserInfo().getSource()));
    }

    private static boolean forLabels(CardInfoDetailDTO available, Map<String, HsaMemberLabel> labels) {
        if (CollUtil.isNotEmpty(labels)) {
            List<String> labelGuid = JSON.parseArray(available.getOpenCardMemberLabelGuid(), String.class);
            for (String guid : labelGuid) {
                if (labels.containsKey(guid))
                    return true;
            }
        }
        return false;
    }

    @Override
    public List<ECardCompareExcelDTO> listAllECard(String operSubjectGuid) {
        if (StringUtils.isBlank(operSubjectGuid)) {
            return new ArrayList<>();
        }
        //通过企业和运营主体查询可用得会员卡
        return electronicCardMapper.listAllECard(operSubjectGuid);
    }

    @Override
    public void freezeOrThawCard(FreezeOrThawQO qo) {
        log.info("【冻结或者解冻电子卡】，参数：{}", qo);
        electronicCardMapper.freezeOrThawCard(qo.getOwnGuidList(), qo.getStatus());
    }

    @Override
    @RedissonLock(lockName = "OPEN_CARD_BY_BACKSTAGE", tryLock = true, leaseTime = 10)
    public void openCardByBackStage(String ownGuid) {
        //查询是否能够开卡
        MemberCardOpenDTO card = electronicCardMapper.findCanOpenCardByMemberCardGuid(ownGuid);
        if (ObjectUtil.isNull(card)) {
            throw new MemberBaseException(systemRoleHelper.getReplace(CardOperationExceptionEnum.MEMBER_CARD_NOT_SUPPORT,
                    ThreadLocalCache.getOperSubjectGuid()));
        }
        //保存电子卡
        HsaElectronicCard electronicCard = buildElectronicCard(guidGeneratorUtil.getStringGuid(HsaElectronicCardService.class.getSimpleName())
                , card.getCardGuid(), new MemberCardOpenDTO(), card.getMemberInfoGuid());
        this.save(electronicCard);

        //更新数量
        List<ECardCreateCountDTO> countList = new ArrayList<>();
        ECardCreateCountDTO count = new ECardCreateCountDTO();
        count.setCardGuid(electronicCard.getCardGuid());
        count.setCount(1);
        countList.add(count);
        openRuleService.updateElectronicCreateCount(countList);
        HsaMemberInfoCard hsaMemberInfoCard = memberInfoCardMapper.queryByGuid(ownGuid);
        HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(hsaMemberInfoCard.getCardGuid());
        List<HsaMemberInfoCard> list = Lists.newArrayList(hsaMemberInfoCard);
        Map<String, BigDecimal> moneyMap = new HashMap<>();
        moneyMap.put(hsaCardBaseInfo.getGuid(), hsaCardBaseInfo.getCardValueMoney());
        //增加消费明显
        addOpenCardBalanceRecord(list, moneyMap, SourceTypeEnum.ADD_BACKGROUND.getCode(),
                CardTypeEnum.CARD_TYPE_EQUITY.getCode());
    }

    private List<ExportOpenCardInfo> dealBatchOpen(List<MemberCardOpenDTO> cardGuidList, String memberGuid) {
        try {
            LocalDateTime openCardTime = LocalDateTime.now();
            List<MemberCardOpenDTO> notOpenCardList;  //不能开通的会员卡集合
            List<MemberCardOpenDTO> accessOpenCardList;  //成功开通的会员卡集合
            List<MemberCardOpenDTO> alreadyOpenCardList = new ArrayList<>();  //已开通的会员卡集合
            //判断是否可以开通电子卡
            List<CardInfoDetailDTO> availableList = hsaCardInfoMapper.getAvailableElectronicCard(
                    cardGuidList.stream().map(MemberCardOpenDTO::getCardGuid).collect(Collectors.toList()));
            List<String> collect = availableList.stream().map(CardInfoDetailDTO::getCardGuid).collect(Collectors.toList());
            notOpenCardList = cardGuidList.stream().filter(x -> !collect.contains(x.getCardGuid())).collect(Collectors.toList());
            cardGuidList = cardGuidList.stream().filter(obj -> collect.contains(obj.getCardGuid())).collect(Collectors.toList());
            accessOpenCardList = cardGuidList;  //可用开通的会员卡集合
            //如果没有可开通的会员卡
            if (ObjectUtil.isNull(cardGuidList) || cardGuidList.isEmpty()) {
                return exportHandler(memberGuid, notOpenCardList, null, null, openCardTime);  //处理当前用户开通失败的会员卡
            }

            //会员卡基础信息
            Map<String, CardInfoDetailDTO> cardInfo = availableList.stream().collect(Collectors.toMap(CardInfoDetailDTO::getCardGuid, Function.identity(), (entity1, entity2) -> entity1));
            //构建需要插入的电子卡信息
            List<HsaElectronicCard> electronicCardList = new ArrayList<>();
            List<HsaMemberInfoCardDTO> memberInfoCardList = new ArrayList<>();
            List<String> cardGuid = cardGuidList.stream().map(MemberCardOpenDTO::getCardGuid).collect(Collectors.toList());
            //超额相关
            Map<String, HsaCardBaseInfo> hsaCardBaseInfoMap = hsaCardBaseInfoMapper.selectList(new LambdaQueryWrapper<HsaCardBaseInfo>()
                            .in(HsaCardBaseInfo::getGuid, cardGuid))
                    .stream()
                    .collect(Collectors.toMap(HsaCardBaseInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));
            final List<String> guids = guidGeneratorUtil.getGuids(HsaElectronicCard.class.getSimpleName(), cardGuidList.size());
            int i = 0;
            for (MemberCardOpenDTO memberCard : cardGuidList) {
                HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMap.get(memberCard.getCardGuid());
                String guid = guids.get(i++);
                HsaElectronicCard hsaElectronicCard = buildElectronicCard(guid, memberCard.getCardGuid(), memberCard, memberGuid);
                electronicCardList.add(hsaElectronicCard);

                memberInfoCardList.add(buildMemberInfoCard(hsaElectronicCard, memberCard, memberGuid, cardInfo.get(memberCard.getCardGuid()), openCardTime, hsaCardBaseInfo));
            }
            //查询出已经绑定的会员卡
            List<MemberCardGuidDTO> memberCardGuidList = hsaMemberInfoCardService
                    .findCardGuidByGuidList(cardGuidList.stream().map(MemberCardOpenDTO::getCardGuid)
                            .distinct().collect(Collectors.toList()), memberGuid);
            List<String> updateGuid = memberCardGuidList.stream().filter(obj -> StringUtils.isNotEmpty(obj.getPhysicalCardGuid())
                            && StringUtils.isEmpty(obj.getElectronicCardGuid()))
                    .map(MemberCardGuidDTO::getCardGuid).collect(Collectors.toList());
            //解决重复开通电子卡问题，alreadyOpenCardGuids:用户已经开通了电子卡的cardGuid集合
            List<String> alreadyOpenCardGuids = memberCardGuidList.stream().filter(obj -> StringUtils.isNotBlank(obj.getElectronicCardGuid()))
                    .map(MemberCardGuidDTO::getCardGuid).collect(Collectors.toList());
            //需要更新的关系
            List<MemberCardBindDTO> updateCollect = memberInfoCardList.stream().filter(obj -> updateGuid.contains(obj.getCardGuid()))
                    .map(item -> buildMemberCardBindDTO(item, cardInfo))
                    .collect(Collectors.toList());
            //需要新增的关系
            List<HsaMemberInfoCardDTO> insertCollect = memberInfoCardList.stream()
                    .filter(obj -> {
                        if (updateGuid.contains(obj.getCardGuid())) {
                            return false;
                        }
                        if (!alreadyOpenCardGuids.contains(obj.getCardGuid())) {
                            return true;
                        }
                        MemberCardOpenDTO memberCard = new MemberCardOpenDTO();
                        memberCard.setCardGuid(obj.getCardGuid());
                        memberCard.setCardName(obj.getCardName());
                        memberCard.setMemberInfoGuid(obj.getMemberInfoGuid());
                        alreadyOpenCardList.add(memberCard);
                        return false;
                    }).collect(Collectors.toList());

            //将已经开通的会员卡去除
            electronicCardList = electronicCardList.stream().filter(x -> !alreadyOpenCardGuids.contains(x.getCardGuid())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(electronicCardList)) {
                //更新开通数量
                saveBatchAndUpdateCount(electronicCardList);
            }

            if (CollUtil.isNotEmpty(updateCollect)) {
                hsaMemberInfoCardService.updateElectronicCardBind(updateCollect);
            }
            if (CollUtil.isNotEmpty(insertCollect)) {
                //查询电子卡面额
                List<String> collectCardGuid = insertCollect.stream().map(HsaMemberInfoCardDTO::getCardGuid).collect(Collectors.toList());
                List<HsaCardBaseInfo> baseInfo = cardBaseInfoService.queryByGuids(collectCardGuid);
                Map<String, BigDecimal> cardMoney = baseInfo.stream().collect(Collectors.toMap(HsaCardBaseInfo::getGuid, HsaCardBaseInfo::getCardValueMoney));
                insertCollect.forEach(obj -> obj.setCardAmount(BigDecimalUtil.nonNullValue(cardMoney.get(obj.getCardGuid()))));
                List<HsaMemberInfoCard> hsaMemberInfoCards = hsaMemberInfoCardService.batchSave(insertCollect);
                addOpenCardBalanceRecord(hsaMemberInfoCards, cardMoney, SourceTypeEnum.ADD_BACKGROUND.getCode(),
                        CardTypeEnum.CARD_TYPE_EQUITY.getCode());
                saveStoreCardRule(insertCollect);
            }
            return exportHandler(memberGuid, notOpenCardList, accessOpenCardList, alreadyOpenCardList, openCardTime);
        } catch (Exception e) {
            e.printStackTrace();
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberInfoCardExceptionEnum.BATCH_OPEN_ELECTRONIC_CARD_FAIL, ThreadLocalCache.getOperSubjectGuid()));

        }
    }

    private MemberCardBindDTO buildMemberCardBindDTO(HsaMemberInfoCardDTO item, Map<String, CardInfoDetailDTO> cardInfo) {
        MemberCardBindDTO bindDTO = new MemberCardBindDTO();
        bindDTO.setCardGuid(item.getCardGuid());
        bindDTO.setGuid(item.getElectronicCardGuid());
        bindDTO.setMemberInfoGuid(item.getMemberInfoGuid());
        bindDTO.setCardValidity(cardInfo.get(item.getCardGuid()).getCardValidity() == 0 ? 0 : 1);
        if (ObjectUtil.equal(CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_DAY.getCode(),
                cardInfo.get(item.getCardGuid()).getCardValidity())) {
            //根据时间单位计算有效期
            bindDTO.setCardValidityDate(buildValidityDate(cardInfo.get(item.getCardGuid()).getValidityUnit(),
                    cardInfo.get(item.getCardGuid()).getCardValidityTime()));
        }
        if (ObjectUtil.equal(CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_YEAR.getCode(),
                cardInfo.get(item.getCardGuid()).getCardValidity())) {
            bindDTO.setCardValidityDate(cardInfo.get(item.getCardGuid()).getCardValidityDate());
        }
        bindDTO.setCardNum(item.getElectronicCardNum());
        bindDTO.setCardState(item.getElectronicCardState());
        bindDTO.setGmtCreate(Objects.isNull(item.getElectronicOpenTime()) ? LocalDateTime.now() : item.getElectronicOpenTime());
        bindDTO.setIsValue(true);
        return bindDTO;
    }


    @Override
    public void addOpenCardBalanceRecord(List<HsaMemberInfoCard> hsaMemberInfoCards, Map<String, BigDecimal> cardMoney,
                                         Integer source, Integer cardType) {
        List<HsaMemberFundingDetail> hsaMemberFundingDetails = Lists.newArrayList();
        for (HsaMemberInfoCard hsaMemberInfoCard : hsaMemberInfoCards) {
            //面值为0 不添加记录
            if (BigDecimalUtil.equelZero(cardMoney.get(hsaMemberInfoCard.getCardGuid()))) {
                continue;
            }
            HsaMemberFundingDetail hsaMemberFundingDetail = hsaMemberFundingDetailService.
                    addOpenCardBalanceRecord(hsaMemberInfoCard, cardMoney.get(hsaMemberInfoCard.getCardGuid()),
                            SourceTypeEnum.valueOfCode(source), cardType);
            hsaMemberFundingDetails.add(hsaMemberFundingDetail);
        }
        if (hsaMemberFundingDetails.size() == 0) {
            return;
        }
        hsaMemberFundingDetailService.saveBatch(hsaMemberFundingDetails);
    }


    /**
     * 处理需要导出开通会员卡是否成功的数据
     *
     * @param memberGuid          会员guid
     * @param notOpenCardList     不能开通的会员卡信息
     * @param accessOpenCard      可用开通的会员卡信息
     * @param alreadyOpenCardList 已开通的会员卡信息
     */
    private List<ExportOpenCardInfo> exportHandler(String memberGuid, List<MemberCardOpenDTO> notOpenCardList,
                                                   List<MemberCardOpenDTO> accessOpenCard,
                                                   List<MemberCardOpenDTO> alreadyOpenCardList, LocalDateTime openCardTime) {
        List<ExportOpenCardInfo> exportOpenCardInfoList = new ArrayList<>();
        if (CollUtil.isNotEmpty(accessOpenCard) && CollUtil.isNotEmpty(alreadyOpenCardList)) {
            List<String> collect = alreadyOpenCardList.stream().map(MemberCardOpenDTO::getCardGuid).collect(Collectors.toList());
            accessOpenCard = accessOpenCard.stream().filter(x -> !collect.contains(x.getCardGuid())).collect(Collectors.toList());
        }
        exportOpenCardInfoList.addAll(exportCardHandler(notOpenCardList, memberGuid, false, false, openCardTime));  //开卡失败数据
        exportOpenCardInfoList.addAll(exportCardHandler(accessOpenCard, memberGuid, true, false, openCardTime));    //开卡成功数据
        exportOpenCardInfoList.addAll(exportCardHandler(alreadyOpenCardList, memberGuid, true, true, openCardTime));//已经开通过的卡数据
        return exportOpenCardInfoList;
    }

    /**
     * 处理会员开卡是否成功，并返回数据
     *
     * @param OpenCardList 会员卡集合
     * @param memberGuid   会员guid
     * @param isAccess     判断开通会员卡是否成功
     * @param isOpen       判断开通的会员卡是否已经开通过了
     * @return 操作结果
     */
    private List<ExportOpenCardInfo> exportCardHandler(List<MemberCardOpenDTO> OpenCardList, String memberGuid,
                                                       boolean isAccess, boolean isOpen, LocalDateTime openCardTime) {
        List<ExportOpenCardInfo> exportOpenCardInfoList = new ArrayList<>();
        if (CollUtil.isNotEmpty(OpenCardList)) {
            final String memberPhone = getMemberPhone(memberGuid);

            for (MemberCardOpenDTO memberCardOpenDTO : OpenCardList) {
                ExportOpenCardInfo exportOpenCardInfo = new ExportOpenCardInfo();
                exportOpenCardInfo.setOpenTime(isAccess ? getOpenCardTime(memberCardOpenDTO.getCardGuid(), memberCardOpenDTO.getMemberInfoGuid(), isOpen, openCardTime) : null);
                exportOpenCardInfo.setMemberAccount(StringUtils.isEmpty(memberPhone) ? memberCardOpenDTO.getMemberPhoneNum() : memberPhone);
                exportOpenCardInfo.setMemberCardName(memberCardOpenDTO.getCardName());
                exportOpenCardInfo.setCardGuid(memberCardOpenDTO.getCardGuid());
                exportOpenCardInfo.setOpenStatus( //判断是否开通成功
                        isAccess ? MiniProgramOpenCardReturnEnum.OPEN_SUCCESS.getDes() : MiniProgramOpenCardReturnEnum.OPEN_ERROR.getDes());
                exportOpenCardInfoList.add(exportOpenCardInfo);
            }
        }
        return exportOpenCardInfoList;
    }

    /**
     * 获取开卡时间
     *
     * @param cardGuid       会员卡guid
     * @param memberInfoGuid 会员guid
     * @param isOpen         是否已经开通过当前会员卡
     * @return 开始时间
     */
    private String getOpenCardTime(String cardGuid, String memberInfoGuid, boolean isOpen, LocalDateTime openCardTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if (Boolean.FALSE.equals(isOpen)) {
            return formatter.format(openCardTime);
        }
        //如果已经开通过了会员卡，那么返回之前开卡时间
        HsaMemberInfoCard hsaMemberInfoCard = memberInfoCardMapper.selectOne(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getMemberInfoGuid, memberInfoGuid)
                .eq(HsaMemberInfoCard::getCardGuid, cardGuid));
        if (!ObjectUtil.isNull(hsaMemberInfoCard) && !ObjectUtil.isNull(hsaMemberInfoCard.getGmtCreate())) {
            return formatter.format(hsaMemberInfoCard.getGmtCreate());
        }
        return null;
    }

    /**
     * 获取会员手机号码
     *
     * @param memberInfoGuid
     * @return 会员手机号码
     */
    private String getMemberPhone(String memberInfoGuid) {
        HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.selectOne(
                new LambdaQueryWrapper<HsaOperationMemberInfo>()
                        .select(HsaOperationMemberInfo::getPhoneNum)
                        .eq(HsaOperationMemberInfo::getGuid, memberInfoGuid));
        return Optional.ofNullable(hsaOperationMemberInfo)
                .map(HsaOperationMemberInfo::getPhoneNum)
                .orElse("");
    }

    private List<MemberCardBindDTO> toMemberCardBindDTO(HsaMemberInfoCardDTO dto, CardInfoDetailDTO cardInfo,
                                                        HsaElectronicCard hsaElectronicCard) {
        MemberCardBindDTO bindDTO = new MemberCardBindDTO();
        bindDTO.setCardGuid(dto.getCardGuid());
        bindDTO.setGuid(dto.getElectronicCardGuid());
        bindDTO.setCardNum(hsaElectronicCard.getCardNum());
        bindDTO.setCardState(hsaElectronicCard.getCardState());
        bindDTO.setMemberInfoGuid(dto.getMemberInfoGuid());
        bindDTO.setCardValidity(cardInfo.getCardValidity() == 0 ? 0 : 1);
        if (ObjectUtil.equal(CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_DAY.getCode(), cardInfo.getCardValidity())) {
            //根据时间单位计算有效期
            bindDTO.setCardValidityDate(buildValidityDate(cardInfo.getValidityUnit(), cardInfo.getCardValidityTime()));
        }
        if (ObjectUtil.equal(CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_YEAR.getCode(), cardInfo.getCardValidity())) {
            bindDTO.setCardValidityDate(cardInfo.getCardValidityDate());
        }
        bindDTO.setGmtCreate(hsaElectronicCard.getGmtCreate());
        return Lists.newArrayList(bindDTO);
    }


    private HsaMemberInfoCardDTO buildMemberInfoCard(HsaElectronicCard hsaElectronicCard,
                                                     MemberCardOpenDTO memberCard,
                                                     String memberInfoGuid,
                                                     CardInfoDetailDTO cardBaseInfo,
                                                     LocalDateTime openCardTime,
                                                     HsaCardBaseInfo hsaCardBaseInfo) {
        //新增
        HsaMemberInfoCardDTO hsaMemberInfoCardDTO = new HsaMemberInfoCardDTO();
        hsaMemberInfoCardDTO.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberInfoCardService.class.getSimpleName()));
        hsaMemberInfoCardDTO.setCardName(Optional.ofNullable(memberCard.getCardName()).orElse(hsaCardBaseInfo.getCardName()));
        hsaMemberInfoCardDTO.setElectronicCardGuid(hsaElectronicCard.getGuid());
        hsaMemberInfoCardDTO.setCardGuid(memberCard.getCardGuid());
        hsaMemberInfoCardDTO.setMemberInfoGuid(memberInfoGuid);
        hsaMemberInfoCardDTO.setElectronicCardState(hsaElectronicCard.getCardState());
        if (!StringUtils.isEmpty(memberCard.getEnterpriseGuid())) {
            hsaMemberInfoCardDTO.setEnterpriseGuid(memberCard.getEnterpriseGuid());
        } else {
            hsaMemberInfoCardDTO.setEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid());
        }
        if (!StringUtils.isEmpty(memberCard.getOperSubjectGuid())) {
            hsaMemberInfoCardDTO.setOperSubjectGuid(memberCard.getOperSubjectGuid());
        } else {
            hsaMemberInfoCardDTO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        }
        hsaMemberInfoCardDTO.setElectronicCardNum(hsaElectronicCard.getCardNum());
        hsaMemberInfoCardDTO.setElectronicOpenTime(hsaElectronicCard.getGmtCreate());
        hsaMemberInfoCardDTO.setMemberPhoneNum(memberCard.getMemberPhoneNum());
        hsaMemberInfoCardDTO.setApplicableAllStore(cardBaseInfo.getApplicableAllStore());
        hsaMemberInfoCardDTO.setCardColor(cardBaseInfo.getCardColor());
        hsaMemberInfoCardDTO.setCardImage(cardBaseInfo.getCardImage());
        hsaMemberInfoCardDTO.setCardRechargeExplain(cardBaseInfo.getCardRechargeExplain());
        hsaMemberInfoCardDTO.setCardEmployExplain(cardBaseInfo.getCardEmployExplain());
        if (!StringUtils.isEmpty(memberCard.getPayPassword())) {
            hsaMemberInfoCardDTO.setCardPayPassword(SecureUtil.md5(memberCard.getPayPassword()).toUpperCase());
        } else {
            hsaMemberInfoCardDTO.setCardPayPassword(SecureUtil.md5(cardBaseInfo.getCardPayPassword()).toUpperCase());
        }
        hsaMemberInfoCardDTO.setElectronicOpenTime(openCardTime);
        hsaMemberInfoCardDTO.setIsPhysicalCardRetreat(cardBaseInfo.getIsPhysicalCardRetreat());
        //更新卡使用时间
        hsaMemberInfoCardDTO.setCardValidity(cardBaseInfo.getCardValidity() == 0 ? 0 : 1);
        if (ObjectUtil.equal(CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_DAY.getCode(), cardBaseInfo.getCardValidity())) {
            //根据时间单位计算有效期
            hsaMemberInfoCardDTO.setCardValidityDate(buildValidityDate(cardBaseInfo.getValidityUnit(), cardBaseInfo.getCardValidityTime()));
        }
        if (ObjectUtil.equal(CardValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_YEAR.getCode(), cardBaseInfo.getCardValidity())) {
            hsaMemberInfoCardDTO.setCardValidityDate(cardBaseInfo.getCardValidityDate());
        }
        hsaMemberInfoCardDTO.setCardAmount(cardBaseInfo.getCardValueMoney());
        //是否超额
        if (hsaCardBaseInfo.getIsExcess() == BooleanEnum.TRUE.getCode()) {
            if (hsaCardBaseInfo.getExcessType() == 0) {
                hsaMemberInfoCardDTO.setExcessTimes(hsaCardBaseInfo.getExcessMoney().intValue());
            } else {
                hsaMemberInfoCardDTO.setExcessAmount(hsaCardBaseInfo.getExcessMoney());
            }
        }
        return hsaMemberInfoCardDTO;
    }

    private LocalDate buildValidityDate(Integer validityUnit, Integer validityTime) {
        LocalDate now = LocalDate.now();
        if (ObjectUtil.equal(PeriodTypeEnum.PERIOD_DAY.getCode(), validityUnit)) {
            return now.plusDays(validityTime);
        }
        if (ObjectUtil.equal(PeriodTypeEnum.PERIOD_WEEK.getCode(), validityUnit)) {
            return now.plusWeeks(validityTime);
        }
        if (ObjectUtil.equal(PeriodTypeEnum.PERIOD_MONTH.getCode(), validityUnit)) {
            return now.plusMonths(validityTime);
        }
        if (ObjectUtil.equal(PeriodTypeEnum.PERIOD_YEAR.getCode(), validityUnit)) {
            return now.plusYears(validityTime);
        }
        return now;
    }

    private HsaElectronicCard buildElectronicCard(String guid, String cardGuid, MemberCardOpenDTO memberCardOpenDTO, String memberInfoGuid) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        Optional<HeaderUserInfo> UserInfo = Optional.ofNullable(headerUserInfo);
        HsaElectronicCard electronicCard = new HsaElectronicCard();
        electronicCard.setCardGuid(cardGuid);
        electronicCard.setCardNum(serialNumberBuilder.getSequence(null));
        electronicCard.setEnterpriseGuid(StringUtils.isNotBlank(memberCardOpenDTO.getEnterpriseGuid()) ? memberCardOpenDTO.getEnterpriseGuid() : ThreadLocalCache.getEnterpriseGuid());
        electronicCard.setOperSubjectGuid(StringUtils.isNotBlank(memberCardOpenDTO.getOperSubjectGuid()) ? memberCardOpenDTO.getOperSubjectGuid() : ThreadLocalCache.getOperSubjectGuid());
        electronicCard.setGuid(guid);
        electronicCard.setGmtCreate(LocalDateTime.now());
        electronicCard.setGmtModified(LocalDateTime.now());
        electronicCard.setIsDelete(BooleanEnum.FALSE.getCode());
        electronicCard.setMemberInfoGuid(memberInfoGuid);
        electronicCard.setSource(ObjectUtil.isNotNull(memberCardOpenDTO.getSource()) ? memberCardOpenDTO.getSource() : SourceTypeEnum.ADD_BACKGROUND.getCode());
        electronicCard.setOpenCardWay(ObjectUtil.isNotNull(memberCardOpenDTO.getElectronicOpenCardWay()) ? memberCardOpenDTO.getElectronicOpenCardWay() : ElectronicCardOpenWayEnum.SPECIFY_USER_OPEN.getCode());
        electronicCard.setOpenWayName(memberCardOpenDTO.getElectronicOpenCardWayName());
        electronicCard.setOpenWay(ObjectUtil.isNotNull(memberCardOpenDTO.getOpenWay()) ? memberCardOpenDTO.getOpenWay() : ElectronicCardOpenTypeEnum.DESIGNATED_OPEN.getCode());
        electronicCard.setOperationName(UserInfo.map(HeaderUserInfo::getUserName).orElse(""));
        electronicCard.setOperationPhone(UserInfo.map(HeaderUserInfo::getTel).orElse(""));
        electronicCard.setCardState(EletronicCardStateEnum.NORMAL.getCode());
        return electronicCard;
    }

    /**
     * 小程序构建电子卡
     *
     * @param guid 电子卡卡号
     * @param dto  批量开卡卡信息
     * @return 封装结果
     */
    private HsaElectronicCard appletBuildElectronicCard(String guid, MemberCardOpenDTO dto, HsaCardPayRecord hsaCardPayRecord) {
        HsaElectronicCard electronicCard = new HsaElectronicCard();
        electronicCard.setCardGuid(dto.getCardGuid());
        electronicCard.setCardNum(serialNumberBuilder.getSequence(null));
        electronicCard.setEnterpriseGuid(dto.getEnterpriseGuid());
        electronicCard.setOperSubjectGuid(dto.getOperSubjectGuid());
        electronicCard.setGuid(guid);
        electronicCard.setGmtModified(LocalDateTime.now());
        electronicCard.setGmtCreate(LocalDateTime.now());
        electronicCard.setIsDelete(BooleanEnum.FALSE.getCode());
        electronicCard.setMemberInfoGuid(dto.getMemberInfoGuid());
        electronicCard.setSource(dto.getSource());
        electronicCard.setOpenCardWay(ElectronicCardOpenWayEnum.INDEPENDENT_OPEN.getCode());
        electronicCard.setOpenWay(Optional.ofNullable(dto.getOpenWay()).orElse(ElectronicCardOpenTypeEnum.FREE_TO_ACTIVATE.getCode()));
        electronicCard.setCardState(EletronicCardStateEnum.NORMAL.getCode());
        if (Objects.nonNull(hsaCardPayRecord)) {
            electronicCard.setPaymentOrderNumber(hsaCardPayRecord.getOrderGuid());
            if (hsaCardPayRecord.getBusinessType() == NumberConstant.NUMBER_10) {
                electronicCard.setSelfRechargeMoney(hsaCardPayRecord.getPaymentMoney());
            } else {
                electronicCard.setSelfPaymentMoney(hsaCardPayRecord.getPaymentMoney());
            }
        }
        return electronicCard;
    }

    private void verify(OpenElectronicCardQO qo) {
        if (ObjectUtil.isNull(qo)) {
            throw new MemberBaseException(CardOperationExceptionEnum.CREATE_PHYSICAL_CARD_SECRET_PARAMS_NULL);
        }
        if (CollUtil.isEmpty(qo.getMemberInfoGuid())) {
            throw new MemberBaseException(systemRoleHelper.getReplace(CardOperationExceptionEnum.MEMBER_GUID_NULL,
                    ThreadLocalCache.getOperSubjectGuid()));
        }
        if (CollUtil.isEmpty(qo.getCardGuidList())) {
            throw new MemberBaseException(systemRoleHelper.getReplace(CardOperationExceptionEnum.OPEN_CARD_GUID_NULL,
                    ThreadLocalCache.getOperSubjectGuid()));
        }
    }

    /**
     * 根据标签开卡
     * 此接口仅用户注册以及外部导入会员
     *
     * @param memberInfoGuid 会员guid
     * @param sourceType     注册来源
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void openCardByMemberLabel(List<String> memberInfoGuid, Integer sourceType) {
        if (CollUtil.isEmpty(memberInfoGuid)) {
            return;
        }
        //会员信息
        List<HsaOperationMemberInfo> hsaOperationMemberInfoList = hsaOperationMemberInfoMapper.selectList(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .in(HsaOperationMemberInfo::getGuid, memberInfoGuid));

        Map<String, HsaOperationMemberInfo> hsaOperationMemberInfoMap = hsaOperationMemberInfoList.stream()
                .collect(Collectors.toMap(HsaOperationMemberInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));

        memberInfoGuid = hsaOperationMemberInfoList.stream().map(HsaOperationMemberInfo::getGuid).collect(Collectors.toList());

        //会员已开通的卡信息
        Map<String, List<HsaMemberInfoCard>> hsaMemberInfoCardMap = getMemberInfoCardMap(memberInfoGuid);

        //会员已开过的实体卡 需要校验此信息
        Map<String, List<HsaMemberInfoCard>> memberInfoCardMap = getInfoCardMap(memberInfoGuid);

        //会员关联标签
        List<HsaMemberLabel> hsaMemberLabelList = hsaMemberLabelMapper.selectList(new LambdaQueryWrapper<HsaMemberLabel>()
                .in(HsaMemberLabel::getOperationMemberInfoGuid, memberInfoGuid));
        Map<String, List<HsaMemberLabel>> memberLabelMap = hsaMemberLabelList.stream().collect(Collectors.groupingBy(HsaMemberLabel::getOperationMemberInfoGuid));

        //处理卡发放开始
        for (String memberGuid : memberInfoGuid) {
            //只开通电子卡
            List<MemberCardOpenDTO> memberCardOpenDTOList = Lists.newArrayList();
            Set<String> cardGuid = new HashSet<>();
            if (CollUtil.isNotEmpty(hsaMemberInfoCardMap) && hsaMemberInfoCardMap.containsKey(memberGuid)) {
                cardGuid = hsaMemberInfoCardMap.get(memberGuid).stream().map(HsaMemberInfoCard::getCardGuid).collect(Collectors.toSet());
            }
            //注册就开通的卡信息
            List<HsaCardOpenRule> hsaCardOpenRules = getCardOpenRules(hsaOperationMemberInfoList, cardGuid);

            if (CollUtil.isNotEmpty(hsaCardOpenRules) && (CollUtil.isNotEmpty(hsaCardOpenRules))) {
                List<String> cardGuidList = hsaCardOpenRules.stream().map(HsaCardOpenRule::getCardGuid).collect(Collectors.toList());
                //卡名称
                Map<String, String> hsaCardBaseNameMap = hsaCardBaseInfoMapper.selectList(new LambdaQueryWrapper<HsaCardBaseInfo>()
                                .in(HsaCardBaseInfo::getGuid, cardGuidList))
                        .stream()
                        .collect(Collectors.toMap(HsaCardBaseInfo::getGuid, HsaCardBaseInfo::getCardName, (entity1, entity2) -> entity1));

                //会员已开的标签信息
                Set<String> hsaMemberLabelGuidList = getMemberLabelGuidList(memberGuid, memberLabelMap);

                //若此用户已有此卡的实体卡则需合并
                Map<String, HsaMemberInfoCard> hsaMemberInfoCardHashMap = getMemberInfoCardHashMap(memberGuid, memberInfoCardMap);

                //批量处理发卡
                MemberCardSendRuleDTO memberCardSendRuleDTO = new MemberCardSendRuleDTO();
                memberCardSendRuleDTO.setSourceType(sourceType)
                        .setMemberGuid(memberGuid)
                        .setHsaCardOpenRules(hsaCardOpenRules)
                        .setHsaCardBaseNameMap(hsaCardBaseNameMap)
                        .setHsaMemberInfoCardHashMap(hsaMemberInfoCardHashMap)
                        .setHsaMemberLabelGuidList(hsaMemberLabelGuidList)
                        .setHsaOperationMemberInfoList(hsaOperationMemberInfoList)
                        .setHsaOperationMemberInfoMap(hsaOperationMemberInfoMap);
                forOpenMemberCard(memberCardSendRuleDTO, memberCardOpenDTOList);

            }
            if (CollUtil.isNotEmpty(memberCardOpenDTOList)) {
                openElectronicCard(memberGuid, memberCardOpenDTOList);
            }
        }
    }

    private void forOpenMemberCard(MemberCardSendRuleDTO memberCardSendRuleDTO,
                                   List<MemberCardOpenDTO> memberCardOpenDTOList) {
        Map<String, String> hsaCardBaseNameMap = memberCardSendRuleDTO.getHsaCardBaseNameMap();
        Map<String, HsaMemberInfoCard> hsaMemberInfoCardHashMap = memberCardSendRuleDTO.getHsaMemberInfoCardHashMap();
        String memberGuid = memberCardSendRuleDTO.getMemberGuid();
        for (HsaCardOpenRule hsaCardOpenRule : memberCardSendRuleDTO.getHsaCardOpenRules()) {
            if (hsaCardBaseNameMap.containsKey(hsaCardOpenRule.getCardGuid())) {
                String cardName = hsaCardBaseNameMap.get(hsaCardOpenRule.getCardGuid());
                //若此用户已有此卡的实体卡则需合并
                if (isaBoolean(hsaMemberInfoCardHashMap, hsaCardOpenRule)) {
                    //若支持电子卡  则开通电子卡
                    checkSupportElectronicCard(memberGuid, hsaMemberInfoCardHashMap, hsaCardOpenRule);
                } else {
                    //只卡电子卡
                    if (Boolean.TRUE.equals(checkOpenCard(hsaCardOpenRule, memberCardSendRuleDTO.getSourceType(), memberCardSendRuleDTO.getHsaMemberLabelGuidList()))) {
                        MemberCardOpenDTO dto = new MemberCardOpenDTO();
                        dto.setCardGuid(String.valueOf(hsaCardOpenRule.getCardGuid()));
                        dto.setCardName(cardName);
                        dto.setElectronicOpenCardWay(ElectronicCardOpenWayEnum.EGISTRATION_COMPLETE_AUTO.getCode());
                        dto.setOpenWay(ElectronicCardOpenTypeEnum.RECHARGE_OPENING.getCode());
                        dto.setSource(memberCardSendRuleDTO.getSourceType());
                        dto.setEnterpriseGuid(memberCardSendRuleDTO.getHsaOperationMemberInfoList().get(0).getEnterpriseGuid());
                        dto.setOperSubjectGuid(memberCardSendRuleDTO.getHsaOperationMemberInfoList().get(0).getOperSubjectGuid());
                        dto.setMemberPhoneNum(memberCardSendRuleDTO.getHsaOperationMemberInfoMap().get(memberGuid).getPhoneNum());
                        memberCardOpenDTOList.add(dto);
                    }
                }
            }
        }
    }

    private void checkSupportElectronicCard(String memberGuid, Map<String, HsaMemberInfoCard> hsaMemberInfoCardHashMap, HsaCardOpenRule hsaCardOpenRule) {
        HsaMemberInfoCard hsaMemberInfoCard = hsaMemberInfoCardHashMap.get(hsaCardOpenRule.getCardGuid());
        if (hsaCardOpenRule.getIsSupportElectronicCard() == BooleanEnum.TRUE.getCode()) {
            //直接开通电子卡
            hsaMemberInfoCardService.openElectronic(hsaMemberInfoCard.getGuid(), memberGuid, BooleanEnum.TRUE.getCode());
        }
    }

    private static boolean isaBoolean(Map<String, HsaMemberInfoCard> hsaMemberInfoCardHashMap, HsaCardOpenRule hsaCardOpenRule) {
        return CollUtil.isNotEmpty(hsaMemberInfoCardHashMap)
                && hsaMemberInfoCardHashMap.containsKey(hsaCardOpenRule.getCardGuid());
    }

    private static Map<String, HsaMemberInfoCard> getMemberInfoCardHashMap(String memberGuid, Map<String, List<HsaMemberInfoCard>> memberInfoCardMap) {
        Map<String, HsaMemberInfoCard> hsaMemberInfoCardHashMap = new HashMap<>();
        if (CollUtil.isNotEmpty(memberInfoCardMap) && memberInfoCardMap.containsKey(memberGuid)) {
            hsaMemberInfoCardHashMap = memberInfoCardMap.get(memberGuid)
                    .stream()
                    .collect(Collectors.toMap(HsaMemberInfoCard::getCardGuid, Function.identity(), (entity1, entity2) -> entity1));
        }
        return hsaMemberInfoCardHashMap;
    }

    private static Set<String> getMemberLabelGuidList(String memberGuid, Map<String, List<HsaMemberLabel>> memberLabelMap) {
        Set<String> hsaMemberLabelGuidList = new HashSet<>();
        if (CollUtil.isNotEmpty(memberLabelMap) && memberLabelMap.containsKey(memberGuid)) {
            hsaMemberLabelGuidList = memberLabelMap.get(memberGuid).stream().map(HsaMemberLabel::getLabelSettingGuid).collect(Collectors.toSet());
        }
        return hsaMemberLabelGuidList;
    }

    private List<HsaCardOpenRule> getCardOpenRules(List<HsaOperationMemberInfo> hsaOperationMemberInfoList, Set<String> cardGuid) {
        return hsaCardOpenRuleMapper.selectList(new LambdaQueryWrapper<HsaCardOpenRule>()
                .eq(HsaCardOpenRule::getOperSubjectGuid, hsaOperationMemberInfoList.get(0).getOperSubjectGuid())
                .eq(HsaCardOpenRule::getIsSupportElectronicCard, BooleanEnum.TRUE.getCode())
                .eq(HsaCardOpenRule::getElectronicOpenWay, ElectronicOpenWayEnum.REGISTER_OPEN.getCode())
                .eq(HsaCardOpenRule::getSendStatus, SendCardStateEnum.CARD_STATE_START.getCode())
                .notIn(CollUtil.isNotEmpty(cardGuid), HsaCardOpenRule::getCardGuid, cardGuid));
    }

    private Map<String, List<HsaMemberInfoCard>> getInfoCardMap(List<String> memberInfoGuid) {
        return memberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                        .in(HsaMemberInfoCard::getMemberInfoGuid, memberInfoGuid)
                        .isNull(HsaMemberInfoCard::getElectronicCardGuid)
                        .isNotNull(HsaMemberInfoCard::getPhysicalCardGuid))
                .stream()
                .collect(Collectors.groupingBy(HsaMemberInfoCard::getMemberInfoGuid));
    }

    private Map<String, List<HsaMemberInfoCard>> getMemberInfoCardMap(List<String> memberInfoGuid) {
        return memberInfoCardMapper.selectList(new LambdaQueryWrapper<HsaMemberInfoCard>()
                        .in(HsaMemberInfoCard::getMemberInfoGuid, memberInfoGuid))
                .stream()
                .collect(Collectors.groupingBy(HsaMemberInfoCard::getMemberInfoGuid));
    }

    public void openElectronicCard(String memberGuid, List<MemberCardOpenDTO> cardGuidList) {
        if (CollUtil.isEmpty(cardGuidList)) {
            return;
        }
        //构建传递参数
        OpenElectronicCardQO qo = new OpenElectronicCardQO();
        qo.setCardGuidList(cardGuidList);
        qo.setMemberInfoGuid(Collections.singletonList(String.valueOf(memberGuid)));
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        memberBaseThreadExecutor.execute(() -> {
            try {
                ThreadLocalCache.put(JSON.toJSONString(headerUserInfo));
                batchOpen(qo);
            } catch (Exception e) {
                log.error(MemberInfoCardExceptionEnum.OPEN_ELECTRONIC_CARD_FAIL.getDes());
            }
        });

    }

    /**
     * 电子开卡时效、限量校验
     *
     * @param hsaCardOpenRule hsaCardOpenRule
     * @return Boolean
     */
    public Boolean checkOpenCard(HsaCardOpenRule hsaCardOpenRule, Integer sourceType, Set<String> hsaMemberLabelList) {
        LocalDateTime now = LocalDateTime.now();
        if (isaSendCountLimit(hsaCardOpenRule)) {
            return true;

        } else if (isOpenCardStartTime(hsaCardOpenRule, now)) {
            return checkOpenCardScopeType(hsaCardOpenRule, sourceType, hsaMemberLabelList);

        } else if (isSurplusSendOpenCountLimit(hsaCardOpenRule)) {
            return checkOpenCardScopeType(hsaCardOpenRule, sourceType, hsaMemberLabelList);

        } else if (isSendCountLimit(hsaCardOpenRule)) {
            return checkOpenCardScopeType(hsaCardOpenRule, sourceType, hsaMemberLabelList);

        } else if (isOpenCardTimeType(hsaCardOpenRule)) {
            return checkOpenCardScopeType(hsaCardOpenRule, sourceType, hsaMemberLabelList);

        }
        return false;
    }

    private static boolean isOpenCardTimeType(HsaCardOpenRule hsaCardOpenRule) {
        return hsaCardOpenRule.getSendCountLimit() == BooleanEnum.TRUE.getCode() && hsaCardOpenRule.getOpenCardTimeType() == 0
                && hsaCardOpenRule.getSurplusSendOpenCountLimit() > 0;
    }

    private static boolean isSendCountLimit(HsaCardOpenRule hsaCardOpenRule) {
        return hsaCardOpenRule.getSendCountLimit() == BooleanEnum.FALSE.getCode() && hsaCardOpenRule.getOpenCardTimeType() == 0;
    }

    private static boolean isSurplusSendOpenCountLimit(HsaCardOpenRule hsaCardOpenRule) {
        return hsaCardOpenRule.getSendCountLimit() == BooleanEnum.TRUE.getCode() && hsaCardOpenRule.getOpenCardTimeType() == 1
                && hsaCardOpenRule.getSurplusSendOpenCountLimit() > 0;
    }

    private static boolean isOpenCardStartTime(HsaCardOpenRule hsaCardOpenRule, LocalDateTime now) {
        return hsaCardOpenRule.getSendCountLimit() == BooleanEnum.FALSE.getCode() && hsaCardOpenRule.getOpenCardTimeType() == 1
                && now.isAfter(hsaCardOpenRule.getOpenCardStartTime()) && now.isBefore(hsaCardOpenRule.getOpenCardEndTime());
    }

    private static boolean isaSendCountLimit(HsaCardOpenRule hsaCardOpenRule) {
        return hsaCardOpenRule.getSendCountLimit() == BooleanEnum.FALSE.getCode() && hsaCardOpenRule.getOpenCardTimeType() == 0
                && hsaCardOpenRule.getOpenCardScopeType() == 0;
    }

    /**
     * 电子开卡范围满足条件校验
     *
     * @param hsaCardOpenRule hsaCardOpenRule
     * @param sourceType      sourceType
     * @return
     */
    private boolean checkOpenCardScopeType(HsaCardOpenRule hsaCardOpenRule, Integer sourceType, Set<String> hsaMemberLabelList) {
        if (hsaCardOpenRule.getOpenCardScopeType() == 1) {
            List<String> channel = Lists.newArrayList();
            List<String> labels = Lists.newArrayList();
            //注册来源
            channel = getChannel(hsaCardOpenRule, channel);
            //标签
            labels = getLabel(hsaCardOpenRule, labels);
            if (hsaCardOpenRule.getOpenCardScopeConditionType() == 0) {
                if (CollUtil.isNotEmpty(channel) && (!channel.contains(String.valueOf(sourceType)))) {
                    return false;

                }
                return !CollUtil.isNotEmpty(labels) || (!Collections.disjoint(labels, hsaMemberLabelList));
            } else {
                return channel.contains(String.valueOf(sourceType)) || !Collections.disjoint(labels, hsaMemberLabelList);
            }
        } else {
            return true;
        }
    }

    private List<String> getLabel(HsaCardOpenRule hsaCardOpenRule, List<String> labels) {
        if (!org.springframework.util.StringUtils.isEmpty(hsaCardOpenRule.getOpenCardMemberLabelGuid())) {
            labels = JSON.parseArray(hsaCardOpenRule.getOpenCardMemberLabelGuid(), String.class);
        }
        return labels;
    }

    private List<String> getChannel(HsaCardOpenRule hsaCardOpenRule, List<String> channel) {
        if (!org.springframework.util.StringUtils.isEmpty(hsaCardOpenRule.getOpenCardRegisterChannel())) {
            channel = JSON.parseArray(hsaCardOpenRule.getOpenCardRegisterChannel(), String.class);
        }
        return channel;
    }
}
