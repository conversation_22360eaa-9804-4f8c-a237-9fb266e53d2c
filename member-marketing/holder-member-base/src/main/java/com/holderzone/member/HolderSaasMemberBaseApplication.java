package com.holderzone.member;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@ComponentScans(value = {
        @ComponentScan("com.holderzone.member.common.*"),
        @ComponentScan("com.holderzone.member.base.handler"),
        @ComponentScan("com.holderzone.member.base.config"),
        @ComponentScan("com.holderzone.member.base.event.listener"),
        @ComponentScan("com.holderzone.member.common.client"),
        @ComponentScan("com.holderzone.member.common.module.settlement.apply"),
        @ComponentScan("com.holderzone.member.common.module.settlement.apply")
})
@MapperScan(basePackages = "com.holderzone.member.base.mapper")
@EnableAsync
@EnableFeignClients(basePackages = "com.holderzone.member.common.feign")
public class HolderSaasMemberBaseApplication {

    public static void main(String[] args) {
        SpringApplication.run(HolderSaasMemberBaseApplication.class, args);
    }

}
