package com.holderzone.member.base.service.growth;


import com.holderzone.member.base.entity.grade.HsaGradeGiftBag;
import com.holderzone.member.base.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.base.entity.growth.HsaGrowthValueDetail;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.dto.growth.HsaGrowthValueDetailDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.growth.GrowthValueDetailMemberQO;
import com.holderzone.member.common.qo.growth.GrowthValueDetailQO;
import com.holderzone.member.common.qo.growth.GrowthValueDetailRequest;
import com.holderzone.member.common.vo.excel.ExcelGrowthValueDetailVO;
import com.holderzone.member.common.vo.growth.GrowthLevelVO;
import com.holderzone.member.common.vo.growth.GrowthValueDetailMemberVO;

import java.util.List;

/**
 * <p>
 * 成长值明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-23
 */
public interface HsaGrowthValueDetailService extends IHolderBaseService<HsaGrowthValueDetail> {

    /**
     * 功能描述：会员账户查询成长值列表
     *
     * @param qo 会员账户查询成长值参数
     * @return com.holderzone.member.common.dto.page.PageResult
     * @date 2021/11/23
     */
    PageResult<GrowthValueDetailMemberVO> listMemberGrowth(GrowthValueDetailMemberQO qo);

    /**
     * 功能描述：统计失效的成长值
     *
     * @param guid     会员guid
     * @param roleType 角色类型
     * @return com.holderzone.member.common.vo.growth.GrowthLevelVO
     * @date 2021/11/23
     */
    GrowthLevelVO memberGrowthLevel(String guid, String roleType);

    /**
     * 获取成长值明细记录列表
     *
     * @param request 筛选的请求参数
     * @return 明细记录列表
     */
    PageResult getGrowthValueDetailList(GrowthValueDetailRequest request);

    /**
     * 功能描述：导出会员账户成长值列表
     *
     * @param qo 查询参数
     * @date 2021/11/24
     */
    void exportMemberGrowth(GrowthValueDetailMemberQO qo);

    /**
     * 导出当前列表所有筛选结果数据
     *
     * @param request 高级筛选请求参数
     */
    void exportGrowthValueDetailList(GrowthValueDetailRequest request);

    List<ExcelGrowthValueDetailVO> queryExcelGrowthValueData(GrowthValueDetailRequest request);

    /**
     * 功能描述：
     *
     * @param id             任务id
     * @param memberInfoGuid 会员guid
     * @param code           任务动作
     * @return com.holderzone.member.common.dto.growth.HsaGrowthValueDetailDTO
     * @date 2021/11/24
     */
    HsaGrowthValueDetailDTO getGrowthValueDetail(Long id, String memberInfoGuid, Integer code);

    /**
     * 保存成长值明细
     *
     * @param operationMemberInfo
     * @param sourceType
     * @param operSubjectGuid
     * @param hsaGradeGiftBag
     * @param hsaMemberGradeInfo
     */
    void saveHsaGrowthValueDetail(HsaOperationMemberInfo operationMemberInfo, Integer sourceType, String operSubjectGuid, HsaGradeGiftBag hsaGradeGiftBag, HsaMemberGradeInfo hsaMemberGradeInfo, List<HsaMemberGradeInfo> hsaMemberGradeInfoList);


    /**
     * 好搭档保存成长值明细
     */
    void savePartnerGrowthValueDetail(GrowthValueDetailQO growthValueDetailQO);
}
