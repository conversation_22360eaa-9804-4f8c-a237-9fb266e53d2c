package com.holderzone.member.base.dto;

import com.holderzone.member.base.entity.card.HsaCardOpenRule;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;
@Accessors(chain = true)
@Data
public class MemberCardSendRuleDTO implements Serializable {

    /**
     * 开卡来源
     */
    private Integer sourceType;

    /**
     * 会员guid
     */
    private String memberGuid;

    /**
     * 会员卡开卡规则
     */
    private List<HsaCardOpenRule> hsaCardOpenRules;

    /**
     * 会员卡guid:名称
     */
    private Map<String, String> hsaCardBaseNameMap;

    /**
     * 会员卡会员信息
     */
    private Map<String, HsaMemberInfoCard> hsaMemberInfoCardHashMap;

    /**
     * 会员标签
     */
    private Set<String> hsaMemberLabelGuidList;

    /**
     * 会员信息
     */
    private List<HsaOperationMemberInfo> hsaOperationMemberInfoList;

    /**
     * 会员信息 会员guid:会员信息
     */
    private Map<String, HsaOperationMemberInfo> hsaOperationMemberInfoMap;
}
