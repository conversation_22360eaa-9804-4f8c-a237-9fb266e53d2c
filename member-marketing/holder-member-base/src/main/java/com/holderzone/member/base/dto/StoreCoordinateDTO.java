package com.holderzone.member.base.dto;

/**
 * 门店坐标信息DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class StoreCoordinateDTO {
    
    /**
     * 纬度
     */
    private final String latitude;
    
    /**
     * 经度
     */
    private final String longitude;

    /**
     * 构造函数
     * 
     * @param latitude 纬度
     * @param longitude 经度
     */
    public StoreCoordinateDTO(String latitude, String longitude) {
        this.latitude = latitude;
        this.longitude = longitude;
    }

    /**
     * 获取纬度
     * 
     * @return 纬度
     */
    public String getLatitude() {
        return latitude;
    }

    /**
     * 获取经度
     * 
     * @return 经度
     */
    public String getLongitude() {
        return longitude;
    }

    @Override
    public String toString() {
        return "StoreCoordinateDTO{" +
                "latitude='" + latitude + '\'' +
                ", longitude='" + longitude + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        StoreCoordinateDTO that = (StoreCoordinateDTO) o;
        
        if (latitude != null ? !latitude.equals(that.latitude) : that.latitude != null) return false;
        return longitude != null ? longitude.equals(that.longitude) : that.longitude == null;
    }

    @Override
    public int hashCode() {
        int result = latitude != null ? latitude.hashCode() : 0;
        result = 31 * result + (longitude != null ? longitude.hashCode() : 0);
        return result;
    }
} 