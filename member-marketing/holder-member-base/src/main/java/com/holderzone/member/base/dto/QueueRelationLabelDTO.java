package com.holderzone.member.base.dto;

import com.holderzone.member.base.entity.member.HsaLabelSetting;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-05-25 17:18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueueRelationLabelDTO {

    private HsaLabelSetting labelSetting;

    private HeaderUserInfo headerUserInfo;

    private List<String> memberGuid;

    private Integer sourceType;

    private Integer triggerType;
}
