package com.holderzone.member.base.dto;

import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.grade.HsaBusinessEquities;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 会员卡权益赠送业务
 */
@Data
@Accessors(chain = true)
public class CardGrantEquitiesDTO implements Serializable {

    /**
     * 权益数据
     */
    private List<HsaBusinessEquities> hsaBusinessEquitiesList;

    /**
     * 会员卡guid
     */
    private String cardGuid;


    /**
     * 会员持卡guid
     */
    private List<HsaMemberInfoCard> hsaMemberInfoCardList;


}
