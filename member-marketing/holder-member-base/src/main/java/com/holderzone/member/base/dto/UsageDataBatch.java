package com.holderzone.member.base.dto;

import com.holderzone.member.common.entity.coupon.HsaMemberCouponUse;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 使用数据批量处理结果
 * 
 * <AUTHOR>
 * @date 2024
 */
@Data
public class UsageDataBatch {
    /**
     * 已使用记录映射 (couponGuid -> 使用记录列表)
     */
    private final Map<String, List<HsaMemberCouponUse>> usedRecordsMap;
    
    /**
     * 已占用次数映射 (couponCode -> 占用次数)
     */
    private final Map<String, Integer> occupiedCountsMap;
    
    public UsageDataBatch(Map<String, List<HsaMemberCouponUse>> usedRecordsMap, 
                         Map<String, Integer> occupiedCountsMap) {
        this.usedRecordsMap = usedRecordsMap != null ? usedRecordsMap : Collections.emptyMap();
        this.occupiedCountsMap = occupiedCountsMap != null ? occupiedCountsMap : Collections.emptyMap();
    }
} 