package com.holderzone.member.base.dto;

import com.holderzone.member.base.entity.member.HsaMemberOrderDiscount;
import com.holderzone.member.common.qo.card.OrderCommodityQO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 下单锁定优惠
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Data
@Accessors(chain = true)
public class SettlementBackDiscountDTO implements Serializable {


    private static final long serialVersionUID = -5239525768641874058L;

    /**
     * 主体
     */
    @ApiModelProperty("主体guid")
    private String operSubjectGuid;

    /**
     * 会员 guid
     */
    @ApiModelProperty("会员guid")
    private String memberGuid;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 0 部分退款 1 整单退款
     */
    @ApiModelProperty(value = "0 部分退款 1 整单退款")
    private Integer refundType;

    /**
     * 部分退款总金额  为空默认全额
     */
    @ApiModelProperty(value = "部分退款金额  为空默认全额")
    private BigDecimal refundAmount;

    /**
     * 优惠退款金额
     */
    @ApiModelProperty(value = "优惠退款金额")
    private BigDecimal discountAmount;

    /**
     * 积分是否可退回  0 不可退 1 可退
     */
    @ApiModelProperty(value = "积分是否可退回  0 不可退 1 可退  ")
    private Integer isIntegralBack;

    /**
     * 需要退款的商品详细
     */
    @ApiModelProperty(value = "需要退款的商品详细")
    private List<OrderCommodityQO> orderCommodityQOList;

    /**
     * 回退的优惠
     */
    @ApiModelProperty("回退的优惠")
    private Map<Integer, List<HsaMemberOrderDiscount>> orderDiscountByOptionMap;
    
}
