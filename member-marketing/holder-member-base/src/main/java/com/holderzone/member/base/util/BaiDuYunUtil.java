package com.holderzone.member.base.util;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.dto.member.BaiduFaceEntryDTO;
import com.holderzone.member.common.util.HttpsClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RefreshScope
public class BaiDuYunUtil {


    /**
     * 百度云人脸识别access_token的url地址
     */
    private static final String BAIDU_FACE_TOKEN_URL = "https://aip.baidubce.com/oauth/2.0/token";

    /**
     * 百度云人脸删除请求的url地址
     */
    private static final String FACE_DELETE_URL = "https://aip.baidubce.com/rest/2.0/face/v3/faceset/face/delete?access_token=%s";

    /**
     * 百度云人脸录入请求的url地址
     */
    private static final String FACE_ENTRY_URL = "https://aip.baidubce.com/rest/2.0/face/v3/faceset/user/add?access_token=%s";

    /**
     * 百度云人脸更新请求的url地址
     */
    private static final String FACE_UPDATE_URL = "https://aip.baidubce.com/rest/2.0/face/v3/faceset/user/update?access_token=%s";

    /**
     * 获取百度云人脸识别ACCESS_TOKEN
     */
    private static final String BAIDU_FACE_ACCESS_TOKEN_KEY = "BaiDuFaceAccessToken:%s";

    private static final String GROUP_ID = "group_id";

    private static final String USER_ID = "user_id";

    /**
     * 百度云clientId
     */
    @Value("${baidu.clientId}")
    private String clientId;

    /**
     * 百度云clientSecret
     */
    @Value("${baidu.clientSecret}")
    private String clientSecret;

    @Resource
    private StringRedisTemplate stringredisTemplate;

    /**
     * 获取微信基础ACCESS TOKEN
     *
     * @return 返回access_token
     */
    public String getBaiduAccessToken() {
        return getBaiduAccessToken(clientId, clientSecret);
    }

    public String getBaiduAccessToken(String clientId, String clientSecret) {
        String accessToken = stringredisTemplate.opsForValue().get(String.format(BAIDU_FACE_ACCESS_TOKEN_KEY, clientId));

        if (StringUtils.isEmpty(accessToken)) {
            String result = "";
            try {
                HashMap<String, Object> paramMap = Maps.newHashMap();
                paramMap.put("grant_type", "client_credentials");
                paramMap.put("client_id", clientId);
                paramMap.put("client_secret", clientSecret);
                result = HttpsClientUtils.doGet(BAIDU_FACE_TOKEN_URL, paramMap);
                log.info("获取百度云人脸识别access_token信息:{} ", result);
                Map<String, Object> accessTokenMap = JSON.parseObject(result, Map.class);
                accessToken = accessTokenMap.get("access_token").toString();
                // 百度云access_token失效时间有30天
                stringredisTemplate.opsForValue().set(String.format(BAIDU_FACE_ACCESS_TOKEN_KEY, clientId), accessToken, 2591000, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("获取百度云人脸识别access_token 失败: {} result:{}", e.getMessage(), result);
            }
        }
        return accessToken;
    }

    /**
     * 删除人脸识别库中的人脸信息
     *
     * @param logId     人脸识别（请求标识码，随机数，唯一）
     * @param userId    人脸识别（用户id）
     * @param groupId   人脸识别（用户分组id）
     * @param faceToken 人脸识别（图片的唯一表示）
     */
    public void faceToDelete(String logId, String userId, String groupId, String faceToken) {
        String response = "";
        try {
            String accessToken = this.getBaiduAccessToken();
            HashMap<String, Object> paramMap = Maps.newHashMap();
//            paramMap.put("access_token", accessToken);
            paramMap.put("log_id", logId);
            paramMap.put(USER_ID, userId);
            paramMap.put(GROUP_ID, groupId);
            paramMap.put("face_token", faceToken);
            response = HttpsClientUtils.doGet(String.format(FACE_DELETE_URL, accessToken), paramMap);
            log.info("response={}", response);
            Map<String, Object> accessTokenMap = JSON.parseObject(response, Map.class);
            int errorCode = Integer.parseInt(accessTokenMap.get("error_code").toString());

            requestError(response, errorCode);
        } catch (Exception e) {
            log.error("百度云 删除人脸识别库中的指定用户报错:{} response:{} ", e.getMessage(), response);
        }
    }

    private void requestError(String response, int errorCode) {
        if (NumberConstant.NUMBER_110 == errorCode || NumberConstant.NUMBER_111 == errorCode) {
            log.error("百度云 token无效或者过期");
            stringredisTemplate.delete(String.format(BAIDU_FACE_ACCESS_TOKEN_KEY, clientId));
        }
        if (NumberConstant.NUMBER_0 != errorCode) {
            log.error("百度云 人脸识别请求报错:{} ", JSON.toJSONString(response));
        }
    }

    /**
     * 人脸录入
     *
     * @param userId  用户id
     * @param groupId 用户分组
     * @param image   人脸录入的图片信息
     */
    public BaiduFaceEntryDTO faceEntry(String userId, String groupId, String image) {
        BaiduFaceEntryDTO baiduFaceEntryDTO = new BaiduFaceEntryDTO();
        String response = "";
        try {
            String accessToken = this.getBaiduAccessToken();
            HashMap<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("liveness_control", "HIGH");
            paramMap.put("quality_control", "HIGH");
            paramMap.put("face_sort_type", "1");
            paramMap.put("image", image);
            paramMap.put("image_type", "BASE64");
            paramMap.put(GROUP_ID, groupId);
            paramMap.put(USER_ID, userId);
            paramMap.put("action_type", "REPLACE");
            response = HttpsClientUtils.doPostJSON(String.format(FACE_ENTRY_URL, accessToken), paramMap);
            log.info("response={}", response);
            baiduFaceEntryDTO = JSON.parseObject(response, BaiduFaceEntryDTO.class);
            assert baiduFaceEntryDTO != null;
            int errorCode = baiduFaceEntryDTO.getErrorCode();

            requestError(response, errorCode);
            return baiduFaceEntryDTO;
        } catch (Exception e) {
            log.error("百度云 录入人脸失败:{} response:{} ", e.getMessage(), response);
        }
        return baiduFaceEntryDTO;
    }

    /**
     * 人脸更新
     *
     * @param userId  用户id
     * @param groupId 用户分组
     * @param image   人脸录入的图片信息
     */
    public BaiduFaceEntryDTO faceUpdate(String userId, String groupId, String image) {
        BaiduFaceEntryDTO baiduFaceEntryDTO = new BaiduFaceEntryDTO();
        String response = "";
        try {
            String accessToken = this.getBaiduAccessToken();
            HashMap<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("image", image);
            paramMap.put("image_type", "BASE64");
            paramMap.put(GROUP_ID, groupId);
            paramMap.put(USER_ID, userId);
            paramMap.put("liveness_control", "HIGH");
            paramMap.put("quality_control", "HIGH");
            paramMap.put("action_type", "UPDATE");
            response = HttpsClientUtils.doPostJSON(String.format(FACE_UPDATE_URL, accessToken), paramMap);
            log.info("百度云[人脸更新]返回 response={}", response);
            baiduFaceEntryDTO = JSON.parseObject(response, BaiduFaceEntryDTO.class);
            assert baiduFaceEntryDTO != null;
            int errorCode = baiduFaceEntryDTO.getErrorCode();

            requestError(response, errorCode);
            return baiduFaceEntryDTO;
        } catch (Exception e) {
            log.error("百度云[人脸更新]失败:{} response:{} ", e.getMessage(), response);
        }
        return baiduFaceEntryDTO;
    }
}
